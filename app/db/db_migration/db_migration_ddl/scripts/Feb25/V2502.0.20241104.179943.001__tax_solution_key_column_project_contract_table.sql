-- liquibase formatted sql
-- OWNER_CONTACT: <EMAIL>
-- changeset <EMAIL>:V2502.0.20241104.179943.001 runOnChange:false logicalFilePath:V2502.0.20241104.179943.001__tax_solution_key_column_project_contract_table.sql
/*
 == DELTA ONLY FOR DEV SCHEMAS ==

 Repair action: IGNORE
 */

DECLARE
nCount pls_integer := 0;
BEGIN
SELECT count(*) into nCount from all_tab_cols where owner = sys_context('userenv', 'current_schema') AND table_name = 'PROJECTCONTRACT' AND column_name = 'TAXSOLUTIONKEY';
IF nCount = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE PROJECTCONTRACT ADD (TAXSOLUTIONKEY NUMBER(15,0) DEFAULT NULL)';
END IF;
END;
/
DECLARE
index_exists NUMBER := 0;
BEGIN
SELECT count(*)
INTO index_exists
FROM all_indexes WHERE table_owner = sys_context('userenv', 'current_schema') AND index_name = 'IX_PJCN_TAXSOLUTIONKEY'
  AND table_name = 'PROJECTCONTRACT';
IF (index_exists = 0) THEN
        EXECUTE IMMEDIATE 'CREATE INDEX IX_PJCN_TAXSOLUTIONKEY on PROJECTCONTRACT(CNY#, TAXSOLUTIONKEY) TABLESPACE ACCTINDX';
END IF;
END;
/
DECLARE
nCount pls_integer := 0;
BEGIN
SELECT count(*) into nCount from all_constraints where owner = sys_context('userenv', 'current_schema') AND constraint_name = 'FK_PJCN_TAXSOLUTIONKEY';
IF nCount = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE PROJECTCONTRACT ADD CONSTRAINT FK_PJCN_TAXSOLUTIONKEY FOREIGN KEY (CNY#, TAXSOLUTIONKEY) REFERENCES TAXSOLUTION(CNY#, RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE';
END IF;
END;
/