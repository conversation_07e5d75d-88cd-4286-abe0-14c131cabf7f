--OWNER_CONTACT:<EMAIL>

--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__VIEW_V_CONTRACTSCHFORECASTRAW.sql

CREATE OR REPLACE FORCE VIEW v_contractschforecastraw (cny#,contractkey,contractdetailkey,melocationkey,contractexpensekey,contractid,"TYPE","SIGN",typeorder,journal,"MONTH",classification,posted,amount) AS
select cs.cny#
    , ch.record# contractkey, cs.contractdetailkey, ch.melocationkey, cs.contractexpensedetailkey contractexpensekey
    , ch.contractid
    , jl.type
    , jl.sign
    , jl.typeorder
    , jl.journal
    , to_char(greatest(cse.postingdate, coalesce(csr.effectivedate, paid.actualpostingdate, billed.actualpostingdate, cse.postingdate)), 'yyyy-mm') month
    , case when paid.cny# is not null then 'P' when billed.cny# is not null then 'B' else 'UB' end as classification
    , nvl(cse.posted, 'F') as posted
    , sum(
        decode(sys_context('TMCtx', 'CURRENCY'), 'B', csr.baseamount, csr.amount)
      ) amount
  from contractschedule cs
    inner join contractscheduleentry cse on cse.cny#=cs.cny# and cse.schedulekey=cs.record# and nvl(cse.state, 'I') !='T'
      and cse.postingdate > to_date(sys_context('TMCtx', 'ASOFDATE'), 'mm/dd/yyyy')
    inner join contract ch on ch.cny#=cs.cny# and ch.record#=cs.contractkey
    inner join iacontractjournallookup jl on jl.schtype=cs.type
    inner join contractschedulesresolve csr on csr.cny#=cse.cny# and cse.record#=csr.revschentrykey
    left outer join contractscheduleentry billed on billed.cny#=csr.cny# and billed.record#=csr.billschentrykey
      and substr(cs.type, 1, 1) = 'R' and nvl(billed.state, 'I')!='T' and billed.actualpostingdate <= to_date(sys_context('TMCtx', 'ASOFDATE'), 'mm/dd/yyyy')
    left outer join contractscheduleentry paid on paid.cny#=csr.cny# and paid.record#=csr.pmtschentrykey
      and nvl(paid.state, 'I')!='T' and paid.actualpostingdate <= to_date(sys_context('TMCtx', 'ASOFDATE'), 'mm/dd/yyyy')
  where
    jl.type='R'
  group by cs.cny#, ch.record#, cs.contractdetailkey, ch.melocationkey, cs.contractexpensedetailkey, ch.contractid, jl.type, jl.sign, jl.typeorder, jl.journal
    , to_char(greatest(cse.postingdate, coalesce(csr.effectivedate, paid.actualpostingdate, billed.actualpostingdate, cse.postingdate)), 'yyyy-mm')
    , case when paid.cny# is not null then 'P' when billed.cny# is not null then 'B' else 'UB' end
    , nvl(cse.posted, 'F')
union all
-- BILLING
  select cs.cny#
    , ch.record# contractkey, cs.contractdetailkey, ch.melocationkey, cs.contractexpensedetailkey contractexpensekey
    , ch.contractid
    , jl.type
    , jl.sign
    , jl.typeorder
    , jl.journal
    , to_char(greatest(cse.postingdate, coalesce(csr.effectivedate, paid.actualpostingdate, cse.postingdate)), 'yyyy-mm') month
    , case when paid.cny# is not null then 'P' else 'UP' end as classification
    , nvl(cse.posted, 'F') as posted
    , sum(
       decode(sys_context('TMCtx', 'CURRENCY'), 'B', nvl(csr.postedbillbaseamount, csr.billbaseamount), csr.amount)
      ) amount
  from contractschedule cs
    inner join contractscheduleentry cse on cse.cny#=cs.cny# and cse.schedulekey=cs.record# and nvl(cse.state, 'I') !='T'
      and cse.postingdate > to_date(sys_context('TMCtx', 'ASOFDATE'), 'mm/dd/yyyy')
    inner join contract ch on ch.cny#=cs.cny# and ch.record#=cs.contractkey
    inner join iacontractjournallookup jl on jl.schtype=cs.type
    inner join contractschedulesresolve csr on csr.cny#=cse.cny# and cse.record#=csr.billschentrykey
    inner join contractscheduleentry rev on rev.cny#=csr.cny# and rev.record#=csr.revschentrykey --and nvl(rev.state, 'I')!='T'
    inner join contractschedule revh on revh.cny#=rev.cny# and revh.record#=rev.schedulekey and revh.type=jl.revtype
    left outer join contractscheduleentry paid on paid.cny#=csr.cny# and paid.record#=csr.pmtschentrykey
      and nvl(paid.state, 'I')!='T' and paid.actualpostingdate <= to_date(sys_context('TMCtx', 'ASOFDATE'), 'mm/dd/yyyy')
  where
    jl.type='B'
  group by cs.cny#, ch.record#, cs.contractdetailkey, ch.melocationkey, cs.contractexpensedetailkey, ch.contractid, jl.type, jl.sign, jl.typeorder, jl.journal
    , to_char(greatest(cse.postingdate, coalesce(csr.effectivedate, paid.actualpostingdate, cse.postingdate)), 'yyyy-mm')
    , case when paid.cny# is not null then 'P' else 'UP' end
    , nvl(cse.posted, 'F')
union all
-- PAYMENT
  select cs.cny#
    , ch.record# contractkey, cs.contractdetailkey, ch.melocationkey, cs.contractexpensedetailkey contractexpensekey
    , ch.contractid
    , jl.type
    , jl.sign
    , jl.typeorder
    , jl.journal
    , to_char(cse.postingdate, 'yyyy-mm') month
    , null as classification
    , nvl(cse.posted, 'F') as posted
    , sum(
        decode(sys_context('TMCtx', 'CURRENCY'), 'B', cse.baseamount, cse.amount)
      ) amount
  from contractschedule cs
    inner join contractscheduleentry cse on cse.cny#=cs.cny# and cse.schedulekey=cs.record# and nvl(cse.state, 'I') !='T'
      and cse.postingdate > to_date(sys_context('TMCtx', 'ASOFDATE'), 'mm/dd/yyyy')
    inner join contract ch on ch.cny#=cs.cny# and ch.record#=cs.contractkey
    inner join iacontractjournallookup jl on jl.schtype=cs.type
  where
    jl.type='P'
  group by cs.cny#, ch.record#, cs.contractdetailkey, ch.melocationkey, cs.contractexpensedetailkey, ch.contractid, jl.type, jl.sign, jl.typeorder, jl.journal
    , to_char(cse.postingdate, 'yyyy-mm')
    , nvl(cse.posted, 'F')
union all
-- EXPENSE
  select cs.cny#
    , ch.record# contractkey, cs.contractdetailkey, ch.melocationkey, cs.contractexpensedetailkey contractexpensekey
    , ch.contractid
    , jl.type
    , jl.sign
    , jl.typeorder
    , jl.journal
    , to_char(cse.postingdate, 'yyyy-mm') month
    , null as classification
    , nvl(cse.posted, 'F') as posted
    , sum(
        decode(sys_context('TMCtx', 'CURRENCY'), 'B', cse.baseamount, cse.amount)
      ) amount
  from contractschedule cs
    inner join contractscheduleentry cse on cse.cny#=cs.cny# and cse.schedulekey=cs.record# and nvl(cse.state, 'I') !='T'
      and cse.postingdate > to_date(sys_context('TMCtx', 'ASOFDATE'), 'mm/dd/yyyy')
    inner join contract ch on ch.cny#=cs.cny# and ch.record#=cs.contractkey
    inner join iacontractjournallookup jl on jl.schtype=cs.type
  where
    jl.type='E'
  group by cs.cny#, ch.record#, cs.contractdetailkey, ch.melocationkey, cs.contractexpensedetailkey, ch.contractid, jl.type, jl.sign, jl.typeorder, jl.journal
    , to_char(cse.postingdate, 'yyyy-mm')
    , nvl(cse.posted, 'F');