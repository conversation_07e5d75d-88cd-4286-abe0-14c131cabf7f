--OWNER_CONTACT:<EMAIL>

--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__VIEW_V_EXCHANGERATETYPES.sql

CREATE OR REPLACE FORCE VIEW v_exchangeratetypes (cny#,"ID","NAME") AS
SELECT
        cny#,
        id,
        name
    FROM
        exchangeratetypes
    UNION ALL
    SELECT
        record#,
        -1,
        'Intacct Daily Rate'
    FROM
        company
    UNION ALL
    SELECT
        record#,
        -99,
        'Custom'
    FROM
        company;