--OWNER_CONTACT:<EMAIL>

CREATE OR REPLACE FORCE VIEW AUDITSTORAGE_FIELDS_NOGLENTRY (CNY#, USERID, ACCESSTIME, OBJECTTYPE, OBJECTKEY,
 ACCESSMODE, RAWMODE, <PERSON><PERSON><PERSON><PERSON>OWACTI<PERSON>, IPADDRESS, SOURCE, BLOB, OPERATION, ID, NOTES, RECORD_URL,
 FIELDNAME, FIELDTYPE, OLDSTRVAL, NEWSTRVAL, OLDINTVAL, NEWINTVAL, OLDNUMVAL, NEWNUMVAL,
 OLDDATEVAL, NEWDATEVAL, OLDVAL, NEWVAL,ACTION_DETAILS, RECORDNO) AS
  select cny#, userid, accesstime, objecttype, objectkey,
  accessmode, accessmode, workflowaction,
  ipaddress, source, null,
  decode(accessmode,'S',workflowaction,'W',workflowaction,'T',workflowaction,'U',workflowaction,accessmode),
  recordid, objectdesc, '',
  fieldname, fieldtype, oldstrval, newstrval, oldintval, newintval, oldnumval, newnumval,
  olddateval, newdateval, oldval, newval, decode(accessmode,'W',workflowaction,'T',workflowaction+1000,'U',workflowaction,null),
'216'|| '-' || recordid || '-' || decode(fieldname, null, 'no_field_diff', fieldname)  AS RECORDNO
  from audittrail_fields
  where migrated = 'T' and accessmode != 'S' and recordid not like '%--VID' and objecttype != 'glentry'
union all
  select s.cny#, u.loginid, CAST(sys_extract_utc(CAST(when AS TIMESTAMP)) as date),
  'userinfo', u.loginid, 'Login', '', null, s.IP, null,
  (decode(s.status, '0', 'Success', '1', 'Password failure', '2', 'IP address failure', '3', 'Day of week failure',
  '4', 'Time of day failure', '5', 'SSO failure', '6', 'Two-step login failure', '7', 'User locked out',
  '8', 'Inactive user failure') || '  ' || s.description ||
  decode(l.location_no,NULL,'','@' || l.location_no || '--' || l.name)),
  u.loginid || ':' || TO_CHAR(when,  'MMDDYYYYHH24MISS'), null, '', null,
  null, null, null, null, null, null, null, null, null, null, null, null, null,
 '217' || '-' || u.loginid || '-' || TO_CHAR(when,  'MMDDYYYYHH24MISS') AS RECORDNO
  from stat_loginlog s, userinfo u,location l
  where s.cny#=u.cny# and s.user#=u.record# and l.cny# (+) = s.cny# and s.locationkey = l.record# (+)
union all
  select p.cny#,u.loginid,p.created_at,d.obj_def_name,to_char(p.obj_id),'Platform Action','',
  null,null,null,null,p.trail_text,TO_CHAR(p.record#),null,null,
  null, null, null, null, null, null, null, null, null, null, null, null, null,
 '218' || '-' || TO_CHAR(p.record#) AS RECORDNO
  from pt_act_trail p, userinfo u, pt_obj_def d, pt_obj_data o
  where p.cny#=u.cny# and p.created_by=u.record# and
  p.cny#=o.cny# and p.obj_id=o.record# and
  p.cny#=d.cny# and o.obj_def_id=d.record#
union all
  select p.cny#,u.loginid,p.created_at,d.obj_def_name,to_char(abs(p.obj_id)),'Platform Def','',
  null,null,null,null,p.trail_text,TO_CHAR(p.record#),null,null,
  null, null, null, null, null, null, null, null, null, null, null, null, null,
 '219' || '-' || TO_CHAR(p.record#) AS RECORDNO
  from pt_act_trail p, userinfo u, pt_obj_def d
  where p.cny#=u.cny# and p.created_by=u.record# and
  p.cny#=d.cny# and -p.obj_id=d.record#;
