--OWNER_CONTACT:<EMAIL>

--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__VIEW_PROJTOT_PAGROUPS.sql

CREATE OR REPLACE FORCE VIEW projtot_pagroups (cny#,property,"VALUE",grp#,normal_balance) AS
SELECT mp.CNY#            as CNY#,
         mp.PROPERTY        as PROPERTY,
         mp.VALUE           as VALUE,
         NVL(b.record#, c.record#) as GRP#,
         NVL(b.NORMAL_BALANCE,c.NORMAL_BALANCE) as NORMAL_BALANCE
  FROM   MODULEPREF mp ,
    (
      SELECT NORMAL_BALANCE,name,cny#, grp.record#
      FROM GLACCTGRP grp
      WHERE grp.HOWCREATED = 'U'
    ) b,
    (
      SELECT iagrp.NORMAL_BALANCE,iagrp.NAME, iagrp.INDUSTRYCODE, iagrp.record#
      FROM IAGLACCTGRP iagrp
    ) c,
    COMPANY2 cc
  WHERE  mp.CNY# = cc.CNY# (+)
         AND mp.VALUE = c.NAME (+)
         AND mp.CNY# = b.CNY#(+)
         AND mp.VALUE = b.NAME (+)
         AND c.INDUSTRYCODE = cc.INDUSTRYCODE (+)
         AND mp.PROPERTY LIKE '%ACCOUNTGROUP'
         AND    mp.MODULEKEY = '48.PROJACCT'
         AND    mp.LOCATIONKEY IS NULL;