--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__TRIG_ARI_AFRSETUPATENTITY.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE TRIGGER ARI_AFRSETUPATENTITY AFTER INSERT ON AFRSETUPATENTITY FOR EACH ROW
BEGIN
  dbms_application_info.read_client_info(acct_utils.trigger_enabled);
  IF acct_utils.trigger_enabled <> 'F' THEN
    -- add the billing event
    INSERT INTO ia_message_queue (message_queue_id, cny#, type, action, payload)
    SELECT message_queue_seq.nextval, :new.cny#, 'MODULEPREF', 'INSERT',
      json_object(
        'company_record' VALUE :new.cny#,
        'module_record' VALUE :new.modulekey,
        'entity_record' VALUE :new.locationkey,
        'properties' VALUE (
          json_array(
            json_object(
              'property' VALUE 'DISABLEEDIT',
              'value' VALUE :new.disableedit
            ),
            json_object(
              'property' VALUE 'DISABLEDELETE',
              'value' VALUE :new.disabledelete
            )
          )
        )
      ) AS json_value
    FROM company c
    WHERE c.record# = :new.cny#
      AND c.ia_billing > 2;
  END IF;
END ARI_AFRSETUPATENTITY;
/
