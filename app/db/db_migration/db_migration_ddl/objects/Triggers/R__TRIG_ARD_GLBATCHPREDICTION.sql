--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__TRIG_ARD_GLBATCHPREDICTION.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE TRIGGER ARD_GL<PERSON>TCHPREDICTION BEFORE DELETE ON GLBATCHPREDICTION FOR EACH ROW
BEGIN
DBMS_APPLICATION_INFO.read_client_info (acct_utils.trigger_enabled);
IF acct_utils.trigger_enabled <> 'F' THEN
  INSERT INTO DELETE_LOG (CNY#, RECORDKEY, OBJECT, RECORDTYPE, DOCTYPE, OBJ_DEF_ID, WHENCREATED, WHENDELETED, DELETEDBY) VALUES(
    :old.cny#,
    :old.record#,
    'glbatchprediction',
    null,
    null,
    null,
    :old.whencreated,
    (CURRENT_TIMESTAMP AT TIME ZONE 'GMT'),
    SYS_CONTEXT('TMCtx', 'USERKEY')
  );
END IF;
END ARD_GLBATCHPREDICTION;
/
