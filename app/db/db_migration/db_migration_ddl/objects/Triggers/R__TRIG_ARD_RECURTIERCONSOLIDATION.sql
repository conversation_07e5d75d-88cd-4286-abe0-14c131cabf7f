--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__TRIG_ARD_RECURTIERCONSOLIDATION.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE TRIGGER ARD_RECUR<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ATION AFTER DELETE ON RECURTIERCONSOLIDATION FOR EACH ROW
    BEGIN
        DBMS_APPLICATION_INFO.READ_CLIENT_INFO(ACCT_UTILS.TRIGGER_ENABLED);
            IF ACCT_UTILS.TRIGGER_ENABLED <> 'F' THEN
                /* Delete corresponding SCHEDULEDOPERATION record */
                DELETE FROM SCHEDULEDOPERATION WHERE CNY# = :old.CNY# AND RECORD# IN(:old.SCHOPKEY);
            END IF;
    END ARD_RECURTIERCONSOLIDATION;
/
