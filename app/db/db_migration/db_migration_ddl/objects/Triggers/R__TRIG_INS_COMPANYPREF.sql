--OWNER_CONTACT:<EMAIL>,<EMAIL>,<EMAIL>

CREATE OR REPLACE TRIGGER ins_companypref
    AFTER INSERT ON companypref
    FOR EACH ROW
    WHEN (NEW.property = 'MAXSESSIONTIMEOUT' OR NEW.property = 'MAXLOGINTIMEOUT')
BEGIN
    dbms_application_info.read_client_info(acct_Utils.Trigger_Enabled);
    IF acct_Utils.Trigger_Enabled <> 'F' THEN
        -- update all the users who are set above the max limit set
        IF (:NEW.property = 'MAXSESSIONTIMEOUT') THEN
            UPDATE userpref
            SET value = TO_NUMBER(:NEW.value) * 3600
            WHERE cny# = :NEW.cny#
              AND property = 'SESSIONTIMEOUT'
              AND TO_NUMBER(value) > TO_NUMBER(:NEW.value) * 3600;
        ELSIF (:NEW.property = 'MAXLOGINTIMEOUT') THEN
            UPDATE userpref
            SET value = TO_NUMBER(:NEW.value) * 3600
            WHERE cny# = :NEW.cny#
              AND property = 'LOGINTIMEOUT'
              AND TO_NUMBER(value) > TO_NUMBER(:NEW.value) * 3600;
        END IF;
    END IF;
END;
/
