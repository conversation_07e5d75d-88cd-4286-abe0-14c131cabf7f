--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__TRIG_BRU_SUBSCRIPTION.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE TRIGGER BRU_SUBSCRIPTION BEFORE UPDATE ON SUBSCRIPTION FOR EACH ROW
BEGIN

    DBMS_APPLICATION_INFO.read_client_info (acct_utils.trigger_enabled);

    IF acct_utils.trigger_enabled <> 'F' THEN
        :NEW.whenmodified := CURRENT_TIMESTAMP AT TIME ZONE 'GMT';
        :NEW.modifiedby := SYS_CONTEXT('TMCtx', 'USERKEY');
    END IF;

END BRU_SUBSCRIPTION;
/
