--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__TRIG_DEL_UGROUP.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE TRIGGER DEL_UGROUP
        after delete on ugroup
        for each row
begin
     dbms_application_info.read_client_info(acct_Utils.Trigger_Enabled);

     if acct_Utils.Trigger_Enabled <> 'F' then
		delete from pathpol where cny#=:old.cny# and U_O_GKEY = :old.record# and type = 'G';
		INSERT INTO DELETE_LOG (CNY#, RECORDKEY, OBJECT, DOCTYPE, OBJ_DEF_ID, WHENCREATED, WHENDELETED, DELETEDBY) VALUES(
            :old.cny#,
            :old.record#,
            'usergroup',
            null,
            null,
            :old.WHENCREATED,
            (CURRENT_TIMESTAMP AT TIME ZONE 'GMT'),
            SYS_CONTEXT('TMCtx', 'USERKEY')
        );
     end if;
end del_ugroup;
/
