--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__TRIG_ARU_LOCATION.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE TRIGGER aru_location AFTER UPDATE ON location FOR EACH ROW
BEGIN
  dbms_application_info.read_client_info(acct_utils.trigger_enabled);
  IF acct_utils.trigger_enabled <> 'F' THEN
    IF ((:new.locationtype IN ('N', 'E') OR :old.locationtype IN ('N', 'E')) AND :new.locationtype <> :old.locationtype)
      OR :new.record# <> :old.record# OR :new.cny# <> :old.cny# OR :new.location_no <> :old.location_no OR :new.name <> :old.name
      OR :new.status <> :old.status OR NVL(:new.currency, ' ') <> NVL(:old.currency, ' ') THEN
      -- TODO eliminate duplicate events
      -- in the code, on update, we first set the currency on null, then, in a separate query, we put the new currency; thus, we get 2 update events

      -- add the billing event
      INSERT INTO ia_message_queue (message_queue_id, cny#, type, action, payload)
      SELECT message_queue_seq.nextval, :new.cny#, 'ENTITY', 'UPDATE',
        json_object(
          'record' VALUE :new.record#,
          'company_record' VALUE :new.cny#,
          'id' VALUE :new.location_no,
          'name' VALUE :new.name,
          'type' VALUE CASE :new.locationtype WHEN 'E' THEN 'Business' WHEN 'N' THEN 'Elimination' END,
          'raw_type' VALUE :new.locationtype,
          'status' VALUE :new.status,
          'currency' VALUE :new.currency,
          'when_created' VALUE :new.whencreated,
          'when_modified' VALUE :new.whenmodified
        )
      FROM company c
      WHERE c.record# = :new.cny#
        AND c.ia_billing > 0
        AND c.moved = 'F';
    END IF;
  END IF;
END aru_location;
/
