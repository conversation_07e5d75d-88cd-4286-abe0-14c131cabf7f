--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__PROC_CC_DROP_ALL_TRIGGERS.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE PROCEDURE CC_Drop_All_Triggers(id in varchar2 default '')
IS
    Type t_tab is table of varchar2(200);
    triggerName t_tab;
    i number;
    sql_stmt varchar2(400);
BEGIN
    if id is null then
        sql_stmt := 'select trigger_name from user_triggers
                where upper(trigger_name) like ''CC\_IU\_%'' escape ''\''
                or upper(trigger_name) like ''CC\_D\_%'' escape ''\''
                order by trigger_name';
    else
        sql_stmt := 'select trigger_name from user_triggers
                where upper(trigger_name) like ''CC\_IU\_' || upper(id) || '\_%'' escape ''\''
                or upper(trigger_name) like ''CC\_D\_' || upper(id) || '\_%'' escape ''\''
                order by trigger_name';
    end if;
    execute immediate sql_stmt bulk collect into triggerName;

    if triggerName.count>0 then
        for i in 1 .. triggerName.count loop
            sql_stmt:= 'DROP TRIGGER ' || triggerName(i);
            --DBMS_OUTPUT.PUT_LINE(sql_stmt);
            Execute Immediate sql_stmt;
        End loop;
    End if;
    DBMS_OUTPUT.PUT_LINE('Dropped ' || triggerName.count || ' change capture triggers.');
END;
/
