--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__PROC_CNYSINVALIDAPPROVERSCHECK.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE PROCEDURE CnysInvalidApproversCheck
IS
  counter NUMBER;
  total_count NUMBER;
  isvalid NUMBER;

BEGIN
    dbms_output.put_line('------------------------------- LIST OF COMPANIES WITH INVALID APPPROVERS ---------------------------------------');

    counter := 0;
    FOR ROW IN (
      SELECT record#, title, migrated
      FROM temp_migrateapprovals
    )
    LOOP
      isvalid := check_valid_approvers(row.record#);
      IF isvalid = 0
      THEN
        counter := counter + 1;
        dbms_output.put_line('Valid Approvers check failed for company ' || TO_CHAR(row.title) || ' CNY# : ' || row.record#);
      END IF;
    END LOOP;

    SELECT count(1) INTO total_count FROM temp_migrateapprovals;
    dbms_output.put_line('----------------------------------------------------------------------------------------------------------------');
    dbms_output.put_line('Companies failing valid approvers check are ' || counter || ' out of a total of ' || total_count || ' companies.');

END CnysInvalidApproversCheck;
/
