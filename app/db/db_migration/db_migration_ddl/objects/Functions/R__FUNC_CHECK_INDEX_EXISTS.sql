--OWNER_CONTACT: <EMAIL>, <EMAIL>

CREATE OR replace FUNCTION CHECK_INDEX_EXISTS
(
   P_INDEX_NAME   IN VARCHAR,
   P_SCHEMA_NAME  IN VARCHAR DEFAULT sys_context('userenv', 'current_schema')
)
RETURN BOOLEAN
IS
   --DECLARE SECTION
   LV_COUNT NUMBER := 0;

BEGIN
   --CODE SECTION

   SELECT
       COUNT(*)
   INTO LV_COUNT
   FROM
       ALL_INDEXES
   WHERE
       OWNER = UPPER(TRIM(P_SCHEMA_NAME)) AND
       INDEX_NAME = UPPER(TRIM(P_INDEX_NAME));

   IF LV_COUNT > 0 THEN
      RETURN TRUE;
   ELSE
      RETURN FALSE;
   END IF;

END;
/
