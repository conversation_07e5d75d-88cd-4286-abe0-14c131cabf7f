--OWNER_CONTACT:<EMAIL>
-- ------------------------------------------------------------------------------------------------------------------------
-- -- V2502.0.20241018.52827.001__cleanup_orphan_data_from_oda_ola_moda_mola
/*

== DELTA ONLY FOR DEV SCHEMAS ==

Description: Cleanup orphan data

Repair action: EXECUTE

*/

BEGIN
    DBMS_APPLICATION_INFO.SET_CLIENT_INFO('F');
END;
/

DELETE FROM OLA WHERE locationkey IS NULL AND locationgrpkey IS NULL AND OBJECTTYPE IN ('CUSTOMER' , 'VENDOR')
/

DELETE FROM ODA WHERE deptkey IS NULL AND deptgrpkey IS NULL AND OBJECTTYPE IN ('CUSTOMER' , 'VENDOR')
/

DELETE FROM MOLA WHERE locationkey IS NULL AND OBJECTTYPE IN ('CUSTOMER' , 'VENDOR')
/

DELETE FROM MODA WHERE deptkey IS NULL AND OBJECTTYPE IN ('CUSTOMER' , 'VENDOR')
/

---Cleanup orphan data from ODA,OLA,MODA,MOLA tables
DELETE FROM ODA o where o.deptkey IS NOT NULL AND o.deptkey !=0 AND NOT EXISTS (select 1 from department where cny# = o.cny# AND record# = o.deptkey);
/

DELETE from oda o where o.deptgrpkey IS NOT NULL AND o.deptgrpkey !=0 AND NOT EXISTS (select 1 from departmentgroup where cny# = o.cny# AND record# = o.deptgrpkey)
/

DELETE FROM MODA o where o.deptkey IS NOT NULL AND o.deptkey !=0 AND NOT EXISTS (select 1 from department where cny# = o.cny# AND record# = o.deptkey)
/

DELETE from MOLA o where o.locationkey IS NOT NULL AND o.locationkey !=0 AND NOT EXISTS (select 1 from location where cny# = o.cny# AND record# = o.locationkey)
/

DELETE from OLA o where o.locationgrpkey IS NOT NULL AND o.locationgrpkey !=0 AND NOT EXISTS (select 1 from locationgroup where cny# = o.cny# AND record# = o.locationgrpkey)
/

DELETE from OLA o where o.locationkey IS NOT NULL AND o.locationkey !=0 AND NOT EXISTS (select 1 from location where cny# = o.cny# AND record# = o.locationkey);
/


BEGIN
  DBMS_APPLICATION_INFO.SET_CLIENT_INFO('T');
END;
/