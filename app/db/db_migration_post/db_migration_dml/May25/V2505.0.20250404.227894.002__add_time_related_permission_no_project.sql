--OWNER_CONTACT:<EMAIL>

-- Copying existing role based permission of below list from Time & Expenses to new module Time
INSERT INTO ROLEPOLICYASSIGNMENT (cny#, rolekey, module, policykey, policyval, record#)
SELECT pa.CNY#,
       pa.ROLEKEY,
       'tt',
       pol_key,
       pa.POLICYVAL,
       get_nextrecordid(pa.cny#, 'rolepolicyassignment')
FROM IAPOLICY p
         JOIN ROLEPOLICYASSIGNMENT pa ON p.RECORD# = pa.POLICYKEY
         JOIN MODULE m ON m.CNY# = pa.CNY#
         LEFT JOIN (
    SELECT p_tt.RECORD# AS pol_key, p_tt.Name
    FROM IAPOLICY p_tt
    WHERE p_tt.MODULE = 'tt'
) pol_subquery ON pol_subquery.Name = p.Name
WHERE p.MODULE = 'ee'
  AND m.MODULEID = '6.EE'
  AND NOT EXISTS (
    SELECT 1
    FROM MODULE m2
    WHERE m2.CNY# = m.CNY#
      AND m2.MODULEID = '48.PROJACCT'
)
  AND p.NAME IN (
                 'Employees',
                 'Employee Groups',
                 'Employee Rates',
                 'My Timesheets',
                 'Staff Timesheets',
                 'Manage Timesheets',
                 'Time sources',
                 'Time preferences',
                 'Employee Types'
    )
  AND NOT EXISTS (
    SELECT 1
    FROM ROLEPOLICYASSIGNMENT rpa
    WHERE rpa.CNY# = pa.CNY#
      AND rpa.ROLEKEY = pa.ROLEKEY
      AND rpa.POLICYKEY = pol_subquery.pol_key
      AND rpa.MODULE = 'tt'
)
/

-- Copying existing user based permission of below list from Time & Expenses to new module Time
INSERT INTO POLICYASSIGNMENT (cny#, user_role_key, policykey, policyval)
SELECT pa.CNY#,
       pa.USER_ROLE_KEY,
       pol_key, -- This will be the POLICYKEY calculated in the subquery
       pa.POLICYVAL
FROM IAPOLICY p
         JOIN POLICYASSIGNMENT pa ON p.RECORD# = pa.POLICYKEY
         JOIN MODULE m ON m.CNY# = pa.CNY#
         LEFT JOIN (
    SELECT p_tt.RECORD# AS pol_key, p_tt.Name, p_tt.MODULE
    FROM IAPOLICY p_tt
    WHERE p_tt.MODULE = 'tt'
) pol_subquery ON pol_subquery.Name = p.Name
WHERE p.MODULE = 'ee'
  AND m.MODULEID = '6.EE'
  AND NOT EXISTS (
    SELECT 1
    FROM MODULE m2
    WHERE m2.CNY# = m.CNY#
      AND m2.MODULEID = '48.PROJACCT'
)
  AND p.NAME IN (
                 'Employees',
                 'Employee Groups',
                 'Employee Rates',
                 'My Timesheets',
                 'Staff Timesheets',
                 'Manage Timesheets',
                 'Time sources',
                 'Time preferences',
                 'Employee Types'
    )
  AND NOT EXISTS (
    SELECT 1
    FROM POLICYASSIGNMENT rpa
    WHERE rpa.CNY# = pa.CNY#
      AND rpa.USER_ROLE_KEY = pa.USER_ROLE_KEY
      AND rpa.POLICYKEY = pol_subquery.pol_key
      AND pol_subquery.MODULE = 'tt' -- Ensure no duplicate (CNY#, USER_ROLE_KEY, POLICYKEY)
)
/

-- updating the user's PERM_CACHE_VALID to F for the users who have the permission of the new module
UPDATE USERINFO u
SET u.PERM_CACHE_VALID = 'F'
WHERE EXISTS (
    SELECT 1
    FROM POLICYASSIGNMENT p
             JOIN IAPOLICY ia ON ia.RECORD# = p.POLICYKEY
    WHERE u.CNY# = p.CNY#
      AND u.RECORD# = p.USER_ROLE_KEY
      AND ia.MODULE = 'tt'
)
/

-- updating the user's PERM_CACHE_VALID to F for the users who have the permission of the new module
UPDATE USERINFO u
SET u.PERM_CACHE_VALID = 'F'
WHERE EXISTS (
    SELECT 1
    FROM ROLEPOLICYASSIGNMENT r
             JOIN IAPOLICY ia ON ia.RECORD# = r.POLICYKEY
    WHERE u.CNY# = r.CNY#
      AND u.RECORD# = r.ROLEKEY
      AND ia.MODULE = 'tt'
)
/