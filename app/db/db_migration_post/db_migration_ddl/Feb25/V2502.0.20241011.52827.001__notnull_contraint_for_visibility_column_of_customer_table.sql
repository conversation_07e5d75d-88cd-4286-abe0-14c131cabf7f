--OWNER_CONTACT:<EMAIL>
-- ------------------------------------------------------------------------------------------------------------------------
-- -- V2502.0.20241011.52827.001__notnull_contraint_for_visibility_column_of_customer_table
-- -- JIRA : IA-52827: Create REST Object CUSTOMERVISIBILITY
-- ------------------------------------------------------------------------------------------------------------------------
/*
== DELTA ONLY FOR DEV SCHEMAS ==
Description: changing column type to CHAR (CHAR 1)
Repair action: EXECUTE
*/

-- -- Add not null constraint for VISIBILITY
ALTER TABLE CUSTOMER MODIFY VISIBILITY CHAR (1 CHAR) DEFAULT 'U'
/