ALTER TABLE FAASSET
    DROP COLUMN DISPOSALACCTKEY
/

DELETE FROM IADEPRMETHOD WHERE RECORD# = '-4'
/

--STATE migration changes as documented in https://intacct.atlassian.net/wiki/spaces/DEV/pages/2468053909/Different+States+Corresponding+DB+Values
UPDATE FAASSET SET STATE = 'R' WHERE STATE = 'O'
/

UPDATE FAASSET SET STATE = 'S' WHERE STATE = 'P'
/

UPDATE FADEPRSCHEDULE SET STATE = 'I' WHERE STATE = 'P'
/

UPDATE FADEPRSCHEDULE SET STATE = 'C' WHERE STATE = 'D'
/

UPDATE FADEPRSCHEDULE SET STATE = 'L' WHERE STATE = 'C'
/

UPDATE FADEPRSCHENTRY SET STATE = 'P' WHERE STATE = 'D'
/
