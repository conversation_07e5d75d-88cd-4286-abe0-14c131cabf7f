------------------------------------------------------------------------------------------------------------------------
------- DDL --JIRA Epic IA-48227 : Add migration for amortization object and amortization forecast report --------
------------------------------------------------------------------------------------------------------------------------

DELETE FROM IAPOLICY WHERE MODULE = 'ap' AND NAME = 'AP Amortization Forecast Report'
/
DELETE FROM IAPOLICY WHERE MODULE = 'ar' AND NAME = 'AR Amortization Forecast Report'
/
DELETE FROM IAPOLICY WHERE MODULE = 'ap' AND NAME = 'AP Amortization Template'
/
DELETE FROM IAPOLICY WHERE MODULE = 'ar' AND NAME = 'AR Amortization Template'
/
ALTER TABLE PRENTRY DROP COLUMN AMORTIZATIONTEMPLATEKEY CASCADE CONSTRAINTS
/
ALTER TABLE PRENTRY DROP COLUMN AMORTIZATIONSTARTDATE
/
ALTER TABLE PRENTRY DROP COLUMN AMORTIZATIONENDDATE
/

DROP TABLE AMORTIZATIONSCHEDULEENTRY
/
DROP TABLE AMORTIZATIONSCHEDULE
/
DROP TABLE AMORTIZATIONTEMPLATE
/
