--liquibase formatted sql
--changeset suresh.adiserla:V2021210.133023.001 runOnChange:false logicalFilePath:V2021210.133023.001__ee_prrecord_legacy.sql

--Disable the triggers
BEGIN
    DBMS_APPLICATION_INFO.SET_CLIENT_INFO('F');
END;
/

UPDATE PRRECORD PR
SET
    PR.LEGACY = 'T'
WHERE
    (PR.RECORD#,PR.CNY#) IN (
    SELECT DISTINCT
            PRR.RECORD#, PRR.CNY#
        FROM
            PRENTRYCURRDETAIL PRC, PRENTRY PRE, PRRECORD  PRR
        WHERE
            PRC.USER_EXCHRATE IS NOT NULL
            AND PRC.PRENTRYKEY = PRE.RECORD#
            AND PRE.CNY# = PRC.CNY#
            AND PRR.RECORD# = PRE.RECORDKEY
            AND PRE.CNY# = PRR.CNY#
            AND PRR.RECORDTYPE IN ( 'ei')
    )
/

--Enable the triggers
BEGIN
    DBMS_APPLICATION_INFO.SET_CLIENT_INFO('T');
END;
/