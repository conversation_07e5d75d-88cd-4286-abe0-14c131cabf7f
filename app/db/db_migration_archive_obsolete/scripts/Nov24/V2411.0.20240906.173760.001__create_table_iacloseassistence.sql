-----START CREATE IAASSISTANTASK TABLE ----------
CREATE TABLE IAASSISTANTTASK (
       RECORD# NUMBER(15,0),
       TASKID VARCHAR2(100 CHAR) CONSTRAINT NN_IAASSISTANTTASK_TASKID NOT NULL ENABLE,
       WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP AT TIME ZONE 'GMT',
       WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP AT TIME ZONE 'GMT',
       SI_UUID VARCHAR2(36 CHAR),
       CONSTRAINT PK_IAASSISTANTTASK PRIMARY KEY (RECORD#) USING INDEX TABLESPACE ACCTINDX,
       CONSTRAINT UQ_IAASSISTANTTASK_TASKID UNIQUE (TASKID) USING INDEX TABLESPACE ACCTINDX
) TABLESPACE ACCTDATA
/

-----END CREATE IAASSISTANTASK TABLE ----------

-----START INSERT INTO IAASSISTANTASK TABLE ----------

INSERT INTO IAASSISTANTTASK ( RECORD#, TASKID ) VALUES ( 1, 'AP_OPEN_TRANSACTIONS')
/

INSERT INTO IAASSISTANTTASK ( RECORD#, TASKID ) VALUES ( 2, 'AP_CLOSE_SUMMARY')
/

INSERT INTO IAASSISTANTTASK ( RECORD#, TASKID ) VALUES ( 3, 'AR_OPEN_TRANSACTIONS')
/

INSERT INTO IAASSISTANTTASK ( RECORD#, TASKID ) VALUES ( 4, 'AR_CLOSE_SUMMARY')
/

INSERT INTO IAASSISTANTTASK ( RECORD#, TASKID ) VALUES ( 5, 'GL_OPEN_TRANSACTIONS')
/

INSERT INTO IAASSISTANTTASK ( RECORD#, TASKID ) VALUES ( 6, 'GL_CLOSE_SUMMARY')
/

INSERT INTO IAASSISTANTTASK ( RECORD#, TASKID ) VALUES ( 7, 'CM_OPEN_RECON')
/

INSERT INTO IAASSISTANTTASK ( RECORD#, TASKID ) VALUES ( 8, 'CM_CLOSE_SUMMARY')
/
-----END INSERT INTO  IAASSISTANTTASK TABLE ----------