---- ***** Adding <PERSON><PERSON><PERSON><PERSON><PERSON>ENTITYDIMKEY in the following tables  
DECLARE
column_count INTEGER;
    loop_table_name
VARCHAR2(30);
    alter_table_stmt
VARCHAR2(200);
    TYPE
table_names_t IS TABLE OF VARCHAR2(30);
    table_names
table_names_t := table_names_t(
        'G<PERSON>CCTGR<PERSON>', 'REPORTINFO', 'GLENTRY', 'GLBUDGET', 'REVRECSCHEDULEENTRY',
        'PRENTRY', 'RECURPRENTRY', 'DOCENTRY', 'RECURDOCENTRY', 'TRANSTMPLENTRY',
        'RECURSUBTOTALS', 'DOCHDRSUBTOTALS', 'PRTAXENTRY', 'TIMESHEETENTRY',
        'ALLOCATIONENTRY', 'RECURGLENTRY', 'CONTRACT', 'CONTRACTDETAIL',
        'CONTRACTEXPENSEDETAIL', 'GENINVOICELINE', 'G<PERSON><PERSON><PERSON>LLOCATIONBASIS',
        '<PERSON><PERSON><PERSON><PERSON>LLOCATIONREVERSE', 'G<PERSON><PERSON><PERSON>LLOCATIONSOURCE', 'G<PERSON><PERSON><PERSON><PERSON>OCATIONTARGET',
        'PJES<PERSON>MATEENTRY', 'PROJECTCONTRACTLINE', 'RATETABLEENTRY_AP',
        'RATETABLEENTRY_CC', 'RATETABLEENTRY_EE', 'RATETABLEENTRY_GL',
        'RATETABLEENTRY_PO', 'RATETABLEENTRY_TS', 'CRDETAIL'
    );
BEGIN
FOR i IN 1..table_names.COUNT LOOP
        loop_table_name := table_names(i);
SELECT COUNT(*)
INTO column_count
FROM all_tab_columns
WHERE owner = sys_context('userenv', 'current_schema')
  AND table_name = loop_table_name
  AND column_name = 'AFFILIATEENTITYDIMKEY';

IF
column_count = 0 THEN
            alter_table_stmt := 'ALTER TABLE ' || loop_table_name || ' ADD (AFFILIATEENTITYDIMKEY NUMBER(15))';
EXECUTE IMMEDIATE alter_table_stmt;
END IF;
END LOOP;
END;
/    

---- ***** Adding AFFILIATEENTITYGROUPKEY in the following tables
DECLARE
column_count INTEGER;
    loop_table_name
VARCHAR2(30);
    alter_table_stmt
VARCHAR2(200);
    TYPE
table_names_t IS TABLE OF VARCHAR2(30);
    table_names
table_names_t := table_names_t(
        'REPORTINFO', 'GLACCTALLOCATIONBASIS', 'GLACCTALLOCATIONSOURCE', 'GLDIMGRPMEMBERS'
    );
BEGIN
FOR i IN 1..table_names.COUNT LOOP
        loop_table_name := table_names(i);
SELECT COUNT(*)
INTO column_count
FROM all_tab_columns
WHERE owner = sys_context('userenv', 'current_schema')
  AND table_name = loop_table_name
  AND column_name = 'AFFILIATEENTITYGROUPKEY';

IF
column_count = 0 THEN
            alter_table_stmt := 'ALTER TABLE ' || loop_table_name || ' ADD (AFFILIATEENTITYGROUPKEY NUMBER(15))';
EXECUTE IMMEDIATE alter_table_stmt;
END IF;
END LOOP;
END;
/


