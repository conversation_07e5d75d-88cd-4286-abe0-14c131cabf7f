--liquibase formatted sql
--changeset anishk:20201005.30552.001 runOnChange:false logicalFilePath:V20201005.30552.001__electronic_payments.sql

------------------------------------------------------------------------------------------------------------------------
-- Start
------------------------------------------------------------------------------------------------------------------------

CREATE TABLE OPSNOTIFICATIONPARENT
(
    CNY#        NUMBER(15) CONSTRAINT NN_OPSNOTIFICATIONPARENT_CNY NOT NULL ENABLE,
    RECORD#     NUMBER(15) CONSTRAINT NN_OPSNOTIFICATIONPARENT_RECORD NOT NULL ENABLE,
    BODY        CLOB,
    SUBJECT     VARCHAR2(80 CHAR),
    AUDIENCE    VARCHAR2(80 CHAR),
    WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT' CONSTRAINT NN_OPSNOTIFICATIONPARENT_WHENCREATED NOT NULL ENABLE,
    CONSTRAINT FK_OPSNOTIFICATIONPARENT_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT PK_OPSNOTIFICATIONPARENT PRIMARY KEY (CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX
)TABLESPACE ACCTDATA
LOB (BODY) STORE AS SECUREFILE  LOB_OPSNOTIFICATIONPARENT_BODY  ( TABLESPACE ACCTLOB DISABLE STORAGE IN ROW)
/

CREATE TABLE OPSNOTIFICATIONQUEUE
(
    CNY#            NUMBER(15) CONSTRAINT NN_OPSNOTIFICATIONQUEUE_CNY NOT NULL ENABLE,
    RECORD#         NUMBER(15) CONSTRAINT NN_OPSNOTIFICATIONQUEUE_RECORD NOT NULL ENABLE,
    ID              VARCHAR2(80 CHAR) CONSTRAINT NN_OPSNOTIFICATIONQUEUE_ID NOT NULL ENABLE,
    PARENTKEY       NUMBER(15),
    TYPE            VARCHAR2(50 CHAR) CONSTRAINT NN_OPSNOTIFICATIONQUEUE_TYPE NOT NULL ENABLE,
    BODY            CLOB,
    STATE           CHAR(1 CHAR),
    ERRORS          CLOB,
    LOCATIONKEY     NUMBER(15),
    DEPARTMENTKEY   NUMBER(15),
    CREATEDBY       NUMBER(15),
    MODIFIEDBY      NUMBER(15),
    WHENCREATED     DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT' CONSTRAINT NN_OPSNOTIFICATIONQUEUE_WHENCREATED NOT NULL ENABLE,
    WHENMODIFIED    DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT' CONSTRAINT NN_OPSNOTIFICATIONQUEUE_WHENMODIFIED NOT NULL ENABLE,
    CONSTRAINT FK_OPSNOTIFICATIONQUEUE_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT PK_OPSNOTIFICATIONQUEUE PRIMARY KEY (CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX,
    CONSTRAINT FK_OPSNOTIFICATIONQUEUE_CREATEDBY FOREIGN KEY (CNY#, CREATEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_OPSNOTIFICATIONQUEUE_MODIFIEDBY FOREIGN KEY (CNY#, MODIFIEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_OPSNOTIFICATIONQUEUE_PARENTKEY FOREIGN KEY (CNY#, PARENTKEY) REFERENCES OPSNOTIFICATIONPARENT (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_OPSNOTIFICATIONQUEUE_DEPTKEY FOREIGN KEY(CNY#, DEPARTMENTKEY) REFERENCES DEPARTMENT(CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_OPSNOTIFICATIONQUEUE_LOCATIONKEY FOREIGN KEY(CNY#, LOCATIONKEY) REFERENCES LOCATION(CNY#, RECORD#) DEFERRABLE ENABLE
) TABLESPACE ACCTDATA
LOB (BODY) STORE AS SECUREFILE  LOB_OPSNOTIFICATIONQUEUE_BODY  ( TABLESPACE ACCTLOB DISABLE STORAGE IN ROW),
LOB (ERRORS) STORE AS SECUREFILE  LOB_OPSNOTIFICATIONQUEUE_ERRORS  ( TABLESPACE ACCTLOB DISABLE STORAGE IN ROW)
/

CREATE INDEX IX_OPSNOTIFICATIONQUEUE_CREATEDBY ON OPSNOTIFICATIONQUEUE (CNY#, CREATEDBY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_OPSNOTIFICATIONQUEUE_MODIFIEDBY ON OPSNOTIFICATIONQUEUE (CNY#, MODIFIEDBY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_OPSNOTIFICATIONQUEUE_PARENTKEY ON OPSNOTIFICATIONQUEUE (CNY#, PARENTKEY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_OPSNOTIFICATIONQUEUE_DEPTKEY ON OPSNOTIFICATIONQUEUE (CNY#, DEPARTMENTKEY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_OPSNOTIFICATIONQUEUE_LOCATIONKEY ON OPSNOTIFICATIONQUEUE (CNY#, LOCATIONKEY) TABLESPACE ACCTINDX
/

DELETE FROM PAYMENTPROVIDER
/

DELETE FROM PROVIDERPAYMETHOD
/

ALTER TABLE PAYMENTPROVIDER ADD (
    AUTHURL VARCHAR2(400 CHAR),
    GUID VARCHAR2(40 CHAR) CONSTRAINT NN_PAYMENTPROVIDER_GUID NOT NULL ENABLE,
    ENABLEBULKVENDORONBOARD CHAR(1 CHAR) DEFAULT 'F',
    ENABLEAUTOVENDORONBOARD CHAR(1 CHAR) DEFAULT 'F'
)
/

ALTER TABLE PRRECORD ADD (
    PROVIDERKEY NUMBER(15),
    PROVIDERPAYMETHODKEY NUMBER(15),
    SELECTEDPROVIDERPAYMETHODKEY NUMBER(15),
    CONSTRAINT FK_PRRECORD_PROVIDERPAYMETHODKEY FOREIGN KEY (CNY#, PROVIDERPAYMETHODKEY) REFERENCES PROVIDERPAYMETHOD (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_PRRECORD_SELECTEDPROVIDERPAYKEY FOREIGN KEY (CNY#, SELECTEDPROVIDERPAYMETHODKEY) REFERENCES PROVIDERPAYMETHOD (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_PRRECORD_PROVIDERKEY FOREIGN KEY (CNY#, PROVIDERKEY) REFERENCES PAYMENTPROVIDER (CNY#, RECORD#) DEFERRABLE ENABLE
)
/

CREATE INDEX IX_PRRECORD_PROVIDERPAYMETHODKEY ON PRRECORD (CNY#, PROVIDERPAYMETHODKEY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_PRRECORD_SELECTEDPROVIDERPAYMETHODKEY ON PRRECORD (CNY#, SELECTEDPROVIDERPAYMETHODKEY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_PRRECORD_PROVIDERKEY ON PRRECORD (CNY#, PROVIDERKEY) TABLESPACE ACCTINDX
/

ALTER TABLE PROVIDERPAYMETHOD ADD (
    PAYMENTTYPE VARCHAR2(20 CHAR) CONSTRAINT NN_PROVIDERPAYMETHOD_PAYMENTTYPE NOT NULL ENABLE,
    IAPAYMETHODKEY NUMBER(15),
    CONSTRAINT FK_PROVIDERPAYMETHOD_IAPAYMETHODKEY FOREIGN KEY(IAPAYMETHODKEY) REFERENCES IAPAYMETHOD(RECORD#) DEFERRABLE ENABLE
)
/

CREATE INDEX IX_PROVIDERPAYMETHOD_IAPAYMETHODKEY ON PROVIDERPAYMETHOD (CNY#, IAPAYMETHODKEY) TABLESPACE ACCTINDX
/

CREATE TABLE PROVIDERBANKACCOUNT (
    CNY# NUMBER(15) CONSTRAINT NN_PROVIDERBANKACCOUNT_CNY NOT NULL ENABLE,
    RECORD# NUMBER(15) CONSTRAINT NN_PROVIDERBANKACCOUNT_RECORD NOT NULL ENABLE,
    PROVIDERKEY NUMBER(15),
    BANKACCOUNTKEY NUMBER(15),
    STATE CHAR(1 CHAR),
    MESSAGE VARCHAR2(500 CHAR),
    AUTHURL VARCHAR2(200 CHAR),
    EDITURL VARCHAR2(200 CHAR),
    STATUS CHAR(1 CHAR) DEFAULT 'F',
    CREATEDBY NUMBER(15),
    MODIFIEDBY NUMBER(15),
    WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT' CONSTRAINT NN_PROVIDERBANKACCOUNT_WHENCREATED NOT NULL ENABLE,
    WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT' CONSTRAINT NN_PROVIDERBANKACCOUNT_WHENMODIFIED NOT NULL ENABLE,
    CONSTRAINT FK_PROVIDERBANKACCOUNT_CNY FOREIGN KEY(CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT FK_PROVIDERBANKACCOUNT_BANKACCOUNTKEY FOREIGN KEY(CNY#, BANKACCOUNTKEY) REFERENCES BANKACCOUNT(CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_PROVIDERBANKACCOUNT_CREATEDBY FOREIGN KEY(CNY#, CREATEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_PROVIDERBANKACCOUNT_MODIFIEDBY FOREIGN KEY(CNY#, MODIFIEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_PROVIDERBANKACCOUNT_PROVIDERKEY FOREIGN KEY(CNY#, PROVIDERKEY) REFERENCES PAYMENTPROVIDER(CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT PK_PROVIDERBANKACCOUNT PRIMARY KEY(CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX
)
TABLESPACE ACCTDATA
/

CREATE INDEX IX_PROVIDERBANKACCOUNT_BANKACCOUNTKEY ON PROVIDERBANKACCOUNT (CNY#, BANKACCOUNTKEY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_PROVIDERBANKACCOUNT_CREATEDBY ON PROVIDERBANKACCOUNT (CNY#, CREATEDBY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_PROVIDERBANKACCOUNT_MODIFIEDBY ON PROVIDERBANKACCOUNT (CNY#, MODIFIEDBY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_PROVIDERBANKACCOUNT_PROVIDERKEY ON PROVIDERBANKACCOUNT (CNY#, PROVIDERKEY) TABLESPACE ACCTINDX
/

CREATE TABLE PROVIDERVENDOR (
    CNY# NUMBER(15) CONSTRAINT NN_PROVIDERVENDOR_CNY NOT NULL ENABLE,
    RECORD# NUMBER(15) CONSTRAINT NN_PROVIDERVENDOR_RECORD NOT NULL ENABLE,
    PROVIDERKEY NUMBER(15),
    VENDORKEY NUMBER(15),
    STATE CHAR(1 CHAR),
    MESSAGE VARCHAR2(500 CHAR),
    WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT' CONSTRAINT NN_PROVIDERVENDOR_WHENCREATED NOT NULL ENABLE,
    WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT' CONSTRAINT NN_PROVIDERVENDOR_WHENMODIFIED NOT NULL ENABLE,
    CREATEDBY NUMBER(15),
    MODIFIEDBY NUMBER(15),
    STATUS CHAR(1 CHAR) DEFAULT 'F',
    CONSTRAINT FK_PROVIDERVENDOR_CNY FOREIGN KEY(CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT PK_PROVIDERVENDOR PRIMARY KEY(CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX,
    CONSTRAINT FK_PROVIDERVENDOR_PROVIDERKEY FOREIGN KEY(CNY#, PROVIDERKEY) REFERENCES PAYMENTPROVIDER(CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_PROVIDERVENDOR_VENDORKEY FOREIGN KEY(CNY#, VENDORKEY) REFERENCES VENDOR(CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_PROVIDERVENDOR_CREATEDBY FOREIGN KEY(CNY#, CREATEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_PROVIDERVENDOR_MODIFIEDBY FOREIGN KEY(CNY#, MODIFIEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE
)
TABLESPACE ACCTDATA
/

CREATE INDEX IX_PROVIDERVENDOR_PROVIDERKEY ON PROVIDERVENDOR (CNY#, PROVIDERKEY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_PROVIDERVENDOR_VENDORKEY ON PROVIDERVENDOR (CNY#, VENDORKEY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_PROVIDERVENDOR_CREATEDBY ON PROVIDERVENDOR (CNY#, CREATEDBY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_PROVIDERVENDOR_MODIFIEDBY ON PROVIDERVENDOR (CNY#, MODIFIEDBY) TABLESPACE ACCTINDX
/

CREATE TABLE PROVIDERPAYMENT (
    CNY# NUMBER(15) CONSTRAINT NN_PROVIDERPAYMENT_CNY NOT NULL ENABLE,
    RECORD# NUMBER(15) CONSTRAINT NN_PROVIDERPAYMENT_RECORD NOT NULL ENABLE,
    GUID VARCHAR2(40 CHAR),
    PAYMENTGUID VARCHAR2(60 CHAR),
    PAYMENTKEY NUMBER(15) CONSTRAINT NN_PROVIDERPAYMENT_PAYMENTKEY NOT NULL ENABLE,
    AUTHURL VARCHAR2(200 CHAR),
    STATE CHAR(1 CHAR),
    ERRORONCONFIRM CHAR(1 CHAR),
    REQUESTPAYLOAD CLOB,
    CREATEDBY NUMBER(15),
    MODIFIEDBY NUMBER(15),
    WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT' CONSTRAINT NN_PROVIDERPAYMENT_WHENCREATED NOT NULL ENABLE,
    WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT' CONSTRAINT NN_PROVIDERPAYMENT_WHENMODIFIED NOT NULL ENABLE,
    CONSTRAINT FK_PROVIDERPAYMENT_CNY FOREIGN KEY(CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT PK_PROVIDERPAYMENT PRIMARY KEY(CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX,
    CONSTRAINT FK_PROVIDERPAYMENT_PAYMENTKEY FOREIGN KEY(CNY#, PAYMENTKEY) REFERENCES PRRECORD(CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_PROVIDERPAYMENT_CREATEDBY FOREIGN KEY(CNY#, CREATEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_PROVIDERPAYMENT_MODIFIEDBY FOREIGN KEY(CNY#, MODIFIEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT UQ_PROVIDERPAYMENT_PAYMENTKEY UNIQUE(CNY#, PAYMENTKEY) USING INDEX TABLESPACE ACCTINDX
)
TABLESPACE ACCTDATA
LOB (REQUESTPAYLOAD) STORE AS SECUREFILE  LOB_PROVIDERPAYMENT_PAYLOAD  ( TABLESPACE ACCTLOB DISABLE STORAGE IN ROW)
/

CREATE INDEX IX_PROVIDERPAYMENT_CREATEDBY ON PROVIDERPAYMENT (CNY#, CREATEDBY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_PROVIDERPAYMENT_MODIFIEDBY ON PROVIDERPAYMENT (CNY#, MODIFIEDBY) TABLESPACE ACCTINDX
/

CREATE TABLE PROVIDERPAYMENTHISTORY (
    CNY# NUMBER(15) CONSTRAINT NN_PROVIDERPAYMENTHISTORY_CNY NOT NULL ENABLE,
    RECORD# NUMBER(15) CONSTRAINT NN_PROVIDERPAYMENTHISTORY_RECORD NOT NULL ENABLE,
    PROVIDERPAYMENTKEY NUMBER(15) CONSTRAINT NN_PROVIDERPAYMENTHISTORY_PROVIDERPYMTKEY NOT NULL ENABLE,
    PAYMENTKEY NUMBER(15) CONSTRAINT NN_PROVIDERPAYMENTHISTORY_PAYMENTKEY NOT NULL ENABLE,
    STATE VARCHAR2(40 CHAR),
    EXTENDEDSTATE VARCHAR2(40 CHAR),
    EXTENDEDDESCRIPTION VARCHAR2(200 CHAR),
    FUNDINGID VARCHAR(100 CHAR),
    PROVIDERREFERENCE VARCHAR2(40 CHAR),
    CREATEDBY NUMBER(15),
    MODIFIEDBY NUMBER(15),
    WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT' CONSTRAINT NN_PROVIDERPAYMENTHISTORY_WHENCREATED NOT NULL ENABLE,
    WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT' CONSTRAINT NN_PROVIDERPAYMENTHISTORY_WHENMODIFIED NOT NULL ENABLE,
    CONSTRAINT FK_PROVIDERPAYMENTHISTORY_CNY FOREIGN KEY(CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT FK_PROVIDERPAYMENTHISTORY_PROVIDERPYMTKEY FOREIGN KEY(CNY#, PROVIDERPAYMENTKEY) REFERENCES PROVIDERPAYMENT(CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_PROVIDERPAYMENTHISTORY_PAYMENTKEY FOREIGN KEY(CNY#, PAYMENTKEY) REFERENCES PRRECORD(CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT PK_PROVIDERPAYMENTHISTORY PRIMARY KEY(CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX,
    CONSTRAINT FK_PROVIDERPAYMENTHISTORY_CREATEDBY FOREIGN KEY(CNY#, CREATEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_PROVIDERPAYMENTHISTORY_MODIFIEDBY FOREIGN KEY(CNY#, MODIFIEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE
)
TABLESPACE ACCTDATA
/

CREATE INDEX IX_PROVIDERPAYMENTHISTORY_CREATEDBY ON PROVIDERPAYMENTHISTORY (CNY#, CREATEDBY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_PROVIDERPAYMENTHISTORY_MODIFIEDBY ON PROVIDERPAYMENTHISTORY (CNY#, MODIFIEDBY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_PROVIDERPAYMENTHISTORY_PROVIDERPAYMENTKEY ON PROVIDERPAYMENTHISTORY (CNY#, PROVIDERPAYMENTKEY) TABLESPACE ACCTINDX
/


------------------------------------------------------------------------------------------------------------------------
-- End
------------------------------------------------------------------------------------------------------------------------