--FAASSET Group table
CREATE TABLE FAASSETGROUP
  (
    CNY#          NUMBER(15,0) NOT NULL,
    RECORD#       NUMBER(15,0) NOT NULL,
    ID            VARCHAR2(50 CHAR) NOT NULL ENABLE,
    NAME          VARCHAR2(500 CHAR) NOT NULL ENABLE,
    DESCRIPTION   VARCHAR2(500 CHAR),
    GROUPTYPE     VARCHAR2(1 CHAR),
    MEMBERFILTERS VARCHAR2(4000 CHAR),
    LOCATIONKEY   NUMBER(15,0) DEFAULT sys_context('TMCtx', 'LOCATIONKEY'),
    DEPTKEY       NUMBER(15,0) DEFAULT sys_context('TMCtx', 'DEPTKEY'),
    WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP AT TIME ZONE 'GMT',
    WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP AT TIME ZONE 'GMT',
    CREATEDBY  NUMBER(15,0),
    MODIFIEDBY NUMBER(15,0),
    CONSTRAINT PK_FAASSETGROUP PRIMARY KEY (CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX,
    CONSTRAINT UQ_FAASSETGROUP_ID UNIQUE (CNY#, ID) USING INDEX TABLESPACE ACCTINDX,
    CONSTRAINT FK_FAASSETGROUP_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSETGROUP_CREATEDBY FOREIGN KEY (CNY#, CREATEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSETGROUP_MODIFIEDBY FOREIGN KEY (CNY#, MODIFIEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSETGROUP_DEPTKEY FOREIGN KEY (CNY#, DEPTKEY) REFERENCES DEPARTMENT (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSETGROUP_LOCATIONKEY FOREIGN KEY (CNY#, LOCATIONKEY) REFERENCES LOCATION (CNY#, RECORD#) DEFERRABLE ENABLE
  )
  TABLESPACE ACCTDATA
/

CREATE INDEX IX_FAASSETGROUP_CREATEDBY ON FAASSETGROUP (CNY#, CREATEDBY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_FAASSETGROUP_MODIFIEDBY ON FAASSETGROUP (CNY#, MODIFIEDBY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_FAASSETGROUP_DEPTKEY ON FAASSETGROUP (CNY#, DEPTKEY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_FAASSETGROUP_LOCATIONKEY ON FAASSETGROUP (CNY#, LOCATIONKEY) TABLESPACE ACCTINDX
/

---Table for FAASSETgroupmemebrs
CREATE TABLE FAASSETGRPMEMBERS
  (
    CNY#        NUMBER(15,0) NOT NULL,
    RECORD#     NUMBER(15,0) NOT NULL,
    GROUPKEY    NUMBER(15,0) NOT NULL ENABLE,
    ASSETKEY    NUMBER(15,0) NOT NULL ENABLE,
    SORTORD     NUMBER(8,0) NOT NULL ENABLE,
    WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP AT TIME ZONE 'GMT',
    WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP AT TIME ZONE 'GMT',
    CREATEDBY  NUMBER(15,0),
    MODIFIEDBY NUMBER(15,0),
    CONSTRAINT PK_FAASSETGRPMEMBERS PRIMARY KEY (CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX,
    CONSTRAINT UQ_FAASSETGRPMEMBERS_FAASSET UNIQUE (CNY#, GROUPKEY, ASSETKEY) USING INDEX TABLESPACE ACCTINDX,
    CONSTRAINT UQ_FAASSETGRPMEMBERS_SORTORD UNIQUE (CNY#, GROUPKEY, SORTORD) USING INDEX TABLESPACE ACCTINDX,
    CONSTRAINT FK_FAASSETGRPMEMBERS_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSETGRPMEMBERS_GROUP FOREIGN KEY (CNY#, GROUPKEY) REFERENCES FAASSETGROUP (CNY#, RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSETGRPMEMBERS_FAASSET FOREIGN KEY (CNY#, ASSETKEY) REFERENCES FAASSET (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSETGRPMEMB_CREATEDBY FOREIGN KEY (CNY#, CREATEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSETGRPMEMB_MODIFIEDBY FOREIGN KEY (CNY#, MODIFIEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE
  )
  TABLESPACE ACCTDATA
/

CREATE INDEX IX_FAASSETGRPMEMBERS_GROUPKEY ON FAASSETGRPMEMBERS (CNY#, GROUPKEY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_FAASSETGRPMEMB_ASSETKEY ON FAASSETGRPMEMBERS (CNY#, ASSETKEY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_FAASSETGRPMEMB_CREATEDBY ON FAASSETGRPMEMBERS (CNY#, CREATEDBY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_FAASSETGRPMEMB_MODIFIEDBY ON FAASSETGRPMEMBERS (CNY#, MODIFIEDBY) TABLESPACE ACCTINDX
/

