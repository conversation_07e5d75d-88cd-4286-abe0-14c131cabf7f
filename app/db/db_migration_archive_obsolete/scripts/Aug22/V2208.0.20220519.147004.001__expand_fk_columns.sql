ALTER TABLE CONTRACT
MODIFY (
    DOCXSLKEY NUMBER(15,0),
    MEAPRCLSTKEY NUMBER(15,0),
    PRCLSTKEY NUMBER(15,0)
    )
/

ALTER TABLE CONTRACTALLOCATIONDETAIL MODIFY CONTRACTBUND<PERSON>ENTRYKEY NUMBER(15,0)
/

ALTER TABLE CONTRACTDETAIL 
MODIFY (
    DRUNBILLEDACCTKEY NUMBER(15,0),
    ITEMPRCLSTKEY NUMBER(15,0),
    REVENUETEMPLATEKEY NUMBER(15,0)   
    )
/

ALTER TABLE CONTRACTRUNGROUP 
MODIFY (
    CREATEDBY NUMBER(15,0),
    MODIFIEDBY NUMBER(15,0)
    )
/

drop index IX_CNTRTSCHDLEENTRY_GLBKEY
/

ALTER TABLE CONTRACTSCHEDULEENTRY MODIFY GLBATCHKEY NUMBER(15,0)
/

create unique index IX_CNTRTSCHDLEENTRY_GLBKEY on CONTRACTSCHEDULEENTRY
(
    case when glbatchkey is not null then cny# end,
    case when glbatchkey is not null then glbatchkey end
) tablespace ACCTINDX
/

ALTER TABLE GENINVOICELINE 
MODIFY (
    DOCENTRYKEY NUMBER(15,0),
    PRENTRYKEY NUMBER(15,0),
    TSENTRYKEY NUMBER(15,0)    
    )
/

ALTER TABLE GENINVOICELINEANDTSENTRY 
MODIFY (
    BILLINGSCHEDULEENTRYKEY NUMBER(15,0),
    CONTRACTDETAILKEY NUMBER(15,0),
    TSENTRYKEY NUMBER(15,0)    
    )
/