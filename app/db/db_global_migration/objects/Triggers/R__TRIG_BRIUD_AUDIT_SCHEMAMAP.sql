CREATE OR <PERSON><PERSON><PERSON>CE EDITIONABLE TRIGGER BRIUD_AUDIT_SCHEMAMAP
BEFORE DELETE OR INSERT OR UPDATE
ON SCHEMAMAP
REFERENCING NEW AS NEW OLD AS OLD
FOR EACH ROW
DECLARE
  L_CNY#              NUMBER(15)  ;
  L_ACTION            VARCHAR(10);
  L_NEW_TITLE         VARCHAR2(40 CHAR);
  L_OLD_TITLE         VARCHAR2(40 CHAR);
  L_NEW_STATUS        CHAR(1 CHAR);
  L_OLD_STATUS        CHAR(1 CHAR);
  L_NEW_TYPE          VARCHAR2(15 CHAR);
  L_OLD_TYPE          VARCHAR2(15 CHAR);
  L_NEW_CONTACTEMAIL  VARCHAR2(100 CHAR);
  L_OLD_CONTACTEMAIL  VARCHAR2(100 CHAR);
  L_NEW_DBID          NUMBER(8);
  L_OLD_DBID          NUMBER(8);
BEGIN
      if INSERTING then
         L_CNY# := :NEW.cny# ;
         L_ACTION            := 'INSERT' ;
         L_NEW_TITLE         := :NEW.TITLE ;
         L_OLD_TITLE         := NULL ;
         L_NEW_STATUS        := :NEW.STATUS ;
         L_OLD_STATUS        := NULL;
         L_NEW_TYPE          := :NEW.TYPE;
         L_OLD_TYPE          := NULL;
         L_NEW_CONTACTEMAIL  := :NEW.CONTACTEMAIL;
         L_OLD_CONTACTEMAIL  := NULL;
         L_NEW_DBID          := :NEW.DATABASEID;
         L_OLD_DBID          := NULL;
      elsif UPDATING then
         L_CNY# := :NEW.cny# ;
         L_ACTION            := 'UPDATE' ;
         L_NEW_TITLE         := :NEW.TITLE ;
         L_OLD_TITLE         := :OLD.TITLE ;
         L_NEW_STATUS        := :NEW.STATUS ;
         L_OLD_STATUS        := :OLD.STATUS ;
         L_NEW_TYPE          := :NEW.TYPE;
         L_OLD_TYPE          := :OLD.TYPE;
         L_NEW_CONTACTEMAIL  := :NEW.CONTACTEMAIL;
         L_OLD_CONTACTEMAIL  := :OLD.CONTACTEMAIL;
         L_NEW_DBID          := :NEW.DATABASEID;
         L_OLD_DBID          := :OLD.DATABASEID;
      else
         L_CNY# := :OLD.cny# ;
         L_ACTION            := 'DELETE' ;
         L_NEW_TITLE         :=  NULL ;
         L_OLD_TITLE         := :OLD.TITLE ;
         L_NEW_STATUS        :=  NULL ;
         L_OLD_STATUS        := :OLD.STATUS ;
         L_NEW_TYPE          :=  NULL ;
         L_OLD_TYPE          := :OLD.TYPE;
         L_NEW_CONTACTEMAIL  :=  NULL ;
         L_OLD_CONTACTEMAIL  := :OLD.CONTACTEMAIL;
         L_NEW_DBID          :=  NULL ;
         L_OLD_DBID          := :OLD.DATABASEID;
      end if;
      insert into  AU_SCHEMAMAP(
          CNY#,
          ACTION,
          WHEN,
          NEW_TITLE,
          OLD_TITLE,
          NEW_STATUS,
          OLD_STATUS,
          NEW_TYPE,
          OLD_TYPE,
          NEW_CONTACTEMAIL,
          OLD_CONTACTEMAIL,
          NEW_DBID,
          OLD_DBID)
      values (
         L_CNY# ,
         L_ACTION,
         SYSDATE,
         L_NEW_TITLE,
         L_OLD_TITLE,
         L_NEW_STATUS,
         L_OLD_STATUS,
         L_NEW_TYPE  ,
         L_OLD_TYPE  ,
         L_NEW_CONTACTEMAIL ,
         L_OLD_CONTACTEMAIL ,
         L_NEW_DBID         ,
         L_OLD_DBID         );
end;
/

