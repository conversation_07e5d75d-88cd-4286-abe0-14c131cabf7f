-- add WHENCREATED and WHENMODIFIED columns to be used during replication of DATABASE table
DECLARE
n pls_integer :=0;
BEGIN
SELECT count(*) into n FROM all_tab_cols WHERE owner = sys_context('userenv', 'current_schema') and column_name = 'WHENMODIFIED' and table_name = 'DATABASE' ;
if n = 0 then
    -- add new colum TABLE_TYPE with a default value
    EXECUTE IMMEDIATE 'alter table DATABASE add (WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP AT TIME ZONE ''GMT'')';
commit;
end if;
END;
/


-- create DATABASELOG table to be used during replication of DATABASE table
declare
n NUMBER;

begin
SELECT count(*) into n from all_tables where owner = sys_context('userenv', 'current_schema') and table_name = 'DATABASELOG';
IF (n <= 0) THEN
    execute immediate '
CREATE TABLE DATABASELOG
(
    DATABASEID NUMBER(8,0) CONSTRAINT NN_DATABASELOG_REC# NOT NULL ENABLE,
    WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP AT TIME ZONE ''GMT'',
    CONSTRAINT PK_DATABASELOG_LOG PRIMARY KEY (DATABASEID) USING INDEX TABLESPACE ACCTGLOBINDX ENABLE
) TABLESPACE ACCTGLOBDATA
    ';

    -- add DATABASE log table in APP_REPLICATED_TABLES
execute immediate '
update APP_REPLICATED_TABLES
set LOG_TABLE = ''databaselog''
where REPLICATED_TABLE = ''database''
';
END IF;
end;
/
