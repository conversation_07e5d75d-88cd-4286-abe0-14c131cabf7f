<?php

namespace tests\source\gl;

use FinancialReportHelper;
use unitTest\core\UnitTestBase;

class BaseGLTotalQueryTest extends UnitTestBase
{

    /**
     * @var array[]
     */
    private static $constraintsDataSet = [
        1 => [
            'LOCATION#' => [ 1, 2, 3 ],
            'CUSTDIM1'  => [ 4, 5, 6 ],
        ],
        2 => [
            'LOCATION#' => [ 1, 2, 3 ],
            'CUSTDIM1'  => [ 7, 8, 9 ],
        ],
        3 => [
            'LOCATION#' => [ 9, 4, 6 ],
            'CUSTDIM1'  => [ 7, 8, 9 ],
        ],
    ];

    /**
     * @var int[] $dummyKids
     */
    private static $dummyKids = [ 11, 22, 33, 44 ];

    protected function setUp() : void
    {
        FinancialReportHelper::getInstance()
                             ->resetCurrentQueryIndex();

    }

    /**
     * @covers FinancialReportHelper::getInstance
     *
     * @return void
     */
    public function testFinancialReportObj()
    {
        $this->assertInstanceOf(FinancialReportHelper::class, FinancialReportHelper::getInstance());
    }

    /**
     * @covers FinancialReportHelper::getBaseGLTotalQueryByIndex
     * @covers FinancialReportHelper::incrementCurrentQueryIndex
     * @covers FinancialReportHelper::getCurrentQueryIndex
     *
     * @return void
     */
    public function testFinancialReportHelperNewBaseGLTotalQuery()
    {
        $helper = FinancialReportHelper::getInstance();
        $query = $helper->newBaseGLTotalQuery();
        $this->assertInstanceOf(\BaseGLTotalQuery::class, $query);
        $this->assertEquals(1, $query->getQueryId());
        $this->assertEquals($helper->getBaseGLTotalQueryByIndex(1), $query);
    }

    /**
     * @dataProvider dataProviderTestBaseGLTotalQueryGroupKey
     *
     * @covers       BaseGLTotalQuery
     *
     * @return void
     */
    public function testBaseGLTotalQueryGroupKey(
        int    $queryId,
        int    $periodkey,
        array  $constraints,
        string $asof,
        string $extraGroup,
        int    $columnNumber,
        array  $kids,
        string $expectedResult
    )
    {
        $query = $this->getMockedBaseGLTotalQuery($queryId, $periodkey, $constraints,
                                                  $asof, $extraGroup, $kids, $columnNumber);
        $this->assertEquals($expectedResult, $query->getGroupKey());
    }

    /**
     * @param int    $queryId
     * @param int    $periodkey
     * @param array  $constraints
     * @param string $asof
     * @param string $extraGroup
     * @param int    $columnNumber
     *
     * @return \BaseGLTotalQuery|\PHPUnit\Framework\MockObject\MockObject
     */
    function getMockedBaseGLTotalQuery(
        int    $queryId,
        int    $periodkey,
        array  $constraints,
        string $asof,
        string $extraGroup,
        array  $kids,
        int    $columnNumber
    )
    {
        $methods = [ 'getColumnParams' ];
        $query = $this->getMockBuilder(\BaseGLTotalQuery::class)
                      ->setConstructorArgs([ $queryId ])
                      ->onlyMethods($methods)
                      ->getMock();
        $query->method('getColumnParams')
              ->willReturn([ 'PERIOD#' => $periodkey ]);
        $query->setConstraintArray($constraints);
        $query->setAsof($asof);
        $query->setColumnOrder($columnNumber);
        $query->setExtragroup($extraGroup);
        $query->setKids($kids);
        $query->setGroupKey();

        return $query;
    }

    /**
     * @return array
     */
    function dataProviderTestBaseGLTotalQueryGroupKey() : array
    {
        return [
            // Case 1 : With all values to form groupkey
            [
                1,
                2,
                [
                    'LOCATION#' => [ 1, 2, 3 ],
                    'CUSTDIM1'  => [ 4, 5, 6 ],
                    'DEPT#'     => [ 7, 8, 9 ],
                ],
                'E',
                'group by LOCATION#',
                1,
                [ 11, 22, 33, 44 ],
                '1#~#E#~#LOCATION#:CUSTDIM1:DEPT##~#group by LOCATION##~##~##~#',
            ],
            // Case 2 : With empty constraints and group by's
            [
                1,
                2,
                [],
                'E',
                '',
                1,
                [ 11, 22, 33, 44 ],
                '1#~#E#~##~##~##~##~#',
            ],
        ];
    }

    /**
     * @dataProvider datProviderTestBaseGLTotalQueryBuilder
     *
     * @covers       \BaseGLTotalQueryBuilder::mergeQueries
     * @covers       \BaseGLTotalQueryBuilder::findCommonConstraints
     * @covers       \BaseGLTotalQueryBuilder::rebuildQueryParams
     * @covers       \BaseGLTotalQueryBuilder::beforeBuild
     *
     * @return void
     */
    public function testBaseGLTotalQueryBuilder($queries, $expected)
    {
        $builder = $this->getMockBuilder(\BaseGLTotalQueryBuilder::class)
                        ->onlyMethods([ 'dumpIntoEntityCache', 'clearEntityCache' ])
                        ->setConstructorArgs([ $queries ])
                        ->getMock();
        $builder->method('dumpIntoEntityCache')
                ->willReturn(true);
        $builder->rebuildQueryParams();

        $mergedQuery = $builder->getMergedQuery();
        $this->assertInstanceOf(\BaseGLTotalQuery::class, $mergedQuery);
        $this->assertEquals($expected['CONSTRAINTS'], $mergedQuery->getCommonConstraints());
        $this->assertEquals($expected['KIDS'], $mergedQuery->getKids());
        $this->assertEquals($expected['EXTRATABLES'], $mergedQuery->getExtraTables());
        $this->assertEquals($expected['CONSTRAINTCLAUSE'], $mergedQuery->getConstraintsClause());
        $this->assertEquals($expected['EXTRAGROUP'], $mergedQuery->getExtragroup());
        $this->assertEquals($expected['EXTRASELECT'], $mergedQuery->getExtraselect());
        $this->assertNotEmpty($mergedQuery->getEntityCacheValues());
    }

    /**
     * @return array[]
     */
    public function datProviderTestBaseGLTotalQueryBuilder() : array
    {
        $query1 = $this->getMockedBaseGLTotalQuery(
            1, 123, self::$constraintsDataSet[1], 'E', '', self::$dummyKids, 1
        );
        $query2 = $this->getMockedBaseGLTotalQuery(
            2, 123, self::$constraintsDataSet[2], 'E', '', self::$dummyKids, 1
        );
        $query3 = $this->getMockedBaseGLTotalQuery(
            2, 123, self::$constraintsDataSet[3], 'E', '', self::$dummyKids, 1
        );

        return [
            // Case 1: Same queries with 1 common constraint
            [
                [ $query1, $query2 ],
                [
                    'CONSTRAINTS'      => [ 'LOCATION#' ],
                    'KIDS'             => self::$dummyKids,
                    'EXTRATABLES'      => ', entitycache account# , entitycache CUSTDIM1',
                    'CONSTRAINTCLAUSE' => [
                        0 => ' and account# = account#.entityno and account#.entitytype = :0  and LOCATION# in (  :1, :2, :3 )  and CUSTDIM1 = CUSTDIM1.entityno and CUSTDIM1.entitytype = :4 and CUSTDIM1.entitytempno (+) = ACCOUNT#.entitytempno ',
                        1 =>
                            [
                                0 => 'ACCOUNT#',
                                1 => 1,
                                2 => 2,
                                3 => 3,
                                4 => 'CUSTDIM1',
                            ],

                        2 => 5,
                    ],
                    'EXTRAGROUP'       => 'group by  account#.entitytempno ',
                    'EXTRASELECT'      => ' account#.entitytempno as queryid,  ',
                ],
            ],
            // Case 2: Same queries with 0 common constraint
            [
                [ $query1, $query3 ],
                [
                    'CONSTRAINTS'      => [],
                    'KIDS'             => self::$dummyKids,
                    'EXTRATABLES'      => ', entitycache account# , entitycache LOCATION#, entitycache CUSTDIM1',
                    'CONSTRAINTCLAUSE' => [
                        0 => ' and account# = account#.entityno and account#.entitytype = :0  and LOCATION# = LOCATION#.entityno and LOCATION#.entitytype = :1 and LOCATION#.entitytempno (+) = ACCOUNT#.entitytempno  and CUSTDIM1 = CUSTDIM1.entityno and CUSTDIM1.entitytype = :2 and CUSTDIM1.entitytempno (+) = LOCATION#.entitytempno ',
                        1 =>
                            [
                                0 => 'ACCOUNT#',
                                1 => 'LOCATION#',
                                2 => 'CUSTDIM1',
                            ],

                        2 => 3,
                    ],
                    'EXTRAGROUP'       => 'group by  account#.entitytempno , account#.entitytempno ',
                    'EXTRASELECT'      => ' account#.entitytempno as queryid,  account#.entitytempno as queryid,   ',
                ],
            ],
        ];
    }

}
