<?php

namespace tests\source\gl;

use GLAcctAllocationRunManager;
require_once 'GLAcctAllocationRunManagerDataProvider.php';
use PHPUnit\Framework\MockObject\MockObject;
use unitTest\core\TestInfo;
use unitTest\core\UnitTestBase;
use unitTest\core\Utils;
use unitTest\core\DataProviderBuilder as DataProviderBuilder;
use unitTest\core\TestKeySet as TestKeySet;

/**
 * @group unit
 */
class GLAcctAllocationRunManagerTest extends UnitTestBase
{

    use GLAcctAllocationRunManagerDataProvider;

    /* @var MockObject|GLAcctAllocationRunManager $glGLacctAllocRunMockObj */
    private $glGLacctAllocRunMockObj;

    /* @var string[][] $overrideDimensions */
    private $overrideDimensions = [
        "OVERRIDEDEPARTMENT" => [
            "value" => "DEPARTMENT",
            "dimkey" => "DEPARTMENT"
        ],
        "OVERRIDELOCATION" => [
            "value" => "LOCATION",
            "dimkey" => "LOCATION"
        ],
        "OVERRIDEPROJECT" => [
            "value" => "PROJECTID",
            "dimkey" => "PROJECT"
        ],
        "OVERRIDECUSTOMER" => [
            "value" => "CUSTOMERID",
            "dimkey" => "CUSTOMER"
        ],
        "OVERRIDEVENDOR" => [
            "value" => "VENDORID",
            "dimkey" => "VENDOR"
        ],
        "OVERRIDEEMPLOYEE" => [
            "value" => "EMPLOYEEID",
            "dimkey" => "EMPLOYEE"
        ],
        "OVERRIDEITEM" => [
            "value" => "ITEMID",
            "dimkey" => "ITEM"
        ],
        "OVERRIDECLASS" => [
            "value" => "CLASSID",
            "dimkey" => "CLASS"
        ],
        "OVERRIDECONTRACT" => [
            "value" => "CONTRACTID",
            "dimkey" => "CONTRACT"
        ],
        "OVERRIDEWAREHOUSE" => [
            "value" => "WAREHOUSEID",
            "dimkey" => "WAREHOUSE"
        ],
        "GLDIMSPEND" => [
            "value" => "custdim_10003",
            "dimkey" => "GLDIMSPEND"
        ],
        "GLDIMUDD_ESCL" => [
            "value" => "custdim_10002",
            "dimkey" => "GLDIMUDD_ESCL",
        ]
    ];

    /* @var string[] $filterdimension */
    private $filterdimension = [
        "DEPTNO" => "dept",
        "LOCNO" => "loc",
        "PROJECTID" => "PROJECTID",
        "CUSTOMERID" => "CUSTOMERID",
        "VENDORID" => "VENDORID",
        "EMPLOYEEID" => "EMPLOYEEID",
        "ITEMID" => "ITEMID",
        "CLASSID" => "CLASSID",
        "CONTRACTID" => "CONTRACTID",
        "WAREHOUSEID" => "WAREHOUSEID",
        "GLDIMSPEND" => "custdim_10003",
        "GLDIMUDD_ESCL" => "custdim_10002",
    ];

    /* @var string[] $dimensionName2IDMap */
    private $dimensionName2IDMap = [
        "DEPARTMENT" => "DEPARTMENTID",
        "LOCATION" => "LOCATIONID",
        "PROJECT" => "PROJECTID",
        "CUSTOMER" => "CUSTOMERID",
        "VENDOR" => "VENDORID",
        "EMPLOYEE" => "EMPLOYEEID",
        "ITEM" => "ITEMID",
        "CLASS" => "CLASSID",
        "CONTRACT" => "CONTRACTID",
        "WAREHOUSE" => "WAREHOUSEID",
        "GLDIMSPEND" => "custdim_10003",
        "GLDIMUDD_ESCL" => "custdim_10002",
    ];

    /**
     * Executes once before every test.
     *
     */
    public function setUp() : void
    {
        parent::setUp();
        $this->createGLAcctAllocationRunMockObj();
    }

    /**
     * @dataProvider dataProviderTestVerifySumByAddingItemValuesAndAdjustDiff
     *
     * @covers GLAcctAllocationRunManager::verifySumByAddingItemValuesAndAdjustDiff
     * @covers GLAcctAllocationRunManager::balanceItemsByAddingPenny
     * @covers GLAcctAllocationRunManager::balanceItemsByRemovingPenny
     *
     * @param array  $items
     * @param string $sum
     * @param string $caclFld
     * @param int    $precision
     * @param array  $expected
     *
     */
    public function testVerifySumByAddingItemValuesAndAdjustDiff(
        array $items,
        string $sum,
        string $caclFld,
        int $precision,
        array $expected
    ) : void {
        $result = $this->invokePrivate(
            $this->glGLacctAllocRunMockObj, 'verifySumByAddingItemValuesAndAdjustDiff',
            $items,
            $sum,
            $caclFld,
            $precision
        );

        self::assertEquals($expected, $result);
    }

    /**
     * @return array
     */
    public function dataProviderTestVerifySumByAddingItemValuesAndAdjustDiff() : array
    {
        return [
            [
                [
                    [
                        'LOCATION' => '1',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => 50
                    ],
                    [
                        'LOCATION' => '2',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => 50.12
                    ],
                ],
                '100',
                'AMOUNT',
                2,
                [
                    [
                        'LOCATION' => '1',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => 49.94
                    ],
                    [
                        'LOCATION' => '2',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => 50.06
                    ],
                ]
            ],
            [
                [
                    [
                        'LOCATION' => '1',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '50'
                    ],
                    [
                        'LOCATION' => '2',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '50.12'
                    ],
                ],
                '100',
                'AMOUNT',
                2,
                [
                    [
                        'LOCATION' => '1',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '49.94'
                    ],
                    [
                        'LOCATION' => '2',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '50.06'
                    ],
                ]
            ],
            [
                [
                    [
                        'LOCATION' => '1',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '49.76'
                    ],
                    [
                        'LOCATION' => '2',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '50'
                    ],
                ],
                '100',
                'AMOUNT',
                2,
                [
                    [
                        'LOCATION' => '1',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '49.88'
                    ],
                    [
                        'LOCATION' => '2',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '50.12'
                    ],
                ]
            ],
            [
                [
                    [
                        'LOCATION' => '1',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '49.12'
                    ],
                    [
                        'LOCATION' => '2',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '50.123'
                    ],
                ],
                '100.2134',
                'AMOUNT',
                4,
                [
                    [
                        'LOCATION' => '1',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => 49.6052
                    ],
                    [
                        'LOCATION' => '2',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => 50.6082
                    ],
                ]
            ],
            [
                [
                    [
                        'LOCATION' => '1',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '49.12'
                    ],
                    [
                        'LOCATION' => '2',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '50.123'
                    ],
                ],
                '100.21',
                'AMOUNT',
                4,
                [
                    [
                        'LOCATION' => '1',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '49.6035'
                    ],
                    [
                        'LOCATION' => '2',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '50.6065'
                    ],
                ]
            ],
            [
                [
                    [
                        'LOCATION' => '1',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '50.12'
                    ],
                    [
                        'LOCATION' => '2',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '50.123'
                    ],
                ],
                '100.2134',
                'AMOUNT',
                4,
                [
                    [
                        'LOCATION' => '1',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '50.1052'
                    ],
                    [
                        'LOCATION' => '2',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '50.1082'
                    ],
                ]
            ],
            [
                [
                    [
                        'LOCATION' => '1',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '50.12'
                    ],
                    [
                        'LOCATION' => '2',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '50.123'
                    ],
                ],
                '100.21',
                'AMOUNT',
                4,
                [
                    [
                        'LOCATION' => '1',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '50.1035'
                    ],
                    [
                        'LOCATION' => '2',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '50.1065'
                    ],
                ]
            ],
            [
                [
                    [
                        'LOCATION' => '1',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '49.12'
                    ],
                    [
                        'LOCATION' => '2',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '50.123'
                    ],
                ],
                '100.21',
                'AMOUNT',
                4,
                [
                    [
                        'LOCATION' => '1',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '49.6035'
                    ],
                    [
                        'LOCATION' => '2',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '50.6065'
                    ],
                ]
            ],
            [
                [
                    [
                        'LOCATION' => '1',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '49.12'
                    ],
                    [
                        'LOCATION' => '2',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '50.123'
                    ],
                ],
                '102.21',
                'AMOUNT',
                4,
                [
                    [
                        'LOCATION' => '1',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '50.6035'
                    ],
                    [
                        'LOCATION' => '2',
                        'DEPARTMENT' => '2',
                        'AMOUNT' => '51.6065'
                    ],
                ]
            ],
        ];
    }

    /**
     * @dataProvider dataProviderTestGetDimensionsTreatment
     *
     * @covers GLAcctAllocationRunManager::getDimensionsTreatment
     *
     * @param array $alloDefObj
     * @param array $expected
     *
     */
    public function testGetDimensionsTreatment(array $alloDefObj, array $expected) : void
    {
        $alongRideDims = $preservedDims = $notInvitedDims = [];

        $method = Utils::getAccessibleReflectionMethod(GLAcctAllocationRunManager::class, "getDimensionsTreatment");
        $method->invokeArgs(
            $this->glGLacctAllocRunMockObj,
            [
                $alloDefObj,
                &$alongRideDims,
                &$preservedDims,
                &$notInvitedDims,
                &$perDimValDims
            ]
        );

        self::assertEquals($expected['notInvitedDims'], $notInvitedDims);
        self::assertEquals($expected['preservedDims'], $preservedDims);
        self::assertEquals($expected['alongRideDims'], $alongRideDims);
        self::assertEquals($expected[\GLAcctAllocationManager::PER_DIMENSION_VALUE], $perDimValDims);
    }

    /**
     * @return array
     */
    public function dataProviderTestGetDimensionsTreatment() : array
    {
        return [
            [
                [
                    'ACCTALLOCATIONID' => 'DIFF_PERIOD',
                    'FOCUSLOCATION' => 'Preserve values',
                    'FOCUSDEPARTMENT' => 'Allocation focus',
                    'FOCUSPROJECT' => 'Not considered',
                    'FOCUSCUSTOMER' => 'Not considered',
                    'FOCUSVENDOR' => 'Not considered',
                    'FOCUSEMPLOYEE' => 'Not considered',
                    'FOCUSITEM' => 'Not considered',
                    'FOCUSCLASS' => 'Not considered',
                    'FOCUSCONTRACT' => 'Allocation focus',
                    'FOCUSWAREHOUSE' => 'Allocation focus',
                    'FOCUSGLDIMUDD_ESCL' => 'Allocation focus',
                    'FOCUSGLDIMSPEND' => 'Not considered',
                ],
                [
                    'alongRideDims' => [
                        'DEPARTMENT',
                        'CONTRACT',
                        'WAREHOUSE',
                        'GLDIMUDD_ESCL'
                    ],
                    'preservedDims' => [
                        'LOCATION',
                    ],
                    'notInvitedDims' => [
                        'PROJECT',
                        'CUSTOMER',
                        'VENDOR',
                        'EMPLOYEE',
                        'ITEM',
                        'CLASS',
                        'GLDIMSPEND',
                    ],
                    \GLAcctAllocationManager::PER_DIMENSION_VALUE => []
                ]
            ],
            [
                [
                    'ACCTALLOCATIONID' => 'DIFF_PERIOD',
                    'FOCUSLOCATION' => 'Preserve values',
                    'FOCUSDEPARTMENT' => 'Allocation focus',
                    'FOCUSPROJECT' => 'Not considered',
                    'FOCUSCUSTOMER' => 'Not considered',
                    'FOCUSVENDOR' => 'Not considered',
                    'FOCUSEMPLOYEE' => 'Not considered',
                    'FOCUSITEM' => 'Not considered',
                    'FOCUSCLASS' => 'Allocation focus',
                    'FOCUSCONTRACT' => 'Allocation focus',
                    'FOCUSWAREHOUSE' => 'Allocation focus',
                    'FOCUSGLDIMUDD_ESCL' => 'Allocation focus',
                    'FOCUSGLDIMSPEND' => 'Not considered',
                ],
                [
                    'alongRideDims' => [
                        'DEPARTMENT',
                        'CLASS',
                        'CONTRACT',
                        'WAREHOUSE',
                        'GLDIMUDD_ESCL'
                    ],
                    'preservedDims' => [
                        'LOCATION',
                    ],
                    'notInvitedDims' => [
                        'PROJECT',
                        'CUSTOMER',
                        'VENDOR',
                        'EMPLOYEE',
                        'ITEM',
                        'GLDIMSPEND',
                    ],
                    \GLAcctAllocationManager::PER_DIMENSION_VALUE => []
                ]
            ]
        ];
    }

    /**
     * @dataProvider dataProviderTestParseDimFilters
     *
     * @covers GLAcctAllocationRunManager::parseDimFilters
     *
     * @param array $source
     * @param array $expected
     *
     */
    public function testParseDimFilters(array $source, array $expected) : void
    {
        $srcFilters = [];

        $method = Utils::getAccessibleReflectionMethod(GLAcctAllocationRunManager::class, "parseDimFilters");
        $method->invokeArgs(
            $this->glGLacctAllocRunMockObj,
            [
                $source,
                &$srcFilters
            ]
        );

        self::assertEquals($expected, $srcFilters);
    }

    /**
     * @return array
     */
    public function dataProviderTestParseDimFilters() : array
    {
        return [
            [
                [
                    "RECORDNO" => "91",
                    "GLACCTALLOCATIONID" => "apidemo1535347088",
                    "GLACCTALLOCATIONKEY" => "170",
                    "GLACCTGRP" => "SaleGrp",
                    "GLACCTGRPKEY" => "144",
                    "PERCENT2ALLOCATE" => "100",
                    "TIMEPERIOD" => "Current month",
                    "GLBOOKID" => "ACCRUAL",
                    "LOCATIONDIMKEY" => "1",
                    "LOCNO" => "1",
                    "LOCATIONNAME" => "United States of America",
                    "LOCATIONGROUPKEY" => null,
                    "LOCGRPNO" => null,
                    "LOCATIONGRPNAME" => null,
                    "DEPARTMENTDIMKEY" => null,
                    "DEPTNO" => null,
                    "DEPARTMENTNAME" => null,
                    "DEPARTMENTGROUPKEY" => null,
                    "DEPARTMENTGRPNAME" => null,
                    "DEPTGRPNO" => null,
                    "PROJECTDIMKEY" => null,
                    "PROJECTNAME" => null,
                    "PROJECTID" => null,
                    "PROJECTGRPNAME" => null,
                    "PROJECTGRPID" => null,
                    "PROJECTGROUPKEY" => null,
                    "CUSTOMERDIMKEY" => null,
                    "CUSTOMERNAME" => null,
                    "CUSTOMERID" => null,
                    "CUSTOMERGRPNAME" => null,
                    "CUSTOMERGRPID" => null,
                    "CUSTOMERGROUPKEY" => null,
                    "VENDORDIMKEY" => null,
                    "VENDORNAME" => null,
                    "VENDORID" => null,
                    "VENDORGRPNAME" => null,
                    "VENDORGRPID" => null,
                    "VENDORGROUPKEY" => null,
                    "EMPLOYEEDIMKEY" => null,
                    "EMPLOYEENAME" => null,
                    "EMPLOYEEID" => null,
                    "EMPLOYEEGRPNAME" => null,
                    "EMPLOYEEGRPID" => null,
                    "EMPLOYEEGROUPKEY" => null,
                    "ITEMDIMKEY" => null,
                    "ITEMNAME" => null,
                    "ITEMID" => null,
                    "ITEMGRPNAME" => null,
                    "ITEMGRPID" => null,
                    "ITEMGROUPKEY" => null,
                    "CLASSDIMKEY" => null,
                    "CLASSNAME" => null,
                    "CLASSID" => null,
                    "CLASSGRPNAME" => null,
                    "CLASSGRPID" => null,
                    "CLASSGROUPKEY" => null,
                    "CONTRACTDIMKEY" => null,
                    "CONTRACTNAME" => null,
                    "CONTRACTID" => null,
                    "CONTRACTGRPNAME" => null,
                    "CONTRACTGRPID" => null,
                    "CONTRACTGROUPKEY" => null,
                    "WAREHOUSEDIMKEY" => null,
                    "WAREHOUSENAME" => null,
                    "WAREHOUSEID" => null,
                    "WAREHOUSEGRPNAME" => null,
                    "WAREHOUSEGRPID" => null,
                    "WAREHOUSEGROUPKEY" => null,
                    "GLDIMUDD_ESCL" => null,
                    "RGLDIM43288900_10051" => null,
                    "GLDIMSPEND" => null,
                    "RGLDIM43288900_10188" => null,
                ],
                [
                    "loc" => "1"
                ]
            ],
            [
                [
                    "RECORDNO" => "91",
                    "GLACCTALLOCATIONID" => "apidemo1535347088",
                    "GLACCTALLOCATIONKEY" => "170",
                    "GLACCTGRP" => "SaleGrp",
                    "GLACCTGRPKEY" => "144",
                    "PERCENT2ALLOCATE" => "100",
                    "TIMEPERIOD" => "Current month",
                    "GLBOOKID" => "ACCRUAL",
                    "LOCATIONDIMKEY" => "1",
                    "LOCNO" => "1",
                    "LOCATIONNAME" => "United States of America",
                    "LOCATIONGROUPKEY" => null,
                    "LOCGRPNO" => null,
                    "LOCATIONGRPNAME" => null,
                    "DEPARTMENTDIMKEY" => null,
                    "DEPTNO" => "3",
                    "DEPARTMENTNAME" => null,
                    "DEPARTMENTGROUPKEY" => null,
                    "DEPARTMENTGRPNAME" => null,
                    "DEPTGRPNO" => null,
                    "PROJECTDIMKEY" => null,
                    "PROJECTNAME" => null,
                    "PROJECTID" => null,
                    "PROJECTGRPNAME" => null,
                    "PROJECTGRPID" => null,
                    "PROJECTGROUPKEY" => null,
                    "CUSTOMERDIMKEY" => null,
                    "CUSTOMERNAME" => null,
                    "CUSTOMERID" => null,
                    "CUSTOMERGRPNAME" => null,
                    "CUSTOMERGRPID" => null,
                    "CUSTOMERGROUPKEY" => null,
                    "VENDORDIMKEY" => null,
                    "VENDORNAME" => null,
                    "VENDORID" => null,
                    "VENDORGRPNAME" => null,
                    "VENDORGRPID" => null,
                    "VENDORGROUPKEY" => null,
                    "EMPLOYEEDIMKEY" => null,
                    "EMPLOYEENAME" => null,
                    "EMPLOYEEID" => null,
                    "EMPLOYEEGRPNAME" => null,
                    "EMPLOYEEGRPID" => null,
                    "EMPLOYEEGROUPKEY" => null,
                    "ITEMDIMKEY" => null,
                    "ITEMNAME" => null,
                    "ITEMID" => null,
                    "ITEMGRPNAME" => null,
                    "ITEMGRPID" => null,
                    "ITEMGROUPKEY" => null,
                    "CLASSDIMKEY" => null,
                    "CLASSNAME" => null,
                    "CLASSID" => null,
                    "CLASSGRPNAME" => null,
                    "CLASSGRPID" => null,
                    "CLASSGROUPKEY" => null,
                    "CONTRACTDIMKEY" => null,
                    "CONTRACTNAME" => null,
                    "CONTRACTID" => null,
                    "CONTRACTGRPNAME" => null,
                    "CONTRACTGRPID" => null,
                    "CONTRACTGROUPKEY" => null,
                    "WAREHOUSEDIMKEY" => null,
                    "WAREHOUSENAME" => null,
                    "WAREHOUSEID" => null,
                    "WAREHOUSEGRPNAME" => null,
                    "WAREHOUSEGRPID" => null,
                    "WAREHOUSEGROUPKEY" => null,
                    "GLDIMUDD_ESCL" => null,
                    "RGLDIM43288900_10051" => null,
                    "GLDIMSPEND" => null,
                    "RGLDIM43288900_10188" => null,
                ],
                [
                    "dept" => "3",
                    "loc" => "1"
                ]
            ]
        ];
    }

    /**
     * @dataProvider dataProviderTestCreateDynamicSplit
     *
     * @covers GLAcctAllocationRunManager::createDynamicSplit
     * @covers GLAcctAllocationRunManager::getUniqueKey
     * @covers GLAcctAllocationRunManager::verifySumByAddingItemValuesAndAdjustDiff
     *
     * @param array $sourceBalance
     * @param array $preservedDims
     * @param array $expected
     *
     *
     */
    public function testCreateDynamicSplit(array $sourceBalance, array $preservedDims, array $expected) : void
    {
        $method = Utils::getAccessibleReflectionMethod(GLAcctAllocationRunManager::class, "createDynamicSplit");
        $result = $method->invokeArgs(
            $this->glGLacctAllocRunMockObj,
            [
                $sourceBalance,
                $preservedDims
            ]
        );

        self::assertEquals($expected, $result);
    }

    /**
     * @return array
     */
    public function dataProviderTestCreateDynamicSplit() : array
    {
        return [
            [
                [
                    [
                        "ACCOUNTID" => "194",
                        "ACCOUNT_NUM" => "4000",
                        "LOCATIONID" => "1",
                        "LOCATIONNAME" => "United States of America",
                        "CUSTDIM_10002__NAME" => "U1",
                        "TOTAL" => "-10",
                    ]
                ],
                [
                    "LOCATION"
                ],
                [
                    [
                        "LOCATIONID" => "1",
                        "percent" => "1",
                    ]
                ]
            ],
            [
                [
                    [
                        "ACCOUNTID" => "194",
                        "ACCOUNT_NUM" => "4000",
                        "LOCATIONID" => "1",
                        "LOCATIONNAME" => "United States of America",
                        "CUSTDIM_10002__NAME" => "U1",
                        "TOTAL" => "-10",
                    ],
                    [
                        "ACCOUNTID" => "194",
                        "ACCOUNT_NUM" => "4000",
                        "LOCATIONID" => "2",
                        "LOCATIONNAME" => "Canada",
                        "CUSTDIM_10002__NAME" => "U2",
                        "TOTAL" => "-30",
                    ]
                ],
                [
                    "LOCATION",
                    "GLDIMUDD_ESCL"
                ],
                [
                    [
                        "LOCATIONID" => "1",
                        "CUSTDIM_10002__NAME" => "U1",
                        "percent" => "0.25",
                    ],
                    [
                        "LOCATIONID" => "2",
                        "CUSTDIM_10002__NAME" => "U2",
                        "percent" => "0.75",
                    ]
                ]
            ]
        ];
    }

    /**
     * @dataProvider dataProviderTestMergeSourceNBasisSplit
     *
     * @covers GLAcctAllocationRunManager::mergeSourceNBasisSplit
     * @covers GLAcctAllocationRunManager::balanceItemsByRemovingPenny
     * @covers GLAcctAllocationRunManager::verifySumByAddingItemValuesAndAdjustDiff
     *
     * @param array $sourceSplit
     * @param array $basisSplit
     * @param array $expected
     *
     */
    public function testMergeSourceNBasisSplit(array $sourceSplit, array $basisSplit, array $expected) : void
    {
        $method = Utils::getAccessibleReflectionMethod(
            GLAcctAllocationRunManager::class,
            'mergeSourceNBasisSplit'
        );
        $result = $method->invokeArgs(
            $this->glGLacctAllocRunMockObj,
            [
                $sourceSplit,
                $basisSplit
            ]
        );

        self::assertEquals($expected, $result);
    }
    /**
     * @return array
     */
    public function dataProviderTestMergeSourceNBasisSplit() : array
    {
        return [
            [
                [
                    [
                        "LOCATIONID" => "1",
                        "percent" => "1.00",
                    ]
                ],
                [
                    [
                        "CUSTDIM_10002NAME" => "U1",
                        "percent" => "0.50",
                    ],
                    [
                        "CUSTDIM_10002NAME" => "U2",
                        "percent" => "0.50",
                    ]
                ],
                [
                    [
                        "LOCATIONID" => "1",
                        "CUSTDIM_10002NAME" => "U1",
                        "percent" => "0.5",
                    ],
                    [
                        "LOCATIONID" => "1",
                        "CUSTDIM_10002NAME" => "U2",
                        "percent" => "0.5",
                    ]
                ]
            ],
            [
                [
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.25",
                    ],
                    [
                        "LOCATIONID" => "2",
                        "percent" => "0.75",
                    ]
                ],
                [
                    [
                        "CUSTDIM_10002NAME" => "U1",
                        "percent" => "0.50",
                    ],
                    [
                        "CUSTDIM_10002NAME" => "U2",
                        "percent" => "0.50",
                    ]
                ],
                [
                    [
                        "LOCATIONID" => "1",
                        "CUSTDIM_10002NAME" => "U1",
                        "percent" => "0.12",
                    ],
                    [
                        "LOCATIONID" => "1",
                        "CUSTDIM_10002NAME" => "U2",
                        "percent" => "0.12",
                    ],
                    [
                        "LOCATIONID" => "2",
                        "CUSTDIM_10002NAME" => "U1",
                        "percent" => "0.38",
                    ],
                    [
                        "LOCATIONID" => "2",
                        "CUSTDIM_10002NAME" => "U2",
                        "percent" => "0.38",
                    ]
                ]
            ],
        ];
    }

    /**
     * Also, check testCreateTargetLineItemsAddionalTestCase() for additional test cases
     *
     * @dataProvider dataProvider_testCreateTargetLineItems
     *
     * @covers GLAcctAllocationRunManager::createTargetLineItems
     * @covers GLAcctAllocationRunManager::debugLog
     *
     * @param array  $balance
     * @param float $percentage2allocate
     * @param string $glacctno
     * @param array  $split
     * @param string[] $currencyDetails
     * @param array  $expected
     *
     */
    public function testCreateTargetLineItems(array $balance, float $percentage2allocate, string $glacctno, array $split, array $currencyDetails, array $expected) : void
    {
        $method = Utils::getAccessibleReflectionMethod(GLAcctAllocationRunManager::class, "createTargetLineItems");
        $result = $method->invokeArgs(
            $this->glGLacctAllocRunMockObj,
            [
                $balance,
                $percentage2allocate,
                $glacctno,
                $split,
                $currencyDetails,
                []
            ]
        );

        self::assertEquals($expected, $result);
    }

    /**
     * @dataProvider dataProvider_testCreateTargetLineItemsAddionalTestCase
     * @covers       GLAcctAllocationRunManager::createTargetLineItems
     *
     * @param TestInfo $testObj
     * @param \stdClass $input
     * @param \stdClass $expectedOutput
     */
    public function testCreateTargetLineItemsAddionalTestCase(TestInfo $testObj, \stdClass $input, \stdClass $expectedOutput): void
    {
        /* It is much ease to write test case using these classes, keeping existing testcase as is and added new test case and test data here. */
        $method = Utils::getAccessibleReflectionMethod(GLAcctAllocationRunManager::class, "createTargetLineItems");
        $result = $method->invokeArgs(
            $this->glGLacctAllocRunMockObj,
            [
                $input->balance,
                $input->percentage2allocate,
                $input->acctNo,
                $input->split,
                $input->currencyDetails,
                $input->preservedDims,
                $input->billable
            ]
        );

        self::assertEquals($expectedOutput->glentry, $result, $testObj->getErrorMsg("glentry not matching"));
    }

    /**
     * @dataProvider dataProvider_testCreateTargetReverseLineItemsAddionalTestCase
     * @covers       GLAcctAllocationRunManager::createTargetLineItems
     * @covers       GLAcctAllocationRunManager::createReverseLineItems
     *
     * @param TestInfo $testObj
     * @param \stdClass $input
     * @param \stdClass $expectedOutput
     */
    public function testCreateTargetReverseLineItemsAddionalTestCase(TestInfo $testObj, \stdClass $input, \stdClass $expectedOutput): void
    {
        /* It is much ease to write test case using these classes, keeping existing testcase as is and added new test case and test data here. */
        $method = Utils::getAccessibleReflectionMethod(GLAcctAllocationRunManager::class, "createTargetLineItems");
        $result = $method->invokeArgs(
            $this->glGLacctAllocRunMockObj,
            [
                $input->balance,
                $input->percentage2allocate,
                $input->acctNo,
                $input->split,
                $input->currencyDetails,
                $input->preservedDims,
                $input->billable
            ]
        );

        self::assertEquals($expectedOutput->glentry, $result, $testObj->getErrorMsg("Target GL-Entries not matching"));

        $method = Utils::getAccessibleReflectionMethod(GLAcctAllocationRunManager::class, "createReverseLineItems");
        $result = $method->invokeArgs(
            $this->glGLacctAllocRunMockObj,
            [
                $input->balance,
                $input->percentage2allocate,
                $input->reverse_acctNo,
                "false",
                $input->currencyDetails,
                $input->preservedDims
            ]
        );

        self::assertEquals($expectedOutput->reverse_glentry, $result, $testObj->getErrorMsg("Reverse GL-Entries not matching"));

    }

    /**
     * @return array
     */
    public function dataProvider_testCreateTargetLineItems() : array
    {
        return [
            [
                [
                    [
                        "ACCOUNTID" => "194",
                        "ACCOUNT_NUM" => "4000",
                        "LOCATIONID" => "1",
                        "LOCATIONNAME" => "United States of America",
                        "CUSTDIM_10002NAME" => "U1",
                        "TOTAL" => "-10",
                    ],
                ],
                100.00,
                "5001",
                [
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U1"
                    ],
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.75",
                        "CUSTDIM_10002NAME" => "U2"
                    ],
                    [
                        "LOCATIONID" => "1",
                        "percent" => "-0.25",
                        "CUSTDIM_10002NAME" => "U2"
                    ]
                ],
                [],
                [
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U1",
                        "LOCATION" => "1",
                        "TR_TYPE" => "-1",
                        "AMOUNT" => "5",
                        "TRX_AMOUNT" => "5",
                        "ACCOUNTNO" => "5001"
                    ],
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.75",
                        "CUSTDIM_10002NAME" => "U2",
                        "LOCATION" => "1",
                        "TR_TYPE" => "-1",
                        "AMOUNT" => "7.5",
                        "TRX_AMOUNT" => "7.5",
                        "ACCOUNTNO" => "5001",
                    ],
                    [
                        "LOCATIONID" => "1",
                        "percent" => "-0.25",
                        "CUSTDIM_10002NAME" => "U2",
                        "LOCATION" => "1",
                        "TR_TYPE" => "1",
                        "AMOUNT" => "2.5",
                        "TRX_AMOUNT" => "2.5",
                        "ACCOUNTNO" => "5001",
                    ]
                ]
            ],
            [
                [
                    [
                        "ACCOUNTID" => "194",
                        "ACCOUNT_NUM" => "4000",
                        "LOCATIONID" => "1",
                        "LOCATIONNAME" => "United States of America",
                        "CUSTDIM_10002NAME" => "U1",
                        "TOTAL" => "-2400",
                    ],
                ],
                100.00,
                "5001",
                [
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.*********",
                        "CUSTDIM_10002NAME" => "U1"
                    ],
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.*********",
                        "CUSTDIM_10002NAME" => "U2"
                    ],
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.*********",
                        "CUSTDIM_10002NAME" => ""
                    ]
                ],
                [],
                [
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.*********",
                        "CUSTDIM_10002NAME" => "U1",
                        "LOCATION" => "1",
                        "TR_TYPE" => "-1",
                        "AMOUNT" => "857.15",
                        "TRX_AMOUNT" => "857.15",
                        "ACCOUNTNO" => "5001"
                    ],
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.*********",
                        "CUSTDIM_10002NAME" => "U2",
                        "LOCATION" => "1",
                        "TR_TYPE" => "-1",
                        "AMOUNT" => "857.14",
                        "TRX_AMOUNT" => "857.14",
                        "ACCOUNTNO" => "5001",
                    ],
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.*********",
                        "CUSTDIM_10002NAME" => "",
                        "LOCATION" => "1",
                        "TR_TYPE" => "-1",
                        "AMOUNT" => "685.71",
                        "TRX_AMOUNT" => "685.71",
                        "ACCOUNTNO" => "5001",
                    ]
                ]
            ],
            [
                [
                    [
                        "ACCOUNTID" => "194",
                        "ACCOUNT_NUM" => "4000",
                        "LOCATIONID" => "1",
                        "LOCATIONNAME" => "United States of America",
                        "CUSTDIM_10002NAME" => "U1",
                        "TOTAL" => "-10",
                    ]
                ],
                100.00,
                "5001",
                [
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U1"
                    ],
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U2"
                    ]
                ],
                [],
                [
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U1",
                        "LOCATION" => "1",
                        "TR_TYPE" => "-1",
                        "AMOUNT" => "5",
                        "TRX_AMOUNT" => "5",
                        "ACCOUNTNO" => "5001"
                    ],
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U2",
                        "LOCATION" => "1",
                        "TR_TYPE" => "-1",
                        "AMOUNT" => "5",
                        "TRX_AMOUNT" => "5",
                        "ACCOUNTNO" => "5001",
                    ]
                ]
            ],
            [
                [
                    [
                        "ACCOUNTID" => "194",
                        "ACCOUNT_NUM" => "4000",
                        "LOCATIONID" => "1",
                        "LOCATIONNAME" => "United States of America",
                        "CUSTDIM_10002NAME" => "U1",
                        "TOTAL" => "-10",
                    ]
                ],
                50.55,
                "5001",
                [
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U1"
                    ],
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U2"
                    ]
                ],
                [],
                [
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U1",
                        "LOCATION" => "1",
                        "TR_TYPE" => "-1",
                        "AMOUNT" => "2.53",
                        "TRX_AMOUNT" => "2.53",
                        "ACCOUNTNO" => "5001"
                    ],
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U2",
                        "LOCATION" => "1",
                        "TR_TYPE" => "-1",
                        "AMOUNT" => "2.53",
                        "TRX_AMOUNT" => "2.53",
                        "ACCOUNTNO" => "5001",
                    ]
                ]
            ],
            [
                [
                    [
                        "ACCOUNTID" => "194",
                        "ACCOUNT_NUM" => "4000",
                        "LOCATIONID" => "1",
                        "LOCATIONNAME" => "United States of America",
                        "CUSTDIM_10002NAME" => "U1",
                        "TOTAL" => "10",
                    ]
                ],
                100.00,
                "5001",
                [
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U1"
                    ],
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U2"
                    ]
                ],
                [],
                [
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U1",
                        "LOCATION" => "1",
                        "TR_TYPE" => "1",
                        "AMOUNT" => "5",
                        "TRX_AMOUNT" => "5",
                        "ACCOUNTNO" => "5001"
                    ],
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U2",
                        "LOCATION" => "1",
                        "TR_TYPE" => "1",
                        "AMOUNT" => "5",
                        "TRX_AMOUNT" => "5",
                        "ACCOUNTNO" => "5001",
                    ]
                ]
            ],
            [
                [
                    [
                        "ACCOUNTID" => "194",
                        "ACCOUNT_NUM" => "4000",
                        "LOCATIONID" => "1",
                        "LOCATIONNAME" => "United States of America",
                        "CUSTDIM_10002NAME" => "U1",
                        "TOTAL" => "10",
                    ],
                    [
                        "ACCOUNTID" => "194",
                        "ACCOUNT_NUM" => "4001",
                        "LOCATIONID" => "1",
                        "LOCATIONNAME" => "United States of America",
                        "CUSTDIM_10002NAME" => "U1",
                        "TOTAL" => "10",
                    ]
                ],
                80.00,
                "5001",
                [
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U1"
                    ],
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U2"
                    ]
                ],
                [],
                [
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U1",
                        "LOCATION" => "1",
                        "TR_TYPE" => "1",
                        "AMOUNT" => "8",
                        "TRX_AMOUNT" => "8",
                        "ACCOUNTNO" => "5001"
                    ],
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U2",
                        "LOCATION" => "1",
                        "TR_TYPE" => "1",
                        "AMOUNT" => "8",
                        "TRX_AMOUNT" => "8",
                        "ACCOUNTNO" => "5001",
                    ]
                ]
            ],
            [
                [
                    [
                        "ACCOUNTID" => "194",
                        "ACCOUNT_NUM" => "4000",
                        "LOCATIONID" => "1",
                        "LOCATIONNAME" => "United States of America",
                        "CUSTDIM_10002NAME" => "U1",
                        "TOTAL" => "10",
                    ],
                    [
                        "ACCOUNTID" => "194",
                        "ACCOUNT_NUM" => "4001",
                        "LOCATIONID" => "1",
                        "LOCATIONNAME" => "United States of America",
                        "CUSTDIM_10002NAME" => "U1",
                        "TOTAL" => "10",
                    ]
                ],
                200.00,
                "5001",
                [
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U1"
                    ],
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U2"
                    ]
                ],
                [],
                [
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U1",
                        "LOCATION" => "1",
                        "TR_TYPE" => "1",
                        "AMOUNT" => "20",
                        "TRX_AMOUNT" => "20",
                        "ACCOUNTNO" => "5001"
                    ],
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U2",
                        "LOCATION" => "1",
                        "TR_TYPE" => "1",
                        "AMOUNT" => "20",
                        "TRX_AMOUNT" => "20",
                        "ACCOUNTNO" => "5001",
                    ]
                ]
            ],
            [
                [
                    [
                        "ACCOUNTID" => "194",
                        "ACCOUNT_NUM" => "4000",
                        "LOCATIONID" => "1",
                        "LOCATIONNAME" => "United States of America",
                        "CUSTDIM_10002NAME" => "U1",
                        "TOTAL" => "-10",
                    ],
                    [
                        "ACCOUNTID" => "194",
                        "ACCOUNT_NUM" => "4001",
                        "LOCATIONID" => "1",
                        "LOCATIONNAME" => "United States of America",
                        "CUSTDIM_10002NAME" => "U1",
                        "TOTAL" => "-10",
                    ]
                ],
                300.00,
                "5001",
                [
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U1"
                    ],
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U2"
                    ]
                ],
                [],
                [
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U1",
                        "LOCATION" => "1",
                        "TR_TYPE" => "-1",
                        "AMOUNT" => "30",
                        "TRX_AMOUNT" => "30",
                        "ACCOUNTNO" => "5001"
                    ],
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U2",
                        "LOCATION" => "1",
                        "TR_TYPE" => "-1",
                        "AMOUNT" => "30",
                        "TRX_AMOUNT" => "30",
                        "ACCOUNTNO" => "5001",
                    ]
                ]
            ],
            [
                [
                    [
                        "ACCOUNTID" => "194",
                        "ACCOUNT_NUM" => "4000",
                        "LOCATIONID" => "1",
                        "LOCATIONNAME" => "United States of America",
                        "CUSTDIM_10002NAME" => "U1",
                        "TOTAL" => "-10",
                    ],
                    [
                        "ACCOUNTID" => "194",
                        "ACCOUNT_NUM" => "4001",
                        "LOCATIONID" => "1",
                        "LOCATIONNAME" => "United States of America",
                        "CUSTDIM_10002NAME" => "U1",
                        "TOTAL" => "-10",
                    ]
                ],
                300.00,
                "5001",
                [
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U1"
                    ],
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U2"
                    ]
                ],
                [
                    'CURRENCY' => 'USD',
                    'EXCH_RATE_TYPE_ID' => 'Intacct Dailt Rate',
                    'EXCH_RATE_DATE' => '01/01/2020'
                ],
                [
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U1",
                        "LOCATION" => "1",
                        "TR_TYPE" => "-1",
                        "AMOUNT" => "30",
                        "TRX_AMOUNT" => "30",
                        "ACCOUNTNO" => "5001",
                        'CURRENCY' => 'USD',
                        'EXCH_RATE_TYPE_ID' => 'Intacct Dailt Rate',
                        'EXCH_RATE_DATE' => '01/01/2020'
                    ],
                    [
                        "LOCATIONID" => "1",
                        "percent" => "0.50",
                        "CUSTDIM_10002NAME" => "U2",
                        "LOCATION" => "1",
                        "TR_TYPE" => "-1",
                        "AMOUNT" => "30",
                        "TRX_AMOUNT" => "30",
                        "ACCOUNTNO" => "5001",
                        'CURRENCY' => 'USD',
                        'EXCH_RATE_TYPE_ID' => 'Intacct Dailt Rate',
                        'EXCH_RATE_DATE' => '01/01/2020'
                    ]
                ]
            ],
        ];
    }
    
    /**
     * @dataProvider dataProviderTestCreateReverseLineItems
     *
     * @covers       GLAcctAllocationRunManager::createReverseLineItems
     *
     * @param array       $balance
     * @param float       $percentage2allocate
     * @param string      $glacctno
     * @param string|null $useSroucePoolAcct
     * @param string[]    $currencyDetails
     * @param array       $expected
     *
     * @throws \ReflectionException
     */
    public function testCreateReverseLineItems(array $balance, float $percentage2allocate, string $glacctno, ?string $useSroucePoolAcct, array $currencyDetails, array $expected) : void
    {
        $method = Utils::getAccessibleReflectionMethod(GLAcctAllocationRunManager::class, "createReverseLineItems");
        $result = $method->invokeArgs(
            $this->glGLacctAllocRunMockObj,
            [
                $balance,
                $percentage2allocate,
                $glacctno,
                $useSroucePoolAcct,
                $currencyDetails,
                []
            ]
        );

        self::assertEquals($expected, $result);
    }

    /**
     * @return array
     */
    public function dataProviderTestCreateReverseLineItems() : array
    {
        return [
            [
                [
                    [
                        "ACCOUNTID" => "391", "ACCOUNT_NUM" => "1106", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "-1400"
                    ],
                    [
                        "ACCOUNTID" => "386", "ACCOUNT_NUM" => "1100", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "800"
                    ],
                    [
                        "ACCOUNTID" => "387", "ACCOUNT_NUM" => "1101", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "1000"
                    ],
                    [
                        "ACCOUNTID" => "393", "ACCOUNT_NUM" => "1108", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "50"
                    ],
                    [
                        "ACCOUNTID" => "392", "ACCOUNT_NUM" => "1107", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "-1300"
                    ]
                ],
                100.00,
                "1103",
                "false",
                [],
                [
                    [
                        "ACCOUNTID" => "391", "ACCOUNT_NUM" => "1106", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1103", "TR_TYPE" => "1", "AMOUNT" => 1400,
                        "TRX_AMOUNT" => 1400, "DESCRIPTION" => "Reverses 1106 account"
                    ],
                    [
                        "ACCOUNTID" => "386", "ACCOUNT_NUM" => "1100", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1103", "TR_TYPE" => "-1", "AMOUNT" => "800",
                        "TRX_AMOUNT" => "800", 'DESCRIPTION' => 'Reverses 1100 account'
                    ],
                    [
                        "ACCOUNTID" => "387", "ACCOUNT_NUM" => "1101", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1103", "TR_TYPE" => "-1", "AMOUNT" => "1000",
                        "TRX_AMOUNT" => "1000", 'DESCRIPTION' => 'Reverses 1101 account'
                    ],
                    [
                        "ACCOUNTID" => "393", "ACCOUNT_NUM" => "1108", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1103", "TR_TYPE" => "-1", "AMOUNT" => "50",
                        "TRX_AMOUNT" => "50", 'DESCRIPTION' => 'Reverses 1108 account'
                    ],
                    [
                        "ACCOUNTID" => "392", "ACCOUNT_NUM" => "1107", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1103", "TR_TYPE" => "1", "AMOUNT" => 1300,
                        "TRX_AMOUNT" => 1300, 'DESCRIPTION' => 'Reverses 1107 account'
                    ]
                ]
            ],
            [
                [
                    [
                        "ACCOUNTID" => "391", "ACCOUNT_NUM" => "1106", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "-1400"
                    ],
                    [
                        "ACCOUNTID" => "386", "ACCOUNT_NUM" => "1100", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "-800"
                    ],
                    [
                        "ACCOUNTID" => "387", "ACCOUNT_NUM" => "1101", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "1000"
                    ],
                    [
                        "ACCOUNTID" => "393", "ACCOUNT_NUM" => "1108", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "50"
                    ],
                    [
                        "ACCOUNTID" => "392", "ACCOUNT_NUM" => "1107", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "1300"
                    ]
                ],
                100.00,
                "1103",
                "false",
                [],
                [
                    [
                        "ACCOUNTID" => "391", "ACCOUNT_NUM" => "1106", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1103", "TR_TYPE" => "1", "AMOUNT" => 1400,
                        "TRX_AMOUNT" => 1400, 'DESCRIPTION' => 'Reverses 1106 account'
                    ],
                    [
                        "ACCOUNTID" => "386", "ACCOUNT_NUM" => "1100", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1103", "TR_TYPE" => "1", "AMOUNT" => 800, "TRX_AMOUNT" => 800,
                        'DESCRIPTION' => 'Reverses 1100 account'
                    ],
                    [
                        "ACCOUNTID" => "387", "ACCOUNT_NUM" => "1101", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1103", "TR_TYPE" => "-1", "AMOUNT" => "1000",
                        "TRX_AMOUNT" => "1000",'DESCRIPTION' => 'Reverses 1101 account'
                    ],
                    [
                        "ACCOUNTID" => "393", "ACCOUNT_NUM" => "1108", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1103", "TR_TYPE" => "-1", "AMOUNT" => "50",
                        "TRX_AMOUNT" => "50",'DESCRIPTION' => 'Reverses 1108 account'
                    ],
                    [
                        "ACCOUNTID" => "392", "ACCOUNT_NUM" => "1107", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1103", "TR_TYPE" => "-1", "AMOUNT" => "1300",
                        "TRX_AMOUNT" => "1300",'DESCRIPTION' => 'Reverses 1107 account'
                    ]
                ]
            ],
            [
                [
                    [
                        "ACCOUNTID" => "391", "ACCOUNT_NUM" => "1106", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "40"
                    ],
                    [
                        "ACCOUNTID" => "386", "ACCOUNT_NUM" => "1100", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "50"
                    ],
                    [
                        "ACCOUNTID" => "387", "ACCOUNT_NUM" => "1101", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "20"
                    ],
                    [
                        "ACCOUNTID" => "393", "ACCOUNT_NUM" => "1108", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "30"
                    ],
                    [
                        "ACCOUNTID" => "392", "ACCOUNT_NUM" => "1107", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "10"
                    ]
                ],
                50,
                "1103",
                null,
                [],
                [
                    [
                        "ACCOUNTID" => "391", "ACCOUNT_NUM" => "1106", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1103", "TR_TYPE" => "-1", "AMOUNT" => 20,
                        "TRX_AMOUNT" => 20, 'DESCRIPTION' => 'Reverses 1106 account'
                    ],
                    [
                        "ACCOUNTID" => "386", "ACCOUNT_NUM" => "1100", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1103", "TR_TYPE" => "-1", "AMOUNT" => "25",
                        "TRX_AMOUNT" => "25", 'DESCRIPTION' => 'Reverses 1100 account'
                    ],
                    [
                        "ACCOUNTID" => "387", "ACCOUNT_NUM" => "1101", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1103", "TR_TYPE" => "-1", "AMOUNT" => "10",
                        "TRX_AMOUNT" => "10", 'DESCRIPTION' => 'Reverses 1101 account'
                    ],
                    [
                        "ACCOUNTID" => "393", "ACCOUNT_NUM" => "1108", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1103", "TR_TYPE" => "-1", "AMOUNT" => 15,
                        "TRX_AMOUNT" => 15, 'DESCRIPTION' => 'Reverses 1108 account'
                    ],
                    [
                        "ACCOUNTID" => "392", "ACCOUNT_NUM" => "1107", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1103", "TR_TYPE" => "-1", "AMOUNT" => "5",
                        "TRX_AMOUNT" => "5", 'DESCRIPTION' => 'Reverses 1107 account'
                    ],
                ]
            ],
            [
                [
                    [
                        "ACCOUNTID" => "391", "ACCOUNT_NUM" => "1106", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "40"
                    ],
                    [
                        "ACCOUNTID" => "386", "ACCOUNT_NUM" => "1100", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "50"
                    ],
                    [
                        "ACCOUNTID" => "387", "ACCOUNT_NUM" => "1101", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "20"
                    ],
                    [
                        "ACCOUNTID" => "393", "ACCOUNT_NUM" => "1108", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "30"
                    ],
                    [
                        "ACCOUNTID" => "392", "ACCOUNT_NUM" => "1107", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "10"
                    ]
                ],
                50,
                "1103",
                "true",
                [],
                [
                    [
                        "ACCOUNTID" => "391", "ACCOUNT_NUM" => "1106", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1106", "TR_TYPE" => "-1", "AMOUNT" => 20,
                        "TRX_AMOUNT" => 20, 'DESCRIPTION' => 'Reverses 1106 account'
                    ],
                    [
                        "ACCOUNTID" => "386", "ACCOUNT_NUM" => "1100", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1100", "TR_TYPE" => "-1", "AMOUNT" => "25",
                        "TRX_AMOUNT" => "25", 'DESCRIPTION' => 'Reverses 1100 account'
                    ],
                    [
                        "ACCOUNTID" => "387", "ACCOUNT_NUM" => "1101", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1101", "TR_TYPE" => "-1", "AMOUNT" => "10",
                        "TRX_AMOUNT" => "10", 'DESCRIPTION' => 'Reverses 1101 account'
                    ],
                    [
                        "ACCOUNTID" => "393", "ACCOUNT_NUM" => "1108", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1108", "TR_TYPE" => "-1", "AMOUNT" => 15,
                        "TRX_AMOUNT" => 15, 'DESCRIPTION' => 'Reverses 1108 account'
                    ],
                    [
                        "ACCOUNTID" => "392", "ACCOUNT_NUM" => "1107", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1107", "TR_TYPE" => "-1", "AMOUNT" => "5",
                        "TRX_AMOUNT" => "5", 'DESCRIPTION' => 'Reverses 1107 account'
                    ],
                ]
            ],
            [
                [
                    [
                        "ACCOUNTID" => "391", "ACCOUNT_NUM" => "1106", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "40"
                    ],
                    [
                        "ACCOUNTID" => "386", "ACCOUNT_NUM" => "1100", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "50"
                    ],
                    [
                        "ACCOUNTID" => "387", "ACCOUNT_NUM" => "1101", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "20"
                    ],
                    [
                        "ACCOUNTID" => "393", "ACCOUNT_NUM" => "1108", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "30"
                    ],
                    [
                        "ACCOUNTID" => "392", "ACCOUNT_NUM" => "1107", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America", "TOTAL" => "10"
                    ]
                ],
                50,
                "1103",
                "true",
                [
                    'CURRENCY' => 'USD',
                    'EXCH_RATE_TYPE_ID' => 'Intacct Dailt Rate',
                    'EXCH_RATE_DATE' => '01/01/2020'
                ],
                [
                    [
                        "ACCOUNTID" => "391", "ACCOUNT_NUM" => "1106", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1106", "TR_TYPE" => "-1", "AMOUNT" => 20,
                        "TRX_AMOUNT" => 20, 'DESCRIPTION' => 'Reverses 1106 account',
                        'CURRENCY' => 'USD',
                        'EXCH_RATE_TYPE_ID' => 'Intacct Dailt Rate',
                        'EXCH_RATE_DATE' => '01/01/2020'
                    ],
                    [
                        "ACCOUNTID" => "386", "ACCOUNT_NUM" => "1100", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1100", "TR_TYPE" => "-1", "AMOUNT" => "25",
                        "TRX_AMOUNT" => "25", 'DESCRIPTION' => 'Reverses 1100 account',
                        'CURRENCY' => 'USD',
                        'EXCH_RATE_TYPE_ID' => 'Intacct Dailt Rate',
                        'EXCH_RATE_DATE' => '01/01/2020'
                    ],
                    [
                        "ACCOUNTID" => "387", "ACCOUNT_NUM" => "1101", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1101", "TR_TYPE" => "-1", "AMOUNT" => "10",
                        "TRX_AMOUNT" => "10", 'DESCRIPTION' => 'Reverses 1101 account',
                        'CURRENCY' => 'USD',
                        'EXCH_RATE_TYPE_ID' => 'Intacct Dailt Rate',
                        'EXCH_RATE_DATE' => '01/01/2020'
                    ],
                    [
                        "ACCOUNTID" => "393", "ACCOUNT_NUM" => "1108", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1108", "TR_TYPE" => "-1", "AMOUNT" => 15,
                        "TRX_AMOUNT" => 15, 'DESCRIPTION' => 'Reverses 1108 account',
                        'CURRENCY' => 'USD',
                        'EXCH_RATE_TYPE_ID' => 'Intacct Dailt Rate',
                        'EXCH_RATE_DATE' => '01/01/2020'
                    ],
                    [
                        "ACCOUNTID" => "392", "ACCOUNT_NUM" => "1107", "DEPARTMENTID" => null, "DEPARTMENTNAME" => null,
                        "LOCATIONID" => "1", "LOCATIONNAME" => "United States of America",
                        "LOCATION" => "1", "ACCOUNTNO" => "1107", "TR_TYPE" => "-1", "AMOUNT" => "5",
                        "TRX_AMOUNT" => "5", 'DESCRIPTION' => 'Reverses 1107 account',
                        'CURRENCY' => 'USD',
                        'EXCH_RATE_TYPE_ID' => 'Intacct Dailt Rate',
                        'EXCH_RATE_DATE' => '01/01/2020'
                    ],
                ]
            ],
        ];
    }


    private function createGLAcctAllocationRunMockObj(): void
    {
        $this->glGLacctAllocRunMockObj = $this->getMockBuilder(GLAcctAllocationRunManager::class)
                                        ->disableOriginalConstructor()
                                        ->onlyMethods([])
                                        ->getMock();
        Utils::setReflectionPrivateProperty(
            $this->glGLacctAllocRunMockObj, "GLAcctAllocationRunManager", "dimensionName2IDMap",
            $this->dimensionName2IDMap
        );
        Utils::setReflectionPrivateProperty(
            $this->glGLacctAllocRunMockObj, "GLAcctAllocationRunManager", "filterDimensions",
            $this->filterdimension
        );
        Utils::setReflectionPrivateProperty(
            $this->glGLacctAllocRunMockObj, "GLAcctAllocationRunManager", "overrideDimensions",
            $this->overrideDimensions
        );
        //$this->glGLacctAllocRunMockObj->setDimensionName2IDMap($this->dimensionName2IDMap);
    }
}
