<?php

namespace tests\source\company;

use AccessLogReport;

class AccessLogReportTest extends \PHPUnit\Framework\TestCase
{
    /**
     * @return array
     */
    public function validateDateTimeFormatDataProvider(): array
    {
        return [
            ['H:i:s', '12:00:00', true],
            ['H:i:s', '12\':00:00', false],
            ['H:i:s', '12:00\':00', false],
            ['H:i:s', '12:00:00\'', false],
            ['H:i:s', '12<script>alert(1)</script>:00:00', false],
            ['Y-m-d H:i:s', '2000-01-01 00:00:00', true],
        ];
    }
    
    /**
     * @param string $format
     * @param string $inputValue
     * @param bool $expected
     *
     * @dataProvider validateDateTimeFormatDataProvider
     */
    public function testValidateDateTimeFormat(string $format, string $inputValue, bool $expected)
    {
        $accessLogReportClass = new AccessLogReport([]);
        self::assertEquals($expected, $accessLogReportClass->validateDateTimeFormat($format, $inputValue));
    }
}