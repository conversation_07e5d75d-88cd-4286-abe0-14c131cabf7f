<?php

namespace test\source\company;

use Globals;

/**
 *    Author:   <PERSON>
 *    Date:     6/12/2020
 *
 * @method assertTrue( bool $param )
 * @method assertNotEmpty( array[]|null $list )
 */

// read-only view
class PartnerAppManagerTest extends \unitTest\core\UnitTestBaseContext
{
    /**
     * Setup data common to all invocations of test routines
     */
    public static function setUpBeforeClass() : void
    {
        parent::setUpBeforeClass();
        print_r("\n" .'==================== [' . __CLASS__ . ' START] ====================' . "\n");

    }

    public static function tearDownAfterClass() : void
    {
        print_r("\n" . '===================== [' . __CLASS__ . ' END] =====================' . "\n\n");
        parent::tearDownAfterClass();
    }

    // need to prepare some testing data before run this: global imspartnerapp table 'INTACCT'
    public function testGet()
    {
        $recordno = QueryResult('select record# from v_glob_imspartnerapp fetch FIRST ROW ONLY')[0]['RECORD#'];
        $manager = Globals::$g->gManagerFactory->getManager('partnerapp');
        $result = $manager->get($recordno);
        $this->assertNotEmpty($result);
    }

    // need to prepare some testing data before run this: global imspartnerapp table 'INTACCT'
    public function testGetList()
    {
        $manager = Globals::$g->gManagerFactory->getManager('partnerapp');
        $result = $manager->GetList();
        $this->assertNotEmpty($result);
    }
}