<?php

namespace tests\source\core\suites\Repository;

use AbstractSuitesRepository;
use PHPUnit\Framework\MockObject\MockObject;
use SuitesUserApplicationRepository;
use SuitesUserApplicationRepositoryInterface;

/**
 * @covers SuitesUserApplicationRepository
 * @group unit
 */
class SuitesUserApplicationRepositoryTest extends \PHPUnit\Framework\TestCase
{

    public function testConstruct(): void
    {
        self::assertInstanceOf(AbstractSuitesRepository::class, new SuitesUserApplicationRepository());
        self::assertInstanceOf(SuitesUserApplicationRepositoryInterface::class, new SuitesUserApplicationRepository());
    }
    
    /**
     * @return array
     */
    public static function getAllDataProvider(): array
    {
        return [
            [123, 456, null, []],
            [123, 456, false, []],
            [123, 456, [], []],
            [123, 456, [1 => []], [1 => []]],
            [123, 456, [0 => []], [0 => []]],
            [123, 456, [['Fo Bar']], [['Fo Bar']]],
        ];
    }
    
    /**
     * @dataProvider getAllDataProvider
     * @param int              $cny
     * @param int              $userId
     * @param array|false|null $queryResult
     * @param array            $expected
     *
     * @return void
     */
    public function testGetAll(int $cny, int $userId, array|false|null $queryResult, array $expected): void
    {
        /** @var MockObject|SuitesUserApplicationRepository $suitesUserApplicationRepositoryMock */
        $suitesUserApplicationRepositoryMock = $this->getMockBuilder(SuitesUserApplicationRepository::class)
            ->onlyMethods(['queryResult'])
            ->getMock();
        $suitesUserApplicationRepositoryMock
            ->expects(self::once())
            ->method('queryResult')
            ->with(
                $this->callback(function ($param) use ($cny, $userId) {
                    self::assertSame(
                        'SELECT sua.* FROM ia_suite_user_app sua, ia_suite_tenant_app sta WHERE sua.cny#=:1 AND sua.userkey=:2 AND sua.cny#=sta.cny# AND sua.app_id=sta.app_id',
                        $param[0]
                    );
                    self::assertSame($cny, $param[1]);
                    self::assertSame($userId, $param[2]);
                    return true;
                })
            )
            ->willReturn($queryResult);
        
        self::assertSame($expected, $suitesUserApplicationRepositoryMock->getAll($cny, $userId));
    }
    
    /**
     * @return array
     */
    public static function getAllWithDetailsByAppIdsDataProvider(): array
    {
        return [
            [123, [], null, []],
            [123, [], false, []],
            [123, [['ID' => 'FO_BAR']], [], []],
            [123, [['ID' => 'FO_BAR']], [1 => []], [1 => []]],
            [123, [['ID' => 'FO_BAR']], [0 => []], [0 => []]],
            [123, [['ID' => 'FO_BAR']], [['Fo Bar']], [['Fo Bar']]],
        ];
    }
    
    /**
     * @dataProvider getAllWithDetailsByAppIdsDataProvider
     * @param int              $cny
     * @param array            $appIds
     * @param array|false|null $queryResult
     * @param array            $expected
     *
     * @return void
     */
    public function testGetAllWithDetailsByAppIds(int $cny, array $appIds, array|false|null $queryResult, array $expected): void
    {
        $appInCondition = '';
        $paramCount = 4;
        foreach ($appIds as $app) {
            $appInCondition .= ":$paramCount,";
            $stmtDesc[$paramCount] = $app['ID'];
            $paramCount++;
        }
        /** @var MockObject|SuitesUserApplicationRepository $suitesUserApplicationRepositoryMock */
        $suitesUserApplicationRepositoryMock = $this->getMockBuilder(SuitesUserApplicationRepository::class)
            ->onlyMethods(['queryResult'])
            ->getMock();
        $suitesUserApplicationRepositoryMock
            ->expects(self::once())
            ->method('queryResult')
            ->with(
                $this->callback(function ($param) use ($cny, $appInCondition, $appIds) {
                    self::assertSame(
                        sprintf(
                            'SELECT ui.CNY#, ui.LOGINID, sua2.USERKEY, sua2.APP_IDS, ct.FIRSTNAME, ct.LASTNAME, ui.EMAIL, ui.TYPE, ui.STATUS, up.value SAGE_ID, up2.value SAGE_ID_PSEUDONYM'
                            . ' FROM userinfo ui'
                            . ' INNER JOIN (SELECT sua.CNY#, sua.USERKEY, LISTAGG(sua.app_id, \',\') WITHIN GROUP (ORDER BY sua.APP_ID) AS APP_IDS'
                            . ' FROM ia_suite_user_app sua'
                            . ' WHERE sua.CNY#=:3 AND sua.APP_ID IN (%s)'
                            . ' GROUP BY sua.CNY#, sua.USERKEY'
                            . ') sua2 ON (ui.CNY#=sua2.CNY# AND ui.RECORD#=sua2.USERKEY)'
                            . ' LEFT JOIN contact ct ON (ui.CNY#=ct.CNY# AND ui.contactkey=ct.RECORD#)'
                            . ' LEFT OUTER JOIN userpref up ON (ui.CNY#=up.CNY# AND ui.RECORD#=up.USERREC AND up.PROPERTY=:1)'
                            . ' LEFT OUTER JOIN userpref up2 ON (ui.CNY#=up2.CNY# AND ui.RECORD#=up2.USERREC AND up2.PROPERTY=:2)'
                            . ' WHERE ui.CNY#=:3',
                            rtrim($appInCondition, ',')
                        ),
                        $param[0]
                    );
                    self::assertSame('SAGE_ID', $param[1]);
                    self::assertSame('SAGE_ID_PSEUDONYM', $param[2]);
                    self::assertSame($cny, $param[3]);
                    $paramCount = 4;
                    foreach ($appIds as $app) {
                        self::assertSame($app['ID'], $param[$paramCount]);
                        $paramCount++;
                    }
                    return true;
                })
            )
            ->willReturn($queryResult);
        
        self::assertSame($expected, $suitesUserApplicationRepositoryMock->getAllWithDetailsByAppIds($cny, $appIds));
    }
    
    /**
     * @return array[]
     */
    public function addDataProvider(): array
    {
        return [
            [123, 234, 'FO_BAR', 0, false],
            [123, 234, 'FO_BAR', 1, true],
            [123, 234, 'FO_BAR', 2, true],
        ];
    }
    
    /**
     * @dataProvider addDataProvider
     * @param int    $cny
     * @param int    $userId
     * @param string $applicationId
     * @param int    $execQueryResponse
     * @param bool   $expected
     *
     * @return void
     */
    public function testAdd(int $cny, int $userId, string $applicationId, int $execQueryResponse, bool $expected): void
    {
        /** @var MockObject|SuitesUserApplicationRepository $suitesUserApplicationRepositoryMock */
        $suitesUserApplicationRepositoryMock = $this->getMockBuilder(SuitesUserApplicationRepository::class)
            ->onlyMethods(['execQuery'])
            ->getMock();
        $suitesUserApplicationRepositoryMock
            ->expects(self::once())
            ->method('execQuery')
            ->with(
                $this->callback(function ($param) use ($cny, $userId, $applicationId) {
                    self::assertSame(
                        "INSERT INTO ia_suite_user_app(cny#, record#, userkey, app_id) VALUES (:1, GET_NEXTRECORDID(:1,'IA_SUITE_USER_APP'), :2, :3)",
                        $param[0]
                    );
                    self::assertSame($cny, $param[1]);
                    self::assertSame($userId, $param[2]);
                    self::assertSame($applicationId, $param[3]);
                    return true;
                })
            )
            ->willReturn($execQueryResponse);
        
        self::assertSame($expected, $suitesUserApplicationRepositoryMock->add($cny, $userId, $applicationId));
    }
}
