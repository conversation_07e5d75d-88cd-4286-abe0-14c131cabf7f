<?php
/**
 * Sanity test cases for API Idempotency Service function
 *
 * <AUTHOR>
 * @copyright Copyright (C)2020 Sage Intacct Corporation, All Rights Reserved
 */

namespace tests\source\api\framework\idempotency;
require_once (__DIR__.'/../APITestHelper.cls');

/**
 * Class APIIdempotencyTest
 * @package tests\source\api\framework\idempotency
 */
class APIIdempotencyTest extends \unitTest\core\UnitTestBaseContext
{
    /**
     * sanity check test framework
     */
    public function testNothing()
    {
        $this->assertTrue(true);
    }

    /**
     * Setup data common to all invocations of test routines
     */
    public static function setUpBeforeClass() : void
    {
        parent::setUpBeforeClass();
        print_r("\n" .'==================== [' . __CLASS__ . ' START] ====================' . "\n");

    }

    public static function tearDownAfterClass() : void
    {
        print_r("\n" . '==================== [' . __CLASS__ . ' END] ====================' . "\n\n");
        parent::tearDownAfterClass();
    }

    public function testNotSupportForQueryService()
    {
        $parameters = [
            'sessionId'     => 's',
            'senderId'      => 's',
            'url'           => 'v5-test/services/query/',
            'httpMethod'    => 'POST',
            'headers'       => [\APIConstants::API_EXTRA_PARAM_REQ_IDEM_KEY=>time()]
        ];
        $response = (\APIDispatcher::getDispatcher($parameters))->dispatch();
        $this->assertTrue($response->getStatus() === 400, $response);
        // TODO: change this back when the I18n pack is ready under 'locales' folder: IA.THE_SERVICE_DOES_NOT_SUPPORT_IDEMPOTENCY => "The ${ACTION} service does not support idempotency"
        // $this->assertTrue(strpos($response->getErrorMessage(), 'idempotency') !== false, $response);
        $this->assertNull($response->getResponseHeaders()[\APIConstants::API_EXTRA_PARAM_REQ_IDEM_KEY], $response);
    }

    /**
     * DE25980: 500 apiLayerIssue sending PATCH with key in the body
     *
     * @throws \APIException
     */
    public function testDE25980InternalError()
    {
        $parameters = [
            'sessionId'     => 's',
            'senderId'      => 's',
            'url'           => 'v0/objects/contact',
            'httpMethod'    => 'PATCH',
            'body'          => '{"key" : {{contact_key_0}},"printAs": "Async updated Contact with the same Idempotency-Key"}',
            'headers'       => [\APIConstants::API_EXTRA_PARAM_REQ_IDEM_KEY=>time()]
        ];
        try {
            (\APIDispatcher::getDispatcher($parameters))->dispatch();
            $this->assertTrue(false);
        } catch (\Exception $e) {
            $this->assertTrue($e instanceof \APIException);
            $this->assertTrue($e->hasAPIError());
            $this->assertNotNull($e->getAPIError());
        }
    }

    /**
     * DE21577: Idempotency Not Supported Operations
     */
    public function testDE21577IdempotencyNotSupported()
    {
        $operation = \APIConstants::API_HTTP_METHOD_OPTIONS;
        $request = new \stdClass();
        $payload = json_encode($request);
        $parameters = [
            'sessionId'     => 'sessionid',
            'senderId'      => 'senderid',
            'clientId'      => 'clientid',
            'url'           => 'v0/objects/user-view',
            'httpMethod'    => $operation,
            'body'          => $payload,
            'headers'       => ['Idempotency-Key' => time()]
        ];
        $dispatcher = \APIDispatcher::getDispatcher($parameters);
        try {
            $dispatcher->dispatch();
        } catch (\Throwable $t) {
            $this->assertTrue($t instanceof \APIException && $t->hasAPIError());
            $error = $t->getAPIError();
            $message = $error->getMessage();
            // TODO: change this back when the I18n pack is ready under 'locales' folder: IA.THE_METHOD_DOES_NOT_SUPPORT_IDEMPOTENCY => "The ${ACTION} method does not support idempotency"
            // $this->assertNotFalse(strpos($message, $operation), $message);
            // $this->assertNotFalse(strpos($message, 'method does not support idempotency'), $message);
            $this->assertTrue($error->getStatus() === 400, $message);
        }
    }

    /**
     * IA-143033: ids required for idempotency are not passed in the headers, but through trusted context
     *
     * @return void
     * @throws \APIException
     */
    // temp disable until figure out how to handle DB switching
    //public function testIdempotencyKeyInHeader()
    //{
    //    $clientId = "d4f2b6b318174b9a60a7.INTACCT.app.sage.com";
    //    $trustedContext = [
    //        "clientId" => $clientId,
    //        "dbId" => 12505,
    //        "dbURI" => "dev125:9080",
    //        "connId" => 125764,
    //        "userKey" => 1,
    //        "companyId" => 45208802,
    //        "locationKey" => 0,
    //        "departmentKey" => 0,
    //        "mongoShard" => "dev_rs2",
    //        "sessionKey" => "DYnhZDp250PXQy33MT3Sh0Y_Q9ZDLQ2J4WQ4xzOh10Mt9zE90oav10PX",
    //        "trxLevel" => 0,
    //        "jwtData" => null
    //    ];
    //    $parameters = [
    //        'url'           => 'v0/objects/user-view',
    //        'httpMethod'    => 'POST',
    //        'body'          => '[{"id":"vendor_82A746402","name":"vendor_82A746402"}]',
    //        'headers'       => ['Idempotency-Key' => time()]
    //    ];
    //    \Globals::$g->serviceMeta->setTrusted(true);
    //    $dispatcher = (\APIDispatcher::getDispatcher($parameters))->setLocalContext($trustedContext);
    //    $this->assertTrue($dispatcher->getClientId() === $clientId);
    //}
}