<?php

/**
 * API Adapter CRUD Orchestrator specific test cases
 * Uses Company and login ID defined in unitTest/core/unitTest.cfg
 *
 * <AUTHOR>
 * @copyright Copyright (C)2020 Sage Intacct Corporation, All Rights Reserved
 */

namespace tests\source\api\framework\crud;
require_once (__DIR__.'/../APITestHelper.cls');

/**
 * Class APICRUDOrchestratorTest
 * @package tests\source\api\framework\crud
 */
class APICRUDOrchestratorTest extends \unitTest\core\UnitTestBaseContext
{

    public function testNothing()
    {
        $this->assertTrue(true);
    }

    public function testconvertWrapperFromAPI()
    {
        $version ='5-test';
        $objectName ='__testglbatch';
        $operation = 'patch';
        $payload = [
            'key' => '4194',
            'entries' => []
        ];
        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orch = new \GenericAPICRUDOrchestrator($handler);
        $objHandler = \RegistryLoader::getInstance($version)->getHandler($objectName);

        $result = $orch->convertFromAPI(
            $operation,
            $objectName,
            $version,
            [$payload],
            $objHandler,
            $objHandler->getAdapter()
        );
        $this->assertNotNull($result);
    }

    public function testConstructRequest01()
    {
//        $object     = '__testglbatch';
        $operation  = 'create';
        $version    = '5-test';
        $payload    = [ 'description' => '2565 updated' ];
        $extraParams = [ \APIConstants::API_EXTRA_PARAM_HIERARCHY => [ '__testglbatch', '']];

        \APICRUDUtil::initHierarchyParams($operation,$extraParams);
        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orch = new \GenericAPICRUDOrchestrator($handler);
        $result = $orch->preProcessPayload(
            $operation,
            $payload,
            \APICRUDUtil::getLeafHandler($version, $extraParams),
            $extraParams
        );

        // empty path node should be cleaned up
        $this->assertTrue(count($extraParams[\APIConstants::API_EXTRA_PARAM_HIERARCHY]) == 1);

        // should be wrapped, but no key inserted
        $this->assertNotNull(count($result) == 1);
        $this->assertTrue(! key_exists(\APIConstants::API_OBJECT_KEY_FIELD_NAME,$result[0]));
    }

    public function testConstructRequest01bulk()
    {
        $operation  = 'create';
        $version    = '5-test';
        $payload    = [
            [ 'description' => '2565 updated' ],
            [ 'description' => '8971 updated' ]
        ];
        $extraParams = [ \APIConstants::API_EXTRA_PARAM_HIERARCHY => [ '__testglbatch', '']];
        \APICRUDUtil::initHierarchyParams($operation, $extraParams);
        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orch = new \GenericAPICRUDOrchestrator($handler);
        $result = $orch->preProcessPayload(
            $operation,
            $payload,
            \APICRUDUtil::getLeafHandler($version, $extraParams),
            $extraParams
        );

        // empty path node should be cleaned up
        $this->assertTrue(count($extraParams[\APIConstants::API_EXTRA_PARAM_HIERARCHY]) == 1);

        // should be wrapped, but no key inserted
        $this->assertTrue(count($result) == 2);
        $this->assertTrue(! key_exists(\APIConstants::API_OBJECT_KEY_FIELD_NAME,$result[0]));
    }

    public function testConstructRequest02()
    {
//        $object     = '__testglbatch';
        $operation  = 'patch';
        $version    = '5-test';
        $payload    = [ 'description' => '2565 updated' ];
        $extraParams = [ \APIConstants::API_EXTRA_PARAM_HIERARCHY => [ '__testglbatch', '123']];

        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orch = new \GenericAPICRUDOrchestrator($handler);
        $result = $orch->preProcessPayload(
            $operation,
            $payload,
            \APICRUDUtil::getLeafHandler($version, $extraParams),
            $extraParams
        );
        $this->assertTrue(count($extraParams[\APIConstants::API_EXTRA_PARAM_HIERARCHY]) == 2);
        $this->assertTrue(count($result) == 1);
        $this->assertTrue(key_exists(\APIConstants::API_OBJECT_KEY_FIELD_NAME, $result[0]));
    }

    /**
     * All owned object related wrapping logic is moved to Adapter level
     * Wrapper is only saved as extra parameters
     *
     * @throws \APIAdapterException
     */
    public function testConstructRequest03()
    {
//        $object     = '__testglbatch';
        $operation  = 'create';
        $version    = '5-test';
        $payload    = [ 'description' => '2565 updated' ];
        $extraParams = [ \APIConstants::API_EXTRA_PARAM_HIERARCHY => [ '__testglbatch', '123', '__testglentry', '']];
        \APICRUDUtil::initHierarchyParams($operation, $extraParams);
        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orch = new \GenericAPICRUDOrchestrator($handler);
        $result = $orch->preProcessPayload(
            $operation,
            $payload,
            \APICRUDUtil::getLeafHandler($version, $extraParams),
            $extraParams
        );

        // empty path node should be cleaned up
        $this->assertTrue(count($extraParams[\APIConstants::API_EXTRA_PARAM_HIERARCHY]) == 3);
        $this->assertTrue(count($result) == 1);
        // should be wrapped, but no key inserted
        $this->assertTrue(key_exists(\APIConstants::API_EXTRA_PARAM_HIERARCHY_WRAPPER,$extraParams));
    }

    /**
     * All owned object related wrapping logic is moved to Adapter level
     * Wrapper is only saved as extra parameters
     *
     * @throws \APIAdapterException
     */
    public function testConstructRequest03Bulk()
    {
        $operation  = 'create';
        $version    = '5-test';
        $payload    = [
            [ 'description' => '2565 updated' ],
            [ 'description' => '8971 updated' ]
        ];
        $extraParams = [ \APIConstants::API_EXTRA_PARAM_HIERARCHY => [ '__testglbatch', '123', '__testglentry', '']];
        $itemWrapperName = 'entries';
        \APICRUDUtil::initHierarchyParams($operation, $extraParams);
        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orch = new \GenericAPICRUDOrchestrator($handler);
        $result = $orch->preProcessPayload(
            $operation,
            $payload,
            \APICRUDUtil::getLeafHandler($version, $extraParams),
            $extraParams
        );

        // empty path node should be cleaned up
        $this->assertTrue(count($extraParams[\APIConstants::API_EXTRA_PARAM_HIERARCHY]) == 3);

        // should be wrapped, but no key inserted
        $this->assertNotNull(count($result) == 1);
        $this->assertNotNull(count($result[$itemWrapperName]?? []) == count($payload?? []));
        $this->assertTrue(key_exists(\APIConstants::API_EXTRA_PARAM_HIERARCHY_WRAPPER,$extraParams));
    }

    /**
     * All owned object related wrapping logic is moved to Adapter level
     * Wrapper is only saved as extra parameters
     *
     * @throws \APIAdapterException
     */
    public function testConstructRequest04()
    {
        $operation  = 'patch';
        $version    = '5-test';
        $payload    = [ 'description' => '2565 updated' ];
        $extraParams = [ \APIConstants::API_EXTRA_PARAM_HIERARCHY => [ '__testglbatch', '123', '__testglentry', '456']];
        $itemWrapperName = 'entries';

        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orch = new \GenericAPICRUDOrchestrator($handler);
        $result = $orch->preProcessPayload(
            $operation,
            $payload,
            \APICRUDUtil::getLeafHandler($version, $extraParams),
            $extraParams
        );
        $this->assertTrue(count($extraParams[\APIConstants::API_EXTRA_PARAM_HIERARCHY]) == 4);
        $this->assertNotNull(count($result) == 1);
        $this->assertNotNull(count($result[$itemWrapperName]?? []) == count($payload?? []));
        $this->assertTrue(key_exists(\APIConstants::API_OBJECT_KEY_FIELD_NAME,$result[0]));
        $this->assertTrue(key_exists(\APIConstants::API_EXTRA_PARAM_HIERARCHY_WRAPPER,$extraParams));
        // $this->assertTrue(key_exists(\APIConstants::API_OBJECT_KEY_FIELD_NAME,$result[0][$itemWrapperName][0]));
    }

    /**
     * Test URI patterns supported by CRUD for GET API
     * /default-record
     * /duplicate-record?key=1
     * /data-from-last?refKey=1&refType=customer
     *
     */
    public function testGetRequestPaths()
    {
        $version    = '0-beta2';
        $operation  = 'read';
        $objectName = 'accounts-payable/vendor';
        $request = [];

        $handler = \RegistryLoader::getInstance($version)->getServiceHandler(\RegistryLoader::CRUD_SERVICE_NAME, null);
        $orch = new \GenericAPICRUDOrchestrator($handler);

        // test default-data success case
        $key = 'default-data';
        $extraParams[\APIConstants::API_EXTRA_PARAM_HIERARCHY] = ['accounts-payable', 'vendor', $key];
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_VERSION] = $version;
        $result = $orch->execute($operation, $request, $extraParams);
        $this->assertTrue(key_exists('key', $result));

        // test data-from-last success case
        $key = 'data-from-last';
        $extraParams[\APIConstants::API_EXTRA_PARAM_HIERARCHY] = ['accounts-payable', 'vendor', $key];
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_VERSION] = $version;
        $extraParams[\APIConstants::API_EXTRA_PARAM_CONTROL_PARAM][\APIConstants::API_URI_QUERY_STRING] = 'refKey=1&refType=class';
        parse_str('refKey=1&refType=class', $extraParams[\APIConstants::API_EXTRA_PARAM_CONTROL_PARAM][\APIConstants::API_URI_QUERY_PARAMS]);
        $result = $orch->execute($operation, $request, $extraParams);
        // Success is when the ia::status and ia::error are in the result as class is not a supported refType for vendor.
        $this->assertTrue(key_exists('ia::status', $result));
        $this->assertTrue(key_exists('ia::error', $result));
        // test data-from-last without required query string, error case
        $extraParams[\APIConstants::API_EXTRA_PARAM_CONTROL_PARAM][\APIConstants::API_URI_QUERY_STRING] = '';
        $extraParams[\APIConstants::API_EXTRA_PARAM_CONTROL_PARAM][\APIConstants::API_URI_QUERY_PARAMS] = [];
        try {
            $orch->execute($operation, $request, $extraParams);
        } catch (\APIException $e) {
            $this->assertStringContainsStringIgnoringCase(
                \APIErrorMessages::buildMessage(\APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0040, ["KEY" => 'refKey,refType', "OBJECT" => $objectName . '/'. $key] ),
                $e->getAPIError()->getMessage());
        }

        // test data-from-last with incorrect query string, error case
        $extraParams[\APIConstants::API_EXTRA_PARAM_CONTROL_PARAM][\APIConstants::API_URI_QUERY_STRING] = 'key=1';
        parse_str('key=1', $extraParams[\APIConstants::API_EXTRA_PARAM_CONTROL_PARAM][\APIConstants::API_URI_QUERY_PARAMS]);
        try {
            $orch->execute($operation, $request, $extraParams);
        } catch (\APIException $e) {
            $this->assertStringContainsStringIgnoringCase(
                \APIErrorMessages::buildMessage(\APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0011, ["PARAMETER" => 'key', "RESOURCE_NAME" => $objectName . '/'. $key, "VERSION" => $version] ),
                $e->getAPIError()->getMessage());
        }

        // test DELETE operation with default-data
        $operation = 'delete';
        $key = 'default-data';
        $extraParams[\APIConstants::API_EXTRA_PARAM_HIERARCHY] = ['accounts-payable', 'vendor', $key];
        $extraParams[\APIConstants::API_EXTRA_PARAM_CONTROL_PARAM][\APIConstants::API_URI_QUERY_STRING] = '';
        $extraParams[\APIConstants::API_EXTRA_PARAM_CONTROL_PARAM][\APIConstants::API_URI_QUERY_PARAMS] = [];
        try {
            $orch->execute($operation, $request, $extraParams);
        } catch (\APIException $e) {
            $this->assertStringContainsStringIgnoringCase(
                \APIErrorMessages::buildMessage(\APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0013, ["OPERATION" => \APIConstants::getOperation($operation), "EXTRA_PARAM" => '/'. $key] ),
                $e->getAPIError()->getMessage());
        }

    }

    public function testKeysAsQueryString()
    {
        $version = '1-beta2';
        $handler = \RegistryLoader::getInstance($version)
                                  ->getServiceHandler(\RegistryLoader::CRUD_SERVICE_NAME, null);
        $orch = new \GenericAPICRUDOrchestrator($handler);

        $extraParams[\APIConstants::API_EXTRA_PARAM_HIERARCHY] = [ 'accounts-payable', 'vendor' ];
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_OBJECT] = 'objects';
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_VERSION] = $version;

        // test a read operation first to get some keys
        $operation = 'read';
        $request = [];
        $res = $orch->execute($operation, $request, $extraParams);
        // print_r($res);
        $this->assertNotNull($res);
        $this->assertNotNull($res['ia::meta']['totalCount']);
        $totalCount = $res['ia::meta']['totalCount'];
        //assert that the total count is greater than 0
        $this->assertTrue($totalCount > 0);
        //get a few keys
        for ( $i = 0; $i < $totalCount; $i++ ) {
            $keys[] = $res['ia::result'][$i]['key'];
            if ( $i > 2 ) {
                break;
            }
        }

        // set keys as query string
        $queryString = 'keys=' . implode(',', $keys);
        $extraParams[\APIConstants::API_EXTRA_PARAM_CONTROL_PARAM][\APIConstants::API_URI_QUERY_STRING] = $queryString;
        parse_str($queryString,
                  $extraParams[\APIConstants::API_EXTRA_PARAM_CONTROL_PARAM][\APIConstants::API_URI_QUERY_PARAMS]);

        // test query string with read operation
        $res = $orch->execute($operation, $request, $extraParams);
        $this->assertIsArray($res);
        $this->assertTrue(count($res) == count($keys));

        // test query string with create operation
        try {
            $operation = 'create';
            $request    = [ 'description' => 'Test create with query param' ];
            $res = $orch->execute($operation, $request, $extraParams);
            $this->fail("should fail");
        } catch ( \APIException $e ) {
            $this->assertStringContainsStringIgnoringCase(
                \APIErrorMessages::buildMessage(\APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0011, [ "PARAMETER" => "keys", "RESOURCE_NAME" => 'accounts-payable/vendor', "VERSION" => $version] ),
                $e->getAPIError()->getMessage());
        }

        // test query string with update operation
        try {
            $operation = 'patch';
            $request    = [ 'description' => 'Test update with query param' ];
            $res = $orch->execute($operation, $request, $extraParams);
            $this->fail("should fail");
        } catch ( \APIException $e ) {
            $this->assertStringContainsStringIgnoringCase(
                \APIErrorMessages::buildMessage(\APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0016, [ "OPERATION" => "PATCH"] ),
                $e->getAPIError()->getMessage());
        }

        // test query string with delete operation
        $operation = 'delete';
        $request = [];
        $res = $orch->execute($operation, $request, $extraParams);
        $this->assertIsArray($res);
        $this->assertTrue(count($res) == count($keys));
    }

    public function testWrongParentRefInPayload()
    {
        $version    = '5-test';
        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orch = new \GenericAPICRUDOrchestrator($handler);

        $operation  = 'create';
        $extraParams = [ \APIConstants::API_EXTRA_PARAM_HIERARCHY => [ '__testglentry' ] ];
        $errorMsg = \APIErrorMessages::buildMessage(\APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0036, [
            "OPERATION" => 'POST',
            "OBJECT" => '__testglentry',
            "KEY" => 'glbatch'
        ]);

        // invalid glbatch structure
        $payload    = ['glbatch'=> '123'];
        try {
            $res = $orch->execute($operation, $payload, $extraParams);
            print_r($res);
            $this->fail("should fail");
        } catch (\APIException $e) {
            $this->assertStringContainsStringIgnoringCase($errorMsg, $e->getAPIError()->getMessage());
        }

        // missing key
        $payload    = [ 'glbatch' => ['name' => 'abc'] ];
        try {
            $res = $orch->execute($operation, $payload, $extraParams);
            print_r($res);
            $this->fail("should fail");
        } catch (\APIException $e) {
            $this->assertStringContainsStringIgnoringCase($errorMsg, $e->getAPIError()->getMessage());
        }

        // missing glbatch
        $payload    = [ 'name' => 'abc' ];
        try {
            $res = $orch->execute($operation, $payload, $extraParams);
            print_r($res);
            $this->fail("should fail");
        } catch (\APIException $e) {
            $this->assertStringContainsStringIgnoringCase($errorMsg, $e->getAPIError()->getMessage());
        }
    }

    public function testErrorChildBatchDelete()
    {
        $version    = '5-test-beta2';
        $operation  = 'read';
        $request = [];

        $handler = \RegistryLoader::getInstance($version)->getServiceHandler(\RegistryLoader::CRUD_SERVICE_NAME, null);
        $orch = new \GenericAPICRUDOrchestrator($handler);
        $extraParams[\APIConstants::API_EXTRA_PARAM_HIERARCHY] = ['gl', '__testglbatch'];
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_VERSION] = $version;
        $result = $orch->execute($operation, $request, $extraParams);
        if (count($result[\APIConstants::IA_RESULT_KEY]) < 2) {
            print "not enough data to test\n";
            $this->assertTrue(true);
            return;
        }

        $key1 = $result[\APIConstants::IA_RESULT_KEY][0]['key'];
        $key2 = $result[\APIConstants::IA_RESULT_KEY][1]['key'];

        $extraParams[\APIConstants::API_EXTRA_PARAM_HIERARCHY] = ['gl', '__testglbatch', "$key1,$key2"];
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_VERSION] = $version;
        $result = $orch->execute($operation, $request, $extraParams);
        $request = [
            [
                'key' => $key1,
                'entries' => [
                    [ "ia::operation" => "delete",  'key' => $result[0]['entries'][0]['key'] ?? 999999999]
                ]
            ],
            [
                'key' => $key2,
                'entries' => [
                    [ "ia::operation" => "delete", 'key' => $result[1]['entries'][0]['key'] ?? 999999999 ]
                ]
            ]
        ];
        $extraParams[\APIConstants::API_EXTRA_PARAM_HIERARCHY] = ['gl', '__testglbatch'];
        $operation  = 'patch';
        $result = $orch->execute($operation, $request, $extraParams);
        foreach ($result as $res) {
            $this->assertNotEquals(500, $res[\APIError::KEY_IA_STATUS], json_encode($res, 64));
        }
    }

    public static function setUpBeforeClass() : void
    {
        parent::setUpBeforeClass();
        print_r("\n" .'==================== [' . __CLASS__ . ' START] ====================' . "\n");

    }

    public static function tearDownAfterClass() : void
    {
        print_r("\n" . '==================== [' . __CLASS__ . ' END] ====================' . "\n\n");
        parent::tearDownAfterClass();
    }

}
