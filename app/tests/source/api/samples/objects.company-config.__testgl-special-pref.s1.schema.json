{"mappedTo": "glbatch", "fields": {"key": {"mappedTo": "__special_setup_key__", "type": "string", "description": "The key", "readOnly": true, "pattern": "/^setup$/"}, "batchNo": {"mappedTo": "BATCHNO", "type": "integer", "description": "The transaction number", "required": true, "nullable": false}, "batchTitle": {"mappedTo": "BATCH_TITLE", "type": "string", "description": "The description", "required": true, "nullable": false}, "balance": {"mappedTo": "BALANCE", "type": "number", "description": "The balance"}, "batchDate": {"mappedTo": "BATCH_DATE", "type": "string", "format": "date", "description": "The posting date", "required": true, "nullable": false}, "state": {"mappedTo": "STATE", "type": "string", "description": "The state", "required": true, "nullable": false}, "journal": {"mappedTo": "JOURNAL", "type": "string", "description": "Journal", "required": true, "nullable": false}, "journalKey": {"mappedTo": "JOURNALKEY", "type": "string", "description": "Journal id"}}, "groups": {"audit": {"fields": {"whenCreated": {"mappedTo": "WHENCREATED", "type": "string", "format": "date-time", "description": "Time of the submission", "readOnly": true}, "whenModified": {"mappedTo": "WHENMODIFIED", "type": "string", "format": "date-time", "description": "Time of the modification", "readOnly": true}, "createdBy": {"mappedTo": "CREATEDBY", "type": "string", "description": "User who created this", "readOnly": true}, "modifiedBy": {"mappedTo": "MODIFIEDBY", "type": "string", "description": "User who modified this", "readOnly": true}}}, "vat": {"fields": {"taxImplications": {"mappedTo": "TAXIMPLICATIONS", "type": "string", "enum": [null, "None", "Inbound", "Outbound"], "description": "Tax Implications"}}, "refs": {"contact": {"apiObject": "co/__testcontact", "fields": {"key": {"mappedTo": "VATCONTACTKEY", "type": "string"}, "id": {"mappedTo": "VATCONTACTID", "type": "string"}}}}}}, "lists": {"entries": {"apiObject": "company-config/__testglentry-special-pref", "mappedTo": "ENTRIES"}}, "httpMethods": "GET,POST,PATCH,DELETE,OPTIONS"}