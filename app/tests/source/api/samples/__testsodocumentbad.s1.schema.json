{"mappedTo": "sodocument", "fields": {"key": {"mappedTo": "RECORDNO", "type": "string", "readOnly": true}, "id": {"mappedTo": "DOCID", "type": "string", "mutable": false}, "documentNo": {"mappedTo": "DOCNO", "type": "string"}, "documentTemplateName": {"mappedTo": "DOCPARID", "type": "string", "mutable": false}, "status": {"mappedTo": "STATUS", "type": "string"}, "contractDescription": {"mappedTo": "CONTRACTDESC", "type": "string"}}, "groups": {"audit": {"fields": {"whenCreated": {"mappedTo": "WHENCREATED", "type": "string", "format": "date-time", "description": "Time of the submission", "readOnly": true}, "whenModified": {"mappedTo": "WHENMODIFIED", "type": "string", "format": "date-time", "description": "Time of the modification", "readOnly": true}, "createdBy": {"mappedTo": "CREATEDBY", "type": "string", "description": "User who created this", "readOnly": true}, "modifiedBy": {"mappedTo": "MODIFIEDBY", "type": "string", "description": "User who modified this", "readOnly": true}}}}, "refs": {"customer": {"apiObject": "__testcustomer", "mappedTo": "customer", "fields": {"key": {"mappedTo": "CUSTVENDID", "type": "string"}, "id": {"mappedTo": "CUSTVENDNAME", "type": "string"}}}}, "httpMethods": "GET,POST,PATCH,DELETE,OPTIONS"}