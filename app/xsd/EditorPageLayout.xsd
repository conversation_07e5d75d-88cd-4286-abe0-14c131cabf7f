<?xml version="1.0" encoding="UTF-8"?>
<schema xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.intacct.com/EditorPageLayout" xmlns:tns="http://www.intacct.com/EditorPageLayout" elementFormDefault="qualified">

    <element name="view" type="tns:ViewType"></element>

    <complexType name="ViewType">
    	<sequence>
    		<element name="tabs" type="tns:TabsType" maxOccurs="1"
    			minOccurs="0">
    		</element>
    		<element name="pages" type="tns:PagesType" maxOccurs="1"
    			minOccurs="0">
    		</element>
    	</sequence>
    </complexType>

    <complexType name="TabsType">
    	<sequence>
    		<element name="tab" type="string"></element>
    	</sequence>
    </complexType>
    
    <complexType name="PagesType">
    	<sequence>
    		<element name="tab" type="string"></element>
    	</sequence>
    </complexType>
</schema>