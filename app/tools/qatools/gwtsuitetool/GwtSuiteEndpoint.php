<?php
require_once 'GwtSuiteTemplate.php';
require_once 'GwtSuiteMain.php';

try {
    switch ($_REQUEST['op']) {
        case 'template':
            $call = new CreateGstTemplate();
            break;
        case 'suite':
            $call = new CreateGwtSuite();
            break;
        case 'requests':
            $call = new CreatePostmanRequests();
            break;
        default:
            throw new DomainException("Invalid call");
    }
    $call->run();
} catch ( Exception $e ) {
    Globals::$g->gErr->addError('0', __FILE__ . ':' . __LINE__, $e->getMessage());
    include '../acct/popuperror.phtml';
    exit(1);
}