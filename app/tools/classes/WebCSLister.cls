<?php
    //=============================================================================
    //
    //	FILE:        CSWebLister.cls
    //	AUTHOR:      <PERSON><PERSON><PERSON> (ovi) <<EMAIL>>
    //	DESCRIPTION: cs tools lister web component class
    //
    //	(C)2021, Intacct Corporation, All Rights Reserved
    //
    //	Intacct Corporation Proprietary Information.
    //	This document contains trade secret data that belongs to Intacct
    //	corporation and is protected by the copyright laws. Information herein
    //	may not be used, copied or disclosed in whole or part without prior
    //	written consent from Intacct Corporation.
    //
    //=============================================================================

    /**
     * Class WebCSLister
     */
    class WebCSLister {
        const INVALID_REQUEST = 1;
        /** @var string $name */
        public $name;
        /**
         * @var array $columns lister columns
         *                     format: array(name => array(column, label, type, width, filter), ...)
         *                     filter key is optional; if not present, the column does not support filtering
         */
        public $columns = array();
        /**
         * @var array $data data to be displayed in the lister
         *                  format: array(array(COUNT, col1, col2, col3, ...))
         *                  COUNT has the total number of records, not just the ones displayed
         */
        public $data = array();
        /** @var bool $hasFilters has filters on top of columns */
        public $hasFilters = true;
        /**
         * @var array $filters filter values
         *                     format: array(column => filter_value)
         */
        public $filters = array();
        /** @var bool $hasOrdering has ordering links on column titles */
        public $hasOrdering = true;
        /** @var int $page current page */
        public $page = 1;
        /** @var int $rowsPerPage maximum number of rows per page */
        public $rowsPerPage = 50;
        /** @var string $order order type for $orderBy column (asc|desc) */
        public $order = "asc";
        /** @var string $orderBy lister column to order by */
        public $orderBy;
        /** @var string $baseURL base url for paginator and ordering links */
        public $baseURL = null;
        /** @var string $template the template used for rendering the lister */
        public $template = "cs_lister";
        /** @var string $title the lister title, it will be shown in the lister header */
        public $title = null;
        /** @var WebCSMessageBox $oMessageBox an instance of message box, it will be available from the lister header */
        public $oMessageBox = null;

        /**
         * WebCSLister constructor.
         *
         * @param string $p_name
         */
        public function __construct($p_name) {
            $this->name = $p_name;
        }

        /**
         * initializes lister by processing data from request: filters, order, current page
         *
         * @param array $p_request
         *
         * @return int
         */
        public function Init($p_request) {
            // validate request params
            if (isset($p_request[$this->name . "_order"])
                and ($p_request[$this->name . "_order"] !== "asc" and $p_request[$this->name . "_order"] !== "desc")) {

                return self::INVALID_REQUEST;
            }

            if (isset($p_request[$this->name . "_order_by"])
                and !in_array($p_request[$this->name . "_order_by"], array_keys($this->columns), true)) {

                return self::INVALID_REQUEST;
            }

            // TODO ovi - validate page_number (int, between min, max)

            // filters
            foreach ($this->columns as $column => $column_info) {
                if (isset($column_info["filter"])) {
                    $this->filters[$column_info["column"]] = $p_request[$this->name . "_filter_" . $column] ?? $column_info["filter"];
                }
            }
            $this->hasFilters = count($this->filters) > 0;

            // ordering
            $this->order = $p_request[$this->name . "_order"] ?? $this->order;
            $this->orderBy = $p_request[$this->name . "_order_by"] ?? $this->orderBy;

            // paginator
            $this->page = max(1, (int)($p_request[$this->name . "_page"] ?? 1));

            return 0;
        }

        /**
         * generate the lister html to be displayed in the browser
         *
         * @return string
         */
        public function Render() {
            // filters
            $url_filters = "";
            if ($this->hasFilters) {
                foreach ($this->columns as $column => $column_info) {
                    if (($this->filters[$column_info["column"]] ?? "") !== "") {
                        $url_filters .= "&" . $this->name . "_filter_" . $column . "=" . urlencode($this->filters[$column_info["column"]]);
                    }
                }
            }

            $url_start = "?";
            if ($this->baseURL !== null) {
                // baseURL is not necessarily set, if you don't need ordering, filtering and pagionation
                $url_start = $this->baseURL . (mb_strpos($this->baseURL, "?") === false ? "?" : "&");
            }

            // column header urls for ordering
            if ($this->hasOrdering) {
                foreach ($this->columns as $column => &$column_info) {
                    if ($column === $this->orderBy) {
                        $column_info["order"] = $this->order;
                        $column_info["url"] = $url_start . $this->name . "_page=" . $this->page . "&" . $this->name . "_order=" . ($this->order === "asc" ? "desc" : "asc") . "&" . $this->name . "_order_by=" . $column . $url_filters;
                    } else {
                        $column_info["url"] = $url_start . $this->name . "_page=" . $this->page . "&" . $this->name . "_order=asc&" . $this->name . "_order_by=" . $column . $url_filters;
                    }
                }
                unset($column_info);
            }

            // setup paginator
            if (count($this->data) === 0) {
                $pg_all_rows = 0;
            } else {
                $pg_all_rows = $this->data[0]["COUNT"] ?? count($this->data);
                $pg_block_size = 5;
                // if we have less than 10 pages, we display all of them
                $pg_min_blocks = 10;
                // the total number of blocks
                $pg_number_of_pages = ceil($pg_all_rows / $this->rowsPerPage);

                if ($pg_number_of_pages < $pg_min_blocks) {
                    // the index for the first block in the group to which it belongs
                    $pg_start_index = 1;
                    // the index for the last block in the group to which it belongs
                    $pg_end_index = $pg_number_of_pages;
                } else {
                    $pg_prev_blocks_count = floor($pg_block_size / 2);
                    $pg_next_blocks_count = ($pg_block_size % 2 === 0) ? $pg_prev_blocks_count - 1 : $pg_prev_blocks_count;

                    if ($this->page <= $pg_prev_blocks_count) {
                        $pg_start_index = 1;
                        $pg_end_index = $pg_block_size;
                    } else if ($this->page + $pg_next_blocks_count > $pg_number_of_pages) {
                        $pg_start_index = $pg_number_of_pages - $pg_block_size + 1;
                        $pg_end_index = $pg_number_of_pages;
                    } else {
                        $pg_start_index = $this->page - $pg_prev_blocks_count;
                        $pg_end_index = $this->page + $pg_next_blocks_count;
                    }
                }

                // paginator buttons
                $pg_buttons = array();
                // add the buttons <<, <, 1
                if ($pg_start_index > 1 and $pg_number_of_pages >= $pg_min_blocks) {
                    $pg_buttons[] = array(
                        "url" => $url_start . $this->name . "_page=" . max($this->page - $pg_block_size, 1) . "&" . $this->name . "_order=" . $this->order . "&" . $this->name . "_order_by=" . $this->orderBy . $url_filters,
                        "selected" => false,
                        "format" => "fa-angle-double-left"
                    );
                    $pg_buttons[] = array(
                        "url" => $url_start . $this->name . "_page=" . max($this->page - 1, 1) . "&" . $this->name . "_order=" . $this->order . "&" . $this->name . "_order_by=" . $this->orderBy . $url_filters,
                        "selected" => false,
                        "format" => "fa-angle-left"
                    );
                    $pg_buttons[] = array(
                        "url" => $url_start . $this->name . "_page=1&" . $this->name . "_order=" . $this->order . "&" . $this->name . "_order_by=" . $this->orderBy . $url_filters,
                        "selected" => false,
                        "format" => "number",
                        "value" => "1"
                    );
                    $pg_buttons[] = array("format" => "fa-ellipsis-h", "display" => "block");
                }

                // add the group of blocks
                if ($pg_number_of_pages > 1) {
                    for ($index = $pg_start_index; $index <= $pg_end_index; $index++) {
                        $pg_buttons[] = array(
                            "url" => $url_start . $this->name . "_page=" . $index . "&" . $this->name . "_order=" . $this->order . "&" . $this->name . "_order_by=" . $this->orderBy . $url_filters,
                            "selected" => $this->page == $index,
                            "format" => "number",
                            "value" => $index
                        );
                    }
                }

                // add the buttons n (the last block), >, >>
                if ($pg_number_of_pages >= $pg_min_blocks and $pg_end_index !== $pg_number_of_pages) {
                    $pg_buttons[] = array("selected" => false, "format" => "fa-ellipsis-h", "display" => "block");
                    $pg_buttons[] = array(
                        "url" => $url_start . $this->name . "_page=" . $pg_number_of_pages . "&" . $this->name . "_order=" . $this->order . "&" . $this->name . "_order_by=" . $this->orderBy . $url_filters,
                        "selected" => false,
                        "format" => "number",
                        "value" => $pg_number_of_pages
                    );
                    $pg_buttons[] = array(
                        "url" => $url_start . $this->name . "_page=" . min($this->page + 1, $pg_number_of_pages) . "&" . $this->name . "_order=" . $this->order . "&" . $this->name . "_order_by=" . $this->orderBy . $url_filters,
                        "selected" => false,
                        "format" => "fa-angle-right"
                    );
                    $pg_buttons[] = array(
                        "url" => $url_start . $this->name . "_page=" . min($this->page + $pg_block_size, $pg_number_of_pages) . "&" . $this->name . "_order=" . $this->order . "&" . $this->name . "_order_by=" . $this->orderBy . $url_filters,
                        "selected" => false,
                        "format" => "fa-angle-double-right"
                    );
                }
                $pg_buttons = count($pg_buttons) === 1 ? array() : $pg_buttons;
            }

            // get base url params; needed for filters form
            $base_params = array();
            if ($this->hasFilters) {
                $url_pieces = explode("?", $this->baseURL);
                if (isset($url_pieces[1])) {
                    $pairs = explode("&", $url_pieces[1]);
                    foreach ($pairs as $pair) {
                        $param = explode("=", $pair);
                        $base_params[$param[0]] = $param[1];
                    }
                }
            }

            // template data
            $t = array(
                "lister_name" => html_prepare($this->name),
                "lister_title" => html_prepare($this->title),
                "oMessageBox" => $this->oMessageBox,
                "columns" => html_prepare($this->columns),
                "data" => html_prepare($this->data),
                "pagination" => array(
                    "all_no" => $pg_all_rows,
                    "first_no" => ($this->page - 1) * $this->rowsPerPage + 1,
                    "last_no" => min($this->page * $this->rowsPerPage, $pg_all_rows),
                    "current_page" => $this->page ?? 1,
                    "number_of_pages" => $pg_number_of_pages ?? 0,
                    "buttons" => $pg_buttons ?? array(),
                ),
                "has_filters" => $this->hasFilters,
                "template" => $this->template,
            );

            if ($this->hasFilters) {
                $t["filters"] = html_prepare($this->filters);
                $t["filter_params"] = array_merge($base_params,
                    array(
                        $this->name . "_order" => $this->order,
                        $this->name . "_order_by" => $this->orderBy,
                    )
                );
            }

            return render("cs_lister.tpl.inc", $t, false);
        }
    }

