<?php
/**
 * Redirect page for SubsystemConfiguration lister in CSTools
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Intacct Corporation All, Rights Reserved
 */

import('Lister');
import('NLister');
import('UIControl');
import('SubsystemManager.cls');
import('SubsystemLister.cls');
import('Request');

require_once 'show_listing.inc';

$entity = &Request::$r->entity;
$entity = 'subsystemconfiguration';

$verb = Request::$r->_do;
$action = Request::$r->{Editor_Action};
$_done = &Request::$r->_done;
$_readtime = &Request::$r->_readtime;
$_r = Request::$r->_r;
$_record = Request::$r->_record;
$cny = Request::$r->cny;

SetDBSchemaVars('0', '01');
Backend_Init::SetEnvironment($cny);
$appurl = "HTTPS://".$_SERVER['HTTP_HOST'].RootPath()."/acct/";

switch ($verb) {
case 'delete':
    $manager = new SubsystemConfigurationManager();
    $ok = $manager->Delete($_record);

    if ($ok) {
        Redirect("subsystems_mgmnt_console.phtml?page=subsystemconfiguration");
    }
    break;
case 'create':
    header(
        "Location: ".$appurl."editor.phtml?.op=" .
        GetOperationId('cerp/lists/subsystemconfiguration/create') .
        "&.done=../cstools/".$_done
    );
    break;
case 'edit':
    header(
        "Location: ".$appurl."editor.phtml?.op=" .
        GetOperationId('cerp/lists/subsystemconfiguration/edit') . "&.r=" . $_r .
        "&.do=edit&.it=subsystemconfiguration&.done=../cstools/".$_done
    );
    break;
default:
    try {
        $lister = new SubsystemConfigurationLister();
        $okProcessing = $lister->processSubmit();

        if (!$okProcessing) {
            include_once 'popuperror.phtml';
            exit(0);
        }

        $lister->BuildTable();
    } catch (Exception $ex) {
        global $gErr;
        LogToFile($ex);
        $gErr->addError(
            'FILTER_ERROR', $ex->getFile() . '.' . $ex->getLine(),
            'Filter Error', $ex->getMessage()
        );
        include_once 'popuperror.phtml';
        exit(0);
    }
    $lister->DrawHTML();
}