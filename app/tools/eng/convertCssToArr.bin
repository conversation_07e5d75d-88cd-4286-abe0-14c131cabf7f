#!/usr/local/php/bin/php -q
<?php

define('CSSINCFOLDER', "../../build/");

// deployed on dev09 try02
//Set the include path for 'link/private/inc' and 'source/*' directories
ini_set(
    'include_path', ini_get('include_path')
    . "../../link/private/inc:../../source/*:../../link/private/lib/log4php:../../link/private/lib/php4:.:../../link/private/lib/misc"
);

//Get the arguments from the command prompt
chdir(trim(getenv("PWD")));
$args = $_SERVER['argv'];

//Check for required args
if ( !in_array(sizeof($args), array(2)) ) {
    error_log("Usage: $args[0] CSS_FILE_NAME\n");
    die(1);
}

$cssFilesArgument = [];
if ( is_dir($args[1]) ) {
    $dir = $args[1];
    foreach ( scandir($dir) as $filename ) {
        if ( $filename !== '.' && $filename !== '..' ) {
            $cssFilesArgument [] = $dir . '/' . $filename;
        }
    }
} else {
    $cssFilesArgument [] = $args[1];
}

foreach ($cssFilesArgument as $arg) {
    //Create new file name to store CSS values as array
    $tmpName = explode("/", $arg);
    preg_match("/(.*).mcss/i", $tmpName[count($tmpName) - 1], $regs);
    $arrFile = CSSINCFOLDER . $regs[1] . '.inc';


    $lookupmap = array(
        "fcolor" => "color",
        "bcolor" => "background-color",
        "bold" => "font-weight",
        "size" => "font-size",
        "align" => "text-align"
    );

    $cssFiles = array(
        'report-alt-xls.mcss',
        'report-alt.mcss',
        'intacct_styles.mcss',
    );

    $cssFName = explode("/", $arg);

    // Create map only for required css files
    if ( in_array(end($cssFName), $cssFiles) ) {

        $css = file_get_contents($arg);

        $res = BreakCSS($css);

        $str
            = "<?
        \$css   =   array (";

        foreach ($res as $k => $val) {
            $str .= "\n\t'" . strtoupper($k) . "'";
            $str .= ConstructStringFromArray($val);
        }

        //Write the footer information
        $str .= "\n);\n";
        //echo '<pre>';
        //print_r(array(__FILE__.":". __LINE__, $arrFile));
        //echo '</pre>';
        file_put_contents($arrFile, $str);

        //var_dump($arrFile);
        //
        //var_dump($str);
    }
}

/**
 * @param string[] $dataArray
 *
 * @return string
 */
function ConstructStringFromArray($dataArray)
{
    $resultStr = '';
    $resultStr .= " => array(";
    foreach ($dataArray as $hdr => $hdrval) {
        $resultStr .= "\n\t\t'" . trim($hdr) . "'\t=> \"" . htmlentities(trim($hdrval), ENT_COMPAT) . "\",";
    }
    $resultStr .= "),";
    return $resultStr;
}

/**
 * @param string $css
 *
 * @return array
 */
function BreakCSS($css)
{

    $results = array();

    $cssLookupList = array(
        'font-size' => 'fsize',
        'font-family' => 'font',
        'background' => 'bcolor',
        'font-weight' => 'fweight',
        'white-space' => 'wrap',
        'text-align' => 'halign',
        'mso-number-format' => 'numformat',
        'border' => 'border',
        'border-top' => 'bordert',
        'border-bottom' => 'borderb',
        'border-left' => 'borderl',
        'border-right' => 'borderr',
    );

    // anything, possible space, curly bracket, possible space, anything, close curly bracket
    // please refer http://stackoverflow.com/questions/1215074/break-a-css-file-into-an-array-with-php
    preg_match_all('/(.+?)\s?\{\s?(.+?)\s?\}/', $css, $matches);

    foreach ( $matches[0] AS $i => $original) {
        foreach (explode(';', $matches[2][$i]) AS $attr) {
            if ( strlen(trim($attr)) > 0 ) // for missing semicolon on last element, which is legal
            {
                list($name, $value) = explode(':', $attr);

                $cssName = trim($name);

                if ( isset($cssLookupList[$cssName]) ) {
                    if ( strstr($name, 'border') ) {
                        $borderStyle = explode(" ", $value);
                        $value = trim($borderStyle[1]);
                    } else {
                        $value = trim($value);
                    }
                    if ( $cssName === 'background' ) {
                        $value = hex2rgb($value);
                    }
                    $cssNewName = $cssLookupList[$cssName];
                    $results[$matches[1][$i]][$cssNewName] = $value;
                }
            }
        }
    }
    return $results;
}

/**
 * @param string $hex
 *
 * @return bool|string
 */
function hex2rgb($hex)
{
    $hex = str_replace("#", "", $hex);

    if ( strlen($hex) == 3 ) {
        $r = hexdec(substr($hex, 0, 1) . substr($hex, 0, 1));
        $g = hexdec(substr($hex, 1, 1) . substr($hex, 1, 1));
        $b = hexdec(substr($hex, 2, 1) . substr($hex, 2, 1));
    } else {
        if ( strlen($hex) == 6 ) {
            $r = hexdec(substr($hex, 0, 2));
            $g = hexdec(substr($hex, 2, 2));
            $b = hexdec(substr($hex, 4, 2));
        } else {
            return false;
        }
    }
    $rgb = array($r, $g, $b);

    return implode(',', $rgb); // returns an string with the rgb values
}
