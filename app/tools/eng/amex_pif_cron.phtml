<?
//=============================================================================
//
//	FILE:			amex_pif_cron.phtml
//	AUTHOR:			Nara
//	DESCRIPTION: Invoked by CRON job to process AMEX outsourced checks processing.
//
//	(C)2014, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information herein
//	may not be used, copied or disclosed in whole or part without prior
//	written consent from Intacct Corporation.
//
//=============================================================================
require_once 'util.inc';
require_once "ims_include.inc";
require_once "LogManager.cls";
define("AMEX_OFFLINE_ENTRY", "AMEX_OFFLINE_PROCESS");

if ( !ValidateAccess() ) {
    logFL();
    exit;
}

header('content-type: text/plain');

perform_pifUpload($_REQUEST['_user'], $_REQUEST['_pwd']);

/**
 * @param string $user
 * @param string $pwd
 */
function perform_pifUpload($user, $pwd)
{
    // #ims-schemalet
    $lock = IMSBeginBlock(AMEX_OFFLINE_ENTRY, true, false);
    $source = $_SERVER['SCRIPT_NAME'];

    if ($lock === false) {
        $msg = "AMEX: " . $source . ", " . "Unable to acquire IMS lock, exiting...\n";
        echo $msg;
        LogToFile($msg, LOG_FILE, true);
        exit;
    }

    echo(date("Y-m-d G:i:s ", time()) . "Start amex_pif_cron\n");
    LogToFile(date("Y-m-d G:i:s ", time()) . "Start amex_pif_cron\n");


    $results = DBRunner::runOnAllDBsInCurrentPOD(
        'RunAMEXPifCron',
        [$user, $pwd],
        null,
        false,
        [__FILE__]
    );
    $results = DBRunner::mergeRunOnSomeResults($results);
    echo implode('', $results);

    echo(date("Y-m-d G:i:s ", time()) . "End amex_pif_cron\n");
    LogToFile(date("Y-m-d G:i:s ", time()) . "End amex_pif_cron.\n");

    // #ims-schemalet release the lock
    IMSEndBlock($lock);
}

/**
 * @param string $user
 * @param string $pwd
 *
 * @return string|false  the errors
 */
function RunAMEXPifCron($user, $pwd)
{
    $error = false;
    $amexUtil = new AmexCronUtil($user, $pwd);
    $ok = $amexUtil->sendPIFFiles();
    if (!$ok) {
        $gErr = Globals::$g->gErr;
        if ($gErr->hasErrors()) {
            $err = $gErr->myToString(false);
            $error = "ERROR: Outsourced Payment Processing SFTP(AMEX) Issue:\n" . $err . "\n";
            $gErr->Clear();
        }
    }

    return $error;
}
