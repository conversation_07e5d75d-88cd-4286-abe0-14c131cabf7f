<?php
    /**
     * Subject Area Admin Tool
     *  folders template
     *
     * <AUTHOR> <<EMAIL>>
     * @copyright 2017 Intacct Corporation, All Rights Reserved
     */

    // init vars to make phpstorm inspections happy :)
    $t_current_sa = $t_current_sa ?? [];
    $t_file_key = $t_file_key ?? null;
    $t_file_name = $t_file_name ?? null;
    $t_sa_key = $t_sa_key ?? null;
    $t_checked_out = $t_checked_out ?? null;
    $t_objects = $t_objects ?? [];
?>
<h1>Folders (<?=$t_current_sa["sa"];?>)</h1>
<div class="breadcrumb">
    <a href="./obi_sa_manager.phtml">Files</a> <span class="fa icon-dright"></span>
    <a href="?page=sas&file_key=<?=$t_file_key;?>"><?=$t_file_name;?></a> <span class="fa icon-dright"></span>
    <a href="?page=objects&file_key=<?=$t_file_key;?>&sa_key=<?=$t_sa_key;?>"><?= $t_current_sa["sa"];?> objects</a> <span class="fa icon-dright"></span>
    <?=$t_current_sa["sa"];?> folders
</div>
<div class="menu">
    <form name="frm_ops" action="" method="post">
        <input name="file_key" type="hidden" value="<?=$t_file_key;?>"/>
        <input name="sa_key" type="hidden" value="<?=$t_sa_key;?>"/>
        <a href="?page=objects&file_key=<?=$t_file_key;?>&sa_key=<?=$t_sa_key;?>" class="btn">objects</a>
        <a href="?page=hierarchies&file_key=<?=$t_file_key;?>&sa_key=<?=$t_sa_key;?>" class="btn">hierarchies</a>
        <? if (!$t_checked_out) { ?>
        <input name="btn_checkout" type="submit" value="checkout" class="btn"/>
        <? } else if ($t_checked_out) { ?>
        <input name="btn_cancel" type="submit" value="cancel" class="btn"/>
        <input name="btn_save" type="submit" value="save" class="btn"/>
        <? } ?>
    </form>
</div>
<div id="error" style="padding:5px;text-align:center;color:red;display:none"></div>
<ul id="folders">
    <?=render_folders($t_objects, (bool)$t_checked_out);?>
</ul>
<div id="overlay" class="overlay" style="display:none;">
    <img src="../resources/obi_sa_tool/loading.gif" class="loading"/>
    <span class="overlay-msg">saving...</span>
</div>

<script>
gb.sa_key = '<?=$t_sa_key;?>';
gb.file_key = '<?=$t_file_key;?>';

var enter_submit = function(e) {
    if (e.keyCode == 13) {
        this.onblur();
    }
};
var elems = document.getElementsByTagName("input");
for (var i = 0; i < elems.length; i++) {
    if (elems[i].id.indexOf("txt_") == 0) {
        elems[i].onkeypress = enter_submit;
    }
}
</script>
