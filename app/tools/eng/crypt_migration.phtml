<?php
/**
 * =============================================================================
 *
 * Migration utility to encrypt database columns.
 *
 * LICENSE:
 * (C)2000-2020 Intacct Corporation, All Rights Reserved
 *
 * Intacct Corporation Proprietary Information.
 * This document contains trade secret data that belongs to Intacct
 * corporation and is protected by the copyright laws. Information herein
 * may not be used, copied or disclosed in whole or part without prior
 * written consent from Intacct Corporation.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2020 Intacct Corporation
 * =============================================================================
 */

require_once 'util.inc';
include_once 'CryptMigration.cls';
Request::FilterRequest();
InitBase();
InitGlobals();

$syncObj   = new CryptMigration();
$sync      = Request::$r->_sync;
$dryrun    = Request::$r->_dryrun;
$dstschema = Request::$r->_dstschema;
$company   = Request::$r->_company;
$field     = Request::$r->_field;

$syncObj->setLogFile($field);
if ($sync && !$dryrun) {
    $syncObj->setDryRun(false);
}

if ($sync || $dryrun) {
    TimerMark('start');
    StartTimer('cryptSync');

    // We'll not allow selcting both company and schema inputs together
    if ($company && $dstschema) {
        $syncObj->out(" Can not select both company and schema values... ");
        dieFL();
    }

    if (isset($dstschema) && $dstschema != '') {
        $schemas = explode('#~#', $dstschema);
    }

    if (in_array('ALLSCHEMAS', $schemas) || (isset($company) && $company != '')) {
        $dbschemas = $syncObj->getSchemas($company);
        $schemas = array_keys($dbschemas);
    }

    if (isset($field) && !$syncObj->canBeEncrypted($field)) {
        $syncObj->out(" You have to select a valid field -> check CryptMigration constants' values");
        dieFL();
    }

    $ok = $syncObj->sync($schemas, $field, $company);
    if (!$ok) {
        $msg = "\n****************FOUND ERRORS****************\n";
    }
    if (HasErrors() && Globals::$g->gErr->ErrorCount) {
        $errors = Globals::$g->gErr->__toString();
        $msg .= "-------------------------------------------------------\n";
        $msg .= "Errors:\n";
        $msg .= "-------------------------------------------------------\n";
        $msg .= $errors;
        $msg .= "\n";
    }
    StopTimer('cryptSync');
    $msg .= TimerReport();
    $syncObj->out($msg);
    exit(0);
}
