<?php

/**
 * LICENSE:
 * (C)1999-2016 Intacct Corporation, All Rights Reserved
 *
 * Intacct Corporation Proprietary Information.
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws. Information herein
 * may not be used, copied or disclosed in whole or part without prior
 * written consent from Intacct Corporation.
 *
 * <AUTHOR> M <<EMAIL>>
 * @copyright 2016 Intacct Corporation
 */

require 'DataFixToolsManager.cls';

/**
 * Class HistoricallyImportedDataDelete contains relevant functions for delete operation
 *
 * @access public
 * <AUTHOR> M <<EMAIL>>
 */
class HistoricallyImportedDataDelete extends DataFixToolsManager 
{
    const ACTION_HOME = 'Home';
    const ACTION_ANALYZE = 'Analyze';
    const ACTION_COMMIT = 'Commit';
    const ACTION_CSTOOLHOME = 'Redirect';
    /** @var array $failureID  */
    private $failureID ;

    /**
     * @param string $tool Tool Name
     *
     * @access public
     */
    public function __construct($tool)
    {
        parent::__construct($tool);
    }
    
    /**
     * Entire Tool action will be taken care from here
     *
     * @param string $action for the tool
     *
     * @access public
     */
    public function processAction($action)
    {
        switch ($action)
        {
        case self::ACTION_HOME:
                $this->home();
            break;
        case self::ACTION_ANALYZE:
                $this->analyze();
            break;
        case self::ACTION_COMMIT:
                $this->commit();
            break;
        case self::ACTION_CSTOOLHOME;
                $this->redirect();
            break;
        }
    }
    
    /**
     * Displaying Error Screen if the user has not uploaded the CSV
     *
     * @access private
     * @return int   0
     */
    private function uploadErrorScreen()
    {
        ?>
        <form action="HistoricallyImportedDataDelete.phtml?.r=<?php echo Request::$r->_r; ?>" method="post"
              enctype="multipart/form-data" name="form1" id="form1">
            <br/>
            <?php
            echo "<hr/><br/>";
            $this->out(
                "<p>\n<font color='red'><b>ERROR:Please upload the CSV file.</b></font></p>"
            );
            echo "<hr/>";
            ?><br/>
            <input type="hidden" id="action" name=".action" value="">
            <input type="hidden" id="index" name=".index" value="">
            <input type="submit" name=".back" value="Go Back" onclick="HomeScreen('')"/>
            <input type="submit" name=".home" value="Home" onclick="HomeScreen('Redirect')"/>
        </form>
        <?php
        return 0;
    }

    /**
     * Displaying error screen for those record#/recordid which do not exist in the company
     *
     * @param string $message    Display Message
     * @param string $columnName Column Name
     *
     * @access private
     *
     * @return int   0
     */
    private function showFailedRecordScreen($message, $columnName)
    {
        ?>
        <form action="HistoricallyImportedDataDelete.phtml?.r=<?php echo Request::$r->_r; ?>" method="post"
              enctype="multipart/form-data" name="form1" id="form1">
            <br/>
            <?php
            // This will print all those Record#/RecordID which is not present in the company
            /** @noinspection PhpUnusedLocalVariableInspection */
            $checked = Request::$r->_radioid;
            $_invalid_id_array = array();
            if (Request::$r->action != self::ACTION_COMMIT) {
                echo "<hr/><br/>";
                $this->out(
                    "<p>\n<font color='#1e90ff'><b>Please wait while we validate the data from the CSV....</b>
                    </font></p>"
                );
            }
            echo "<hr/>";
            ?>  <br/><br/>
            <?php
            $this->out(
                "<b><font color='red'>".$message."</font></b>"
            );
            ?><br/>
            <table border="1">
                <tr>
                    <td style="width: 200px;background: blanchedalmond;">
                        <b><?php echo $columnName;?></b>
                    </td>
                </tr>
                <?php
                if (empty($_invalid_id_array)) {
                    $_invalid_id_array = $this->failureID;
                }
                foreach ($_invalid_id_array as $val) {
                    ?>
                    <tr>
                        <td style="width: 200px;"><?php echo $val; ?></td>
                    </tr>
                    <?php
                }
                ?>
            </table>
            <br/>
            <?php
            echo "<hr/><br/>";
            ?>
            <input type="hidden" id="action" name=".action" value="">
            <input type="hidden" id="index" name=".index" value="">
            <input type="submit" name=".back" value="Go Back" onclick="HomeScreen('')"/>
            <input type="submit" name=".home" value="Home" onclick="HomeScreen('Redirect')"/>
            </form>
            <?php
            return 0;
    }

    /**
     * Displaying the Correct/Validated Data
     *
     * @param array $dataArray Array of Record#/RecordNo
     *
     * @access private
     */
    private function correctData($dataArray)
    {
        $checked = Request::$r->_radioid;
        if ($checked == 'matchrecord') {
            $columnbit = 'RECORD#';
        } else if ($checked == 'matchid') {
            $columnbit = 'RECORDID';
        } else if ($checked == 'matchprtitle') {
            $columnbit = 'PRBATCHTITLE';
        } else {
            $columnbit = 'PRBATCHKEY';
        }
        ?>
        <form action="HistoricallyImportedDataDelete.phtml?.r=<?php echo Request::$r->_r; ?>" method="post"
              enctype="multipart/form-data" name="form1" id="form1">
            <br/>
            <b>HISTORICALLY IMPORTED DATA DELETE TOOL</b>
            <br/>
            <br/><hr/><br/>
            <?php
            $this->out(
                "<b><font color='black'>Historically Imported Data associated with the following $columnbit will be"
                ." deleted on click of 'Commit'.</font></b>"
            );
            ?><br/>
            <table border="1">
                <tr>
                    <td style="width: 200px; background: lightgreen;"><?php $this->out("<b> $columnbit </b>"); ?>
                    </td>
                </tr>
            </table>
            <?php
            foreach ($dataArray as $val) {
                ?>
                <table border="1">
                    <tr>
                        <td style="width: 200px;"> <?php echo $val; ?> </td>
                    </tr>
                </table>
                <?php
            }?>
            <br/>
            <?php
            echo "<hr/><br/>";
            ?>
            <input type="hidden" id="action" name=".action" value="">
            <input type="hidden" id="index" name=".index" value="">
            <input type="hidden" id="commit" name=".commit" value="">
            <input type="hidden" id="checked" name=".radioid" value="<?php echo Request::$r->_radioid; ?>">
            <input type="hidden" id="recordType" name=".recordType" value="<?php echo Request::$r->_recordType; ?>">
            <input type="hidden" id="csv" name=".csv" value="<?php $importFilePath = "/tmp";
            $pushfilepath = INTACCTtempnam($importFilePath, "IntacctDeleteCSV");
            $filepath = base64_encode($pushfilepath);
            if (!move_uploaded_file($_FILES['csv']['tmp_name'], $pushfilepath)) {
                echo("There is some problem with uploading the file");
            }
            echo $filepath;?>">
            <input type="submit" name=".back" value="Go Back" onclick="HomeScreen('')"/>
            <input type="submit" name=".commit" value="Commit" onclick="HomeScreen('Commit')"/>
            <input type="submit" name=".home" value="Home" onclick="HomeScreen('Redirect')"/>
        </form>
        <?php
    }

    /**
     * Home screen for the tool after validating the Intacct user and Tool Authorization
     */
    private function home()
    {
        $this->deleteFileName();
        ?>
        <form action="HistoricallyImportedDataDelete.phtml?" method="post" enctype="multipart/form-data" name="form1" id="form1">
            <br/>
            <b>HISTORICALLY IMPORTED DATA DELETE TOOL</b>
            <br/>
            <hr/>
            <br/>
            <b>Company ID:</b>
            <label><?php echo $this->compinfo['TITLE']; ?></label>
            <b>&nbsp;&nbsp;Record Type</b>
            <select name=".recordType">
                <option value="ri">ri</option>
                <option value="pi">pi</option>
            </select>
            <b>&nbsp;&nbsp;&nbsp;Upload the CSV file</b> <input id="csv" type="file" name="csv"/>
            <br/><br/>
            <font color='#ff8c00'><b>Match with record# </b></font><input type="radio" name=".radioid" id="mrecord"
                                                                          value="matchrecord"
                                                                          onclick="getRadioAction('matchrecord')"/>
            <font color='#ff8c00'><b>Match with ID </b></font><input type="radio" name=".radioid" id="mid"
                                                                     value="matchid"
                                                                     onclick="getRadioAction('matchid')"/>
            <font color='#ff8c00'><b>Match with PRBATCH Title </b></font><input type="radio" name=".radioid" id="mprtitle"
                                                                     value="matchprtitle"
                                                                     onclick="getRadioAction('matchprtitle')"/>
            <font color='#ff8c00'><b>Match with PRBATCH Key </b></font><input type="radio" name=".radioid" id="mprkey"
                                                                     value="matchprkey"
                                                                     onclick="getRadioAction('matchprkey')"/>

            <br/><br/>
            <input type="hidden" id="actionname" name=".action" value="">
            <input type="hidden" id="cny" name=".r" value="">
            <input type="submit" name=".debug" value="Analyze"
                   onclick="setAction('Analyze','<?php echo Request::$r->_r; ?>');"/>
            <input type="submit" name=".save" value="Commit"
                   onclick="setAction('Commit','<?php echo Request::$r->_r; ?>'); "/><br/><br/>
            <hr/>
            <br/></CENTER>
        </form>

        <b> NOTE: <br/><br/> </b>
        <b>* Select a Record Type : </b><font color='red'><b>'ri' </b></font><b>for Invoices, </b>
            <font color='red'><b>'pi' </b></font><b>for Bills</b> .<br/>
        <b>* It is mandatory to choose one among this</b><font color='red'><b> (Match with record#, Match with
            ID, Match with PRBATCH Title or Match with PRBATCH Key) </b></font> .<br/>
        <b>* If you choose <font color='red'><b> Match with record# </b></font>, then the header of First Column in CSV
            must be <font color='red'><b>'RECORD#'</b></font> .</b><br/>
        <b>* If you choose <font color='red'><b> Match with ID </b></font>, then the header of First Column in CSV
            must
            be </b><font color='red'><b>'RECORDID'</b></font> .<br/>
        <b>* If you choose <font color='red'><b> Match with PRBATCH Title </b></font>, then the header of First
            Column in CSV must be <font color='red'><b>'PRBATCHTITLE'</b></font> .</b><br/>
        <b>* If you choose <font color='red'><b> Match with PRBATCH Key </b></font>, then the header of First
            Column in CSV must be <font color='red'><b>'PRBATCHKEY'</b></font> .</b><br/>
        <b>* <font color='red'><b>'Analyze'</b></font><b> button will validate and display the analysis of
                CSV input .</b><br/>
        <b>* After 'Analyze', if there is no <font color='red'><b>'ERROR'</b></font>, Click 'Commit' button to
            delete Historically Imported Data . <br/></b>
        <b>* The tool will delete the data only if both the Bills/Invoices and their corresponding payments are Historically
            Imported . <br/></b>

        <?php
    }

    /**
     * Analyzing the CSV to validate whether contents are correct or not
     *
     * @access private
     */
    private function analyze()
    {
        $ok = $this->validateCSV();
        if ($ok[0]) {
            $this->correctData($ok[2]);
        }
    }

    /**
     * Committing to DB after successful validation of Record#/RecordID
     *
     * @access private
     */
    private function commit()
    {
        $ok = $this->validateCSV();

        if ($ok[0] && Request::$r->_action == self::ACTION_COMMIT) {
            $resultFlag = $this->deleteHistoricallyImportedData($ok[1], $ok[3]);
            $this->deleteFileName();

            if ($resultFlag) {
                $memo = addoraslashes(
                    "Customer support user ".$this->userName." has invoked Engineering tool i.e., ".
                    "(Historically Imported Data Delete Tool) to Delete Historically Imported Bills/Invoices".
                    " in ".getenv('IA_SERVER_NAME')." server with the".
                    " tracking id ".Globals::$g->perfdata->getSerialnbr()." at ". date("m/d/Y h:i:s")
                );
                LogCSAction("CSTOOLS_TOOLNAME_ENG_HISTORICALLY_IMPORTED_DATA_DELETE", $this->userName, $memo, Request::$r->_r);
                $msg = "Historically imported data, for the input from CSV has been deleted successfully";
                $this->printMessage($msg, true);
            } else {
                $this->printMessage("While deleting Historically Imported Data", false);
            }
        }
    }

    /**
     * Validating the CSV
     *
     * @access private
     * @return int|array
     */
    private function validateCSV()
    {
        $ok = true;
        $showFailedID = false;
        $recordNumber = [];
        $paymentKey = [];

        $checked = Request::$r->_radioid;
        $recordType = Request::$r->_recordType;
        $cny = Request::$r->_r;

        $msg = '';

        if ( $recordType == 'ri' ) {
            $typeOfRecord = 'Invoices';
        } else {
            $typeOfRecord = 'Bills';
        }

        if ($checked == 'matchrecord') {
            $columnbit = 'RECORD#';
        } else if ($checked == 'matchid') {
            $columnbit = 'RECORDID';
        } else if ($checked == 'matchprtitle') {
            $columnbit = 'PRBATCHTITLE';
        } else {
            $columnbit = 'PRBATCHKEY';
        }

        if ($checked == 'matchrecord' || $checked == 'matchid' || $checked == 'matchprtitle'
            || $checked == 'matchprkey'
        ) {
            $file = $_FILES['csv']['tmp_name'];
            if ($file != '') {
                $flag = 1;
                $bit = $this->readCSV($flag);
                $recordIdOrNumber = $bit[1];
            } else if (isset(Request::$r->_csv)) {
                $flag = 2;
                $bit = $this->readCSV($flag);
                $recordIdOrNumber = $bit[1];
            } else {
                $this->status = $this->printMessage("Please upload the CSV file", false);
                return $this->status;
            }

            if ($bit[0]
                && isset($recordIdOrNumber)
                && $recordIdOrNumber[0][0] == $columnbit
            ) {
                // If Match with ID or Match With Record# is selected
                if ($checked == 'matchid' || $checked == 'matchrecord') {
                    array_shift($recordIdOrNumber);
                    if ($checked == 'matchrecord') {
                        sort($recordIdOrNumber);
                        foreach ( $recordIdOrNumber as $recValue) {
                            $columnData[] = $recValue[0];
                            foreach ($columnData as $columnVal) {
                                if (!is_numeric($columnVal)) {
                                    $this->status = $this->printMessage("RECORD# must be numeric only. Please correct and re-upload the CSV file", false);
                                    return $this->status;
                                }
                            }
                        }
                    } else {
                        foreach ( $recordIdOrNumber as $data) {
                            $columnData[] = $data[0];
                        }
                    }
                    /** @noinspection PhpUndefinedVariableInspection */
                    $final_array = array_unique($columnData);
                    $QueryResult = $this->getListID($recordIdOrNumber, $checked);

                    //If ID/Record# exists in that company
                    if (count($QueryResult) > 0) {
                        //This set of logic is required to make sure if any RECORDID/RECORD# which is provided in
                        //csv doesn't exist in that company
                        foreach ( $QueryResult as $arrayvalue) {
                            $actualValue[] = $arrayvalue[$columnbit];
                            $recordNumber[] = $arrayvalue['RECORD#'];
                        }

                        /** @noinspection PhpUndefinedVariableInspection */
                        $this->failureID = array_diff($final_array, $actualValue);
                        $_failed_count = count($this->failureID);

                        if ($_failed_count > 0) {
                            $showFailedID = true;
                            $msg = "Following is the list of ".$columnbit." that do not match with any Historically Imported ".$typeOfRecord.
                                " in the company or there are blank lines in CSV.<BR/> Please correct and re-upload the CSV file";
                            $ok = false;
                        } else {
                            $paymentCheck = $this->paymentNoGlCheck($recordNumber, $columnbit);
                            if (count($paymentCheck) > 0) {
                                foreach ( $paymentCheck as $arrayvalue) {
                                    $noGLFalsePayment[] = $arrayvalue[$columnbit];
                                }
                                /** @noinspection PhpUndefinedVariableInspection */
                                $noGLFalsePaymentList = array_intersect($final_array, $noGLFalsePayment);
                                if (!empty($noGLFalsePaymentList)) {
                                    $this->failureID = $noGLFalsePaymentList;
                                    $_failed_count = count($this->failureID);

                                    if ($_failed_count > 0) {
                                        $showFailedID = true;
                                        $msg = "Following is the list of " . $columnbit . " of " . $typeOfRecord . " whose payment does not" .
                                            " reflect as Historically Imported data in the company";
                                        $ok = false;
                                    }
                                } else {
                                    $recNoStr = implode(",", $recordNumber);
                                    $paymentKeyQuery = "select PAYMENTKEY from prentrypymtrecs where cny# = :1 and ".
                                        "RECORDKEY in (" . $recNoStr . ")";
                                    $paymentKeyArray = QueryResult(array($paymentKeyQuery, $cny));
                                    foreach ( $paymentKeyArray as $value) {
                                        $paymentKey[] = $value['PAYMENTKEY'];
                                    }
                                }
                            } else {
                                $recNoStr = implode(",", $recordNumber);
                                $paymentKeyQuery = "select PAYMENTKEY from prentrypymtrecs where cny# = :1 and ".
                                    "RECORDKEY in (" . $recNoStr . ")";
                                $paymentKeyArray = QueryResult(array($paymentKeyQuery, $cny));
                                foreach ( $paymentKeyArray as $value) {
                                    $paymentKey[] = $value['PAYMENTKEY'];
                                }
                            }
                        }
                    } else {
                        $this->failureID = $final_array;
                        $_failed_count = count($this->failureID);

                        if ($_failed_count > 0) {
                            $showFailedID = true;
                            $msg = "Following is the list of ".$columnbit." that do not match with any Historically Imported ".$typeOfRecord.
                                " in the company or there are blank lines in CSV.<BR/> Please correct and re-upload the CSV file.";
                            $ok = false;
                        }
                    }
                } else if ($checked == 'matchprtitle' || $checked == 'matchprkey') {
                    $prBatchKeyOrTitle = $recordIdOrNumber;
                    array_shift($prBatchKeyOrTitle);
                    if ($checked == 'matchprkey') {
                        sort($prBatchKeyOrTitle);
                        foreach ( $prBatchKeyOrTitle as $recValue) {
                            $columnData[] = $recValue[0];
                            foreach ($columnData as $columnVal) {
                                if (!is_numeric($columnVal)) {
                                    $this->status = $this->printMessage("PRBATCHKEY must be numeric only.
                                     Please correct and re-upload the CSV file.", false);
                                    return $this->status;
                                }
                            }
                        }
                    } else {
                        foreach ( $prBatchKeyOrTitle as $data) {
                            $columnData[] = $data[0];
                        }
                    }
                    /** @noinspection PhpUndefinedVariableInspection */
                    $final_array = array_unique($columnData);
                    $QueryResult = $this->getListIDprBatch($final_array, $checked);

                    //If PRBATCH KEY/TITLE exists in that company
                    if (count($QueryResult) > 0) {
                        //This set of logic is required to make sure if any PRBATCH KEY/TITLE which is provided in
                        //csv doesn't exist in that company
                        foreach ( $QueryResult as $arrayvalue) {
                            $actualValue[] = $arrayvalue['PRBATCHVALUE'];
                            $prBatchKey[] = $arrayvalue['PRBATCHKEY'];
                            $recordNumber[] = $arrayvalue['RECORD#'];
                        }

                        /** @noinspection PhpUndefinedVariableInspection */
                        $prBatchKeyArr = array_unique($prBatchKey);

                        /** @noinspection PhpUndefinedVariableInspection */
                        $this->failureID = array_diff($final_array, $actualValue);
                        $_failed_count = count($this->failureID);

                        if ($_failed_count > 0) {
                            $showFailedID = true;
                            $msg = "Following is the list of ".$columnbit." that do not match with any Historically Imported ".$typeOfRecord.
                                " in the company or there are blank lines in CSV.<BR/> Please correct and re-upload the CSV file.";
                            $ok = false;
                        } else {
                            if ($columnbit == 'PRBATCHTITLE') {
                                $columnPrBatch = 'TITLE';
                            } else if ($columnbit == 'PRBATCHKEY') {
                                $columnPrBatch = 'RECORD#';
                            }
                            /** @noinspection PhpUndefinedVariableInspection */
                            $paymentBatchCheck = $this->paymentBatchNoGlCheck($prBatchKeyArr, $columnPrBatch);
                            if (count($paymentBatchCheck) > 0) {
                                foreach ( $paymentBatchCheck as $arrayvalue) {
                                    $noGLFalsePaymentBatch[] = $arrayvalue[$columnPrBatch];
                                }
                                /** @noinspection PhpUndefinedVariableInspection */
                                $noGLFalsePaymentBatchList = array_intersect($final_array, $noGLFalsePaymentBatch);
                                if (!empty($noGLFalsePaymentBatchList)) {
                                    $this->failureID = $noGLFalsePaymentBatchList;
                                    $_failed_count = count($this->failureID);

                                    if ($_failed_count > 0) {
                                        $showFailedID = true;
                                        $msg = "Following is the list of " . $columnbit . " whose corresponding payment does not" .
                                            " reflect as Historically Imported data in the company";
                                        $ok = false;
                                    }
                                } else {
                                    $recNoStr = implode(",", $recordNumber);
                                    $paymentKeyQuery = "select PAYMENTKEY from prentrypymtrecs where cny# = :1 and ".
                                        "RECORDKEY in (" . $recNoStr . ")";
                                    $paymentKeyArray = QueryResult(array($paymentKeyQuery, $cny));
                                    foreach ( $paymentKeyArray as $value) {
                                        $paymentKey[] = $value['PAYMENTKEY'];
                                    }
                                }
                            } else {
                                $recNoStr = implode(",", $recordNumber);
                                $paymentKeyQuery = "select PAYMENTKEY from prentrypymtrecs where cny# = :1 and ".
                                    "RECORDKEY in (" . $recNoStr . ")";
                                $paymentKeyArray = QueryResult(array($paymentKeyQuery, $cny));
                                foreach ( $paymentKeyArray as $value) {
                                    $paymentKey[] = $value['PAYMENTKEY'];
                                }
                            }
                        }
                    } else {
                        $this->failureID = $final_array;
                        $_failed_count = count($this->failureID);

                        if ($_failed_count > 0) {
                            $showFailedID = true;
                            $msg = "Following is the list of ".$columnbit." that do not match with any Historically Imported ".$typeOfRecord.
                                " in the company or there are blank lines in CSV.<BR/> Please correct and re-upload the CSV file.";
                            $ok = false;
                        }
                    }
                }

                if ($showFailedID == true) {
                    $this->status = $this->showFailedRecordScreen($msg, $columnbit);
                    return $this->status;
                }
            } else if ($bit[0]) {
                    $this->status = $this->printMessage("Please make sure that csv column header is correct", false);
                    return $this->status;
            } else {
                    $this->status = $this->printMessage("Please make sure that CSV is not empty or it does not contain
            more than 999 rows", false);
                    return $this->status;
            }
        } else {
            $this->status = $this->printMessage("Please select a radio button :
                        'Match with record#' or 'Match with ID' or 
                        'Match with PRBATCH Title' or 'Match with PRBATCH Key'", false);
            return $this->status;
        }

        /** @noinspection PhpUndefinedVariableInspection */
        return array( $ok, $recordNumber, array_values($final_array), $paymentKey);
    }

    /**
     * Reading from CSV
     *
     * @param int $flag 1 or 0
     *
     * @access private
     * @return int|array     It contains CSV Content
     */
    private function readCSV($flag)
    {
        $row = 0;
        $csvData = [];
        if ($flag == 1) {
            $file = $_FILES['csv']['tmp_name'];
        } else if ($flag == 2) {
            $file = base64_decode(Request::$r->_csv);
        } else {
            $this->status = $this->uploadErrorScreen();
            return $this->status;
        }
        $_handle = fopen($file, "rb");
        while (($data = fgetcsv($_handle, 50000, ",")) !== false) {
            $row++;
        }

        $pos = 0;
        if ($row > 0 && $row <= 999) {
            //loop through the csv file
            $_handle = fopen($file, "rb");
            do {
                if ($data) {
                    $csvData[] = $data;
                    /** @noinspection OnlyWritesOnParameterInspection */
                    $pos++;
                }
            } while ($data = fgetcsv($_handle, 50000, ",", '"', "\n"));
            $bit=1;
        } else {
            $bit=0;
        }
        return array($bit,$csvData);
    }

    /**
     * Forming the array after reading the csv and getting the record# or recordid based on the option selected in UI
     * i.e., Match with record# or Match with Id
     *
     * @param array  $csvarray Array of CSV Data
     * @param string $bit      matchid or matchrecord
     *
     * @access private
     * @return array
     */
    private function getListID($csvarray, $bit)
    {
        $table = '';
        $column = '';
        $selectColumn = '';
        $csvData = array();
        $finaldata = '';
        $openBraces = '';
        $closingBraces = '';
        $recordType = Request::$r->_recordType;

        if ($bit == 'matchid') {
            $table = 'PRRECORD';
            $column = 'RECORDID';
            $selectColumn = "RECORD#,RECORDID";
            $openBraces = "('";
            $closingBraces = "') AND RECORDTYPE = '".$recordType."'";
            foreach ( $csvarray as $data) {
                $csvData[] = $data[0];
            }
            $finaldata = implode("','", $csvData);
        } else if ($bit == 'matchrecord') {
            $table = 'PRRECORD';
            $column = 'RECORD#';
            $selectColumn = "RECORD#,RECORDID";
            $openBraces = "(";
            $closingBraces = ") AND RECORDTYPE = '".$recordType."'";
            foreach ( $csvarray as $data) {
                $csvData[] = $data[0];
            }
            $finaldata = implode(",", $csvData);
        }
        $queryStatement = "Select ".$selectColumn." FROM ".$table." WHERE CNY# = :1 and " . $column . " in ".$openBraces.
                           $finaldata . $closingBraces . " AND EXISTS ".
                           "(SELECT 1 FROM PRBATCH WHERE CNY# = :1 AND NOGL='T' AND PRBATCH.RECORD#=PRRECORD.PRBATCHKEY)";
        $result = QueryResult(array($queryStatement, Request::$r->_r));
        return $result;
    }

    /**
     * Forming the array after reading the csv and getting the prrecord record# and prbatch key/title based on the option selected in UI
     * i.e., Match with PRBATCHKEY or Match with PRBATCHTITLE
     *
     * @param array  $dataArray Array of CSV Data - prbatch title/key
     * @param string $bit       matchprtitle or matchprkey
     *
     * @access private
     * @return array
     */
    private function getListIDprBatch($dataArray, $bit)
    {
        $column = '';
        $openBraces = '';
        $closingBraces = '';
        $finaldata = '';
        $recordType = Request::$r->_recordType;

        if ($bit == 'matchprtitle') {
            $column = 'TITLE';
            $openBraces = "('";
            $closingBraces = "') AND PR.RECORDTYPE = '".$recordType."'";
            $finaldata = implode("','", $dataArray);
        } else if ($bit == 'matchprkey') {
            $column = 'RECORD#';
            $openBraces = "(";
            $closingBraces = ") AND PR.RECORDTYPE = '".$recordType."'";
            $finaldata = implode(",", $dataArray);
        }
        $queryStatement = "SELECT P1.RECORD#, PR.".$column." AS PRBATCHVALUE, PR.RECORD# AS PRBATCHKEY FROM PRRECORD P1, PRBATCH PR WHERE ".
                            "P1.CNY# = :1 AND PR.CNY# = P1.CNY# AND P1.PRBATCHKEY = PR.RECORD# AND PR.".$column." IN ".
                           $openBraces.$finaldata.$closingBraces." AND NOGL='T'";
        $result = QueryResult(array($queryStatement, Request::$r->_r));
        return $result;
    }

    /**
     * Core function - After validating the Record#/RecordID from CSV with company data, historically imported data
     * associated with the given Record#/RecordID is deleted from the company.
     *
     * @param array $recordNum  Array of Record# of PRRECORD - Bill/Inv
     * @param array $paymentKey Array of Record# of PRRECORD - Payment
     *
     * @access private
     * @return bool   ok?
     */
    private function deleteHistoricallyImportedData($recordNum, $paymentKey)
    {
        XACT_BEGIN('HistoricallyImportedDataDelete');
        StartTimer('HistoricallyImportedDataDelete');

        $ok = true;

        $recordNumbers = $recordNum;

        $recordNumPayKey = array_merge($recordNum, $paymentKey);
        $recordNumPayKey = array_unique($recordNumPayKey);

        foreach ($this->getTableAndColumnDetails('TABLE') as $index => $inside) {
            if (is_array($inside)) {
                if ($inside['executeFirst']) {
                    $ok = $ok && $this->queryBuildAndExecute($index, $inside['column'], $recordNumPayKey);
                } else {
                    $ok = $ok && $this->queryBuildAndExecuteLast($index, $inside['column'], $inside['innerTable']);
                }
            } else {
                $ok = $ok && $this->queryBuildAndExecute($index, $inside, $recordNumbers);
            }
        }

        $ok = $ok && XACT_COMMIT('HistoricallyImportedDataDelete');

        if (!$ok) {
            XACT_ABORT('HistoricallyImportedDataDelete');
        }

        StopTimer('HistoricallyImportedDataDelete');
        return $ok;
    }

    /**
     * Get the table and column details
     *
     * @param string $identifier TABLE
     *
     * @access private
     * @return array
     */
    private function getTableAndColumnDetails($identifier)
    {
        $tableColumn = array(
            'TABLE' => array(
                'PRENTRYPYMTRECS' => 'RECORDKEY',
                'PRENTRY' => array('column' => 'RECORDKEY', 'executeFirst' => true),
                'PRRECORD' => array('column' => 'RECORD#', 'executeFirst' => true),
                'PRBATCH' => array('column' => array('NOGL', 'PRBATCHKEY', 'RECORD#'), 'innerTable' => 'PRRECORD', 'executeFirst' => false)
            )
        );
        return $tableColumn[$identifier];
    }

    /**
     * Build and execute delete query
     *
     * @param string $table     TableName
     * @param string $column    ColumnName
     * @param int[] $recordNum RecordNumbers
     *
     * @return bool  ok?
     */
    private function queryBuildAndExecute($table,$column,$recordNum)
    {
        $stmt = array("delete from $table where cny# = :1 ", GetMyCompany());
        $stmt = PrepINClauseStmt($stmt, $recordNum, " and ".$column);
        $ok = ExecStmt($stmt);
        return $ok;
    }

    /**
     * Build and execute final delete query
     *
     * @param string $table      TableName
     * @param array  $columns    Column Name
     * @param string $innerTable Inner Table Name
     *
     * @return bool  ok?
     */
    private function queryBuildAndExecuteLast($table,$columns,$innerTable)
    {
        $delQryFinal = "DELETE FROM " . $table . " WHERE CNY#=:1 AND " . $columns[0] . " = 'T' AND NOT EXISTS (" .
            "SELECT 1 FROM " . $innerTable . " WHERE CNY#=:1 AND " . $columns[1] . " = " . $table .".". $columns[2] . ")";
        $stmt = array($delQryFinal, GetMyCompany());
        $ok = ExecStmt($stmt);
        return $ok;
    }


    /**
     * NOGL check for payment of Bill/Invoice
     *
     * @param array  $dataArray Array of Record#
     * @param string $bit       RecordID or Record#
     *
     * @access private
     * @return array
     */
    private function paymentNoGlCheck($dataArray, $bit)
    {
        $finaldata = implode(",", $dataArray);

        $queryStatement = "select p1.".$bit." from prentrypymtrecs prp, prrecord p, prbatch b, prrecord p1 where ".
                            "prp.recordkey in (".$finaldata.") and prp.paymentkey = p.record# and ".
                              "b.record# = p.prbatchkey and b.nogl = 'F' and prp.recordkey=p1.record# and p.cny#=:1 ".
                                "and p.cny#=b.cny# and b.cny#=prp.cny# and prp.cny#=p1.cny#";
        $result = QueryResult(array($queryStatement, Request::$r->_r));
        return $result;
    }

    /**
     * NOGL check for payment batch
     *
     * @param array  $dataArray Array of PRBATCHKEY
     * @param string $bit       Title or Record#
     *
     * @access private
     * @return array
     */
    private function paymentBatchNoGlCheck($dataArray, $bit)
    {
        $finaldata = implode(",", $dataArray);

        $queryStatement = "select distinct b1.".$bit." from prentrypymtrecs prp, prrecord p1, prrecord p2, prbatch b, ".
                            "prbatch b1 where p1.prbatchkey in (".$finaldata.") and prp.recordkey = p1.record# and ".
                              "b.record# = p2.prbatchkey and p2.record# = prp.paymentkey and b.nogl = 'F' and ".
                                "p1.prbatchkey=b1.record# and p1.cny#=:1 and p1.cny#=b.cny# and b.cny#=prp.cny# and ".
                                  "p1.cny#=p2.cny# and p2.cny#=b1.cny#";
        $result = QueryResult(array($queryStatement, Request::$r->_r));
        return $result;
    }

}
