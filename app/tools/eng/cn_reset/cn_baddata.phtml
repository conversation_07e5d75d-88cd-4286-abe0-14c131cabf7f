<?php
    //=============================================================================
    //
    //	FILE:        cn_badata.phtml
    //	AUTHOR:      <PERSON><PERSON> <<EMAIL>>
    //	DESCRIPTION: this is master page from reset page will load
    //
    //	(C)2021, Intacct Corporation, All Rights Reserved
    //
    //	Intacct Corporation Proprietary Information.
    //	This document contains trade secret data that belongs to Intacct
    //	corporation and is protected by the copyright laws. Information herein
    //	may not be used, copied or disclosed in whole or part without prior
    //	written consent from Intacct Corporation.
    //
    //=============================================================================

    $r_page = Request::$r->page;

    try {
        // check requested page
        $pages = array("reset", "ajax");
        if (in_array($r_page, $pages, true)) {
            $page = $r_page;
        } else if ($r_page === null) {
            // default page
            $page = "reset";
        } else {
            // invalid page; 404
            include "cs_not_found.inc";

            exit;
        }

        require_once "cs_common.inc";

        // frame template data
        $t["tool_styles"] = array("cn_reset.css", "font-awesome.min.css");
        $t["tool_scripts"] = array("cn_reset.js");

        // load page script
        ob_start();
        $requirePage = "cn_reset_" . $page . ".inc";

        require $requirePage;
    } catch (Exception $e) {
        ob_clean();

        $t_exception = $e;
        include_once "cs_exception.inc";
    }

    header("Content-Type: text/html; charset=utf-8");
    ob_end_flush();
