<?php
/**
 * EntityTree tool. Some of the code is adapted from entvalidator.bin
 *  <AUTHOR> adapted from EntityValidator.cls
 * @copyright 2021 Intacct Corporation, All Rights Reserved
 */

class EntityTree
{
    // arguments
  const CTRL_ARG_KEY_ENTFILE = 'entfile=';

  // the validation result
  /** @var array $beenThere */
  private static $beenThere = [];

    /**
     * @param string[] $argv
     *
     * @return bool
     */
    public static function run($argv)
    {
        $entName = "";

        foreach ($argv as $arg) {
            if (strpos($arg, self::CTRL_ARG_KEY_ENTFILE) !== false) {
                // entfile=
                $entName = substr($arg, strlen(self::CTRL_ARG_KEY_ENTFILE));

            }
        }
        // should be a function so it can be called from the .bin file?  and be in one place
      if ($entName == "") {
        echo "\nerror: missing entfile argument\n\n";
        echo "valid arguments:\n";
        echo "\tentfile=<.ent file without the .ent extension>\n";

        echo "\tgetentarr    <optional: No = after this.  It will echo out the entity file PHP array>\n";

        exit;
      }

      $tm = RegistryLoader::getInstance('0-beta2')->getObjectNameForMappedToName('apbillbatch');
      echo "is this it: " . $tm;
exit ('testing');

    }

  /**
   * @param string $entName
   * @param string $showAs
   * @param int $maxDepth
   * @param string $entityChildAry
   */
    public static function findEntity($entName, $showAs, $maxDepth=2, $entityChildAry='children')
    {
      try {
        $tree = [];
        $root = self::recurse([], $tree, $entName, $entityChildAry);
        $data = '';
        if ($showAs == 'chart') {
          $root->chartDFS([], '', $data);
          $entityChart = self::createOrgChartFileNode($data);
          echo $entityChart;
        } elseif ($showAs == 'gotree') {
          $root->gojsTreeDFS([], '', $data, $maxDepth);
          $entityTree = self::createGojsTreeChartFileNode($data);
          echo $entityTree;
        } elseif ($showAs == 'gochart') {
          $root->gojsTreeDFS([], '', $data, $maxDepth);
          $entityChart = self::createGojsChartFileNode($data);
          echo $entityChart;
        } elseif ($showAs == 'text') {
          echo '<textarea id="entview" name="entview" rows="40" cols="80" style="border: 1px solid black; height: 80%">';
          $root->printDFS([], $maxDepth);
          echo '</textarea>';
        } else {
          $root->treeDFS([], '', $data, $maxDepth);
          $entityTree = self::createTreeChartFileNode($data);
          echo $entityTree;
        }
      } catch (Throwable $e) {
        echo "\n exception: " . $e->getMessage() . " File: " . $e->getFile() . 'Line: ' . $e->getLine() . "\n";
      }
    }

  /**
   * @param array  $tree
   * @param string $entityName
   * @param string $parent
   * @param string $altEntityName
   */
    public static function processEntity(&$tree, $entityName, $parent, $altEntityName='') {
      if (self::$beenThere[$entityName]) {
        return;
      } else {
        self::$beenThere[$entityName] = true; // node here if already there.
      }
      // var_dump(self::getEntDefinitions($entName));
      // load the entity file
      $entityInfo = self::getEntDefinitions($entityName);
      if (!$entityInfo && $altEntityName != '') {
        $entityInfo = self::getEntDefinitions($altEntityName);
        if ($entityInfo) {
            $entityName = $altEntityName;
        }
      }
      //if ($entityName != $parent) $tree[] = 'parent: ' . $parent;
      $tree[$parent][] = $entityName;
      foreach ($entityInfo['children'] as $key => $value) {
        // make sure it is in the nexus to include in the tree.
 //       if (array_key_exists($key, $entityInfo['nexus'])) {
          echo "key: $key, value['table']: " . $value['table'] . "\n";
          self::processEntity($tree, $key, $entityName, $value['table']);

 //       }
      }

    }

  /**
   * @param array $tree
   * @return string
   */
    public static function createOrgChartFile ($tree) {
      $header = '<html>
  <head>
  <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
       <script type="text/javascript">
             google.charts.load("current", {packages:["orgchart"]});
      google.charts.setOnLoadCallback(drawChart);

      function drawChart() {
        var data = new google.visualization.DataTable();
        data.addColumn("string", "Name");
        data.addColumn("string", "Manager");
        data.addColumn("string", "ToolTip");
        data.addRows([
';
      $returnStr = $header;
      foreach ($tree as $key => $value) {
        foreach ($value as $cv) {
          $returnStr .= '["' .  $cv . '","' . $key . '",""],' . "\n";
        }
      }
      $returnStr .= "])";
      $returnStr .= "        // Create the chart.
        var chart = new google.visualization.OrgChart(document.getElementById('chart_div'));
        // Draw the chart, setting the allowHtml option to true for the tooltips.
        chart.draw(data, {'allowHtml':true, 'allowCollapse':true, 'size':'small'});
      }
   </script>
    </head>
  <body>
    <div id='chart_div'></div>
  </body>
</html>";
      return $returnStr;
    }

  /**
   * @param string $data
   * @return string
   */
  public static function createOrgChartFileNode ($data) {
    $returnStr = '<html>
  <head>
  	<title>Awesome Entity Chart Viewer</title>

  <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
       <script type="text/javascript">
             google.charts.load("current", {packages:["orgchart"]});
      google.charts.setOnLoadCallback(drawChart);

      function drawChart() {
        var data = new google.visualization.DataTable();
        data.addColumn("string", "Name");
        data.addColumn("string", "Manager");
        data.addColumn("string", "ToolTip");
        data.addRows([
';
    $returnStr .= $data;
    $returnStr .= "])";
    $returnStr .= "        // Create the chart.
        var chart = new google.visualization.OrgChart(document.getElementById('chart_div'));
        // Draw the chart, setting the allowHtml option to true for the tooltips.
        chart.draw(data, {'allowHtml':true, 'allowCollapse':true, 'size':'small'});
      }
   </script>
    </head>
  <body>
    <div id='chart_div'></div>
  </body>
</html>";
    return $returnStr;
  }

  /**
   * @param string $data
   * @return string
   */
  public static function createTreeChartFileNode($data)
  {
    $returnStr = <<<'tcfn'
<!DOCTYPE html>
<html lang="en">
	<head>
	<title>Awesome Entity Tree Viewer</title>
	<link href="jquery.treegrid.css" rel="stylesheet">

<script type="text/javascript"   src="jquery-1.12.0.min.js"></script>
<script type="text/javascript"   src="jquery.treegrid.min.js"></script>

</head>
<body>

<table class="tree">

  <tbody>

tcfn;
    $returnStr .= $data;
    $returnStr .= "
  </tbody>

</table>
<script type='text/javascript'>
$('.tree').treegrid();
</script></body>
</html>";
    return $returnStr;
  }

  /**
   * @param string $title
   * @return string
   */
  public static function gojsBeginning($title = 'Awesome Entity Tree Viewer') {
    $gojsSource = '../resources/thirdparty/gojs/release/go.js';
    $returnStr = '<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>' . $title . '</title>
    <script src="' . $gojsSource . '"></script>
  </head>
  <body onload="init()">
    <div class="p-4 w-full">
    <script id="code">
    // 1/12/22 new key for version 2.2:  switch to this when it gets released.
    go.Diagram.licenseKey = "73f942e7b26128a800ca0d2b113f69ee1bb37b319e831ff35e5341f5ef0c694370c9ed7958d68fc3c0e848fd4a75c1dd8e973d7c9145053aee35d6894ab082adb53671b7125845dcf7562296caa829f6fb2d79f1d0a571f78a7e8ca0bba9d18c5fe9f0d757ca11b82c";
';
    return $returnStr;
  }

  /**
   * @param string $data
   * @return string
   */
  public static function createGojsChartFileNode($data) {
    $data = substr($data, 0, -2);
// look at how they did OrgChart4Editor.cls and maybe do this that way?
    $returnStr = self::gojsBeginning('Awesome Entity Tree Viewr');
    $returnStr .= '    go.Shape.defineFigureGenerator("ExpandedLine", function(shape, w, h) {
      return new go.Geometry()
            .add(new go.PathFigure(0, 0.25*h, false)
                  .add(new go.PathSegment(go.PathSegment.Line, .5 * w, 0.75*h))
                  .add(new go.PathSegment(go.PathSegment.Line, w, 0.25*h)));
    });

    // use a sideways V figure instead of PlusLine in the TreeExpanderButton
    go.Shape.defineFigureGenerator("CollapsedLine", function(shape, w, h) {
      return new go.Geometry()
            .add(new go.PathFigure(0.25*w, 0, false)
                  .add(new go.PathSegment(go.PathSegment.Line, 0.75*w, .5 * h))
                  .add(new go.PathSegment(go.PathSegment.Line, 0.25*w, h)));
    });

    function init() {
      var $ = go.GraphObject.make;  // for conciseness in defining templates

var myDiagram =
  $(go.Diagram, "myDiagramDiv",
    {
      "undoManager.isEnabled": true,
      layout: $(go.TreeLayout,
                { angle: 90, layerSpacing: 35 })
    });

      // Setup zoom to fit button
      document.getElementById("zoomToFit").addEventListener("click", function() {
        myDiagram.commandHandler.zoomToFit();
      });

      document.getElementById("centerRoot").addEventListener("click", function() {
        myDiagram.scale = 1;
        myDiagram.commandHandler.scrollToPart(myDiagram.findNodeForKey(1));
      });

      document.getElementById("vertical").addEventListener("click", function() {
        myDiagram.scale = 1;
        myDiagram.layout.alignment = go.TreeLayout.AlignmentBottomRightBus;
      });

      document.getElementById("horizontal").addEventListener("click", function() {
        myDiagram.scale = 1;
        myDiagram.layout.alignment = go.TreeLayout.AlignmentStart;
      });

      document.getElementById("centerHorizontal").addEventListener("click", function() {
        myDiagram.scale = 1;
        myDiagram.layout.alignment = go.TreeLayout.AlignmentCenterChildren;
      });

myDiagram.nodeTemplate =
  $(go.Node, "Horizontal",
    { background: "#44CCFF" },new go.Binding("background", "color"),
    $(go.TextBlock, "Default Text",
      { margin: 12, stroke: "white", font: "bold 16px sans-serif" },
      new go.Binding("text", "name")),
    $(go.TextBlock, "Default Text",
      { margin: 12, stroke: "white", font: "bold 16px sans-serif" },
      new go.Binding("text", "apiname"))
  );

// define a Link template that routes orthogonally, with no arrowhead
myDiagram.linkTemplate =
  $(go.Link,
    { routing: go.Link.Orthogonal, corner: 5 },
    $(go.Shape, // the links path shape
      { strokeWidth: 3, stroke: "#555" })
  );

      // the tree data
      var nodeDataArray = [';
    $returnStr .= $data;
    $returnStr .= '];

      myDiagram.model = new go.TreeModel(nodeDataArray);
    }

    window.addEventListener("DOMContentLoaded", init);
  </script>

  <div id="myDiagramDiv" style="border: 1px solid black; width: 100%; height: 80%"></div>
    </div>
  <p><button id="centerHorizontal">Center Horizontal</button> <button id="zoomToFit">Zoom to Fit</button> <button id="centerRoot">Center on root</button> <button id="vertical">Vertical</button> <button id="horizontal">Horizontal left</button></p>
	</body>
</html>';
    return $returnStr;

  }

  /**
   * @param string $data
   * @return string
   */
  public static function createGojsTreeChartFileNode($data)
  {
    $data = substr($data, 0, -2);
// look at how they did OrgChart4Editor.cls and maybe do this that way?
    $returnStr = self::gojsBeginning('Awesome Entity Tree Viewr');
    $returnStr .= '
    go.Shape.defineFigureGenerator("ExpandedLine", function(shape, w, h) {
      return new go.Geometry()
            .add(new go.PathFigure(0, 0.25*h, false)
                  .add(new go.PathSegment(go.PathSegment.Line, .5 * w, 0.75*h))
                  .add(new go.PathSegment(go.PathSegment.Line, w, 0.25*h)));
    });

    // use a sideways V figure instead of PlusLine in the TreeExpanderButton
    go.Shape.defineFigureGenerator("CollapsedLine", function(shape, w, h) {
      return new go.Geometry()
            .add(new go.PathFigure(0.25*w, 0, false)
                  .add(new go.PathSegment(go.PathSegment.Line, 0.75*w, .5 * h))
                  .add(new go.PathSegment(go.PathSegment.Line, 0.25*w, h)));
    });

    function init() {
      var $ = go.GraphObject.make;  // for conciseness in defining templates

      myDiagram =
        $(go.Diagram, "myDiagramDiv",
          {
            allowMove: false,
            allowCopy: false,
            allowDelete: false,
            allowHorizontalScroll: false,
            layout:
              $(go.TreeLayout,
                {
                  alignment: go.TreeLayout.AlignmentStart,
                  angle: 0,
                  compaction: go.TreeLayout.CompactionNone,
                  layerSpacing: 16,
                  layerSpacingParentOverlap: 1,
                  nodeIndentPastParent: 1.0,
                  nodeSpacing: 0,
                  setsPortSpot: false,
                  setsChildPortSpot: false
                })
          });

      myDiagram.nodeTemplate =
        $(go.Node,
          { // no Adornment: instead change panel background color by binding to Node.isSelected
            selectionAdorned: false,
            // a custom function to allow expanding/collapsing on double-click
            // this uses similar logic to a TreeExpanderButton
            doubleClick: function(e, node) {
              var cmd = myDiagram.commandHandler;
              if (node.isTreeExpanded) {
                if (!cmd.canCollapseTree(node)) return;
              } else {
                if (!cmd.canExpandTree(node)) return;
              }
              e.handled = true;
              if (node.isTreeExpanded) {
                cmd.collapseTree(node);
              } else {
                cmd.expandTree(node);
              }
            }
          },
          $("TreeExpanderButton",
            { // customize the button"s appearance
              "_treeExpandedFigure": "ExpandedLine",
              "_treeCollapsedFigure": "CollapsedLine",
              "ButtonBorder.fill": "whitesmoke",
              "ButtonBorder.stroke": null,
              "_buttonFillOver": "rgba(0,128,255,0.25)",
              "_buttonStrokeOver": null
            }),
$(go.Panel, "Horizontal",
{ position: new go.Point(18, 0) },
new go.Binding("background", "isSelected", function(s) { return (s ? "lightblue" : "white"); }).ofObject(),
            $(go.Picture,
              {
                width: 18, height: 18,
                margin: new go.Margin(0, 4, 0, 0),
                imageStretch: go.GraphObject.Uniform
              },
              // bind the picture source on two properties of the Node
              // to display open folder, closed folder, or document
              new go.Binding("source", "isTreeExpanded", imageConverter).ofObject(),
              new go.Binding("source", "isTreeLeaf", imageConverter).ofObject()),
            $(go.TextBlock,
              { font: "9pt Verdana, sans-serif", width: 200 },
              // new go.Binding("text", "name", function(s) { return s + "---"; })),
              new go.Binding("text", "name")),
            $(go.TextBlock,
              { textAlign: "left", font: "9pt Verdana, sans-serif", width: 200 },
	      new go.Binding("text", "apiname"))
          )  // end Horizontal Panel
        );  // end Node

      // without lines
      //myDiagram.linkTemplate = $(go.Link);

      // // with lines
       myDiagram.linkTemplate =
           $(go.Link,
           { selectable: false,
             routing: go.Link.Orthogonal,
             fromEndSegmentLength: 4,
             toEndSegmentLength: 4,
             fromSpot: new go.Spot(0.001, 1, 7, 0),
             toSpot: go.Spot.Left },
           $(go.Shape,
             { stroke: "gray", strokeDashArray: [1,2] }));
       
      var nodeDataArray = [
';
    $returnStr .= $data;
    $returnStr .= '];
      myDiagram.model = new go.TreeModel(nodeDataArray);
    }

    // takes a property change on either isTreeLeaf or isTreeExpanded and selects the correct image to use
    function imageConverter(prop, picture) {
      var node = picture.part;
      if (node.isTreeLeaf) {
        return "images/document.svg";
      } else {
        if (node.isTreeExpanded) {
          return "images/openFolder.svg";
        } else {
          return "images/closedFolder.svg";
        }
      }
    }
    window.addEventListener("DOMContentLoaded", init);
  </script>

  <div id="myDiagramDiv" style="border: 1px solid black; width: 100%; height: 80%"></div>


    </div>
	</body>
</html>
';
    return $returnStr;
  }

  /**
   * @param array $beenThere
   * @param Node $allNodes
   * @param string $entity
   * @param string $entityChildAry
   * @return mixed|Node
   * @throws APIException
   */
  public static function recurse($beenThere, &$allNodes, $entity, $entityChildAry='children')
  {
    // iterate over children
    $entity = strtolower($entity);
    $mst = substr($entity, -3);
    if ($mst == 'mst') {
        $entity = substr($entity, 0, -3);
    }
    $entityInfo = self::getEntDefinitions($entity);
    if ($entityInfo !== false) {
        if (array_key_exists('hasdimensions', ($entityInfo ?? []))) {
            //echo "Entname: $entity: Has Dimensions<br/>";
            // Get all standard dimension objects:
            IADimensions::setForceAllStdDimensions(true);
            $dimArray = IADimensions::GetDimensionIDs(false);
            foreach ($dimArray as $dimFieldId) {
                // doesn't get costtype since we are not in a company and no feature flag
                // $test1 = Pt_StandardUtil::getDimensionEntity($dimFieldId);
                // ID is at the end of every dimFieldId, so remove it for the object name
                $dimObjectName = strtolower(substr($dimFieldId, 0, -2));
                // if it isn't there, add it
                if (!array_key_exists($dimObjectName, $entityInfo[$entityChildAry] ?? [])) {
                    $entityInfo[$entityChildAry][$dimObjectName] = $dimObjectName;
                }
            }
        }
        // if entityChildAry is fields then build list of entities from fieldinfo
        if ($entityChildAry == 'fields') {
            foreach ($entityInfo['fieldinfo'] as $value) {
                if (array_key_exists('entity', ($value['type'] ?? []))) {
                    $entityInfo[$entityChildAry][$value['type']['entity']] = $value['type']['entity'];
                }
            }
        }
        // if we have owned objects, add them as well.
        if (array_key_exists('ownedobjects', $entityInfo)) {
          foreach ($entityInfo['ownedobjects'] as $key => $value) {
            if (array_key_exists('entity', ($value ?? []))) {
              $entityInfo[$entityChildAry][$value['entity']] = $value['entity'];
            }
          }
        }
    }
    // if we have dimensions then we need to add all dimensions to the children or nexus array
    $apiName = RegistryLoader::getInstance('0-beta2')->getObjectNameForMappedToName($entity);
    if (!isNullOrBlank($apiName)) {
      $entity .= '--' . $apiName;
    }


    if ( $allNodes[$entity] ?? false) {
      return $allNodes[$entity];
    }
    $node = new Node($entity);
    $allNodes[$entity] = $node;
    // cycle detection

      if ($entityInfo !== false) {
          if (isset($entityInfo[$entityChildAry]) && is_array([$entityChildAry])) {
              foreach ($entityInfo[$entityChildAry] as $key => $value) {
                  // make sure it is in the nexus to include in the tree.
                  if (self::getEntDefinitions($key) === false && is_array($value)) {
                      // not an entity, try using object if nexus, or table if children
                      $objKey =  ($entityChildAry == 'nexus' ) ? 'object' : 'table';
                      $key = $value[$objKey];
                  }
                  $node->addChild($key, self::recurse($beenThere, $allNodes, $key, $entityChildAry));
              }
          }
      }
    return $node;
  }

    /**
     * load ent by invoking EntityManger::includeEntity
     *
     * @param string $entName entitiy name
     *
     * @return array|bool the array of the ent
     */
    private static function getEntDefinitions($entName)
    {
        // some entities need includes from the manager
        $manager = class_lookup($entName . 'Manager');
        if ($manager) {
            import($manager);
        }

        $entArray = EntityManager::includeEntity($entName);
        if ($entArray === false) {
            return false;
        }

        return $entArray;
    }

}

class Node
{
  /** @var string $entityName */
  public $entityName;
  /** @var array $children */
  public $children = [];

  /**
   * @param string $name
   */
  public function __construct($name)
  {
    $this->entityName = $name;
  }

  /**
   * @param string $name
   * @param node $node
   */
  public function addChild(string $name, Node $node)
  {
    $this->children[$name] = $node;
  }

  /**
   * @param array $beenThere
   * @param int $maxDepth
   * @param int $depth
   */
  public function printDFS($beenThere=[], $maxDepth=2, $depth=0)
  {
    $name = $this->entityName;
    echo str_repeat("  ", $depth) . $name;
    if ( $beenThere[$name] ) {
      echo " (cycle)\n";
    } else {
      echo "\n";
      $beenThere[$name] = 1;
      if ($depth <= $maxDepth) {
        foreach ( $this->children as $child ) {
          $child->printDFS($beenThere, $maxDepth, $depth+1);
        }
      }

    }
  }

  /**
   * @param array $beenThere
   * @param string $parent
   * @param string $data
   */
  public function chartDFS($beenThere=[], $parent="", &$data="")
  {
    $name = $this->entityName;

    if ( $beenThere[$name] ) {
      $data .= '["' .  $name . ' (cycle)","' . $parent . '",""],' . "\n";
    } else {
      $data .= '["' .  $name . '","' . $parent . '",""],' . "\n";
      $beenThere[$name] = 1;
      foreach ( $this->children as $child ) {
        $child->chartDFS($beenThere, $name, $data);
      }
    }
  }

  /**
   * @param array $beenThere
   * @param string $parent
   * @param string $data
   * @param int $maxDepth
   * @param int $line
   * @param int $parentline
   * @param int $depth
   */
  public function treeDFS($beenThere=[], $parent="", &$data="", $maxDepth=2, &$line=0, $parentline=0, $depth=0)
  {
    $line++;
    //$name = $this->entityName;
    list($name, $apiname) = explode('--', $this->entityName);
//    $nameg = ($name == 'parent') ? 'parent1' : $name;
    $expanded =  ($parent == '') ? ' expanded' : '';
    $parenta = ($parent == 'parent') ? 'parent1' : $parent;
    $parentc = ($parenta == '') ? '' : ' treegrid-parent-' . $parentline;
    if ( $beenThere[$name] ) {
      $data .= '<tr class="treegrid-' . $line . $parentc . $expanded . '"><td>' . $name . ' (cycle)</td><td>' . $apiname . '</td></tr>' . "\n";
    } else {
      $data .= '<tr class="treegrid-' . $line . $parentc . $expanded . '"><td>' . $name . '</td><td>' . $apiname . '</td></tr>' . "\n";
      $beenThere[$name] = 1;
      $parentline = $line;
      if ($depth <= $maxDepth) {
        foreach ( $this->children as $child ) {
          $child->treeDFS($beenThere, $name, $data, $maxDepth, $line, $parentline, $depth+1);
        }
      }

    }
  }

  /**
   * @param array $beenThere
   * @param string $parent
   * @param string $data
   * @param int $maxDepth
   * @param int $line
   * @param int $parentline
   * @param int $depth
   */
  public function gojsTreeDFS($beenThere=[], $parent="", &$data="", $maxDepth=2, &$line=0, $parentline=0, $depth=0)
  {
    $line++;
    list($name, $apiname) = explode('--', $this->entityName);
    $parenta = ($parent == 'parent') ? 'parent1' : $parent;
    $parentc = ($parenta == '') ? '' : '",  parent: "' . $parentline;
    $color = (isNullOrBlank($apiname)) ? ', color: "red"' : '';
    if ( $beenThere[$name] ) {
      $data .= '{key: "' . $line . $parentc . '", name: "' . $name . '(cycle)'. '", apiname: "' . $apiname . '"' . $color . ' },' . "\n";
    } else {
      $data .= '{key: "' . $line . $parentc . '", name: "' . $name . '", apiname: "' . $apiname . '"' . $color . '},' . "\n";
      $beenThere[$name] = 1;
      $parentline = $line;
      if ($depth <= $maxDepth) {
        foreach ( $this->children as $child ) {
          $child->gojsTreeDFS($beenThere, $name, $data, $maxDepth, $line, $parentline, $depth+1);
        }
      }

    }
  }
}
