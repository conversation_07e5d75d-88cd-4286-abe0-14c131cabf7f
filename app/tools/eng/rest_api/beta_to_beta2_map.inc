<?php

 $lookupMap = [
    'document-sequence' =>'company-config/document-sequence',
    'document-sequence-number' =>'company-config/document-sequence',
    'inventory-document-history' =>'inventory-control/document-history',
    'order-entry-document-history' =>'order-entry/document-history',
    'order-entry-document-line-detail' =>'order-entry/document-line-detail',
    'inventory-document-line-detail' =>'inventory-control/document-line-detail',
    'project-group' =>'projects/project-group',
    'asset-depreciation-rule' =>'fixed-assets/asset-depreciation-rule',
    'asset' =>'fixed-assets/asset',
    'classification-depreciation-rule' =>'fixed-assets/classification-depreciation-rule',
    'asset-classification' =>'fixed-assets/asset-classification',
    'depreciation-schedule-entry' =>'fixed-assets/depreciation-schedule-entry',
    'depreciation-schedule' =>'fixed-assets/depreciation-schedule',
    'depreciation-method' =>'fixed-assets/depreciation-method',
    'fa-setup-posting-rule' =>'fixed-assets/setup-posting-rule',
    'fa-setup' =>'fixed-assets/setup',
    'wip-period' =>'construction-forecasting/wip-period',
    'wip-project' =>'construction-forecasting/wip-project',
    'wip-setup' =>'construction-forecasting/wip-setup',
    'wip-setup-account' =>'construction-forecasting/wip-setup-account',
    'user-view' =>'core/user-view',
    'user' =>'company-config/user',
    'user-group' =>'company-config/user-group',
    'role' =>'company-config/role',
    'role-permission-assignment' =>'company-config/role-permission-assignment',
    'checking-account' =>'cash-management/checking-account',
    'bank-txn-rule-set' =>'cash-management/bank-txn-rule-set',
    'bank-txn-rule-map' =>'cash-management/bank-txn-rule-map',
    'bank-txn-rule' =>'cash-management/bank-txn-rule',
    'journal-entry-template' =>'cash-management/journal-entry-template',
    'journal-entry-line-template' =>'cash-management/journal-entry-line-template',
    'credit-card-txn-template' =>'cash-management/credit-card-txn-template',
    'credit-card-txn-line-template' =>'cash-management/credit-card-txn-line-template',
    'bank-txn-rule-match' =>'cash-management/bank-txn-rule-match',
    'bank-txn-rule-group' =>'cash-management/bank-txn-rule-group',
    'bank-txn-rule-filter' =>'cash-management/bank-txn-rule-filter',
    'ar-tax-schedule' =>'tax/ar-tax-schedule',
    'order-entry-tax-schedule' =>'tax/order-entry-tax-schedule',
    'purchasing-tax-schedule' =>'tax/purchasing-tax-schedule',
    'purchasing-subtotal-template' =>'purchasing/subtotal-template',
    'order-entry-subtotal-template' =>'order-entry/subtotal-template',
    'purchasing-txn-definition' =>'purchasing/txn-definition',
    'order-entry-txn-definition' =>'order-entry/txn-definition',
    'order-entry-txn-definition-additionalgl-account-detail' =>'order-entry/txn-definition-additional-gl-detail',
    'order-entry-txn-definition-ar-directgl-account-detail' =>'order-entry/txn-definition-ar-direct-gl-detail',
    'order-entry-txn-definition-cogsgl-account-detail' =>'order-entry/txn-definition-cogs-gl-detail',
    'order-entry-txn-definition-entity-setting-detail' =>'order-entry/txn-definition-entity-setting-detail',
    'order-entry-txn-definition-inv-total-detail' =>'order-entry/txn-definition-inventory-total-detail',
    'order-entry-txn-definition-source-document-detail' =>'order-entry/txn-definition-source-document-detail',
    'order-entry-txn-definition-subtotal-detail' =>'order-entry/txn-definition-subtotal-detail',
    'ar-tax-schedule-map' =>'tax/ar-tax-schedule-map',
    'ap-tax-schedule-map' =>'tax/ap-tax-schedule-map',
    'order-entry-tax-schedule-map' =>'tax/order-entry-tax-schedule-map',
    'purchasing-tax-schedule-map' =>'tax/purchasing-tax-schedule-map',
    'ar-tax-schedule-detail' =>'tax/ar-tax-schedule-detail',
    'order-entry-tax-schedule-detail' =>'tax/order-entry-tax-schedule-detail',
    'purchasing-tax-schedule-detail' =>'tax/purchasing-tax-schedule-detail',
    'financial-institution' =>'cash-management/financial-institution',
    'vendor' =>'accounts-payable/vendor',
    'vendor-account-group' =>'accounts-payable/vendor-account-group',
    'vendor-account-number' =>'accounts-payable/vendor-account-number',
    'vendor-contact' =>'accounts-payable/vendor-contact',
    'vendor-email-template' =>'accounts-payable/vendor-email-template',
    'vendor-payment-provider' =>'accounts-payable/vendor-payment-provider',
    'vendor-bank-file-setup' =>'accounts-payable/vendor-bank-file-setup',
    'payment-provider' =>'cash-management/payment-provider',
    'provider-payment-method' =>'cash-management/provider-payment-method',
    'payment-provider-bank-account' =>'cash-management/payment-provider-bank-account',
    'vendor-type' =>'accounts-payable/vendor-type',
    'gl-account' =>'general-ledger/account',
    'location' =>'company-config/location',
    'email-template' =>'company-config/email-template',
    'contact-tax-group' =>'tax/contact-tax-group',
    'avatax-activity-record' =>'tax/avatax-activity-record',
    'tax-return' =>'tax/tax-return',
    'contact' =>'company-config/contact',
    'purchasing-tax-detail' =>'tax/purchasing-tax-detail',
    'order-entry-tax-detail' =>'tax/order-entry-tax-detail',
    'ar-account-label' =>'accounts-receivable/account-label',
    'tax-authority' =>'tax/tax-authority',
    'account-label-tax-group' =>'tax/account-label-tax-group',
    'sandbox' =>'company-config/sandbox',
    'ap-account-label' =>'accounts-payable/account-label',
    'ap-term' =>'accounts-payable/term',
    'attachment' =>'company-config/attachment',
    'checklist-status' =>'company-config/checklist-status',
    'file' =>'company-config/file',
    'purchasing-price-list' =>'purchasing/price-list',
    'purchasing-price-schedule' =>'purchasing/price-schedule',
    'tax-solution' =>'tax/tax-solution',
    'employee' =>'company-config/employee',
    'department' =>'company-config/department',
    'class' =>'company-config/class',
    'employee-type' =>'company-config/employee-type',
    'employee-expense' =>'expenses/employee-expense',
    'employee-expense-line' =>'expenses/employee-expense-line',
    'employee-expense-summary' =>'expenses/employee-expense-summary',
    'employee-expense-type' =>'expenses/employee-expense-type',
    'unit-rate' =>'expenses/unit-rate',
    'employee-expense-payment-type' =>'expenses/employee-expense-payment-type',
    'earning-type' =>'company-config/earning-type',
    'folder' =>'company-config/folder',
    'customer' =>'accounts-receivable/customer',
    'holiday' =>'company-config/holiday',
    'holiday-schedule' =>'company-config/holiday-schedule',
    'timesheet-rule' =>'time/timesheet-rule',
    'customer-email-template' =>'accounts-receivable/customer-email-template',
    'customer-contact' =>'accounts-receivable/customer-contact',
    'customer-item-cross-reference' =>'accounts-receivable/customer-item-cross-reference',
    'customer-account-group' =>'accounts-receivable/customer-account-group',
    'order-entry-price-list' =>'order-entry/price-list',
    'project' =>'projects/project',
    'project-status' =>'projects/project-status',
    'project-type' =>'projects/project-type',
    'project-contract' =>'construction/project-contract',
    'project-contract-type' =>'construction/project-contract-type',
    'project-contract-line-entry' =>'construction/project-contract-line-entry',
    'project-contract-line' =>'construction/project-contract-line',
    'project-estimate' =>'construction/project-estimate',
    'project-estimate-type' =>'construction/project-estimate-type',
    'project-estimate-line' =>'construction/project-estimate-line',
    'project-change-order' =>'construction/project-change-order',
    'standard-task' =>'construction/standard-task',
    'employee-position' =>'construction/employee-position',
    'change-request-type' =>'construction/change-request-type',
    'change-request-status' =>'construction/change-request-status',
    'change-request-line' =>'construction/change-request-line',
    'change-request' =>'construction/change-request',
    'rate-table-accounts-payable-line' =>'construction/rate-table-accounts-payable-line',
    'rate-table-credit-card-line' =>'construction/rate-table-credit-card-line',
    'rate-table-employee-expense-line' =>'construction/rate-table-employee-expense-line',
    'rate-table-journal-line' =>'construction/rate-table-journal-line',
    'rate-table-purchasing-line' =>'construction/rate-table-purchasing-line',
    'rate-table-timesheet-line' =>'construction/rate-table-timesheet-line',
    'rate-table' =>'construction/rate-table',
    'ar-term' =>'accounts-receivable/term',
    'contract' =>'contracts/contract',
    'contract-expense' =>'contracts/expense',
    'billing-price-list' =>'contracts/billing-price-list',
    'billing-price-list-entry' =>'contracts/billing-price-list-entry',
    'billing-price-list-entry-line' =>'contracts/billing-price-list-entry-line',
    'billing-price-list-entry-line-tier' =>'contracts/billing-price-list-entry-line-tier',
    'billing-template' =>'contracts/billing-template',
    'billing-template-line' =>'contracts/billing-template-line',
    'contract-type' =>'contracts/contract-type',
    'contract-usage' =>'contracts/contract-usage',
    'mea-price-list' =>'contracts/mea-price-list',
    'contract-revenue-template' =>'contracts/revenue-template',
    'txn-currency' =>'company-config/txn-currency',
    'customer-type' =>'accounts-receivable/customer-type',
    'shipping-method' =>'accounts-receivable/shipping-method',
    'journal-entry-line' =>'general-ledger/journal-entry-line',
    'journal-entry' =>'general-ledger/journal-entry',
    'task' =>'projects/task',
    'cost-type' =>'construction/cost-type',
    'item' =>'inventory-control/item',
    'warehouse' =>'inventory-control/warehouse',
    'accumulation-type' =>'construction/accumulation-type',
    'item-gl-group' =>'inventory-control/item-gl-group',
    'item-tax-group' =>'tax/item-tax-group',
    'unit-of-measure-group' =>'inventory-control/unit-of-measure-group',
    'unit-of-measure' =>'inventory-control/unit-of-measure',
    'item-warehouse-inventory' =>'inventory-control/item-warehouse-inventory',
    'inventory-total' =>'inventory-control/total',
    'inventory-txn-definition' =>'inventory-control/txn-definition',
    'inventory-txn-definition-entity-detail' =>'inventory-control/txn-definition-entity-detail',
    'inventory-txn-definition-cogs-gl-detail' =>'inventory-control/txn-definition-cogs-gl-detail',
    'inventory-txn-definition-source' =>'inventory-control/txn-definition-source',
    'inventory-txn-definition-subtotal-detail' =>'inventory-control/txn-definition-subtotal-detail',
    'inventory-txn-definition-total-detail' =>'inventory-control/txn-definition-total-detail',
    'product-line' =>'inventory-control/product-line',
    'territory' =>'accounts-receivable/territory',
    'standard-cost-type' =>'construction/standard-cost-type',
    'statistical-gl-account' =>'general-ledger/statistical-account',
    'gl-journal' =>'general-ledger/journal',
    'contract-line' =>'contracts/contract-line',
    'contract-billing-schedule' =>'contracts/billing-schedule',
    'contract-billing-schedule-line' =>'contracts/billing-schedule-line',
    'contract-expense-template' =>'contracts/expense-template',
    'contract-expense-template-line' =>'contracts/expense-template-line',
    'contract-revenue-schedule' =>'contracts/revenue-schedule',
    'contract-revenue-schedule-line' =>'contracts/revenue-schedule-line',
    'contract-expense-schedule' =>'contracts/expense-schedule',
    'contract-expense-schedule-line' =>'contracts/expense-schedule-line',
    'statistical-journal' =>'general-ledger/statistical-journal',
    'gaap-adjustment-journal' =>'general-ledger/gaap-adjustment-journal',
    'tax-adjustment-journal' =>'general-ledger/tax-adjustment-journal',
    'user-defined-book' =>'general-ledger/user-defined-book',
    'user-defined-journal' =>'general-ledger/user-defined-journal',
    'budget' =>'general-ledger/budget',
    'reporting-period' =>'general-ledger/reporting-period',
    'budget-detail' =>'general-ledger/budget-detail',
    'bill-line' =>'accounts-payable/bill-line',
    'bill-summary' =>'accounts-payable/bill-summary',
    'bill' =>'accounts-payable/bill',
    'billback-template' =>'accounts-receivable/billback-template',
    'billback-template-line' =>'accounts-receivable/billback-template-line',
    'txn-definition' =>'core/txn-definition',
    'inventory-cycle' =>'inventory-control/cycle',
    'entity' =>'company-config/entity',
    'bin' =>'inventory-control/bin',
    'revenue-recognition-template' =>'accounts-receivable/revenue-recognition-template',
    'revenue-recognition-schedule' =>'order-entry/revenue-recognition-schedule',
    'revenue-recognition-schedule-line' =>'inventory-control/revenue-recognition-schedule-line',
    'revenue-recognition-category' =>'inventory-control/revenue-recognition-category',
    'txn-allocation-template' =>'general-ledger/txn-allocation-template',
    'txn-allocation-template-line' =>'general-ledger/txn-allocation-template-line',
    'gl-account-name-entity-map' =>'general-ledger/account-name-entity-map',
    'aisle' =>'inventory-control/aisle',
    'bin-face' =>'inventory-control/bin-face',
    'bin-size' =>'inventory-control/bin-size',
    'row' =>'inventory-control/row',
    'zone' =>'inventory-control/zone',
    'lot-category' =>'inventory-control/lot-category',
    'inventory-price-list' =>'inventory-control/price-list',
    'item-vendor' =>'inventory-control/item-vendor',
    'kit-component' =>'inventory-control/kit-component',
    'item-landed-cost' =>'inventory-control/item-landed-cost',
    'item-cross-reference' =>'inventory-control/item-cross-reference',
    'item-warehouse-vendor' =>'inventory-control/item-warehouse-vendor',
    'item-warehouse-standard-cost' =>'inventory-control/item-warehouse-standard-cost',
    'ar-adjustment' =>'accounts-receivable/adjustment',
    'ar-adjustment-line' =>'accounts-receivable/adjustment-line',
    'ap-summary' =>'accounts-payable/summary',
    'ap-payment' =>'accounts-payable/payment',
    'ap-payment-detail' =>'accounts-payable/payment-detail',
    'ap-payment-line' =>'accounts-payable/payment-line',
    'ap-adjustment' =>'accounts-payable/adjustment',
    'ap-adjustment-line' =>'accounts-payable/adjustment-line',
    'ap-adjustment-summary' =>'accounts-payable/adjustment-summary',
    'ar-invoice' =>'accounts-receivable/invoice',
    'ar-invoice-line' =>'accounts-receivable/invoice-line',
    'ar-summary' =>'accounts-receivable/summary',
    'ar-payment' =>'accounts-receivable/payment',
    'ar-payment-detail' =>'accounts-receivable/payment-detail',
    'ar-payment-line' =>'accounts-receivable/payment-line',
    'subledger-record' =>'accounts-payable/subledger-record',
    'subledger-record-line' =>'accounts-payable/subledger-record-line',
    'ar-recurring-invoice' =>'accounts-receivable/recurring-invoice',
    'ar-recurring-invoice-line' =>'accounts-receivable/recurring-invoice-line',
    'credit-card-account' =>'cash-management/credit-card-account',
    'credit-card-txn' =>'cash-management/credit-card-txn',
    'credit-card-txn-line' =>'cash-management/credit-card-txn-line',
    'other-receipt' =>'cash-management/other-receipt',
    'other-receipt-line' =>'cash-management/other-receipt-line',
    'bank-account' =>'cash-management/bank-account',
    'project-resource' =>'projects/project-resource',
    'savings-account' =>'cash-management/savings-account',
    'order-entry-document-entry-detail' =>'order-entry/document-entry-detail',
    'order-entry-document-subtotal' =>'order-entry/document-subtotal',
    'order-entry-renewal-template' =>'order-entry/renewal-template',
    'timesheet' =>'time/timesheet',
    'timesheet-line' =>'time/timesheet-line',
    'time-type' =>'time/time-type',
    'labor-class' =>'construction/labor-class',
    'labor-shift' =>'construction/labor-shift',
    'labor-union' =>'construction/labor-union',
    'gl-account-group' =>'general-ledger/account-group',
    'gl-report-audience' =>'general-ledger/report-audience',
    'gl-report-type' =>'general-ledger/report-type',
    'assignment-status' =>'company-config/assignment-status',
    'assignment' =>'company-config/assignment',
    'assignment-constraint' =>'company-config/assignment-constraint',
    'assignment-category' =>'company-config/assignment-category',
    'checklist-category' =>'company-config/checklist-category',
    'ar-advance' =>'accounts-receivable/advance',
    'ar-advance-line' =>'accounts-receivable/advance-line',
    'delivery-history' =>'accounts-receivable/delivery-history',
    'deposit' =>'cash-management/deposit',
    'deposit-line' =>'cash-management/deposit-line',
    'instance' =>'console/instance',
    'template' =>'console/template',
    'company-message' =>'company-config/company-message',
    'custom-field' =>'platform/custom-field',
    'permission' =>'company-config/permission',
    'checklist' =>'company-config/checklist',
    'undeposited-fund' =>'cash-management/undeposited-fund',
    'deposited-fund' =>'cash-management/deposited-fund',
    'deposit-detail' =>'cash-management/deposit-detail',
    'bank-fee' =>'cash-management/bank-fee',
    'bank-fee-line' =>'cash-management/bank-fee-line'
];
