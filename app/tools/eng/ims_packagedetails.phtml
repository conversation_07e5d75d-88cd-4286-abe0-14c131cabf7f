<?
//=============================================================================
// FILE:			ims_packagedetails.phtml
// AUTHOR:			<PERSON>
// DESCRIPTION:		Intacct Messaging Service Queue Manager
//
//	(C)2000-2001, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================

require_once 'ims_include.inc';
require_once 'ims_queue_manager.inc';

$_done = Request::$r->_done;
$packageid = Request::$r->packageid;
$cny = Request::$r->cny;
$context = Request::$r->context;
$display = &Request::$r->display;
$_display = Request::$r->_display;
$_ret = Request::$r->_ret;
$action = &Request::$r->action;
$_retry = Request::$r->_retry;
$_showcontent = Request::$r->_showcontent;
$timestamp = &Request::$r->timestamp;
$_deliveryhistory = Request::$r->_deliveryhistory;
$title            = Request::$r->title;
// Metadata to define the display
$display = array(
    'display' => array(
        'title' => 'IMS Package Details',
        'buttons' => array('ret'),
        'jsbuttons' => array(
            array('deliveryhistory', 'Delivery History', "return true;"),
            array('retry', 'Retry Delivery', "return confirm('Are you sure you want to retry delivery?');"),
        ),
    ),
    'deliveryattempt' => array(
        'title' => 'IMS Package Delivery Attempt Log',
        'buttons' => array('ret'),
        'jsbuttons' => array(
            array('deliveryhistory', 'Delivery History', "return true;"),
            array('display', 'Package Detail', "return true;"),
            array('retry', 'Retry Delivery', "return confirm('Are you sure you want to retry delivery?');"),
        ),
    ),
    'retry' => array(
        'title' => 'IMS Package Retry Results',
        'buttons' => array('ret'),
        'jsbuttons' => array(
            array('display', 'Package Detail', "return true;"),
            array('deliveryhistory', 'Delivery History', "return true;"),
            array('retry', 'Retry Delivery', "return confirm('Are you sure you want to retry delivery?');"),
        ),
    )

);

// TRANSLATE THE SUBMIT PARAMS TO ACTIONS - If posted from ims_queue_manager, action will already be set
if ($_deliveryhistory) {
    $action = 'deliveryhistory';
} elseif ($_display) {
    $action = 'display';
} elseif ($_retry) {
    $action = 'retry';
} elseif ($_showcontent) {
    $action = 'showcontent';
}

// REDIRECT TO HOME PAGE IF ACTION IS EMPTY
if ($_ret) {
    Ret();
}
if (!$action) {
    Go('ims_queue_manager.phtml');
}

// #ims-schemalet
if (!$context) {
    echo "Please specify the company/db context";
    exit();
}
SetDBSchema($context);

// CREATE A DB SESSION
ims_queue::createAppSessionNoCredentials();

if ($action == 'purge' ) {
    $result = PurgePackage($packageid, $cny);
    if ($result) {
        //Just keep us on the same lister page please
        Ret();
    }
    else {
        DisplayResult("Unable to purge package $packageid.", 'failure');
        exit();
    }
}

if ($action == 'deliveryhistory') {
    // SELECT DELIVERY HISTORY FROM IMS_DELIVERY
    $sql = "select TIMESTAMP from IMSDELIVERY where IMSPACKAGEKEY = :1 and CNY# = :2";
    $sqlStmt = [$sql, $packageid, $cny];
    $res = IMSQueryResult($sqlStmt);
    if (Util::php7Count($res) < 2) {
        $action = 'deliveryattempt';
        $timestamp = $res[0]['TIMESTAMP'];
    }
}

if ($action == 'showcontent') {
    $label = $_showcontent;
    ShowContent($label);
    exit;
}

// EXTRACT THE PARAMS FROM THE DISPLAY METADATA
if (is_array($display[$action])) {
    extract($display[$action]);
}

// RETURN IF CANCELLED


// PRINT IMS QUEUE MANAGERHEADER
?>
    <!--	<h2>IMS Queue Management Utility <a href="ims_queue_manager.phtml?action=display">
        <image src='../resources/images/ia-app/buttons/refresh.gif' border=0><font face=arial size=2>Refresh/Home</font></a></h2>-->
<?

if ($action == 'deliveryhistory') {
    ?>
    <form name='imspackagedetail'>
        <input type="hidden" name='packageid' value="<? echo $packageid; ?>">
        <input type="hidden" name='cny' value="<? echo $cny; ?>">
        <input type="hidden" name='context' value="<? echo $context; ?>">
        <input type="hidden" name='.done' value="<? echo util_encode(insertDoneUnEnc($_done)); ?>">
        <? DisplayDeliveryHistoryList($packageid, $cny); ?>
    </form>
    <?
} else {
    ?>
    <form name='imspackagedetail' action="<? echo GoUrl("ims_packagedetails.phtml"); ?>" method=post
          ENCTYPE="multipart/form-data">
        <input type="hidden" name='packageid' value="<? echo $packageid; ?>">
        <input type="hidden" name='cny' value="<? echo $cny; ?>">
        <input type="hidden" name='context' value="<? echo $context; ?>">
        <input type="hidden" name='.done' value="<? echo util_encode(insertDoneUnEnc($_done)); ?>">
        <? htmlHeaderBar($title . " " . $packageid, '', $buttons, $jsbuttons); ?>
        <? switch ($action) {
        case 'display':
            // GET THE PACKAGE
            $package = ims_package::RecreateEntirePackage($packageid, $cny);
            if (!$package) {
                DisplayMessage("The Package Details were not logged.");
            } else {
                DisplayPackage($package);
            }
            break;

        case 'deliveryattempt':
            //DisplayDeliveryHistory($packageid, $cny, $timestamp);
            DisplayDeliveryAttempt($packageid, $cny, $timestamp);
            break;

        case 'retry':
        // RECONSTRUCT THE PACKAGE FROM THE ID
        $ims_package = ims_package::constructPackageFromBody($packageid, $cny);

        if (!$ims_package) {
            DisplayMessage("The Package Details were not logged, you cannot retry the delivery.");
        } else {

        // INSTANTIATE A LOCAL QUEUE FOR DELIVERY
        $ims_adapters = Globals::$g->ims_adapters;
        $retryqueue = new ims_queue(IMS_QUEUE_NORMAL);
        $retryqueue->InitFromTemplate($ims_adapters[$ims_package->GetRecipient()]['queue']);
        $retryqueue->RetryDelivery($ims_package, $qresp_pkg);
        $retryqueue->Destroy();

        // EXTRACT THE RESPONSE OBJECT AND DELIVERY LOG
        /** @var  ims_package $qresp_pkg */
        /** @var  ims_response $qresp */
        $qresp = $qresp_pkg->PopMessageVariable();
        $result = $qresp->GetResult();
        ?><b>Results</b>
        <p><?
            imsDisplayRecursiveInfo($result, "Results");
            DisplayAuditLog($qresp_pkg->GetAuditTrail());
            DisplayDebug($qresp_pkg->GetDebug());
            }
            }
            ?>
        <p>
            <? htmlButtonFooter('', $buttons, $jsbuttons); ?>
    </form>
    <?
}

