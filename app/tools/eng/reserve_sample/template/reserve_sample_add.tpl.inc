<?php
    //=============================================================================
    //
    //	FILE:        reserve_sample_add.tpl.inc
    //	AUTHOR:      <PERSON><PERSON><PERSON> <<EMAIL>>
    //	DESCRIPTION: add new reservation template
    //
    //	(C)2021, Intacct Corporation, All Rights Reserved
    //
    //	Intacct Corporation Proprietary Information.
    //	This document contains trade secret data that belongs to Intacct
    //	corporation and is protected by the copyright laws. Information herein
    //	may not be used, copied or disclosed in whole or part without prior
    //	written consent from Intacct Corporation.
    //
    //=============================================================================

    // just to keep inspections happy
    $t_params = $t_params ?? array();
    $t_alerts = $t_alerts ?? array();
    $t_page_name = $t_page_name ?? "";
    $t_header_buttons = $t_header_buttons ?? array();
    $t_pools = $t_pools ?? array();
    // end inspections
?>
<div class="sec-header">
    <div class="sec-header-title"><?=$t_page_name;?></div>
    <div class="sec-header-buttons">
        <? foreach ($t_header_buttons as $button) { ?>
        <? if ($button["type"] === "submit") { ?>
        <input name="<?=$button["name"];?>" type="submit" form="<?=$button["form"];?>" value="<?=$button["label"];?>" class="sec-header-button"/>
        <? } else if ($button["type"] === "link") { ?>
        <a href="<?=$button["url"];?>" class="button-link" onclick="showLoader();"><?=$button["label"];?></a>
        <? } ?>
        <? } ?>
    </div>
</div>
<? if (count($t_alerts) > 0) { ?>
<div class="alerts">
    <? foreach ($t_alerts as $alert) { ?>
    <div class="alert alert-<?=$alert["type"];?>">
        <? foreach ($alert["message"] as $message) { ?>
        <span class="alert-message"><?=$message;?></span>
        <? } ?>
    </div>
    <? } ?>
</div>
<? } ?>
<div class="page-row">
    <div class="page-col2">
        <form id="reservation_form" method="post" class="box">
            <input name="company_type" id="company_type" type="hidden" value="<?=$t_pools[$t_params["selected_template"]]["type"];?>"/>
            <div class="form-row">
                <label for="reservation_id" class="clickable"> Reservation ID (must be unique) <span class="required">*</span></label>
                <input id="reservation_id" name="reservation_id" type="text" class="form-input" value="<?=$t_params["reservation_id"] ?? "";?>"/>
            </div>

            <div class="form-row">
                <label for="template_name" class="clickable">Template name <span class="required">*</span></label>
                <div class="select-group" id="select_template">
                    <button id="template_name" type="button" class="select-btn" onclick="showDropdown(event,'template_selector');">
                        <span id="template_selector_label" class="select-label"><?=$t_params["selected_template"] . " - " . strtolower($t_pools[$t_params["selected_template"]]["type"]);?></span>
                        <span class="select-arrow-box"><span class="select-arrow"></span></span>
                    </button>
                    <div id="template_selector" class="select-dropdown">
                        <? $index = 1;?>
                        <? foreach ($t_params["templates"] as $item) { ?>
                            <input id="template<?=$index;?>" name="template" type="radio" value="<?=$item;?>" <?= $t_params["selected_template"] === $item ? "checked" : "" ?> class="select-option" onclick="setSelector('template_selector_label','<?=$item . " - " . strtolower($t_pools[$item]["type"]);?>');"/>
                            <label for="template<?=$index;?>" title="<?=$item . " - " . strtolower($t_pools[$item]["type"]);?>" class="select-item" tabindex="-1"><?=$item . " - " . strtolower($t_pools[$item]["type"]);?></label>
                            <? $index++; ?>
                        <? } ?>
                    </div>
                </div>
            </div>

            <div id="company_group" class="form-row" style="display: <?=$t_pools[$t_params["selected_template"]]["type"] === "TRAINING" ? "block" : "none";?>">
                <label for="company_id" class="clickable">Company ID <span class="required">*</span></label>
                <input id="company_id" name="company_id" type="text" class="form-input" value="<?=$t_params["company_id"];?>"/>
            </div>

            <div class="form-row">
                <label for="user_firstname" class="clickable">
                    First name
                    <span id="required_first_name" class="required" style="display: <?=$t_pools[$t_params["selected_template"]]["type"] === "TRAINING" ? "none" : "inline";?>">*</span></label>
                <input id="user_firstname" name="user_firstname" type="text" class="form-input" value="<?=$t_params["user_firstname"] ?? "";?>"/>
            </div>
            <div class="form-row">
                <label for="user_lastname" class="clickable">
                    Last name
                    <span id="required_last_name" class="required" style="display:<?=$t_pools[$t_params["selected_template"]]["type"] === "TRAINING" ? "none" : "inline";?>">*</span>
                </label>
                <input id="user_lastname" name="user_lastname" type="text" class="form-input" value="<?=$t_params["user_lastname"] ?? "";?>"/>
            </div>
            <div class="form-row">
                <label for="email" class="clickable">
                    Your email
                    <span id="required_email" class="required" style="display:<?=$t_pools[$t_params["selected_template"]]["type"] === "TRAINING" ? "none" : "inline";?>">*</span>
                </label>
                <input id="email" name="email" type="text" class="form-input" value="<?=$t_params["email"] ?? "";?>"/>
            </div>

            <div id="title_group" class="form-row" style="display:<?=($t_params["custom_field_info"]["TITLE"] !== "" and $t_pools[$t_params["selected_template"]]["custom_field_title"]) ? "block" : "none";?>">
                <label for="title" class="clickable">Your title <span class="required">*</span></label>
                <div class="select-group">
                    <button id="title" type="button" class="select-btn" onclick="showDropdown(event,'title_selector');">
                        <span id="title_selector_label" class="select-label"><?=$t_params["user_title"];?></span>
                        <span class="select-arrow-box"><span class="select-arrow"></span></span>
                    </button>
                    <div id="title_selector" class="select-dropdown">
                        <? if ($t_params["custom_field_info"]["TITLE"] !== "") { ?>
                        <? $index = 1;?>
                        <? foreach ($t_params["custom_field_info"]["TITLE"]["values"] as $item) { ?>
                        <input id="user_title<?=$index;?>" name="user_title" type="radio" value="<?=$item;?>" <?=$t_params["user_title"] === $item ? "checked" : ""?> class="select-option" onclick="setSelector('title_selector_label','<?=$item;?>');"/>
                        <label for="user_title<?=$index;?>" class="select-item"><?=$item;?></label>
                        <? $index++; ?>
                        <? } ?>
                        <? } ?>
                    </div>
                </div>
            </div>

            <div id="phone_group" class="form-row" style="display:<?=($t_params["custom_field_info"]["PHONE"] and $t_pools[$t_params["selected_template"]]["custom_field_phone"]) ? "block" : "none" ;?>">
                <label for="user_phone" class="clickable">Your phone <span class="required">*</span></label>
                <input id="user_phone" name="user_phone" type="text" class="form-input" value="<?=$t_params["user_phone"] ?? "";?>"/>
            </div>

            <div class="form-row">
                <label for="duration" class="clickable">Sample duration (in days)</label>
                <input id="duration" name="duration" type="text" class="form-input" value="<?=$t_params["duration"] ?? "";?>"/>
            </div>
            <div class="form-row">
                <label for="reservation_duration" class="clickable">Reservation duration (in days)</label>
                <input id="reservation_duration" name="reservation_duration" type="text" class="form-input" value="<?=$t_params["reservation_duration"] ?? "";?>"/>
            </div>
            <div class="form-row">
                <label for="partner_name" class="clickable">Partner name</label>
                <input id="partner_name" name="partner_name" type="text" class="form-input" value="<?=$t_params["partner_name"] ?? "";?>"/>
            </div>
            <div class="form-row">
                <label for="partner_email" class="clickable">Partner email</label>
                <input id="partner_email" name="partner_email" type="text" class="form-input" value="<?=$t_params["partner_email"] ?? "";?>"/>
            </div>
            <div class="form-row">
                <? foreach ($t_header_buttons as $button) { ?>
                <? if ($button["type"] === "submit") { ?>
                <input name="<?=$button["name"];?>" type="submit" form="<?=$button["form"];?>" value="<?=$button["label"];?>"/>
                <? } else if ($button["type"] === "link") { ?>
                <a href="<?=$button["url"];?>" class="button-link" onclick="showLoader();"><?=$button["label"];?></a>
                <? } ?>
                <? } ?>
            </div>
        </form>
    </div>
</div>
<script>
    var pools = <?=json_encode($t_pools);?>;
</script>
