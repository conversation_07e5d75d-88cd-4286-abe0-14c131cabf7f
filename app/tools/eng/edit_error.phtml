<?php
    //=============================================================================
    //
    //	FILE:			edit_error.phtml
    //	AUTHOR:
    //	DESCRIPTION:
    //
    //	(C)2000, Intacct Corporation, All Rights Reserved
    //
    //	Intacct Corporation Proprietary Information.
    //	This document contains trade secret data that belongs to Intacct
    //	corporation and is protected by the copyright laws. Information herein
    //	may not be used, copied or disclosed in whole or part without prior
    //	written consent from Intacct Corporation.
    //
    //=============================================================================

    $kMaxRecords = 1000000000;
    $_r           = Request::$r->_r;
    $_do          = Request::$r->_do;
    $query        = &Request::$r->query;
    $_ret         = Request::$r->_ret;
    $hlpfile      = &Request::$r->hlpfile;
    $_description = Request::$r->_description;
    $_save        = Request::$r->_save;
    $_severity    = Request::$r->_severity;
    $_source      = Request::$r->_source;
    $_errno       = Request::$r->_errno;
    $_correction  = Request::$r->_correction;
    $_class       = Request::$r->_class;
    $schemaMap = QueryResult("select distinct databaseid, schemaid from schemamap", 0, '', GetGlobalConnection());

    foreach ($schemaMap as $schema) {
        SetDBSchemaVars($schema['DATABASEID'], $schema['SCHEMAID']);
        if ($_save) {

            if (!isl_trim($_severity)) {
                $gErr->addError("PL037", "edit_error.phtml:9", "The Severity cannot be empty");
            }

            if (!isl_trim($_description)) {
                $gErr->addError("PL037", "edit_error.phtml:13", "The Description cannot be empty");
            }

            if (!HasErrors()) {
                $err = array (
                'ERR_NO'         => isl_trim($_errno),
                'DESCRIPTION'    => isl_trim($_description),
                'CORRECTION'     => isl_trim($_correction),
                'SEVERITY'       => $_severity,
                'CLASS'          => isl_trim($_class)
                );
                if ($_r) {
                    $acc['RECORD#'] = $_r;
                    $stmt = "update IAERRORS set description = '$_description',
                                    correction = '$_correction',
                                    class	= '$_class'
                                where record# = $_r";
                    $ret = ExecSimpleStmt($stmt);
                } else {
                    $res = QueryResult("select max(record#) MAXRECNO from IAERRORS");
                    $res = $res[0];
                    if (empty($res['MAXRECNO'])) {
                        $res['MAXRECNO']=0;
                    }
                    $newrecno=$res['MAXRECNO']+1;
                    $stmtstr = "insert into IAERRORS (RECORD#, ERR_NO, DESCRIPTION, CORRECTION, SEVERITY, CLASS) values('$newrecno',
                                            '$_errno',
                                            '$_description',
                                            '$_correction',
                                            '$_severity',
                                            '$_class')";
                    $ret=ExecSimpleStmt($stmtstr);
                    $_obj['RECORD#'] = $newrecno;
                }
            }
        } else if (isset($_do) && $_do == 'del') {
            ExecStmt(array("delete from IAERRORS where record# = :1", $_r));
        }
    }

    if ($_ret) {
        Ret();
    }

    SetDBSchemaVars($schemaMap[0]['DATABASEID'], $schemaMap[0]['SCHEMAID']);
    $err = array();
    if ($_r) {
        $k = $_r;
        $err = QueryResult(array("SELECT * FROM iaerrors WHERE record# = :1", $_r));
        $err = $err[0];
    }

    if ($_r != '') {
        $query = "SELECT err_no FROM iaerrors";
        $errs = QueryResult($query);
    }
    $errMap = array('' => '-- Select Parent Account --');
    for ($i = 0; $i < count($errs); $i++) {
        $er = $errs[$i];
        $errMap[$er['RECORD#']] = $er['ERR_NO'];
    }

    $errSource = array(
        "PL" => "Presentation Layer(PL)",
        "BL" => "Business Layer(BL)",
        "DL" => "Data Access Layer(DL)",
        "XL" => "XML Layer(XL)",
        "PT" => "Platform (PT)"
    );
    $errSeverity = array(
        "" => "--------------Select--------------",
        "01" => "1 (Errors That can't be Handled)",
        "02" => "2 (Coding Errors)",
        "03" => "3 (Logical Errors)",
        "04" => "4 (Authenticaton Errors)",
        "05" => "5 (Warnings)"
    );

    $title = 'Intacct - Error Information';
    $hlpfile='';
?>

<? require_once 'header.inc'; ?>

<script language="JavaScript">
function callError(){
document.NumSource.submit();
}
</script>

   <table width="90%" border="0" cellpadding=0 cellspacing=0 bgcolor="<? /** @noinspection PhpUndefinedVariableInspection */
   echo $color4 ?>">
        <tr bgcolor="<? /** @noinspection PhpUndefinedVariableInspection */
        echo $color1 ?>">
			<td><img src="../resources/images/ia-app/backgrounds/trans.gif" alt="" width="1" height="20" border="0"></td>
            <td colspan="2" align=left width="100%">
                <font size=+1 color="#ffffff" face="arial"><b>
                    Error Information
                </b></font>
            </td>
            <? PrintHelpLink(); ?>
        </tr>
    </table>
    <p>
    <table border=0 cellpadding=2 cellspacing=0 width="90%">
	    <form name="NumSource" action="<? echo GoUrl(ExtendUrl("edit_error.phtml", (".do=edit&.r=$k")));  ?>" method=post>
		<input type=hidden name=".r" value="<?echo $_r; ?>">
        <tr>
            <td align=right valign=top>
                <font face="verdana, arial" size="2">
                    Source
                </font>
            </td>
            <td valign=top>
           		<select name=".source">
        <? if(isl_substr($err['ERR_NO'], 0, 2) != '') {
            ShowOptions(isl_substr($err['ERR_NO'], 0, 2), $errSource);
} else {
            ShowOptions(Request::$r->_source, $errSource);
}
        ?>
           		</select>
            </td>
        </tr>
        <tr>
            <td align=right valign=top>
                <font color="#FF0000" face="verdana, arial" size="2">
                    *Severity
                </font>
            </td>
            <td valign=top>
            	<select name=".severity" onChange="callError()">
        <? if($err['SEVERITY'] != '') {
            ShowOptions($err['SEVERITY'], $errSeverity);
} else{
            ShowOptions($_severity, $errSeverity);
}
        ?>
            	</select>
            </td>
        </tr>
	    </form>
	    <form action="<? echo GoUrl("edit_error.phtml"); ?>" method=post>
		<input type=hidden name=".r" value="<?echo $_r; ?>">
		<input type=hidden name=".ret" value="1">
		<input type=hidden name=".severity" value="<?
        if( $_severity != '') {echo $_severity;
}else{
		    echo $err['SEVERITY'];
} ?>">
		<tr>
            <td align=right valign=top>
                <font face="verdana, arial" size="2">
                    Err Number
                </font>
            </td>
            <td valign=top>
 			<?
            $errno = QueryResult("select max(to_number(substr(err_no,5))) MAXERRNO from IAERRORS group by substr(err_no,1,2) having substr(err_no,1,2)='" . Request::$r->_source . "'");
                $errno = $errno[0];
    if (empty($errno['MAXERRNO'])) {
        $errno['MAXERRNO']=0;
    }
                $newerrno=$errno['MAXERRNO']+1;
    if($_source != '' && $_severity != '') {
        if($newerrno < 10) {
            $newerrno = $_source.$_severity."00000".$newerrno;
        } elseif($newerrno >= 10 && $newerrno < 100) {
            $newerrno = $_source.$_severity."0000".$newerrno;
        } elseif($newerrno >= 100 && $newerrno < 1000) {
            $newerrno = $_source.$_severity."000".$newerrno;
        } elseif($newerrno >= 1000 && $newerrno < 10000) {
            $newerrno = $_source.$_severity."00".$newerrno;
        } elseif($newerrno >= 10000 && $newerrno < 100000) {
            $newerrno = $_source.$_severity."0".$newerrno;
        } elseif($newerrno >= 100000) {
            $newerrno = $_source.$_severity.$newerrno;
        }
        $newerr = $newerrno;
        echo util_encode($newerrno);
    } elseif($err['ERR_NO'] != '') {
        echo util_encode($err['ERR_NO']);
    }

    ?>
			<input type=hidden name=".errno" value="<? echo util_encode($newerrno); ?>">
            </td>
        </tr>
        <tr>
            <td align=right valign=top>
                <font color="#FF0000" face="verdana, arial" size="2">
                    *Description
                </font>
            </td>
            <td valign=top>
                <input type=text name=".description" value="<? echo isl_htmlspecialchars($err['DESCRIPTION']) ?>" size=40 maxlength="<? echo FieldSize('iaerror.description'); ?>" >
            </td>
        </tr>
        <tr>
            <td align=right valign=top>
                <font face="verdana, arial" size="2">
                    Correction
                </font>
            </td>
            <td valign=top>
             <input type=text name=".correction" value="<? echo isl_htmlspecialchars($err['CORRECTION']) ?>" size=40 maxlength="<? echo FieldSize('iaerror.description'); ?>" >
            </td>
        </tr>
        <tr>
            <td align=right valign=middle>
                <font face="verdana, arial" size="2">
                    Class
                </font>
            </td>
            <td valign=top>
             <input type=text name=".class" value="<? echo isl_htmlspecialchars($err['CLASS']) ?>" size=40 maxlength="<? echo FieldSize('iaerror.description'); ?>" >
            </td>
        </tr>
		<tr>
			<td><font color="#FF0000" face="Verdana, Arial, Helvetica" size="0">* required </font></td>
			<td >&nbsp;</td>
		</tr>
    </table>
      <p>
      <table width="90%" border="0" cellpadding=4 cellspacing=0 bgcolor="<?echo $color4 ?>">
        <tr>
            <? PrintSaveCancelButton(); ?>
        </tr>
      </table>
	 </form>
<?
    require_once 'footer.inc';
