<?php

    // Note: this file is not generated by ent2qry.  There is no makefile in this directory.

$kIMSSummaryQueries['QRY_IMS_GENERAL_STATUS'] = array (
    'QUERY'        => "select count(1) as queued from imspackage as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
	                where state = 'Q' and imspackage.imsqueuekey IN (SELECT imsqueuetype.permanentqueueguid FROM imsqueuetype)
	                and exists (select 1 from company where cny# = imspackage.cny# and status = 'T')",
    'COLUMNS'   => array('QUEUED' => 11),
    'TITLE'     => 'Current status for all packages:',
);

$kIMSSummaryQueries['QRY_IMS_TOPIC_QUEUED_CURRENT'] = array (
    'QUERY'        => "select topic, queued_current from
(
 select topic, count(1) as queued_current
 from imspackage as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
 where state = 'Q'
 and imspackage.imsqueuekey IN (SELECT imsqueuetype.permanentqueueguid FROM imsqueuetype)
 and exists (select 1 from schemamap where cny# = imspackage.cny# and status = 'T')
 group by topic
 order by queued_current desc
)
where rownum <= 10",
    'COLUMNS'   => array('TOPIC' => -45, 'IN QUEUE' => 1),
    'Aggregate' => [
        'gb' => 'TOPIC',
        'totals' => [
            ['QUEUED_CURRENT', 'sum'],
        ]
    ],
    'Top' => [
        'cnt' => 10,
        'order' => [ 'QUEUED_CURRENT', 'DESC']
    ],
);

$kIMSSummaryQueries['QRY_IMS_CNY_QUEUED_CURRENT'] = array (
    'QUERY'        => "
select cny#, title, queued_current from
(
 select p.cny#, c.title, count(1) as queued_current
 from imspackage as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') p,
      company as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') c
 where p.cny# = c.record# and p.state = 'Q'
 and p.imsqueuekey IN (SELECT imsqueuetype.permanentqueueguid FROM imsqueuetype)
 and c.status = 'T'
 group by p.cny#, c.title
 order by queued_current desc
)
where rownum <= 10",
    'COLUMNS'   => array('CNY#' => 20, 'TITLE' => -45, 'IN QUEUE' => 1),
    'Top' => [
        'cnt' => 10,
        'order' => [ 'QUEUED_CURRENT', 'DESC']
    ],
);

$kIMSSummaryQueries['QRY_IMS_TRANSIT_CURRENT'] = array (
    'QUERY'        => "select p.cny#, c.title, p.topic, to_char(round((to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - p.timestarted ) * 24 * 60, 2),'99999999990.99') as time_in_transit
                    from imspackage as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') p,
                         company as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') c
                    where p.cny# = c.record# and p.state = 'T' order by time_in_transit desc",
    'COLUMNS'   => array('CNY#' => 20, 'TITLE' => -45, 'TOPIC' => -45, 'MINUTES IN TRANSIT' => 1),
);

$kIMSSummaryQueries['QRY_IMS_GENERAL_STATUS_X'] = array (
    'QUERY'        => "select decode(state, 'D', 'Dead', 'S', 'Delivered', 'Q', 'Queued', 'T', 'In Transit') status, count(1) as packages from imspackage as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
	                where timestamp_date > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24)) and imspackage.imsqueuekey IN (SELECT imsqueuetype.permanentqueueguid FROM imsqueuetype) group by state order by status desc",
    'COLUMNS'   => array('STATUS' => 11, 'PACKAGES' => 11),
    'TITLE'     => 'Current status for packages queued since {X} hours ago:',
    'Aggregate' => [
        'gb' => 'STATUS',
        'totals' => [
            ['PACKAGES', 'sum'],
        ]
    ],
);

$kIMSSummaryQueries['QRY_IMS_TOPIC_QUEUED_CURRENT_X'] = array (
    'QUERY'        => "
select topic, queued_current from
(
 select topic, count(1) as queued_current
 from imspackage as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
 where state = 'Q' and timestamp_date > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))
 and imspackage.imsqueuekey IN (SELECT imsqueuetype.permanentqueueguid FROM imsqueuetype)
 group by topic
 order by queued_current desc
)
where rownum <= 10",
    'COLUMNS'   => array('TOPIC' => -45, 'IN QUEUE' => 9),
    'Aggregate' => [
        'gb' => 'TOPIC',
        'totals' => [
            ['QUEUED_CURRENT', 'sum'],
        ]
    ],
    'Top' => [
        'cnt' => 10,
        'order' => [ 'QUEUED_CURRENT', 'DESC']
    ],
);

$kIMSSummaryQueries['QRY_IMS_CNY_QUEUED_CURRENT_X'] = array (
    'QUERY'        => "
SELECT cny#,
  title,
  NVL(inqueue, 0) inqueue,
  NVL(delivered, 0) delivered,
  NVL(dead, 0) dead
FROM
  (SELECT *
  FROM
    (SELECT p.cny#,
      c.title,
      p.state
    FROM imspackage AS OF TIMESTAMP to_date(:1, 'YYYY-MM-DD HH24:MI:SS') p,
         company AS OF TIMESTAMP to_date(:1, 'YYYY-MM-DD HH24:MI:SS') c
    WHERE p.cny# = c.record# and p.timestamp_date > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))
    AND p.imsqueuekey                                         IN
      (SELECT imsqueuetype.permanentqueueguid FROM imsqueuetype
      )
    ) PIVOT (SUM(1) FOR (state) IN ('Q' AS inqueue, 'S' AS delivered, 'D' AS dead))
  ORDER BY inqueue DESC nulls last
  )
WHERE rownum <= 10",
    'COLUMNS'   => array('CNY#' => 20, 'TITLE' => -45, 'IN QUEUE' => 9, 'DELIVERED' => 9, 'DEAD' => 9),
    'Top' => [
        'cnt' => 10,
        'order' => [ 'INQUEUE', 'DESC']
    ],
);

$kIMSSummaryQueries['QRY_IMS_INQUEUE_BY_HOUR_QUEUED'] = array (
    'QUERY'        => "
SELECT hour,
  COUNT(1) ct
FROM
  (SELECT TO_CHAR(timestamp_date, 'YYYY-MM-DD HH24:MI:SS') AS HOUR
  FROM imspackage as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
  WHERE state = 'Q'
  AND EXISTS
    (SELECT 1 FROM company as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') WHERE record# = imspackage.cny# AND status = 'T'
    )
  )
GROUP BY hour
ORDER BY hour DESC",
    'COLUMNS'   => array('HOUR' => -25, 'IN QUEUE' => 9),
    'TITLE' => 'In queue by hour queued:',
    'Aggregate' => [
        'gb' => 'HOUR',
        'totals' => [
            ['CT', 'sum'],
        ]
    ],
    'Top' => [
        'order' => ['HOUR', 'DESC']
    ],
);

$kIMSSummaryQueries['QRY_IMS_ACTIVITY_LAST_X'] = array (
    'QUERY'        => "
with QLH as (
select count(1) as queued_LH from imspackage as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
 where timestamp_date > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))
 and imspackage.imsqueuekey IN (SELECT imsqueuetype.permanentqueueguid FROM imsqueuetype)),
SLH as (
select count(1) as started_LH from imspackage as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
 where timestarted > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))
 and imspackage.imsqueuekey IN (SELECT imsqueuetype.permanentqueueguid FROM imsqueuetype)),
FLH as (
select count(1) as finished_LH from imspackage as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
 where timefinished > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))
 and imspackage.imsqueuekey IN (SELECT imsqueuetype.permanentqueueguid FROM imsqueuetype))
select queued_LH, started_LH, finished_LH from QLH, SLH, FLH",
    'COLUMNS'   => array('QUEUED' => 8, 'STARTED' => 8, 'FINISHED' => 8),
    'TITLE' => 'Activity in the last {X} hour(s):'
);

$kIMSSummaryQueries['QRY_IMS_TOPIC_QUEUED_SINCE_X'] = array (
    'QUERY'        => "
select topic, queued_since_x_hours_ago from
(
 select topic, count(1) as queued_since_x_hours_ago
 from imspackage as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
 where timestamp_date > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))
 and imspackage.imsqueuekey IN (SELECT imsqueuetype.permanentqueueguid FROM imsqueuetype)
 group by topic
 order by queued_since_x_hours_ago desc
)
where rownum <= 10",
    'COLUMNS'   => array('TOPIC' => -45, 'QUEUED' => 1),
    'Aggregate' => [
        'gb' => 'TOPIC',
        'totals' => [
            [strtoupper('queued_since_x_hours_ago'), 'sum'],
        ]
    ],
    'Top' => [
        'cnt' => 10,
        'order' => [ strtoupper('queued_since_x_hours_ago'), 'DESC']
    ],
);

$kIMSSummaryQueries['QRY_IMS_CNY_QUEUED_SINCE_X'] = array (
    'QUERY'        => "
select cny#, title, queued_since_x_hours_ago from
(
 select p.cny#, c.title, count(1) as queued_since_x_hours_ago
 from imspackage as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') p,
      company as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') c
 where p.cny# = c.record# and p.timestamp_date > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))
 and p.imsqueuekey IN (SELECT imsqueuetype.permanentqueueguid FROM imsqueuetype)
 group by p.cny#, c.title
 order by queued_since_x_hours_ago desc
)
where rownum <= 10",
    'COLUMNS'   => array('CNY#' => 20, 'TITLE' => -45, 'QUEUED' => 1),
    'Top' => [
        'cnt' => 10,
        'order' => [ strtoupper('queued_since_x_hours_ago'), 'DESC']
    ],
);

$kIMSSummaryQueries['QRY_IMS_ORPHANS_SINCE_X'] = array (
    'QUERY'        => "
select cny#, title, topic, orphaned_since_x_hours_ago from
(
 select p.cny#, c.title, p.topic, count(1) as orphaned_since_x_hours_ago
 from imspackage as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') p,
      company as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') c
 where p.cny# = c.record# and p.timestamp_date > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))
 and p.imsqueuekey IN (SELECT imsqueuetype.permanentqueueguid FROM imsqueuetype)
 and p.timestarted is not null
 and p.timefinished is null
 and p.state = 'D'
 group by p.cny#, c.title, p.topic
 order by orphaned_since_x_hours_ago desc
)
where rownum <= 25",
    'COLUMNS'   => array('CNY#' => 20, 'TITLE' => -45, 'TOPIC' => -45, 'ORPHANS' => 1),
    'TITLE' => 'Orphaned packages (Top 25) in the last {X} hour(s):',
    'Top' => [
        'cnt' => 25,
        'order' => [ strtoupper('orphaned_since_x_hours_ago'), 'DESC']
    ],
);

$kIMSSummaryQueries['QRY_IMS_ACTIVITY_PREVIOUS_X'] = array (
    'QUERY'        => "
with QLH as (
select count(1) as queued_LH from imspackage as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
 where timestamp_date > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - ((:2*2)/24))
 and timestamp_date <= (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))
 and imspackage.imsqueuekey IN (SELECT imsqueuetype.permanentqueueguid FROM imsqueuetype)),
SLH as (
select count(1) as started_LH from imspackage as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
 where timestarted > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - ((:2*2)/24))
 and timestarted <= (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))
 and imspackage.imsqueuekey IN (SELECT imsqueuetype.permanentqueueguid FROM imsqueuetype)),
FLH as (
select count(1) as finished_LH from imspackage as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
 where timefinished > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - ((:2*2)/24))
 and timefinished <= (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))
 and imspackage.imsqueuekey IN (SELECT imsqueuetype.permanentqueueguid FROM imsqueuetype))
select queued_LH, started_LH, finished_LH from QLH, SLH, FLH",
    'COLUMNS'   => array('QUEUED' => 8, 'STARTED' => 8, 'FINISHED' => 8),
    'TITLE' => 'Activity in the previous {X} hour(s):'
);

$kIMSSummaryQueries['QRY_IMS_TOPIC_QUEUED_PREVIOUS_X'] = array (
    'QUERY'        => "
select topic, queued_since_x_hours_ago from
(
 select topic, count(1) as queued_since_x_hours_ago
 from imspackage as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
 where timestamp_date > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - ((:2*2)/24))
 and timestamp_date <= (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))
 and imspackage.imsqueuekey IN (SELECT imsqueuetype.permanentqueueguid FROM imsqueuetype)
 group by topic
 order by queued_since_x_hours_ago desc
)
where rownum <= 10",
    'COLUMNS'   => array('TOPIC' => -45, 'QUEUED' => 1),
    'Aggregate' => [
        'gb' => 'TOPIC',
        'totals' => [
            [strtoupper('queued_since_x_hours_ago'), 'sum'],
        ]
    ],
    'Top' => [
        'cnt' => 10,
        'order' => [ strtoupper('queued_since_x_hours_ago'), 'DESC']
    ],
);

$kIMSSummaryQueries['QRY_IMS_CNY_QUEUED_PREVIOUS_X'] = array (
    'QUERY'        => "
select cny#, title, queued_since_x_hours_ago from
(
 select p.cny#, c.title, count(1) as queued_since_x_hours_ago
 from imspackage as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') p,
      company as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') c
 where p.cny# = c.record# and p.timestamp_date > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - ((:2*2)/24))
 and p.timestamp_date <= (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))
 and p.imsqueuekey IN (SELECT imsqueuetype.permanentqueueguid FROM imsqueuetype)
 group by p.cny#, c.title
 order by queued_since_x_hours_ago desc
)
where rownum <= 10",
    'COLUMNS'   => array('CNY#' => 20, 'TITLE' => -45, 'QUEUED' => 1),
    'Top' => [
        'cnt' => 10,
        'order' => [ strtoupper('queued_since_x_hours_ago'), 'DESC']
    ],
);

$kIMSSummaryQueries['QRY_IMS_ACTIVITY_LAST_HOUR'] = array (
    'QUERY'        => "
with QLH as (
select count(1) as queued_LH from imspackage as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
 where timestamp_date > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (1/24))
 and imspackage.imsqueuekey IN (SELECT imsqueuetype.permanentqueueguid FROM imsqueuetype)),
SLH as (
select count(1) as started_LH from imspackage as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
 where timestarted > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (1/24))
 and imspackage.imsqueuekey IN (SELECT imsqueuetype.permanentqueueguid FROM imsqueuetype)),
FLH as (
select count(1) as finished_LH from imspackage as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
 where timefinished > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (1/24))
 and imspackage.imsqueuekey IN (SELECT imsqueuetype.permanentqueueguid FROM imsqueuetype))
select queued_LH, started_LH, finished_LH from QLH, SLH, FLH",
    'COLUMNS'   => array('QUEUED' => 8, 'STARTED' => 8, 'FINISHED' => 8),
    'TITLE' => 'Activity in the last hour:'
);

$kIMSSummaryQueries['QRY_IMS_TOPIC_LAST_HOUR'] = array (
    'QUERY'        => "
select topic, queued_since_x_hours_ago from
(
 select topic, count(1) as queued_since_x_hours_ago
 from imspackage as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
 where timestamp_date > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (1/24))
 and imspackage.imsqueuekey IN (SELECT imsqueuetype.permanentqueueguid FROM imsqueuetype)
 group by topic
 order by queued_since_x_hours_ago desc
)
where rownum <= 10",
    'COLUMNS'   => array('TOPIC' => -45, 'QUEUED' => 1),
    'Aggregate' => [
        'gb' => 'TOPIC',
        'totals' => [
            [ strtoupper('queued_since_x_hours_ago'), 'sum'],
        ]
    ],
    'Top' => [
        'cnt' => 10,
        'order' => [ strtoupper('queued_since_x_hours_ago'), 'DESC']
    ],
);

$kIMSSummaryQueries['QRY_IMS_CNY_LAST_HOUR'] = array (
    'QUERY'        => "
select cny#, title, queued_since_x_hours_ago from
(
 select p.cny#, c.title, count(1) as queued_since_x_hours_ago
 from imspackage as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') p,
      company as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') c
 where p.cny# = c.record# and p.timestamp_date > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (1/24))
 and p.imsqueuekey IN (SELECT imsqueuetype.permanentqueueguid FROM imsqueuetype)
 group by p.cny#, c.title
 order by queued_since_x_hours_ago desc
)
where rownum <= 10",
    'COLUMNS'   => array('CNY#' => 20, 'TITLE' => -45, 'QUEUED' => 1),
    'Top' => [
        'cnt' => 10,
        'order' => [ strtoupper('queued_since_x_hours_ago'), 'DESC']
    ],
);


$kIMSSummaryQueries['QRY_IMS_COUNT_BY_DB'] = array (
    'QUERY'        => "
select queued from
(
 select count(1) as queued
 from imspackagedetail_fast as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
 where state = 'Q'
 and inpermanentqueue = 'Yes'
 order by queued desc
)",
    'COLUMNS'   => array('IN QUEUE' => 1),
    'TITLE' => "Currently queued by database:",
    'Top' => [
        'order' => [ strtoupper('queued'), 'DESC']
    ],
);

/*
 * Disabling this query because it consumes too much time
 * We may re-enable it after we improve its performance (need DBA assistance)

$kIMSSummaryQueries['QRY_IMS_ACTIVITY_BY_DB'] = array (
    'QUERY'        => "
with QLH as (
select db, count(1) as queued_LH from imspackagedetail_fast as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
 where timestamp_date > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))
 and inpermanentqueue = 'Yes'
 group by db),
SLH as (
select db, count(1) as started_LH from imspackagedetail_fast as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
 where timestarted > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))
 and inpermanentqueue = 'Yes'
 group by db),
FLH as (
select db,
  count(1) as finished_LH,
  to_char(round(sum(timeintransit) / :2,0), '999990') intrans,
  NUMHIGHRESOURCEPROCESSES,
  (to_char(round((100/numhighresourceprocesses) * sum(timeintransit) / (:2 * 3600), 2),'990.99') || '%') as efficiency
  from
  (
    SELECT db,
    CASE
      WHEN timestarted > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))
      THEN (timefinished - timestarted) * 86400
      ELSE (timefinished - (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))) * 86400
    END as timeintransit
    FROM imspackagedetail_fast as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
    WHERE timefinished is not null
    AND timefinished > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))
    AND inpermanentqueue = 'Yes'
  ) p, imsqdbconfigurationline c
where c.databaseid = p.db
group by db, NUMHIGHRESOURCEPROCESSES)
select qlh.db, queued_LH, started_LH, finished_LH, intrans, numhighresourceprocesses, efficiency from QLH, SLH, FLH
where qlh.db = slh.db
and qlh.db = flh.db
order by db asc",
    'COLUMNS'   => array('DBID' => -5, 'QUEUED' => 9, 'STARTED' => 9, 'FINISHED' => 9, 'SECS/HOUR' => 10, 'CONCUR' => 1, 'UTILIZATION' => 6),
    'TITLE' => 'Activity by database in the last {X} hour(s):'
);
*/
