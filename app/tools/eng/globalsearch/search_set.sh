#! /bin/bash

cmd=
invalid=0

for i in $*; do
	case $i in
	-cnys=*|-cnames=*|-otypes=*|-lockfor=*|-maxbprocs=*|-engine=* )
		cmd="${cmd}-d .${i:1} "
		;;
    -engine=* )
        cmd="${cmd}-d .${i:1} "
        ;;
	-fullbuild )
		cmd="${cmd}-d .isincr=0 "
		;;
	-setpending )
		cmd="${cmd}-d .settodo=1 "
		;;
	-setdone )
		cmd="${cmd}-d .settodo=0 "
		;;
	-overridelock )
		cmd="${cmd}-d .overridelock=0 "
		;;
	-prior=*|-procnum=*|-nprocs=* )
		cmd="${cmd}-d .${i:1} "
		;;
	-u=* )
		upath="${i:3}/tools/globalsearch/search_admin.phtml"
		;;
	-dlog=* )
		cmd="${cmd}-d .${i:1} "
		;;
	-detlog=* )
		cmd="${cmd}-d .${i:1} "
		;;
	-dbid=*|-schemaid=*|-dlog=* )
		cmd="${cmd}-d .${i:1} "
		;;
	-help )
		invalid=1
		;;
	*)
		echo "Unknown option $i"
		invalid=1
		;;
    esac
done

if [ -z "$upath" ]
then
	echo "You must specify the -u=url argument to set the URL path"
	invalid=1
fi

if [ $invalid -eq 1 ]
then
	echo "Valid arguments are:"
	echo "   -cnys=list  takes a comma-separated list of cny# for companies to set (default is all companies)"
	echo "   -engine=ENG sets engine to ENG (default ORA, available values ORA, ES) "
	echo "   -cnames=pattern  takes a regular expression of matched company names to set (default is all companies)"
	echo "   -otypes=list  takes a comma-separated list of object types to set (default is all object types)"
	echo "   -fullbuild  sets the builds to full (default is incremental)"
	echo "   -setpending  sets the affected companies to build Pending (default is Pending)"
	echo "   -setdone  sets the affected companies to build Done (default is Pending)"
	echo "   -prior=N  sets the affected companies build priority to N (default is 1)"
	echo "   -procnum=N  sets the affected companies build proc number to N (default is 1)"
	echo "   -u=url  sets the URL path to the script (as in 'https://URL/cstools/...')"
	echo "   -pnum=N  sets proc number to N (default is 1)"
	echo "   -dbid  sets the DB id (default is 100)"
	echo "   -schemaid  sets the schema id (default is 01)"
	echo "   -lockfor=N locks setting for N seconds (default is no lock)"
	echo "   -overridelock overrides any existing locks set by -lockfor (default is no lock override)"
	echo "   -maxbprocs=N limits the maximum build procs to N (default is infinite)"
	echo "   -dlog=logfile  sets the production log file (in the apache /tmp directory) (default is no log file)"
	echo "   -detlog=logfile  sets the detailed log file (in the apache /tmp directory) (default is no log file)"
	echo "   -help  prints this message"
	exit 1
fi

echo "Running:" "curl " -d ".saop=setprior" $cmd $upath
CSTATUS=`curl -d ".saop=setprior" $cmd $upath`
if [ "$?" -ne "0" ]; then
	echo "curl non-zero exit code:" $?
	exit 1
fi

echo "$CSTATUS" | grep "ExitStatus:OK" >/dev/null 2>&1
if [ $? -ne 0 ]; then
	echo $CSTATUS | fgrep "ExitStatus:LOCKED" > /dev/null 2>&1
	if [ $? -ne 0 ]; then
		echo "Search set failed:"
		echo $CSTATUS
		exit 1
	else
		echo "Search set locked out by a previous search set"
		exit 2
	fi
fi

echo "Search set was successful:"
echo $CSTATUS
exit 0

