<?php
//=============================================================================
//
//	FILE:			los_rate_limit_edit_selected.phtml
//	AUTHOR:			Marina Vatkina
//	DESCRIPTION:	Edit existing Tier/Type throttle and rate limits
//
//	(C)2023, Sage Intacct, All Rights Reserved
//
//=============================================================================
require_once 'RateLimitHelper.cls';

$rateLimitHelper = new RateLimitHelper();
$_r = Request::$r->_r;
// convert from recordNo
logToFileDebug("++++++++++++++++data from recordno: " . json_encode($_r));
$typeOrName = $rateLimitHelper->getTierOrTypeFromRecordNo($_r);

logToFileDebug("++++++++++++++++  name: $typeOrName");

$record = &Request::$r->_record;
logToFileDebug("++++++++++++++++FOUND: " . json_encode($record));
$kRateLimits = Request::$r->_kRateLimits;
$maxLimits = Request::$r->_maxLimits;
$tenantAppProfileKey = $record['RATELIMITPROFILEKEY'] ?? '';

$isAPI = ($record['SERVICE_NAME'] ?? LOSRateLimitManager::API_SERVICE_NAME) === LOSRateLimitManager::API_SERVICE_NAME;
logToFileDebug("++++++++++++++++isAPI: $isAPI");

$focus = '.id';?>
<?
Head('#FFFFFF', '#000000', '#006699', '#990000', '#000000');
?>

<form name = los_rate_limit_edit_selected method = post>
    <BR>
    <?php
    $title = 'Rate Limit Manager: Tier/Type Throttle and Rate Limits';
    htmlHeaderBar($title);
    ?>
    <p style="margin:40px 0 0 0"></p>
    <table align=center border="1" cellpadding="2" bgcolor="#FFFFFF"  cellspacing="1" width="40%" >
        <BR>
        <tr>
            <td align=right valign=center >
                <font  face="verdana, arial" size="3" style="white-space: nowrap">
                    Name
                    &nbsp;
                </font>
            </td>
            <td align=left valign=center >
                <font face="Verdana, Arial, Helvetica" ><? echo $record['NAME'] ?></b></font>
            </td>
        </tr>
	<tr>
            <td align=right valign=center >
                <font  face="verdana, arial" size="3">
                    Tenant Type
                    &nbsp;
                </font>
            </td>
            <td align=left valign=center >
                <font face="Verdana, Arial, Helvetica" ><? echo ($record['TENANT_TYPE'] ?? ($rateLimitHelper->getTypeFromName($typeOrName) ?? LOSRateLimitManager::DEFAULT_TENANT_TYPE)) ?></b></font>
            </td>
        </tr>
        <tr>
            <td align=right valign=center >
                <font  face="verdana, arial" size="3">
                    Tier
                    &nbsp;
                </font>
            </td>
            <td align=left valign=center >
                <font face="Verdana, Arial, Helvetica" ><? echo ($record['TIER'] ?? ($rateLimitHelper->getTierFromName($typeOrName) ?? LOSRateLimitManager::DEFAULT_TIER)) ?></b></font>
            </td>
        </tr>
        <tr>
            <td align=right valign=top>
                <font face="verdana, arial" size="3" style="white-space: nowrap">
                    Rate Limit Profile
                    &nbsp
                </font>
            </td>
            <td valign=top>
                <?if (!$isAPI){ ?>
                    <font face="Verdana, Arial, Helvetica" ><? echo RateLimitHelper::NOT_AVAILABLE ?></b></font>
                <?} else { ?>
                    <select name=".ratelimitprofilekey">
                        <? ShowOptions($tenantAppProfileKey, $kRateLimits); ?>
                    </select>
                <?} ?>
            </td>
        </tr>
        <tr>
            <td align=right valign=center >
                <font  face="verdana, arial" size="3">
                    Throttle
                    &nbsp;
                </font>
            </td>
            <td valign=left>
                <?if (!$isAPI){ ?>
                    <font face="Verdana, Arial, Helvetica" ><? echo ( $record['THROTTLE_LIMIT'] ?? 0) ?></b></font>
                <?} else { ?>
                    &nbsp;<input type=number min="-1" step="1" name=".throttle" value="<? echo ( $record['THROTTLE_LIMIT'] ?? 0) ?>">
                <?} ?>
            </td>
        </tr>
        <tr>
            <td align=right valign=center >
                <font  face="verdana, arial" size="3" style="white-space: nowrap">
                    Service Name
                    &nbsp;
                </font>
            </td>
            <td valign=left>
                <font face="Verdana, Arial, Helvetica" ><? echo ($record['SERVICE_NAME'] ?? LOSRateLimitManager::API_SERVICE_NAME) ?></b></font>
            </td>
        </tr>
        <tr>
            <td align=right valign=center >
                <font  face="verdana, arial" size="3" style="white-space: nowrap">
                    Service Value
                    &nbsp;
                </font>
            </td>
            <td valign=left>
                <?if ($isAPI){ ?>
                    <font face="Verdana, Arial, Helvetica" ><? echo $record['SERVICE_VALUE'] ?></b></font>
                <?} else { ?>
                    &nbsp;<input type=number min="-1" step="1" name=".service_value" value="<? echo ( $record['SERVICE_VALUE'] ?? 0) ?>">
                <?} ?>
            </td>
        </tr>
        <tr>
            <td align=right valign=top>
                <font face="verdana, arial"  size="3" style="white-space: nowrap">
                    Throttle Coefficient &nbsp;
                </font>
            </td>
            <td valign=top>
                    <input type=number name=".throttle_coefficient" value="<? echo isl_htmlspecialchars($record['THROTTLE_COEFFICIENT'] ?? 1) ?>" min="-1" step="0.01" pattern="^\(-1)?d(?:\.\d{1,2})?$" max="<? echo $maxLimits[RateLimitHelper::THROTTLE_COEFFICIENT_MAX_KEY] ?>" >
            </td>
        </tr>
        <tr>
            <td align=right valign=center >
                <font  face="verdana, arial" size="3">
                    Notes
                    &nbsp;
                </font>
            </td>
            <td align=left valign=center >
                &nbsp;<textarea name=".notes" align="left" maxlength="255" rows="3" cols="50"><?=isl_htmlspecialchars($record['NOTES'] ?? '');?></textarea>
            </td>
        </tr>
        <tr>
            <td colspan="2" align =center valign=center>
                <input type=submit name=".save" value="Save" >
                <input type=submit name=".save" value="Cancel" >
            </td>
        </tr>
    </table>
</form>
</body>
</html>
