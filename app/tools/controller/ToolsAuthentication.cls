<?php
/**
 * Wrapper class to do the cstools authentication
 *
 * LICENSE:
 * (C)2000-2016 Intacct Corporation, All Rights Reserved
 *
 * Intacct Corporation Proprietary Information.
 * This document contains trade secret data that belongs to Intacct
 * corporation and is protected by the copyright laws. Information herein
 * may not be used, copied or disclosed in whole or part without prior
 * written consent from Intacct Corporation.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 */

use <PERSON><PERSON><PERSON><PERSON>\XMLSecLibs\XMLSecEnc as XMLSecEnc;
use <PERSON><PERSON><PERSON><PERSON>\XMLSecLibs\XMLSecurityKey as XMLSecurityKey;

/**
 * Wrapper class to do cstools authentication
 *
 * <AUTHOR> <<EMAIL>>
 */

final class ToolsAuthentication
{
    /**
     * Cookie name used for cstools authentication
     */
    const TOOLS_AUTH_COOKIE_NAME = 'php_mini_auth';

    /**
     * <PERSON>ie expiry time
     */
    const TOOLS_AUTH_COOKIE_EXPIRY_TIME = 14400;

    /**
     * IDP types - Azure/Okta/None
     */
    const IDP_TYPE_AZURE = 'AZURE';
    const IDP_TYPE_OKTA  = 'OKTA';
    const IDP_TYPE_NONE  = 'NONE';
    /**
     * IDP Issuer hostnames
     */
    const IDP_HOST_OKTA  = '.okta.com';
    const IDP_HOST_AZURE = '.windows.net';

    /**
     * CSTools user record number
     *
     * @var int $userRecordNo
     */
    public $userRecordNo = null;

    /**
     * CSTools username
     *
     * @var string $userName
     */
    public $userName = null;

    /**
     * CSTools user password in plain text
     *
     * @var string $userPassword
     */
    private $userPassword = null;

    /**
     * CSTools authentication cookie value
     *
     * @var string $authCookieValue
     */
    private $authCookieValue = null;

    /**
     * SAML response in case of SSO login
     *
     * @var string $responseSaml
     */
    private $responseSaml;

    /**
     * SSO disabled or not for CSTools
     *
     * @var bool $ssoDisabled
     */
    private $ssoDisabled;

    /**
     * IDP type used for the saml request
     *
     * @var string $idpType
     */
    private $idpType;

    /**
     * If SSO disabled generally, allow SSO for specific cstools users
     *
     * @var string[] $allowSSOForUsers
     */
    private $allowSSOForUsers = array();

    /**
     * CSTools authentication happening using password or not.
     * This is used to redirect to correct URL soon after the login
     *
     * @var bool $authUsingPassword
     */
    private $authUsingPassword = false;

    /**
     * CSTools authentication happening using SSO
     * This is used to redirect to correct URL soon after the login
     *
     * @var bool $authUsingSso
     */
    private $authUsingSso = false;

    /**
     * Tool access token to allow access from third party scripts
     *
     * @var string $toolAccessToken
     */
    private $toolAccessToken;

    /**
     * To avoid multiple times authentication
     *
     * @var bool $alreadyAuthenticated
     */
    private $alreadyAuthenticated = false;

    /**
     * Singleton
     *
     * @var ToolsAuthentication $t Singleton class
     */
    public static $t = null;

    private function __construct()
    {
        $cstoolsSsoConfig = GetValueForIACFGProperty('CSTOOLS_SSO_CONFIG');
        $this->ssoDisabled = $cstoolsSsoConfig['DISABLE_SSO'] === "1";
        $usersList         = $cstoolsSsoConfig['SSO_USERS'];
        if ( !empty($usersList)) {
            $this->allowSSOForUsers = explode(',', $usersList);
            array_walk($this->allowSSOForUsers, 'trim');
        }
    }

    /**
     * Create a singleton instance of the ToolsAuthentication
     *
     * @return ToolsAuthentication Authentication class
     */
    public static function getInstance()
    {
        if ( self::$t instanceof self ) {
            return self::$t;
        } else {
            self::$t = new self();
            return self::$t;
        }
    }

    /**
     * Validate the cstools authentication cookie
     *
     * @return bool True if success else False
     */
    private function validateAuthCookie()
    {
        if ( $this->authCookieValue != '' ) {
            $cookieValueDecrypted = TwoWayDecryptWithKey($this->authCookieValue, "IA_INIT");
            $cookieInfo = explode("-", $cookieValueDecrypted);
            $cookieUserRecordNo = $cookieInfo[0];
            $CookieSecretString = $cookieInfo[1];
            // override username from cookie
            $this->userRecordNo = $cookieUserRecordNo;
            $sqlAuth = "select username from csaccl where record#=:1 and string=:2";
            $resultData = QueryResult(
                array($sqlAuth, $cookieUserRecordNo, $CookieSecretString), 0, '', GetCSConnection()
            );
            if ($resultData[0]['USERNAME']) {
                $this->userName = $resultData[0]['USERNAME'];
                Globals::$g->cstoolsLoginId = $this->userName;
                Request::$r->_username = $this->userRecordNo;
                return true;
            } else {
                // reset the cookie if couldn't log in (invalid credentials)
                $this->clearAuthentication();
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * Validate the cstools authentication through username and password
     *
     * @return bool True if success else False
     */
    private function validateLoginCredentials() {
        if ($this->isSSOUser($this->userName)) {
            addLog("CSTools SSO Error: Form based authentication not allowed for " . $this->userName, LogManager::ERROR);
            return false;
        }

        $oCSUser = CSUserModel::CreateByUsername($this->userName);
        if ($oCSUser === null) {
            return false;
        }

        if ($oCSUser->password2 !== null and strlen($oCSUser->password2) > 0) {
            // user has the new encryption password; us this for authentication
            if (!password_verify($this->userPassword, $oCSUser->password2)) {
                return false;
            }
        } else {
            // TODO 2023-05-12 ovi - remove after users are migrated (like in a year or so :) )
            // user has only the old encryption
            addLog("CSTools auth: user with old encryption password. username:" . $oCSUser->username, LogManager::WARN);
            // validate old encryption
            if (!hash_equals($oCSUser->password, CSUserModel::DeprecatedEncryptPassword($this->userPassword))) {
                return false;
            }

            // migrate user to new encryption
            $oCSUser->password2 = CSUserModel::EncryptPassword($this->userPassword);
            $oCSUser->Save();
        }

        $this->userRecordNo = $oCSUser->record;
        $this->authUsingPassword = true;
        Globals::$g->cstoolsLoginId = $this->userName;
        // TODO ovi - we should not set any Request vars !!
        // this is misleading... username set as recordno !!!
        Request::$r->_username = $this->userRecordNo;

        return true;
    }

    /**
     * Pull the details of cstools user
     *
     * @param string $userName CSTools username
     *
     * @return string[][]
     * @throws IAException
     */
    // TODO ovi - replace with calls to CSUserModel::CreateByUsername()
    private function fetchUserInfo($userName) {
        $sql = "SELECT record# recordno, username, password FROM csaccl WHERE username = :1";
        $result = readQuery(array($sql, $userName), GetCSConnection(), "Cannot get user info (from username).");

        return $result;
    }

    /**
     * Pull the details of cstools user based on email id
     *
     * @param string $userEmail CSTools user email id
     *
     * @return string[][]
     * @throws IAException
     */
    // TODO ovi - replace with CSUserModel::CreateByEmail() (need to define it :) )
    private function fetchUserInfoByEmail($userEmail) {
        $sql = "SELECT record# recordno, username, password FROM csaccl WHERE email = :1";
        $result = readQuery(array($sql, $userEmail), GetCSConnection(), "Cannot get user info (from email).");

        if (count($result) > 1) {
            LogToFile("CSTools Error: Two users found for the same email id " . $userEmail);
            return array();
        }

        return $result;
    }

    /**
     * Get the type of Identity Provider  - Azure or Okta
     *
     * @return string IDP type
     */
    private function getIDPType()
    {
        $tempAssertion = base64_decode($this->responseSaml);
        $samlDocument = new DOMDocument();
        $samlDocument->loadXML($tempAssertion);
        $issuerNode = $samlDocument->getElementsByTagName('Issuer');
        if ($issuerNode->length == 0) {
            logToFileError('CSTools SSO Error: Unable to find Issuer in the SAML request');
        // ovi - 2023-02-11 - microsoft generates now 2 Issuer nodes, with the same value;
        // skip the exactly one node requirement
//        } elseif ($issuerNode->length > 1) {
//            logToFileError('CSTools SSO Error: Multiple Issuer found for the SAML request');
        } else {
            $issuerUrl = $issuerNode->item(0)->nodeValue;
            if (strstr($issuerUrl, self::IDP_HOST_OKTA)){
                return self::IDP_TYPE_OKTA;
            } elseif (strstr($issuerUrl, self::IDP_HOST_AZURE)){
                return self::IDP_TYPE_AZURE;
            } else {
                logToFileError('CSTools SSO Error: Invalid IDP - Only Okta/Azure is supported');
            }
        }
        return self::IDP_TYPE_NONE;
    }

    /**
     * Fetch sso configuration based on the saml request from the IDP
     *
     * @param string|null $idpType IDP Type
     *
     * @return array Configuration of SSO
     */
    private function getCSToolsSSOConfig(string $idpType = null)
    {
        $idpType = $idpType ?: $this->idpType;
        if ( $idpType === self::IDP_TYPE_AZURE ){
            return GetValueForIACFGProperty('CSTOOLS_SSO_CONFIG_AZURE');
        } elseif ( $idpType === self::IDP_TYPE_OKTA ){
            return GetValueForIACFGProperty('CSTOOLS_SSO_CONFIG_OKTA');
        } else {
            return [];
        }
    }

    /**
     * Validate the SSO login
     *
     * @return bool True if success else False
     */
    private function validateSSOLogin() {
        if (empty($this->responseSaml)) {
            addLog("CSTools SSO Error: empty SAML response", LogManager::ERROR);

            return false;
        }

        $this->idpType = $this->getIDPType();
        $idpCertificate = $this->getCSToolsIdpCertificate();
        if (empty($idpCertificate)) {
            addLog("CSTools SSO Error: empty idp certificate", LogManager::ERROR);

            return false;
        }

        try {
            $samlSetting = new Saml_Settings();
            $samlSetting->setIdpPublicCertificate($idpCertificate);
            $samlResponse = new Saml_Response($samlSetting, $this->responseSaml);

            if (!$samlResponse->isValid()) {
                addLog("CSTools SSO Error: SAML response validation failure", LogManager::ERROR);

                return false;
            }

            if ($samlResponse->isEncryptedAssertion() and !$this->decryptAssertion($samlResponse)) {
                addLog("CSTools SSO Error: Unable to decrypt saml assertion", LogManager::ERROR);

                return false;
            }

            $userEmail = $samlResponse->getNameId();
            if (empty($userEmail)) {
                addLog("CSTools SSO Error: Missing email id in the SAML response", LogManager::ERROR);

                return false;
            }

            // search by emailid first, if not found then search by username
            $userInfo = $this->fetchUserInfoByEmail($userEmail);
            if (!isset($userInfo[0]["RECORDNO"])) {
                $emailInfo = explode("@", $userEmail);
                $userInfo = $this->fetchUserInfo($emailInfo[0]);
                if (!isset($userInfo[0]["RECORDNO"])) {
                    addLog("CSTools SSO Error: Missing user info in DB for the email " . $userEmail, LogManager::ERROR);

                    return false;
                }
            }

            if ($this->isSSOUser($userInfo[0]["USERNAME"])) {
                $this->userRecordNo         = $userInfo[0]["RECORDNO"];
                $this->userName             = $userInfo[0]["USERNAME"];
                Globals::$g->cstoolsLoginId = $this->userName;
                // TODO ovi - we should not set any Request vars !!
                // this is misleading... username set as recordno !!!
                Request::$r->_username      = $this->userRecordNo;
                $this->authUsingSso         = true;

                return true;
            }
            if ($this->ssoDisabled) {
                logToFileError("CSTools SSO Error: SSO is not enabled for " . $userInfo[0]["USERNAME"]);
            }
        } catch (Exception $e) {
            logToFileError("CSTools SSO Error: Exception message - " . $e->getMessage());
        }

        return false;
    }

    /**
     * Find if the user is sso user or not. This is purely based on the CFG configuration
     *
     * @param string $userName CSTools username
     *
     * @return bool True if the user is cstools user else False
     */
    public function isSSOUser($userName)
    {
        // SSO can be enabled for all users or specific users. See the config in CFG file
        return !$this->ssoDisabled || in_array($userName, $this->allowSSOForUsers);
    }

    /**
     * Decrypt SAML assertion based on the algorithms supported for cstools
     *
     * @param Saml_Response $samlResponse Saml Response Object
     *
     * @return bool True if success else False
     */
    private function decryptAssertion(Saml_Response $samlResponse)
    {
        $preConditions        = true;
        $samlEncAssertionPath = '/samlp:Response/saml:EncryptedAssertion';
        $samlEncKeyPath       = '//xmlencr:EncryptedKey';
        $samlEncDataPath      = $samlEncAssertionPath . '/xmlencr:EncryptedData';
        $cstoolsSsoConfig     = $this->getCSToolsSSOConfig();
        $transportPassword    = TwoWayDecryptWithKey($cstoolsSsoConfig['TRANSPORT_PRIVATE_KEY_PASSPHRASE'], 'IA_INIT');
        $transportKeyFilePath = ia_cfg::getCfgDir() . $cstoolsSsoConfig['TRANSPORT_PRIVATE_KEY_FILENAME'];
        $assertionAlgorithm   = $samlResponse->getEncryptionAlgorithm($samlEncDataPath);
        $assertionTransportNodeList = $samlResponse->queryEncryptedAssertion($samlEncDataPath);
        $assertionTransportNode = $assertionTransportNodeList->item(0);
        $xmlSecKeyInfo = XMLSecEnc::staticLocateKeyInfo(null, $assertionTransportNode);
        $transportAlgorithm = $xmlSecKeyInfo->getAlgorithm();


        // Validation before the decryption for the configurations and supported algorithms
        if (empty($cstoolsSsoConfig['TRANSPORT_PRIVATE_KEY_PASSPHRASE'])) {
            LogToFile("CSTools SSO Error: Missing CFG config TRANSPORT_PRIVATE_KEY_PASSPHRASE");
            $preConditions = false;
        }
        if (empty($cstoolsSsoConfig['TRANSPORT_PRIVATE_KEY_FILENAME'])) {
            LogToFile("CSTools SSO Error: Missing CFG config TRANSPORT_PRIVATE_KEY_FILENAME");
            $preConditions = false;
        }
        if (empty(file_get_contents($transportKeyFilePath))) {
            LogToFile("CSTools SSO Error: Empty key file $transportKeyFilePath");
            $preConditions = false;
        }

        if ( $preConditions ) {
            // Transport Token decryption
            $xmlSecEncTransport = new XMLSecEnc();
            $xmlSecKeyTransport = new XMLSecurityKey($transportAlgorithm, ['type' => 'private']);
            $xmlSecKeyTransport->passphrase = $transportPassword;
            $xmlSecKeyTransport->loadKey($transportKeyFilePath, true, false);
            $keyNodeList = $samlResponse->queryEncryptedAssertion($samlEncKeyPath);
            $keyNode = $keyNodeList->item(0);
            $xmlSecEncTransport->setNode($keyNode);
            $xmlSecEncTransport->type = $keyNode->getAttribute('type');
            $decryptedToken = $xmlSecEncTransport->decryptNode($xmlSecKeyTransport);

            // Assertion decryption using the transport token
            $xmlSecEncAssertion = new XMLSecEnc();
            $xmlSecKeyAssertion = new XMLSecurityKey($assertionAlgorithm, []);
            $xmlSecKeyAssertion->loadKey($decryptedToken);
            $dataNodeList = $samlResponse->queryEncryptedAssertion($samlEncDataPath);
            $xmlSecEncAssertion->type = XMLSecEnc::Element;
            $xmlSecEncAssertion->setNode($dataNodeList->item(0));
            $decryptedAssertion = $xmlSecEncAssertion->decryptNode($xmlSecKeyAssertion, false);

            // Replace the assertion data in the main xml document for further processing of attributes coming from IDP
            // No need to look for assertion details here after decryption since this gets validated in the later stage
            $assertionNodeList = $samlResponse->queryEncryptedAssertion($samlEncAssertionPath);
            $assertionOldNode = $assertionNodeList->item(0);
            $newNodeDoc = new DOMDocument();
            $newNodeDoc->loadXML($decryptedAssertion);
            $assertionNode = $newNodeDoc->childNodes->item(0);
            $assertionNewNode = $samlResponse->document->importNode($assertionNode, true);
            $assertionOldNode->parentNode->replaceChild($assertionNewNode, $assertionOldNode);

            return true;
        }

        return false;
    }

    /**
     * Get the IDP certificate for cstools SSO assertion
     *
     * @return string IDP certificate
     */
    private function getCSToolsIdpCertificate()
    {
        $cstoolsSsoConfig = $this->getCSToolsSSOConfig();
        $idpCertificateFileName = $cstoolsSsoConfig['IDP_CERTIFICATE'];
        if (!empty($idpCertificateFileName)) {
            if (Globals::$g->islive) {
                $idpCertificateFullPath = SYSETCDIR . $idpCertificateFileName;
            } else {
                $idpCertificateFullPath = ETCDIR . $idpCertificateFileName;
            }
            if (file_exists($idpCertificateFullPath)) {
                $idpCertificateDetails = file_get_contents($idpCertificateFullPath);
                if (!empty($idpCertificateDetails)) {
                    return $idpCertificateDetails;
                } else {
                    LogToFile("CSTools SSO Error: CSTools Idp Certificate $idpCertificateFullPath is empty");
                }
            } else {
                LogToFile("CSTools SSO Error: CSTools Idp Certificate $idpCertificateFullPath missing");
            }
        } else {
            LogToFile('CSTools SSO Error: Configuration missing in ia_init.cfg - CSTOOLS_IDP_CERTIFICATE');
        }

        return '';
    }

    /**
     * Fetch IDP home page url from the CFG file
     *
     * @param string $idpType IDP Type
     *
     * @return string IDP home page URL
     */
    public function getIDPHomePageUrl(string $idpType)
    {
        $ssoConfig = $this->getCSToolsSSOConfig($idpType);
        if ( empty($ssoConfig['IDP_HOMEPAGE_URL']) ) {
            LogToFile("CSTools SSO Error: CSTools IDP homepage url missing in ia_init.cfg - IDP_HOMEPAGE_URL ");

            return '';
        }
        return $ssoConfig['IDP_HOMEPAGE_URL'];
    }

    /**
     * Generate secret cookie, set the same in browser and store in db
     *
     * @return bool True if success else False
     */
    private function generateAndStoreCookie()
    {
        $randomString = bin2hex(openssl_random_pseudo_bytes(16));
        $authCookie = TwoWayEncryptWithKey($this->userRecordNo . "-" . $randomString, "IA_INIT");
        if ( $authCookie !== false ) {
            setcookie(
                self::TOOLS_AUTH_COOKIE_NAME,
                $authCookie,
                time() + self::TOOLS_AUTH_COOKIE_EXPIRY_TIME,
                RootPath(),
                "",
                true,
                true
            );
            $arrSqlUpdate = array("update csaccl set string=:1 where record#=:2", $randomString, $this->userRecordNo);
            return ExecStmt($arrSqlUpdate, 1, GetCSConnection());
        } else {
            return false;
        }
    }

    /**
     * Do cstools authentication - first check cookie check and then username + password
     *
     * @param string|null $p_mck
     *
     * @return bool True if success else False
     * @throws Exception
     */
    public function doAuthentication($p_mck) {
        if ($this->alreadyAuthenticated) {
            return true;
        }

        if ($this->captureAuthDetails()) {
            if (isset($this->userName) and !$this->isSSOUser($this->userName) and $this->validateLoginCredentials()) {
                // Authentication through username and password
                // Store cookie for successive authentication
                $this->alreadyAuthenticated = $this->generateAndStoreCookie();
            } else if ($this->validateAuthCookie()) {
                // Authentication through cookie
                $this->alreadyAuthenticated = true;
            } else if ($this->validateSSOLogin()) {
                $this->alreadyAuthenticated = $this->generateAndStoreCookie();
            } else if ($this->validateToolAccessToken()) {
                $this->alreadyAuthenticated = true;
            }
        }

        if ($p_mck !== null and strlen($p_mck) > 0) {
            Request::FilterRequest();
            $this->alreadyAuthenticated = $this->podAuthentication($p_mck);
        }

        return $this->alreadyAuthenticated;
    }

    /**
     * Capture the authentication details to validate the cstools user
     *
     * @return bool True if success else False
     */
    private function captureAuthDetails()
    {
        $this->userName = Request::$r->_username;
        $this->userPassword = Request::$r->_password;
        $this->authCookieValue = $_COOKIE[self::TOOLS_AUTH_COOKIE_NAME] ?? null;
        $this->responseSaml = Request::$r->SAMLResponse;
        $this->toolAccessToken = Request::$r->_cstoken;
        // ovi - need to keep this check;
        // users in prod do not have a password and we don't want anyone to login without giving a password or use SSO/cookie
        if ($this->userName != '' && $this->userPassword != '') {
            return true;
        } elseif ($this->authCookieValue != '') {
            return true;
        } elseif ($this->responseSaml != '') {
            return true;
        } elseif ($this->userName != '' && $this->toolAccessToken != '') {
            // Restrict the post of token through URL, always post param expected
            if ($_GET['_cstoken'] != '') {
                $_GET['_cstoken'] = Request::$r->_cstoken = '';
                return false;
            }
            return true;
        }

        return false;
    }

    /**
     * Remove authentication details
     *
     * @return bool True if success else False
     */
    public function clearAuthentication() {
        $sql = "UPDATE csaccl SET string = :1 WHERE record# = :2";
        $result = ExecStmt(array($sql, "0", $this->userRecordNo),  true, GetCSConnection());
        if ($result === false) {
            return false;
        }

        return setcookie(self::TOOLS_AUTH_COOKIE_NAME, "", 1, RootPath(), "", true, true);
    }

    /**
     * Validate token if available to support third party scripts.
     * Token is accepted only when following conditions are met:
     * - Must be curl call and token usage in the browser not allowed.
     * - Specific tools are only allowed, one at the moment RecalcPRbatch tool
     * - Token access restricted to allowed IP adresses
     * - Token not expired
     *
     * @return bool True if valid else False
     */
    // TODO ovi - is this still used? does it have to be a special case?
    private function validateToolAccessToken()
    {
        $tokensAcceptedTools = array('recalcprbatch.phtml');
        $currentTool = basename($_SERVER['SCRIPT_NAME']);
        if ( !empty($this->toolAccessToken) ) {
            // Only Curl call allowed to use token
            $callThroughCurl = stristr($_SERVER['HTTP_USER_AGENT'], "curl") ? true : false;
            if ( $callThroughCurl && ValidateAccess() && in_array($currentTool, $tokensAcceptedTools)) {
                $csacclMgr = Globals::$g->gManagerFactory->getManager('csaccl');
                return $csacclMgr->validAccessToken($this->toolAccessToken);
            }
        }
        return false;
    }

    /**
     * Update secret cookie if you have it already, set the same in browser and store in db
     *
     * @param string $authCookie   Authentication cookie
     * @param string $randomString Cookie secret random string
     * @param string $userRecordNo Cstools user record number
     *
     * @return bool True if success else False
     */
    private function updateAndStoreCookie(string $authCookie, string $randomString, string $userRecordNo) : bool
    {
        setcookie(
            self::TOOLS_AUTH_COOKIE_NAME,
            $authCookie,
            time() + self::TOOLS_AUTH_COOKIE_EXPIRY_TIME,
            RootPath(),
            "",
            true,
            true
        );

        return ExecStmt(
            ['update csaccl set string=:1 where record#=:2', $randomString, $userRecordNo],
            1,
            GetCSConnection()
        );
    }

    /**
     * @param string $memCacheKey
     *
     * @return bool
     * @throws Exception
     */
    private function podAuthentication(string $memCacheKey) : bool
    {
        $memCacheClient = CacheClient::getInstance();

        if ($memCacheClient) {
            $cookieValue = $memCacheClient->get($memCacheKey);
            if (!$cookieValue) {
                logToFileError("MemCache access for key $memCacheKey failed (" . __FILE__.':'.__LINE__ . ")");
                return false;
            } else {
                $mcValueNotDeleted = !$memCacheClient->delete($memCacheKey);
                if ($mcValueNotDeleted) {
                    logToFileError("Deletion of MemCache value for key $memCacheKey failed (" . __FILE__.':'.__LINE__ . ")");
                    return false;
                } else {
                    $cookieValueParts = explode('-',  TwoWayDecryptWithKey($cookieValue, "IA_INIT"));
                    $updateStatus = $this->updateAndStoreCookie($cookieValue, $cookieValueParts[1], $cookieValueParts[0]);
                    $memCacheDoneKey = "done_$memCacheKey";
                    $doneValue = $memCacheClient->get($memCacheDoneKey);
                    if (!$doneValue || !$updateStatus) {
                        logToFileError("MemCache access for key $memCacheDoneKey failed (" . __FILE__.':'.__LINE__ . ")");
                        return false;
                    } else {
                        $mcValueNotDeleted = !$memCacheClient->delete($memCacheDoneKey);
                        if ($mcValueNotDeleted) {
                            logToFileError("Deletion of Memcache value for key $memCacheDoneKey failed (" . __FILE__.':'.__LINE__ . ")");
                            return false;
                        } else {
                            // Compute the redirect URL
                            $doneValue = getRedirectUrl($doneValue);
                            Redirect($doneValue);
                        }
                    }
                }
            }
        } else {
            logToFileError("MemCache server not available (" . __FILE__.':'.__LINE__ . ")");
            return false;
        }

        return true;
    }

    /**
     * Retrieve the cstools username
     *
     * @return string Username
     */
    public function getUserName()
    {
        return $this->userName;
    }

    /**
     * Retrieve the cstools user record number
     *
     * @return int user's record#
     */
    public function getUserRecordNo()
    {
        return $this->userRecordNo;
    }

    /**
     * Check if cstools auth cookie exists or not
     *
     * @return bool True if exists else False
     */
    public function existsAuthCookie()
    {
        return $this->authCookieValue ? true : false;
    }

    /**
     * Check if authentication happens using password or not OR
     * Check if the authentication happens using SSO
     * If above case then redirect to .done if necessary
     *
     * @return bool True if exists else False
     */
    public function redirectionTobeDone()
    {
        return $this->authUsingPassword || $this->authUsingSso;
    }

    /**
     * Override to stop from logging password fields in the log
     *
     * @return string
     */
    public function __toString()
    {
        return "Authentication details: " . $this->userName;
    }
}

// Auto initialization
ToolsAuthentication::getInstance();
