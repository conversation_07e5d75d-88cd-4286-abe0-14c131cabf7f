{"$schema": "http://json-schema.org/draft-07/schema", "title": "Intacct Application Schema for Language Resource Files", "type": "array", "items": {"type": "object", "properties": {"id": {"title": "ID", "type": "string", "pattern": "^[\\w\\-]+(?:\\.[\\w\\-]+)*$", "minLength": 1, "maxLength": 100}, "value": {"title": "Original Value", "type": "string", "minLength": 0}, "markdown": {"title": "<PERSON><PERSON>", "type": "boolean", "default": false}}, "required": ["id", "value"], "additionalProperties": false}}