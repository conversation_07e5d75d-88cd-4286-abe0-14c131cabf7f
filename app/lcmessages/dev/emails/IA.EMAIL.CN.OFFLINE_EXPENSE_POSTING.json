[{"id": "IA.EMAIL.CN.OFFLINE_EXPENSE_POSTING.SUBJECT.TEXT", "value": "Post expense results for ${JOURNAL_NAME} (Total: ${TOTAL_COUNT}, Successful: ${SUCCESS_COUNT}, Errors: ${ERROR_COUNT})"}, {"id": "IA.EMAIL.CN.OFFLINE_EXPENSE_POSTING.BODY.START", "value": "Dear ${FIRST_NAME},<br><br>"}, {"id": "IA.EMAIL.CN.OFFLINE_EXPENSE_POSTING.BODY.COMPANYINFO", "value": "Company: ${COMPANY_NAME}<br>Entity: ${ENTITY_NAME}<br>"}, {"id": "IA.EMAIL.CN.OFFLINE_EXPENSE_POSTING.BODY.RUNSTATS", "value": "Total number of expense schedule entries processed: ${TOTAL_COUNT}<br>Number of expense schedule entries successfully posted: ${SUCCESS_COUNT}<br>Number of expense schedule entries with errors: ${ERROR_COUNT}<br>"}, {"id": "IA.EMAIL.CN.OFFLINE_EXPENSE_POSTING.BODY.RUNLINK", "value": "<a href=${RUN_URL}>View bulk action run</a><br>"}, {"id": "IA.EMAIL.CN.OFFLINE_EXPENSE_POSTING.BODY.END", "value": "<p>Thank you,<br>Sage Intacct support team</p>"}]