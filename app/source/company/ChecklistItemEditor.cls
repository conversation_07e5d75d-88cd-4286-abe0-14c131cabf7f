<?php

/**
 * Class ChecklistItemEditor
 * Editor class for the Checklist Item
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
class ChecklistItemEditor extends FormEditor
{
    
    /**
     * @param array $_params default editor parameters
     */
    public function __construct($_params = array())
    {
        parent::__construct($_params);
    }

    
     /**
     * getJavaScriptFileNames
     * 
     * @return string[] $jsfiles
     */
    protected function getJavaScriptFileNames()
    {
        $jsfiles[] = "../resources/js/contract.js";
        return $jsfiles;
    }

    /**
     * Make name read-only.
     *
     * @param array $obj
     *
     * @return bool
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        if (Editor_ShowEditState === $this->state) {
            $view = $this->getView();
            $view->findAndSetProperty(
                array('path' => 'NAME'), array('readonly' => true),
                EditorComponentFactory::TYPE_FIELD
            );
        }
        $ok = parent::mediateDataAndMetadata($obj);

        return $ok;
    }


}
