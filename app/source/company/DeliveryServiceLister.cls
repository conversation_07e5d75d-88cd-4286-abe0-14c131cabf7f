<?php

/**
 * Lister class for the DeliveryService object
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

/**
 * Lister class for DeliveryService object
 */
class DeliveryServiceLister extends NLister
{
    /** @var array $dsTokens */
    protected array $dsTokens = [
        ['id' => 'IA.HTTP'],
        ['id' => 'IA.DROPBOX'],
        ['id' => 'IA.GOOGLE_DRIVE'],
        ['id' => 'IA.BOX'],
        ['id' => 'IA.AMAZON_S3'],
        ['id' => 'IA.ONEDRIVE'],
        ['id' => 'IA.AZURE_STORAGE'],
        ['id' => 'IA.CLOUD_STORAGE_TARGETS'],
        ['id' => 'IA.SUCCESSFUL'],
        ['id' => 'IA.ERROR'],
        ['id' => 'IA.DELIVERY_ERROR'],
        ['id' => 'IA.NO_DELIVERIES_REQUESTED_YET']
    ];
    public function __construct()
    {
        $fld = array('NAME', 'TYPE', 'LASTDELIVERY', 'WHENDELIVERED');

        parent::__construct(
            array(
                'entity' => 'deliveryservice',
                'title' => 'IA.CLOUD_STORAGE_TARGETS',
                'fields' => $fld,
                // depending on lister some fields might not need to be encoded,
                // because they are used as a placeholder for a url link
                'nonencodedfields' => array('RECORD_URL')
            )
        );
        I18N::addTokens($this->dsTokens);
        I18N::getText();
    }

    function BuildTable() 
    {

        parent::BuildTable();

        $table = &$this->table;
        
        foreach( $table as $i => $rec) {
            $lastDelivery = $rec['LASTDELIVERY'];

            if ($lastDelivery == 'Successful') {
                $table[$i]['LASTDELIVERY'] = I18N::getSingleToken('IA.SUCCESSFUL');
            } else if ($lastDelivery == 'No deliveries requested yet') {
                $table[$i]['LASTDELIVERY'] = I18N::getSingleToken('IA.NO_DELIVERIES_REQUESTED_YET');
            } else {
                $table[$i]['LASTDELIVERY'] = I18N::getSingleToken('IA.DELIVERY_ERROR');
            }

            $type = $rec['TYPE'];

            if ($type == 'GoogleDrive') {
                $table[$i]['TYPE'] = I18N::getSingleToken('IA.GOOGLE_DRIVE');
            } else if ($type == 'Aws') {
                $table[$i]['TYPE'] = I18N::getSingleToken('IA.AMAZON_S3');
            } else if ($type == 'AzureStorage') {
                $table[$i]['TYPE'] = I18N::getSingleToken('IA.AZURE_STORAGE');
            } else if ($type == 'HTTP') {
                $table[$i]['TYPE'] = I18N::getSingleToken('IA.HTTP');
            } else if ($type == 'Dropbox') {
                $table[$i]['TYPE'] = I18N::getSingleToken('IA.DROPBOX');
            } else if ($type == 'Box') {
                $table[$i]['TYPE'] = I18N::getSingleToken('IA.BOX');
            } else if ($type == 'OneDrive') {
                $table[$i]['TYPE'] = I18N::getSingleToken('IA.ONEDRIVE');
            }
            
            switch($rec['STATE']) {
                case 'Successfull':
                    $table[$i]['STATE'] = I18N::getSingleToken('IA.SUCCESSFUL');
                    break;
                    
                case 'Error':
                    $table[$i]['STATE'] = I18N::getSingleToken('IA.ERROR');
                    break;
            }
        }
    }
}
