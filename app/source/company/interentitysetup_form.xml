<?xml version="1.0" encoding="ISO-8859-1"?>
<ROOT>
    <entity>interentitysetup</entity>
    <title>IA.INTER_ENTITY_ACCOUNT_MAPPING</title>
    <view system="true">
        <pages id="InterEntityPageList">
            <page id="mainPage">
                <section title="IA.IET_ACCOUNT_MAPPING_PLAN" isCollapsible="false" id="defaultTab">
                    <subsection className="subSection noborder">
                        <field fullname="IA.BASIC" path="DUMMYHDRBASIC" isHTML="1" className="textlabel" readonly="true">
                            <default>IA.WITH_THE_BASIC_INTER_ENTITY_MAPPING_PLAN_YOU</default>
                        </field>
                    </subsection>
                    <subsection>
                        <field hidden="true">
                            <path>INCLUDEINACTIVE</path>
                            <events>
                                <click>includeInactive(this.meta)</click>
                            </events>
                        </field>
                    </subsection>
                    <subsection>
                        <sentencerow>
                            <button id="overrideImport">
                                <name>IA.IMPORT</name>
                                <events>
                                    <click>importData()</click>
                                </events>
                            </button>
                            <button id="overrideExport">
                                <name>IA.EXPORT_TEMPLATE</name>
                                <events>
                                    <click>exportData('ENTITYACCTDEFAULTS')</click>
                                </events>
                            </button>
                        </sentencerow>
                    </subsection>
                    <grid noDragDrop="true">
                        <path>ENTITYACCTDEFAULTS</path>
                        <column>
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="ENTITYID">
                                    <path>SEARCH_ENTITYID</path>
                                </field>
                            </gridHeading>
                            <field sortable="true">
                                <path>ENTITYID</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="IERECEIVABLEACCTNO">
                                    <path>SEARCH_IERECEIVABLEACCTNO</path>
                                </field>
                            </gridHeading>
                            <field sortable="true">
                                <path>IERECEIVABLEACCTNO</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="IEPAYABLEACCTNO">
                                    <path>SEARCH_IEPAYABLEACCTNO</path>
                                </field>
                            </gridHeading>
                            <field sortable="true">
                                <path>IEPAYABLEACCTNO</path>
                            </field>
                        </column>
                        <column>
                            <field hidden="true">
                                <path>RECORDNO</path>
                            </field>
                        </column>
                    </grid>
                </section>

                <section title="IA.IET_ACCOUNT_MAPPING_PLAN" isCollapsible="false" id="overrideTab">
                    <subsection className="subSection noborder">
                        <field fullname="IA.ADVANCED" path="DUMMYHDRADV" isHTML="1" className="textlabel" readonly="true">
                            <default>IA.WITH_THE_ADVANCED_INTER_ENTITY_MAPPING_PLAN_YOU</default>
                        </field>
                    </subsection>
                    <subsection>
                        <field hidden="true">
                            <path>INCLUDEINACTIVE</path>
                            <events>
                                <click>includeInactive(this.meta)</click>
                            </events>
                        </field>
                    </subsection>
                    <subsection>
                        <sentencerow>
                            <button id="overrideImport">
                                <name>IA.IMPORT</name>
                                <events>
                                    <click>importData()</click>
                                </events>
                            </button>
                            <button id="overrideExport">
                                <name>IA.EXPORT_TEMPLATE</name>
                                <events>
                                    <click>exportData('ENTITYACCTOVERRIDES')</click>
                                </events>
                            </button>
                        </sentencerow>
                    </subsection>
                    <grid noDragDrop="true">
                        <path>ENTITYACCTOVERRIDES</path>
                        <column>
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="ENTITYA">
                                    <path>SEARCH_ENTITYA</path>
                                </field>
                            </gridHeading>
                            <field required="true" sortable="true">
                                <path>ENTITYA</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="ENTITYB">
                                    <path>SEARCH_ENTITYB</path>
                                </field>
                            </gridHeading>
                            <field required="true" sortable="true">
                                <path>ENTITYB</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="ENTITYAIER">
                                    <path>SEARCH_ENTITYAIER</path>
                                </field>
                            </gridHeading>
                            <field required="true" sortable="true">
                                <path>ENTITYAIER</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="ENTITYAIEP">
                                    <path>SEARCH_ENTITYAIEP</path>
                                </field>
                            </gridHeading>
                            <field required="true" sortable="true">
                                <path>ENTITYAIEP</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="ENTITYBIER">
                                    <path>SEARCH_ENTITYBIER</path>
                                </field>
                            </gridHeading>
                            <field required="true" sortable="true">
                                <path>ENTITYBIER</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="ENTITYBIEP">
                                    <path>SEARCH_ENTITYBIEP</path>
                                </field>
                            </gridHeading>
                            <field required="true" sortable="true">
                                <path>ENTITYBIEP</path>
                            </field>
                        </column>
                    </grid>
                </section>
            </page>
        </pages>
    </view>
</ROOT>
