<?

/**
 * Class ExchangeRateLister
 */
class ExchangeRateLister extends NLister
{
    /** @var string $basecurr */
    protected $basecurr;
    
    /**
     * Exchange rate specific tokens
     * @var string[]
     */
    protected $additionalTokens = [];

    public function __construct()
    {

        if (IsMCMESubscribed()) {
            $fields = ['TYPENAME', 'FROM_CURRENCY', 'TO_CURRENCY'];
        } else {
            $fields = ['TYPENAME', 'FROM_CURRENCY'];
        }
        $params =
            [
                'entity' => 'exchangerate',
                'title' => 'IA.EXCHANGE_RATES',
                'helpfile' => 'Viewing_and_Managing_an_Exchange_Rate_List',
                'fields' => $fields,
                'importtype' => 'exchangerates',
                'importperm' => 'co/lists/exchangerate/create',
                'importbuttonname' => 'IA.IMPORT_EXCHANGE_RATES',
            ];

        global $gManagerFactory;
        $mgr = $gManagerFactory->getManager('exchangeratetypes');
        $ratetypes = $mgr->GetUserDefinedExchangeRateTypes();
        $disableadd = (is_array($ratetypes['ID']) && count($ratetypes['ID']) == 0);

        if ($disableadd === true) {
            $params['disableadd'] = true;
        }

        $this->basecurr = GetBaseCurrency();
        parent::__construct($params);
    }


    /**
     * @param int $i
     * @return string
     */
    function calcEditUrl($i)
    {
        if ($this->basecurr != '' && $this->table[$i]['TO_CURRENCY'] != '' && $this->table[$i]['TO_CURRENCY'] != $this->basecurr) {
            $this->noedit = true;
        } else {
            $this->noedit = false;
        }
        return parent::calcEditUrl($i);
    }


    /**
     * @param int $i
     * @param string  $owner
     * @param string  $ownerloc
     * @param string  $ownedObj
     * @return bool|string
     */
    function calcDeleteUrl($i, $owner = null, $ownerloc = null, $ownedObj = null)
    {
        if ($this->basecurr != '' && $this->table[$i]['TO_CURRENCY'] != '' && $this->table[$i]['TO_CURRENCY'] != $this->basecurr) {
            return false;
        }
        return parent::calcDeleteUrl($i);
    }

}


