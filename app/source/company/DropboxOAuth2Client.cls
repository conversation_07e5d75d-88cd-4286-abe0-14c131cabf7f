<?php

/**
 * Class used to address updates from dropbox, using refresh tokens for getting the access token
 *
 * <AUTHOR> B. <<EMAIL>>
 * @copyright 2020 Intacct Corporation All, Rights Reserved
 */

use Kunnu\Dropbox\DropboxApp;
use <PERSON>nnu\Dropbox\DropboxRequest;
use <PERSON>nnu\Dropbox\DropboxClient;
use Kunnu\Dropbox\Security\RandomStringGeneratorInterface;

class DropboxOAuth2Client extends \Kunnu\Dropbox\Authentication\OAuth2Client
{
    /**
     * @var string $refreshToken
     */
    private $refreshToken;
    
    /**
     * DropboxOAuth2Client constructor.
     *
     * @param DropboxApp                          $app
     * @param DropboxClient                       $client
     * @param RandomStringGeneratorInterface|null $randStrGenerator
     */
    public function __construct(DropboxApp $app, DropboxClient $client, ?RandomStringGeneratorInterface $randStrGenerator = null)
    {
        parent::__construct($app, $client, $randStrGenerator);
    }
    
    /**
     * The refresh token can be set, to use it for revoke action if necessary
     * 
     * @param string $token
     */
    public function setRefreshToken(string $token) {
        $this->refreshToken = $token;
    }
    /**
     * Get Access Token or get access token based on refresh_token
     *
     * @param string $code        Authorization Code
     * @param string|null   $redirectUri Redirect URI used while getAuthorizationUrl
     * @param string $grant_type  Grant Type ['authorization_code']
     *
     * @return array
     * @throws \Kunnu\Dropbox\Exceptions\DropboxClientException
     */
    public function getAccessToken($code, $redirectUri = null, $grant_type = 'refresh_token'): array
    {   
        //Request Params
        if ($grant_type === 'refresh_token') {
            $params = [
                'grant_type' => $grant_type,
                'refresh_token' => $code,
                'client_id' => $this->getApp()->getClientId(),
                'client_secret' => $this->getApp()->getClientSecret()
            ];
        } else {
            $params = [
                'code' => $code,
                'grant_type' => $grant_type,
                'client_id' => $this->getApp()->getClientId(),
                'client_secret' => $this->getApp()->getClientSecret(),
                'redirect_uri' => $redirectUri
            ];
        }
    
        $params = http_build_query($params);
    
        $apiUrl = static::AUTH_TOKEN_URL;
        $uri = $apiUrl . "?" . $params;
    
        //Send Request through the DropboxClient
        //Fetch the Response (DropboxRawResponse)
        if ($grant_type === 'refresh_token') {
            $response = $this->getClient()
                ->getHttpClient() //maybe try json content type in headers
                ->send($apiUrl, 'POST', $params);
        } else {
            $response = $this->getClient()
                ->getHttpClient() //maybe try json content type in headers
                ->send($uri, 'POST', null);
        }
    
        //Fetch Response Body
        $body = $response->getBody();
    
        //Decode the Response body to associative array
        //and return
        return json_decode((string) $body, true);
    }
    
    /**
     * Disables the refresh token
     *
     * @throws \Kunnu\Dropbox\Exceptions\DropboxClientException
     */
    public function revokeRefreshToken()
    {        
        //Request
        $request = new DropboxRequest('POST', '/auth/token/revoke', $this->refreshToken);
        // Do not validate the response
        // since the /token/revoke endpoint
        // doesn't return anything in the response.
        // See: https://www.dropbox.com/developers/documentation/http/documentation#auth-token-revoke
        $request->setParams(['validateResponse' => false]);
        
        //Revoke Access Token
        $this->getClient()->sendRequest($request);
    }
}
