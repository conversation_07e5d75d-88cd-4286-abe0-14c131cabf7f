<?php

/**
 *    FILE:            SampleSetupOwnedObjAtEntityManager.cls
 *    AUTHOR:          Girish D <<EMAIL>>
 *    DESCRIPTION:     Sample Owned Object for modulepref enhancements At entity context
 *
 *    (C) 2008, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */
class SampleSetupOwnedObjAtEntityManager extends OwnedObjectManager
{

    /**
     * __construct
     *
     * @param array $params
     */
    public function __construct($params = array())
    {
        $params['moduleKey'] = Globals::$g->kAFRid;
        parent::__construct($params);
    }

    /**
     * Validate the record
     *
     * @param array &$values the data
     *
     * @return bool false if error else true
     */
    protected function ValidateRecord(&$values)
    {
        $ok = parent::ValidateRecord($values);
        $ok = $ok && $this->validateNTranslate($values);
        return $ok;
    }


    /**
     * @param array $values
     *
     * @return bool
     */
    private function validateNTranslate(&$values)
    {
        $ok = true;
        $gManagerFactory = Globals::$g->gManagerFactory;
        $gErr = Globals::$g->gErr;
        if (isset($values['JOURNALENTRY']) && $values['JOURNALENTRY'] != '') {
            $manager = $gManagerFactory->getManager('gljournal');
            $journal = explode("--", $values['JOURNALENTRY'])[0];
            $key = $manager->GetRecordNoFromVid($journal);

            if (!isset($key)) {
                $msg = _("Journal ID " . $values['JOURNALENTRY'] . " is not valid.");
                $gErr->addError('BL03000113', __FILE__ . ':' . __LINE__, $msg);
                $ok = false;
            }
            $values['JOURNALKEY'] = $key;
        } else {
            $ok = false;
        }

        if ($ok && isset($values['DEPTNO']) && $values['DEPTNO'] !== "") {
            list($dept_no) = explode("--", $values['DEPTNO']);
            $objDeptMgr = $gManagerFactory->getManager('department');
            $deprec = $objDeptMgr->GetRaw($dept_no);

            if (!isset($deprec[0]["RECORD#"]) || $deprec[0]["RECORD#"] == '') {
                $msg = _("Invalid department " . $values['DEPT_NO'] . " specified.");
                $gErr->addError('BL01001973', __FILE__ . ':' . __LINE__, $msg);
                $ok = false;
            }

            $values['DEPTKEY'] = $deprec[0]["RECORD#"];
        }

        if ($ok && $values['GLACCOUNTNO'] != '') {
            $glacctmgr = $gManagerFactory->getManager('glaccount');
            list($glacct_no) = explode('--', $values['GLACCOUNTNO']);

            $glaccount = $glacctmgr->GetRaw($glacct_no);
            if (!isset($glaccount[0]) || $glaccount[0] == '') {
                $gErr->addError(
                    "BL03000007", __FILE__ . ':' . __LINE__, "Please enter the valid GL Account No"
                );
                $ok = false;
            }
            $values['ACCOUNTKEY'] = $glaccount[0]['RECORD#'];
        }
        $values['MODULEKEY'] = Globals::$g->kAFRid;

        return $ok;
    }
}
