<?php
/**
 * Lister class for Affiliateentity
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2024 Sage Intacct Corporation All, Rights Reserved
 */

/**
 * Class AffiliateEntityLister extends NLister
 *
 * Lister class for Affiliateentity
 */

class AffiliateEntityLister extends NLister
{
    /**
     * AffiliateEntityLister specific tokens
     * @var string[]
     */
    protected $additionalTokens = [];

    public function __construct()
    {
        parent::__construct(
            [
                'entity'=>  'affiliateentity',
                'title' =>  'IA.AFFILIATE_ENTITIES',
                'fields' => ['AFFILIATEENTITYID','NAME','STATUS'],
                'nonencodedfields' => ['AFFILIATEENTITYID']
            ]
        );
       
    }

    /**
     * BuildQuerySpec new method
     *
     * @return array $querySpec
     */
    function BuildQuerySpec(): array
    {   
        $querySpec = parent::BuildQuerySpec();
        $gManagerFactory = Globals::$g->gManagerFactory;

        $dimComp = Request::$r->_dimComp;
        if ( isset($dimComp) && $dimComp!='' ) {
            if($this->showPrivate ) {
                SetReportViewContext();
            }
            $dimCompMgr = $gManagerFactory->getManager('glacctgrp');
            $members = $dimCompMgr->getDimensionMembers($dimComp);
            if ( !count($members) ) {
                $members = array('9999999');
            }
            $querySpec['filters'][0][] = array('RECORDNO', 'IN', $members);
        }

        // when it comes from group
        $grpId = Request::$r->_groupid;
        if ( isset($grpId) && $grpId!='' && $grpId!='None' ) {
            if($this->showPrivate ) {
                SetReportViewContext();
            }
            $groupMgr = $gManagerFactory->getManager('affiliateentitygroup');
            $members = $groupMgr->getGroupMembersById($grpId, false);
            $querySpec['filters'][0][] = array('RECORDNO', 'IN', $members['MEMBERRECS']);
        }

        return $querySpec;

    }


    /**
     * genGlobs new method
     *
     * @return string $ret
     */
    function genGlobs(): string
    {
        $ret = parent::genGlobs();
        $ret .= "<g name='.groupid'>" . isl_htmlspecialchars($this->_params['groupid']) . "</g>";
        $ret .= "<g name='.dimComp'>" . isl_htmlspecialchars(Request::$r->_dimComp) . "</g>";
        return $ret;
    }

    /**
     * getMoreTags new method
     *
     * @return string $tags
     */
    protected function getMoreTags(): string
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $grpMgr = $gManagerFactory->getManager('affiliateentitygroup');

        $params = array(
            'selects' => array('ID'),
            'orders' => array(array('ID')),
        );

        $lists = $grpMgr->GetList($params);
        $grps = [GT($this->textMap, 'IA.NONE')];
        foreach($lists as $grp) {
            $grps[] = $grp['ID'];
        }

        $groupsMenu = implode('~~', $grps);
        $grpid = $this->_params['groupid'] ?: 'None';

        $tags = "<groupsMenu>$groupsMenu</groupsMenu>\n
                <grpid>$grpid</grpid>\n";

        return $tags;
    }

    /**
     * CalcParams new method
     *
     * @param array $_params
     *
     * @return array $_params
     */
    function CalcParams($_params): array
    {
        $_params = parent::CalcParams($_params);
        $_params['groupid'] = Request::$r->_groupid;

        return $_params;
    }

}


