<?
//===========================================================================
//	FILE:			IMSPackageDetailManager.cls
//	AUTHOR:			<PERSON>
//	DESCRIPTION:
//
//	(C) 2000, Intacct Corporation, All Rights Reserved
//
//	This document contains trade secret data that belongs to Intacct
//	Corporation and is protected by the copyright laws.  Information
//	herein may not be used, copied or disclosed in whole or in part
//	without prior written consent from Intacct Corporation.
//===========================================================================

class IMSPackageDetailManager extends EntityManager
{
    const IMSPD_DUMMY = 'DUMMY';
    const IMSPD_PACKAGEID = 'PACKAGEID';
    const IMSPD_DELIVERY = 'DELIVERY';
    const IMSPD_PURGE = 'PURGE';
    const IMSPD_QUEUE = 'QUEUE';
    const IMSPD_IMSQUEUEKEY = 'IMSQUEUEKEY';
    const IMSPD_SENDER = 'SENDER';
    const IMSPD_RECIPIENT = 'RECIPIENT';
    const IMSPD_TOPIC = 'TOPIC';
    const IMSPD_STATE = 'STATE';
    const IMSPD_CONTEXT = 'CONTEXT';
    const IMSPD_CNY = 'CNY#';
    const IMSPD_USER = 'LOGINID';
    const IMSPD_SANDBOX = 'SANDBOX';
    const IMSPD_PRIORITY = 'PRIORITY';
    const IMSPD_TIMESTAMP_DATE = 'TIMESTAMP_DATE';
    const IMSPD_TIMESTARTED = 'TIMESTARTED';
    const IMSPD_TIMEFINISHED = 'TIMEFINISHED';
    const IMSPD_WEBSERVER = 'WEBSERVER';
    const IMSPD_TRACKINGID = 'TRACKINGID';
    const IMSPD_PARENTTRACKINGID = 'PARENTTRACKINGID';
    const IMSPD_PROCESSID = 'PROCESSID';
    const IMSPD_INPERMANENTQUEUE = 'INPERMANENTQUEUE';
    const IMSPD_TIMEINQUEUE = 'TIMEINQUEUE';
    const IMSPD_TIMEINTRANSIT = 'TIMEINTRANSIT';
    const IMSPD_TYPE = 'TYPE';
    const IMSPD_PACKAGE_DETAILS = 'PACKAGE_DETAILS';
    const IMSPD_PACKAGE_ACTION = 'PACKAGE_ACTION';
    const IMSPD_DB = 'DB';
}
