<?php

class ExpenseFileUploadQueueManager extends FIFODispatcherQueueManager
{
    const EXPENSEFILEUPLOAD = 'EXPENSEFILEUPLOAD';
    const EXPENSEJOB_QUEUED = 'Queued';
    const EXPENSEJOB_SUCCESS = 'Success';
    const EXPENSEJOB_SKIP = 'Skipped';

    const EXPENSEJOB_FAILED = 'Failed';
    const FILE = 'File';
    const DRAFT = 'Draft';

    const ITERATION = 'iteration';
    const DELAY = 'delay';
    const STATUS = 'status';
    const LATEST_REC = 'latest_job';
    const RECORDNO = 'RECORDNO';
    const OBJECTRECID = 'OBJECTRECID';
    const RESOURCETYPE = 'RESOURCETYPE';
    const FILEID = 'FILEID';

    const SKIPPED_STATE = 'S';

    public function addJobsInQueue(): bool
    {
        $ok = parent::addJobsInQueue();
        return $ok;
    }

    /**
     * @param array $job
     * @param bool $includeUserRecord
     * @param bool $includeLocation
     * @param bool $includeDepartment
     * @return bool
     */
    public function addJobInQueue($job, bool $includeUserRecord = true, bool $includeLocation = true, bool $includeDepartment = true): bool
    {
        $ok = false;
        if ($this->completeAndValidateJob($job, $includeUserRecord, $includeLocation, $includeDepartment)) {
            $ok = parent::add($job);
            if (!$ok) {
                logToFileCritical(self::LOG_PREFIX . "($this->_entity) - Could not add job in queue.");
            }
        }
        return $ok;
    }

    /**
     * @param string $ID
     * @param string[]|null $fields
     *
     * @return array|false
     *
     */
    public function get($ID, $fields = null)
    {
        $job = parent::get($ID, $fields);

        return $job;
    }

    /**
     * @param int $recordno
     * @param array|null $fields
     *
     * @return array|null
     */
    public function GetByRecordNo($recordno, $fields = null): ?array
    {
        $job = parent::GetByRecordNo($recordno, $fields);

        return $job;
    }

    /**
     * @param array $params
     * @param bool $_crosscny
     * @param bool $nocount
     *
     * @return array[]
     */
    public function GetList($params = [], $_crosscny = false, $nocount = true): array
    {
        $jobs = parent::GetList($params, $_crosscny, $nocount);

        return $jobs;
    }

}