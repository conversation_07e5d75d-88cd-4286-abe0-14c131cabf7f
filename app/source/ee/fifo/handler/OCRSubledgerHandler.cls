<?php
//=============================================================================
//
//	FILE:			 OCRSubledgerHandler.cls
//	<AUTHOR> Adiserla <<EMAIL>>
//	DESCRIPTION:	 OCRSubledgerHandler class
//
//
//=============================================================================

/**
 * Class OCRSubledgerHandler
 */
class OCRSubledgerHandler extends OCRHandler
{
    public const SINGLE_LINE_CONF = 'Single';

    /**
     * OCRSubledgerHandler constructor.
     *
     * @param array $expenseDraftDocDetails
     * @param array $job
     */
    public function __construct(array $expenseDraftDocDetails, array $job)
    {
        parent::__construct($expenseDraftDocDetails, $job);
    }

    /**
     * @param array $draftAvailableDoc
     * @param array $employee
     *
     * @return array
     */
    public function prepareDocumentValue(array &$draftAvailableDoc, array $employee): array
    {
        logToFileDebug('RECEIPT CONSUMER LOG - ' . __FUNCTION__ . ' Start');
        $newTrx = [];
        if (!empty($this->expenseDraftDocDetails)) {
            logToFileDebug('RECEIPT CONSUMER LOG - ' . __FUNCTION__ . ' expenseDraftDocDetails is present');

            $draftDocument = $this->prepareValueForAdd($draftAvailableDoc, $employee );

            logToFileDebug('RECEIPT CONSUMER LOG - ' . __FUNCTION__ . ' Populate dimension from Last Bill');
            //Lets now get the values from previous document
            $newTrx = $draftDocument;

        }
        logToFileDebug('RECEIPT CONSUMER LOG - ' . __FUNCTION__ . ' Draft Bill : ' . json_encode($newTrx));
        logToFileDebug('RECEIPT CONSUMER LOG - ' . __FUNCTION__ . ' End');
        return $newTrx;
    }

    /**
     * @param array $draftAvailableDoc
     *
     * @return array
     */
    public function prepareSetDocumentValue(array &$draftAvailableDoc, array $receipt): array
    {
        logToFileDebug('RECEIPT CONSUMER LOG - ' . __FUNCTION__ . ' Start');
        $newTrx = [];
        if (!empty($this->expenseDraftDocDetails)) {
            logToFileDebug('RECEIPT CONSUMER LOG - ' . __FUNCTION__ . ' expenseDraftDocDetails is present');

            $draftDocument = $this->prepareValueForUpdate($draftAvailableDoc, $receipt);

            logToFileDebug('RECEIPT CONSUMER LOG - ' . __FUNCTION__ . ' Populate dimension from Last Bill');
            //Lets now get the values from previous document
            $newTrx = $draftDocument;

        }
        logToFileDebug('RECEIPT CONSUMER LOG - ' . __FUNCTION__ . ' Draft Bill : ' . json_encode($newTrx));
        logToFileDebug('RECEIPT CONSUMER LOG - ' . __FUNCTION__ . ' End');
        return $newTrx;
    }

    /**
     * @param array $draftAvailableDoc
     * @param array $draftDocument
     */
    protected function prepareHeaderValueFromStx(array &$draftAvailableDoc, array &$draftDocument): void
    {
        $result = $this->expenseDraftDocDetails;
        if (!empty($result['results'][0])) {
        logToFileDebug('RECEIPT CONSUMER LOG - ' . __FUNCTION__ . ' Start  ');
        foreach (ExpenseFileUploadProcessor::$expenseMetaHeaders as $keyMap => $headerMap) {
            $draftDocument[$keyMap] = $this->getValueFromGetDraft($headerMap, $result['results'][0]);
        }

        $this->setDatesToDraftDocument($draftDocument);

        //Extract the currency from the File, if not provided assign the Base currency
        //TODO: Handle non existent currency scenario in R4
        $this->setCurrencyToDraftDocument($draftDocument);
        //TODO: Handle non existent currency scenario in R4
        $draftDocument['RECORDNO'] = $draftAvailableDoc['PRRECORDKEY'];
        $draftDocument['ACTION'] = SubLedgerTxnManager::DRAFT_ACTION;
        $draftDocument['SUPDOCID'] = $draftAvailableDoc['SUPDOCID'];
        $draftDocument['MODIFIEDBY'] = GetMyUserid();
        $draftDocument['DOCSOURCE'] = ExpenseFileuploadManager::EXPENSE_SOURCE[$draftAvailableDoc['SOURCE']];
        logToFileDebug('RECEIPT CONSUMER LOG - ' . __FUNCTION__ . ' End  ');
        }
    }

    /**
     * Prepares the line items value from STX lines.
     *
     * @param array $draftAvailableDoc The available draft document.
     * @param array $draftDocument The draft document to be prepared.
     * @param array $employee The employee details.
     */
    protected function prepareLineItemsValueFromStxLines(array &$draftAvailableDoc, array &$draftDocument, array $employee): void
    {
        logToFileDebug('Receipt CONSUMER LOG - ' . __FUNCTION__ . ' Start');
        $expenseDraftDocDetails = $this->expenseDraftDocDetails;

        if (!empty($employee[0]['LOCATIONKEY'])) {
            $locationDetails = $this->getLocationDetailsFromDoc($employee[0]['LOCATIONKEY']);
        } else if (!empty($draftAvailableDoc['LOCATIONKEY'])){
            $locationDetails = $this->getLocationDetailsFromDoc($draftAvailableDoc['LOCATIONKEY']);
        } else {
            $locationDetails = $this->getLocationDetailsFromDoc($employee['LOCATIONKEY']);
        }

        $this->setBaseCurrency($draftDocument, $locationDetails);

        $this->setLineItemsFromDraftResponse($draftDocument, $expenseDraftDocDetails['results'][0], $locationDetails);

        logToFileDebug('Receipt CONSUMER LOG - ' . __FUNCTION__ . ' End');
    }

    private function getLocationDetailsFromDoc(int $locationKey): array
    {
        if (!empty($locationKey)) {
            logToFileDebug('Receipt CONSUMER LOG - ' . __FUNCTION__ . ' Location Details from Locationkey ');
            return $this->getLocationDetails($locationKey);
        }
        return [];
    }

    private function setBaseCurrency(array &$draftDocument, array $locationDetails): void
    {
        if (IsMCMESubscribed() && !GetContextLocation()) {
            $draftDocument['BASECURR'] = GetBaseCurrency() ?: ($locationDetails['CURRENCY'] ?? '');
        }
    }
    
    /**
     * @param array $draftDocument
     * @param array $expenseDraftDocDetails
     * @param array $locationDetails
     */
    private function setLineItemsFromDraftResponse(array &$draftDocument, array $expenseDraftDocDetails, array $locationDetails)
    {
        $expenseLines = is_array($expenseDraftDocDetails['extraction']['line_items']) ? $expenseDraftDocDetails['extraction']['line_items'] : [];

        $expenseCategory = is_array($expenseDraftDocDetails['expense_category']) ? $expenseDraftDocDetails['expense_category'] : [];
        $vendor = is_array($expenseDraftDocDetails['extraction']['vendor']) ? $expenseDraftDocDetails['extraction']['vendor'] : [];
        $glAccount = is_array($expenseDraftDocDetails['general_ledger']) ? $expenseDraftDocDetails['general_ledger'] : [];

        $predictions = ExpenseFileUploadProcessor::$expenseMetaLines;

        logToFileDebug('Receipt COSUMER LOG - ' . __FUNCTION__ . ' Single Line Configuration');
        if (count($expenseLines) === 1) {
            foreach ($expenseLines as $keyLineItem => $expenseLine) {
                $this->setLineItemsToValues($draftDocument, $locationDetails, $keyLineItem);
                $this->prepareLineItemsFromSource($expenseLine, $keyLineItem, $predictions, $draftDocument);
            }
        } else {
            $this->setLineItemsToValues($draftDocument, $locationDetails, 0);
        }
        if (!isSpecified($draftDocument['ITEMS'][0]['TRX_AMOUNT']) || $draftDocument['ITEMS'][0]['TRX_AMOUNT'] == 0) {
            $draftDocument['ITEMS'][0]['TRX_AMOUNT'] = $draftDocument['TRX_TOTALENTERED'];
        }
        if (GetLabelStatus('ei')) {
            if (!empty($draftDocument['ITEMS'][0]) && !empty($expenseCategory[0]['category_id']['value'])) {
                $draftDocument['ITEMS'][0]['ACCOUNTLABELKEY'] = $expenseCategory[0]['category_id']['value'];
            }
            if (!empty($vendor)) {
                $draftDocument['ITEMS'][0]['DESCRIPTION'] = $vendor['name']['value'];
            }
        } else {
            if (!empty($glAccount['account'])) {
                $draftDocument['ITEMS'][0]['GLACCOUNTNO'] = $glAccount[0]['account']['value'];
            }
        }
    }

    /**
     * @param array $invoiceLine
     * @param int $keyLineItem
     * @param array $predictions
     * @param array $draftDocument
     */
    private function prepareLineItemsFromSource(array $invoiceLine, int $keyLineItem, array $predictions, array &$draftDocument): void
    {
        foreach ($predictions as $keyLines => $stxLine) {
            $draftDocument['ITEMS'][$keyLineItem][$keyLines] = $this->getValueFromGetDraft($stxLine, $invoiceLine);
            if ($keyLines === 'TOTAL_WITHOUT_TAX' &&
                isNullOrBlank($draftDocument['ITEMS'][$keyLineItem]['TRX_AMOUNT'])) {
                $draftDocument['ITEMS'][$keyLineItem]['TRX_AMOUNT'] =
                    !isNullOrBlank($draftDocument['ITEMS'][$keyLineItem][$keyLines]) ?
                        $draftDocument['ITEMS'][$keyLineItem][$keyLines] : 0;
            }
        }
    }

    /**
     * @param array $draftDocument
     * @param array $locationDetails
     * @param string $keyLineNo
     */
    protected function setLineItemsToValues(array &$draftDocument, array $locationDetails, string $keyLineNo): void
    {
        $draftDocument['ITEMS'][$keyLineNo]['LINE_NO'] = 1;
        $draftDocument['ITEMS'][$keyLineNo]['LOCATIONID'] = $locationDetails['LOCATIONID'] ?? '';
        $draftDocument['ITEMS'][$keyLineNo]['LOCATION#'] = $locationDetails['RECORD#'] ?? '';
        $draftDocument['ITEMS'][$keyLineNo]['EMPLOYEEID'] = $draftDocument['EMPLOYEEID'] ?? '';
    }
    
    /**
     * @return ElectronicReceiptsManager
     */
    public function getSourceManager()
    {
        return Globals::$g->gManagerFactory->getManager('electronicreceipts');
    }
}