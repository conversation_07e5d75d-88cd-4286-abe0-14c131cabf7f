<?php
//=============================================================================
//
//	FILE:			ExpenseFileUploadDispatcher.cls
//	<AUTHOR> Adiserla <<EMAIL>>
//	DESCRIPTION:	ExpenseFileUploadDispatcher  Class//
//
//
//=============================================================================


final class ExpenseFileUploadDispatcher extends FIFODispatcher
{
    use ExpenseWebhookTrait;

    /**
     * Return the dispatcher type (the value to store in DB, singe char)
     *
     * @return string
     */
    public function getType(): string
    {
        return FIFODispatcher::DISPATCHER_TYPES['expensefileuploadqueue'];
    }


    /**
     * @return string
     */
    public function getConfigSectionName(): string
    {
        return 'EXPENSEFILEUPLOAD_DISPATCHER';
    }

    /**
     * @param DBSchemaInfo $dbInfo
     * @return int
     */
    public function getDBBandwidth(DBSchemaInfo $dbInfo): int
    {
        return $dbInfo->getExpenseExternalWorkflowBandwidth();
    }

    /**
     * @param DBSchemaInfo $dbInfo
     * @return int
     */
    public function getDBTimeLimit(DBSchemaInfo $dbInfo): int
    {
        return $dbInfo->getExpenseExternalWorkflowTimeLimit();
    }

    /**
     * @return string
     */
    public function getControlLockPrefix(): string
    {
        return 'expenseFileUploadDispatch';
    }

}