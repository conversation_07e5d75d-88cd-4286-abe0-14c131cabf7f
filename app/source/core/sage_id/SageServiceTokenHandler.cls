<?php

require_once '../../private/lib/JwtFramework/autoload.php';

use <PERSON>\Component\Core\AlgorithmManagerFactory;
use <PERSON>\Component\Core\JWKSet;
use <PERSON>\Component\Signature\Algorithm\RS256;
use <PERSON>\Component\Signature\JWS;
use <PERSON>\Component\Signature\JWSVerifierFactory;
use Jose\Component\Signature\Serializer\CompactSerializer;
use Jose\Component\Signature\Serializer\JWSSerializerManagerFactory;

/**
 * Class SageServiceTokenHandler
 */
class SageServiceTokenHandler
{
    /** @var SageIdConfigProvider $configProvider; */
    private $configProvider;

    /**
     * @param SageIdConfigProvider $configProvider
     */
    public function __construct(SageIdConfigProvider $configProvider) {
        $this->configProvider = $configProvider;
    }
    
    /**
     * @param string $token
     *
     * @return JWS
     */
    public function unserialize(string $token) : JWS
    {
        $serializerFactory = new JWSSerializerManagerFactory();
        $serializerFactory->add(new CompactSerializer());
        $jwsSerializerManager = $serializerFactory->create(['jws_compact']);
        return $jwsSerializerManager->unserialize($token);
    }
    
    /**
     * @param JWS $jws
     *
     * @return array|null
     */
    public function getPayload(JWS $jws) : array|null
    {
        $payload = json_decode($jws->getPayload(), true);
        if ( json_last_error() !== JSON_ERROR_NONE ) {
            die(json_last_error());
        }
        return $payload;
    }
    
    /**
     * @return string|null
     */
    public function getJwks() : string|null
    {
        Util::httpCall($this->configProvider->getJwksUrl(), [], $JWKSValue, true);
        return $JWKSValue;
    }
    
    /**
     * @param JWS $jws
     *
     * @return bool
     */
    public function verifySignature(JWS $jws, string $JWKSValue) : bool
    {
        $algorithmFactory = new AlgorithmManagerFactory();
        $algorithmFactory->add('RS256', new RS256());
    
        $verifierFactory = new JWSVerifierFactory($algorithmFactory);
        $verifier = $verifierFactory->create([ 'RS256' ]);
        if ( ! $verifier->verifyWithKeySet($jws, JWKSet::createFromJson($JWKSValue), 0)) {
            return false;
        }
        return true;
    }
}
