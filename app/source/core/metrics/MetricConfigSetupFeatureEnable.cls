<?php
/**
 * Metric class for configSetupcomponent, featureEnable metric id
 *
 * <AUTHOR>
 * @copyright 2020 Intacct Corporation, All Rights Reserved
 */

/* --------------------------------------------------------------------------------------------------------------
 * DO NOT EDIT THIS FILE DIRECTLY. Update metrics_definition.json, then regenerate the file with metrics_gen.bin
 * --------------------------------------------------------------------------------------------------------------
 */
 
final class MetricConfigSetupFeatureEnable  extends IAMetrics
{

    /**
     * IAMetrics constructor.
     *
     * @param string $object
     * @param bool $isEnabled
     * @param string $sourceModule
     * @param int $location

     */
    public function __construct($object = null, $isEnabled = null, $sourceModule = null, $location = null)
    {
        parent::__construct(
            'configSetup',
            'featureEnable',
            0,
            [],
            [
                'object',
                'isEnabled',
                'sourceModule',
                'location#'
            ]
            , $object, $isEnabled, $sourceModule, $location
        );
    }

    /**
     * @return string|null
     */
    public function getObject(): ?string
    {
        return parent::getAttribute('object');
    }
    
    /**
     * @param string $object
     */
    public function setObject(string $object)
    {
        parent::setAttribute('object', $object);
    }

    /**
     * @return bool|null
     */
    public function getIsEnabled(): ?bool
    {
        return parent::getAttribute('isEnabled');
    }
    
    /**
     * @param bool $isEnabled
     */
    public function setIsEnabled(bool $isEnabled)
    {
        parent::setAttribute('isEnabled', $isEnabled);
    }

    /**
     * @return string|null
     */
    public function getSourceModule(): ?string
    {
        return parent::getAttribute('sourceModule');
    }
    
    /**
     * @param string $sourceModule
     */
    public function setSourceModule(string $sourceModule)
    {
        parent::setAttribute('sourceModule', $sourceModule);
    }

    /**
     * @return int|null
     */
    public function getLocation(): ?int
    {
        return parent::getAttribute('location#');
    }
    
    /**
     * @param int $location#
     */
    public function setLocation(int $location)
    {
        parent::setAttribute('location#', $location);
    }

}
