<?php
/**
 * Metric class for DispatcherEnginecomponent, cnyLimit metric id
 *
 * <AUTHOR>
 * @copyright 2020 Intacct Corporation, All Rights Reserved
 */

/* --------------------------------------------------------------------------------------------------------------
 * DO NOT EDIT THIS FILE DIRECTLY. Update metrics_definition.json, then regenerate the file with metrics_gen.bin
 * --------------------------------------------------------------------------------------------------------------
 */
 
final class MetricDispatcherEngineCnyLimit  extends IAMetrics
{

    /**
     * IAMetrics constructor.
     *
     * @param string $componentName the component name
     * @param string $id

     */
    public function __construct(string $componentName, $id = null)
    {
        parent::__construct(
            $componentName,
            'cnyLimit',
            0,
            [
                'jobsSkipped'
            ],
            [
                'id'
            ]
            , $id
        );
    }

    /**
     * @param int|float $quantity
     */
    public function addJobsSkipped($quantity)
    {
        parent::addQuantity('jobsSkipped', $quantity);
    }
    
    public function incrementJobsSkipped()
    {
        parent::increment('jobsSkipped');
    }
    
    public function decrementJobsSkipped()
    {
        parent::decrement('jobsSkipped');
    }

    /**
     * @return float|null
     */
    public function getJobsSkipped(): ?float
    {
        return parent::getValue('jobsSkipped');
    }
    
    /**
     * @param float $jobsSkipped
     */
    public function setJobsSkipped(float $jobsSkipped)
    {
        parent::setValue('jobsSkipped', $jobsSkipped);
    }

    /**
     * @return string|null
     */
    public function getId(): ?string
    {
        return parent::getAttribute('id');
    }
    
    /**
     * @param string $id
     */
    public function setId(string $id)
    {
        parent::setAttribute('id', $id);
    }

}
