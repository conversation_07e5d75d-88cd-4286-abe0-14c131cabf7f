<?
import('UIControl');
import('Request');
class FileUploadControl extends UIControl {

    /**
     * @param array $_params
     */
    function __construct($_params) {
		parent::__construct($_params);
		$this->_params = INTACCTarray_merge($this->_params, $this->CalcParams($_params));
	}


    /**
     * @param array $_params
     *
     * @return array
     */
    function CalcParams($_params) {
		if (!isset($_params['browseboxnum'])) { 
			$_params['browseboxnum'] = 5; 
		}

		if (!isset($_params['browseboxname'])) { 
			$_params['browseboxname'] = 'userfile'; 
		}

		return $_params;
	}


	function Show()
    {
		//$_sess = Session::getKey();
		$browseboxnum = $this->_params['browseboxnum'];
		$browseboxname = $this->_params['browseboxname'];
	
?>


If you do not see a "Browse" button, your browser does not support attachments.
<BR>

<? for ($i=1;$i<=$browseboxnum;$i++){ ?>
	<INPUT TYPE="file" NAME=".<? echo $browseboxname.$i; ?>" size="50"><BR>
<? } ?>
	<BR>
<B>Step 2:</B> Click <B>Attach File</B>.
			<INPUT class="nosavehistory" TYPE="submit" NAME=".upload" VALUE="Attach File">
			<INPUT class="nosavehistory" TYPE="button" NAME=".doclose" VALUE="Close" onclick="self.close();">
<!-- </DIV> -->
<BR>
<?
	}

}


