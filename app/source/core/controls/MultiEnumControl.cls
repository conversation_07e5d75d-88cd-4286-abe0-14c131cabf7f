<?
import('EnumControl');

class MultiEnumControl extends EnumControl
{
    /**
     * @param array $_params
     */
    function __construct($_params)
    {
        parent::__construct($_params);
        $this->_params = INTACCTarray_merge($this->_params, $this->CalcParams($_params));
    }

    /**
     * @param array $_params
     *
     * @return array
     */
    function CalcParams($_params)
    {
        $_params['size'] = $_params['size']  ?: 6;
        return $_params;
    }

    function Show() 
    {
        $size = $this->_params['size'];
        $form = $this->_params['form'];
        $layer = $this->_params['layer'];

        global $kValueDelimiter;
        $localDelim = ($this->_params['type']['delimiter']) ?: $kValueDelimiter; 

        $name = $this->_params['varname'];
        $value = $this->_params['value'];

        $value = explode($localDelim, $value);

        $onchange_js = $this->_params['onchange'];
        $onchange_js .= "MultiFieldCollect('$name', '$form', '$layer', '$localDelim');";
        ?>
     <input type=hidden name="<?= $name; ?>" value="<? echo join($localDelim, $value); ?>" size=200 >
     <select multiple size="<?= $size; ?>" name="<?= "helper_$name"; ?>"
      onChange="<?=$onchange_js?>" >
        <? ShowOptions($value, $this->_params['validvalues']); ?>
     </select>
        <?
    }

    /**
     * @param string $docType
     */
    function ShowReadOnly($docType = null)
    {
        $value = $this->_params['value'];
        $delimiter = $this->_params['type']['delimiter'];
        $selectedvalues = ($value && $value != '') ?
            explode($delimiter, $value) : array();

        $showvalues = $selectedvalues;

        $validlabels = $this->_params['type']['validlabels'];
        $validvalues = $this->_params['type']['validvalues'];

        if( is_array($validvalues) && count($validvalues) > 0 ) {
            $selectedvalues = array_intersect($validvalues, $showvalues);
        }
                
        if( ! is_array($validlabels) || count($validlabels) == 0 ) {
            $validlabels = $selectedvalues;
        }

        foreach ( $selectedvalues as $key => $sv) {
            ?><b><?=$validlabels[$key]?></b><br><?
        }
        ?><input type="hidden" name="<?=('hasselected'.$this->_params['path'])?>" value="<?=count($selectedvalues)?>"><?
    }

}

