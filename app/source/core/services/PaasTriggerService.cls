<?php

require_once 'xmlgw_router.3.0.cls';

class PaasTriggerService
{
    /**
     * @param array $request
     *
     * @return array
     * @throws APIException
     * @throws APIInternalException
     */
    public function createObject(array $request) : array
    {
        $fieldData = $request['data'];
        if ( $fieldData == null ) {
            $error = APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0205, ["PARAMETER" => 'data']);
            throw (new APIException())->setAPIError($error);
        }

        $destObjDefKey = $request['objDefKey'];
        if ( $destObjDefKey == null ) {
            $error = APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0205, ["PARAMETER" => 'objDefKey']);
            throw (new APIException())->setAPIError($error);
        }

        $res['result'] = '';
        try {
            $destObjName = Util_StandardObjectRegistry::getIdObjectMap()[$destObjDefKey];
            $manager = Globals::$g->gManagerFactory->getManager($destObjName);
            $manager->add($fieldData);
        } catch (Exception $exception) {
            $res['result'] = $exception->getMessage();
        }

        return $res;
    }

    /**
     * @param array $request
     *
     * @return array
     * @throws APIException
     * @throws APIInternalException
     */
    public function updateRelated(array $request) : array
    {
        $destObjDefKey = $request['objDefKey'];
        if ( $destObjDefKey == null ) {
            $error = APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0205, ["PARAMETER" => 'objDefKey']);
            throw (new APIException())->setAPIError($error);
        }

        $destObjName = $request['objName'];
        if ( $destObjName == null ) {
            $error = APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0205, ["PARAMETER" => 'objName']);
            throw (new APIException())->setAPIError($error);
        }

        $keys = $request['objKeys'];
        if ( $keys == null ) {
            $error = APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0205, ["PARAMETER" => 'objKeys']);
            throw (new APIException())->setAPIError($error);
        }

        $eventsKey = $request['eventDefKeys'];
        if ( $eventsKey == null ) {
            $error = APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0205, ["PARAMETER" => 'eventDefKeys']);
            throw (new APIException())->setAPIError($error);
        }

        try {
            $manager = Globals::$g->gManagerFactory->getManager($destObjName);

            foreach ($keys as $key) {
                $data = $manager->GetByRecordNo($key);
                if ($data) {
                    $objs[] = $data;
                }
            }

            foreach ($eventsKey as $eventKey) {
                $event = Pt_EventDefManager::getById($eventKey);
                if ($event) {
                    $events[] = $event;
                }
            }

            $res['result'] = '';
            $objDef = Pt_DataObjectDefManager::getById($destObjDefKey);
            if ($objDef == null) {
                $res['result'] = 'Error: Can\'t find the object definition';
            }
            $runDownstreamTriggers = !($objDef instanceof Pt_StdDataObjectDef);
            $eventRunner = new Pt_EventRunner();
            foreach ($objs as $obj) {
                $data = Pt_StdDataObjectManager::convertStdObjToCustObj($manager, $obj, $objDef);
                $eventRunner->runSelected($data, $events, $runDownstreamTriggers);
            }
        } catch (Exception $e) {
            $res['result'] = $e->getMessage();
        }

        return $res;
    }

    /**
     * @param array $request
     *
     * @return array
     * @throws APIException
     * @throws APIInternalException
     */
    public function restApi(array $request) : array
    {
        Backend_Init::Init();
        $eventDef = Pt_EventDefManager::getById($request['eventDefKey']);
        if ( $eventDef == null ) {
            $error = APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0205, ["PARAMETER" => 'eventDefKey']);
            throw (new APIException())->setAPIError($error);
        }

        $objKey = $request['objKey'];
        if ( $objKey == null ) {
            $error = APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0205, ["PARAMETER" => 'objKey']);
            throw (new APIException())->setAPIError($error);
        }

        try {
            $objDef = $eventDef->getObjectDef();
            $manager = Globals::$g->gManagerFactory->getManager($objDef->getObjectDefName());
            $objData = $manager->GetByRecordNo($objKey);
            $data = Pt_StdDataObjectManager::convertStdObjToCustObj($manager, $objData, $objDef);
            if ( $data ) {
                $intacctRestApi = Pt_RuntimeEventFactory::create($eventDef, $data, null);
                $intacctRestApi->trigger(new Pt_EventRunner(CTX_UI));
            }

            $res['result'] = '';
        } catch ( Exception $e ) {
            $res['result'] = $e->getMessage();
        }

        return $res;
    }

    /**
     * @param array $request
     *
     * @return array
     * @throws APIException
     * @throws APIInternalException
     */
    public function intacctApi(array $request) : array
    {
        Backend_Init::Init();
        $rawData = $request['rawData'];
        if ( $rawData == null ) {
            $error = APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0205, ["PARAMETER" => 'rawData']);
            throw (new APIException())->setAPIError($error);
        }

        $res['result'] = '';
        $runner = new Pt_EventRunner();
        $xmlgw = new xmlgw_router_3_0();
        $losProfile = LOSManager::getLOSProfileForCompany(GetMyCompany(), LOSProfile::LOS_PROCESS_LOG_TYPE_WEB_SERVICE, LOSProfile::LOS_CLIENT_PLATFORM);
        $throwError = false;
        $xmlgw->setLOSProfile($losProfile);
        $xmlgw->setTriggerRunner($runner);
        if( $losProfile->clientHasTransactionQuota() ) {
            if (!$xmlgw->apiTrigger($rawData)) {
                $throwError = true;
            }
        } else {
            $res['result'] = LOSManager::INSUFFICIENT_QUOTA_MESSAGE;
            return $res;
        }

        LOSManager::finalizeLOSProfile($losProfile);

        if ( $throwError ) {
            global $gErr;
            $res['result'] = $gErr->__toString();
        }

        return $res;
    }
}
