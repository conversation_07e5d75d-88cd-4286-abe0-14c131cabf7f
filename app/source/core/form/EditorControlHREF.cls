<?php

/**
 * Class EditorControlHREF
 */
class EditorControlHREF extends EditorControl
{
    /**
     * @param array       $params
     * @param EditorField $editorField
     */
    public function __construct(&$params, EditorField $editorField)
    {
        parent::__construct($params, $editorField);
    }
    
    protected function CalcParams()
    {
        parent::CalcParams();
        
        if (!isset($this->params['maxlength'])) {
            $this->params['maxlength'] = $this->params['type']['maxlength']; 
        }
        if (!isset($this->params['size'])) {
            $this->params['size'] = $this->params['maxlength'] ? min($this->params['maxlength'], 50) : 40; 
        }
    }
}
