<?php

/**
 * Class EditorControlSmartlinkFetch
 */
class EditorControlSmartlinkFetch extends EditorControlSmartlink
{
    /**
     * @param array       $params
     * @param EditorField $editorField
     */
    public function __construct(&$params, EditorField $editorField)
	{
		parent::__construct($params, $editorField);
	}

    /**
     * @return string
     */
    protected function getDetailsKey()
	{
		return 'fetch';
	}

    /**
     * @param CspPolicy  $cspPolicy
     * @param FormEditor $editor
     */
    public function setCspPolicy(CspPolicy $cspPolicy, FormEditor $editor)
    {
        $slMgr = Globals::$g->gManagerFactory->getManager('smartlink');
        $slObj = $slMgr->getFlat($this->params['type']['key']);

        $slf = new SmartLinkFetch($slObj['RECORDNO']);
        $slf->SetDocType($editor->getDocType());

        $url = $slf->Inject($slf->_target, Request::$r->GetCurrentObject());
        if ( ! isl_str_startswith($url, './') ) {
            $url = ascertainProtocol($url);
        }

        $cspPolicy->addAllowed(CspPolicy::POLICY_FRAME, $url);

    }
}