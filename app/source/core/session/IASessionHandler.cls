<?php
//=============================================================================
//
//  FILE:        IASessionHandler.cls
//  AUTHOR:      <PERSON><PERSON>
//  DESCRIPTION: This is the class that handles a session. This is a platform-level class.
//               Do not use it directly in the application. Use Session class instead.
//
//  (C)2000,2009 Intacct Corporation, All Rights Reserved
//
//  Intacct Corporation Proprietary Information.
//  This document contains trade secret data that belongs to Intacct
//  corporation and is protected by the copyright laws. Information herein
//  may not be used, copied or disclosed in whole or part without prior
//  written consent from Intacct Corporation.
//
//=============================================================================

require_once "backend_company.inc";
require_once "cpa_util.inc";

/**
 * This class handles the sessions through out the application.
 *
 * THIS CLASS IS PART OF THE APPLICATION FRAMEWORK. DO NOT USE IT DIRECTLY IN THE APPLICATION.
 *
 * <AUTHOR>
 */
class IASessionHandler
{
    
    const LOG_PREFIX = LoginManager::LOG_PREFIX . '::' . __CLASS__;
    
    const SESSION_TYPE_LOGIN = 'LOGIN';
    const SESSION_TYPE_SLIDEIN = 'SLIDEIN';
    const SESSION_TYPE_SSO = 'SSO';
    const SESSION_TYPE_REMOTECONN = 'REMOTECONN';
    const SESSION_TYPE_PORTAL = 'PORTAL';
    const SESSION_TYPE_UI_GET_SESSION = 'UI-GET-SESSION';
    const SESSION_TYPE_MFA_COMPLETION = 'MFA_COMPLETION';
    const SESSION_TYPE_IMS = 'IMS';
    const SESSION_COOKIE_DELETED_VALUE = 'deleted';
    const USER_ID_PROPERTY = '##user_id';
    const RLOC_CACHE_KEY = '##rloc_list';
    const RDEPT_CACHE_KEY = '##rdept_list';

    // session types:
    // 'IMSOID'     - IMS session for the XML API
    // 'RESTOID'    - REST session for the REST API
    // .oid cookie  - UI session
    const IMS_SESSION_KEY   = 'IMSOID';
    const REST_SESSION_KEY  = 'RESTOID';

    /**
     * An associative array to map session keys to Session Handler objects
     *
     * @var IASessionHandler[] $sessions
     */
    private static $sessions;
    /**
     * The session key (unique identifier) of this handler
     *
     * @var string $key
     */
    private $key;
    /**
     * The session oid - the cookie that associates the session with a client machine
     *
     * @var string $oid
     */
    private $oid;
    /**
     * The cached properties of the session. This array holds the properties that have been loaded/stored from/to
     * mecache in this unit of work
     *
     * @var array $properties
     */
    private $properties;
    /**
     * The handler of the profile associated with this session
     *
     * @var ProfileHandler $profileHandler
     */
    private $profileHandler;
    /**
     * The memcache client instance for this session
     *
     * @var CacheClient $cacheClient
     */
    private $cacheClient;
    /**
     * Flag to indicate if the current instance has been initialized
     *
     * @var bool $initialized
     */
    private $initialized;
    /**
     * Indicates whether session is from UI.
     *
     * @var bool $isUISession
     */
    private $isUISession;
    
    /**
     * This method returns an instance of the session handler for the specified or implicit session key.
     * The instance will be initialized unless the delayInit parameter is set to true.
     *
     * @param string $key          the session key, if no key is specified the .sess value from Request is used
     * @param string $oid
     * @param bool   $delayInit    whether to delay the initialization of this session handler
     * @param bool   $dontCreate   whether to create the instance if none exists
     * @param bool   $getInactive  whether to returned inactive sessions
     *
     * @return IASessionHandler|null    the instance of the session handler for the specified/implicit session key
     */
    public static function getInstance($key = null, $oid = null, $delayInit = false, $dontCreate = false, $getInactive = false)
    {
        // get the session key if needed and create the session if needed
        //FIXME: we should get the session key from the REQUEST structure only - if we call Request::Get() we actually get it from GLOBALS array :(
        //FIXME: what about IMS sessions?
        $_sess = Request::$r->_sess ?? null;
        if ( ! $key ) {
            $key = $_sess; //Request::$r->_sess;
        }
        if ( ! $key ) {
            return null;
        }
        if ( ! (self::$sessions[$key] ?? false) && ! $dontCreate ) {
            $sessionHandler = new IASessionHandler($key, $oid, $delayInit, $getInactive);
            if ( $delayInit || $sessionHandler->isValidSession() ) {
                self::$sessions[$key] = $sessionHandler;
            }
        }
        if ( (self::$sessions[$key] ?? false ) && ! $delayInit ) {
            $sessionHandler = self::$sessions[$key];
            $sessionHandler->init(false);
        }

        // the $oid value passed in might overwrite the default set by the app earlier on the session
        if ( $oid && (self::$sessions[$key] ?? false) ) {
            self::$sessions[$key]->oid = $oid;
        }

        return self::$sessions[$key] ?? null;
    }

    /**
     * Remove the specified session handler instance from memory.
     * This method doesn't remove the data from memory cache. To clean the
     * memory cache use self::cleanUp().
     *
     * @param string $key       the session key
     * @param string $logPrefix Used to add a prefix to the log messages
     *
     * @return IASessionHandler|null  the removed instance or null if none is found
     */
    public static function removeInstance($key = null, string $logPrefix = self::LOG_PREFIX)
    {
        LogToFile(sprintf("%s::%s:%d sess=%s\n", $logPrefix, __METHOD__, __LINE__, $key));
        // get the session key if needed
        if ( ! $key ) {
            $key = Request::$r->_sess;
        }
        if ( self::$sessions[$key] ) {
            $session = self::$sessions[$key];
            unset(self::$sessions[$key]);

            return $session;
        }

        return null;
    }

    /**
     * Setup a session object. This method grabs an existing active matching session, if any exists, or creates a new
     * session object. If valid session already exist for same cny# & userrec for same machine (oid) JOIN the existing
     * session, else CREATE a brand new session. If valid session(s) already exist for same cny# & userrec but for
     * different machine DELETE the existing session(s) and CREATE a brand new session else CREATE a brand new session
     *
     * @param string $type the type of the session
     * @param string $_userid the user id that owns the session
     * @param string $parent the key of the session parent if any
     * @param string $location the location to which this session applies
     * @param string $department the department to which this session applies
     * @param string $locgrp the location group to which this session applies
     * @param string $deptgrp the department group to which this session applies
     * @param string $sessionKey the optional sessionKey. Do not generate if provided
     * @param string $logPrefix Used to add a prefix to the log messages
     *
     * @return IASessionHandler  the session handler instance of the instance that was setup.
     */
    public static function setupSession(
        string $type,
        $_userid,
        $parent = '',
        $location = '',
        $department = '',
        $locgrp = '',
        $deptgrp = '',
        $sessionKey = '',
        string $logPrefix = self::LOG_PREFIX
    ) {
        LogToFile(
            sprintf(
                "%s::%s:%d type=%s, userid=%s, parent=%s, loc=%s, dep=%s, locGrp=%s, depGrp=%s, sessKey=%s \n",
                $logPrefix, __METHOD__, __LINE__,
                $type, $_userid, $parent, $location, $department, $locgrp, $deptgrp, $sessionKey
            )
        );
        if (empty($type)) {
            LogToFile(sprintf("%s::%s:%d empty session type '%s'\n", $logPrefix, __METHOD__, __LINE__, $type));
            $type = self::SESSION_TYPE_LOGIN;
        }
        if (!self::isValidSessionType($type)) {
            LogToFile(sprintf("%s::%s:%d invalid session type '%s'\n", $logPrefix, __METHOD__, __LINE__, $type));
        }

        $activeSessArr = self::getActiveSessions($_userid, $location, $department, $locgrp, $deptgrp, $parent);
        if ( ! $activeSessArr ) {
            $sessionHandler = self::createNewSession($type, $_userid, $parent, $location, $department, $locgrp, $deptgrp, $sessionKey);
        } else {
            $killSessArr = [];
            self::processActiveSessions($activeSessArr, $killSessArr, $joinSess);
            // If we're a front door login request and not a slide-in request,
            // Kill all the active sessions belonging to other machines
            if ( empty($parent) && count($killSessArr) && $type !== self::SESSION_TYPE_PORTAL) {
                self::logOutOtherMachine($killSessArr);
            }
            // If we got a session to join, do so
            // else create a new one
            if ( isset($joinSess) && $joinSess instanceof self) {
                $sessionHandler = self::joinExistingSession($type, $joinSess, $_userid);
                $profileHandler = $sessionHandler->getProfileHandler();
                // Force a regeneration of the session cache
                $profileHandler->rebuildCache();
            } else {
                $sessionHandler = self::createNewSession($type, $_userid, $parent, $location, $department, $locgrp, $deptgrp, $sessionKey);
            }
        }
        $sessionHandler->createSessionCookie();

        return $sessionHandler;
    }
    
    /**
     * @param string $type
     *
     * @return bool
     */
    private static function isValidSessionType(string $type): bool
    {
        return in_array($type, [
            self::SESSION_TYPE_LOGIN,
            self::SESSION_TYPE_SLIDEIN,
            self::SESSION_TYPE_SSO,
            self::SESSION_TYPE_REMOTECONN,
            self::SESSION_TYPE_PORTAL,
            self::SESSION_TYPE_UI_GET_SESSION,
            self::SESSION_TYPE_MFA_COMPLETION,
            self::SESSION_TYPE_IMS,
        ]);
    }

    /**
     * Check for an existing session. If found, join the existing session, else create a new one.
     *
     * @param string $_userid
     * @param string $parent
     * @param string $location
     * @param string $department
     * @param string $logPrefix
     * @param string $sessionType
     * @param string $senderid
     *
     * @return IASessionHandler
     * @throws Exception
     */
    public static function imsSetupSession(
        $_userid,
        $parent = '',
        $location = '',
        $department = '',
        string $logPrefix = self::LOG_PREFIX,
        string $sessionType = self::IMS_SESSION_KEY,
        string $senderid = ''
    ) {
        LogToFile(
            sprintf(
                "%s::%s:%d called, userid=%s, par=%s, loc=%s, dep=%s \n",
                $logPrefix, __METHOD__, __LINE__, $_userid, $parent, $location, $department
            )
        );
        Request::$r->_oid = $sessionType;

        // Added by Aaron to get more logging on profile creation failures
        global $gIMSSessionLog;
        $gIMSSessionLog = [];
        // Remove all references to this global when finished debugging the problem

        [$user_rec, $cny] = explode('@', $_userid);
        $user_rec = isset($user_rec) ? trim($user_rec) : '';

        if ( !empty($user_rec)) {
            // Track current CNY for logging purposes - ticket 1520
            // We do this here in addition to in util.inc Init() because Init() is not always called.
            logBasicCompanyInfo(__CLASS__ . '::' . __FUNCTION__, $cny);

            SetDBSchema($cny, 'cny');
            
            if ( !($result = self::getActiveSessions($_userid, $location, $department, senderid: $senderid, parent: $parent))) {
                /** @noinspection OnlyWritesOnParameterInspection */
                $gIMSSessionLog[] = "Number of Active Sessions: 0";
                $session = self::createNewSession(self::SESSION_TYPE_IMS, $_userid, $parent, $location, $department, senderid: $senderid);
                $profile = $session->getProfileHandler();
                // Backend_Init::SetEnvironment() sets up an environment with an empty user record.  If that envionment
                // creates an IMS job then $profile will be null.  Bypass profile operations if there's no user_rec.
                if ($user_rec !== '' || $profile) {
                    AddForcedElements($profile, GetMyAdmin(true));
                    UnsetNAPermissions($profile);
                    AddAllowOps($profile);
                    $profile->saveProfile();
                }
                /** @noinspection OnlyWritesOnParameterInspection */
                $gIMSSessionLog[] = "No active sessions, so created a new profile like this:";
                /** @noinspection OnlyWritesOnParameterInspection */
                $gIMSSessionLog[] = ppp($profile);
            } else {
                /** @noinspection OnlyWritesOnParameterInspection */
                $gIMSSessionLog[] = "Number of Active Sessions: " . count($result);
                $foundsession = null;

                foreach ($result as $session) {
                    if (CheckRequestMachine($session->getOID())) {
                        //we need to init the session object here because we used delayInit before
                        $session->init(false);
                        $profile = $session->getProfileHandler();

                        // If permission cache is invalid, need to regenerate
                        $usrPermCache = $profile->getProperty('userinfo.perm_cache_valid');
                        if ($usrPermCache === 'F') {
                            // regenerate profile['operations'] (permissions)
                            // only if its not a top-down slide (console having role based and named-slide enabled).
                            $topdown = false;
                            $extCny = $profile->getCompanyCacheProperty('CPAInfo', 'EXTERNCNYKEY');
                            if (isset($extCny) && IsExternalUser(GetMyUserCategory())
                                && IsRoleBasedCny($extCny)
                                && IsPracticeCompany($extCny)
                            ) {
                                $topdown = true;
                            }
                            if ( !$topdown) {
                                $baseperm = GetPermissions($user_rec);
                                if ($baseperm) {
                                    $profile->setProperty('operations', $baseperm);
                                }
                                AddForcedElements($profile, GetMyAdmin(true));
                                UnsetNAPermissions($profile);
                                AddAllowOps($profile);
                                $profile->saveProfile();
                            }
                        }
                        $_sess = &Request::$r->_sess;
                        if ( !isset($_sess) || $_sess == '') {
                            $_sess = $session->getKey();
                        }
                        // Check to see if this is a valid profile.
                        $operations = $profile->getProperty('operations');
                        if (isset($operations)) {
                            $foundsession = $session;
                            /** @noinspection OnlyWritesOnParameterInspection */
                            $gIMSSessionLog[] = "Found a valid session.  Using this profile:";
                            /** @noinspection OnlyWritesOnParameterInspection */
                            $gIMSSessionLog[] = ppp($profile);
                            break;
                        }
                    }
                }

                if ( $foundsession) {
                    $session = $foundsession;
                } else {
                    $session = self::createNewSession(self::SESSION_TYPE_IMS, $_userid, $parent, $location, $department, senderid: $senderid);
                    $profile = $session->getProfileHandler();
                    AddForcedElements($profile, GetMyAdmin(true));
                    UnsetNAPermissions($profile);
                    AddAllowOps($profile);
                    $profile->saveProfile();
                    /** @noinspection OnlyWritesOnParameterInspection */
                    $gIMSSessionLog[] = "Did not find a valid session.  Created this profile:";
                    /** @noinspection OnlyWritesOnParameterInspection */
                    $gIMSSessionLog[] = ppp($profile);
                }
            }

            // Backend_Init::SetEnvironment() sets up an environment with an empty user record.  If that envionment
            // creates an IMS job then $profile will be null.  Bypass profile operations if there's no user_rec.
            /** @noinspection PhpUndefinedVariableInspection */
            if ($user_rec !== '' || $profile) {
                /** @noinspection PhpUndefinedVariableInspection */
                $profile->validateCache($cacheRefreshed);

                $renaming = $profile->getProperty('renaming');
                if ( !is_array($renaming)) {
                    $profile->setProperty('renaming', Dictionary::Load($cny));
                }
            }
        } else {
            $session = null;
            LogToFile(sprintf("%s::%s:%d Error, user id not found\n", $logPrefix, __METHOD__, __LINE__));
            // Add a generic error
            Globals::$g->gErr->addError('CORE-1051', __FILE__ . '.' . __LINE__, "Can't setup a session when no user id is given");
        }

        return $session;
    }

    /**
     * verify that the key appears to be a well-formed session id
     *
     * @param string $key a possible session id
     *
     * @return bool true if the key looks like it might be a session id
     */
    public static function verifyKeyFormat($key, string $logPrefix = self::LOG_PREFIX)
    {
        [$serverId, $schemaId] = GetDBSchemaFromSession($key);

        if (!is_numeric($serverId) || !is_numeric($schemaId)) {
            if (!is_numeric($serverId)) {
                LogToFile(
                    sprintf(
                        "%s::%s:%d Error, serverId is not numeric, serverId=%s\n",
                        $logPrefix, __METHOD__, __LINE__, $serverId
                    )
                );
            }
            if (!is_numeric($schemaId)) {
                LogToFile(
                    sprintf(
                        "%s::%s:%d Error, schemaId is not numeric, schemaId=%s\n",
                        $logPrefix, __METHOD__, __LINE__, $schemaId
                    )
                );
            }
            return false;
        }
        
        if ($serverId <= 0) {
            LogToFile(
                sprintf(
                    "%s::%s:%d Error, serverId is lower than 0, serverId=%s\n",
                    $logPrefix, __METHOD__, __LINE__, $serverId
                )
            );
            return false;
        }

        $connections = getDatabaseServerInfo();
        if ( ! $connections ) {
            return false;
        }

        $serverId = str_pad($serverId, 2, '0', STR_PAD_LEFT);
        
        if (!isset($connections[$serverId])) {
            LogToFile(
                sprintf(
                    "%s::%s:%d Error, no connection found for serverId=%s\n",
                    $logPrefix, __METHOD__, __LINE__, $serverId
                )
            );
            return false;
        }

        $dbInfo = $connections[$serverId];
        if ($schemaId <= 0 || $schemaId > $dbInfo['schemacnt']) {
            LogToFile(
                sprintf(
                    "%s::%s:%d Error, schemaId lower than 0 or greater than the schemacnt, schemaId=%s, schemacnt=%s\n",
                    $logPrefix, __METHOD__, __LINE__, $schemaId, $dbInfo['schemacnt']
                )
            );
            return false;
        }

        return true;
    }

    /**
     * reads a session property from memcache
     *
     * @param string $key      a session key
     * @param string $property the property to read
     *
     * @return mixed the property's value for the session, if present
     */
    public static function getFromMemcache($key, $property, string $logPrefix = self::LOG_PREFIX)
    {
        LogToFile(
            sprintf(
                "%s::%s:%d sess=%s, property=%s\n",
                $logPrefix, __METHOD__, __LINE__, $key, $property
            )
        );
        $memcacheClient = CacheClient::getInstance();

        $cacheKey = self::_buildCacheKey($key, $property);
        $data = $memcacheClient->get($cacheKey);

        return $data;
    }

    /**
     * Marks active sessions as 'to-be-killed' and identifies single (if any)
     * session as 'to-be-joined'
     *
     * @param array                   $activeSessArr input: Array of active sessions
     * @param array                   $killSessArr   output: Sessions 'to-be-killed'
     * @param string|IASessionHandler $joinSess      output: Session 'to-be-joined'
     */
    private static function processActiveSessions(
        $activeSessArr,
        &$killSessArr,
        &$joinSess,
        string $logPrefix = self::LOG_PREFIX
    ) {
        if (!isset($activeSessArr) || !is_array($activeSessArr)) {
            LogToFile(sprintf("%s::%s:%d empty or not a valid payload\n", $logPrefix, __METHOD__, __LINE__));
            return;
        }

        $joinSessArr = [];
        $joinSess = '';
        foreach ( $activeSessArr as $activeSess ) {
            /** @noinspection PhpUnusedLocalVariableInspection */
            $sess = $activeSess->getKey();
            $oid = $activeSess->getOID();
            // process an active session only if its non-IMS/REST
            // We never kill/join an IMS/REST session from createSession()
            if (isUISession($oid)) {
                global $FromBackgroundReporter;
                // Check session's machine with actual machine
                // requesting createSession()
                $ok = CheckRequestMachine($oid);
                if ( ! $FromBackgroundReporter && ! $ok ) {
                    // If this active session belongs to different machine ($oid)
                    // mark it as 'to-be-killed'
                    $killSessArr[] = $activeSess;
                } else {
                    // If this active session belongs to same machine ($oid)
                    // requesting createSession() - mark as 'to-be-joined'
                    $joinSessArr[] = $activeSess;
                }
            } else if (Request::$r->_oid === $oid && !isUISession($oid)) {
                // Join an IMS/REST session if we are an IMS/REST session
                $joinSessArr[] = $activeSess;
                break; // We don't want to kill any IMS sessions, so we break here
            }
        }
        // Process sessions 'to-be-joined'
        $joinCnt = count($joinSessArr);
        if ( $joinCnt ) {
            // Handle scenario where we've multiple active session with // 'to-be-joined' status
            // We'll pick only first session to join and mark rest as
            // 'to-be-killed'
            if ( $joinCnt > 1 ) {
                // Join first session
                $joinSess = $joinSessArr[0];
                // Mark rest as 'to-be-killed'
                for ( $i = 1; $i < $joinCnt; $i++ ) {
                    $killSessArr[] = $joinSessArr[$i];
                }
            } else {
                // We've exactly one session 'to-be-joined'
                $joinSess = $joinSessArr[0];
            }
        }
    }

    /**
     * @param IASessionHandler[] $sessArr
     * @param string             $logPrefix Used to add a prefix to the log messages
     */
    private static function logOutOtherMachine($sessArr, string $logPrefix = self::LOG_PREFIX)
    {
        LogToFile(
            sprintf(
                "%s::%s:%d called, sessArr=%s\n",
                $logPrefix, __METHOD__, __LINE__,
                implode("','", array_map(function ($session) {
                    return $session->getKey();
                }, $sessArr))
            )
        );
        foreach ( $sessArr as $session ) {
            // process session tree
            $stmtstr = ["select session# from userprof start with session# = :1 connect by prior session# = parent", $session->getKey()];
            if ($sessTree = self::getSessionsFromDB($stmtstr)) {
                // clean the cached session data for the session tree
                foreach ( $sessTree as $sess ) {
                    $sess->init(false);
                    /* @var ProfileHandler $profileHandler */
                    $profileHandler = $sess->getProfileHandler();
                    if ($profileHandler) {
                        LogToFile(
                            sprintf(
                                "%s::%s:%d logout sess=%s\n",
                                $logPrefix, __METHOD__, __LINE__, $sess->getKey()
                            )
                        );
                        $profileHandler->setProperty('logout', 1);
                        $profileHandler->saveProfile();
                    }
                    $sess->cleanUp();
                }
            }

            // expire session tree
            $sqlstr = [
                "update userprof set status = 'F' where session# in (
                    select session# from userprof start with session# = :1 connect by prior session# = parent
                )",
                $session->getKey(),
            ];
            ExecStmt($sqlstr);
        }
    }

    /**
     * @param string           $type
     * @param IASessionHandler $sessionHandler Session Handler
     * @param string           $_userid
     * @param string           $logPrefix Used to add a prefix to the log messages
     *
     * @return IASessionHandler
     */
    private static function joinExistingSession(
        $type,
        $sessionHandler,
        $_userid,
        string $logPrefix = self::LOG_PREFIX
    ) {
        //we need to init the session object here because we used delayInit before
        $sessionHandler->init(false);
        if ($type == '') {
            $type = self::SESSION_TYPE_LOGIN;
        }
        $sess = $sessionHandler->getKey();
        LogToFile(
            sprintf(
                "%s::%s:%d called, type=%s, sess=%s, userid=%s \n",
                $logPrefix, __METHOD__, __LINE__, $type, $sess, $_userid
            )
        );
        
        Request::$r->_sess = $sess;
        ExecStmt([ "update userprof set birth = sysdate,lastaccess=sysdate where session# = :1", $sess ]);
        // regenerate profile['operations'] (permissions)
        [$user_rec] = explode('@', $_userid);
        $baseperm = GetPermissions($user_rec, $type);
        $app = GetDefaultApplication();
        $modcache = GetModuleIdList($_userid);

        /** @noinspection PhpUnusedLocalVariableInspection */
        $props = [];
        if ( $baseperm ) {
            // also update the birth on the profile to reflect the update we made in the database
            // this is needed to update the seconds since birth on the profile from cache if any
            $profileHandler = $sessionHandler->getProfileHandler();
            if ($profileHandler) {
                $profileHandler->updateProfile(
                    ['operations', 'app', 'modulecache', 'BIRTH'],
                    [$baseperm, $app, $modcache, time()]
                );
            }
        } else {
            // update the birth on the profile to reflect the update we made in the database
            $sessionHandler->getProfileHandler()->updateProfile(['BIRTH'], [time()]);
        }
        
        return $sessionHandler;
    }

    /**
     * Retrieve the active session that match the given criteria.
     *
     * @param string       $userid the id of the user that owns the session
     * @param string|false $location the location for which the session was created
     * @param int          $department the department for which the session was creaed
     * @param int          $locgrp the location group
     * @param int          $deptgrp the department group
     * @param string       $parent the parent session id
     * @param bool         $checkLastAccessTime add last access time when querying for active sessions
     * @param string       $logPrefix Used to add a prefix to the log messages
     * @param string       $senderid the sender id
     *
     * @return IASessionHandler[]|false possibly empty array of IASessionHandler objects or false if problems
     */
    private static function getActiveSessions(
        $userid,
        $location = null,
        $department = null,
        $locgrp = null,
        $deptgrp = null,
        $parent = null,
        $checkLastAccessTime = true,
        string $logPrefix = self::LOG_PREFIX,
        string $senderid = ''
    ) {
        $_oid = Request::$r->_oid;
        LogToFile(sprintf("%s::%s:%d called, OID=%s\n", $logPrefix, __METHOD__, __LINE__, $_oid ?? ''));
        [$userrec, $cny] = explode('@', $userid);
        
        if (!$userrec) {
            LogToFile(sprintf("%s::%s:%d Error, user id not found \n", $logPrefix, __METHOD__, __LINE__));
            throw new Exception("Can't fetch active sessions when no user id is given");
        }
        // Get user session/login timeouts
        $cnyPreferences = GetCompanyPreferences($cnyPreferences);
        $cnyInactivityTimeout = $cnyPreferences['SESSIONTIMEOUT'] ?? 3600;
        $cnyLoginTimeout = $cnyPreferences['LOGINTIMEOUT'] ?? 21600;
        $userInactivityTimeout = GetUserPreferences($userpref, 'SESSIONTIMEOUT', $cnyInactivityTimeout);
        $userLoginTimeout = GetUserPreferences($userpref, 'LOGINTIMEOUT', $cnyLoginTimeout);

        $qry[0] = "select userprof.session#,userprof.oid from userprof " .
                  " where userprof.cny# = :1 " .
                  " and userprof.userrec = :2 " .
                  " and (sysdate-userprof.birth)*24*60*60 < :3 " .
                  " and userprof.status = :4" .
                  " and userprof.profile_blob is not null";
        $qry[1] = $cny;
        $qry[2] = $userrec;
        $qry[3] = $userLoginTimeout;
        $qry[4] = "T";

        // Maintain argument counter
        $cnt = 5;

        // Process last access time
        if ( $checkLastAccessTime ) {
            $qry[0] .= " and (sysdate-userprof.lastaccess)*24*60*60 < :$cnt ";
            $qry[$cnt] = $userInactivityTimeout;
            $cnt++;
        }

        // Process location slide
        empty($location) && $location = GetRestrictedUserLocation();
        if ( $location ) {
            $qry[0] .= " and locationkey =  :" . $cnt;
            $qry[$cnt] = $location;
            $cnt++;
        } else {
            $qry[0] .= " and locationkey is null ";
        }

        // Process department slide
        if ( $department ) {
            $qry[0] .= " and departmentkey =  :" . $cnt;
            $qry[$cnt] = $department;
            $cnt++;
        } else {
            $qry[0] .= " and departmentkey is null ";
        }

        // Process location group slide
        if ( $locgrp ) {
            $qry[0] .= " and locgrpkey =  :" . $cnt;
            $qry[$cnt] = $locgrp;
            $cnt++;
        } else {
            $qry[0] .= " and locgrpkey is null ";
        }

        // Process department group slide
        if ( $deptgrp ) {
            $qry[0] .= " and deptgrpkey =  :" . $cnt;
            $qry[$cnt] = $deptgrp;
            $cnt++;
        } else {
            $qry[0] .= " and deptgrpkey is null ";
        }
        if (self::IMS_SESSION_KEY === $_oid) {
            $qry[0] .= " and oid =  :" . $cnt;
            $qry[$cnt] = $_oid;
            $cnt++;
        }

        // Process location group slide
        if ( $parent ) {
            $qry[0] .= " and parent =  :" . $cnt;
            $qry[$cnt] = $parent;
            /** @noinspection PhpUnusedLocalVariableInspection */
            $cnt++;
        } else {
            $qry[0] .= " and parent is null ";
        }

        // Process sender id
        if ( $senderid ) {
            $qry[0] .= " and senderid =  :" . $cnt;
            $qry[$cnt] = $senderid;
            $cnt++;
        } else {
            $qry[0] .= " and senderid is null ";
        }

        return self::getSessionsFromDB($qry);
    }

    /**
     * Execute the specified query that retrieves a list of session keys and return the corresponding list of session
     * objects.
     *
     * @param array  $qry the query to run. The query must return at least SESSION# and optionally the OID
     * @param bool   $delayInit whether to delay the initialization of this session handler
     *
     * @return IASessionHandler[]|bool  possibly empty array of IASessionHandler objects or false on problem
     */
    private static function getSessionsFromDB($qry, $delayInit = true)
    {
        $res = QueryResult($qry);
        if ($res === false) {
            return false;
        } elseif (count($res) == 0) {
            /** @var IASessionHandler[] $res */
            return $res;
        }
        $sessions = [];
        foreach ( $res as $record ) {
            $sessions[] = self::getInstance($record['SESSION#'], $record['OID'], $delayInit);
        }

        return $sessions;
    }

    /**
     * Create a new session object. This method generates a unique session key, creates a session handler for it
     * and also initializes the session porfile.
     *
     * @param string $type the type of the session
     * @param string $userid the user id that owns the session
     * @param string $parent the key of the session parent if any
     * @param string $location the location to which this session applies
     * @param string $department the department to which this session applies
     * @param string $locgrp the location group to which this session applies
     * @param string $deptgrp the department group to which this session applies
     * @param string $sessionKey the optional sessionKey. Do not generate if provided
     * @param string $logPrefix Used to add a prefix to the log messages
     * @param string $senderid the sender id
     *
     * @return IASessionHandler  the newly created session handler instance
     */
    public static function createNewSession(
        $type,
        $userid,
        $parent,
        $location,
        $department = '',
        $locgrp = '',
        $deptgrp = '',
        $sessionKey = '',
        string $logPrefix = self::LOG_PREFIX,
        string $senderid = ''
    ) {
        $originalType = $type;
        $type = $originalType != '' ? $originalType : 'LOGIN';
        
        [$user_rec] = explode('@', $userid);
        if (!$user_rec) {
            LogToFile(sprintf("%s::%s:%d user id not found\n", $logPrefix, __METHOD__, __LINE__));
            throw new Exception("Can't create a session when no user id is given");
        }
        //FIXME: we should remove the global def once we clean up all the session stuff
        if ($sessionKey === '') {
            $_sess = GenerateSessionKey();
        } else {
            $_sess = $sessionKey;
        }
        LogToFile(
            sprintf(
                "%s::%s:%d called, type=%s, userid=%s, parent=%s, loc=%s, dep=%s, locGrp=%s, depGrp=%s, sessKey=%s, sess=%s\n",
                $logPrefix, __METHOD__, __LINE__, $originalType, $userid, $parent, $location, $department, $locgrp,
                $deptgrp, $sessionKey, $_sess
            )
        );

        Request::$r->_sess = $_sess;
        CheckCreateOid();
        $_oid = Request::$r->_oid;
        $sessionHandler = self::getInstance($_sess, $_oid, true);
        if ($sessionHandler !== null) {
            $sessionHandler->setProperty(self::USER_ID_PROPERTY, $userid);
            // create user profile
            $profileHandler = ProfileHandler::createProfile(
                $sessionHandler, $type, $user_rec, $parent, $location, $department, $locgrp, $deptgrp, $senderid
            );
            $sessionHandler->setProfileHandler($profileHandler);
        }
        
        return $sessionHandler;
    }

    /**
     * @param string $key
     * @param string $oid
     * @param bool   $delayInit
     * @param bool   $getInactive
     */
    private function __construct($key, $oid, $delayInit, $getInactive = false)
    {
        $this->key = $key;
        $this->cacheClient = CacheClient::getInstance();
        
        if (!$oid) {
            $oid = (string)Request::$r->_oid;
        }
        $this->oid = $oid;
        $this->properties = [];
        if ( ! $delayInit ) {
            $this->init($getInactive);
        }
    }

    /**
     * Public wrapper over init method.
     *
     * @param bool $getInactive
     */
    public function initialize(bool $getInactive)
    {
        $this->init($getInactive);
    }

    /**
     * @param bool $getInactive
     */
    private function init($getInactive, string $logPrefix = self::LOG_PREFIX)
    {
        if ($this->initialized) {
            return;
        }
        $this->initialized = true;
        if (!$this->key) {
            LogToFile(sprintf("%s::%s:%d Error, empty session\n", $logPrefix, __METHOD__, __LINE__));
            return;
        }

        $userId = $this->getProperty(self::USER_ID_PROPERTY);
        if ( ! $userId ) {
            if (!$getInactive) {
                $stmt = "select cny#, userrec from userprof where session# = :1 and status = 'T'";
            } else {
                $stmt = "select cny#, userrec from userprof where session# = :1";
            }
            $result = QueryResult([$stmt, $this->key]);
            if (is_array($result) && count($result) == 1) {
                $userId = $result[0]['USERREC'] . '@' . $result[0]['CNY#'];
                $this->setProperty(self::USER_ID_PROPERTY, $userId);
            }
        }
        if ( $userId ) {
            $this->profileHandler = ProfileHandler::getInstance($this);
        }
    }

    /**
     * Check whether the session is valid, i.e. if the session data was sucessfully read from cache or db
     *
     * @return bool  true if the session is valid and false otherwise
     */
    public function isValidSession(string $logPrefix = self::LOG_PREFIX)
    {
        if (!$this->properties[self::USER_ID_PROPERTY]) {
            LogToFile(sprintf("%s::%s:%d Error, not a valid session\n", $logPrefix, __METHOD__, __LINE__));
            return false;
        }
        return true;
    }

    /**
     * ResetSessionBirth - resets the session birth/update time to now.
     *
     * @param int $ageCheckMinutes
     */
    public function ResetSessionBirth($ageCheckMinutes = 0, string $logPrefix = self::LOG_PREFIX)
    {
        $sess = $this->getKey();

        //  If a age check is given, only rebirth session if it is older than the check.
        if ( $ageCheckMinutes > 0 ) {
            $inHours = $ageCheckMinutes / 1440;
            $result = QueryResult(
                ["SELECT session# FROM userprof WHERE session#=:1 and sysdate-birth > :2", $sess, $inHours]
            );
            if (count($result) == 0) {
                LogToFile(
                    sprintf(
                        "%s::%s:%d session is not older than the check, sess=%s, inMinutes=%s, inHours=%",
                        $logPrefix, __METHOD__,  __LINE__, $sess, $ageCheckMinutes, $inHours));
                return;
            }
        }
        LogToFile(
            sprintf("%s::%s:%d try reset session birth on session: %s", $logPrefix, __METHOD__, __LINE__, $sess)
        );
        
        //  Update the session the database so both its birth and last access are Now.
        $ok = ExecStmt(["update userprof set birth = sysdate,lastaccess=sysdate where session# = :1", $sess]);
        if (!$ok) {
            LogToFile(
                sprintf(
                    "%s::%s:%d failed to reset session birth on session: %s",
                    $logPrefix, __METHOD__, __LINE__, $sess
                )
            );
        }
        
        //  Update the birth on the profile to reflect the update we made in the database.
        $this->getProfileHandler()->updateProfile(['BIRTH'], [time()]);
    }

    /**
     * @param string $logPrefix Used to add a prefix to the log messages
     *
     * @return void
     */
    private function cleanUp(string $logPrefix = self::LOG_PREFIX)
    {
        LogToFile(
            sprintf("%s::%s:%d sess=%s, oid=%s\n", $logPrefix, __METHOD__, __LINE__, $this->key, $this->oid)
        );
        $this->deleteSessionCookie();

        // The profile handler might have not been initialized if the session andler was created with
        // delay init parameter. In this case we get a non initialized instance of the profile handler and
        // use that to clean the cache
        if ( $this->profileHandler ) {
            $profileHandler = $this->profileHandler;
        } else {
            $profileHandler = ProfileHandler::getInstance($this, true);
        }
        if ( $profileHandler ) {
            $profileHandler->cleanUp();
        }

        if ( isset($this->properties) && is_array($this->properties) ) {
            foreach ( $this->properties as $property => $value ) {
                $this->deleteProperty($property);
            }
        }

        $this->deleteProperty(self::USER_ID_PROPERTY);
    }

    /**
     * Return the session key
     *
     * @return string  the session key
     */
    public function getKey()
    {
        return $this->key;
    }

    /**
     * Return the oid = session client key
     *
     * @return string  the session client key
     */
    public function getOID()
    {
        return $this->oid;
    }

    /**
     * @return bool
     */
    public function isImsSession()
    {
        return $this->oid === self::IMS_SESSION_KEY;
    }

    /**
     * @return bool
     */
    public function isUISession()
    {
        return $this->isUISession;
    }

    /**
     * Set whether session is UI based
     *
     * @param bool $isUISession
     *
     */
    public function setUISession(bool $isUISession)
    {
        $this->isUISession = $isUISession;
    }

    /**
     * @param string $property
     *
     * @return string
     */
    public function buildCacheKey($property)
    {
        return self::_buildCacheKey($this->key, $property);
    }

    /**
     * @param string $key
     * @param string $property
     *
     * @return string
     */
    private static function _buildCacheKey($key, $property)
    {
        return $key . '@sess_' . $property;
    }

    /**
     * Set the given (property, value) pair in the session. This stores the (property, value) pair in
     * the session handler's properties and in memory cache.
     *
     * @param string $property the name of the property
     * @param mixed  $value the value of the property
     * @param bool   $storeLocally whther to store the property in $this->properties (beside storing it in memory
     *                              cache)
     */
    public function setProperty($property, $value, $storeLocally = true)
    {
        if ( $storeLocally ) {
            $this->properties[$property] = $value;
        }
        $cacheKey = $this->buildCacheKey($property);
        $this->cacheClient->set($cacheKey, $value);
    }

    /**
     * Increment the value of the specified property.
     *
     * @param string $property the property name
     * @param int    $value the value to increment by
     *
     * @return int the new value
     */
    public function incrementProperty($property, $value = 1)
    {
        $cacheKey = $this->buildCacheKey($property);
        $newValue = $this->cacheClient->increment($cacheKey, $value, 1);

        $this->properties[$property] = $newValue;
        
        return $newValue;
    }
    
    /**
     * Return the value of the given property from the session. If the property is not found in the handler's
     * properties, it is looked up in memory cache.
     *
     * @param string $property the name of the property to retrieve
     * @param bool   $storeLocally true if the property is to be stored in the handler's properties after being
     *                              retrieved from memory cache
     *
     * @return mixed  the value of the named property if any was found, null otherwise
     */
    public function getProperty($property, $storeLocally = true)
    {
        if ( ! isset($this->properties[$property]) ) {
            $cacheKey = $this->buildCacheKey($property);
            $data = $this->cacheClient->get($cacheKey);
            if ( $storeLocally ) {
                $this->properties[$property] = $data;
            }
        } else {
            $data = $this->properties[$property];
        }

        return $data;
    }

    /**
     * @return mixed
     */
    public function getUserId()
    {
        return $this->getProperty(self::USER_ID_PROPERTY);
    }

    /**
     * Delete the given property from session.
     *
     * @param string $property The name of the property to delete
     */
    public function deleteProperty($property)
    {
        if ( isset($this->properties[$property]) ) {
            unset($this->properties[$property]);
        }
        $cacheKey = $this->buildCacheKey($property);
        $this->cacheClient->delete($cacheKey);
    }

    /**
     * @param ProfileHandler $profileHandler
     *
     * @return ProfileHandler
     */
    private function setProfileHandler($profileHandler)
    {
        return $this->profileHandler = $profileHandler;
    }

    /**
     * @return ProfileHandler
     */
    public function getProfileHandler()
    {
        return $this->profileHandler;
    }

    /**
     * Delete the sessions from database and memory cache. When any session logs out cleanup its children as well.
     *
     * @param string $sess the session id of the session to delete
     * @param int    $cny the cny# of the user's company for which to delete the sessions
     * @param string $userrec the record# of the user for which to delete the sessions
     * @param string $logPrefix Used to add a prefix to the log messages
     * @param string $oid the oid of the session to delete
     *
     * @return bool
     */
    public static function deleteSessions($sess, $cny = 0, $userrec = '', string $logPrefix = self::LOG_PREFIX, string $oid = '')
    {
        LogToFile(
            sprintf(
                "%s::%s:%d sess=%s, cny=%s, userrec=%s\n",
                $logPrefix, __METHOD__, __LINE__, $sess, $cny, $userrec
            )
        );
        $extra = '';
        // delete the session from userprof - logging out from parent would log out all the child sessions opened
        if ( isset($sess) && $sess != '' ) {
            $stmt = '';
            $extra = "start with session# = :1 connect by prior session# = parent";
            $params = [ $sess ];
        } else {
            if (empty($oid)) {
                $stmt = "cny# = :1 and userrec = :2";
                $params = [ $cny, $userrec ];
            } else {
                $stmt = "cny# = :1 and userrec = :2 and oid = :3";
                $params = [ $cny, $userrec, $oid ];
            }
        }
        
        $ok = self::deleteSessionsBySQL($stmt, $params, $extra);

        if ( ! $cny ) {
            $cny = (int) GetMyCompany();
        }
        // if we are in a practice company, let's kick off a background job to clean up orphan child sessions
        if ( $ok && $cny && IsPracticeCompany($cny) ) {
            LoginManager::kickChildSessionsCleanup($cny);
        }

        return $ok;
    }

    /**
     * Delete the sessions from database and memory cache.
     *
     * @param string $whereClause
     * @param array  $params
     * @param string $extra
     * @param string $logPrefix Used to add a prefix to the log messages
     *
     * @return bool
     */
    public static function deleteSessionsBySQL(
        $whereClause,
        $params = [],
        $extra = '',
        string $logPrefix = self::LOG_PREFIX
    ) {
        LogToFile(sprintf("%s::%s:%d called  \n", $logPrefix, __METHOD__, __LINE__));
        $where = '';
        if ($whereClause) {
            $where = "where $whereClause";
        }
        $stmt = "select session#, oid from userprof $where $extra";

        $sql = $params;
        array_unshift($sql, $stmt);

        // first delete the profiles from cache
        $result = QueryResult($sql);
        if ( $result ) {
            foreach ( $result as $row ) {
                /* @var IASessionHandler $sessionHandler */
                $sessionHandler = self::getInstance($row['SESSION#'], null, false, false, true);
                if (isset($sessionHandler)) {
                    $sessionHandler->cleanUp();
                }
            }
        }

        // add the delete statement fragment into our stmt array
        $sql[0] = "delete from userprof where session# in (select session# from userprof $where $extra)";

        return ExecStmt($sql);
    }
    
    /**
     * This method sets the API endpoint for a given sessionid.
     *
     * @param string $url
     *
     * @return bool
     */
    public function setEndpoint($url)
    {
        $ret = ExecStmt([ "update userprof set apiendpoint=:1 where session# = :2", $url, $this->key ]);

        return $ret;
    }

    /**
     * This method gets the API endpoint from a given sessionid.
     *
     * @param string $sess
     *
     * @return string
     */
    public function getEndpoint($sess = null)
    {
        $whichSess = ( ( $sess == null ) ? $this->key : $sess );
        $ret = QueryResult([ "SELECT apiendpoint FROM userprof WHERE session#=:1", $whichSess ]);

        //  Check to make sure the record was found.  It should be if the session is valid (i.e. if
        //   it got this far), but check for safety.
        if ( ! is_array($ret) || count($ret) == 0 ) {
            global $gErr;
            $gErr->addIAError(
                'CORE-1052', __FILE__ . '.' . __LINE__,
                "Invalid sessionid '" . $whichSess . "' given for API operation",
                ['WHICH_SESSION' => $whichSess]
            );

            return 'ERROR';
        }

        return $ret[0]['APIENDPOINT'];
    }

    /**
     * checkEndpoint
     * This method checks to see if the given URL endpoint matches the URL endpoint stored in
     * this session.  If not, an error is pushed, and the method returns false.
     *
     * @param string $sess
     * @param string $url
     *
     * @return bool
     */
    public function checkEndpoint($sess, $url)
    {
        global $gErr;

        $endpointURL = $this->getEndpoint($sess);
        if ( $endpointURL == 'ERROR' ) {
            return false;
        }

        //  Make sure the stored URL matches the given one.
        if ( $url != $endpointURL ) {
            LogToFile(
                "IASessionHandler.checkEndpoint: Given API Endpoint '" . $url
                . "' does not match expected login endpoint '" .
                $endpointURL . "'\n"
            );

            $gErr->addError(
                'CORE-1053', __FILE__ . '.' . __LINE__,
                "API operations require use of URL returned by the getAPISession function"
            );

            return false;
        }

        return true;
    }

    /**
     * @param array $profData
     * @param int   $userrec
     * @param array $grpMembers
     *
     * @return bool
     */
    public function setDynamicLocGrpSession(&$profData, $userrec, $grpMembers)
    {
        if ( $profData['ISME'] != 'T' ) {
            return true;
        }

        $gManagerFactory = Globals::$g->gManagerFactory;
        /** @noinspection PhpUnusedLocalVariableInspection */
        $grpManager = $gManagerFactory->getManager('locationgroup');
        $sessionKey = $this->getKey();

        $source = 'IASessionHandler::setDynamicLocGrpSession';
        $ok = true;

        XACT_BEGIN($source);

        $delStmt = "DELETE FROM SESSIONLOCABOVE SA WHERE SA.CNY#=:1 and SA.SESSIONKEY=:2";
        $ok = $ok && ExecStmt([ $delStmt, GetMyCompany(), $sessionKey ]);

        $delStmt = "DELETE FROM SESSIONLOCBELOW SB WHERE SB.CNY#=:1 and SB.SESSIONKEY=:2";
        $ok = $ok && ExecStmt([ $delStmt, GetMyCompany(), $sessionKey ]);

        $res = QueryResult(
            [
                "SELECT COUNT(1) as res FROM USERLOC U WHERE U.CNY# = :1 AND U.USERKEY = :2",
                GetMyCompany(),
                $userrec,
            ]
        );

        $restrictedUser = false;
        if ( $res[0]['RES'] ) {
            $restrictedUser = true;
        }

        if ( ! GetContextLocation() ) {
            if ( $restrictedUser ) {
                $insertStmt[0] = "INSERT INTO SESSIONLOCBELOW (CNY#, LOCATIONKEY, SESSIONKEY, LOCLEVEL, LOCATION_NO)
                    SELECT cny#, record#, skey, lvl, location_no
                    FROM (
                    SELECT lm.cny#, lm.record#, :2 skey, LEVEL lvl, lm.location_no
                    , ROW_NUMBER() OVER (PARTITION BY lm.record# ORDER BY lm.record#) AS dups
                    FROM    LOCATIONMST lm
                    WHERE ISROOT = 'F' AND lm.cny#=:1 
                    AND lm.record# in (
                            select record# from locationmst l where cny#=:1 start with cny#=:1 and record# in (
                                select locationkey from USERLOC where cny#=:1 and userkey=:3
                            ) 
                            CONNECT BY l.CNY# = :1 and l.PARENTKEY = PRIOR l.RECORD#
                        ) 
                    START WITH lm.CNY# = :1 ";

                $insertStmt[] = GetMyCompany();
                $insertStmt[] = $sessionKey;
                $insertStmt[] = $userrec;

                if ( count($grpMembers) ) {
                    $insertStmt = PrepINClauseStmt($insertStmt, $grpMembers, " and lm.record# ", true, 'locationgroup');
                }

                $insertStmt[0] .= " AND LM.ISROOT = 'F' CONNECT BY lm.CNY# = :1 
                    and lm.PARENTKEY = PRIOR lm.RECORD# AND LM.ISROOT = 'F') a
                    WHERE a.dups = 1";

                $ok = $ok && ExecStmt($insertStmt);
            } else {
                $insertStmt[0] = "INSERT INTO SESSIONLOCBELOW (cny#, locationkey, sessionkey, loclevel, location_no)
                    SELECT cny#, record#, skey, lvl, location_no
                    FROM (
                    SELECT lm.cny#, lm.record#, :2 as skey, LEVEL lvl, lm.location_no
                    , ROW_NUMBER() OVER (PARTITION BY lm.record# ORDER BY lm.record#) AS dups
                    FROM   locationmst lm
                    WHERE lm.cny# = :1 
                    START WITH lm.CNY# = :1 ";

                $insertStmt[] = GetMyCompany();
                $insertStmt[] = $sessionKey;

                if ( count($grpMembers) ) {
                    $insertStmt = PrepINClauseStmt($insertStmt, $grpMembers, " and lm.record# ", true, 'locationgroup');
                }

                $insertStmt[0] .= " CONNECT BY lm.CNY# = :1 and lm.PARENTKEY = PRIOR lm.RECORD# ) a
                    WHERE a.dups = 1";

                $ok = $ok && ExecStmt($insertStmt);
            }
        } else {
            $insertStmt[0] = "INSERT INTO SESSIONLOCABOVE (CNY#, LOCATIONKEY, SESSIONKEY, LOCLEVEL, LOCATION_NO)
                SELECT	distinct lm.CNY#, lm.record#, :2, 1-LEVEL, lm.LOCATION_NO
                FROM    LOCATIONMST lm
                WHERE lm.cny# = :1 
                START   WITH lm.CNY# = :1 and LM.RECORD# = :3
                CONNECT BY lm.CNY# = :1 and PRIOR LM.PARENTKEY = LM.RECORD# ";

            $insertStmt[] = GetMyCompany();
            $insertStmt[] = $sessionKey;
            $insertStmt[] = GetContextLocation();

            $ok = $ok && ExecStmt($insertStmt);
            $insertStmt = [];

            if ( $restrictedUser ) {
                $insertStmt[0] = "INSERT INTO SESSIONLOCBELOW (CNY#, LOCATIONKEY, SESSIONKEY, LOCLEVEL, LOCATION_NO)
                    SELECT loc.CNY#, loc.RECORD#, loc.SESSIONKEY, loc.ALEVEL, loc.LOCATION_NO
                    from(
                        SELECT distinct CNY#, RECORD#, :1 SESSIONKEY, level-1 ALEVEL, lm.location_no
                        FROM LOCATIONMST lm 
                        WHERE lm.cny# = :1 and lm.record# in (
                            select record# from locationmst l start with cny#=:1 and record# in (
                                select locationkey from USERLOC where cny#=:1 and userkey=:3
                            ) 
                            CONNECT BY l.CNY# = :1 and l.PARENTKEY = PRIOR l.RECORD#
                        )
                        START WITH lm.CNY# = :1 ";

                $insertStmt[] = GetMyCompany();
                $insertStmt[] = $sessionKey;
                $insertStmt[] = $userrec;

                if ( count($grpMembers) ) {
                    $insertStmt = PrepINClauseStmt($insertStmt, $grpMembers, " and lm.record# ", true, 'locationgroup');
                }

                $insertStmt[0] .= " CONNECT BY lm.CNY# = :1 and lm.PARENTKEY = PRIOR lm.RECORD#) loc ";
                $ok = $ok && ExecStmt($insertStmt);
            } else {
                $insertStmt[0] = "INSERT INTO SESSIONLOCBELOW (CNY#, LOCATIONKEY, SESSIONKEY, LOCLEVEL, LOCATION_NO)
                    SELECT	distinct LOC.CNY#, LOC.RECORD#, :2, LEVEL-1, LOC.LOCATION_NO
					FROM LOCATIONMST LOC
                    WHERE LOC.cny# = :1 
                    START   WITH LOC.CNY# = :1 ";

                $insertStmt[] = GetMyCompany();
                $insertStmt[] = $sessionKey;
                $insertStmt[] = $userrec;

                if ( count($grpMembers) ) {
                    $insertStmt =
                        PrepINClauseStmt($insertStmt, $grpMembers, " and LOC.record# ", true, 'locationgroup');
                }

                $insertStmt[0] .= " CONNECT BY LOC.CNY# = :1 and LOC.PARENTKEY = PRIOR LOC.RECORD# ";

                $ok = $ok && ExecStmt($insertStmt);
            }
        }

        /* @var array $insertStmt annontation needed to work around https://youtrack.jetbrains.com/issue/WI-42529 */
        $insertStmt = [];
        $insertStmt[0] = "INSERT INTO SESSIONLOCABOVE (CNY#, LOCATIONKEY, SESSIONKEY, LOCLEVEL, 
            LOCATION_NO, ENTITY#, ENTITY_NO)
		SELECT	:1, 0, :2,
				nvl(min(loclevel),1)-1, NULL, NULL, NULL
		FROM SESSIONLOCABOVE
		WHERE cny# = :1 and sessionkey=:2";

        $insertStmt[] = GetMyCompany();
        $insertStmt[] = $sessionKey;
        $ok = $ok && ExecStmt($insertStmt);

        $updateStmt = [];
        $updateStmt[0] = "UPDATE SESSIONLOCABOVE LA
            SET (ENTITY#, ENTITY_NO) = (
            SELECT RECORD#, LOCATION_NO
            FROM   LOCATIONMST L
            WHERE  L.CNY# = :1 AND LOCATIONTYPE = 'E' AND ISROOT = 'F'
            START  WITH L.CNY# = LA.CNY# AND L.RECORD# = LA.LOCATIONKEY AND L.ISROOT = 'F'
            CONNECT BY L.CNY# = PRIOR L.CNY# AND L.RECORD# = PRIOR L.PARENTKEY AND L.ISROOT = 'F'
            )
            WHERE LA.CNY# = :1 AND LA.SESSIONKEY = :2 and LA.ENTITY# is NULL";

        $updateStmt[] = GetMyCompany();
        $updateStmt[] = $sessionKey;
        $ok = $ok && ExecStmt($updateStmt);

        if ( ! $profData['LOCATIONKEY'] ) {
            $insertStmt = [];
            $insertStmt[0] = "INSERT INTO SESSIONLOCBELOW (CNY#, LOCATIONKEY, SESSIONKEY, LOCLEVEL, 
                LOCATION_NO, ENTITY#, ENTITY_NO)
            SELECT	:1, 0, :2, 0, NULL, NULL, NULL
            FROM DUAL";
            $insertStmt[] = GetMyCompany();
            $insertStmt[] = $sessionKey;

            $ok = $ok && ExecStmt($insertStmt);
        }

        $updateStmt = [];
        $updateStmt[0] = "UPDATE SESSIONLOCBELOW LB
            SET (ENTITY#, ENTITY_NO) = (
            SELECT RECORD#, LOCATION_NO
            FROM   LOCATIONMST L
            WHERE  L.CNY# = :1 AND LOCATIONTYPE = 'E' AND L.ISROOT = 'F'
            START  WITH L.CNY# = LB.CNY# AND L.RECORD# = LB.LOCATIONKEY AND L.ISROOT = 'F'
            CONNECT BY L.CNY# = PRIOR L.CNY# AND L.RECORD# = PRIOR L.PARENTKEY AND L.ISROOT = 'F'
            )
            WHERE	CNY# = :1 AND SESSIONKEY = :2 and LB.ENTITY# is NULL";

        $cnyKey = GetMyCompany();
        $updateStmt[] = $cnyKey;
        $updateStmt[] = $sessionKey;
        $ok = $ok && ExecStmt($updateStmt);

        $sysContext[0] = "declare v1 varchar2(1000 char); v2 varchar2(1000 char); begin v1 := :1; v2 := :2;
            Tmproc('LOCMAPBELOW', co_utils.getcnylocmapbelow(v1, v2));
            Tmproc('LOCMAPABOVE', co_utils.getcnylocmapabove(v1, v2));
            Tmproc('LOCMAPBOTH', co_utils.getcnylocmapboth(v1, v2)); end;";
        $sysContext[] = $cnyKey;
        $sysContext[] = $sessionKey;
        $ok = $ok && ExecStmt($sysContext);

        $userProfStmt[0] = "update userprof set LOCMAPBELOW = sys_context('TMCtx', 'LOCMAPBELOW', 4000),
		  LOCMAPABOVE = sys_context('TMCtx', 'LOCMAPABOVE', 4000),
		  LOCMAPBOTH = sys_context('TMCtx', 'LOCMAPBOTH',4000) where cny# = :1 and session# = :2";
        $userProfStmt[] = $cnyKey;
        $userProfStmt[] = $sessionKey;
        $ok = $ok && ExecStmt($userProfStmt);

        $ok = $ok && XACT_COMMIT($source);
        if ( ! $ok ) {
            XACT_ABORT($source);
        }

        return $ok;
    }

    /**
     * @param array $profData
     * @param int   $userrec
     * @param array $grpMembers
     *
     * @return bool
     */
    public function setDynamicDepGrpSession(&$profData, $userrec, $grpMembers)
    {
        if ( $profData['ISME'] != 'T' ) {
            return true;
        }

        $gManagerFactory = Globals::$g->gManagerFactory;
        /** @noinspection PhpUnusedLocalVariableInspection */
        $grpManager = $gManagerFactory->getManager('departmentgroup');
        $sessionKey = $this->getKey();
        $ok = true;
        $source = 'IASessionHandler::setDynamicDepGrpSession';
        XACT_BEGIN($source);

        $delStmt = "DELETE FROM SESSIONDEPTABOVE SA WHERE SA.CNY# = :1 and SA.SESSIONKEY=:2";
        $ok = $ok && ExecStmt([ $delStmt, GetMyCompany(), $sessionKey ]);

        $delStmt = "DELETE FROM SESSIONDEPTBELOW SB WHERE SB.CNY#=:1 and SB.SESSIONKEY=:2";
        $ok = $ok && ExecStmt([ $delStmt, GetMyCompany(), $sessionKey ]);

        $res = QueryResult(
            [
                "SELECT COUNT(1) as res FROM USERDEPT U WHERE U.CNY# = :1 AND U.USERKEY = :2",
                GetMyCompany(),
                $userrec,
            ]
        );

        $restrictedUser = false;
        if ( $res[0]['RES'] ) {
            $restrictedUser = true;
        }

        if ( $restrictedUser ) {
            $insertStmt[0] = "INSERT INTO SESSIONDEPTABOVE (CNY#, DEPTKEY, SESSIONKEY)
                SELECT  DISTINCT dm.CNY#, dm.RECORD#, :2
                FROM DEPARTMENTMST dm
                WHERE dm.CNY# = :1
                AND dm.record# in (
                        select record# from departmentmst d start with cny#=:1 and record# in (
                            select deptkey from USERDEPT where cny#=:1 and userkey=:3
                        ) 
                        CONNECT BY d.CNY# = :1 and d.PARENT# = PRIOR d.RECORD#
                    ) 
                START WITH dm.CNY# = :1 ";

            $insertStmt[] = GetMyCompany();
            $insertStmt[] = $sessionKey;
            $insertStmt[] = $userrec;

            if ( count($grpMembers) ) {
                $insertStmt = PrepINClauseStmt($insertStmt, $grpMembers, " and dm.record# ", true, 'departmentgroup');
            }

            $insertStmt[0] .= " CONNECT BY dm.CNY# = :1 
                and dm.PARENT# = PRIOR dm.RECORD# ";
            $ok = $ok && ExecStmt($insertStmt);

            /* @var array $insertStmt annontation needed to work around https://youtrack.jetbrains.com/issue/WI-42529 */
            $insertStmt = [];

            $insertStmt[0] = "INSERT INTO SESSIONDEPTBELOW (CNY#, DEPTKEY, SESSIONKEY)
                SELECT  DISTINCT dm.CNY#, dm.RECORD#, :2
                FROM DEPARTMENTMST dm
                WHERE dm.CNY# = :1
                AND dm.record# in (
                        select record# from departmentmst d start with cny#=:1 and record# in (
                            select deptkey from USERDEPT where cny#=:1 and userkey=:3
                        ) 
                        CONNECT BY d.CNY# = :1 and d.PARENT# = PRIOR d.RECORD#
                    ) 
                START   WITH dm.CNY# = :1 ";

            $insertStmt[] = GetMyCompany();
            $insertStmt[] = $sessionKey;
            $insertStmt[] = $userrec;

            if ( count($grpMembers) ) {
                $insertStmt = PrepINClauseStmt($insertStmt, $grpMembers, " and dm.record# ", true, 'departmentgroup');
            }

            $insertStmt[0] .= " CONNECT BY dm.CNY# = :1 
                and dm.PARENT# = PRIOR dm.RECORD# ";
            $ok = $ok && ExecStmt($insertStmt);
        } else {
            $insertStmt = [];

            $insertStmt[0] = "INSERT INTO SESSIONDEPTBELOW (CNY#, DEPTKEY, SESSIONKEY)
                SELECT  DISTINCT dm.CNY#, dm.RECORD#, :2
                FROM DEPARTMENTMST dm
                WHERE dm.CNY# = :1
                START   WITH dm.CNY# = :1 ";

            $insertStmt[] = GetMyCompany();
            $insertStmt[] = $sessionKey;

            if ( count($grpMembers) ) {
                $insertStmt = PrepINClauseStmt($insertStmt, $grpMembers, " and dm.record# ", true, 'departmentgroup');
            }

            $insertStmt[0] .= " CONNECT BY dm.CNY# = :1 
                and dm.PARENT# = PRIOR dm.RECORD# ";
            $ok = $ok && ExecStmt($insertStmt);
        }

        /* @var array $insertStmt annontation needed to work around https://youtrack.jetbrains.com/issue/WI-42529 */
        $insertStmt = [];
        $insertStmt[0] = "INSERT INTO SESSIONDEPTABOVE (CNY#, DEPTKEY, SESSIONKEY)
		SELECT	:1, 0, :2
		FROM	DUAL";

        $insertStmt[] = GetMyCompany();
        $insertStmt[] = $sessionKey;
        $ok = $ok && ExecStmt($insertStmt);

        $insertStmt = [];
        $insertStmt[0] = "INSERT INTO SESSIONDEPTBELOW (CNY#, DEPTKEY, SESSIONKEY)
		SELECT	:1, 0, :2
		FROM	DUAL";

        $cnyKey = GetMyCompany();
        $insertStmt[] = $cnyKey;
        $insertStmt[] = $sessionKey;
        $ok = $ok && ExecStmt($insertStmt);

        $sysContext[0] = "declare var1 varchar2(100 char); var2 varchar2(100 char); begin var1 := :1; var2 := :2;
            Tmproc('DEPTMAPBELOW', co_utils.getcnydeptmapbelow(var1, var2));
            Tmproc('DEPTMAPABOVE', co_utils.getcnydeptmapabove(var1, var2));
            Tmproc('DEPTMAPBOTH', co_utils.getcnydeptmapboth(var1, var2)); end;";
        $sysContext[] = $cnyKey;
        $sysContext[] = $sessionKey;
        $ok = $ok && ExecStmt($sysContext);

        $userProfStmt[0] = "update userprof set DEPTMAPBELOW = sys_context('TMCtx', 'DEPTMAPBELOW',4000),
		  DEPTMAPABOVE = sys_context('TMCtx', 'DEPTMAPABOVE',4000),
		  DEPTMAPBOTH = sys_context('TMCtx', 'DEPTMAPBOTH',4000) where cny# = :1 and session# = :2";
        $userProfStmt[] = $cnyKey;
        $userProfStmt[] = $sessionKey;
        $ok = $ok && ExecStmt($userProfStmt);

        $ok = $ok && XACT_COMMIT($source);
        if ( ! $ok ) {
            XACT_ABORT($source);
        }

        return $ok;
    }

    /**
     * Generates a session cookie name/value pair.  This data is based off of the current session.
     *
     * @param string $userId
     * @param string $cnyId
     * @param string $sessionId
     * @param string $cookieName
     * @param string $cookieValue
     *
     * @return bool
     */
    private static function sessionCookieRecipie(
        $userId,
        $cnyId,
        $sessionId,
        &$cookieName,
        &$cookieValue,
        string $logPrefix = self::LOG_PREFIX
    ) {
        if (!$userId) {
            LogToFile(sprintf("%s::%s:%d Error, userId is null or empty\n", $logPrefix, __METHOD__, __LINE__));
            return false;
        }
        if (!$cnyId) {
            LogToFile(sprintf("%s::%s:%d Error, cnyId is null or empty\n", $logPrefix, __METHOD__, __LINE__));
            return false;
        }
        if (!$sessionId) {
            LogToFile(sprintf("%s::%s:%d Error, sessionId is null or empty\n", $logPrefix, __METHOD__, __LINE__));
            return false;
        }

        $trPatterns = [ 'OxsKYZR5WBS', 'eTnLy7ahdcV', 'MjwF|GogCtp', 'IA0z9r~buvJ', 'l68_fmDEHqi', 'P23-N4UQXk1' ];

        $sessionSum = 0;
        $sessLen = strlen($sessionId);
        for ( $i = 0; $i < $sessLen; $i++ ) {
            $sessionSum += ord($sessionId[$i]);
        }

        $t1 = $cnyId . '-' . $userId;
        $t1Len = strlen($t1);

        // Create a filler at least as long as the user/cny string
        $t2 = '';
        for ( $i = 0; strlen($t2) < $t1Len; $i++ ) {
            $t2 = $t2 . strtr($sessionSum, "0123456789", $trPatterns[$i % 6]);
        }

        // Merge the user/company and filler
        $t3A = [];
        for ( $i = 0; $i < $t1Len; $i++ ) {
            $t3A[] = $t1[$i];
            $t3A[] = $t2[$i];
        }
        $t3 = join('', $t3A);

        // Transliterate then encrypt the string to make the cookie name
        $t4 = strtr($t3, "0123456789-", $trPatterns[$sessionSum % 6]);

        $cookieName = 'sess-' . $t4;
        $cookieValue = strtr($sessionSum, "0123456789-", $trPatterns[5 - ( $sessionSum % 6 )]);

        return true;
    }

    /**
     * generates a session cookie name/value pair.  This data is based off
     * of the current session.
     *
     * @param string &$cookieName the name to use for the session cookie
     * @param string &$cookieValue the value for the session cookie
     * @param string $logPrefix Used to add a prefix to the log messages
     *
     * @return bool false if we're not able to create a session cookie
     */
    public function generateSessionCookieData(&$cookieName, &$cookieValue, string $logPrefix = self::LOG_PREFIX)
    {
        $userId = $this->getUserId();
        if (!isset($userId) || $userId === '') {
            LogToFile(sprintf("%s::%s:%d Error, userId is null or empty\n", $logPrefix, __METHOD__, __LINE__));
            return false;
        }
        // Strip off any @Application
        [$user, $cny] = explode('@', $userId);

        $sess = $this->getKey();

        $ret = self::sessionCookieRecipie($user, $cny, $sess, $cookieName, $cookieValue);

        return $ret;
    }

    /**
     * @param string $cookieName
     *
     * @return bool
     */
    public static function isSessionCookie($cookieName)
    {
        return strpos($cookieName, "sess-") === 0;
    }

    /**
     * creates a cookie specific to this session.
     *
     * @return bool false if we couldn't figure out what the cookie's name should be.
     */
    private function createSessionCookie(string $logPrefix = self::LOG_PREFIX)
    {
        if (false === $this->generateSessionCookieData($cookieName, $cookieValue)) {
            LogToFile(
                sprintf("%s::%s:%d failed to generate session cookie data \n", $logPrefix, __METHOD__, __LINE__)
            );
            return false;
        }

        INTACCTsetcookie(
            $cookieName,
            $cookieValue,
            time() + (new SessionInfo(new CompanyUtil(), new UserUtil(), new UserPrefUtil()))->getLoginTimeout(),
            '/',
            getTLD()
        );
        $_COOKIE[$cookieName] = $cookieValue;

        return true;
    }

    /**
     * verifies that the expected cookie associated with this session is present and valid.
     *
     * @param string $logPrefix Used to add a prefix to the log messages
     *
     * @return bool true if the expected cookie exists and contains the correct value.
     */
    public function validateSessionCookie(string $logPrefix = self::LOG_PREFIX)
    {
        if ($this->isImsSession()) {
            return true;
        }
        
        if (!$this->generateSessionCookieData($cookieName, $expectedCookieValue)) {
            LogToFile(
                sprintf(
                    "%s::%s:%d Error, failed to generate session cookie data\n",
                    $logPrefix, __METHOD__, __LINE__
                )
            );
            return false;
        }

        $cookieValue = crackArray($_COOKIE, $cookieName, null);
        if ($cookieValue === null) {
            LogToFile(
                sprintf("%s::%s:%d Error, session cookie not set\n", $logPrefix, __METHOD__, __LINE__)
            );
            return false;
        } elseif ($cookieValue != $expectedCookieValue) {
            LogToFile(
                sprintf(
                    "%s::%s:%d Error, session cookie not set to correct value\n",
                    $logPrefix, __METHOD__, __LINE__
                )
            );
            return false;
        }

        return true;
    }

    /**
     * deletes the cookie associated with this session
     *
     * @param string $logPrefix Used to add a prefix to the log messages
     *
     * @return bool false if we couldn't figure out what the cookie's name should be.
     */
    private function deleteSessionCookie(string $logPrefix = self::LOG_PREFIX)
    {
        if (!$this->generateSessionCookieData($cookieName, $unsedValue)) {
            LogToFile(
                sprintf(
                    "%s::%s:%d Error, unable to generate session cookie data, userId=%s, sessionId=%s\n",
                    $logPrefix, __METHOD__, __LINE__, $this->getUserId(), $this->getKey())
            );

            return false;
        }
        LogToFile(sprintf("%s::%s:%d delete cookie %s\n", $logPrefix, __METHOD__, __LINE__, $cookieName));
        INTACCTunsetcookie($cookieName, self::SESSION_COOKIE_DELETED_VALUE);
        
        (new IdPCookieProvider())
            ->removeCookieForSession($this->getKey(), (new IdPEntityProvider())->getDomain());

        return true;
    }

    /**
     * generates a string representation of the cookie name/value pair.  This value should
     * be passed to setSessionCookieFromToken to create the session cookie at some later time.
     *
     * @param string $logPrefix Used to add a prefix to the log messages
     *
     * @return bool|string  false on errors otherwise the session cookie keyring.
     */
    public function getSessionCookieToken(string $logPrefix = self::LOG_PREFIX)
    {
        if (!$this->generateSessionCookieData($cookieName, $cookieValue)) {
            LogToFile(
                sprintf(
                    "%s::%s:%s Error, Unable to generate session cookie data, userId=%s, SessionId=%s\n",
                    $logPrefix, __METHOD__, __LINE__, $this->getUserId(), $this->getKey()
                )
            );

            return false;
        }

        return "$cookieName=$cookieValue";
    }

    /**
     * Sets the session cookie from the keyring generated by getSessionCookieToken
     *
     * @param string $sessionId
     * @param string $userId
     * @param string $cnyId
     * @param string $sessionCookieToken
     * @param string $logPrefix          Used to add a prefix to the log messages
     *
     * @return bool
     */
    public static function setSessionCookieFromToken($sessionId, $userId, $cnyId, $sessionCookieToken, string $logPrefix = self::LOG_PREFIX)
    {
        [$cookieName, $cookieValue] = explode('=', $sessionCookieToken);
        if (!isset($cookieName) || !isset($cookieValue) || $cookieName == '' || $cookieValue == '') {
            LogToFile(sprintf("%s::%s:%s bad sessionCookieToken, return false\n", $logPrefix, __METHOD__, __LINE__));
            return false;
        }
        
        if (!self::sessionCookieRecipie($userId, $cnyId, $sessionId, $expectedCookieName, $expectedCookieValue)) {
            LogToFile(sprintf("%s::%s:%s fail on sessionCookieRecipie, return false\n", $logPrefix, __METHOD__, __LINE__));
            return false;
        }
        
        if ($expectedCookieName != $cookieName || $expectedCookieValue !== $cookieValue) {
            LogToFile(
                sprintf(
                    "%s::%s:%s fail on expectedvalues, expCookieName=%s, cookieName=%s, expCookieValue=%s, cookieValue=%s, return false\n",
                    $logPrefix, __METHOD__, __LINE__, $expectedCookieName, $cookieName, $expectedCookieValue, $cookieValue
                )
            );
            return false;
        }

        INTACCTsetcookie($cookieName, $cookieValue, domain: getTLD());
        $_COOKIE[$cookieName] = $cookieValue;

        return true;
    }

    /**
     * Sets the session cookie from the keyring generated by getSessionCookieToken
     *
     * @param string $sessionCookieToken
     * @param int    $loginTimeoutOffset
     * @param string $logPrefix Used to add a prefix to the log messages
     *
     * @return bool
     */
    public static function forceSessionCookieFromToken(
        string $sessionCookieToken,
        int $loginTimeoutOffset,
        string $logPrefix = self::LOG_PREFIX
    ) {
        if (!$sessionCookieToken) {
            LogToFile(sprintf("%s::%s:%d Error, empty sessCookieToken\n", $logPrefix, __METHOD__, __LINE__));
            return false;
        }

        [$cookieName, $cookieValue] = explode('=', $sessionCookieToken);
        if (!isset($cookieName) || !isset($cookieValue) || $cookieName == '' || $cookieValue == '') {
            LogToFile(sprintf("%s::%s:%d Error, empty cookie name or value\n", $logPrefix, __METHOD__, __LINE__));
            return false;
        }

        INTACCTsetcookie($cookieName, $cookieValue, time() + $loginTimeoutOffset, '/', getTLD());
        $_COOKIE[$cookieName] = $cookieValue;

        return true;
    }

    /**
     * Computes the time this session is expected to expire if no further activity occurs on it.
     *
     * @return int Unix timestamp indicating when the session will expire
     */
    public function expiresAt()
    {
        global $g_posix_times_start;

        $ret = 0;

        if ( $this->isUISession() ) {
            // If session is UI based, return inactivity duration + estimated last access time
            // Note, we estimate last access time because we don't have access to actual last access time
            $activityTimeoutDuration = $this->profileHandler->getUserCacheProperty('USERPREF', 'SESSIONTIMEOUT');
            if ($activityTimeoutDuration !== null) {
                $now = time();
                $times = posix_times();
                // Estimated time request received = number of clock ticks at start of request - number of clock ticks since reboot
                // Estimated last access = now - Estimated time request received/100)
                // Clock ticks in centiseconds
                $estimatedLastAccess = $now - intdiv(($times['ticks'] - $g_posix_times_start['ticks']), 100);
                $activityTimeout = $estimatedLastAccess + $activityTimeoutDuration;

                $ret = $activityTimeout;
            }
        } else {
            // If session is API based, return login duration + session birth
            $birth = $this->profileHandler->getProperty('BIRTH');
            if (isset($birth)) {
                $loginTimeoutDuration = $this->profileHandler->getUserCacheProperty('USERPREF', 'LOGINTIMEOUT');
                if ($loginTimeoutDuration !== null) {
                    $loginTimeout = $birth + $loginTimeoutDuration;

                    $ret = $loginTimeout;
                }
            }
        }
        
        return $ret;
    }

    /**
     * Retrieve the list of visible locations in reporting context mode
     *
     * @return int[]
     */
    public function getRModeVisibleLocations()
    {
        $list = $this->getProperty(self::RLOC_CACHE_KEY);
        if ( $list === false || ! is_array($list) ) {
            $list = QueryResult("select locationkey L from sessionloc order by 1");
            array_walk($list, function (&$value) {
                $value = (int) $value['L'];
            });
            $this->setProperty(self::RLOC_CACHE_KEY, $list);
        }

        return $list;
    }

    public function clearRModeLocationsCache()
    {
        $this->getProperty(self::RLOC_CACHE_KEY);
    }

    /**
     * Retrieve the list of visible departments in reporting context mode
     *
     * @return int[]
     */
    public function getRModeVisibleDeparments()
    {
        $list = $this->getProperty(self::RDEPT_CACHE_KEY);
        if ( $list === false || ! is_array($list) ) {
            $list = QueryResult("select deptkey D from sessiondept order by 1");
            array_walk($list, function (&$value) {
                $value = (int) $value['D'];
            });
            $this->setProperty(self::RDEPT_CACHE_KEY, $list);
        }

        return $list;
    }

    public function clearRModeDepartmentsCache()
    {
        $this->getProperty(self::RDEPT_CACHE_KEY);
    }


    /**
     * Initialize the context with the session ID
     *
     * @param $sessionid
     *
     * @return bool
     * @throws IAException
     */
    public static function initContext($sessionid)
    {
        // Turn off possible advanced audit tracking for this authentication.
        /** @noinspection PhpUnusedLocalVariableInspection */
        $restorer = AdvAuditTracking::setManagedTracking(false);

        if (empty($sessionid)) {
            return false;
        }

        //  Set the db id and schema id from session.
        $dbvars = GetDBSchemaFromSession($sessionid);
        SetDBSchemaVars($dbvars[0], $dbvars[1]);
        if ( !ValidSessionSansOID($sessionid, $reason, $warnhr, $warnmin) ) {
            logToFileError('====================>>> ' . $reason . ' <<<====================');
            
            return false;
        }
        Request::$r->_sess = $sessionid;

        // Initialize the app based on the current user/companyid
        InitIMSRemoteAppSession();

        InitRestrictAuthorization();

        return true;
    }

    /**
     *  This function authenticates the API request, given user credentials or
     *  existing session id.  It also sets up various session context data, such as
     *  caches, optional client permission handling, etc.
     *
     * @param string      $sessionid Optional existing session id to
     *                                          use.
     * @param string      $senderid Sender id of the operation.
     * @param bool        $hasContent True if this request has content
     *                                          (if so, permissions etc are set
     *                                          up).
     * @param string|null $userid User id (if sessionid not supplied)
     * @param string|null $companyid Company id (if sessionid not
     *                                          supplied)
     * @param string      $password Password (if sessionid not
     *                                          supplied)
     * @param array       $partnerInfo
     * @param string|null $locationid Optional location id, will only be set if sessionid is passed
     * @param string      $clientid Optional client id (if sessionid
     *                                          not supplied)
     * @param bool        $reuseSession
     * @param bool        $isClearPassword Optional, if false, indicates that the
     *                                          authentication has to be done
     *                                          considering encrypted password
     * @param string      $sessionType  IMS_SESSION_KEY or REST_SESSION_KEY
     * @param bool        $createSession    Optional, if true, indicates that a new session
     * @param bool        $setParentSession Optional, if true, indicates that the parent session
     *
     * @return bool status False for failure, true for success.  If false, errors
     *              are pushed onto gErr stack.
     */
    public static function processAuthentication(
        $sessionid, $senderid, $hasContent, &$userid, &$companyid, &$password,
        &$partnerInfo, &$locationid = null, $clientid = '', $reuseSession = false,
        $isClearPassword = true, string $sessionType = self::IMS_SESSION_KEY,
        bool $createSession = false, bool $setParentSession = false
    ) {
        global $_userid, $gErr;

        $multilevel = false;
        $companies = [];
        $multilevelLocation = '';
        $locationid = $locationid ?? '';

        // Turn off possible advanced audit tracking for this authentication.
        /** @noinspection PhpUnusedLocalVariableInspection */
        $restorer = AdvAuditTracking::setManagedTracking(false);

        $locationkey = '';
        $origCompanyId = $companyid;

        if ( ! $sessionid ) {
            $reuseSession = false;
        }
        //  Check for multi-level slide.
        if ( ! $sessionid && isl_strpos($companyid, '|') !== false ) {
            //  First, validate the companies in the path, and possible ending location.
            if ( Autoslide::extractOptLocation($companyid, $multilevelLocation) === false ) {
                $gErr->addError(
                    'CORE-1054', __FILE__ . '.' . __LINE__,
                    'A company or location in the companyid path does not exist.'
                );

                return false;
                //  Don't allow the location both at the end of the multislide companyid and in the locationid and they are not the same.
            } else if ( !empty($multilevelLocation) && !empty($locationid) && $multilevelLocation !== $locationid) {
                $gErr->addError(
                    'CORE-1055', __FILE__ . '.' . __LINE__,
                    'The location name cannot be given both at the end of the companyid and in the locationid.'
                );

                return false;

                //  If the location is not given in the multilevel path, but is via the locationid, then use
                //   it as the multilevelLocation (whose resolution is deferred until we slide into the final company/client).
            } else if ( $locationid != '' ) {
                $multilevelLocation = $locationid;
            }

            //  Set the current companyid to the first company in the chain, and remember the entire chain for later.
            $companies = explode('|', $companyid);
            $companyid = $companies[0];

            //  Don't allow the client both at the end of the multislide companyid and in the clientid.
            if ( $clientid != '' && $clientid == $companies[count($companies) - 1] ) {
                $gErr->addError(
                    'CORE-1056', __FILE__ . '.' . __LINE__,
                    'The client name cannot be given both at the end of the companyid and in the clientid.'
                );

                return false;
            }

            $multilevel = count($companies) > 1;    // Treat single company|location as non-multillevel.

            //  If there is only one level of company with a location, we don't treat it as multilevel.
            //   But we need to pretend that the locationid was set for the proper location checking.
            if ( ! $multilevel && $multilevelLocation != '' ) {
                $locationid = $multilevelLocation;
            }
        }

        //  If both the clientid and locationid are specified, make this seem like a multislide so the code will work.
        //   Originally, both clientid and locationid could not be provided, so the code couldn't handle it.
        if ( $locationid != '' && $clientid != '' ) {
            /** @noinspection PhpUndefinedVariableInspection */
            if ( count($companies) == 0 ) {
                $companies = [ $companyid, $clientid ];
            } else {
                $companies[] = $clientid;
            }
            $multilevel = true;
            $multilevelLocation = $locationid;
            $clientid = '';                        // Client is now part of companyid.
        }

        //  Use sessionid if present.
        $sourceInfo = null;
        $ret = true;
        if ( $sessionid ) {
            if ( ! $reuseSession ) {
                //  Set the db id and schema id from session.
                $dbvars = GetDBSchemaFromSession($sessionid);
                SetDBSchemaVars($dbvars[0], $dbvars[1]);
                $ret = ValidSessionSansOID($sessionid, $reason, $warnhr, $warnmin);
                // Block the XML API from using the REST API session
                $featureFlag = FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('BLOCK_REST_API_SESSION');
                if ($ret &&
                    isset(Request::$r->_oid) &&
                    $sessionType !== Request::$r->_oid  &&
                    !$createSession &&
                    $featureFlag) {
                    logToFileError("XML API is using the REST API session. This is not allowed.");
                    $reason = 'wrong session type';
                    $ret = false;
                } else {
                    Request::$r->_oid = $sessionType;
                }
            }

            if ( ! $ret ) {
                $ret = false;
                // Add reason to logged message for better error analysis, ticket IA-126588
                $gErr->addIAError(
                    'CORE-1057', __FILE__ . ':' . __LINE__,
                    "Invalid session " . $sessionid . " Reason: " . ($reason ?? "<UNKNOWN>"),
                    ['SESSION_ID' => $sessionid]
                );
            } else {
                $sessionHandler = self::getInstance($sessionid);
                $profileHandler = ProfileHandler::getInstance($sessionHandler);

                //  Set the user id and company id and locationid.
                $userid = $profileHandler->getProperty('login');
                $companyid = $profileHandler->getCompanyCacheProperty('company', 'TITLE');
                $locationid = $profileHandler->getLocationCacheProperty('CONTEXTLOCATION', 'LOCATION_NO');
                $origCompanyId = $companyid;      // Return the extracted companyid to caller.

                //  Populate $locationkey as IASessionHandler::imsSetupSession uses this variable.
                //   For ME shared entity, this is the way we slide via XMLGW
                $locationkey = $profileHandler->getProperty('LOCATIONKEY');
                if ( ! self::checkPortalUser($userid) ) {
                    $ret = false;
                    //  If no errors, get the partner info for use by caller.
                } else {
                    $partnerInfo = self::getPartnerInfo($senderid);
                    $copyEndpoint = $sessionHandler->getEndpoint();
                }
            }
        } //  Convert the userid and company id to a $_userid structure and set the status
        else if ( ( $clientid || $multilevel ) && ( $cny = GetCNYbyTitle($companyid) ) !== false ) {
            $practiceResult = DBRunner::runOnCnyDB(
                [ "\IASessionHandler", "processPracticeAuthentication" ],
                [ $userid, $companyid, $clientid, $isClearPassword, $password, $companies, $multilevelLocation, $senderid, self::LOG_PREFIX, $sessionType ],
                $cny
            );
            if ( ! $practiceResult ) {
                $ret = false;
            } else {
                $userid = $practiceResult['loginId'];
                $sourceInfo = $practiceResult['sourceInfo'];
                $locationkey = $practiceResult['locationKey'];
                Globals::$g->_userid = $practiceResult['globalUserId'];
                SetDBSchema(GetMyCompany(), '');
            }
        } else if ( ! GetUserid($userid, $companyid, $_userid) ) {
            $ret = false;
        } else if ( ! self::checkPortalUser($userid) ) {
            $ret = false;
        } else {
            //  Translate the locationid to locationkey unless this is a multilevel with more than one level.
            //   If that is the case, we want to delay this location translation until we have the correct
            //   context set (in processPracticeAuthentication).
            if ( $locationid != '' ) {
                if ( $multilevel ) {
                    $locationid =
                        '';        // We'll rely on $multilevelLocation passed to processPracticeAuthentication.
                } else {
                    // get the locationkey (LOCATION.RECORD)
                    import('LocationManager');
                    $locMgr = new LocationManager();
                    $locObj = $locMgr->GetRaw($locationid);
                    $locationkey = $locObj[0]['RECORD#'];
                }
            }

            $authMsg = null;

            if ( $isClearPassword ) {
                $isAuthenticated = AuthenticateWithEncryption(
                    $userid, $companyid, $password, $locationid, $locationkey,
                    $authMsg, LOG_SOURCE_API);
            } else {
                // $password = encrypted password
                $isAuthenticated = Authenticate(
                    $userid, $companyid, $password, $locationid, $locationkey,
                    $authMsg);
            }

            if ( ! $reuseSession && ! $isAuthenticated ) {
                // Check the authorization message that ultimately comes from ValidateUser(), and trust it
                if ( StrUtil::isNonEmpty($authMsg) ) {
                    $msg = urldecode($authMsg);
                } else {
                    $msg = I18N::getSingleToken(getInvalidLoginMsg());
                }
    
                $gErr->addError('**********', __FILE__ . '.' . __LINE__, $msg, '', '', 'LOGIN_SECURE');
                $ret = false;
            } else if ( isset($locationid) && $locationid != '' && ! IsLocEntity($locationid) ) {
                $msg = $locationid
                       . " is not a valid entity. Transaction should be owned by an entity and not by a sub-location.";
                $gErr->addIAError('CORE-1060', __FILE__ . '.' . __LINE__, $msg, ['LOCATION_ID' => $locationid]);
                $ret = false;
            } else if ( ! self::isCnySubscribed($companyid, $senderid, $partnerInfo) ) {
                $ret = false;
            } else if ( ! $reuseSession ) {
                // we don't overwrite oid value when we reuse the session
                Request::$r->_oid = $sessionType;
            }
        }

        // Check if the partner identified by it's sender id is active
        if ( $ret && ! self::isPartnerActive($senderid) ) {
            $ret = false;
        }

        // Check the tenant's sender ID whitelist
        if ( $ret && ! self::checkSenderIdWhitelist($senderid, $partnerInfo, $userid, null) ) {
            $ret = false;
        }

        // Check if company is delinquent
        if ( $ret && IsDelinquent() ) {
            Globals::$g->gErr->addIAError(
                'CORE-1061', __FILE__ . '.' . __LINE__,
                "A payment is due for the company id $companyid", [ 'COMPANY_ID' => $companyid]
            );
            $ret = false;
        }

        // Skip this authentication block and remove the error.
        // And move to the next operation on authentication failure
        if ( ! $ret ) {
            return $ret;
        } else if ( ! $reuseSession && $hasContent ) {
            // InitModules must be executed once for each user in the XML request
            //			$this->_ProcessProfile($locationkey);

            // If a valid user session already exists, don't create a new one.
            //			if($_sess != '' && $_userid != '') return true;

            /** @noinspection PhpUnusedLocalVariableInspection */
            $ok = self::initSession(
                $sourceInfo,
                $locationkey,
                senderid: $senderid,
                parent: $setParentSession ? ($sessionid ?? '') : ''
            );

            //  Save the original password in the profile, for possible use by userinfo API calls.
            $profileHandler = ProfileHandler::getInstance();
            $profileHandler->setProperty('origpasswd', $isClearPassword ? EncryptPwd($password) : $password);

            //  Make sure the global admin level is correct through possible multislides.
            $_admin = &Request::$r->_admin;
            $_admin = GetMyAdminLevel();

            // reset all managers one more time - at this point we have finished the Init phase completely
            // so the manager can be initialized with the correct flags - for example GLBatchManager will see
            // correctly the non standard periods flag
            ManagerFactory::PurgeObjectInstances();
            global $gManagerFactory;
            $gManagerFactory->PurgeManagerInstances();
        }

        //  If we were given a session with an api endpoint, make sure any new session has that endpoint.
        if ( ! empty($copyEndpoint) ) {
            $currSession = &Request::$r->_sess;
            if ( $currSession != $sessionid ) {
                $sessionHandler = self::getInstance($currSession);
                if ( $sessionHandler != null ) {
                    $sessionHandler->setEndpoint($copyEndpoint);
                }
            }
        }

        //  Remember the original password for use when creating users.
        global $apiP;
        $apiP = $password;

        // Set the returned companyid back to the original id. Note that for non-multislide, this is a
        //  no-op, but for multislide it is needed so the response will be the correct original company.
        $companyid = $origCompanyId;
        if ( $ret ) {  //TM 63468; CE 12538127
            /** @noinspection PhpUndefinedVariableInspection */
            if ( $sessionHandler == null ) {
                $sess = &Request::$r->_sess;
                $sessionHandler = self::getInstance($sess);
            }
            if ( $sessionHandler != null ) {
                $sessionHandler->ResetSessionBirth();
            }
        }
        Globals::$g->perfdata->monitorCompanyPerfData();
        
        if (!Globals::$g->islive) {
            if (
                !FeatureConfigManagerFactory::getInstance()
                    ->isFeatureEnabled('RELATIONSHIP_REDESIGN_READ_NEW_PLACE')
                && !FeatureConfigManagerFactory::getInstance()
                    ->isFeatureEnabled('RELATIONSHIP_REDESIGN_SKIP_CHECK_FLAG')
            ) {
                $msg = 'Please migrate the company to PRR read new/ write in both stage. ';
                $msg .= 'For guidance, refer to https://intacct.atlassian.net/wiki/spaces/~*********/pages/2339013068/PRR+Overview+and+Migration+process';
                throw new IAException($msg, '**********', 'Company not migrated to PRR');
            }
        }

        return $ret;
    }

    /**
     * Checks the senderId status onfly if the VALIDATE_PARTNER_STATUS configuration parameter  is set to true.
     * Always returns true if the parameter is not specified
     *
     * @param string $senderId
     *
     * @return bool
     */
    public static function isPartnerActive(string $senderId)
    {
        $active = true;
        $validatePartnerStatus = GetValueForIACFGProperty('VALIDATE_PARTNER_STATUS');
        if ( $validatePartnerStatus ) {
            $partnerInfoManager = Globals::$g->gManagerFactory->getManager('imspartnerinfo');
            $partnerInfo = $partnerInfoManager->GetList(
                [
                    'selects' => [ 'STATUS' ],
                    'filters' => [ [ [ 'SENDERID', '=', $senderId ] ] ]
                ]
            );
            $active = ! empty($partnerInfo) && 'active' === $partnerInfo[0]['STATUS'];
            if ( ! $active ) {
                $companyHandler = CompanyCacheHandler::getInstance(null);
                if ( $companyHandler === null ) {
                    // No company found
                    return false;
                }
                $companyTitle = $companyHandler->getProperty('company', 'TITLE');

                Globals::$g->gErr->addIAError(
                    'CORE-1062',
                    __FILE__ . ':' . __LINE__,
                    "Invalid Web Services Authorization",
                    [],
                    "Partner with the Sender ID '$senderId' is not active and cannot make Web Services requests to company ID '$companyTitle'.",
                    ['SENDER_ID' => $senderId, 'COMPANY_TITLE' => $companyTitle],
                    "Use an active partner or contact Support for assistance."
                );
            }
        }

        return $active;
    }

    /**
     * @param string $userid
     *
     * @return bool
     */
    public static function checkPortalUser($userid)
    {
        $portalId = GetValueForIACFGProperty("IA_PORTALUSER_USERID");
        $allowApp = GetValueForIACFGProperty("IA_PORTALUSER_ALLOW_APP");
        if ( ( ! $allowApp || $allowApp != '1' ) && $userid == $portalId ) {
            global $gErr;
            $gErr->addError('**********', __FILE__ . ':' . __LINE__, 'Invalid credentials.');

            return false;
        }

        return true;
    }

    /**
     *
     * @param string $senderid
     *
     * @return array Partner info associative array
     *
     * string senderid     The senderid of the partner to get info about.
     *
     */
    public static function getPartnerInfo($senderid)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $partnerMgr = $gManagerFactory->getManager('imspartnerinfo');
        $partnerInfo = $partnerMgr->getRecordFromSenderId($senderid);

        return $partnerInfo;
    }

    /**
     * @param string   $userid
     * @param string   $companyid
     * @param string   $clientid
     * @param string   $isClearPassword
     * @param string   $password
     * @param string[] $companies
     * @param string   $multilevelLocation
     * @param string   $senderid
     * @param string   $logPrefix
     * @param string   $sessionType
     *
     * @return false|array
     */
    public static function processPracticeAuthentication(
        $userid,
        $companyid,
        $clientid,
        $isClearPassword,
        $password,
        $companies,
        $multilevelLocation,
        $senderid,
        string $logPrefix = self::LOG_PREFIX,
        string $sessionType = self::IMS_SESSION_KEY
    ) {
        global $_userid;

        if ( ! GetUserid($userid, $companyid, $_userid) ) {
            return false;
        }

        $authMsg = null;
        if ( $isClearPassword ) {
            $isAuthenticated = AuthenticateWithEncryption(
                $userid, $companyid, $password, '', $locationkey, $authMsg, LOG_SOURCE_API
            );
        } else {
            // $password = encrypted password
            $isAuthenticated = Authenticate($userid, $companyid, $password, '', $locationkey, $authMsg);
        }
        if ( ! $isAuthenticated ) {
            // Check the authorization message that ultimately comes from ValidateUser(), and trust it
            if ( StrUtil::isNonEmpty($authMsg) ) {
                $msg = urldecode($authMsg);
            } else {
                $msg = urldecode(getInvalidLoginMsg());
            }

            Globals::$g->gErr->addError('**********', __FILE__ . '.' . __LINE__, $msg, '', '', 'LOGIN_SECURE');

            return false;
        }
        if ( ! self::isCnySubscribed($companyid, $senderid, $partnerInfo) ) {
            return false;
        } else {
            // we don't overwrite oid value when we reuse the session
            Request::$r->_oid = $sessionType;
        }

        /** @noinspection PhpUnusedLocalVariableInspection */
        $_sess = Session::getKey();

        if ( ! Profile::exists() ) {
            // Establish the user session and all profile parameters
            /** @noinspection PhpUnusedLocalVariableInspection */
            $session = self::imsSetupSession($_userid, logPrefix: $logPrefix, sessionType: $sessionType, senderid: $senderid);
            
            // Initialize the app based on the current user/companyid
            InitIMSRemoteAppSession();
        }

        //  The client may be specified and/or an array of companies (for a multi-level login).
        //   Combine them into a single array.
        if ( ! is_array($companies) ) {
            $companies = [];
        } else {
            array_shift($companies);        // The first company has already been processed.
        }

        //  If supplied, add the explicit client to the end of the company list.
        if ( $clientid != '' ) {
            $companies[] = $clientid;
        }

        //  Go through all the companies in the list (if just a clientid, only one.  For multilevel slide, could
        //   be more, as in 'A|B|C|D'.
        if (!self::processPracticeAuthRecursive($companies, 0, $multilevelLocation, $sourceInfo, $userid,$locationkey, $sessionType)) {
            return false;
        }

        return [
            'loginId'      => $userid,
            'sourceInfo'   => $sourceInfo,
            'locationKey'  => $locationkey,
            'globalUserId' => $_userid,
        ];
    }

    /**
     *   Make sure they are subscribed to the given access point module (e.g. gateway).  Current version
     *  only supports checking for gateway (not SOAP/REST).  TODO: add support for SOAP/REST checking.
     *
     * @param string $company
     * @param string $senderid
     * @param array  $partnerInfo
     *
     * @return bool
     *
     * company      The company title
     * senderid     Current senderid
     * partnerinfo  Returned partner info
     */
    private static function isCnySubscribed($company, $senderid, &$partnerInfo)
    {
        global $gErr;

        //  Check if intacctready flag is set
        $partnerInfo = self::getPartnerInfo($senderid);
        if ( $partnerInfo && $partnerInfo['INTACCTREADY'] == 'true' ) {
            return true;
        }

        global $gQueryMgr, $kXDAid;
        $qry = [
            'QRY_CNY_GW_SUBSCRIBED' => [
                'QUERY' => "select module.moduleid as ID from module, company where moduleid = '$kXDAid' and company.title = ? and company.record# = module.cny#",
                'ARGTYPES' => ['text'],
            ],
        ];
        $gQueryMgr->LoadQueries($qry);
        $args = [ $company ];
        $ret = $gQueryMgr->DoQuery('QRY_CNY_GW_SUBSCRIBED', $args);

        if ( isset($ret[0]['ID']) ) {
            return true;
        }
        $gErr->addIAError(
            'CORE-1064',
            __FILE__ . ':' . __LINE__,
            "Invalid Web Services Subscription", [],
            "The company ID '$company' is not subscribed to Web Services.", ['COMPANY' => $company],
            "Contact the administrator to subscribe to Web Services."
        );

        return false;
    }

    /**
     * Check the sender ID is authorized to access this tenant
     *
     * @param string $senderId
     * @param array  $partnerInfo
     * @param string $userId
     * @param int    $cny
     *
     * @return bool
     */
    private static function checkSenderIdWhitelist($senderId, &$partnerInfo, $userId, $cny = null)
    {
        // Load the company cache
        $companyHandler = CompanyCacheHandler::getInstance($cny);
        if ( $companyHandler === null ) {
            // No company found
            return false;
        }

        // Grab the company ID from the company cache
        $companyTitle = $companyHandler->getProperty('company', 'TITLE');
        // Grab the white list from the company cache
        $senderIdWhitelist = $companyHandler->getProperty('SENDERIDWHITELIST');
        if ( ! is_array($senderIdWhitelist) ) {
            $senderIdWhitelist = [];
        }

        if ( ! $partnerInfo ) {
            $partnerInfo = self::getPartnerInfo($senderId);
        }
        $companyInfo = $companyHandler->getProperty('company');
        $tenantType = $companyInfo['TYPE'];

        $permissionBitMask = $partnerInfo['PERMISSIONBITMASK'] ?? 0;

        if ( $userId == SUPPORTUSER_LOGINID && IMSPartnerInfoPermissions::SUPPORT_BYPASS & $permissionBitMask ) {
            // Let the 'intacct' support user bypass the whitelist, if this
            // sender ID has the support bypass permission bit
            return true;
        }

        if ( ! self::checkSenderAllowedInTenantType($permissionBitMask, $tenantType) ) {
            Globals::$g->gErr->addIAError(
                'CORE-1065',
                __FILE__ . ':' . __LINE__,
                "Invalid Web Services Authorization",
                [],
                "Sender ID '$senderId' is not authorized to make Web Services requests to company ID '$companyTitle' because"
                . " it is a '$tenantType' type tenant.",
                ['SENDER_ID' => $senderId, 'COMPANY_TITLE' => $companyTitle, 'TENANT_TYPE' => $tenantType],
                "Use a sender ID that is authorized to make requests to '$tenantType' type tenants, or contact Support for assistance.",
                ['TENANT_TYPE' => $tenantType]
            );

            return false;
        } else if ( ! in_array($senderId, $senderIdWhitelist) ) {
            Globals::$g->gErr->addIAError(
                'CORE-1066',
                __FILE__ . ':' . __LINE__,
                "Invalid Web Services Authorization", [],
                "The sender ID '$senderId' is not authorized to make Web Services requests to company ID '$companyTitle'.",
                ['SENDER_ID' => $senderId, 'COMPANY_TITLE' => $companyTitle],
                "Contact the company administrator to grant Web Services authorization to this sender ID."
            );

            return false;
        } else {
            return true;
        }
    }

    /**
     * @param array                 $sourceInfo
     * @param string                $locationkey
     * @param IASessionHandler|null $sessionHandler
     * @param string                $senderid
     * @param string                $parent
     *
     * @return bool
     */
    public static function initSession($sourceInfo, $locationkey, $sessionHandler = null, string $senderid = '', string $parent = '')
    {
        global $_userid;
        $ok = true;

        //  We are initializing a new session. make sure that sess is set to null.
        $sess = '';
        Request::$r->_sess = $sess;
        Profile::cleanHandler();

        //  Reset all managers since they hold an instance variable that is set to the cny# - it could be set to
        //   the parent or previous cny#
        // - we should place this in the InitGlobals or top init functions
        ManagerFactory::PurgeObjectInstances();
        global $gManagerFactory;
        $gManagerFactory->PurgeManagerInstances();

        // Establish the user session and all profile parameters
        self::imsSetupSession($_userid, $parent, $locationkey, sessionType: Request::$r->_oid ?? self::IMS_SESSION_KEY, senderid: $senderid);

        // Initialize the app based on the current user/companyid
        InitIMSRemoteAppSession();

        // This actually needs to be added to the profile, and InitGlobals should be called. But, right now, that will break
        // many places in the gateway that use a global $imsgw_userid as the xml userid , and it gets overwritten by InitGlobals.
        // This is the safest fix for now.
        include_once 'backend_company.inc';
        global $gNonStandardPeriods;
        $gNonStandardPeriods = IsNonStandardPeriods();

        //  If this is a slide, reassert the merged ops calculated earlier.
        if ( $sourceInfo ) {
            //  We need to figure the permissions for mconsole sessions sliding into a role-based company.
            //   Note that we need to do this after setting up the IMS session, so that admin level is correct.
            $_admin = GetMyAdminLevel();
            [ , $cny ] = explode('@', $sourceInfo['origuserid']);
            if ( IsRoleBasedCny($cny) && IsPracticeCompany($cny) ) {
                $sourceInfo['NO_INTERSECTION'] = true;
                $sourceInfo['SLIDEPERMS'] = BuildRolePermsAndAdminPriv($sourceInfo, $_admin);
            }

            $profileHandler = ProfileHandler::getInstance($sessionHandler);

            //  Set the proper permissions based on forced permissions, readonly restrictions, etc.
            addOtherPermissionsToProfile($profileHandler, GetMyAdminLevel(), true, $sourceInfo, 'SLIDEIN');

            if ( $sourceInfo['NO_INTERSECTION'] ) {
                // we're in the case when we slide into a client and the permissions are carried over from parent
                // we need to update the userinfo permission cache in the DB for child sessions to work properly
                $userrec = $profileHandler->getUserrec();
                $ok = SetPermissionsBin($userrec, $profileHandler->getProperty('operations'));
                $ok = $ok && SetPermissionCacheValidity($userrec, true);
            }
        }
        InitRestrictAuthorization();

        return $ok;
    }

    /**
     * @param string[] $companies
     * @param int      $index
     * @param string   $locationid
     * @param array    $sourceInfo
     * @param string   $userid
     * @param string   $locationkey
     * @param string   $sessionType
     *
     * @return bool
     *
     * @throws Exception
     */
    public static function processPracticeAuthRecursive($companies, $index, $locationid, &$sourceInfo, &$userid, &$locationkey, string $sessionType = self::IMS_SESSION_KEY)
    {
        global $_userid;

        $cnt = count($companies);
        if ( $index >= $cnt ) {
            return false;
        }

        $gErr = Globals::$g->gErr;
        $lastCompany = $companies[$cnt - 1];
        $nextCompany = $companies[$index];

        //The user must have either 'Manage Clients' or 'My Clients' permission to perform this operation
        $id1 = GetOperationId('mp/setup/client');
        $id2 = GetOperationId('cl/activities/client');

        $id1access = CheckAuthorization($id1, 'XML');
        $id2access = CheckAuthorization($id2, 'XML');

        if ( ! ( $id1access || $id2access ) ) {
            $gErr->addIAError(
                'CORE-1067', __FILE__ . '.' . __LINE__,
                "This user does not have slide permission for company: " . $nextCompany,
                ['COMPANY_TITLE' =>  $nextCompany]
            );

            return false;
        }

        $_srcop = ( $id1access ) ? $id1 : $id2;

        // Get Client CNY# from TITLE
        $clientcny = LookupCompany($nextCompany);
        $clientcny = $clientcny['RECORD#'];
        if ( ! $clientcny ) {
            $gErr->addError(
                'CORE-1068', __FILE__ . '.' . __LINE__,
                "Unable to determine client record number from client title"
            );

            return false;
        }

        //  If this is the last company and there is a location supplied, we'll need to retry validation
        //   to see if the user has access to this particular location (in the case of restricted users).
        //   We need to translate locationid to locationkey, but to do that, we need to temporarily
        //   reset the client company id into the $_userid, then set it back after fetching the location.
        $useLocationId = null;
        if ( $nextCompany == $lastCompany && $locationid != '' ) {
            $locObj = DBRunner::runOnCnyDB(
                [ 'Util', 'privateFetchRawLocation' ],
                [ $clientcny, $locationid ],
                $clientcny
            );
            $locationkey = $locObj[0]['RECORD#'];
            $useLocationId = $locationid;
        }

        // Get client login credentials for e-practice
        // this will also check whether this user is assigned to this client for slidein
        require_once 'slide.inc';
        if ( ! FetchClientLink(
            $clientcny, $locationkey, $nextCompany, $user, $password, $_attr, $_srcop,
            $sourceInfo
        ) ) {
            $gErr->addIAError(
                'CORE-1069', __FILE__ . '.' . __LINE__,
                "Access to $nextCompany is disabled", ['COMPANY_TITLE' => $nextCompany]
            );

            return false;
        }

        $result = DBRunner::runOnCnyDB(
            [ "\IASessionHandler", "processPracticeAuthDescend" ],
            [
                $user, $nextCompany, $password, $useLocationId, $lastCompany, $companies,
                $index, $locationid, $sourceInfo, $userid, $locationkey, $sessionType
            ],
            $clientcny
        );

        if ( ! $result ) {
            return false;
        } else {
            $userid = $result['loginId'];
            $sourceInfo = $result['sourceInfo'];
            $locationkey = $result['locationKey'];
            $_userid = $result['globalUserId'];

            return true;
        }
    }

    /**
     * Check the sender Id is allowed in this type of tenant
     *
     * @param int $permissionBitMask
     * @param string $tenantType
     *
     * @return bool
     */
    private static function checkSenderAllowedInTenantType($permissionBitMask, $tenantType)
    {
        if (
            ($tenantType == COMPANY_TYPE_PRODUCTION || $tenantType == COMPANY_TYPE_IMAGE)
            && IMSPartnerInfoPermissions::TYPE_PRODUCTION & $permissionBitMask
        ) {
            return true;
        }
        if (
            $tenantType == COMPANY_TYPE_SANDBOX
            && IMSPartnerInfoPermissions::TYPE_SANDBOX & $permissionBitMask
        ) {
            return true;
        }
        if (
            $tenantType == COMPANY_TYPE_IMPLEMENTATION
            && IMSPartnerInfoPermissions::TYPE_IMPLEMENTATION & $permissionBitMask
        ) {
            return true;
        }
        if (
            $tenantType == COMPANY_TYPE_ARCHIVE
            && IMSPartnerInfoPermissions::TYPE_ARCHIVE & $permissionBitMask
        ) {
            return true;
        }
        if (
            $tenantType == COMPANY_TYPE_TEMPLATE
            && IMSPartnerInfoPermissions::TYPE_TEMPLATE & $permissionBitMask
        ) {
            return true;
        }
        if (
            $tenantType == COMPANY_TYPE_DEBUG
            && IMSPartnerInfoPermissions::TYPE_DEBUG & $permissionBitMask
        ) {
            return true;
        }
        if (
            $tenantType == COMPANY_TYPE_SALESDEMO
            && IMSPartnerInfoPermissions::TYPE_SALESDEMO & $permissionBitMask
        ) {
            return true;
        }
        if (
            $tenantType == COMPANY_TYPE_DEMO
            && IMSPartnerInfoPermissions::TYPE_DEMO & $permissionBitMask
        ) {
            return true;
        }
        if (
            $tenantType == COMPANY_TYPE_DEVELOPER
            && IMSPartnerInfoPermissions::TYPE_DEVELOPER & $permissionBitMask
        ) {
            return true;
        }
        if (
            $tenantType == COMPANY_TYPE_SAMPLE
            && IMSPartnerInfoPermissions::TYPE_SAMPLE & $permissionBitMask
        ) {
            return true;
        }
        if (
            $tenantType == COMPANY_TYPE_TRAINING
            && IMSPartnerInfoPermissions::TYPE_TRAINING & $permissionBitMask
        ) {
            return true;
        }
        if (
            $tenantType == COMPANY_TYPE_TEST
            && IMSPartnerInfoPermissions::TYPE_TEST & $permissionBitMask
        ) {
            return true;
        }

        return false;
    }

    /**
     * @param string   $user
     * @param string   $nextCompany
     * @param string   $password
     * @param int      $useLocationId
     * @param string   $lastCompany
     * @param string[] $companies
     * @param int      $index
     * @param string   $locationid
     * @param array    $sourceInfo
     * @param string   $userid
     * @param string   $locationkey
     * @param string   $sessionType
     *
     * @return false|array
     *
     * @throws Exception
     */
    public static function processPracticeAuthDescend(
        $user, $nextCompany, $password, $useLocationId, $lastCompany, $companies, $index,
        $locationid, $sourceInfo, $userid, $locationkey, string $sessionType = self::IMS_SESSION_KEY
    ) {
        global $_userid;
        // we could be in a remote process now. Make sure we're setting the OID for API/IMS/REST
        Request::$r->_oid = $sessionType;

        // populate global $_userid for client company access
        if ( ! GetUserid($user, $nextCompany, $_userid) ) {
            return false;
        }
        if ( ! Authenticate($user, $nextCompany, $password, $useLocationId) ) {
            Globals::$g->gErr->addError(
                'CORE-1070', __FILE__ . '.' . __LINE__,
                "Invalid login credentials",
                "Either the user/password is invalid or the company path is incorrect"
            );

            return false;
        }

        //  If this is not the last company, properly init the session for this intermediate step.
        //   The last company will have its session inited in the calling function.
        if ( $nextCompany != $lastCompany ) {
            if ( self::initSession($sourceInfo, '') == false ) {
                return false;
            }
            if ( ! self::processPracticeAuthRecursive($companies, $index + 1, $locationid, $sourceInfo,
                                                      $userid, $locationkey, $sessionType)
            ) {
                return false;
            }
        } else {
            // Need to return the userid
            $userid = $user;
        }

        return [
            'loginId' => $userid,
            'sourceInfo' => $sourceInfo,
            'locationKey' => $locationkey,
            'globalUserId' => $_userid,
        ];
    }
}

