<?php


/**
 *    FILE:          TierConsolidationHandler.cls
 *    AUTHOR:        <PERSON>iri<PERSON> <<EMAIL>>
 *    DESCRIPTION:   Handler class for Tier Consolidation
 *
 *    (C) 2014, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */

/**
 * Handler class for Global Consolidation
 */
class TierConsolidationHandler extends ConsolidationHandler
{
    /** @var bool $isRollupEntity */
    private $isRollupEntity = false;

    /** @var string $rollUpEntityBookId */
    private $rollUpEntityBookId = '';

    /** @var string $rollUpEntBudgetKey */
    private $rollUpEntBudgetKey = '';

    /** @var string $ownershipPerc */
    private $ownershipPerc = '';

    /** @var string[] $priorPeriodEntityArray */
    private $priorPeriodEntityArray = [];
    
    /** @var string $priorPeriodEntityOwnershipPerc */
    private $priorPeriodEntityOwnershipPerc = '';

    /** @var string $structureName */
    protected $structureName;

    /** @var array $subsidiaryEntityKeys */
    private $subsidiaryEntityKeys = [];

    /** @var array $subsidiaryBookElimAccts */
    private $subsidiaryBookElimAccts = [];
    
    /** @var array $subsAllEntitiesArray */
    private $subsAllEntitiesArray = [];

    /** @var array $bookArray */
    private $bookArray = [];
    
    /** @var array $consEntities */
    private $consEntities = [];

    /** @var array $consBookIDKeyMap */
    private $consBookIDKeyMap = [];

    /** @var bool $isTierConsValidated */
    private $isTierConsValidated = false;

    /** @var array $deletedCSNBooksArray */
    private $deletedCSNBooksArray = [];

    /** @var array $deletedCSNBookBudgetHdrKey */
    private $deletedCSNBookBudgetHdrKey = [];

    /** @var string $consolidationMethod */
    private $consolidationMethod = '';

    /** @var string $subsidiaryRecNo */
    protected $subsidiaryRecNo = '';
    
    /** @var string $parentEntityKey */
    protected $parentEntityKey = '';
    
    /** @var array $parentEntityLocationArray */
    protected $parentEntityLocationArray = [];

    /** @var array $nciGLBatches */
    private $nciGLBatches = [];

    /** @var bool $isMultiPeriodTierCSN */
    private $isMultiPeriodTierCSN = false;

    /** @var string $structureDetailKey */
    private $structureDetailKey = '';
    
    /** @var array $structureInfo */
    private $structureInfo = [];

    // trait that handles all logic related to posting NCI trx
    use TierConsolidationNCITrait;
    
    /**
     * TierConsolidationHandler constructor.
     *
     * @param string      $structureName
     * @param string|null $bookID
     */
    public function __construct(string $structureName, ?string $bookID = null)
    {
        if (isset($structureName)) {
            $this->initializeStructureData($structureName);
        }
        parent::__construct($bookID);
    }

    /**
     * @param string $structureName
     */
    private function initializeStructureData(string $structureName)
    {
        $this->structureName = $structureName;
        $gManagerFactory = Globals::$g->gManagerFactory;
        $ownershipStrMgr = $gManagerFactory->getManager('gcownershipstructure');
        $this->structureInfo = $ownershipStrMgr->Get($this->structureName);
        $this->isEliminateStructByAffEntity = ($this->structureInfo['ELIMINATEBYAFFILIATEENTITY'] == 'true') ? true
            : false;
    }

    /**
     * @param array $params
     * @param bool|false $directConsolidate
     *
     * @return bool
     */
    public function createTierConsolidation(array $params, bool $directConsolidate = false): bool
    {
        $ok = true;
        if ($directConsolidate) {
            $this->cacheTierConsolidationParams($params);
            $ok = $ok && $this->createAtlasConsolidation($params, $directConsolidate);
            // If it is at root, no need to create any jobs.
            // Irrespective of whether jobs are successful or not, we should decrement CSNTIERJOBTRACKER using 
            // pragma autonomous_transaction function

            // In any abrupt termination of job or job dead, then count won't be zero
            // in that case, we do not create jobs for next rollups
            $isLastJobOfCurrLevel = $this->decrementCSNHistoryCount($params['STRUCTUREKEY']);

            // Create next roll-up when
            // 1. If it is last job of current level
            // 2. Delete record from CSNTIERJOBTRACKER
            // 3. Current status is success
            // 4. Next roll-up exists
            // 5. If multiple jobs are there then check status of other jobs, if success then
            if ($isLastJobOfCurrLevel && $this->deleteRecFromCSNTierJobTracker($params['STRUCTUREKEY']) && $ok
                && !empty($params['CONSBOOKORDER'])
                && $this->isCSNHistoryIsSuccess($params)
            ) {
                // Create next roll-up if all conditions are met
                // This method creates entity arrays of all the books in the next level from CSNHISTORY
                // this fetches all the entities from CSNHISTORY along with overridden rates
                // and formats array for consolidation
                $ok = $ok && $this->formatEntitiesFromCSNHistory($params);
                $ok = $ok && $this->runTierConsolidation($params);
                if (!$ok && $this->enableLocking) {
                    // If validation fails for roll-up then do below steps
                    // 1. Update CSNHISTORY status to Failed if any status are in Queue,
                    //  it means, set the roll-up consolidation history to failed, user will get to know that all
                    //  all the subsequent level jobs are failed 
                    // 2. Release structure lock
                    $this->setQueueStatusToFailedForRollups();
                }
            } else {
                // createMultiPeriodConsolidation doesn't get executed for normal tier consolidation
                // and it returns true if it last run which means to release lock
                // or false which means there are consolidation exist for subsequent periods
                $isLastRun = $this->createMultiPeriodConsolidation($ok, $params, $isLastJobOfCurrLevel);
                // There are cases where consolidation process fails irrespective of first level or last level of
                // structure and need to set subsequent period as fail
                $this->validateStructLockBeforeRelease($ok, $params, $isLastJobOfCurrLevel, $isLastRun);
            }
        } else {
            // We don't have to either commit or rollback trx as it is handled at the root level
            // We should not release lock when structure consolidation getting started from leaf
            // We should apply structure lock only when all the validations are success
            $queuedBooksArray = [];
            $ok = $ok && $this->validateNTranslateStructure($params);
            $ok = $ok && $this->fetchOrderOfConsolidationBook($params);
            $ok = $ok && $this->formatCSNHistoryEntityArray($params, $queuedBooksArray);
            $ok = $ok && $this->updateCSNHistoryToQueue($params, $queuedBooksArray);
            $ok = $ok && $this->updateMultiPeriodCSNHistory($params);
            $ok = $ok && $this->runTierConsolidation($params, true);
        }

        if (!$ok) {
            $tempstructureName = isl_htmlspecialchars($this->structureName);
            $this->_Err->addIAError(
                'IGC-0123', __FILE__ . ":" . __LINE__,
                "We were unable to process Tier consolidation request for structure " . $tempstructureName,
                ['STRUCTURE_NAME' => $tempstructureName]
            );
        }
        return $ok;
    }

    /**
     * @param array $params
     * @param array $queuedBooksArray
     *
     * @return bool
     */
    private function formatCSNHistoryEntityArray(array &$params, array &$queuedBooksArray = []): bool
    {
        // In this method
        // 1. We are going to format entity array of entire structure for all the books
        // 2. Even we validate entities passed via API's
        // 3. If user overrides few entities then rest of entities are formatted and fetched rates from Oanda
        // 4. $queuedBooksArray returns roll-up book and entities which has to be insert as 'Queue' in CSNHISTORY 

        // Loop through all the books and format arrays of entities with rates
        $ok = true;
        // top book is always first consolidation book
        $isLeafBook = true;
        foreach ($params['CONSBOOKORDER'] as $books) {
            // There can be multiple books at any level, we should set as in Queue for all the books
            foreach ($books as $bookId) {
                // Do not allow to consolidate if the user has department restriction
                // and validate user has all entities access
                $ok = $ok && $this->isConsolidationAllowed($bookId);
                $glBookLocs = [];
                $bookInfo = [];
                // Below method returns all the entities of book along with 
                // ownership % and if it is parent entity then it's ROLLUPENTBOOKID and book budget id
                $ok = $ok
                    && $this->getEntitiesOfConsBook(
                        $bookId, $params['STRUCTUREDETAILKEY'], $glBookLocs, $bookInfo
                    );

                $bookCurrency = $bookInfo['CURRENCY'];
                $glBookLocsArray = $glBookLocs;
                // check if the book rates are overridden
                // if overridden then remove those entities from $glBookLocs so that we don't have to request OANDA rates
                // if only one element exists then API framework won't make CSNENTITY as array
                $bookIdArray = $params['OVERRIDEEXCHANGERATES'][$bookId] ?? null;
                // if rates are overridden and if only one entity is overridden then make it an array
                //$bookRates is always expected as array and used in loop in other function below hence making it array
                $bookRates = (is_array($bookIdArray) && !is_array($bookIdArray[0]) ? [$bookIdArray] : $bookIdArray) ?? [];

                // Array to keey rates with locationkey
                $bsOverriddenRates = [];
                $waOverriddenRates = [];
                $ok = $ok
                    && $this->validateAndFormatOverriddenRates(
                        $bookCurrency, $bsOverriddenRates, $waOverriddenRates, $bookRates, $glBookLocs,
                        $bookInfo['EENAME']
                    );

                // user should not pass rates while updating consolidation
                // if $glBookLocs is empty then it is complete book is overridden, in that case, we don't have to call 
                // OAnda for any entities
                if (!empty($glBookLocs)) {
                    $ok = $ok
                        && $this->getExchRates(
                            $glBookLocs, $bookCurrency, $this->periodStartDate, $this->periodEndDate, $esRates,
                            $waRates, $bookId
                        );
                }

                // merge Intacct and overriden rates to format complete entity arrays
                $this->mergeIntacctAndOverriddenRates($esRates, $bsOverriddenRates, $waRates, $waOverriddenRates);

                // Send full book entity arrays to format complete entity arrays with rates
                if ($ok) {
                    $csnEntityArray = $this->buildCSNEntityInfo(
                        $glBookLocsArray, $bookCurrency, $esRates, $waRates
                    );

                    // Cache consolidation book and its key map
                    if (empty($this->consBookIDKeyMap[$bookInfo['BOOKID']])) {
                        $this->consBookIDKeyMap[$bookInfo['BOOKID']] = $bookInfo['RECORDNO'];
                    }

                    // If it is leaf books then format it in $this->consEntities as we don't have to update
                    // CSNHISTORY as queue
                    // We don't have keep in params array making it bulky instead we can cache and clear it once it is 
                    // used
                    if ($isLeafBook) {
                        $this->consEntities[$bookId] = $csnEntityArray;
                    } else {
                        $queuedBooksArray[$bookId] = $csnEntityArray;
                    }
                }

                // unset overridden rates array from params to release memory
                unset($params['OVERRIDEEXCHANGERATES'][$bookId]);
                if (!$ok) {
                    $tempBookId = isl_htmlspecialchars($bookId);
                    $this->_Err->addIAError(
                        'IGC-0124', __FILE__ . ":" . __LINE__,
                        "We were unable to fetch entities for book " . $tempBookId
                        . ". Please wait a few minutes and then try again.",
                        ['BOOK_ID' => $tempBookId]
                    );
                    return false;
                }
            }
            if ($isLeafBook) {
                // set leaf book to false after one loop
                $isLeafBook = false;
            }
        }

        $ok = $ok && $this->validateCSNBook($params);
        return $ok;
    }

    /**
     * @param array $params
     *
     * @return bool
     */
    private function validateCSNBook(array $params): bool
    {
        // Validating consolidation books passed via API
        // After formatting each books and entities, we unset $params['OVERRIDEEXCHANGERATES']
        // IF anything left in $params['OVERRIDEEXCHANGERATES'] then it is invalid book
        // This happens only in API
        if (!empty($params['OVERRIDEEXCHANGERATES'])) {
            foreach ($params['OVERRIDEEXCHANGERATES'] as $bookId => $entities) {
                $this->_Err->addIAError(
                    'IGC-0125', __FILE__ . ":" . __LINE__,
                    "Invalid consolidation book id - " . $bookId .
                    ", please verify and try again. ",
                    ['BOOK_ID' => $bookId]
                );
                return false;
            }
        }
        return true;
    }
    
    /**
     * @param string $bookId
     * @param string $structureDetailKey
     * @param array  $glBookLocs
     * @param array  $bookInfo
     *
     * @return bool
     */
    private function getEntitiesOfConsBook($bookId, $structureDetailKey, &$glBookLocs, &$bookInfo = [])
    {
        $gcBookMgr = Globals::$g->gManagerFactory->getManager('gcbook');
        // get book information ie RECORDNO, BOOKID, EENAME, BUDGETID, CURRENCY
        $filter = array(
            'selects' => ['RECORDNO', 'BOOKID', 'EENAME', 'BUDGETID', 'CURRENCY'],
            'filters' => [[['BOOKID', '=', $bookId]]]
        );
        $bookInfo = $gcBookMgr->GetList($filter)[0];

        // Throw error if wrong books are passed
        if (empty($bookInfo)) {
            $tempBookId = isl_htmlspecialchars($bookId);
            $tempStructureName = isl_htmlspecialchars($this->structureName);
            $this->_Err->addIAError(
                'IGC-0126', __FILE__ . ":" . __LINE__, $tempBookId
                . " does not exist in the structure. " . $tempStructureName
                . " or you may not have permission to perform this operation.",
                ['BOOK_ID' => $tempBookId, 'STRUCTURE_NAME' => $tempStructureName]
            );
            return false;
        }

        // get all the entities of each book
        $glBookLocs = $this->getConsolidationBookEntities($bookInfo, $structureDetailKey);
        return true;
    }

    /**
     * @param string $bookId
     *
     * @return array
     */
    private function getCSNHistoryOfBook(string $bookId): array
    {
        // do GetList to fetch entities and rates from csnhistory having state as Queue
        // We are getting order by LOCATIONNAME, this order defines the order of consolidation entities
        // currently, we display and consolidate based on location name in UI
        $gcTierConsolidationMgr = Globals::$g->gManagerFactory->getManager('gctierconsolidation');
        $filter = array(
            'selects' => ['LOCATIONKEY', 'WARATE', 'BSRATE'],
            'filters' => [
                [
                    ['BOOKKEY', '=', $bookId],
                    ['STRUCTURENAME', '=', $this->structureName],
                    ['TIMEPERIOD', '=', $this->periodID]
                ]
            ],
            'orders' => [['LOCATIONNAME']]
        );

        $csnHistoryresults = $gcTierConsolidationMgr->GetList($filter);
        return $csnHistoryresults;
    }
    
    /**
     * @param string[] $esRates
     * @param string[] $bsOverriddenRates
     * @param string[] $waRates
     * @param string[] $waOverriddenRates
     */
    private function mergeIntacctAndOverriddenRates(&$esRates, $bsOverriddenRates, &$waRates, $waOverriddenRates)
    {
        // merge Intacct and overriden rates to format complete entity arrays
        // if there are duplicate rates then overridden takes upper hand
        $esRates = (array)$bsOverriddenRates + (array)$esRates;
        $waRates = (array)$waOverriddenRates + (array)$waRates;
    }
    
    /**
     * @param string   $bookCurrency
     * @param string[] $bsOverriddenRates
     * @param string[] $waOverriddenRates
     * @param array    $bookRates
     * @param array    $glBookLocs
     * @param string   $eeName
     *
     * @return bool
     */
    private function validateAndFormatOverriddenRates(
        $bookCurrency, &$bsOverriddenRates, &$waOverriddenRates, $bookRates = [], &$glBookLocs = [], $eeName = ''
    )
    {
        // This method validates each overridden rates for all the conditions and removes overridden rates from
        // $glBookLocs so that we don't have to get rates for those entities
        global $gCurrencyFormatTenDec;
        $foundEntityIDs = [];
        
        foreach ($bookRates as $entity) {
            $found = false;
            $entName = $this->entityMap[$entity['ENTITYID']]['NAME'];
            if ($entName == '') {
                $tempEntity = isl_htmlspecialchars($entity['ENTITYID']);
                $this->_Err->addIAError(
                    'IGC-0127', __FILE__ . '.' . __LINE__, '', [],
                    "The consolidation request failed because the entity '" . $tempEntity
                    . "'  doesn't exist.",
                    ['ENTITY_ID' => $tempEntity]
                );
                return false;
            }
            foreach ($glBookLocs as $key => $glBookLoc) {
                // very very important to add '===' to filter unwanted entities to get consolidated
                if ($entity['ENTITYID'] === $glBookLoc['LOCATIONID']) {
                    // remove entities from $glBookLocs if it is overridden
                    unset($glBookLocs[$key]);
                    $found = true;
                }
            }

            if (!$found) {
                $tempEntityName = isl_htmlspecialchars($entName);
                $this->_Err->addIAError(
                    'IGC-0128', __FILE__ . ":" . __LINE__, $tempEntityName
                    . " does not exist in the IGC book. or you may not have permission to perform this operation.",
                    ['ENTITY_ID' => $tempEntityName]
                );
                return false;
            }

            if (in_array($entity['ENTITYID'], $foundEntityIDs, true)) {
                $tempEntityName = isl_htmlspecialchars($entName);
                $this->_Err->addIAError(
                    'IGC-0129', __FILE__ . ":" . __LINE__,
                    "Entity must not repeat. Entity '" . $tempEntityName
                    . "' is specified multiple times in the request. ",
                    ['ENTITY_ID' => $tempEntityName]
                );
                return false;
            }

            $foundEntityIDs[] = $entity['ENTITYID'];
            // set overridden rates by locationkey
            $bsOverriddenRates[$this->entityMap[$entity['ENTITYID']]['RECORDNO']] = $entity['BSRATE'];
            $waOverriddenRates[$this->entityMap[$entity['ENTITYID']]['RECORDNO']] = $entity['WARATE'];

            $currency = $this->entityMap[$entity['ENTITYID']]['CURRENCY'];
            if ($currency == $bookCurrency && ($entity['BSRATE'] != 1 || $entity['WARATE'] != 1)) {
                $tempEntityName = isl_htmlspecialchars($entName);
                $tempBookCurrency = isl_htmlspecialchars($bookCurrency);
                $this->_Err->addIAError(
                    'IGC-0130', __FILE__ . '.' . __LINE__, '', [],
                    "The Weighted Average Rate and The Ending Spot Rate must be 1 for entity "
                    . $tempEntityName . " as it's currency is same as book currency, which is " . $tempBookCurrency
                    . ".", ['ENTITY_NAME' => $tempEntityName, 'BOOK_CURRENCY' => $tempBookCurrency]
                );
                return false;
            }
            
            if ($entity['ENTITYID'] == $eeName) {
                if ($entity['BSRATE'] != 1) {
                    $this->_Err->addError(
                        'IGC-0131', __FILE__ . '.' . __LINE__, '',
                        "The Ending Spot Rate for elimination entity must specify as 1."
                    );
                    return false;
                }
                if ($entity['WARATE'] != 1) {
                    $this->_Err->addError(
                        'IGC-0132', __FILE__ . '.' . __LINE__, '',
                        "The Weighted Average Rate for elimination entity must specify as 1."
                    );
                    return false;
                }
            }

            if (self::$isMeSharedCompany && ($entity['BSRATE'] != 1 || $entity['WARATE'] != 1)) {
                $this->_Err->addError(
                    'IGC-0133', __FILE__ . '.' . __LINE__, '',
                    "The Weighted Average Rate and The Ending Spot Rate must specify as 1 for Multi-entity Shared companies."
                );
                return false;
            }

            if (!is_numeric($entity['BSRATE']) || $entity['BSRATE'] <= 0
                || !preg_match(
                    "$gCurrencyFormatTenDec", $entity['BSRATE']
                )
            ) {
                $tempEntityName = isl_htmlspecialchars($entName);
                $this->_Err->addIAError(
                    'IGC-0134', __FILE__ . '.' . __LINE__, '', [],
                    "The Ending Spot Rate for entity " . $tempEntityName . " is invalid. "
                    . " Please specify a different rate for the consolidation request.",
                    ['ENTITY_NAME' => $tempEntityName]
                );
                return false;
            }

            if (!is_numeric($entity['WARATE']) || $entity['WARATE'] <= 0
                || !preg_match(
                    "$gCurrencyFormatTenDec", $entity['WARATE']
                )
            ) {
                $tempEntityName = isl_htmlspecialchars($entName);
                $this->_Err->addIAError(
                    'IGC-0135', __FILE__ . '.' . __LINE__, '', [],
                    "The Weighted Average Rate for entity " . $tempEntityName .
                    " is invalid. Please specify a different rate for the consolidation request.",
                    ['ENTITY_NAME' => $tempEntityName]
                );
                return false;
            }
        }

        return true;
    }

    /**
     * @param array $params
     * @param array $queuedBooksArray
     *
     * @return bool
     */
    private function updateCSNHistoryToQueue(array $params, array $queuedBooksArray): bool
    {
        // In this method, we update CSNHISTORY status as Queue for all the roll-up books
        // $queuedBooksArray keep roll-up books and entity arrays
        // First delete CSNHISTORY records for each entity and 
        // insert new row as Queue.
        // Delete only entities which are there in $queuedBooksArray array, if any entity is deleted from
        // structure then let's not delete that from CSNHISTORY
        // Later we will build the logic to delete trx of deleted entities

        // no transaction commit here, will commit once everything goes well
        // This is to cache $this->userName
        $this->initLogAndUserInfo();
        $ok = true;
        $bulkInsertArray = [];

        // loop through each book in $queuedBooksArray, these are roll-up books
        foreach ($queuedBooksArray as $bookId => $entityArray) {
            // Each book will have several entities, loop through it
            // delete record from CSNHISTORY if it is already consolidated and insert into CSNHISTORY one by one
            // delete only that period data and during consolidation we take care of updating subsequent periods
            // We use PrepINClauseStmt to delete all entities at once
            $entityRecNoArray = [];
            foreach ($entityArray as $entity) {
                $entityRecNoArray[] = $this->entityMap[$entity['ENTITYID']]['RECORDNO'];
                // Creating Bulk Insert array here to avoid another loop
                $bulkInsertArray[] = [
                    $this->cny, // CNY#
                    $bookId,    // BOOKKEY
                    $this->entityMap[$entity['ENTITYID']]['RECORDNO'],  // LOCATIONKEY
                    $this->periodID,    // TIMEPERIOD
                    $this->reportingPeriodName, // PERIODNAME
                    $this->userName,    // STARTEDBY
                    $entity['WARATE'],  // WARATE
                    $entity['BSRATE'],  // BSRATE
                    self::CSN_IN_QUEUE_INTERNAL,   // STATUS
                    I18N::getSingleToken(self::QUEUE_MESSAGE),    // MESSAGE
                    $params['STRUCTUREKEY'], // STRUCTUREKEY
                    $this->consBookIDKeyMap[$bookId] // GCBOOKKEY
                ];
            }
            $ok = $ok && $this->deleteEntityFromCSNHISTORY($entityRecNoArray, $bookId);
            if (!$ok) {
                return $ok;
            }
        }

        // Will do bulk insert to insert into csnhistory table as single insert is expensive
        $ok = $ok && $this->bulkInsertCSNHISTORY($bulkInsertArray);
        unset($bulkInsertArray);

        // for the current level or first level also, let's delete csnhistory if it is already consolidated
        // We insert into csnhistory as Offline in progress while creating job
        // There can be multiple books at leaf level so let's loop through each book
        foreach ($this->consEntities as $bookId => $entities) {
            // Each book will have several entities, loop through it and delete one by one
            $entityRecNoArray = [];
            foreach ($entities as $entity) {
                $entityRecNoArray[] = $this->entityMap[$entity['ENTITYID']]['RECORDNO'];
            }
            // We use PrepINClauseStmt to delete all entities at once
            $ok = $ok && $this->deleteEntityFromCSNHISTORY($entityRecNoArray, $bookId);
            if (!$ok) {
                return $ok;
            }
        }
        return $ok;
    }

    /**
     * @param array $entityRecNoArray
     * @param string   $bookId
     *
     * @return bool
     */
    private function deleteEntityFromCSNHISTORY(array $entityRecNoArray, string $bookId): bool
    {
        $ok = true;
        if (!empty($entityRecNoArray)) {
            $stmt = "DELETE FROM CSNHISTORY WHERE CNY# = :1 AND BOOKKEY = :2 AND TIMEPERIOD = :3 AND 
                        STATUS NOT IN (:4, :5)";
            $stmt = [
                $stmt, $this->cny, $bookId, $this->periodID, self::CSN_OFFLINE_INTERNAL, self::CSN_IN_PROGRESS_INTERNAL
            ];
            $stmt = PrepINClauseStmt($stmt, $entityRecNoArray, " and LOCATIONKEY ");
            $ok = $ok && ExecStmt($stmt);
            if (!$ok) {
                $tempBookId = isl_htmlspecialchars($bookId);
                $this->_Err->addIAError(
                    'IGC-0136', __FILE__ . ":" . __LINE__,
                    "We were unable to delete consolidation history for book " . $tempBookId . ". 
                    Please wait a few minutes and then try again.",
                    ['BOOK_ID' => $tempBookId]
                );
                return $ok;
            }
        }

        return $ok;
    }

    /**
     * @param array $bulkInsertArray
     *
     * @return bool
     */
    private function bulkInsertCSNHISTORY(array $bulkInsertArray): bool
    {
        $ok = true;
        // Inserting CSNHISTORY record as Queue for roll-up books
        // Will do bulk insert to insert into csnhistory table as single insert is expensive
        if (!empty($bulkInsertArray)) {
            // BULK INSERT NOW....
            $bulkArr = [];
            $bulkArr[0]
                = "INSERT INTO CSNHISTORY (CNY#, BOOKKEY, LOCATIONKEY, TIMEPERIOD, PERIODNAME, ACTIONTIME, STARTEDBY, WARATE, BSRATE, STATUS, MESSAGE, RECORD#, STRUCTUREKEY, GCBOOKKEY) VALUES (:1(i), :2(i), :3(i), :4(i), :5(i), CURRENT_TIMESTAMP at TIME zone 'GMT', :6(i), :7(i), :8(i), :9(i), :10(i), GET_NEXTRECORDID(:1(i), 'CSNHISTORY'), :11(i), :12(i))";
            $bulkArr[1] = $bulkInsertArray;
            $bulkArr[2] = [
                "integer", "text", "integer", "integer", "text", "timestamp", "text", "decimal", "decimal", "text",
                "text", "integer", "integer", "integer"
            ];
            $ok = $ok && ExecBulkStmt($bulkArr);
            if (!$ok) {
                $tempStructureName = isl_htmlspecialchars($this->structureName);
                $this->_Err->addIAError(
                    'IGC-0137', __FILE__ . ":" . __LINE__,
                    "We were unable to insert consolidation history for structure " . $tempStructureName . ".
                     Please wait a few minutes and then try again.",
                    ['STRUCTURE_NAME' => $tempStructureName]
                );
            }
        }
        return $ok;
    }

    /**
     * @param array $params
     *
     * @return bool
     */
    private function formatEntitiesFromCSNHistory(array &$params): bool
    {
        // Overridden & Intacct rates are stored in CSNHISTORY so we have to build entity array from CSNHISTORY
        // This method creates entity arrays of all the books in the next level from CSNHISTORY
        // this fetches all the entities from CSNHISTORY having status as Queue along with overridden rates
        $bookOrder = $params['CONSBOOKORDER'];
        // unset entities array as we need to create entities array for next roll-ups and we don't need
        // already consolidated books entities
        unset($params['ENTITIES']);
        $topBooks = array_shift($bookOrder);
        $ok = true;

        // loop through each book as there can be multiple books at any level
        foreach ($topBooks as $bookId) {
            $csnHistoryresults = $this->getCSNHistoryOfBook($bookId);
            $bookEntities = [];
            $entityRecNoArray = [];
            $glBookLocs = [];

            $ok = $ok && $this->getEntitiesOfConsBook($bookId, $params['STRUCTUREDETAILKEY'], $glBookLocs);

            foreach ($csnHistoryresults as $csnHistoryresult) {
                // Loop through each entity of CSNHISTORY because this will hold any deleted entities not $glBookLocs
                // find same entity in $glBookLocs and format the result
                foreach ($glBookLocs as $glBookLoc) {
                    // very very important to add '===' to filter unwanted entities which get consolidated
                    if ($glBookLoc['LOCATIONKEY'] === $csnHistoryresult['LOCATIONKEY']) {
                        $bookEntities[] = $this->buildCSNEntityArray(
                            $glBookLoc, $csnHistoryresult['BSRATE'], $csnHistoryresult['WARATE']
                        );
                        $entityRecNoArray[] = $csnHistoryresult['LOCATIONKEY'];
                    }
                }
            }
            // We use PrepINClauseStmt to delete all entities at once in each book
            // Deleting csnhistory records for roll-up to avoid multiple entries which are in Queue
            $ok = $ok && $this->deleteEntityFromCSNHISTORY($entityRecNoArray, $bookId);
            if (!$ok) {
                return $ok;
            }
            $this->consEntities[$bookId] = $bookEntities;
        }
        return $ok;
    }
    
    /**
     * @param array $params
     *
     * @return bool
     */
    private function validateNTranslateStructure(array &$params): bool
    {
        $structDetailMgr = Globals::$g->gManagerFactory->getManager('gcownershipstructuredetail');
        $ok = $this->validateCommondFields($params);
        $ok = $ok && $structDetailMgr->validateStructureForConsolidation($params, $this->periodID);
        // If multi period consolidation then set last consolidation period of given structure
        $ok = $ok && $this->isMultiPeriodConsolidation($params);
        return $ok;
    }

    /**
     * @param array $params
     *
     * @return bool
     */
    private function isMultiPeriodConsolidation(array &$params): bool
    {
        // UPDATESUCCEEDINGPERIODS is no longer provided by user, as there is no delete and consolidate option
        // system should be intelligent enough to determine whether it is one period consolidation
        // or multi period and if it is multi period then always it is upsert, no more delete
        if (!isset($params['UPDATESUCCEEDINGPERIODS'])
            || isset($params['UPDATESUCCEEDINGPERIODS'])
            && $params['UPDATESUCCEEDINGPERIODS'] != 'T'
        ) {
            $lastCSNPeriod = $this->getLatestCSNPeriod($params);
            // Once UPDATESUCCEEDINGPERIODS is set to T, we don't have to fetch LASTCSNPERIOD for each multi period
            // consolidation
            if ($this->periodID < $lastCSNPeriod) {
                $params['UPDATESUCCEEDINGPERIODS'] = 'T';
                $params['LASTCSNPERIOD'] = $lastCSNPeriod;
                $params['STARTCSNPERIOD'] = $this->periodID;
            }
        }
        return true;
    }

    /**
     * @param array $params
     */
    private function cacheTierConsolidationParams(array $params)
    {
        $this->structureDetailKey = $params['STRUCTUREDETAILKEY'];
        $this->cachePriorPeriodOwnershipPercByEntity($params);
        $this->cacheMultiPeriodTierCons($params['UPDATESUCCEEDINGPERIODS'] ?? '');
        $this->cacheIsRecurrence($params);
    }

    /**
     * @param array $params
     */
    private function cacheIsRecurrence(array $params)
    {
        if (isset($params['ISRECURRENCE']) && $params['ISRECURRENCE']) {
            $this->isRecurrence = true;
        }
    }
    
    /**
     * @param array $params
     */
    private function cachePriorPeriodOwnershipPercByEntity(array $params)
    {
        // We should apply ownership perc calculation while fetching prev ACCRUAL balance and
        // not for consolidation balance irrespective of roll-up condition
        // If prior period (detail) not found then we should apply current period ownership %
        // and for parent entity and elimination entity ownership % is always 100% hence no need to apply ownership perc
        
        // Get all the entities and ownership percentage of prior period by given book and cache it in class variable
        // We have to fetch with respect to each entity not by book
        // Example: if any entity is added later, it has to consolidate as the period consolidation by
        // applying ownership % calculation to Prev ACCRUAL balance

        // Here we are going to cache prior period ownership percentage for each entities.
        // If the consolidation method is selected, no need to get prior period ownership percentage.
        $gManagerFactory = Globals::$g->gManagerFactory;

        $ownershipDetailMgr = $gManagerFactory->getManager('gcownershipstructuredetail');
        $periodID = Date2Period(ReformatDate($params['STARTDATE'], IADATE_STDFORMAT, '/mdY'));
        $priorPeriodStructureDetailKey = $ownershipDetailMgr->getPriorPeriodStructureRecNo(
            $params['STRUCTURENAME'], $periodID
        );

        // avoid PHP 8.1 strict compatible check adding explicit condition
        if ($priorPeriodStructureDetailKey != '') {
            $gcOwnershipEntityMgr = $gManagerFactory->getManager('gcownershipentity');
            $filter = [
                'selects' => ['RECORDNO'],
                'filters' => [
                    [
                        ['GCOWNERSHIPDETAILKEY', '=', $priorPeriodStructureDetailKey],
                        ['BOOK.BOOKID', '=', $params['BOOKID']]
                    ],
                ],
            ];

            // Get parent entity record# of given structure detail key and book id
            $gcOwnershipEntityRecNo = $gcOwnershipEntityMgr->GetList($filter);

            // as we just need ownership % better to do getlist than calling entire get of the object
            if ($gcOwnershipEntityRecNo && isset($gcOwnershipEntityRecNo[0]['RECORDNO'])) {
                $parentEntityRecNo = $gcOwnershipEntityRecNo[0]['RECORDNO'];

                // Here we are going to cache prior period ownership percentage for each entities.
                // If the consolidation method is selected, no need to get prior period ownership percentage.
                // Get all the distinct child entities from all the details
                $gcOwnershipChildEntityMgr = $gManagerFactory->getManager('gcownershipchildentity');
                $filter = [
                    'selects' => ['ENTITYKEY', 'OWNERSHIPPERCENTAGE'],
                    'filters' => [
                        [
                            ['GCOWNERSHIPENTITYKEY', '=', $parentEntityRecNo],
                            ['CONSOLIDATIONMETHOD', '!=', GCOwnershipChildEntityManager::CONSOLIDATION_VALUE]
                        ]
                    ]
                ];

                $gcBookEntities = $gcOwnershipChildEntityMgr->GetList($filter);

                foreach ($gcBookEntities as $val) {
                    $this->priorPeriodEntityArray[$val['ENTITYKEY']] = $val['OWNERSHIPPERCENTAGE'];
                }
            }
        }
    }

    /**
     * @param bool  $ok
     *
     * @param array $params
     *
     * @param bool  $isLastJobOfCurrLevel
     *
     * @return bool
     */
    private function createMultiPeriodConsolidation(bool $ok, array $params, bool $isLastJobOfCurrLevel): bool
    {
        $isLastRun = true; // TRUE => Last iteration; FALSE => Need to consolidate next period.
        // create multi period consolidation job for next period when
        // 1. If updatesucceedingperiods is set to 'T'
        // 2. If it is last job of current level and there is no roll-up exists
        // 3. If it is last job of current level and consolidation is success for the current job
        // 4. If it is last job of current level and consolidation is success for all the parallel jobs
        // 5. If next consolidation period < latest consolidation period in this structure
        if (isset($params['UPDATESUCCEEDINGPERIODS']) && $params['UPDATESUCCEEDINGPERIODS'] == 'T'
            && $isLastJobOfCurrLevel
            && empty($params['CONSBOOKORDER'])
            && $ok
            && $this->isCSNHistoryIsSuccess($params)
        ) {
            $execDate = $params['EXECDATE']; //The DATE on which consolidation has to run now.
            $execPeriod = Date2Period($execDate);
            if ($params['LASTCSNPERIOD'] > $execPeriod) {
                $isLastRun = false;
            }

            // If it is not last run then
            // 1. set STARTDATE = next period
            // 2. Call createTierConsolidation to create next period consolidation
            // If it is last run then no need to do anything as we release lock in subsequent method validateStructLockBeforeRelease
            if (!$isLastRun) {
                [$stdt] = Period2Dates($execPeriod + 1);
                $imsParams = $this->formatArrayForMultiPeriodCSN($params, $stdt);
                // After this method we call validateStructLockBeforeRelease where we catch all errors and set the status 
                // in CSNHISTORY and release lock 
                // If something fails in method createTierConsolidation for next period, we have taken care of it  
                $ok = $this->createTierConsolidation($imsParams);
                // If any error happens then set $isLastRun as true so that we release lock and set the error message
                // in CSNHISTORY
                if (!$ok) {
                    // Whenever system triggers multi period consolidation for next period,
                    // 1. It validates structure, structure details, and state etc
                    // 2. Each entities, it's histories, rates and many more
                    // In any failures, we should update the csnhistory to failed for the next period
                    // so that customers will get to know the issue Ex: structure is draft etc
                    $this->setNextPeriodCSNHistoryToFailed($imsParams['STARTDATE']);
                    $isLastRun = true;
                }
            }
        }
        return $isLastRun;
    }

    /**
     * @param array  $params
     * @param string $stdt
     *
     * @return array
     */
    private function formatArrayForMultiPeriodCSN(array $params, string $stdt): array
    {
        // In this method, format array for Multi period consolidation
        // We keep structure lock at the beginning of tier consolidation and no need to release on every period consolidation
        // during multi period consolidation, so we set SKIPVALIDATINGSTRUCTURELOCK as T
        $imsParams = [
            'STRUCTURENAME' => $params['STRUCTURENAME'],
            'STARTDATE' => $stdt,
            'UPDATESUCCEEDINGPERIODS' => 'T',
            'CHANGESONLY' => 'T',
            'OFFLINE' => 'T',
            'LASTCSNPERIOD' => $params['LASTCSNPERIOD'],
            'STARTCSNPERIOD' => $params['STARTCSNPERIOD'],
            'SKIPVALIDATINGSTRUCTURELOCK' => 'T',
            'EMAIL' => $params['EMAIL'],
            'OLDSTRUCTUREDETAILKEY' => $params['STRUCTUREDETAILKEY'],
            'OLDCONSBOOKORDER' => $params['OLDCONSBOOKORDER'],
            'ISRECURRENCE' => $params['ISRECURRENCE']
        ];
        return $imsParams;
    }
    
    /**
     * @param bool  $ok
     *
     * @param array $params
     *
     * @param bool  $isLastJobOfCurrLevel
     * 
     * @param bool  $isLastRun
     */
    private function validateStructLockBeforeRelease(
        bool $ok, array $params, bool $isLastJobOfCurrLevel, bool $isLastRun
    )
    {
        // Release structure lock when
        // 1. If it is last job of current level and there is no roll-up exists
        // 2. If it is last job of current level and consolidation fails
        // this works for parallel or non-parallel jobs
        // 3. In parallel run, first job fails, but we should release lock at the end of last job
        // check all parallel jobs are success or not
        $isUpdateCSNHistoryToFailed = $isLastRun && $isLastJobOfCurrLevel
            && (empty($params['CONSBOOKORDER']) || !$ok
                || $this->isCSNHistoryIsSuccess(
                    $params
                ));
        // There are cases where consolidation process fails irrespective of first level or last level of
        // structure and need to set subsequent period as fail
        if ($this->enableLocking && ($isUpdateCSNHistoryToFailed || !$ok)) {
            // If conditions are met then follow the following steps
            // 1. Update CSNHISTORY status to Failed if any status are in Queue,
            //  it means, set the roll-up consolidation history to failed, user will get to know that all
            //  all the subsequent level jobs are failed 
            // 2. Release structure lock

            // Send the summary email if email is sent
            $this->sendSummaryEmailForStructureConsolidation($params);
            $this->setQueueStatusToFailedForRollups();
        }
    }
    
    /**
     * Add tier consolidation specific validation in this method
     *
     * @param array      &$params
     *
     * @return bool
     */
    protected function validateCommondFields(array &$params): bool
    {
        //For tier consolidation OFFLINE is always true and CHANGESONLY is always true, no delete
        // Keeping this method alive, in future, if any tier specific validation comes then will place it here
        $params['OFFLINE'] = 'T';
        $params['CHANGESONLY'] = 'T';
        // generally this method gets called twice.
        // 1. Once tier consolidation is initiated
        // 2. When each books are validated in prepValues method
        // Agenda is, let us validate parameters first, if everything is good then do further processing
        // So we don't have to validate two or multiple times and we are avoiding it by caching value after first validation
        if (!$this->isTierConsValidated) {
            $this->isTierConsValidated = true;
            return parent::validateCommondFields($params);
        }
        return true;
    }

    /**
     * @return bool
     */
    private function validateNSetStructureLock(): bool
    {
        //set memcache lock for structure
        $structLockName = $this->getStructureLockName();
        if ($this->enableLocking && Lock::lockHeld($structLockName)) {
            $tempStructureName = isl_htmlspecialchars($this->structureName);
            $this->_Err->addIAError(
                'IGC-0138', __FILE__ . ":" . __LINE__,
                "We were unable to process your consolidation because another request is currently "
                . "running a consolidation for this structure " . $tempStructureName
                . ". Wait until the consolidation has finished, and then try running your consolidation again.",
                ['STRUCTURE_NAME' => $tempStructureName]
            );
            return false;
        } else {
            if ($this->enableLocking) {
                $lock = new Lock();
                $lock->setClearLockOnDestroy(false);
                $lock->setLock($structLockName, 86400, false);
            }
            return true;
        }
    }

    /**
     * decrementCSNHistoryCount returns true if current job is last parallel job else returns false
     *
     * @param string $structureKey
     *
     * @return bool
     */
    private function decrementCSNHistoryCount(string $structureKey): bool
    {
        // In any abrupt termination of job or job dead, then count won't be zero
        // in that case, we do not create jobs for next rollups
        $res = QueryResult(
            array(
                'SELECT DECREMENT_CSNTIERJOBTRACKER(:1, :2) JOBSTOBECOMPLETED FROM DUAL',
                $this->cny, $structureKey
            )
        );

        return ($res[0]['JOBSTOBECOMPLETED']) == 0 ? true : false;
    }

    /**
     * @param string $structureKey
     *
     * @return bool
     */
    private function deleteRecFromCSNTierJobTracker(string $structureKey): bool
    {
        // delete record from CSNTIERJOBTRACKER
        $args = [];
        $args[] = "DELETE FROM CSNTIERJOBTRACKER WHERE CNY# = :1 AND STRUCTUREKEY = :2";
        $args[] = $this->cny;
        $args[] = $structureKey;
        $ok = ExecStmt($args);
        if (!$ok) {
            $this->_Err->addError(
                'IGC-0139', __FILE__ . ":" . __LINE__,
                "We are unable to delete record from CSNTIERJOBTRACKER table. Please wait a few minutes and then try again."
            );
        }
        return $ok;
    }


    /**
     * @param array $params
     *
     * @return bool
     */
    private function isCSNHistoryIsSuccess(array $params): bool
    {
        // PARALLELJOBS is set only for parallel jobs and won't be set for normal jobs
        // For normal jobs, no need to check the status
        if (isNullOrBlank($params['PARALLELJOBS'])) {
            return true;
        }

        $gManagerFactory = Globals::$g->gManagerFactory;
        $gcTierConsolidationMgr = $gManagerFactory->getManager('gctierconsolidation');

        $filter = array(
            'selects' => array(
                array(
                    'fields' => array('RECORDNO'),
                    'function' => 'count(${1})'
                )
            ),
            'filters' => array(
                array(
                    array('CSNSTATUS', '!=', self::CSN_SUCCESS),
                    array('TIMEPERIOD', '=', $this->periodID),
                    array('BOOKKEY', 'IN', $params['PARALLELJOBS'])
                )
            ),
            'columnaliases' => array('CNT')
        );

        $result = $gcTierConsolidationMgr->GetList($filter);
        return ($result[0]['CNT']) > 0 ? false : true;
    }

    /**
     * @param array $params
     *
     * @param false|bool $isStructLeafConsolidation
     *
     * @return bool
     */
    private function runTierConsolidation(array $params, bool $isStructLeafConsolidation = false): bool
    {
        // Here we create Offline jobs for parallel or single books
        $topBooks = array_shift($params['CONSBOOKORDER']);

        $ok = $this->validateConsJobBeforeCreation($params, $topBooks);

        if ($ok) {
            // We should put structure lock only when
            // 1. all the validation are success
            // 2. ready to create IMS job
            // 3. only for Structure getting consolidation from leaf
            // 4. when multi period consolidation roll-up not running
            if ($isStructLeafConsolidation
                && !(isset($params['UPDATESUCCEEDINGPERIODS']) && $params['UPDATESUCCEEDINGPERIODS'] == 'T'
                    && $params['SKIPVALIDATINGSTRUCTURELOCK'] == 'T')
            ) {
                $ok = $ok && $this->validateNSetStructureLock();
            }

            // While creating jobs, we decided first to validate all the jobs and if all are success then only
            // 1. Delete row if exists and Update count variable using autonomous_transaction function
            // 2. Will create IMS job
            // There is no difference between parallel and non-parallel, both execute same steps

            $ok = $ok && $this->createCSNTierJobTrackerRecord($params['STRUCTUREKEY']);
            foreach ($this->bookArray as $param) {
                $bookLockName = $this->getBookLockNameForTier($param['BOOKID']);
                $this->formatArrayForParallelJobs($param, $topBooks);
                $ok = $ok && parent::createConsolidationIMSJob($param, $bookLockName);
            }

            // As this array is large in size, so we are releasing memory
            $this->bookArray = [];

            if (!$ok) {
                $tempStructureName = isl_htmlspecialchars($params['STRUCTURENAME']);
                $this->_Err->addIAError(
                    'IGC-0140', __FILE__ . ":" . __LINE__,
                    "We are unable to create Offline job for structure " . $tempStructureName . " .
                     Please wait a few minutes and then try again.",
                    ['STRUCTURE_NAME' => $tempStructureName]
                );
            }
        } else {
            $this->_Err->addError(
                'IGC-0141', __FILE__ . ":" . __LINE__,
                "We are unable to process the offline consolidation at this time. Please wait "
                .
                "a few minutes and then try again"
            );
        }

        return $ok;
    }

    /**
     * @param array  $params
     *
     * @param string $bookLockName
     *
     * @return bool
     */
    protected function createConsolidationIMSJob(array $params, string $bookLockName = ''): bool
    {
        // While creating parallel jobs, we decided first to validate all the jobs and if all are success then only
        // and update count variable using autonomous_transaction function and will create IMS job
        // For Non-parallel it makes no difference
        $this->bookArray[] = $params;
        return true;
    }

    /**
     * @param array $params
     * @param array $topBooks
     *
     * @return bool
     */
    private function validateConsJobBeforeCreation(array &$params, array $topBooks): bool
    {
        $ok = true;
        foreach ($topBooks as $bookId) {
            $ok = $ok && $this->formatArrayBeforeConsolidation($params, $bookId);
            // Create Offline tier consolidation jobs
            $ok = $ok && $this->createAtlasConsolidation($params);
        }
        return $ok;
    }

    /**
     * @param array    $param
     *
     * @param array $topBooks
     */
    private function formatArrayForParallelJobs(array &$param, array $topBooks)
    {
        // If count($topBooks) > 1 then it has parallel jobs
        if (count($topBooks) > 1) {
            $param['PARALLELJOBS'] = $topBooks;
            // In the case of parallel jobs, we first validation all books so last validating book gets cached
            // over-writing other book data in updateHistory
            $this->conBookName = $param['BOOKID'];
        }
    }

    /**
     * @param string $structurekey
     *
     * @return bool
     */
    private function createCSNTierJobTrackerRecord(string $structurekey): bool
    {
        // Irrespective or parallel or non-parallel jobs, we insert into CSNTIERJOBTRACKER
        // IF there is no structure lock and jobs are dead or terminated abruptly
        // Then delete record if exists and re-insert
        // delete record from CSNTIERJOBTRACKER if exists and re-insert
        $ok = $this->deleteRecFromCSNTierJobTracker($structurekey);

        // Create row in CSNTIERJOBTRACKER for STRUCTUREKEY and count as count($this->bookArray))
        $args = [];
        $args[] = "INSERT INTO CSNTIERJOBTRACKER (CNY#, STRUCTUREKEY, JOBSTOBECOMPLETED) VALUES (:1,:2,:3)";
        $args[] = $this->cny;
        $args[] = $structurekey;
        $args[] = count($this->bookArray);
        $ok = $ok && ExecStmt($args);
        if (!$ok) {
            $this->_Err->addError(
                'IGC-0142', __FILE__ . ":" . __LINE__,
                "We are unable to create consolidation parallel job history record. Please wait "
                .
                "a few minutes and then try again"
            );
            return false;
        }

        return true;
    }

    /**
     * @param array  $bookInfo
     *
     * @param string $structureDetailKey
     *
     * @return array
     */
    private function getConsolidationBookEntities(array $bookInfo, string $structureDetailKey): array
    {
        $bookEntities = [];
        $gManagerFactory = Globals::$g->gManagerFactory;
        $gcOwnershipEntityMgr = $gManagerFactory->getManager('gcownershipentity');
        $filter = [
            'selects' => [
                'RECORDNO', 'BOOK.BOOKKEY', 'BOOK.BOOKID', 'PARENTENTITYKEY', 'PARENTENTITYID', 'BOOK.CURRENCY'
            ],
            'filters' => [[['GCOWNERSHIPDETAILKEY', '=', $structureDetailKey]]]
        ];

        //Get all the parent entities of given structure detail key
        //We are running one getlist to get all the parent entites and also to check whether it is a book or entity
        $gcOwnershipEntityInfo = $gcOwnershipEntityMgr->GetList($filter);

        $parentEntityRecNo = '';
        //This keeps all the parent entity of this structure
        $parentEntityArray = [];
        $parentBookCurrencyArray = [];
        foreach ($gcOwnershipEntityInfo as $gcOwnershipEntity) {
            // very very important to add '===' to filter unwanted entities to get consolidated
            if ($gcOwnershipEntity['BOOK.BOOKKEY'] === $bookInfo['RECORDNO']) {
                $parentEntityRecNo = $gcOwnershipEntity['RECORDNO'];
            }
            $parentEntityArray[$gcOwnershipEntity['PARENTENTITYKEY']] = $gcOwnershipEntity['BOOK.BOOKID'];
            // Keep track of all the Book ID and its currency
            $parentBookCurrencyArray[$gcOwnershipEntity['BOOK.BOOKID']] = $gcOwnershipEntity['BOOK.CURRENCY'];
        }

        if ($parentEntityRecNo === '') {
            $tempBookId = isl_htmlspecialchars($bookInfo['BOOKID']);
            $this->_Err->addIAError(
                'IGC-0143', __FILE__ . ":" . __LINE__,
                "We are unable to retrieve the parent entity of book " . $tempBookId
                . ". Please wait a few minutes and then try again",
                ['BOOK_ID' => $tempBookId]
            );
            return [];
        }

        //Get child entities in this parent
        $parentEntityInfo = $gcOwnershipEntityMgr->get($parentEntityRecNo);

        $bookKey = $parentEntityInfo['BOOK']['BOOKKEY'];
        $bookId = $parentEntityInfo['BOOK']['BOOKID'];
        //Add parent entity
        $bookEntities[] = [
            'RECORDNO' => $parentEntityInfo['RECORDNO'],
            'BOOKKEY' => $bookKey,
            'BOOKID' => $bookId,
            'LOCATIONID' => $parentEntityInfo['PARENTENTITYID'],
            'LOCATIONNAME' => $parentEntityInfo['PARENTENTITYNAME'],
            'LOCATIONKEY' => $parentEntityInfo['PARENTENTITYKEY'],
            'OWNERSHIPPERCENTAGE' => 100
        ];

        //Add children entities
        foreach ($parentEntityInfo['GCOWNERSHIPCHILDENTITIES'] as $gcownershipchildentity) {
            $entityarray = [
                'RECORDNO' => $gcownershipchildentity['RECORDNO'],
                'BOOKKEY' => $bookKey,
                'BOOKID' => $bookId,
                'LOCATIONID' => $gcownershipchildentity['ENTITYID'],
                'LOCATIONNAME' => $gcownershipchildentity['ENTITYNAME'],
                'LOCATIONKEY' => $gcownershipchildentity['ENTITYKEY'],
                'OWNERSHIPPERCENTAGE' => $gcownershipchildentity['OWNERSHIPPERCENTAGE']
            ];
            //if this entity is parent entity?
            if (isset($parentEntityArray[$gcownershipchildentity['ENTITYKEY']])) {
                $entityarray['ISROLLUPENTITY'] = true;
                $parentEntityBookID = $parentEntityArray[$gcownershipchildentity['ENTITYKEY']];
                $entityarray['ROLLUPENTBOOKID'] = $parentEntityBookID;
                $childEntityCurrency = $this->entityMap[$gcownershipchildentity['ENTITYID']]['CURRENCY'];
                $parentBookCurrency = $parentBookCurrencyArray[$parentEntityBookID];

                // If the child entity is parent entity and its book currency and entity currencies are different
                // then while retreiving the rate for the rollup during consolidation, fetch book currency not entity
                // currency
                if ($childEntityCurrency != '' && $parentBookCurrency != ''
                    && $childEntityCurrency != $parentBookCurrency
                ) {
                    $entityarray['ROLLUPBOOKCURRENCY'] = $parentBookCurrency;
                }

                // If the budget is set in parent entity then send as params while creating IMS job
                // $this->csnBudgetHdrKey holds consolidation budget key of prior book
                if ($bookInfo['BUDGETID'] ?? null && $this->csnBudgetHdrKey ?? null) {
                    $entityarray['ROLLUPENTBUDGETKEY'] = $this->csnBudgetHdrKey;
                }
            }

            // If the subsidiary has consolidation method then set CONSOLIDATIONMETHOD
            // No need to save all the accounts and it can become bulky array if large number of subsidiaries are added
            // Just save record# and call perform getlist to get accountkey
            if ($gcownershipchildentity['CONSOLIDATIONMETHOD'] == GCOwnershipChildEntityManager::CONSOLIDATION_VALUE) {
                $entityarray['CONSOLIDATIONMETHOD'] = $gcownershipchildentity['CONSOLIDATIONMETHOD'];
                $entityarray['SUBSIDIARYRECNO'] = $gcownershipchildentity['RECORDNO'];
                // We need parent entity key to post NCI entries into parent entity
                $entityarray['PARENTENTITYKEY'] = $parentEntityInfo['PARENTENTITYKEY'];
                // If the consolidation method is selected then follow full consolidation.
                // We need ownership perc for calculating Income attributed to NCI and we use SUBSIDIARYRECNO and call getlist()  
                $entityarray['OWNERSHIPPERCENTAGE'] = 100;
            }
            $bookEntities[] = $entityarray;
        }

        // Add elimination entities, as it is mandatory whether auto-elim enabled or not
        $bookEntities[] = [
            'BOOKKEY' => $bookKey,
            'BOOKID' => $bookId,
            'LOCATIONID' => $bookInfo['EENAME'],
            'LOCATIONNAME' => $this->entityMap[$bookInfo['EENAME']]['NAME'],
            'LOCATIONKEY' => $this->entityMap[$bookInfo['EENAME']]['RECORDNO'],
            'OWNERSHIPPERCENTAGE' => 100
        ];

        return $bookEntities;
    }


    /**
     * @param array  $gbl
     * @param string|null $bsrate
     * @param string|null $wsrate
     *
     * @return array
     */
    protected function buildCSNEntityArray(array $gbl, ?string $bsrate, ?string $wsrate): array
    {
        $entityTempArray = [
            'ENTITYID' => $gbl['LOCATIONID'],
            'BSRATE' => $bsrate ?? 1,
            'WARATE' => $wsrate ?? 1,
            'OWNERSHIPPERCENTAGE' => $gbl['OWNERSHIPPERCENTAGE']
        ];

        // setting parent entity for tier consolidation
        if (isset($gbl['ISROLLUPENTITY']) && $gbl['ISROLLUPENTITY']) {
            $entityTempArray['ISROLLUPENTITY'] = true;
            $entityTempArray['ROLLUPENTBOOKID'] = $gbl['ROLLUPENTBOOKID'];

            // If parent entity budget id exists then
            if ($gbl['ROLLUPENTBUDGETKEY'] ?? null) {
                $entityTempArray['ROLLUPENTBUDGETKEY'] = $gbl['ROLLUPENTBUDGETKEY'];
            }
        }

        // If the subsidiary has consolidation method then set CONSOLIDATIONMETHOD
        // No need to save all the accounts and it can become bulky array if large number of subsidiaries are added
        if (isset($gbl['CONSOLIDATIONMETHOD'])
            && $gbl['CONSOLIDATIONMETHOD'] == GCOwnershipChildEntityManager::CONSOLIDATION_VALUE
        ) {
            $entityTempArray['CONSOLIDATIONMETHOD'] = $gbl['CONSOLIDATIONMETHOD'];
            $entityTempArray['SUBSIDIARYRECNO'] = $gbl['SUBSIDIARYRECNO'];
            // We need parent entity key to post NCI entries into parent entity
            $entityTempArray['PARENTENTITYKEY'] = $gbl['PARENTENTITYKEY'];
        }        
        return $entityTempArray;
    }

    /**
     * @return string
     */
    protected function getOfflineJobIMSTopic(): string
    {
        return self::RUN_TIER_CONSOLIDATION_TOPIC;
    }

    /**
     * @return string
     */
    protected function getMultiPeriodIMSTopic(): string
    {
        return self::RUN_MULTIPERIOD_TIER_CONSOLIDATION;
    }
    
    /**
     * @param array  $params
     *
     * @param string $bookId
     *
     * @return bool returns true if success else false if any error
     */
    private function formatArrayBeforeConsolidation(array &$params, string $bookId): bool
    {
        //Format array to consolidate a book
        $params['BOOKID'] = $bookId;

        // unset PARALLELJOBS if exists
        unset($params['PARALLELJOBS']);

        // prepare entities array
        $entities = $this->consEntities[$bookId];
        unset($this->consEntities[$bookId]);
        $params['ENTITIES']['ENTITY'] = $entities;

        return $this->initializeBook($params['BOOKID']);
    }

    /**
     * @param array $params
     *
     * @return bool
     */
    private function fetchOrderOfConsolidationBook(array &$params): bool
    {
        // This hierarchical query is very expensive and time consuming and not needed to run for each period
        // consolidation in multi period consolidation. If prior and current period STRUCTUREDETAILKEY
        // are same then can ignore this and pass the prior period CONSBOOKORDER
        if (self::isConsolidationOrderNeeded($params)) {
            $res = self::queryOrderOfConsolidationBook($params['STRUCTUREDETAILKEY'], $this->cny);
            if (!$res) {
                $this->_Err->addError(
                    'IGC-0144', __FILE__ . ":" . __LINE__,'',
                    "The ownership structure doesn't have any children or tier."
                );
                return false;
            }

            foreach ($res as $val) {
                $params['CONSBOOKORDER'][$val['L']][] = $val['BOOKID'];
            }

            // If it is multi period consolidation and system fetches order freshly then cache it in the payload
            // to avoid multiple hierarchical queries
            if (isset($params['UPDATESUCCEEDINGPERIODS']) && $params['UPDATESUCCEEDINGPERIODS'] == 'T') {
                $params['OLDCONSBOOKORDER'] = $params['CONSBOOKORDER'];
            }
        }

        return true;
    }

    /**
     * @param array $params
     *
     * @return bool
     */
    private static function isConsolidationOrderNeeded(array &$params): bool
    {
        // This hierarchical query is very expensive and time consuming and not needed to run for each period
        // consolidation in multi period consolidation. If prior and current period STRUCTUREDETAILKEY
        // are same then can ignore this and pass the prior period CONSBOOKORDER
        if (self::isSameOwnershipStructureDetail($params) && !empty($params['OLDCONSBOOKORDER'])) {
            $params['CONSBOOKORDER'] = $params['OLDCONSBOOKORDER'];
            return false;
        }
        return true;
    }

    /**
     * @param array $params
     *
     * @return bool
     */
    public static function isSameOwnershipStructureDetail(array $params): bool
    {
        // This method checks below conditions and retuns true if satisfied
        // 1. Is multi period tier consolidation
        // 2. Old and current same ownership structure key
        if (isset($params['UPDATESUCCEEDINGPERIODS']) && $params['UPDATESUCCEEDINGPERIODS'] == 'T'
            && isset($params['OLDSTRUCTUREDETAILKEY'])
            && $params['OLDSTRUCTUREDETAILKEY'] == $params['STRUCTUREDETAILKEY']
        ) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @param int    $structureDetailKey
     *
     * @param string $cny
     *
     * @param string $orderBy
     * 
     * @return false|string[][]
     */
    public static function queryOrderOfConsolidationBook(int $structureDetailKey, string $cny, string $orderBy = 'DESC')
    {
        $query = self::getHierarchicalQueryForCons();
        $query
            = "$query
                    SELECT
                        ROWNO.BOOKKEY,
                        GL.BOOKID,
                        ROWNO.L
                    FROM
                        rownumber ROWNO,
                        glbook GL
                    WHERE
                        GL.CNY# = :1
                        AND ROWNO.BOOKKEY = GL.RECORD#
                        AND GL.TYPE = :3
                        AND ROWNO.RN = 1
                    ORDER BY
                        ROWNO.L " . $orderBy;

        $res = QueryResult([$query, $cny, $structureDetailKey, GCBookManager::BOOK_TYPE_TIER_CSN_INTERNAL]);
        // No need to validate circular reference as this hierarchical query throws error if exists
        if (!$res) {
            Globals::$g->gErr->addError(
                'IGC-0145', __FILE__ . ":" . __LINE__,'',
                "The ownership structure relationship is having circular reference. 
                Make necessary changes."
            );
        }
        return $res;
    }

    /**
     * @param string $distinctKey
     *
     * @return string
     */
    private static function getHierarchicalQueryForCons(string $distinctKey = 'BOOKKEY', string $startWith = ''): string
    {
        // Standard hierarchical query to run on the consolidation object to fetch all the book or entity details
        // belong to the structure 
        $query = "WITH cte AS 
                (
                    SELECT 
                        P.BOOKKEY, 
                        C.CNY#, 
                        C.ENTITYKEY,
                        P.PARENTENTITYKEY, 
                        P.GCOWNERSHIPDETAILKEY
                    FROM 
                        gcownershipentity P, 
                        gcownershipchildentity C
                    WHERE 
                        P.CNY# = C.CNY#  
                        AND P.CNY# = :1 
                        AND P.RECORD# = C.GCOWNERSHIPENTITYKEY 
                        AND P.GCOWNERSHIPDETAILKEY = :2
                ), book AS 
                (
                    SELECT DISTINCT 
                        P.$distinctKey,
                        LEVEL AS L 
                    FROM 
                        cte P 
                        $startWith
                    CONNECT BY PRIOR P.ENTITYKEY = P.PARENTENTITYKEY
                        AND P.CNY# = :1 
                        AND P.GCOWNERSHIPDETAILKEY = :2
                ), rownumber AS 
                (
                    SELECT	$distinctKey,
                            L,
                            ROW_NUMBER()
                                OVER (
                                PARTITION BY $distinctKey
                                ORDER BY 
                                    L DESC ) 
                                    AS rn FROM book
                )";
        return $query;
    }

    /**
     * This method initializes journal information for Legacy books so returning false
     *
     * initJournalInfo
     *
     * @return bool
     */
    protected function initJournalInfo(): bool
    {
        return false;
    }

    /**
     * Validates Book journals for Legacy books hence returning true
     *
     * @param array $gcBookEntities
     *
     * @return bool
     */
    protected function isBookJournalSameForEntities(array $gcBookEntities): bool
    {
        return true;
    }

    /**
     * initParentEntityBooks
     *
     * @return bool
     */
    protected function initParentEntityBooks()
    {
        if ($this->isRollupEntity) {
            $gManagerFactory = Globals::$g->gManagerFactory;
            $gcBookMgr = $gManagerFactory->getManager('gcbook');
            $this->parentEntityConsBooks = [];
            $this->parentEntityConsBooks[$this->sourceBook]['CSNBOOKID'] = $this->rollUpEntityBookId;

            $csnAdjBooks = $gcBookMgr->getCSNAdjustmentBooks($this->rollUpEntityBookId, $this->sourceBook);

            foreach ($csnAdjBooks as $csnAdjBook) {
                $adjBookID = substr($csnAdjBook, 0, -strlen($this->rollUpEntityBookId)) . $this->sourceBook;
                $this->parentEntityConsBooks[$adjBookID]['CSNBOOKID'] = $csnAdjBook;
            }
        } else {
            $this->parentEntityConsBooks = [];
        }
        return true;
    }

    /**
     * @param string $srcBookID
     *
     * @return bool
     */
    protected function IsUDBBookExists(string $srcBookID): bool
    {
        // In the case of entity which is parent, we go to it's book and get ACCRUAL and UDB books
        // If UDB books are not exists in consolidating book then no need to consolidate
        // parentEntityConsBooks holds parent entity ACCRUAL and UDB books details
        if ($this->isRollupEntity && !isset($this->parentEntityConsBooks[$srcBookID])) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * @return string|null
     */
    protected function getEntityBaseCurrency(): ?string
    {
        // In the case of parent entity, we should get data from the subsidiary book's currency
        // and create glbatch with this currency as trx currency not entity currency
        if ($this->isRollupEntity) {
            return GetBookCurrency($this->rollUpEntityBookId);
        } else {
            return parent::getEntityBaseCurrency();
        }
    }
    
    /**
     * @param string $srcBookID
     */
    protected function setReportingBookAndCurrencyContext(string $srcBookID)
    {
        if ($this->isRollupEntity) {
            // If the entity is parent entity then set it's book context and currency and get book trx
            SetReportingBookContext($this->parentEntityConsBooks[$srcBookID]['CSNBOOKID']);
        } else {
            //set the book context (accrual) and currency context(entity base currency)
            SetReportingBookContext($srcBookID);
        }
        // entityBaseCurrency and book currency is same in the case of non-parent entity
        SetCurrencyContext($this->entityBaseCurrency);
    }

    /**
     * prepareTrialBalanceData
     *
     * @param array  $locationArray locationArray
     *
     * @param string $ownershipPerc
     *
     * @return array
     */
    protected function prepareTrialBalanceData($locationArray = [], $ownershipPerc = '')
    {
        // This method fetches current ACCRUAL/BOOK balance, irrespective of whether roll up entity or just entity
        // We should apply ownershipPerc calculation
        if ($this->isRollupEntity) {
            // Roll-up always picks from new table CONSGLENTRY table
            // Prepare Trial Balance data based on new table CONSGLENTRY
            // To Get current balance of parent entity follow these steps
            // 1. Set Reporting book context as parent entity book
            // 2. Remove location# filter to fetch all the trx from the book
            // 3. Add proper $currBookFilter to get book balance
            // 4. select location# as parent entity record#
            // 5. Removing GLTOTALS.LOCATION# from GROUP by clause so that accounts will be summed-up irrespective
            // of which entity it is posted from in child entity
            // It means, get all the trx of current period from book, set this entity location#

            $bindQuery = ["", $this->cny, $this->periodID];
            $this->getCSNBalanceFilters($bindQuery, $dimensionFieldStr, $currBookFilter);

            $bindQuery[0] = "SELECT 
						GLTOTALS.ACCOUNT#, BASEACCOUNT.ACCT_NO, BASEACCOUNT.STATISTICAL, BASEACCOUNT.ACCOUNT_TYPE, 
						$dimensionFieldStr $this->entityID AS LOCATION#,
						(SUM(GLTOTALS.DEBIT) + SUM(GLTOTALS.ADJDEBIT)) $this->ownershipPerc AS DEBIT,
						(SUM(GLTOTALS.CREDIT) + SUM(GLTOTALS.ADJCREDIT)) $this->ownershipPerc AS CREDIT
					FROM CONSGLENTRYMST GLTOTALS, BASEACCOUNT BASEACCOUNT
					WHERE GLTOTALS.CNY# = :1 AND GLTOTALS.TIMEPERIOD = :2
						  AND BASEACCOUNT.CNY# = GLTOTALS.CNY# AND BASEACCOUNT.RECORD# = GLTOTALS.ACCOUNT#
                          $currBookFilter ";

            $bindQuery = PrepINClauseStmt(
                $bindQuery, $this->txnHRRateAcctList, ' AND GLTOTALS.ACCOUNT# NOT ', true, 'accounttype'
            );
            $groupByClause = " GROUP BY GLTOTALS.ACCOUNT#, BASEACCOUNT.ACCT_NO, BASEACCOUNT.STATISTICAL, 
				          $dimensionFieldStr BASEACCOUNT.ACCOUNT_TYPE ";
            $bindQuery[0] .= $groupByClause;

            if ($this->isEliminateByAffiliateEntityEnabled()) {
                // Exclude already eliminated accounts and NCI entries in the lower level when the Affiliate entity
                // based elimination is selected.
                $this->excludeAlreadyEliminatedAcctTrx($bindQuery);
            }

            $trans = QueryResult($bindQuery);

            return ($trans) ?: [];
        } else {
            return parent::prepareTrialBalanceData($locationArray, $this->ownershipPerc);
        }
    }

    /**
     * preparePrevBalanceSheetData
     *
     * @param array      $locationArray locationFilter
     *
     * @param array      $dimensionStr
     *
     * @param array      $consBook
     *
     * @param bool|false $isHistorical
     *
     * @param bool|false $isPrevConsBal
     *
     * @param string     $ownershipPerc
     *
     * @return array
     */
    protected function preparePrevBalanceSheetData(
        $locationArray = [], $dimensionStr = [], $consBook = [], $isHistorical = false, $isPrevConsBal = false,
        $ownershipPerc = ''
    )
    {
        // This method fetches both ACCRUAL and consolidation balance for both entity and rollup entity (OB)
        // We should apply ownership perc calculation while fetching prev ACCRUAL balance and
        // not for consolidation balance irrespective of roll-up condition
        // If prior period (detail) not found then we should apply current period ownership %
        // and for parent entity and elimination entity ownership % is always 100% hence no need to apply ownership perc
        $ownershipPerc = '';
        if (!$isPrevConsBal) {
            $ownershipPerc = $this->priorPeriodEntityOwnershipPerc;
        }

        // If we are fetching historical balances and there is no historical account then no need to run the query
        // Just return an empty array
        if ($isHistorical && count($this->txnHRRateAcctList) == 0) {
            return [];
        }

        if ($this->isRollupEntity) {
            // To Get Prev balance of parent entity follow these steps
            // 1. Set Reporting book context as consolidation book
            // 2. Add proper $currBookFilter to get book balance
            // 3. Remove location# filter to fetch all the trx from the book (only for prev ACCRUAL balance)
            // 4. select location# as parent entity record#
            // 5. Remove location# from group by clause so that trx will be summarized.
            // 6. While fetching previous consolidation balance, we should respect balance by location
            // It means, we can't directly massage location# as parententity, instead we should have it's sublocation
            // in select and group by clause
            // Roll-up always picks from new table CONSGLENTRY table

            // To get previous consolidation balance then we need to get this entity trx in the book in this case
            // we have to apply $locationFilter
            $bindQuery = ["", $this->cny, $this->prevBalSheetDate];
            $this->getDimensionFieldStrFilters($dimensionStr, $dimensionFieldStr);

            // If Previous consolidation balance then we should get consolidation book filter
            // If not then it gets from context
            $locWhereClauseForPrevBal = '';
            if ($isPrevConsBal) {
                $currBookFilter = GetBookAndCurrFilter('GLTOTALS', false, false, $consBook, $bindQuery);

                // Point 6 as stated above
                $selectlocClauseForPrevBal = 'GLTOTALS.LOCATION#';
                $locWhereClauseForPrevBal = ', GLTOTALS.LOCATION#';
            } else {
                $currBookFilter = GetBookAndCurrFilter('GLTOTALS', false, false, [], $bindQuery);
                $selectlocClauseForPrevBal = $this->entityID . ' AS LOCATION#';
            }

            // Prepare prev Balancesheet data based on CONSGLENTRY table
            $bindQuery[0]
                = "SELECT GLTOTALS.ACCOUNT#, BASEACCOUNT.ACCT_NO,
                               SUM(GLTOTALS.AMOUNT) $ownershipPerc AS AMOUNT, 
							   $dimensionFieldStr $selectlocClauseForPrevBal 
                        FROM CONSGLENTRYMST GLTOTALS, BASEACCOUNTMST BASEACCOUNT
                        WHERE GLTOTALS.CNY# =:1 AND BASEACCOUNT.CNY# = GLTOTALS.CNY#
                              AND BASEACCOUNT.RECORD# = GLTOTALS.ACCOUNT#
                              AND GLTOTALS.TIMEPERIOD <= :2
                              AND BASEACCOUNT.STATISTICAL = 'F'
                              AND BASEACCOUNT.ACCOUNT_TYPE = 'N' 
                              $currBookFilter ";

            // this condition checks whether it is previous ACCURAL balance or consolidation
            if ($isPrevConsBal) {
                // Get locationfilter array and bind all the parameters using PrepINClauseStmt
                $bindQuery = PrepINClauseStmt(
                    $bindQuery, $locationArray, ' AND GLTOTALS.LOCATION# ', true, 'locationtype'
                );
            }
            $bindQuery = $this->getAccountToOverrideFilter($bindQuery, $isHistorical);

            $groupByClause = " GROUP BY GLTOTALS.ACCOUNT#, $dimensionFieldStr BASEACCOUNT.ACCT_NO $locWhereClauseForPrevBal 
            HAVING SUM(GLTOTALS.AMOUNT) <> 0 ";

            $bindQuery[0] .= $groupByClause;
            $bsResultSet = QueryResult($bindQuery);

            return ($bsResultSet) ?: [];

        } else {
            return parent::preparePrevBalanceSheetData(
                $locationArray, $dimensionStr, $consBook, $isHistorical, $isPrevConsBal, $ownershipPerc
            );
        }
    }

    /**
     * prepareGLedgerData
     *
     * @param string $srcBookID
     *
     * @param array  $locationArray
     *
     * @param string $ownershipPerc
     *
     * @return array
     */
    protected function prepareGLedgerData($srcBookID, $locationArray, $ownershipPerc = ''): array
    {
        // This method fetches current ACCRUAL balance of each entity for historical accounts,
        // irrespective of whether roll up entity or just entity
        // We should apply ownershipPerc calculation

        // nothing to do if there is no account using historical rate
        if (count($this->txnHRRateAcctList) == 0) {
            return [];
        }

        if ($this->isRollupEntity) {
            // To Prepare data for GLedger consolidation of parent entity then follow these steps
            // 1. Set Reporting book context as consolidation book
            // 2. Add proper $currBookFilter to get book balance
            // 3. Remove location# filter to fetch all the trx from the book
            // 4. select location# as parent entity record#
            // 5. Remove location# from group by clause so that trx will be summarized.
            // It means, get all the trx of current period from book, set this entity location#
            // Roll-up always picks from new table CONSGLENTRY table
            // in Roll-up entry date is period end date, the logic is, if the line level rate type is selected then
            // pick the rate from EXCH_RATE_DATE else from ENTRY_DATE
            $bindQuery = ["", $this->cny, $this->periodID, $this->periodEndDate];

            // Roll-up always picks from new table CONSGLENTRY table
            // in Roll-up entry date is period end date, the logic is, if the line level rate type is selected then
            // pick the rate from EXCH_RATE_DATE else from ENTRY_DATE
            $this->getCSNBalanceFilters($bindQuery, $dimensionFieldStr, $currBookFilter);

            // Pick the historical account data from new table CONSGLENTRY
            $bindQuery[0] = "SELECT 
						GLTOTALS.ACCOUNT#, BASEACCOUNT.ACCT_NO, TO_CHAR(TO_DATE(:3, 'MM/DD/YYYY'), 'MM/DD/YYYY') AS ENTRY_DATE,
                        GLTOTALS.EXCH_RATE_DATE, $dimensionFieldStr $this->entityID AS LOCATION#,
						SUM(GLTOTALS.DEBIT) $this->ownershipPerc AS DEBIT,
						SUM(GLTOTALS.CREDIT) $this->ownershipPerc AS CREDIT
					FROM CONSGLENTRYMST GLTOTALS, BASEACCOUNT BASEACCOUNT
					WHERE GLTOTALS.CNY# = :1 AND GLTOTALS.TIMEPERIOD = :2 AND GLTOTALS.AMOUNT != 0
					      AND BASEACCOUNT.STATISTICAL = 'F'
						  AND BASEACCOUNT.CNY# = GLTOTALS.CNY# AND BASEACCOUNT.RECORD# = GLTOTALS.ACCOUNT# 
					      $currBookFilter ";

            $bindQuery = $this->getAccountToOverrideFilter($bindQuery, true);
            $groupByClause
                = " GROUP BY GLTOTALS.ACCOUNT#, BASEACCOUNT.ACCT_NO, $dimensionFieldStr GLTOTALS.EXCH_RATE_DATE ";
            $bindQuery[0] .= $groupByClause;

            if ($this->isEliminateByAffiliateEntityEnabled()) {
                // Exclude already eliminated accounts and NCI entries in the lower level when the Affiliate entity
                // based elimination is selected.
                $this->excludeAlreadyEliminatedAcctTrx($bindQuery);
            }
            $glPeriodTxns = QueryResult($bindQuery);

            return ($glPeriodTxns) ?: [];

        } else {
            return parent::prepareGLedgerData($srcBookID, $locationArray, $this->ownershipPerc);
        }
    }

    /**
     * prepareBudgetData
     *
     * @param array $locationArray
     *
     * @return array
     */
    protected function prepareBudgetData($locationArray): array
    {
        // This method prepares budget data of each entity of current period
        if ($this->isRollupEntity) {
            // To Prepare data for Budget consolidation of parent entity then follow these steps
            // 1. Remove location# filter to fetch all the trx from the consolidation budget of subsidiary
            // 2. Instead of default budget add consolidation budget of subsidiary in the filter
            // 3. Set location# as parent entity record#

            // There are cases where parent entity has selected budgets and child has not selected budgets then
            // safer way is to return [] and bypass budget consolidation  
            if (isNullOrBlank($this->rollUpEntBudgetKey)) {
                return [];
            }

            $gManagerFactory = Globals::$g->gManagerFactory;
            $glbudgetMgr = $gManagerFactory->getManager('glbudget');
            $params = array(
                'filters' => array(
                    array(
                        array('PERIODKEY', '=', $this->budgetPeriodKey),
                        array('BUDGETKEY', '=', $this->rollUpEntBudgetKey),
                    ),
                ),
            );

            $params['amount_conversion'] = false;
            $glBudgetData = $glbudgetMgr->GetList($params);

            // Set locationKey as parent entity location# and rest unset it as it is not needed
            if ($glBudgetData > 0) {
                foreach ($glBudgetData as &$glBudget) {
                    $glBudget['LOCATIONKEY'] = $this->entityID;
                    unset($glBudget['LOCATIONID']);
                    unset($glBudget['LOCATION_NO']);
                    unset($glBudget['LOCATIONTITLE']);
                }
            }

            return $glBudgetData;
        } else {
            return parent::prepareBudgetData($locationArray);
        }
    }

    /**
     * @param array  $budgetTxns
     *
     * @return array
     */
    protected function createConsolidatedBudgetArray(array $budgetTxns): array
    {
        $csnBudgetRecs = [];
        $gManagerFactory = Globals::$g->gManagerFactory;
        $glbudgetMgr = $gManagerFactory->getManager('glbudget');

        // Calculate ownership perc
        $ownershipPercNum = preg_replace('/[^0-9.]+/', '', $this->ownershipPerc);
        foreach ($budgetTxns as $transEntry) {
            $transEntry['AMOUNT'] = ibcmul($transEntry['AMOUNT'], $ownershipPercNum, 2, true);
            //recalculate the amount for this reporting currency.
            if ($transEntry['STATISTICAL'] == 'T') {
                $bookAmount = $transEntry['AMOUNT'];
            } else {
                $bookAmount = $this->calReportingCurrencyAmount($transEntry['AMOUNT'], $transEntry, $convRate);
            }

            //create budget record
            /** @noinspection PhpUndefinedVariableInspection */
            $budgetRec = array(
                'ACCOUNT#' => $transEntry['ACCOUNTKEY'],
                'LOCATION#' => $transEntry['LOCATIONKEY'],
                'ACCOUNTKEY' => $transEntry['ACCOUNTKEY'],
                'LOCATIONKEY' => $transEntry['LOCATIONKEY'],
                'AMOUNT' => $bookAmount,
                'BUD_TYPE#' => $transEntry['PERIODKEY'],
                'PERIODKEY' => $transEntry['PERIODKEY'],
                'BASEDON' => $bValidVal[$transEntry['BASEDON']],
                'PERPERIOD' => $pValidVal[$transEntry['PERPERIOD']],
                'BUDGETKEY' => $this->csnBudgetHdrKey
            );

            //copy department only if dept is enabled in IGC book
            if (in_array('GLTOTALS.DEPT#', self::$glDimKeys)) {
                $budgetRec['DEPT#'] = $transEntry['DEPTKEY'];
                $budgetRec['DEPTKEY'] = $transEntry['DEPTKEY'];
            }
            self::copyMyDimensionValues($transEntry, $budgetRec, true, 'glbudget');
            $glbudgetMgr->setCustomDimensionsCache($budgetRec);
            self::summarizeDuplicateConsolidationBudgets($budgetRec, $csnBudgetRecs);
        }
        return $csnBudgetRecs;
    }

    /**
     * prepareEliminationData
     *
     * @param array  $locationArray
     *
     * @param string $ownershipPerc
     *
     * @return array|false
     */
    protected function prepareEliminationData($locationArray, $ownershipPerc = '')
    {
        // We prepare elimination data from posted entries hence it is always CONSGLENTRY for tier consolidation
        return parent::prepareEliminationData($locationArray, $this->ownershipPerc);
    }

    /**
     * @param array $entities
     *
     * @return array
     */
    protected function getWorkingEntityForIET(array $entities = []): array
    {
        // Here we are avoiding unnecessary query to get entities from structure
        $entityResult = [];
        foreach ($entities as $entity) {
            $entityRecNo = $this->entityMap[$entity['ENTITYID']]['RECORDNO'];
            if ($entityRecNo !== $this->eliminationEntityID) {
                $entityResult[]
                    = [
                    'LOCATIONKEY' => $entityRecNo,
                    'NAME' => $this->entityMap[$entity['ENTITYID']]['NAME'],
                    'LOCATION_NO' => $entity['ENTITYID']
                ];
            }
        }

        return $entityResult;
    }

    /**
     * @param array $entity
     *
     * @return bool
     */
    protected function initializeEntityDetails(array $entity): bool
    {
        $ok = parent::initializeEntityDetails($entity);
        $this->isRollupEntity = $entity['ISROLLUPENTITY'] ?? false;
        $this->rollUpEntityBookId = $entity['ROLLUPENTBOOKID'] ?? '';
        $this->rollUpEntBudgetKey = $entity['ROLLUPENTBUDGETKEY'] ?? '';
        $this->ownershipPerc = ' * ' . ibcdiv($entity['OWNERSHIPPERCENTAGE'], 100, 12, true);

        // We should apply ownership perc calculation while fetching prev ACCRUAL balance and
        // not for consolidation balance irrespective of roll-up condition
        // If prior period (detail) not found then we should apply current period ownership %
        // and for parent entity and elimination entity ownership % is always 100% hence no need to apply ownership perc
        $priorPeriodEntityOwnershipPerc = $this->priorPeriodEntityArray[$this->entityID] ??
            $entity['OWNERSHIPPERCENTAGE'];
        $this->priorPeriodEntityOwnershipPerc = ' * ' . ibcdiv($priorPeriodEntityOwnershipPerc, 100, 12, true);

        // Cache consolidation method fields for NCI postings
        $this->cacheCSNMethodValues($entity);

        // Exclude already eliminated accounts in the lower level when the Affiliate entity based elimination is
        // selected.
        // Cache subsidiary entities and it's elimination accounts only when the given entity is rollup entity and
        // elimination by affiliate entity option is enabled
        if ($this->isRollupEntity && $this->rollUpEntityBookId != '' && $this->isEliminateByAffiliateEntityEnabled()) {
            $this->cacheSubsKeysAndElimAcctsRollUp($this->rollUpEntityBookId, $this->entityID);
        }
        return $ok;
    }

    /**
     * @param MetricConsolidationHandlerProcess $consolidationMetrics
     * @param array                             $entities
     */
    protected function setMetricsProperties(MetricConsolidationHandlerProcess $consolidationMetrics, array $entities)
    {
        // We are not logging Structure name for direct consolidation and we log only for tier consolidation
        // so that in Kibana exists filter will return only tier consolidation result
        $consolidationMetrics->setStructureName($this->structureName ?? '');
        parent::setMetricsProperties($consolidationMetrics, $entities);
    }

    /**
     * @param string $bookID
     *
     * @return array
     */
    protected function runQueryForLocRestriction($bookID)
    {
        // For tier consolidation, restricted user should have access to all the entities of the book
        // then only he can perform consolidation
        return QueryResult(
            array(
                "WITH BOOKENTITIES AS
                        ( 
                            SELECT p.PARENTENTITYKEY, ch.ENTITYKEY, l.record# from GCOWNERSHIPENTITY p, 
                            GCOWNERSHIPCHILDENTITY ch, glbook gl, locationmst l where p.cny#=:1 and p.cny# = ch.cny# 
                            and p.record# = ch.GCOWNERSHIPENTITYKEY and gl.cny# = p.cny# and p.bookkey = gl.record# 
                            and l.cny# = gl.cny# and gl.EENAME = l.LOCATION_NO and gl.bookid = :2 and gl.type = :3
                        ) 
                        SELECT PARENTENTITYKEY as locationkey from BOOKENTITIES 
                            UNION 
                        SELECT ENTITYKEY as locationkey from BOOKENTITIES 
                            UNION 
                        SELECT record# as locationkey from BOOKENTITIES
                    MINUS
                     (
                      SELECT UL.LOCATIONKEY FROM USERLOC UL WHERE UL.CNY# = :1 
                      AND UL.USERKEY = sys_context('TMCtx', 'USERKEY')
                     )", GetMyCompany(), $bookID, GCBookManager::BOOK_TYPE_TIER_CSN_INTERNAL
            )
        );
    }

    /**
     * cancelOfflineConsolidationForStructure
     *
     * @return bool
     */
    public function cancelOfflineConsolidationForStructure(): bool
    {
        // Get the consolidation books from csnhistory where status is Offline Consolidation in progress
        // There might be parallel books which went for toss, those can be cancelled
        // In parallel jobs, if one is running then user can't cancel it. Either one can complete and other went 
        // for toss then can cancel it
        // If jobs are in queue and not picked for consolidation
        // 1. release structure lock
        // 2. release each book lock
        // 3. set csnhistory as failed
        $gManagerFactory = Globals::$g->gManagerFactory;
        $gcTierConsolidationMgr = $gManagerFactory->getManager('gctierconsolidation');
        $filter = [
            'selects' => [
                [
                    'fields' => ['BOOKKEY'],
                    'function' => 'DISTINCT(${1})'
                ]
            ],
            'filters' => [
                [
                    ['STRUCTURENAME', '=', $this->structureName],
                    ['CSNSTATUS', '=', self::CSN_OFFLINE]
                ]
            ]
        ];

        $result = $gcTierConsolidationMgr->GetList($filter);
        $ok = true;

        $source = "TierConsolidationHandler::cancelOfflineConsolidationForStructure";
        // method cancelQueuedPackage commits trx
        // we need trx to rollback if any error happens  
        XACT_BEGIN($source);

        // release lock of each book if all are in queue
        // If all jobs are in queue then update state as cancelled
        foreach ($result as $book) {
            $bookLockName = $this->getBookLockNameForTier($book['BOOKKEY']);
            $packageId = array(
                'context' => GetMyCompanyTitle(), 'topic' => self::RUN_TIER_CONSOLIDATION_TOPIC,
                'sender' => $bookLockName
            );
            $ok = $ok && ims_package::cancelQueuedPackage($packageId);
            if (!$ok) {
                $this->_Err->addError(
                    'IGC-0146', __FILE__ . ":" . __LINE__,'',
                    "The package is not available in queue. It may be either 'In progress' or got deleted already."
                );
            }
        }

        // If all jobs are in queue then release structure lock
        $ok = $ok && $this->releaseStructureLock();

        if ($ok) {
            foreach ($result as $book) {
                $bookLockName = $this->getBookLockNameForTier($book['BOOKKEY']);
                $ok = $ok && $this->releaseCSNBookLock($book['BOOKKEY']);
                if (Lock::lockHeld($bookLockName)) {
                    $ok = false;
                }
                if (!$ok) {
                    $this->_Err->addError(
                        'IGC-0147', __FILE__ . ":" . __LINE__,'',
                        "Deleted the consolidation request in queue. But failed to release the book level lock"
                    );
                }
            }
        }

        if ($ok) {
            XACT_COMMIT($source);
        } else {
            $this->_Err->addError(
                'IGC-0148', __FILE__ . ":" . __LINE__,
                "Cannot cancel offline consolidation because a consolidation is already in progress for the selected book."
            );
            XACT_ABORT($source);
        }
        return $ok;
    }

    /**
     * @param array $params
     *
     * @return bool
     */
    protected function checkAndValidateEntities(array &$params): bool
    {
        // For tier consolidation, we don't have to validate at this place. We validate entities, books before
        // creating job itself. We format array and insert into CSNHISTORY as Queue for roll-up
        // We even get rates and create entity array in method formatCSNHistoryEntityArray
        // We have to set BOOKCURRENCY here
        $params['BOOKCURRENCY'] = $this->bookInfo['CURRENCY'];
        return true;
    }

    /**
     * @param array $params
     *
     * @return int
     */
    protected function getLatestCSNPeriod($params): int
    {
        // Check current period consolidation is last period consolidation?
        // In consolidation logic, we consolidate till last successfully consolidated period
        // what if the last period is failed? we should consolidate that period as well
        // In tier we fetch last csnhistory period and consolidate till that period
        $filter = [
            'selects' => [
                [
                    'fields' => ['TIMEPERIOD'],
                    'function' => 'MAX(${1})'
                ]
            ],
            'columnaliases' => ['TIMEPERIOD'],
            'filters' => [
                [
                    ['STRUCTURENAME', '=', $this->structureName]
                ]
            ]
        ];

        return self::getLastPeriod($filter);
    }

    /**
     * @param array $filter
     *
     * @return int
     */
    private static function getLastPeriod(array $filter): int
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $gcTierConsolidationMgr = $gManagerFactory->getManager('gctierconsolidation');
        $lastCSNPeriod = -1;
        $latestPeriod = $gcTierConsolidationMgr->GetList($filter);
        if ($latestPeriod[0]['TIMEPERIOD'] != '') {
            $lastCSNPeriod = $latestPeriod[0]['TIMEPERIOD'];
        }
        return $lastCSNPeriod;
    }

    /**
     * @param array  $params
     * @param array  $entityLastCSNPeriod
     * @param string $bookLastConsolidated
     *
     * @return bool
     */
    protected function validateMultiPeriodForCons(&$params, $entityLastCSNPeriod, $bookLastConsolidated): bool
    {
        // This code validates each entities whether prior all periods are successfully consolidated or not
        foreach ($params['ENTITIES'] as $entity) {
            $entName = $this->entityMap[$entity['ENTITYID']]['NAME'];
            $requestedEntityRecs[] = $this->entityMap[$entity['ENTITYID']]['RECORDNO'];
            // below validation is needed for tier consolidation also
            foreach ($entityLastCSNPeriod as $entRes) {
                if ($entRes['LOCATIONKEY'] == $this->entityMap[$entity['ENTITYID']]['RECORDNO']
                    && $this->periodID > $entRes['LASTCSNPERIOD']
                ) {
                    if (($entRes['LASTCSNPERIOD'] == '' && $this->periodID != $bookLastConsolidated)
                        || $entRes['LASTCSNPERIOD'] != ($this->periodID - 1)
                    ) {
                        $tempEntityName = isl_htmlspecialchars($entName);
                        $this->_Err->addIAError(
                            'IGC-0149', __FILE__ . '.' . __LINE__, '', [],
                            "You can't consolidate books for the specified period because the prior period wasn't " .
                            "consolidated. Please specify a different Start Date and End Date for entity ."
                            . $tempEntityName,
                            ['ENTITY_NAME' => $tempEntityName]
                        );
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * @param string $entityID
     *
     * @return array|bool
     */
    protected function getCSNHistoryStatus($entityID)
    {
        // In the case of tier consolidation, there is no delete and consolidation and no changesonly = F
        // hence no need to get TIMEPERIOD >= ?
        // During tier, we delete already consolidated csnhistory data and insert new entry as O
        // So there will not be any latest successful consolidation for the currency period
        // and we handle deleted entities differently hence just return time period in this method
        $res[]['TIMEPERIOD'] = $this->periodID;
        return $res;
    }

    /**
     * @param string $entityID
     *
     * @param string $bookID
     *
     * @return array|bool
     */
    protected function deleteStatusRecord($entityID, $bookID)
    {
        // In the case of tier consolidation, there is no delete and consolidation and no changesonly = F
        // hence no need to delete TIMEPERIOD >= ?, as we delete CSNHISTORY by period by period
        // This method deletes any csnhistory consolidated record of Success or failures
        // This method also deletes for entities having status as Reconsolidation required
        $gManagerFactory = Globals::$g->gManagerFactory;
        $csnHistoryMgr = $gManagerFactory->getManager('csnhistory');
        $res = $csnHistoryMgr->DeleteStatusRecord(
            $bookID, $entityID, $this->periodID, true
        );
        return $res;
    }

    /**
     * @param array $params
     *
     * @return bool
     */
    protected function validateMultiPeriodConsolidation(array $params): bool
    {
        // No need to validate for multi period consolidation for tier consolidation
        // as this is determined by the system
        return true;
    }

    /**
     * @return bool
     */
    protected function deleteTrxFromRemovedEntities(): bool
    {
        // Do the following steps in this method
        // 1. Get the list of entities and it's bookid where consolidation status is Reconsolidation required
        // We are deleting all the entities of all the level irrespective of level, hierarchy of given period
        // so no need to incorporate different logic for parent entity and entity removal cases 
        // 2. If there are no entities then return true
        // 3. If there are entities then
        // 4. Cache each bookinfo and other books included in the book. Because when the parent entity is deleted
        // we have to delete all the books included in the book ex. GAAP/TAX/UDB
        // 4. Delete  transactions of each entities by book either GAAP/TAX/UDB etc
        // 5. Delete trx by locations also if it is balance by location
        // 6. Delete GL budgets if exists in these entities
        // 8. Delete CSNHISTORY for each entity
        // 9. commit the trx after each entity deletion 
        $deletedEntities = $this->getDeletedEntitiesFromStruct();
        $ok = true;
        if (empty($deletedEntities)) {
            // return true if there are no deleted entities from the structure of this book
            return $ok;
        }

        // In the case of removal of parent entities we delete all the entities trx, budgets and history status
        // we delete it by each entity wise
        // We are making use of current consolidation delete and re-consolidation logic but there is a catch
        // We follow upsert logic and loop through each entries which is not needed during parent deletion or entity
        // deletion case because it is direct deletion from consglentry for given period and entities hence
        // dividing the entire code block into smaller chunks and making use of existing logic to ease code maintenance 
        foreach ($deletedEntities as $deletedEntityArray) {
            $this->entityID = $deletedEntityArray['LOCATIONKEY'];
            $bookID = $deletedEntityArray['BOOKID'];

            // If deleted entities exist then get all sublocations 
            $locationArray = $this->locationMgr->GetNestedLocationKeys([$this->entityID], false, "ALL");

            $this->cacheDeletedConsolidationBooks($deletedEntityArray);
            // begin the trx
            $source = "TierConsolidationHandler:deleteTrxFromRemovedEntities";
            XACT_BEGIN($source);

            // Delete transactions by each entity for the given period
            $ok = $ok && $this->deleteCSNDataByEntity($bookID);
            $ok = $ok
                && $this->deleteCSNHistoryAndBudgets(
                    $locationArray, $bookID, $this->deletedCSNBookBudgetHdrKey[$bookID]
                );

            $ok = $ok && XACT_COMMIT($source);
            if (!$ok) {
                $tempEntityId = isl_htmlspecialchars($this->entityID);
                $tempConsolidationBook = isl_htmlspecialchars($this->conBookName);
                $this->_Err->addIAError(
                    'IGC-0150', __FILE__ . ":" . __LINE__,
                    "Unable to delete " . $tempEntityId . " entity transactions from the book "
                    . $tempConsolidationBook,
                    ['ENTITY_ID' => $tempEntityId, 'CONSOLIDATION_BOOK' => $tempConsolidationBook]
                );
                XACT_ABORT($source);
                return $ok;
            }
        }

        return $ok;
    }

    /**
     * @return array
     */
    private function getDeletedEntitiesFromStruct(): array
    {
        // When the consolidation is initiated, we get all the entities from the structure and set the status of
        // each entities to either offline is progress or in Queue.
        // There are entities whose status are still Re-consolidation required those are deleted entities
        // Fetch those entities and pass it as params
        // In this method we are going to return all the entities and respective books from csnhistory
        // whose status is Reconsolidation required for the given period and structure
        $gcTierConsolidationMgr = Globals::$g->gManagerFactory->getManager('gctierconsolidation');
        $filter = [
            'selects' => ['LOCATIONKEY', 'BOOKKEY'],
            'columnaliases' => ['LOCATIONKEY', 'BOOKID'],
            'filters' => [
                [
                    ['STRUCTURENAME', '=', $this->structureName],
                    ['TIMEPERIOD', '=', $this->periodID],
                    ['CSNSTATUS', '=', self::CSN_RECONSOLIDATION_REQUIRED]
                ]
            ],
            'orders' => [['RECORDNO', 'desc']]
        ];

        return $gcTierConsolidationMgr->GetList($filter);
    }

    /**
     * @param string $startDate
     */
    private function setNextPeriodCSNHistoryToFailed(string $startDate)
    {
        // Whenever system triggers multi period consolidation for next period,
        // 1. It validates structure, structure details, and state etc
        // 2. Each entities, it's histories, rates and many more
        // In any failures, we should update the csnhistory to failed for the next period
        // so that customers will get to know the issue Ex: structure is draft etc
        $tempStartDate = isl_htmlspecialchars($startDate);
        $this->_Err->addIAError(
            'IGC-0151', __FILE__ . ":" . __LINE__,
            "We were unable to create Multi period consolidation for period having start date "
            . $tempStartDate,
            ['START_DATE' => $tempStartDate]
        );
        // We insert errors into consolidation history table where customers can view it
        // This is not an error logging into acct_log hence tokenizing it
        $errorMessage = $this->_Err->__toString() . I18N::getSingleToken(
                'IA.THE_CONSOLIDATION_REQUEST_FAILED_PLEASE_CORRECT'
            );

        // Covert start date to period id
        $periodId = Date2Period(ReformatDate($startDate, IADATE_STDFORMAT, '/mdY'));;
        ExecStmt(
            [
                "UPDATE CSNHISTORY SET STATUS = :1, MESSAGE = :2 WHERE CNY# = :3 AND STRUCTUREKEY = :4 AND TIMEPERIOD = :5",
                self::CSN_FAIL_INTERNAL, $errorMessage, GetMyCompany(), $this->structureKey, $periodId
            ]
        );
    }

    /**
     * Sends success or failure email
     *
     * @param array $entities
     * @param bool  $ret
     */
    protected function sendEmailOfflineConsolidationStatus(array $entities, bool $ret)
    {

    }

    /**
     * @param array $params
     */
    private function sendSummaryEmailForStructureConsolidation(array $params)
    {
        $entitystatus = [];
        $gcTierConsolidationMgr = Globals::$g->gManagerFactory->getManager('gctierconsolidation');

        if (isset($this->contactEmail) && $this->contactEmail != '') {
            $filter = array(
                'selects' => ['LOCATIONNAME', 'CSNSTATUS', 'MESSAGE'],
                'filters' => [
                    [
                        ['STRUCTURENAME', '=', $this->structureName],
                        ['TIMEPERIOD', '=', $this->periodID]
                    ]
                ],
                'orders' => [['RECORDNO', 'asc']]
            );

            $csnHistoryresults = $gcTierConsolidationMgr->GetList($filter);

            foreach ($csnHistoryresults as $entity) {
                $entitystatus[$entity['LOCATIONNAME']]['STATUS'] = ($entity['CSNSTATUS']
                    == AbstractConsolidationHandler::CSN_SUCCESS) ? 'S' : 'F';
            }

            $errorMessage = $this->getCSNStructureErrorMessages();
            $period = $this->getPeriodNameForEmail($this->periodStartDate, $this->periodEndDate);
            
            $eParams = array(
                'ID' => $this->structureName,
                'PERIODNAME' => $period,
                'ENTITIES' => $entitystatus,
                'ERRORS' => $errorMessage
            );

            if (isset($params['UPDATESUCCEEDINGPERIODS']) && $params['UPDATESUCCEEDINGPERIODS'] == 'T') {
                if ($params['STARTCSNPERIOD'] != '') {
                    $prevPeriods = array();
                    for ($k = $params['STARTCSNPERIOD']; $k < $params['LASTCSNPERIOD']; $k++) {
                        list($st, $ed) = Period2Dates($k);
                        $prevPeriods[] = $this->getPeriodNameForEmail($st, $ed);
                    }
                    $eParams['PERIODSCONSOLIDATED'] = $prevPeriods;
                }
                $this->CNSMultiRunstatus($eParams, 'UPDATE', $this->contactEmail, 'Structure');
            } else {
                $this->CNSOfflineStatus($eParams, 'Structure');
            }
        }
    }

    /**
     * @return string
     */
    private function getCSNStructureErrorMessages(): string
    {
        $gcTierConsolidationMgr = Globals::$g->gManagerFactory->getManager('gctierconsolidation');
        // catch Error messages from other failed consoldation book.
        $filters = [
            'selects' => [
                [
                    'fields' => ['MESSAGE'],
                    'function' => 'DISTINCT(${1})'
                ]
            ],
            'filters' => [
                [
                    ['STRUCTURENAME', '=', $this->structureName],
                    ['TIMEPERIOD', '=', $this->periodID],
                    ['CSNSTATUS', '!=', AbstractConsolidationHandler::CSN_SUCCESS]
                ]
            ]
        ];
        $csnErrorMessages = $gcTierConsolidationMgr->GetList($filters);
        $errorMessage = $this->_Err->__toString();
        foreach ($csnErrorMessages as $message) {
            $errorMessage .= $message['MESSAGE'] . ". ";
        }
        return $errorMessage;
    }

    /**
     * @param array   &$transEntry
     * @param array   &$entryvalues
     * @param bool     $entityMgrBased
     */
    protected function copyAllMyDimensionValues(array &$transEntry, array &$entryvalues, bool $entityMgrBased)
    {
        // $entityMgrBased is set to true for historical account which is general ledger consolidation
        // We pick historical accounts current period data from GLEntry but in the case of roll-up
        // we pick from new table hence we don't do getlist so no need entity manager to convert platform
        // dimensions
        // set $entityMgrBased to false when it is a roll-up entity
        if ($entityMgrBased && $this->isRollupEntity) {
            $entityMgrBased = false;
        }
        self::copyMyDimensionValues($transEntry, $entryvalues, $entityMgrBased, 'glentry');
    }

    /**
     * @param array $deletedEntityArray
     */
    private function cacheDeletedConsolidationBooks(array $deletedEntityArray)
    {
        // Here we are going to create arrays having below items and cache it only once by book
        // 1. Default book
        // 2. UDB books included in book
        // 3. Budgets 

        $gManagerFactory = Globals::$g->gManagerFactory;
        $gcBookMgr = $gManagerFactory->getManager('gcbook');
        $budHeaderMgr = $gManagerFactory->getManager('budgetheader');
        $bookID = $deletedEntityArray['BOOKID'];
        // We cache book data here and use it all the places during deletion
        if (!isset($this->deletedCSNBooksArray[$bookID])) {
            $bookInfo = $gcBookMgr->Get($bookID);
            $this->deletedCSNBooksArray[$bookID] = $this->getConsolidationBooksArray($bookID, $bookInfo['RECORDNO']);
            if ($bookInfo['BUDGETID'] != '') {
                $budgetHeader = $budHeaderMgr->GetRaw($bookID . "-" . $bookInfo['BUDGETID']);
                $this->deletedCSNBookBudgetHdrKey[$bookID] = $budgetHeader[0]['RECORD#'];
            }
        }
    }

    /**
     * @param string $bookID
     *
     * @return bool
     */
    private function deleteCSNDataByEntity(string $bookID): bool
    {
        $entityID = $this->entityID;
        $locationList = $this->locationMgr->GetNestedLocationKeyNames([$entityID], false, "ALL");

        // 1. $locationList has all the locations and sub-location record#
        // 2. get cached all the UDB books from books
        // 3. form inclause query to delete 
        // 4. delete in batch of 1000
        // 5. Delete transactions by each entity for the given period

        $qry = ["DELETE FROM consglentry WHERE cny# = :1 and timeperiod = :2 ", GetMyCompany(), $this->periodID];
        $qry = PrepINClauseStmt($qry, array_keys($locationList), " and location# ");

        $booksArray = $this->deletedCSNBooksArray[$bookID];
        $ok = true;
        // loop through each UDB book and delete
        // it is easy to debug instead of deleting all in a chunck 
        foreach ($booksArray as $books) {
            $stmt = $qry;
            $stmt[0] = $qry[0] . " and bookkey = " . $books['CSNBOOKKEY'];
            $ok = $ok && DeleteByRowCount($stmt, self::DELETE_ROW_COUNT);
            if (!$ok) {
                $tempCSNBookId = isl_htmlspecialchars($books['CSNBOOKID']);
                //raise an error here, and don't continue because we could not remove the existing batch
                $this->_Err->addIAError(
                    'IGC-0152', __FILE__ . ":" . __LINE__,
                    "Failed to delete entry for book " . $tempCSNBookId,
                    ['CSN_BOOK_ID' => $tempCSNBookId]
                );
            }
        }

        return $ok;
    }

    /**
     * @param array $locationArray
     *
     * @return bool
     */
    protected function cacheConsolidationMethodNCITrx(array $locationArray): bool
    {
        // trait that handles all logic related to posting NCI trx
        // We cannot post NCI entries after each entity consolidation because it posts to parent entity and elimination
        // entities. We do not consolidate parent entities first all the time and the order can change and we consolidate
        // elimination entities at the end. 
        // So even if we post NCI entries to elimination entity, during elimination entity consolidation, these NCI
        // entries will be deleted.
        // We need to commit trx after each entity consolidation or else it creates issues for large data consolidation.
        // The solution will be, we just cache NCI entries of each subsidiary entities and post it after all the entity
        // consolidation is done.
        // In any worst case, NCI creates an issue, will set csnhistory to failed with valid errors but consolidation
        // entries won't be reverted or rolled-back. Customers can fix NCI entries and re-consolidation will be faster.
        // The flaw in this design is, consolidation always deletes NCI entries and consolidates and adds new NCI entries 
        // during consolidation. 
        // We merge all the NCI entries of each subsidiary entities and cache in nciGLBatches variable and perform
        // one time bulk insert than multiple bulk insert
        $ok = true;
        if ($this->consolidationMethod == GCOwnershipChildEntityManager::CONSOLIDATION_VALUE) {
            $ok = $ok && $this->cacheNCITrxForConsolidationMethod($locationArray);
            // Merge arrays and create one batch array and perform one CRUD operation using bulk insert
            $this->mergeNCIBatches();
        }
        return $ok;
    }

    /**
     * @param string $failedEntityKey
     *
     * @return bool
     */
    protected function postNCIGLBatches(string &$failedEntityKey): bool
    {
        // Process posting NCI entries after all the entities and elimination entities consolidation
        // If any error occurs during NCI posting then return false along with $failedEntityKey to update csnhistory
        // Here we should create new db transaction
        $ok = true;
        if (countArray($this->nciGLBatches) > 0) {
            $ok = $ok && $this->postTierConsNCIGLBatches($failedEntityKey);
        }

        return $ok;
    }

    /**
     * @return bool
     */
    protected function validateNTranslateElimAdjKey(): bool
    {
        // For all the tier consolidation books, elimination adj accounts are mandatory. This account is needed to post
        // auto elimination adj entry due to NCI
        if (isNullOrBlank($this->eliminationAdjAcctNo)) {
            $tempConsolidationBook = isl_htmlspecialchars($this->conBookName);
            $this->_Err->addIAError(
                'IGC-0153', __FILE__ . '.' . __LINE__, '', [],
                "Please configure Elimination adjustment account in the book " . $tempConsolidationBook,
                ['CONSOLIDATION_BOOK' => $tempConsolidationBook]
            );
            return false;
        }

        $this->eliminationAdjAcctKey = $this->getEliminationAdjAcctKey();
        return true;
    }

    /**
     * @param string $updateSucceedingPeriods
     *
     * @return void
     */
    private function cacheMultiPeriodTierCons($updateSucceedingPeriods = 'F') : void
    {
        if ($updateSucceedingPeriods == 'T') {
            $this->isMultiPeriodTierCSN = true;
        }
    }

    /**
     * @param Lock $lock
     */
    protected function updateHistoryAfterConsolidation($lock)
    {
        // In the case of multi period tier consolidation, there is an issue that during consolidation, status shows
        // success even though the next period consolidation exists which misleads customers.
        // This is because, after each period consolidation system deletes csnhistory records for status 'Multi run'
        // and releases book level lock. And fetches next period entities, rates, gets order of consolidation
        // (hierarchical query) and many more. It is seemed that hierarchical query is taking longer time and after that
        // system updates csnhistory for next period. We see there is a significant difference and delay.
        // After analyzing lots of approaches, decided to first update all the csnhistory status to Multi run
        // for the first time. And replace updateHistory method by dbquery because updateHistory deletes all the 
        // period records for status in ('M')
        // This change is only for multi period tier consolidation only and rest remains same

        if ($this->isMultiPeriodTierCSN && $this->enableLocking) {
            // When multi period consolidation starts, system updates all the csnhistory status to
            // 'Offline consolidation in progress' and it deletes current period status after each consolidation
            // If multi period tier consolidation then after consolidation
            // 1. Release book level lock
            // 2. Delete csnhistory for status in ('O') for given period
            // In view map, system shows multi run status color similar to in queue color code and if we set current
            // status as multi run then it contradicts hence setting it to Offline in progress 
            $ok = true;
            $stmt = "DELETE FROM CSNHISTORY WHERE CNY# = :1 AND BOOKKEY = :2 AND TIMEPERIOD = :3 AND STATUS IN (:4)";
            $stmt = [
                $stmt, $this->cny, $this->conBookName, $this->periodID, self::CSN_OFFLINE_INTERNAL
            ];
            $ok = $ok && ExecStmt($stmt);
            $ok = $ok && $lock->releaseLock();
            if (!$ok) {
                $tempConsolidationBook = isl_htmlspecialchars($this->conBookName);
                $this->_Err->addIAError(
                    'IGC-0154', __FILE__ . ":" . __LINE__,
                    "We were unable to delete consolidation history for book " . $tempConsolidationBook . ". 
                    Please wait a few minutes and then try again.",
                    ['BOOK_ID' => $tempConsolidationBook]
                );
            }
        } else {
            parent::updateHistoryAfterConsolidation($lock);
        }
    }

    /**
     * @param array $params
     *
     * @return bool
     */
    private function updateMultiPeriodCSNHistory(array $params): bool
    {
        // In the case of multi period tier consolidation, there is an issue that during consolidation, status shows
        // success even though the next period consolidation exists which misleads customers.
        // To solve that Update csnhistory to Multi run from the next period in the case of multi period tier consolidation.
        // This should happen only for the first multi period consolidation and only for status is success or Failures
        // When entities are deleted we set status as Reconsolidation required, to handle that we only update
        // csnhistory status as Multi run only for Success or Failures
        $ok = true;
        if (isset($params['UPDATESUCCEEDINGPERIODS']) && $params['UPDATESUCCEEDINGPERIODS'] == 'T'
            && $params['STARTCSNPERIOD'] == $this->periodID
        ) {
            $ok = $ok
                && ExecStmt(
                    [
                        "UPDATE CSNHISTORY SET STATUS = :1, MESSAGE = :2 WHERE CNY# = :3 AND STRUCTUREKEY = :4 AND 
                     TIMEPERIOD > :5 AND STATUS IN (:6, :7)",
                        self::CSN_MULTI_RUN_INTERNAL, I18N::getSingleToken(self::MULTI_PERIOD_CSN_IN_PROGRESS_LABEL),
                        $this->cny, $params['STRUCTUREKEY'], $this->periodID, self::CSN_SUCCESS_INTERNAL,
                        self::CSN_FAIL_INTERNAL
                    ]
                );
            if (!$ok) {
                $this->_Err->addError(
                    "IGC-0155", __FILE__ . ':' . __LINE__, "Error in processing consolidation request",
                    'System failed to update multi period consolidation status to in progress.',
                    "Please contact Sage Intacct support."
                );
            }
        }

        return $ok;
    }

    /**
     * getCSNStatus
     *
     * @param bool $offline
     * @param bool $multirun
     *
     * @return string self::CSN_OFFLINE_INTERNAL or CSN_IN_PROGRESS_INTERNAL constant
     */
    protected function getCSNStatus(bool $offline, bool $multirun = false): string
    {
        // For tier consolidation it is always offline
        // Even in the case of multi period consolidation, no need to insert csnhistory statys as M because we 
        // insert as M for subsequent periods status. If we insert as M for current period then it is going to 
        // confuse user 
        // In view map, system shows multi run status color similar to in queue color code and if we set current
        // status as multi run then it contradicts hence setting it to Offline in progress
        return self::CSN_OFFLINE_INTERNAL;
    }

    /**
     * Get the Start and End dates for the recurrence
     *
     * @param array $params
     *
     * @return int
     */
    public function getExecutionDateForTierCSN(&$params)
    {
        // In this method, we need to set STARTCSNPERIOD, STARTDATE, LASTCSNPERIOD and system will automatically
        // consolidates without any core logic change
        $recurDate = $params['RECURTDATE'];
        $structureName = $params['STRUCTURENAME'];
        
        $gManagerFactory = Globals::$g->gManagerFactory;
        $recurPeriod = Date2Period($recurDate);

        // In the multi period consolidation we consolidate till the last consolidation. but in the recurrence, we
        // consolidate till last month
        // $recurPeriod decides LASTCSNPERIOD and $csnStartPeriod decides STARTCSNPERIOD
        $csnEndPeriod = $recurPeriod - 1;

        // If the last consolidation is failed then system fails to consolidate the next period.
        // Let's get the latest successful consolidation period and consolidate from that period
        // Same way it is performed in normal consolidation
        $csnStartPeriod = $this->getLatestSuccessfulCSNPeriod($params);

        // In the multi period consolidation we consolidate till the last consolidation period.
        // but in the recurrence, we consolidate till last month
        // Case -1 :
        // If the current period is already consolidated then
        // In IGC, we consolidate one month prior and delete current period
        // In Tier, there is no concept of deletion hence we need to modify the logic such that, we should consolidate
        // always till last month or the latest consolidation if last month < latest consolidation
        if ($csnStartPeriod == -1) {
            // If the consolidation is not done then consolidate from first activated structure
            // What if current month is the first activated structure (today) then consolidate just today
            $structDetailMgr = $gManagerFactory->getManager('gcownershipstructuredetail');
            $csnStartPeriod = $structDetailMgr->getFirstActivatedStructurePeriod($structureName);
        } elseif ($recurPeriod == $csnStartPeriod) {
            // We always consolidate from last month even when current period is consolidated.
            // If the $csnStartPeriod is same as current period then we need to consolidate last month + current month
            // We perform same way in consolidation also.
            $csnStartPeriod = $csnStartPeriod - 1;
        } elseif ($recurPeriod > ($csnStartPeriod + 1)) {
            // In a case, where the system has consolidated till last period, REPEATBY is weekly or daily, 
            // next recurrence should only consolidate last period hence increament logic doesn't work here

            // Consolidation exists and consolidation starting period is prior to current period
            // then start from its next period
            $csnStartPeriod = $csnStartPeriod + 1;
        }

        // If there are any consolidation happened in the current period then we need to consolidate current period
        // also so ($recurPeriod - 1) doesn't work in this case.
        // Case - 2:
        // When the last couple of period consolidations are failed including current period then also we need to
        // consolidate till current period. 
        // We need to pick the latest consolidation history period not just successful one and consolidate till then
        $lastCSNPeriod = $this->getLatestCSNPeriod($params);
        if ($recurPeriod == $lastCSNPeriod) {
            $csnEndPeriod = $recurPeriod;
        }

        // if there are no active structure then throw error
        if ($csnStartPeriod == -1) {
            $this->_Err->addError(
                'IGC-0120', __FILE__ . ":" . __LINE__, '',
                "Invalid Ownership structure detail. Make sure respective structure is Active and Approved."
            );
            return false;
        }

        // In any case, if calculation goes wrong and start period > end period then throw error
        if ($csnStartPeriod > $csnEndPeriod) {
            $this->_Err->addError('IGC-0315', __FILE__ . ':' . __LINE__, "Start date cannot be greater than end date");
            return false;
        }

        $params['STARTCSNPERIOD'] = $csnStartPeriod;
        $params['STARTDATE'] = Period2Dates($csnStartPeriod)[0];
        $params['LASTCSNPERIOD'] = $csnEndPeriod;
        $params['UPDATESUCCEEDINGPERIODS'] = 'T';
        return true;
    }

    /**
     * @param array $params
     *
     * @return int
     */
    private function getLatestSuccessfulCSNPeriod(array $params): int
    {
        // If the last consolidation is failed then system fails to consolidate the next period.
        // Let's get the latest successful consolidation period and consolidate from that period
        // Same way it is performed in normal consolidation
        // System needs to get the latest successful consolidation period to start the recurrence
        // This period decides the startdate of recurrence
        // What if the latest consolidation is failed?
        // What if one of entity consolidation is failed?
        // We need to check whether all the entities of the latest period are succeeded then pick that period

        $query = "SELECT MAX(TIMEPERIOD) AS TIMEPERIOD FROM CSNHISTORY WHERE CNY# = :1 AND STRUCTUREKEY = :2 
                    AND STATUS = :3 AND TIMEPERIOD NOT IN 
                    (SELECT TIMEPERIOD FROM CSNHISTORY WHERE CNY# = :1 AND STRUCTUREKEY = :2 AND STATUS != :3)";
        $latestPeriod = QueryResult([$query, $this->cny, $params['STRUCTUREKEY'], self::CSN_SUCCESS_INTERNAL]);

        $lastCSNPeriod = -1;
        if ($latestPeriod[0]['TIMEPERIOD'] != '') {
            $lastCSNPeriod = $latestPeriod[0]['TIMEPERIOD'];
        }
        return $lastCSNPeriod;
    }

    /**
     * @return bool
     */
    protected function isEntityFirstPeriodConsolidation(): bool
    {
        // Check whether the entity is getting consolidated first period.
        // Return true if first period consolidation else false 
        $gManagerFactory = Globals::$g->gManagerFactory;
        $gcTierConsolidationMgr = $gManagerFactory->getManager('gctierconsolidation');

        $filter = [
            'selects' => [
                [
                    'fields' => array('RECORDNO'),
                    'function' => 'count(${1})'
                ]
            ],
            'filters' => [
                [
                    ['STRUCTURENAME', '=', $this->structureName],
                    ['TIMEPERIOD', '<', $this->periodID],
                    ['BOOKKEY', '=', $this->conBookName],
                    ['LOCATIONKEY', '=', $this->entityID],
                ]
            ],
            'columnaliases' => ['CNT']
        ];

        $result = $gcTierConsolidationMgr->GetList($filter);
        return ($result[0]['CNT']) > 0 ? false : true;
    }

    /**
     * getStructureLockName
     *
     * @return string
     */
    protected function getStructureLockName(): string
    {
        return 'CSNSTRUCTURE-' . $this->cny . '-' . md5($this->structureName);
    }

    /**
     * @param string $bookkey
     * @param array  $existingEntities
     *
     * @return array
     */
    private function getAllSubsidiaryEntitiesByBook(string $bookkey, array $existingEntities = []
    ): array
    {
        // Get all the entities which are direct and indirectly mapped to the book
        $query = self::getHierarchicalQueryForCons('ENTITYKEY', 'START WITH p.bookkey = :3');
        $query
            = "$query
                SELECT 
                    entitykey, l
                FROM 
                    rownumber
                WHERE
                    rn = 1
                ORDER BY
                    l ASC";

        $res = QueryResult([$query, $this->cny, $this->structureDetailKey, $bookkey]);

        if (countArray($res) > 0) {
            foreach ($res as $entity) {
                $existingEntities[] = $entity['ENTITYKEY'];
            }
        }

        // To eliminate indirect relationship, we need all the subsidiary entities in the consolidating book
        // System needs to pull all the direct and indirect entities within the structure inside this book.
        // In the multi level ownership structure, we need to fetch all the entities within the each subsidiary book
        // to exclude already eliminated transactions and need to fetch all the entities within the consolidating
        // book to support elimination by affiliate entity
        // To achieve both, let's not run hierarchical query many times, instead, lets run it for each rollup books
        // within the parent entity and cache it for each entity. This cache can be used to eliminate across the structure
        $this->allEntityKeys = array_merge($this->allEntityKeys, $existingEntities);
        return $existingEntities;
    }

    /**
     * @param string $bookID
     * @param string $parentEntityKey
     */
    private function cacheSubsKeysAndElimAcctsRollUp(string $bookID, string $parentEntityKey)
    {
        // This method fetches all the subsidiary entities for the given parent entity and the book
        // 1. Get parent entity object record#
        // 2. Fetch all the subsidiary entitykey from the parent entity record#
        // 3. Cache all the NCI accounts included inside the parent entity

        $gManagerFactory = Globals::$g->gManagerFactory;
        $structEntityMgr = $gManagerFactory->getManager('gcownershipentity');
        $params = [
            'selects' => ['RECORDNO', 'BOOK.BOOKKEY'],
            'filters' => [
                [
                    ['BOOK.BOOKID', '=', $bookID],
                    ['PARENTENTITYKEY', '=', $parentEntityKey],
                    ['GCOWNERSHIPDETAILKEY', '=', $this->structureDetailKey],
                    ['STRUCTURENAME', '=', $this->structureName],
                ]
            ]
        ];

        $result = $structEntityMgr->GetList($params);
        $parentEntityRecordNo = ($result[0]['RECORDNO']) ?? '';
        $bookKey = ($result[0]['BOOK.BOOKKEY']) ?? '';

        // Get all the subsidiary entities for this parent entity
        if ($parentEntityRecordNo != '' && $bookKey != '') {
            $this->subsidiaryEntityKeys = [];
            $this->subsidiaryEntityKeys[] = $this->entityID;
            $this->subsidiaryEntityKeys = $this->getAllSubsidiaryEntitiesByBook($bookKey, $this->subsidiaryEntityKeys);

            $locEntityMgr = $gManagerFactory->getManager('location');
            $params = [
                'selects' => ['RECORDNO', 'LOCATIONID'],
                'filters' => [[['RECORDNO', 'IN', $this->subsidiaryEntityKeys]]]
            ];

            $gcBookEntities = $locEntityMgr->GetList($params);

            if (countArray($gcBookEntities) > 0) {
                foreach ($gcBookEntities as $entity) {
                    $this->subsAllEntitiesArray[] = [
                        'ENTITYID' => $entity['LOCATIONID']
                    ];
                }

                // Exclude already eliminated accounts in the lower level when the Affiliate entity based elimination is
                // selected. So cache all the eliminated accounts in the lower level and exclude those balances
                // Cache elimination accounts only when the given entity is rollup entity and elimination by affiliate
                // entity option is enabled
                $this->subsidiaryBookElimAccts = $this->getConsBookElimAccounts($this->subsAllEntitiesArray, $bookID);
            }
            
            // Cache all the NCI accounts included inside the parent entity
            // Exclude already eliminated NCI entries such as (NCI Elimination accounts) i.e Investment, capital,
            // subsidiary revenue accounts should not be rolled up when ending balance is zero and Allocate subs income
            // option is enabled
            $gcOwnershipChildEntityMgr = $gManagerFactory->getManager('gcownershipchildentity');
            $filter = [
                'selects' => [
                    'INVESTMENTINSUBSACCTKEY', 'SUBSIDIARYREVENUEACCTKEY', 'CONTRIBUTEDCAPITALACCTKEY'
                ],
                'filters' => [
                    [
                        ['GCOWNERSHIPENTITYKEY', '=', $parentEntityRecordNo],
                        ['CONSOLIDATIONMETHOD', '=', GCOwnershipChildEntityManager::CONSOLIDATION_VALUE],
                        ['ISALLOCATESUBSIDIARYINCOME', '=', 'true'],
                    ]
                ]
            ];

            $nciAccountDetails = $gcOwnershipChildEntityMgr->GetList($filter);
            $nci = [];
            foreach ($nciAccountDetails as $nciAccounts) {
                // To avoid storing duplicate accounts in array, setting accounts in array key 
                $nci[$nciAccounts['INVESTMENTINSUBSACCTKEY']] = '';
                $nci[$nciAccounts['SUBSIDIARYREVENUEACCTKEY']] = '';
                $nci[$nciAccounts['CONTRIBUTEDCAPITALACCTKEY']] = '';
            }

            if (!empty($nci)) {
                $this->subsidiaryBookElimAccts = array_merge($this->subsidiaryBookElimAccts, array_keys($nci));
            }
        }
    }

    /**
     * @param array $bindQuery
     */
    private function excludeAlreadyEliminatedAcctTrx(array &$bindQuery)
    {
        // Remove already eliminated account transactions while rolling up to the next level
        // Execute below codes only when there IET accounts setup

        if (countArray($this->subsidiaryBookElimAccts) > 0 && countArray($this->subsidiaryEntityKeys) > 0) {
            $bindQuery[0] = "WITH cte as ( " . $bindQuery[0] . " ) 
                         SELECT * FROM cte 
                         MINUS
                         SELECT * FROM cte WHERE ";

            $bindQuery = PrepINClauseStmt(
                $bindQuery, $this->subsidiaryEntityKeys, ' AFFILIATEENTITYDIMKEY ', true, 'affiliatedim'
            );

            $bindQuery = PrepINClauseStmt(
                $bindQuery, $this->subsidiaryBookElimAccts, ' AND ACCOUNT# ', true, 'acct_no'
            );
        }
    }

    /**
     * initbookElimAccountsForCons
     *
     * @param array $entities
     */
    protected function initbookElimAccountsForCons(array $entities)
    {
        // When the Elimination by Affiliate option is enabled system needs to perform elimination for the indirect
        // IET mappings. System needs to fetch all the IET account mappings for each entities and subsidiary entities
        // and cache all the accounts 
        // subsAllEntitiesArray holds all the subsidiary entities in the given book and $entities holds current 
        // all the entities and we merge to get all the entities and retreive IET accounts
        if (empty($this->bookElimAccts) && countArray($this->subsAllEntitiesArray) > 0
            && $this->isEliminateByAffiliateEntityEnabled()
        ) {
            $allEntities = array_merge($this->subsAllEntitiesArray, $entities);
            $this->bookElimAccts = $this->getConsBookElimAccounts($allEntities, $this->conBookName);
        } else {
            parent::initbookElimAccountsForCons($entities);
        }
    }
}
