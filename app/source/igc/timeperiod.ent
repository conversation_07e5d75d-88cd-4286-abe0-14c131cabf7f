<?php

/**
 *    FILE:            timeperiod.ent
 *    AUTHOR:          <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
 *    DESCRIPTION:     ent for Period of FROMPERIOD in gcownershipstructuredetail
 *                     it will act as master table for fromperiod where it has
 *                     all the names in table e.g: 'Jan 2020'
 *
 *    (C) 2008, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */
$kSchemas['timeperiod'] = [
    'schema' => [
        'RECORDNO' => 'record#',
        'ID' => 'ID',
        'PERIODNAME' => 'periodname',
        'STARTDATE' => 'startdate',
        'ENDDATE' => 'enddate'
    ],
    'object' => [
        'RECORDNO',
        'ID',
        'PERIODNAME',
        'STARTDATE',
        'ENDDATE'
    ],

    'fieldinfo' => [
        $gRecordNoFieldInfo,
        [
            'fullname' => 'IA.ID',
            'type' => ['ptype' => 'text', 'type' => 'text', 'maxlength' => 20],
            'required' => false,
            'desc' => 'IA.ID',
            'path' => 'ID',
            'id' => 1,
        ],
        [
            'fullname' => 'IA.PERIOD_NAME',
            'type' => ['ptype' => 'text', 'type' => 'text', 'maxlength' => 50],
            'required' => false,
            'desc' => 'IA.PERIOD_NAME',
            'path' => 'PERIODNAME',
            'id' => 2,
        ],
        [
            'fullname' => 'IA.START_DATE',
            'desc' => 'IA.START_DATE',
            'type' => $gDateType,
            'required' => false,
            'path' => 'STARTDATE',
            'id' => 3,
        ],
        [
            'fullname' => 'IA.END_DATE',
            'desc' => 'IA.END_DATE',
            'type' => $gDateType,
            'required' => false,
            'path' => 'ENDDATE',
            'id' => 4,
        ]
    ],

    'api' => [
        'PERMISSION_READ' => 'ALL',
        'PERMISSION_CREATE' => 'NONE',
        'PERMISSION_UPDATE' => 'NONE',
        'PERMISSION_DELETE' => 'NONE'
    ],

    'table' => 'v_timeperiod',
    'vid' => 'ID',
    'module' => 'atlas',
    'printas' => 'IA.TIMEPERIOD',
    'pluralprintas' => 'IA.TIMEPERIOD',
];