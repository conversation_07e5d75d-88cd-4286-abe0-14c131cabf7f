<?php

use DBSchemaInfo;

class SNLDispatcher extends RunObjectDispatcher
{
    const SNLRUNPROCESSOR_PREFIX = 'Savings and Loan Run Processor';
    /**
     * @inheritDoc
     */
    public function getLogPrefix(): string
    {
        return self::SNLRUNPROCESSOR_PREFIX;
    }

    /**
     * @inheritDoc
     */
    public function getConfigSectionName(): string
    {
        return 'SNL_RUN_DISPATCHER';
    }

    /**
     * @inheritDoc
     */
    public function getDBBandwidth(DBSchemaInfo $dbInfo): int
    {
        return $dbInfo->getSNLRunWorkflowBandwidth();
    }

    /**
     * @inheritDoc
     */
    public function getDBTimeLimit(DBSchemaInfo $dbInfo): int
    {
        return $dbInfo->getSNLRunWorkflowTimeLimit();
    }

    /**
     * @inheritDoc
     */
    public function getControlLockPrefix(): string
    {
        return 'snlDispatch';
    }

    /**
     * @inheritDoc
     */
    protected function getModule(): string
    {
        return 'SL';
    }

    /**
     * @return bool
     */
    public function checkFeatureFlag()
    {
        return true;
    }
}