<?
/**
 * ENT for DBSERVER Throttle data
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (C)2023 Sage Intacct Corporation, All Rights Reserved
 */

global $gRecordNoFieldInfo, $gWhenCreatedFieldInfo, $gWhenModifiedFieldInfo, $gStatusFieldInfo, $gRecordNoFormat, $gDateType;

$kSchemas['dbserverthrottle'] = array(
    'object' => array(
        'RECORDNO',
        'DBSERVER',
        'THROTTLE_LIMIT',
        'THROTTLE_LIMIT_OVERRIDE',
        'END_DATE',
        'NOTES',
        'STATUS',
        'SERVICE_NAME',
        'SERVICE_VALUE',
        'THROTTLE_COEFFICIENT',
        'WHENCREATED',
        'WHENMODIFIED',
    ),

    'schema' => array(
        'RECORDNO'                => 'record#',
        'DBSERVER'                => 'dbserver',
        'THROTTLE_LIMIT'          => 'throttle_limit',
        'THROTTLE_LIMIT_OVERRIDE' => 'throttle_limit_override',
        'END_DATE'                => 'end_date',
        'NOTES'                   => 'notes',
        'STATUS'                  => 'status',
        'SERVICE_NAME'            => 'service_name',
        'SERVICE_VALUE'           => 'service_value',
        'THROTTLE_COEFFICIENT' => 'throttle_coefficient',
        'WHENCREATED'             => 'whencreated',
        'WHENMODIFIED'            => 'whenmodified',
    ),

    'fieldinfo' => array(
        array(
            'path'     => 'DBSERVER',
            'fullname' => 'IA.DBSERVER',
            'desc'     => 'IA.DBSERVER',
            'type'     => array(
                'ptype' => 'text',
                'type'  => 'text',
            ),
            'required' => true
        ),
        array(
            'path'     => 'THROTTLE_LIMIT',
            'fullname' => 'IA.THROTTLE_LIMIT',
            'desc'     => 'IA.THROTTLE_LIMIT',
            'type'     => array(
                'ptype' => 'integer',
                'type'  => 'integer',
            ),
        ),
        array(
            'path'     => 'THROTTLE_LIMIT_OVERRIDE',
            'fullname' => 'IA.THROTTLE_LIMIT_OVERRIDE',
            'desc'     => 'IA.THROTTLE_LIMIT_OVERRIDE',
            'type'     => array(
                'ptype' => 'integer',
                'type'  => 'integer',
            ),
        ),
        array(
            'path'     => 'END_DATE',
            'fullname' => 'IA.OVERRIDE_END_DATE',
            'desc'     => 'IA.OVERRIDE_END_DATE',
            'type'     => $gDateType,
        ),
        array(
            'path'     => 'NOTES',
            'fullname' => 'IA.NOTES',
            'desc'     => 'IA.NOTES',
            'type'     => array(
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 255,
            ),
        ),
        array(
            'path'     => 'SERVICE_NAME',
            'fullname' => 'IA.SERVICE_NAME',
            'desc'     => 'IA.SERVICE_NAME',
            'type'     => array(
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 30,
            ),
        ),
        array(
            'path'     => 'SERVICE_VALUE',
            'fullname' => 'IA.VALUE',
            'desc'     => 'IA.VALUE',
            'type'     => array(
                'ptype' => 'integer',
                'type'  => 'integer',
            ),
        ),
        array(
            'path'     => 'THROTTLE_COEFFICIENT',
            'fullname' => 'IA.THROTTLE_COEFFICIENT',
            'desc'     => 'IA.THROTTLE_COEFFICIENT',
            'type'     => array(
                'ptype' => 'decimal',
                'type'  => 'decimal',
            ),
            'default'  => 1,
        ),
        $gRecordNoFieldInfo,
        $gStatusFieldInfo,
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENMODIFIED',
        'WHENCREATED',
        'END_DATE'
    ),
    'table'         => 'dbserverthrottle',
    'vid'           => 'DBSERVER',
    'autoincrement' => 'RECORDNO',
    'module'        => 'co',
    'global'        => true,
    'globalschema'  => true
);
