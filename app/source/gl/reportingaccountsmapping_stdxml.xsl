<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format">
<xsl:output encoding="UTF-8"/>

<xsl:template match="report">
	<report 
		showHeader="{@showHeader}"
		orientation="{@orientation}" report_date="{@reportdate}" report_time="{@reporttime}"
		align_currency="left" page_number="Y" action="reportor.phtml" sess="{@sess}" 
		done="" footer_allpages="Y">

		<xsl:if test="(@orientation = 'Portrait')">
			<xsl:attribute name="maxfit">Y</xsl:attribute>
		</xsl:if>
        <company s="2">
            <xsl:value-of select="@co"/>
        </company>
		<title s="3" titleNum="1"><xsl:value-of select="@title"/></title>
		<title s="4" titleNum="2"><xsl:value-of select="@title2"/></title>

		<logo align="start"><xsl:value-of select="@logo"/></logo>

		<footer s="5" lines="1" footerNum="1"><xsl:value-of select="@titlecomment"/></footer>
        <xsl:for-each select="rtdim">
        	<rtdim name="@name" s="4">
        		<name><xsl:value-of select="@name"/></name>
				<value><xsl:value-of select="@value"/></value>
			</rtdim>
        </xsl:for-each>
		
		<xsl:call-template name="buildheaderrows"/>
		<xsl:apply-templates/>
		<xsl:call-template name="stylegroups"/>
	</report>
</xsl:template>

<xsl:template name="buildheaderrows">
	<xsl:variable name="deptwidth">
		<xsl:choose>
			<xsl:when test="@requiredept = 1">0</xsl:when>
			<xsl:otherwise>3</xsl:otherwise>
		</xsl:choose>
	</xsl:variable>
	<xsl:variable name="locwidth">
		<xsl:choose>
			<xsl:when test="@requireloc = 1">0</xsl:when>
			<xsl:otherwise>3</xsl:otherwise>
		</xsl:choose>
	</xsl:variable>
	<xsl:variable name="statuswidth">
		<xsl:choose>
			<xsl:when test="@requirestatus = 1">0</xsl:when>
			<xsl:otherwise>3</xsl:otherwise>
		</xsl:choose>
	</xsl:variable>
	<xsl:variable name="colwidth">
		<xsl:choose>
			<xsl:when test="@isstatistical = 0">12</xsl:when>
			<xsl:otherwise>31</xsl:otherwise>
		</xsl:choose>
	</xsl:variable>
	<header>
		<hrow s="7">
			<hcol s="11" id="0" width="{$colwidth + $deptwidth + $locwidth + $statuswidth}"><xsl:value-of select="@header_col1"/></hcol>
			<hcol s="11" id="1" width="{$colwidth + $deptwidth + $locwidth + $statuswidth + 5}"><xsl:value-of select="@header_col2"/></hcol>
			<xsl:if test="@isstatistical = 0">
				<hcol s="11" id="2" width="5"><xsl:value-of select="@header_col3"/></hcol>
				<hcol s="11" id="3" width="9"><xsl:value-of select="@header_col4"/></hcol>
				<hcol s="11" id="4" width="11"><xsl:value-of select="@header_col5"/></hcol>
			</xsl:if>
			<xsl:if test="@includeTaxCode = 1">
				<hcol s="11" id="8" width="9"><xsl:value-of select="@header_col5a"/></hcol>
				<hcol s="11" id="8" width="9"><xsl:value-of select="@header_col5b"/></hcol>
			</xsl:if>
			<xsl:if test="@requirestatus = 1">
				<hcol s="11" id="7" width="9"><xsl:value-of select="@header_col8"/></hcol>
			</xsl:if>
			<xsl:apply-templates select="CS/C"/>
		</hrow>		
	</header>
</xsl:template>

<xsl:template match="C">
	<hcol s="11" width="22">
		<xsl:value-of select="@L"/>
	</hcol>
</xsl:template>

<xsl:template match="GLACCOUNT">
	<body s="1">
		<xsl:apply-templates/>
	</body>
</xsl:template>
<xsl:template match="NODATA">
    <xsl:if test="string(@NODATA)=1">
	<body s="1">
		<row s="9">
			<col id="0" s="10" colspan="3">IA.NO_DATA_FOUND_FOR_SELECTED_FILTERS</col>
		</row>
	</body>
    </xsl:if>
</xsl:template>

<xsl:template match="REPORTINGHEADERS">
	<col s="10">
		<xsl:value-of select="@PV"/>
	</col>
</xsl:template>

<xsl:template match="VALUES">
	<row s="9">
		<col id="0" s="10">
			<xsl:value-of select="@ACCOUNTNO"/>
		</col>
		<col id="1" s="10"><xsl:value-of select="@TITLE"/></col>
		<xsl:if test="/reportdata/report/@isstatistical = 0">
			<col id="2" s="10"><xsl:value-of select="@ACCOUNTTYPE"/></col>
			<col id="3" s="10"><xsl:value-of select="@NORMALBALANCE"/></col>
			<col id="4" s="10"><xsl:value-of select="@CLOSETOACCTKEY"/></col>
		</xsl:if>
		<xsl:if test="/reportdata/report/@includeTaxCode = 1">
			<col id="8" s="10"><xsl:value-of select="@TAXCODE"/></col>
			<col id="9" s="10"><xsl:value-of select="@MRCCODE"/></col>
		</xsl:if>
		<xsl:if test="/reportdata/report/@requirestatus = 1">
			<col id="7" s="10"><xsl:value-of select="@STATUS"/></col>
		</xsl:if>
		<xsl:apply-templates select="REPORTINGHEADERS"/>
	</row>
</xsl:template>



<xsl:template name="stylegroups">
	<stylegroups>
		<stylegroup id="1" name="body" class="BODY" font="Helvetica" size="8" weight="normal" style="normal" color="black"/>
		<stylegroup id="2" name="company" class="W" font="Helvetica" size="12" weight="bold" style="normal" color="black"/>
		<stylegroup id="3" name="title" class="W" font="Helvetica" size="12" weight="bold" style="normal" color="black"/> 	
		<stylegroup id="4" name="title2" class="W" font="Helvetica" size="12" weight="bold" style="normal" color="black"/>	
		<stylegroup id="5" name="footer" class="FOOT" font="Helvetica" size="9" weight="bold" style="normal" color="black" alignment="M"/>
		<stylegroup id="6" name="header_row" class="HEAD" font="Helvetica" size="9" weight="bold" style="normal" color="black"/>
		<stylegroup id="7" name="header_row1" class="HEAD" font="Helvetica" size="9" weight="bold" style="normal" color="black" underline_type="1"/>
		<stylegroup id="8" name="header_CN" type="number"/>
		<stylegroup id="9" name="trans_row" font="Helvetica" size="8" style="normal" weight="normal" color="black"/>
		<stylegroup id="10" name="trans_CT" class="W"/>
		<stylegroup id="11" name="header_cell" class="REPCOLHDL" />
	</stylegroups>
</xsl:template>

</xsl:stylesheet>
