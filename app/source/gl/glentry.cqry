<?

/*$kglentryQueries['QRY_GLENTRY_SELECT_POSTTEMP_BY_PARENT'] = array(

	'QUERY' => "SELECT glentry.record#,glentry.status,glentry.entry_date,glentry.batch#,glentry.document,glentry.description,glaccount.acct_no,glentry.amount,glentry.units,glentry.currency_type,glentry.user#,location.location_no,dept.dept_no,glentry.line_no,glentry.tr_type,glentry.adj,glentry.cleared,glentry.clrdate,glentry.timeperiod,transtmplentry.label,transtmplentry.deptuserprov,transtmplentry.locuserprov,transtmplentry.tr_type, transtmplentry.hidedept, transtmplentry.hideloc FROM glentry glentry,glaccount glaccount,glbatch glbatch,transtmplbatch transtmplbatch,transtmplentry transtmplentry,department dept,location location WHERE (glentry.batch# =  ? ) and glentry.account# = glaccount.record#  (+)   and glaccount.cny# (+) = ? and transtmplentry.line_no = glentry.line_no and transtmplentry.glacctkey = glentry.account# (+) and transtmplbatch.record# = transtmplentry.transtmplkey  (+)   and transtmplentry.cny# (+) = ?  and glbatch.templatekey = transtmplbatch.record#   and transtmplbatch.cny# (+) = ?  and glentry.batch# = glbatch.record#   and glbatch.cny# (+) = ?  and glentry.dept# = dept.record#  (+)   and dept.cny# (+) = ?  and glentry.location# = location.record#  (+)   and location.cny# (+) = ?  and glentry.cny# (+) = ? order by line_no",

	'ARGTYPES' => array('integer', 'integer', 'integer', 'integer', 'integer', 'integer', 'integer', 'integer'),

);*/

$kglentryQueries['QRY_GLENTRY_ALLOC'] = array(
    'QUERY' => "SELECT glentry.amount, department.dept_no DEPARTMENTID, department.title DEPARTMENTNAME, location.location_no LOCATIONID, location.name LOCATIONNAME, project.PROJECTID, project.name PROJECTNAME, customer.CUSTOMERID, customer.name CUSTOMERNAME, vendor.VENDORID, vendor.name VENDORNAME, employee.EMPLOYEEID, employee_contact.name EMPLOYEENAME, item.ITEMID, item.name ITEMNAME, class.CLASSID, class.name CLASSNAME FROM glentry, location , department, project project, customermst customer, vendormst vendor, employeemst employee,contact employee_contact,  icitem item, class class WHERE  glentry.allocationkey= ? and glentry.GLBATCH#=? AND glentry.lineno = ? AND glentry.cny# = ? AND glentry.cny# = department.cny# (+) AND glentry.dept# = department.record# (+) AND glentry.cny# = location.cny# (+) AND glentry.location# = location.record# (+) AND glentry.projectdimkey = project.record# (+) AND glentry.cny# = project.cny# (+) AND glentry.customerdimkey = customer.record# (+) AND glentry.cny# = customer.cny# (+) AND glentry.vendordimkey = vendor.record# (+) AND glentry.cny# = vendor.cny# (+) AND glentry.employeedimkey = employee.record# (+) AND glentry.cny# = employee.cny# (+) AND employee.CONTACTKEY = employee_contact.record# (+) AND employee.cny#  = employee_contact.cny# (+) AND glentry.itemdimkey = item.record# (+) AND glentry.cny# = item.cny# (+) AND glentry.classdimkey = class.record# (+) AND glentry.cny# = class.cny# (+) ",
    'ARGTYPES' => array('integer', 'integer', 'integer'),
);

$kglentryQueries['QRY_GLENTRY_ALLOC_MCP'] = array(
    'QUERY' => "SELECT glentry.trx_amount AMOUNT, department.dept_no DEPARTMENTID, department.title DEPARTMENTNAME, location.location_no LOCATIONID, location.name LOCATIONNAME, project.PROJECTID, project.name PROJECTNAME, customer.CUSTOMERID, customer.name CUSTOMERNAME, vendor.VENDORID, vendor.name VENDORNAME, employee.EMPLOYEEID, employee_contact.name EMPLOYEENAME, item.ITEMID, item.name ITEMNAME, class.CLASSID, class.name CLASSNAME FROM glentry, location , department, project project, customermst customer, vendormst vendor, employeemst employee,contact employee_contact,  icitem item, class class WHERE  glentry.allocationkey= ? and glentry.GLBATCH#=? AND glentry.lineno = ? AND glentry.cny# = ? AND glentry.cny# = department.cny# (+) AND glentry.dept# = department.record# (+) AND glentry.cny# = location.cny# (+) AND glentry.location# = location.record# (+) AND glentry.projectdimkey = project.record# (+) AND glentry.cny# = project.cny# (+) AND glentry.customerdimkey = customer.record# (+) AND glentry.cny# = customer.cny# (+) AND glentry.vendordimkey = vendor.record# (+) AND glentry.cny# = vendor.cny# (+) AND glentry.employeedimkey = employee.record# (+) AND glentry.cny# = employee.cny# (+) AND employee.CONTACTKEY = employee_contact.record# (+) AND employee.cny#  = employee_contact.cny# (+) AND glentry.itemdimkey = item.record# (+) AND glentry.cny# = item.cny# (+) AND glentry.classdimkey = class.record# (+) AND glentry.cny# = class.cny# (+) ",
    'ARGTYPES' => array('integer', 'integer', 'integer'),
);
$kglentryQueries['QRY_GLENTRY_DELETE_BY_REVRECSCHEDULE'] = array(
        'QUERY' => "DELETE FROM glentry WHERE exists 
					(SELECT record# FROM glbatchmst WHERE rrsentrykey = ? and cny# = ? and record# = glentry.batch#) and cny# = ?",
        'ARGTYPES' => array('integer' ,'integer' ),
    );