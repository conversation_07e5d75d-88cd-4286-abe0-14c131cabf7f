<?php
/**
 * Editor class for GL graphs
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

require_once 'std_reports.inc';
require_once 'FinancialReportWizard.inc';

define("GRPSUMMARY", "T");
define("GRPQUARTERS", "Q");
define("GRPMONTHS", "P");
define("GRPWEEKS", "W");
define("GRPDAYS", "Y");

/**
 * Editor class for GL graphs
 */
class GraphsEditor extends FormEditor
{
    const NEW_GRAPH_TOKEN = 'IA.NEW_GRAPH';
    /* @var bool $isMultiBookCompany*/
    var $isMultiBookCompany = false;
    /* @var array $kPeriodMap*/
    var $kPeriodMap = array();
    /* @var array $kPeriodMapLabels*/
    var $kPeriodMapLabels = array();
    /* @var array $dimInfo*/
    var $dimInfo = array();
    /* @var string $pageTitle*/
    var $pageTitle = self::NEW_GRAPH_TOKEN;
    /* @var bool $globTable*/
    var $globTable = false;

    /**
     * @var array $additionalTokens
     */
    protected $additionalTokens = [
        "IA.ALL_PLATFORM_OBJECTS",
        "IA.ALL_DEPARTMENT",
        "IA.ALL_LOCATION",
        "IA.ALL_PROJECT",
        "IA.ALL_CUSTOMER",
        "IA.ALL_VENDOR",
        "IA.ALL_EMPLOYEE",
        "IA.ALL_ITEM",
        "IA.ALL_CLASS",
        "IA.ALL_CONTRACT",
        "IA.ALL_TASK",
        "IA.ALL_WAREHOUSE",
        "IA.ALL_COSTTYPE",
        "IA.ALL_FIXEDASSET",
        "IA.VIEW_GRAPH",
        "IA.PRINTABLE_PDF",
        "IA.SPECIFIC_DEPARTMENT_SLASH_GROUP",
        "IA.SPECIFIC_LOCATION_SLASH_GROUP",
        "IA.SPECIFIC_PROJECT_SLASH_GROUP",
        "IA.SPECIFIC_CUSTOMER_SLASH_GROUP",
        "IA.SPECIFIC_VENDOR_SLASH_GROUP",
        "IA.SPECIFIC_EMPLOYEE_SLASH_GROUP",
        "IA.SPECIFIC_ITEM_SLASH_GROUP",
        "IA.SPECIFIC_CLASS_SLASH_GROUP",
        "IA.SPECIFIC_CONTRACT_SLASH_GROUP",
        "IA.SPECIFIC_TASK_SLASH_GROUP",
        "IA.SPECIFIC_WAREHOUSE_SLASH_GROUP",
        "IA.SPECIFIC_COSTTYPE_SLASH_GROUP",
        "IA.SPECIFIC_FIXEDASSET_SLASH_GROUP",
        "IA.SPECIFIC_PLATFORM_OBJECT_SLASH_GROUP",
        "IA.SELECT_IAACCTGRPPICK",
        'IA.ACCOUNT_GROUPS_OR_ACCOUNTS',
        'IA.REPORTING_PERIOD',
        'IA.DIMENSION_STRUCTURE',
        self::NEW_GRAPH_TOKEN
    ];

    /**
     * @var array $bookIdLabels
     */
    protected $bookIdLabels = [];

    /**
     * @param array $_params Input params
     */
    public function __construct($_params = array())
    {
        $this->textTokens[] = 'IA.THE_GRAPH_PREVIEW_WILL_APPEAR_HERE';
        $this->textTokens[] = 'IA.PROCESSING_PLEASE_WAIT';
        $this->textTokens[] = 'IA.MORE_EDIT_THIS_PAGE';
        $this->textTokens[] = 'IA.MORE_OBJECT_DEFINITION';
        $this->textTokens[] = 'IA.MORE_EDIT_GRID_LAYOUT';
        $this->textTokens[] = 'IA.MORE_TABLE_SIZE_ROWS';
        $this->textTokens[] = 'IA.MORE_TABLE_SIZE_MAX_ROWS';
        $this->textTokens[] = 'IA.MORE_TABLE_SIZE';
        $this->textTokens[] = 'IA.AVAILABLE';
        $this->textTokens[] = 'IA.SELECTED';
        $this->textTokens[] = 'IA.AVAILABLE_BOOKS';
        $this->textTokens[] = 'IA.SELECTED_BOOKS';
        $this->textTokens[] = 'IA.NONE';

        $textTokens = [
            "IA.CURRENT_MONTH", "IA.CURRENT_MONTH_TO_DATE", "IA.CURRENT_QUARTER", "IA.CURRENT_QUARTER_TO_DATE",
            "IA.CURRENT_YEAR", "IA.CURRENT_YEAR_TO_DATE", "IA.THIS_WEEK", "IA.TODAY", "IA.FISCAL_CURRENT_MONTH",
            "IA.FISCAL_CURRENT_QUARTER", "IA.FISCAL_CURRENT_QUARTER_TO_DATE", "IA.FISCAL_CURRENT_YEAR",
            "IA.FISCAL_CURRENT_YEAR_TO_DATE", "IA.TWELVE_MONTHS_TO_CURRENT_MONTH",
            "IA.TWELVE_MONTHS_TO_CURRENT_DATE",
            'IA.PIE',
            'IA.DOUGHNUT_HTML_ONLY',
            'IA.LINE',
            'IA.COLUMN',
            'IA.STACKED_COLUMN_HTML_ONLY',
            'IA.PARALLEL_COLUMN_HTML_ONLY',
            'IA.BAR',
            'IA.STACKED_BAR_HTML_ONLY',
            'IA.AREA_HTML_ONLY',
            'IA.STACKED_AREA_HTML_ONLY',
            'IA.WATERFALL_HTML_ONLY',
            'IA.MAIN_REPORTING_BOOK',
            'IA.TO_ADD_AN_AVAILABLE_BOOK_TO_THE_SELECTED_BOOKS',
            'IA.TO_COMBINE_AMOUNTS_FROM_MULTIPLE_BOOKS',
            'IA.USE_ENTRIES_FROM',
            'IA.SELECTED_BOOKS_ONLY',
            'IA.MAIN_REPORTING_BOOK_AND_SELECTED_BOOKS',
            'IA.SET',
            'IA.CANCEL',
            'IA.OTHER_AVAILABLE_BOOKS',
            'IA.SELECTED_BOOKS',
            'IA.YOU_MUST_CHOOSE_AT_LEAST_ONE_REPORTING_BOOK',
            'IA.SELECT_REPORTING_BOOKS',
            'IA.LOADING_PLEASE_WAIT',
            'IA.ACCOUNT_GROUPS_OR_ACCOUNTS',
            'IA.REPORTING_PERIOD',
            'IA.DIMENSION_STRUCTURE',
            'IA.REQUIRED'
        ];

        $this->textTokens = array_merge($textTokens, $this->textTokens);
        $showBooks = checkMultiBook($companyBooks, $adjbooks, true, true);
        $this->bookIdLabels = array_merge($companyBooks, $adjbooks);
        $this->isMultiBookCompany = $showBooks;
        $this->setGloablTable();
        parent::__construct($_params);
    }

    /**
     * Flag to indicate whether to use IA graphs or not
     */
    public function setGloablTable()
    {
        $this->globTable = false;
    }

    /**
     * Build dynamic meta data
     *
     * @param array $params Input params
     */
    protected function buildDynamicMetadata(&$params)
    {
        parent::buildDynamicMetadata($params);

        $this->addDynamicDimensionFields($params);
    }

    /**
     * @param array $buttons
     * @param string $id
     * @param string $name
     * @param string $button
     * @param string $action
     * @param bool $submitData
     * @param string $jsCode
     * @param bool $serverAction
     * @param bool $disableValidation
     * @param string $args
     */
    protected function setButtonDetails(&$buttons, $id, $name, $button, $action, $submitData = true, $jsCode = '',
                                        $serverAction = true, $disableValidation = false, $args = '')
    {
        /**
         * Attaching custom JsCode for Save and saveAndNew buttons.
         */
        if (in_array($id, array(Editor_SaveBtnID, Editor_SaveAndNewBtnID))) {
            $jsCode = "stdSaveButton();";
            parent::setButtonDetails(
                $buttons, $id, $name, $button, $action, $submitData, $jsCode,
                $serverAction, $disableValidation, $args
            );
        } else {
            parent::setButtonDetails(
                $buttons, $id, $name, $button, $action, $submitData, $jsCode,
                $serverAction, $disableValidation, $args
            );
        }
    }

    /**
     * Transform view object to business
     *
     * @param array $obj
     *
     * @return bool True if success
     *
     */
    protected function transformViewObjectToBiz(&$obj)
    {
        if (!util_isPlatformDisabled()) {
            $dimInfo = $this->getEntityMgr()->GetDimensionFields();
            foreach ($dimInfo as $dimObj) {
                if (!empty($dimObj['platform'])) {
                    $key = $dimObj['path'] . "_disp";
                    if (isset($obj[$key])) {
                        if ( isset($obj['FILTER' . $dimObj['path']])
                             && $obj['FILTER' . $dimObj['path']] === 'nofilter' ) {
                            $obj[$dimObj['path']] = $obj[$key] = '';
                        }
                        $obj['_CUSTDIMVALUES'][$dimObj['path']] = $obj[$dimObj['path']];
                        $obj[$dimObj['path']] = $obj[$key];
                    }
                }
            }
        }
        $this->getEntityMgr()->transformViewObjectToBiz($obj);
        return parent::transformViewObjectToBiz($obj);
    }

    /**
     * Transform business object to view
     *
     * @param array $obj Inout values
     *
     * @return bool True if success
     */
    protected function transformBizObjectToView(&$obj)
    {
        // For backward compatibility
        // clear secondary series value if report type is pie or doughnut
        if (in_array($obj['RE_TYPE'], array('GRAPH.pie.1', 'Doughnut'))) {
            $obj['SECONDARYSERIES'] = 'N';
        }

        // Formeditor expects it in storage
        $obj['ASOFDATE'] = FormatDateForStorage($obj['ASOFDATE']);

        if (!$this->globTable) {
            if ($this->isMultiBookCompany) {
                $obj['REPORTING_BOOK'] = $obj['REPORTINGBOOK'];
                $obj['REPORTING_BOOK2'] = $this->bookIdLabels[$obj['REPORTINGBOOK']] ?? $obj['REPORTINGBOOK'];
                $obj['REPORTING_BOOK_MULTIPLE'] = json_encode(
                    explode('#~#', $obj['ADJUSTMENTBOOK'])
                );
                $obj['REPORTING_BOOK_ONLYFILTERADDBOOKS']
                    = ($obj['INCLUDEREPBOOK'] === 'F') ? 'filter' : 'nofilter';
                $obj['REPORTING_BOOK_LISTTYPE'] = (isset($obj['ADJUSTMENTBOOK'])
                    && $obj['ADJUSTMENTBOOK'] !== '') ? 'multiple' : 'single';
            }
        }

        // While editing graph, set WF_SECONSERIES if it is not set
        if (!empty($obj['RECORDNO']) && $obj['RE_TYPE'] === 'Waterfall'
            && empty($obj['WF_SECONSERIES'])
        ) {
            $obj['WF_SECONSERIES'] = 'AGORA';
        }

        $state = $this->getState();

        if ($state !== Editor_ShowNewState) {
            if ($obj['RE_TYPE'] === 'Waterfall') {
                $obj['SECONDARYSERIES'] = 'N';
            } else {
                if ((!empty($obj['EXPANDBY'])
                        && substr($obj['EXPANDBY'], 0, 3) === 'PRD')
                    || !empty($obj['TRENDING'])
                    || ($obj['PERIODS'] === "34"
                        && !empty($obj['PERIODOFFSETBY'])) // today
                ) {
                    $periodCompCheckbox = $obj['PRIMARYSERIES'] === 'P'
                        ? 'psPeriodExpnasion' : 'ssPeriodExpnasion';
                    $obj[$periodCompCheckbox] = 'true';
                }
            }
        }

        return parent::transformBizObjectToView($obj);
    }

    /**
     * Mediate additional data and meta data
     *
     * @param array $obj
     *
     * @return bool True if success
     *
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        $ok = parent::mediateDataAndMetadata($obj);

        $this->kPeriodMap = ($this->globTable) ? GetIAPeriodMap()
            : GetPeriodMap();
        $this->kPeriodMapLabels = ($this->globTable) ? GetIAPeriodMap(true)
            : GetPeriodMap(true, '', true);
        // used to populate series
        $dimInfo = $this->getEntityMgr()->GetDimensionFields();

        $view = $this->getView();
        $state = $this->getState();

        //SETTING DEFAULT OTHER BOOKS
        $this->setOtherDefaultBooks();

        if ($state !== Editor_ShowNewState) { // set page title
            $this->pageTitle = $obj['NAME'];
        }

        if ($state === Editor_ShowNewState) {

            // Default reporting book value 
            if (empty($obj['REPORTING_BOOK'])) {
                $obj['REPORTING_BOOK'] = GetDefaultBook();
            }
        }

        $field = array();
        if ($view->findComponents(
            array('path' => 'PERIODS'), EditorComponentFactory::TYPE_FIELD,
            $field
        )
        ) {

            $field[0]->setProperty(
                array('type', 'validlabels'), array_values($this->kPeriodMapLabels)
            );
            $field[0]->setProperty(
                array('type', 'validvalues'), array_keys($this->kPeriodMapLabels)
            );
        }

        $field = array();
        if ($view->findComponents(
            array('path' => 'TRENDING'), EditorComponentFactory::TYPE_FIELD,
            $field
        )
        ) {

            $trendLab = array();
            $trendVal = array();
            for ($i = -25; $i <= 25; $i++) {
                if ( $i == 0 ) {
                    $trendLab[] = 'IA.NONE';
                    $trendVal[] = '0';
                } else {
                    if ( $i == -1 || $i == 1 ) {
                        $trendLab[] = I18N::getSingleToken('IA.NUMBER_PERIODS',
                                                           I18N::mapToPlaceholderArray(
                                                               ['NUMBER' => $i]
                                                           ));
                        $trendVal[] = $i;
                    } else {
                        $trendLab[] = I18N::getSingleToken('IA.NUMBER_PERIODS',
                                                           I18N::mapToPlaceholderArray(
                                                               ['NUMBER' => $i]
                                                           ));
                        $trendVal[] = $i;
                    }
                }

            }
            $field[0]->setProperty(
                [ 'type', 'validlabels' ], $trendLab
            );
            $field[0]->setProperty(
                [ 'type', 'validvalues' ], $trendVal
            );
        }

        $field = array();
        if ($view->findComponents(
            array('path' => 'GROUPS'), EditorComponentFactory::TYPE_FIELD,
            $field
        )
        ) {
            $industry = $obj['INDUSTRY'];
            // GET ALL ACCT GROUPS FOR THE UI MAP
            InitReportBase($this->globTable, $industry);
            //HTML encoding is relaxed to display special char as it is in Account groups list,
            $groupMap = $this->GLGraph_GetUIAcctGrpMap(true, false);

            // Hide system account group, show only if they are already selected in the graph
            if (isset($obj['GROUPS']) && $obj['GROUPS'] != '') {
                $kDelim = '#~#';
                $groups  = explode($kDelim, $obj['GROUPS']);
            }

            $accountGroupMap = array();
            if ($industry) {
                $accountGroupMap = $groupMap;
            } else {
                $accountGroupMap[''] = '-- '.I18N::getSingleToken('IA.SELECT_IAACCTGRPPICK').' --';
                foreach($groupMap as $key => $value) {
                    // PHP8_NUMERIC_STRING_COMPARE; Priority: low; Behavior: same, Risk: low, Solution: php7eq0
                    if (Util::php7gt0($key)) {
                        $accountGroupMap[$key] = $value;
                    } else if (isset($groups) && in_array($key, $groups)) {
                        $accountGroupMap[$key] = $value;
                    }
                }
            }


            $field[0]->setProperty(
                array('type', 'validlabels'), array_values($accountGroupMap)
            );
            $field[0]->setProperty(
                array('type', 'validvalues'), array_keys($accountGroupMap)
            );
        }

        $field = array();
        if ($view->findComponents(
            array('path' => 'PRIMARYSERIES'),
            EditorComponentFactory::TYPE_FIELD, $field
        )
        ) {
            $fldtype = $field[0]->getProperty('type');
            $validValues = $fldtype['validvalues'];
            $validLabels = $fldtype['validlabels'];

            foreach ( $dimInfo as $val) {
                if (empty($val['graphDBVal'])) {
                    continue;
                }
                $validLabels[] = I18N::getSingleToken($val['fullname']);
                $validValues[] = $val['graphDBVal'];
            }

            $field[0]->setProperty(array('type', 'validlabels'), $validLabels);
            $field[0]->setProperty(array('type', 'validvalues'), $validValues);
        }

        $field = array();
        if ($view->findComponents(
            array('path' => 'SECONDARYSERIES'),
            EditorComponentFactory::TYPE_FIELD, $field
        )
        ) {
            $fldtype = $field[0]->getProperty('type');
            $validValues = $fldtype['validvalues'];
            $validLabels = $fldtype['validlabels'];

            foreach ( $dimInfo as $val) {
                if (empty($val['graphDBVal'])) {
                    continue;
                }
                $validLabels[] = I18N::getSingleToken($val['fullname']);
                $validValues[] = $val['graphDBVal'];
            }

            $field[0]->setProperty(array('type', 'validlabels'), $validLabels);
            $field[0]->setProperty(array('type', 'validvalues'), $validValues);
        }

        $field = array();
        if ($view->findComponents(
            array('path' => 'BUDGETS'), EditorComponentFactory::TYPE_FIELD,
            $field
        )
        ) {
            if (IsMCMESubscribed()) {
                $repBook = $obj["REPORTINGBOOK"];
            }

            /** @noinspection PhpUndefinedVariableInspection */
            $budgetMap = $this->GetBudgetMap($repBook);

            $field[0]->setProperty(
                array('type', 'validlabels'), array_values($budgetMap)
            );
            $field[0]->setProperty(
                array('type', 'validvalues'), array_keys($budgetMap)
            );
        }

        $field = array();
        /** @noinspection PhpUnusedLocalVariableInspection */
        $kCompareBy = array();
        if ($view->findComponents(
            array('path' => 'EXPANDBY'), EditorComponentFactory::TYPE_FIELD,
            $field
        )
        ) {
            //Adding rolling period values into compare by option
            $kCompareBy = array(
                GRPSUMMARY => 'IA.NONE',
            );

            if (count(explode('#~#', $obj['PERIODS'])) === 1) {
                switch ($this->kPeriodMap[$obj['PERIODS']]) {
                case "Current Year":
                case "Fiscal - Current Year":
                case "Fiscal - Current Year To Date":
                case "Current Year To Date":
                    $kCompareBy[GRPQUARTERS] = 'IA.EXPAND_QUARTERS';
                    $kCompareBy[GRPMONTHS] = 'IA.EXPAND_MONTHS';
                    break;
                case "12 Months to Current Month":
                case "Current Quarter":
                case "Fiscal - Current Quarter To Date":
                case "Current Quarter To Date":
                case "Fiscal - Current Quarter":
                    $kCompareBy[GRPMONTHS] = 'IA.EXPAND_MONTHS';
                    break;
                case "Current Month To Date":
                case "Current Month":
                    $kCompareBy[GRPWEEKS] = 'IA.EXPAND_WEEKS';
                    break;
                case "This Week":
                    $kCompareBy[GRPDAYS] = 'IA.EXPAND_DAYS';
                    break;
                }
            }

            $field[0]->setProperty(
                array('type', 'validlabels'), array_values($kCompareBy)
            );
            $field[0]->setProperty(
                array('type', 'validvalues'), array_keys($kCompareBy)
            );

            $field = array();
            if ($view->findComponents(
                array('path' => 'WF_EXPANDBY'),
                EditorComponentFactory::TYPE_FIELD, $field
            )
            ) {
                array_shift($kCompareBy); //remove 'None' option

                $field[0]->setProperty(
                    array('type', 'validlabels'), array_values($kCompareBy)
                );
                $field[0]->setProperty(
                    array('type', 'validvalues'), array_keys($kCompareBy)
                );

//                if ( in_array($obj['WF_EXPANDBY'], array_keys($kCompareBy)) ) {
//                    $obj['WF_EXPANDBY'] = $obj['WF_SECONSERIES'];
//                    $obj['WF_SECONSERIES'] = 'P';
//                }
            }
        }
//        
//        $field = array();
//        if ( $view->findComponents(array('path' => 'PERIODOFFSETBY'), EditorComponentFactory::TYPE_FIELD, $field) ) {
//            if($this->kPeriodMap[$obj['PERIODS']] !== 'Today' ){
//                $field[0]->setProperty('disabled', true);
//            }
//        }

        if (!$this->globTable) {
            $showBooks = checkMultiBook($companyBooks, $adjbooks);
            $fields = array();
            $view->findComponents(
                array('path' => 'REPORTING_BOOK'),
                EditorComponentFactory::TYPE_FIELD, $fields
            );
            $type = $fields[0]->getProperty('type');
            $companyBooksLabels = [];


            asort($companyBooks);
            foreach ($companyBooks as $key => $value) {
                $companyBooksLabels[$key] = $value;
                if (strpos($value, 'ACCRUAL') !== false
                    || strpos(
                        $value, 'CASH'
                    ) != false
                ) {
                    $companyBooksLabels[$key] = $value;
                    $companyBooksLabels[$key] = str_replace(
                        'Gaap', 'GAAP', $companyBooksLabels[$key]
                    );
                }
            }

            //company books
            asort($adjbooks);
            $adjbooksLabels = [];
            foreach ($adjbooks as $key => $value) {
                $adjbooksLabels[$key] = $value;
                if (strpos($value, 'ACCRUAL') !== false
                    || strpos(
                        $value, 'CASH'
                    ) != false
                ) {
                    $adjbooksLabels[$key] = $value;
                    $adjbooksLabels[$key] = str_replace(
                        'Gaap', 'GAAP', $adjbooksLabels[$key]
                    );
                }
            }


            $companybooks2 = $companyBooks;
            translateReportingBooksDBValue($companyBooksLabels);
            $companybooks2Labels = $companyBooksLabels;

            $type['validlabels'] = array_values($companyBooksLabels);
            $type['validvalues'] = array_values($companyBooks);
            $fields[0]->setProperty('type', $type);
            $view->findAndSetProperty(
                array('path' => 'REPORTING_BOOK'),
                array('hidden' => 'true', 'required' => 'false')
            );
            $view->findAndSetProperty(
                array('path' => 'REPORTING_BOOK'),
                array('default' => GetDefaultBook())
            );

            $fields = array();
            $view->findComponents(
                array('path' => 'REPORTING_BOOK2'),
                EditorComponentFactory::TYPE_FIELD, $fields
            );
            $type['validlabels'] = array_values($companybooks2Labels);
            $type['validvalues'] = array_values($companybooks2);
            $fields[0]->setProperty('type', $type);
            $view->findAndSetProperty(
                array('path' => 'REPORTING_BOOK2'),
                array('hidden' => 'true', 'required' => 'false')
            );
            $view->findAndSetProperty(
                array('path' => 'REPORTING_BOOK2'),
                array('default' => GetDefaultBook())
            );

            if (!$showBooks) {
                $view->findAndSetProperty(
                    array('path' => 'REPORTING_BOOK'),
                    array('hidden' => 'true', 'required' => 'false')
                );
                $view->findAndSetProperty(
                    array('path' => 'REPORTING_BOOK2'),
                    array('hidden' => 'true', 'required' => 'false')
                );
                $view->findAndSetProperty(
                    array('path' => 'REPORTING_BOOK_LIST'),
                    array('hidden' => true)
                );
                $view->findAndSetProperty(
                    array('path' => 'REPORTINGBOOKHELP'),
                    array('hidden' => true)
                );
                $view->findAndSetProperty(
                    array('path' => 'rowRptBookCtrls'), array('hidden' => true)
                );
                $view->findAndSetProperty(
                    array('path' => 'rowRptBookHlp'), array('hidden' => true)
                );
            } else {
                //adjustment books
                $fields = array();

                $view->findComponents(
                    array('path' => 'REPORTING_BOOK_ADJUSTMENTBOOKS'),
                    EditorComponentFactory::TYPE_FIELD, $fields
                );
                $type = $fields[0]->getProperty('type');
                translateReportingBooksDBValue($adjbooksLabels);
                /** @noinspection PhpUndefinedVariableInspection */
                $type['validlabels'] = array_map('strval', array_values($adjbooksLabels));
                $type['validvalues'] = array_map('strval', array_keys($adjbooks));
                $fields[0]->setProperty('type', $type);

                $view->findAndSetProperty(
                    array('path' => 'REPORTING_BOOK_ADJUSTMENTBOOKS'),
                    array('hidden' => 'true', 'required' => 'false')
                );

                if (in_array(
                    $state, array(Editor_ShowEditState, Editor_ShowNewState)
                )) {
                    $view->findAndSetProperty(
                        array('path' => 'REPORTINGBOOKHELP'),
                        array('fullname' => '')
                    );
                } else {
                    if ($state === Editor_ShowViewState) {
                        $view->findAndSetProperty(
                            array('path' => 'REPORTING_BOOK'),
                            array('hidden' => 'true', 'required' => 'false')
                        );
                        $view->findAndSetProperty(
                            array('path' => 'REPORTING_BOOK2'),
                            array('hidden' => 'true', 'required' => 'false')
                        );
                        $view->findAndSetProperty(
                            array('path' => 'REPORTING_BOOK_LIST'),
                            array('hidden' => true)
                        );
                        $view->findAndSetProperty(
                            array('path' => 'REPORTING_BOOK_LISTTYPE'),
                            array('hidden' => true)
                        );
                        $view->findAndSetProperty(
                            array('path' => 'rowRptBookCtrls'),
                            array('hidden' => true)
                        );
                    }
                }

                if ($state !== Editor_ShowNewState) {
                    $obj['REPORTINGBOOKHELP'] = '';
                    if ($obj['REPORTING_BOOK_ONLYFILTERADDBOOKS']
                        === 'nofilter'
                    ) {
                        $obj['REPORTINGBOOKHELP'] .= $companybooks2Labels[$obj['REPORTINGBOOK']]
                            . ", ";
                    }

                    if (!empty($obj['ADJUSTMENTBOOK'])) {
                        foreach (
                            explode('#~#', $obj['ADJUSTMENTBOOK']) as $k => $v
                        ) {
                            if ($k > 0) {
                                $obj['REPORTINGBOOKHELP'] .= ', ';
                            }
                            $obj['REPORTINGBOOKHELP'] .= $adjbooksLabels[$v];
                        }
                    }

                    if (empty($obj['REPORTINGBOOKHELP'])) {
                        $obj['REPORTINGBOOKHELP']
                            = $companybooks2Labels[$obj['REPORTING_BOOK']];
                    }
                }
            }
        }


        //Waterfall chart specific controls
        $field = array();
        if ($view->findComponents(
            array('path' => 'WF_SECONSERIES'),
            EditorComponentFactory::TYPE_FIELD, $field
        )
        ) {
            $fldtype = $field[0]->getProperty('type');
            $validValues = $fldtype['validvalues'];
            $validLabels = $fldtype['validlabels'];

            $validLabels[] = I18N::getSingleToken('IA.ACCOUNT_GROUPS_OR_ACCOUNTS');
            $validValues[] = "AGORA"; // account group or account expansion


            $validLabels[] = I18N::getSingleToken('IA.REPORTING_PERIOD');
            $validValues[] = "P";

            if (!$this->globTable) {
                $validLabels[] = I18N::getSingleToken('IA.DIMENSION_STRUCTURE');
                $validValues[] = "ACCT";
            }

            foreach ( $dimInfo as $val) {
                if (empty($val['graphDBVal'])) {
                    continue;
                }
                $validLabels[] = I18N::getSingleToken($val['fullname']);
                $validValues[] = $val['graphDBVal'];
            }

            $field[0]->setProperty(array('type', 'validlabels'), $validLabels);
            $field[0]->setProperty(array('type', 'validvalues'), $validValues);
        }

        $dimensionMap = array();
        $field = array();
        if ($view->findComponents(
            array('path' => 'WF_DIMENSION'), EditorComponentFactory::TYPE_FIELD,
            $field
        )
        ) {
            $fldtype = $field[0]->getProperty('type');
            $validValues = $fldtype['validvalues'];
            $validLabels = $fldtype['validlabels'];

            foreach ( $dimInfo as $val) {
                if (empty($val['graphDBVal'])) {
                    continue;
                }
                $validLabels[] = I18N::getSingleToken($val['fullname']);
                $validValues[] = $val['graphDBVal'];
                $dimensionMap[I18N::getSingleToken($val['fullname'])]
                    = $val['graphDBVal'];
            }

            $field[0]->setProperty(array('type', 'validlabels'), $validLabels);
            $field[0]->setProperty(array('type', 'validvalues'), $validValues);
        }

        $field = array();
        if ($view->findComponents(
            array('path' => 'WF_DIMSTRUC'), EditorComponentFactory::TYPE_FIELD,
            $field
        )
        ) {
            $validLabels = array();

            $mapDimensionToDimensionClassID = getDimensionToDimensionClassIDMap(
            );
            foreach ( $mapDimensionToDimensionClassID as $k => $v) {
                $dimStruc = GetUIAcctGrpMap(false, '', $k, true);
                if (!empty($dimStruc)) {
                    $validLabels = array_merge(
                        $validLabels, array_values($dimStruc)
                    );
                }
            }

            $field[0]->setProperty(array('type', 'validlabels'), $validLabels);
            $field[0]->setProperty(array('type', 'validvalues'), $validLabels);
        }

        // Look up and set dimension structure category
        if ($obj['RE_TYPE'] === 'Waterfall') {
            $acctGrpUIAllDimMap = GetUIAcctGrpMap(
                false, 'cat', 'dim-all', true
            );
            $obj['WF_DIMENSION']
                = $dimensionMap[$acctGrpUIAllDimMap[$obj['WF_DIMSTRUC']]];
        }


        // set dimension filters UI
        $field = array();
        if ($view->findComponents(
            array('path' => 'FILTERDEPARTMENT'),
            EditorComponentFactory::TYPE_FIELD, $field
        )
        ) {
            $validValues = array('nofilter', 'specifichierarchy');
            $validLables = array('IA.ALL_DEPARTMENT', 'IA.SPECIFIC_DEPARTMENT_SLASH_GROUP');

            $field[0]->setProperty(array('type', 'validlabels'), $validLables);
            $field[0]->setProperty(array('type', 'validvalues'), $validValues);
        }
        $field = array();
        if ($view->findComponents(
            array('path' => 'FILTERLOCATION'),
            EditorComponentFactory::TYPE_FIELD, $field
        )
        ) {
            $name = isl_strtolower(I18N::getSingleToken('IA.LOCATION'));
            $pname = isl_strtolower(I18N::getSingleToken('IA.LOCATIONS'));

            $validValues = array('nofilter', 'specifichierarchy');
            $validLables = array('IA.ALL_LOCATION', 'IA.SPECIFIC_LOCATION_SLASH_GROUP');

            $field[0]->setProperty(array('type', 'validlabels'), $validLables);
            $field[0]->setProperty(array('type', 'validvalues'), $validValues);
        }

        $isPlatformEnabled = !util_isPlatformDisabled();
        foreach ($this->dimInfo as $key => $dimObj) {

            $path = $dimObj['path'];

            if ($key === 'location') {
                $path = 'LOCATION';
            }

            if ($key === 'department') {
                $path = 'DEPARTMENT';
            }

            if (!empty($obj[$path])) {
                $obj['FILTER' . $path] = 'specifichierarchy';
                // Skipping the custom dimension display key in View state
                if ($isPlatformEnabled && $this->state !== Editor_ShowViewState) {
                    $_custDimVal = $obj['_CUSTDIMVALUES'][$dimObj['path']];
                    if (isset($_custDimVal)) {
                        /* Form custom dimension display key */
                        $custDimDisplayKey = $dimObj['path'] . "_disp";
                        $obj[$custDimDisplayKey] = $obj[$dimObj['path']];
                        $obj[$dimObj['path']] = $_custDimVal;
                    }
                }
            } else {
                if (!empty($obj[$this->dimInfo[$dimObj['relatedFields'][0]]['path']])) {
                    $obj['FILTER' . $path] = 'type';
                } else {
                    $obj['FILTER' . $path] = 'nofilter';
                }
            }

        }

        return $ok;
    }

    /**
     * Initialize class varibale dimInfo
     */
    protected function setDimensionInfo()
    {
        $this->dimInfo = $this->getEntityMgr()->GetDimensionFields();
    }

    /**
     * Add dimension related fields
     *
     * @param array $params Input params
     */
    protected function addDynamicDimensionFields(&$params)
    {
        $this->setDimensionInfo();

        $matches = array();
        self::findElements(
            $params, array('id' => 'filterInfo'),
            EditorComponentFactory::TYPE_SECTION, $matches
        );
        $child = &$matches[0]['child'][0];
        $rows = &$child['row'];

        foreach ($this->dimInfo as $key => $dim) {

            // Skip location, costtpe(DE13319) and department here since t
            //hey are already added in the layout file...
            if ((!$dim['isDimension'] && !$dim['isGLDimension'])
                || in_array($key, array('department', 'location','costtype'))
            ) {
                continue;
            }

            $rows = array_merge($rows, $this->createDimFilterRow($key, $dim));
        }
    }

    /**
     * Create dimension filter row
     *
     * @param string $key    Dimension key
     * @param array $dimObj Vaules of dimension
     *
     * @return array List of dimension filter row fields
     */
    private function createDimFilterRow(&$key, &$dimObj)
    {
        $field = array();
        $row = array();
        $section = array();

        $row[0]['label'] = $dimObj['fullname'];

        $dimName = $dimObj['internalpath']?strtoupper($dimObj['internalpath']):strtoupper($key);
        $validValues = array('nofilter', 'specifichierarchy');

        if($dimObj['standard']){
            $validLables = array('IA.ALL_'.strtoupper($dimObj['entity']), 'IA.SPECIFIC_'.strtoupper($dimObj['entity']).'_SLASH_GROUP');
        } else {
            $validLables = array(I18N::getSingleToken('IA.ALL_PLATFORM_OBJECTS', I18N::mapToPlaceholderArray(
                ['OBJECT_NAME' => $dimObj['fullname']]
            )), I18N::getSingleToken('IA.SPECIFIC_PLATFORM_OBJECT_SLASH_GROUP', I18N::mapToPlaceholderArray(
                ['PLATFORM_OBJECT' => $dimObj['fullname']]
            )));
        }

        $params = ",'" . $dimObj['path'] . "'";
        if (!empty($dimObj['relatedFields'])) {
            $validValues[] = 'type';
            $validLables[] = $this->dimInfo[$dimObj['relatedFields'][0]]['fullname'];
            $params .= ",'"
                . $this->dimInfo[$dimObj['relatedFields'][0]]['path'] . "'";
        }

        $field[] = array(
            'path' => 'FILTER' . $dimObj['path'],
            'fullname' => '',
            'type' => array(
                'type' => 'enum',
                'ptype' => 'enum',
                'validvalues' => $validValues,
                'validlabels' => $validLables,
            ),
            'events' => array(
                'change' => 'showHidePickerFromForm(this ' . $params . ')'
            ),
            'className' => 'dimFilters'
        );

        $row[0]['child'][]['field'] = $field;

        $field = array();
        $field[] = array(
            'path' => $dimObj['path'],
            'fullname' => ''
        );

        if (!empty($dimObj['relatedFields'])) {
            $field[] = array(
                'path' => $this->dimInfo[$dimObj['relatedFields'][0]]['path'],
                'fullname' => ''
            );
        }
        $fields = array();

        /** @noinspection PhpUndefinedVariableInspection */
        $fields[] = array(
            'path' => 'PROMPT' . $dimName,
            'fullname' => 'IA.DISPLAY',
            'type' => array(
                'type' => 'char',
                'ptype' => 'boolean',
                'validvalues' => $gBooleanValues,
                '_validivalues' => $gBooleanIValues,
            ),
        );

        if ($dimObj['standard'] && $dimName !== 'ITEM' && $dimName !== 'AFFILIATEENTITY') {
            $fields[] = array('path' => 'EXCLSUBS_FOR_' . $dimName);
        }

        $section[0]['child'][]['field'] = $fields;
        $section[0]['className'] = 'noborder filterPrompt';
        $row[1]['className'] = 'comparisonInfoRow alignfilters';
        $row[1]['child'][]['field'] = $field;
        $row[1]['child'][]['section'] = $section;

        return $row;
    }

    /**
     * Get editor related globals
     *
     * @return array List of JS globals
     */
    protected function getEditorGlobals()
    {
        $arr = array();

        // ww: fix later.
        $pageTitle = I18N::getSingleToken($this->getTitle(), [], true);

        $pageTitleSuffix = $this->pageTitle;
        if ( $pageTitleSuffix == self::NEW_GRAPH_TOKEN ) {
            $pageTitleSuffix = I18N::getSingleToken(self::NEW_GRAPH_TOKEN);
        }
        $arr['pageTitle'] = $pageTitle . ' - ' . util_encode($pageTitleSuffix);
        $arr['gJSSess'] = Session::getKey();
        $arr['kPeriodMap'] = $this->kPeriodMapLabels;

        //Initializing Compare by options which are needed for rolling period
        $arr['aRollingCompareByLab'] = array(
            'IA.NONE', 'IA.QUARTERS', 'IA.MONTHS', 'IA.WEEKS', 'IA.DAYS'
        );
        $arr['aRollingCompareByVal'] = array(
            GRPSUMMARY, GRPQUARTERS, GRPMONTHS, GRPWEEKS, GRPDAYS
        );

        //Hard coding required rolling period values depends on reporting period
        $arr['yearRolOpts'] = array(0, 1, 2);
        $arr['tMonthRolOpts'] = array(0, 2);
        $arr['quarterRolOpts'] = array(0, 2);
        $arr['monthRolOpts'] = array(0, 3);
        $arr['weekRolOpts'] = array(0, 4);

        $dimInfo = $this->getEntityMgr()->GetDimensionFields();

        //Initializing basic options for series and plot series
        $aserieslab = array(
            'IA.ACCOUNT_GROUPS', 'IA.REPORTING_PERIODS', 'IA.BUDGET_COMPARISON'
        );
        $aseriesval = array('A', 'P', 'B');
        $dimLab = array();
        $dimVal = array();
        foreach ( $dimInfo as $val) {
            if ($val["graphDBVal"]) {
                $dimLab[] = $val['fullname'];
                $dimVal[] = $val["graphDBVal"];
            }
        }
        $aserieslab = array_merge($aserieslab, $dimLab);
        $aseriesval = array_merge($aseriesval, $dimVal);
        $arr['aSeriesLab'] = $aserieslab;
        $arr['aSeriesVal'] = $aseriesval;

        $arr['aDimLab'] = $dimLab;
        $arr['aDimVal'] = $dimVal;


        $IS_MULTIBOOK = checkMultiBook($companyBooks, $adjbooks) ? true : false;
        $IS_MULTIBOOK = ($IS_MULTIBOOK || count($adjbooks) > 0);
        $arr['IS_MULTIBOOK'] = $IS_MULTIBOOK;

        $arr['locationRequiredIfActualBook'] = IsMCMESubscribed()
            && !GetContextLocation();

        // populate dimension structures
        $showDimStruc = 'F';
        $dimGrpUIDimMaps = array();
        $mapDimensionToDimensionClassID = getDimensionToDimensionClassIDMap();
        foreach ($mapDimensionToDimensionClassID as $k => $v) {
            $dimStruc = GetUIAcctGrpMap(false, '', $k, true);
            $dimGrpUIDimMaps[$v['id_internal']]['val'] = array_keys($dimStruc);
            $dimGrpUIDimMaps[$v['id_internal']]['lab'] = array_values($dimStruc);
            
            // Show dimension structure option only if they have it
            if ($showDimStruc === 'F' && count($dimStruc) > 0) {
                $showDimStruc = 'T';
            }
        }

        $arr['DIMSTRUC'] = $dimGrpUIDimMaps;
        $arr['SHOWDIMSTRUC'] = $showDimStruc;
        $arr['appendnew'] = Request::$r->_appendnew;

        $arr['globTable'] = ($this->globTable) ? "T" : "F";
        return $arr;
    }

    /**
     * Get list of JS files
     *
     * @return array List of files
     */
    protected function getJavaScriptFileNames()
    {
        return array(
            '../resources/js/graph.js',
            '../resources/thirdparty/jquery-ui-multiselect/js/ui.multiselect.js',
            '../resources/js/dialog.js',
            '../resources/thirdparty/fusioncharts-suite-xt/js/fusioncharts.js'
        );
    }

    /**
     * Get list of CSS files
     *
     * @return array List of files
     */
    protected function getCssFileNamesPostTheme()
    {
        return array(
            '../resources/css/graphs.css',
            '../resources/css/chartTypes.css',
            '../resources/thirdparty/jquery-ui-multiselect/css/ui.multiselect.css'
        );
    }

    /**
     * Disable print option
     *
     * @return bool Return false
     */
    protected function CanPrint()
    {
        return false;
    }

    /**
     * PrepareInputValues
     *    override parent, mainly for unformatting display values
     *
     * @param array $_params input parameters
     * @param array $_obj    object data
     *
     * @return bool
     */
    protected function prepareInputValues(&$_params, &$_obj)
    {
        $ok = parent::PrepareInputValues($_params, $_obj);

        // Legacy codes expects date to be in user format
        $_obj['ASOFDATE'] = FormatDateForDisplay($_obj['ASOFDATE']);

        return $ok;
    }

    /**
     * Get account group map
     *
     * @param bool $includeSelect Flag
     * @param bool $htmlEncode    Flag
     *
     * @return array List of account groups
     */
    protected function GLGraph_GetUIAcctGrpMap(
        $includeSelect = true, $htmlEncode = true
    )
    {
        return IAAcctGrpPickManager::getAcctGrpsDropDownOptions($includeSelect, $htmlEncode);
    }

    /**
     * Get value map
     *
     * @param string $repBook List of Values
     *
     * @return array
     */
    protected function GetBudgetMap($repBook)
    {
        return GetBudgets(true, $repBook, false);
    }

    /**
     * This is passed to the Audit window to indicate which Entity Manager to load
     *
     * @return string
     */
    protected function getAuditEntity()
    {
        return 'graphs';
    }

    /**
     * The function will override the getPopupCloseJS()
     * @param string $key
     * @param array  $obj
     *
     * @return string]
     */
    protected function getPopupCloseJS($key, $obj = [])
    {
        $_appendnew = Request::$r->appendnew;
        $js = "";
        if ($_appendnew == 1) {
            // If the graph type is waterfall then append 'Waterfall' to the name else append 'Financials
            $reType = $obj['RE_TYPE'];
            $name = ($reType == 'Waterfall') ? 'Waterfall -- ' . $obj['NAME'] : 'Financials -- ' . $obj['NAME'];
            $js .= "window.opener.appendNameToList('" . $name . "', '" . $key . "'); window.close();";
        } else {
            $js .= parent::getPopupCloseJS($key);
        }

        return $js;

    }

    /**
     * @return string[]|null
     */
    protected function getEditorHiddenFields()
    {
        $extrafields['.appendnew'] = Request::$r->_appendnew;
        return $extrafields;
    }


    /**
     * @param string $state
     *
     * @return mixed
     */
    public function getStandardButtons($state)
    {
        $buttons = parent::getStandardButtons($state);
        $_appendnew = Request::$r->_appendnew;
        // Remove save and new button if the call is from dashboard
        foreach ( $buttons as $key => &$value ) {
            if ( $value['id'] === Editor_SaveAndNewBtnID ) {
                if ( $_appendnew ) {
                    array_splice($buttons, $key, 1);
                } else {
                    $value['jsCode'] = "stdSaveButton();";
                }
                break;
            }
        }

        return $buttons;

    }

    /**
     * @return GraphsManager
     */
    public function getEntityMgr()
    {
        assert($this->entityMgr instanceof GraphsManager);
        return $this->entityMgr;
    }

    /**
     * Setting for other default books
     *
     * @access private
     */
    private function setOtherDefaultBooks()
    {
        $view = $this->getView();
        $GlSetupManagerObject = Globals::$g->gManagerFactory->getManager("glsetup");
        $getAllOtherBooks = $GlSetupManagerObject->getDefaultBooksSetting();

        $multiPickerProperty = [];
        $view->findComponents(['path' => 'DEFAULTOTHERBOOKS'], EditorComponentFactory::TYPE_FIELD, $multiPickerProperty);
        if($multiPickerProperty) {
            $setValues = $multiPickerProperty[0]->getProperty('type');
            $setValues['validvalues'] = $getAllOtherBooks;
            $multiPickerProperty[0]->setProperty('type', $setValues);
        }
    }

}
