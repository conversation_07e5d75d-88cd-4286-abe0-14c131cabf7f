<?php

/**
 *    FrwVersionHistoryEditor.cls
 *
 * <AUTHOR> <jana<PERSON>anan.r<PERSON><PERSON><PERSON>@sage.com>
 * @copyright 2023, Intacct Corporation, All Rights Reserved
 */

class FrwVersionHistoryEditor extends FormEditor
{

    /**
     * @param array $params
     */
    public function __construct($params)
    {
        $this->additionalTokens = [
            'IA.YOU_ARE_NOT_AUTHORIZED_TO_ACCESS_THIS_OBJECT',
            'IA.FRW_VERSION_HISTORY_REPORT',
            'IA.CURRENT_VERSION',
            'IA.VIEW',
            'IA.RESTORED_FROM_VERSION',
        ];
        parent::__construct($params);
    }

    /**
     * getEntityData
     *    helper function to get data from entity manager and format the data for display
     *
     * @param string        $entity entity or object
     * @param string        $objId  entityid or objectid
     * @param string        $doctype
     * @param string[]|null $fields
     *
     * @return array the formatted result set
     * @throws NotAuthorizedException
     */
    function getEntityData($entity, $objId, $doctype = '', $fields = null)
    {
        $reportKey = Request::$r->_reportKey;

        $finrptpermissions = Globals::$g->gManagerFactory->getManager('finrptpermissions');
        if ( ! $finrptpermissions->isCurrentUserAllowedFor($reportKey) ) {
            throw new NotAuthorizedException(GT($this->textMap, 'IA.YOU_ARE_NOT_AUTHORIZED_TO_ACCESS_THIS_OBJECT'));
        }
        $params = [
            'selects' => [ 'VERSION', 'WHENCREATED', 'USERLOGINID', 'REPORTINFOKEY', 'RESTOREVERSION' ],
            'filters' => [ [ [ 'REPORTINFOKEY', '=', $reportKey ] ] ],
            'orders'  => [ [ 'VERSION', 'desc' ] ],
        ];

        $manager = Globals::$g->gManagerFactory->getManager($entity);
        $obj['VERSIONHISTORY'] = $manager->GetList($params) ? : [];

        return $obj;
    }

    /**
     * This is a hook functions for subclases to adjust the metadata according to the current data & state.
     * At the time this function is called:
     *   - the data is available and it is in view format.
     *   - the metadata is expanded and the view objects are built - use $this->getView() call to get a refernece to
     *   the view object.
     *
     * WARNING: Because the metadata is expanded at the time of this call, the subclass has to be careful when making
     * changes to the metadata. For example, when adding/removing fields that belong to a grid, the code needs to
     * operate on the grid object.
     *
     * @param  array $obj  the data
     *
     * @return bool  true on success and false on failure - make sure an error is raised in case of failure
     */
    function mediateDataAndMetadata(&$obj)
    {
        if ( ! empty($obj['VERSIONHISTORY']) ) {
            foreach ( $obj['VERSIONHISTORY'] as $key => &$value ) {
                if ( $key != 0 ) {
                    $value['VIEWLINK'] = $this->genViewTag($value);
                } else {
                    // first one is the current
                    $value['VERSION'] = I18N::getSingleToken('IA.CURRENT_VERSION', I18N::mapToPlaceholderArray(
                        [
                            'VERSION' => $value['VERSION']
                        ]
                    ));
                }
                if ( ! empty($value['RESTOREVERSION']) ) {
                    $value['VERSION'] = I18N::getSingleToken('IA.RESTORED_FROM_VERSION', I18N::mapToPlaceholderArray(
                        [
                            'NEW_VERSION' => $value['VERSION'],
                            'OLD_VERSION' => $value['RESTOREVERSION'],
                        ]
                    ));
                }
            }
            if ( isset($obj['VERSIONHISTORY'][0]['REPORTINFOKEY']) ) {
                $res = QueryResult([ "SELECT NAME FROM REPORTINFO WHERE CNY# = :1 AND RECORD# = :2", GetMyCompany(),
                                     $obj['VERSIONHISTORY'][0]['REPORTINFOKEY'] ]);
                if ( ! empty($res) ) {
                    $this->setTitle(I18N::getSingleToken('IA.FRW_VERSION_HISTORY_REPORT', I18N::mapToPlaceholderArray(
                        [
                            'REPORT_NAME' => $res[0]['NAME'],
                        ]
                    )));
                }
            }
        }

        return parent::mediateDataAndMetadata($obj);
    }

    /**
     * @param array $versionHistory
     *
     * @return string
     */
    function genViewTag(array $versionHistory) : string
    {
        $js = "javascript:openVersion({$versionHistory['VERSION']});";
        return "<a href=\"javascript:void(0)\" onclick=\"$js\">" . GT($this->textMap, 'IA.VIEW') . "</a>";
    }

    /**
     * @return bool
     */
    protected function CanPrint()
    {
        return false;
    }

    /**
     * Does this window have an audit trail button?
     *
     * @return bool
     */
    protected function canShowAuditTrail()
    {
        return false;
    }

    /**
     * @param string $state
     *
     * @return array|mixed
     */
    public function getStandardButtons($state)
    {
        $buttons = parent::getStandardButtons($state);

        foreach ( $buttons as &$button ) {
            if ( $button['id'] == 'cancelbuttonid' ) {
                if ( QXCommon::isQuixote() ) {
                    $button['jsCode'] =
                        "window.closeQxDialog ? window.closeQxDialog(window.frameElement) : window.close();";
                } else {
                    $button['jsCode'] = "window.close();";
                }
                break;
            }
        }

        return $buttons;
    }

    /**
     * Returns the list of Javascript files to include in the page.
     *
     * @return array the list of Javascript files to include
     */
    protected function getJavaScriptFileNames()
    {
        $jsFiles[] = '../resources/js/frwversionhistory.js';

        return $jsFiles;
    }

}
