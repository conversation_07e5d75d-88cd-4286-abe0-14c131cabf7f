<?php

/**
 * Editor class for Posting Template Batch
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

/**
 * Editor class for Posting Template Batch
 */
class PostTmplBatchEditor extends FormEditor
{

    /**
     * @param array $_params
     */
    public function __construct($_params)
    {
        parent::__construct($_params);
    }

    /**
     * Return an Array of javascript files to include into the page
     * 
     * @return array the list of javascript files to include
     */
    protected function getJavaScriptFileNames()
    {
        return array('../resources/js/posttmpl.js');
    }

    /**
     * This is a hook functions for subclases to adjust the metadata according
     * to the current data & state
     *
     * @param array &$obj the data
     *
     * @return bool|string
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        if ( $this->state == $this->kShowNewState ) {
            
            // Default the batch date to today for new transactions
            if ( !isset($obj['BATCH_DATE']) || $obj['BATCH_DATE'] == '' ) {
                $obj['BATCH_DATE'] = GetCurrentDate();
            }    
        }
        
        // If we never show location / department / dimensions in any rows of the template then we just hide
        // the columns from the grid completely
        $hideDept = true;
        $hideLoc = true;
        $hideDim = true;
        
        // If we never show location / department / dimensions as user-provided we can also hide the header
        $hideDeptHeader = true;
        $hideLocHeader = true;
        $hideDimHeader = true;
            
        // Let's loop through all the entries and see if location / department / dimensions is being hidden 
        // on all the rows
        foreach ( $obj['ENTRIES'] as $line ) {
            if ( $line['HIDEDEPT'] != 'T' ) {
                $hideDept = false;
            }  
            if ( $line['HIDELOC'] != 'T' ) {
                $hideLoc = false;
            }
            if ( $line['HIDEDIM'] != 'T' ) {
                $hideDim = false;
            }
            
            if ( $line['DEPTUSERPROV'] == 'T' ) {
                $hideDeptHeader = false;
            }  
            if ( $line['LOCUSERPROV'] == 'T' ) {
                $hideLocHeader = false;
            }
            if ( $line['DIMUSERPROV'] == 'T' ) {
                $hideDimHeader = false;
            }
        }    
         
        // Do we hide the header or not ? If the column is hidden the header will be hidden also.
        // If the column is not hidden but all the line for it are in read-only we will still hide the header.
        $hideDeptHeader = $hideDept ?: $hideDeptHeader;
        $hideLocHeader = $hideLoc ?: $hideLocHeader;
        $hideDimHeader = $hideDim ?: $hideDimHeader;

        // Build the array of path of all the potential column / fields we will have to hide 
        $fieldPaths = array(
            'DEPARTMENT' => $hideDept,
            'LOCATION' => $hideLoc,
            'HEADER_DEPARTMENT' => $hideDeptHeader,
            'HEADER_LOCATION' => $hideLocHeader
        );

        $dimPaths = IADimensions::GetDimensionIDs(! util_isPlatformDisabled());
        //epp('$dimPaths:');eppp($dimPaths);

        foreach ( $dimPaths as $dimPath ) {
            $fieldPaths[$dimPath] = $hideDim;
            $fieldPaths['HEADER_'.$dimPath] = $hideDimHeader;
        }    
        
        // Get the view
        $view = $this->getView();
   
        // Loop through all the column / fields and hide them if necessary
        $hideDefaults = true;
        foreach ( $fieldPaths as $path => $hide ) {

            if ( strpos($path, 'HEADER_') === false ) {
                $view->findAndSetProperty(
                    array('path' => $path), 
                    array('clazz' => 'PostTmplDimensionField'), 
                    EditorComponentFactory::TYPE_FIELD
                );
            }

            if ( !$hide ) {
                if ( strpos($path, 'HEADER_') !== false ) {
                    $hideDefaults = false;
                }    
                continue;
            }  
            
            // Hide the column / fields
            $view->findAndSetProperty(
                array('path' => $path), array('hidden' => true), EditorComponentFactory::TYPE_FIELD
            );
        }
        
        // If we hide all the defaults field then we can also hide the default entry button / popup
        if ( $hideDefaults ) {
     
            // Get the grid
            $matches = array();
            $view->findComponents(array('path' => 'ENTRIES'), EditorComponentFactory::TYPE_GRID, $matches);
            $grid = &$matches[0];
            
            // Get the caption
            $matches = array();
            $grid->findComponents(null, EditorComponentFactory::TYPE_CAPTION, $matches);
            $caption = &$matches[0];

            // Remove the grid's caption
            $grid->removeChild($caption);
        }

        /* @var GLBatchManager $glBatchMgr */
        $glBatchMgr = Globals::$g->gManagerFactory->getManager('glbatch');

        if (!$glBatchMgr->isJournalBillabled($obj['JOURNAL'])) {
            $billableMatches = array();
            $view->findComponents(array('path' => 'BILLABLE'), EditorComponentFactory::TYPE_GRID,
                $billableMatches);
            if ($billableMatches[0]) {
                $billableMatches[0]->setProperty('hidden', true);
            }
        }

        return true;
    }   
     
    /**
     * Prepare the data before the copy
     * 
     * @param array &$obj the object data
     * 
     * @return bool false if error else true
     */
    protected function prepareObjectForCopyNew(&$obj)
    {
        // Remove unecessary values
        // This is important because we do not want to duplicate keys or reverse information
        unset($obj['RECORDNO']);
        unset($obj['REVERSED']);
        unset($obj['REVERSEDKEY']);
        unset($obj['REVERSEDFROM']);
        unset($obj['PRBATCHKEY']);
        unset($obj['PRBATCHRECTYPE']);
        unset($obj['SCHOPKEY']);
        unset($obj['RRSENTRYKEY']);
        unset($obj['RRSKEY']);
        unset($obj['CHILDENTITY']);
        unset($obj['BATCHNO']);

        // Unset values for the entries
        foreach ( $obj['ENTRIES'] as &$entry ) {
            unset($entry['PARENTGLENTRYKEY']);
            unset($entry['RECON_DATE']);
            unset($entry['CLEARED']);
            unset($entry['CLRDATE']);
        }
        unset($entry); // unset the reference

        return true;
    }

    /**
     * This function will return the list of fields to add in the Defaults popup for the grid
     * 
     * @param array &$_params the metadata
     * @param array $objRec   the ownedobject information
     * 
     * @return array the list of fields to add into the popup
     */
    protected function getGridDefaultsFields(&$_params, $objRec)
    {
        $fields = parent::getGridDefaultsFields($_params, $objRec);

        // Do we have departments ?
        if ( departmentsExist() ) {
            $fields[] = array('path' => 'DEPARTMENT');
        }

        // Do we have locations ?
        if ( locationsExist() ) {
            $fields[] = array('path' => 'LOCATION');
        }

        return $fields;
    }
   
    /**
     * Get the default transaction from the transaction template
     * 
     * @param array $obj the data array of the previous transaction ( in case of Save & New )
     * 
     * @return array the default transaction data 
     */
    private function getDefaultTransaction($obj = array())
    {
        // Get the template key
        $id = Request::$r->_TEMPLATERECNO;
        
        // Get the ID from the URL or the object ( in case of Save & New )
        $id = ( isset($id) && $id != '' ) ? $id : $obj['TEMPLATEKEY'];
        
        // This should never happen but it does not hurt to validate
        if ( !isset($id) || $id == '' ) {
            $msg = "Could not find the transaction template key";
            $gErr = Globals::$g->gErr;
            $gErr->addError('GL-0802', __FILE__ . ':' . __LINE__, $msg);
            $this->state = $this->kErrorState;
        }

        // Get the template data
        $gManagerFactory = Globals::$g->gManagerFactory;
        $transTmplMgr = $gManagerFactory->getManager('transtmplbatch');
        $template = $transTmplMgr->get($id);
        //epp('$template:');eppp($template);

        if ( !util_isPlatformDisabled() ) {
            $platformDef = Pt_StandardUtil::getObjDef('transtmplentry');
            if ( $platformDef ) {
                $platformRels = Pt_StandardUtil::getRelationshipFields($platformDef);

                $ownedObjects =& $transTmplMgr->GetOwnedObjects();

                // TRANSTMPLENTRY
                $this->fetchPlatformRels($ownedObjects[0], $platformRels, $template);
            }
            //epp('$template:');eppp($template);dieFL();
        }

        // Create the default transaction from the template definition
        $defaultTrx = $this->getEntityMgr()->CreatePostTmplObject($template);
        
        // Build the paired values
        $this->buildPairedFieldValues($this->getEntity(), $defaultTrx, $defaultTrx); 
        
        return $defaultTrx;
    }
    
    /**
     * This is a hook functions for subclases to adjust the data for new object
     * 
     * @param array &$obj the data
     *
     * @return bool
     */
    protected function innerProcessEditNewAction(&$obj)
    {
        $obj = $this->getDefaultTransaction();
        return parent::innerProcessEditNewAction($obj); 
    }
        
    /**
     * Reset the obj data array keys on Save and New / Duplicate
     * 
     * @param EntityManager  $entityMgr
     * @param array   &$obj
     * @param bool $isCopyNew 
     */
    protected function resetKeys($entityMgr, &$obj, $isCopyNew = false)
    {
        // We will remove all the data for save and new
        if ( $isCopyNew ) {
            parent::resetKeys($entityMgr, $obj, $isCopyNew);
        } else {        
            $obj = $this->getDefaultTransaction($obj);
        } 
    }

    /**
     * Overriding getEntityMgr
     *
     * @return PostTmplBatchManager
     */
    public function getEntityMgr(){
        assert($this->entityMgr instanceof PostTmplBatchManager);
        return $this->entityMgr;
    }
}
