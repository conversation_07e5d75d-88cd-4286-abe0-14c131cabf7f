<?php

/**
 * Manager class for the Journal Entry History type object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2012 Intacct Corporation All, Rights Reserved
 */

/**
 * Manager class for the Journal Entry History type object
 */
class GLBatchHistoryManager extends EntityManager
{

    const COMMENT_LENGTH = 1000;
    /**
     * @param array $params the default parameters
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
    }

    /**
     * @param array &$values
     *
     * @return bool
     */
    protected function regularAdd(&$values) {

        $ok = parent::regularAdd($values);

        // Do the GLBATCH auditing here ...
        if ($ok) {
            self::addGLAuditEvent($values);
        }

        return $ok;

    }

    /**
     * interprets GL values to add appropriate audit event.
     *
     * @param array $values
     */
    public static function addGLAuditEvent($values)
    {
        $auditTrailSession = AuditTrailSession::getInstance();
        if ($auditTrailSession->isAuditEnabled()) {
            $event = $values['EVENT'];
            $auditEntity = $values[':entity'];
            $auditKey = $values['GLBATCHKEY'];
            $auditPayload = null;
            $editAuditEvent = null;
            $workflowAction = null;

            //   If Create/Edit/Submit, add the appropriate new/update event.
            if ($event == 'Create' || $event == 'Edit' || $event == 'Submit' || $event == GLBatchManager::RECLASS_EVENT) {
                // leave the payload blank so that we read it from the cache later
                $auditPayload = new AuditTrailPayload($auditEntity, $auditKey, null);
            }
            if ($values['EVENT'] == 'Create') {
                $editAuditEvent = AuditTrail::AUDITTRAIL_EVENT_NEW;
                $hasCreate = true;
            } else {
                $hasCreate = $auditTrailSession->hasCreateEvent($auditEntity, $auditKey);
                if ($values['EVENT'] == 'Edit' ||
                 (($values['EVENT'] == 'Submit' || $values['EVENT'] == 'Post' || $values['EVENT']== GLBatchManager::RECLASS_EVENT) && !$hasCreate)) {

                    //  Does this for Edit and Submit/Post that was not part of a Create (e.g. submit after draft).
                    $editAuditEvent = AuditTrail::AUDITTRAIL_EVENT_UPDATE;
                }
            }
            if ($editAuditEvent) {
                $auditTrailSession->addAuditEvent($auditEntity, $auditKey, $editAuditEvent, $auditPayload,
                    $workflowAction);
            }

            //  If warranted, add the appropriate user action.
            //  Don't log approval rule-initiated events as user actions unless 'Approve' or 'Decline'.
            if ($values[':isapproval'] == true && !($values['EVENT'] == 'Approve' || $values['EVENT'] == 'Decline')) {
                return;
            }

            //  Need to differentiate between POST, DRAFT, and SAVE.
            if ($values['EVENT'] === 'Post' || $values['EVENT'] === 'Submit' ||
             $values['EVENT'] == 'Edit' || $values['EVENT'] === 'Create') {
                if ($values['STATE'] == 'Draft') {
                    $auditEventId = AuditTrail::AUDITTRAIL_ACTION_DRAFT;
                } else if ($hasCreate) {
                    $auditEventId = AuditTrail::AUDITTRAIL_ACTION_POST;
                } else if ($values[':previousstate'] == 'Submitted' && $values['STATE'] == 'Submitted') {
                    $auditEventId = AuditTrail::AUDITTRAIL_ACTION_POST;
                } else {
                    $auditEventId =
                     ($values[':previousstate'] == 'Draft' || $values[':previousstate'] == 'Declined')
                     ? AuditTrail::AUDITTRAIL_ACTION_POST : AuditTrail::AUDITTRAIL_ACTION_SAVE;
                }
            } else if ($values['EVENT'] === 'Decline') {
                $auditEventId = AuditTrail::AUDITTRAIL_ACTION_DECLINE;
            } else if ($values['EVENT'] === 'Approve') {
                $auditEventId = AuditTrail::AUDITTRAIL_ACTION_APPROVE;
            } else if ($values['EVENT'] === 'Recall') {
                $auditEventId = AuditTrail::AUDITTRAIL_ACTION_RECALL;
            } else if ($values['STATE'] === 'Draft') {
                $auditEventId = AuditTrail::AUDITTRAIL_ACTION_DRAFT;
            } else if ($values['EVENT'] === GLBatchManager::RECLASS_EVENT) {
                $auditEventId = AuditTrail::AUDITTRAIL_ACTION_RECLASS;
            }
            /** @noinspection PhpUndefinedVariableInspection */
            if ($auditEventId) {
                AuditTrailSession::createAuditActionEvent($auditEntity, $auditKey, $auditEventId);
            }
        }
    }

    /**
     * @return bool
     */
    public function IsAuditEnabled()
    {
        // No auditing on this object that is system generated
        return false;
    }

    /**
     * @param string $verb
     * @param string $key
     * @param mixed  $param1
     * @param mixed  $param2
     * @param array  $values
     *
     * @param bool   $fastUpdate
     *
     * @return bool
     */
    public function DoEvent($verb, $key, $param1 = null, $param2 = null, $values = [], $fastUpdate = false)
    {
        // No reason to raise events on this object
        return true;
    }
}
