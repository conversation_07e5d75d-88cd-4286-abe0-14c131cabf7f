<?xml version="1.0" encoding='UTF-8'?>
<ROOT>
    <entity>glacctallocationrun</entity>
    <title>IA.GENERATE_ALLOCATION</title>
    <helpfile>Adding_Editing_and_Viewing__AccountAllocation</helpfile>
    <view system="true">
        <events>
            <load>accountRunOnLoad();</load>
        </events>
        <pages id="mainPages">
            <page id="subPage">
                <section>
                    <title>IA.ALLOCATION_PARAMETERS</title>
                    <subsection>
                        <field path="DUMMYHDR" isHTML="1" noLabel="1">
                            <type type="textlabel" ptype="textlabel"></type>
                            <default>IA.ENTER_GL_POSTING_DATE_AND_YOUR_EMAIL_ADDRESS_AN</default>
                        </field>
                    </subsection>
                    <subsection columnCount="3">
                        <field hidden="true">
                            <path>RECORDNO</path>
                        </field>
                        <field>
                            <path>GLPOSTINGDATE</path>
                        </field>
                        <field>
                            <path>EMAIL</path>
                        </field>
                    </subsection>
                </section>
                <section>
                    <title>IA.ALLOCATION_ID</title>
                    <subsection>
                        <field path="DUMMYHDR" isHTML="1" noLabel="1">
                            <type type="textlabel" ptype="textlabel"></type>
                            <default>IA.SELECT_THE_ALLOCATION_TO_BE_PROCESSED</default>
                        </field>
                    </subsection>
                    <subsection columnCount="3">
                        <field>
                            <path>ASOFDATE</path>
                        </field>
                    </subsection>
                    <subsection columnCount="3">
                        <field fullname="IA.ALLOCATION_RUN_TYPE">
                            <path>ALLOCATIONRUNTYPE</path>
                            <type>
                                <type>radio</type>
                                <ptype>radio</ptype>
                            </type>
                            <layout>portrait</layout>
                            <events>
                                <change>hideAllocationType(this.meta)</change>
                            </events>
                        </field>
                        <field>
                            <path>GLACCTALLOCATION</path>
                        </field>
                        <field>
                            <path>GLACCTALLOCATIONGRP</path>
                        </field>
                    </subsection>
                    <subsection id="allocationgrpmembersection">
                        <grid noDragDrop="true" isCollapsible="false" hasFixedNumOfRows="true">
                            <path>GLACCTALLOCATIONMEMBERS</path>
                            <column>
                                <field fullname = "IA.MEMBER" readonly="true">
                                    <path>MEMBER</path>
                                </field>
                            </column>
                            <column>
                                <field fullname = "IA.SOURCE_POOL_TIME_PERIOD" readonly="true">
                                    <path>SOURCE_TIME_PERIOD</path>
                                </field>
                            </column>
                            <column>
                                <field fullname = "IA.BASIS_TIME_PERIOD" readonly="true">
                                    <path>BASIS_TIME_PERIOD</path>
                                </field>
                            </column>
                            <column>
                                <field fullname = "IA.POSTING_JOURNAL" readonly="true">
                                    <path>POSTING_JOURNAL</path>
                                </field>
                            </column>
                            <column>
                                <field fullname = "IA.LAST_POSTED_DATE" readonly="true">
                                    <path>POSTED_DATE</path>
                                </field>
                            </column>
                        </grid>
                    </subsection>
                </section>
            </page>
        </pages>
    </view>
</ROOT>
