<?php
/**
 * Lister class for GL account group
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

/**
 * Lister class for GL account group
 */
class GLAcctGrpLister extends NLister
{

    /* @var string $extraHtmlHeaderContent */
    private $extraHtmlHeaderContent = '';

    /* @var int $isNotMegaSharedEntity */
    var $isNotMegaSharedEntity = 1;

    /* @var string $opSubAction */
    var $opSubAction = 'account';

    /* @var null|string $opSubActionDimFilter */
    var $opSubActionDimFilter = '';

    /* @var string $dimEntity */
    var $dimEntity = '';

    /* @var string[][] $dimNameTypeMap */
    var $dimNameTypeMap;

    /* @var array $dimFields */
    var $dimFields;

    /* @var array $map; */
    var $map;

    /* @var bool $useiatables */
    public  $useiatables = false;

    /**
     * @var array
     */
    protected $dimHeader = ['vendor','customer','project','employee','item','class','contract','task','warehouse','costtype','fixedasset','affiliateentity'];
    
    /**
     * @var array
     */
    protected $additionalTokens = [
        'IA.ACCOUNT_GROUP_TYPE','IA.DIMENSION_REPORT_STRUCTURE_TYPE','IA.GROUP_OF_ACCOUNT_GROUPS','IA.REPORTS','IA.GRAPHS','IA.HIERARCHY', 'IA.ACCOUNT_GROUPS','IA.IMPORT','IA.GROUP_MEMBERS','IA.EXPORT',
        'IA.FLIPVIEW','IA.MEMBERS','IA.FLIP_VIEW','IA.IMPORT','IA.EXPORT','IA.ACCOUNT_GROUPS_LIBRARY','IA.SHOW_ACCOUNT_GROUPS_LIBRARY','IA.PLEASE_SELECT_AN_OPTION'
    ];

    public function __construct()
    {
        $this->isQuixote = QXCommon::isQuixote();
        /** @noinspection PhpUnusedLocalVariableInspection */
        $gErr = Globals::$g->gErr;
        $gManagerFactory = Globals::$g->gManagerFactory;
        
        /** @var  IAGLAcctGrpManager|GLAcctGrpManager $mgr */
        $mgr =  ($this->useiatables) ? 'IAGLAcctGrpManager' : 'GLAcctGrpManager';
        $mapping = $mgr::getDimensionIDToDimensionComponentMapping();
        $dimDisplayAsNameMap = [];

        foreach ( $mapping as $value) {
            $keys = $value['dim_name_pl'];
            $keyDG = "Group of " . $value['dim_name_pl'];
            $dimDisplayAsNameMap[$value['dim_component_id_internal']] = $keys;
            $dimDisplayAsNameMap[$value['dim_component_id_internal_dg']] = $keyDG;
        };

        $this->map = $dimDisplayAsNameMap;


        $this->opSubAction = Request::$r->opSubAction;
        $this->opSubActionDimFilter = Request::$r->dimGroupTypeRestriction;
        if ($this->opSubAction == '') {
            $this->opSubAction = Request::$r->_opSubAction;
            if ($this->opSubAction == '') {
                $this->opSubAction = 'account';
            }
        }
        if ($this->opSubActionDimFilter == '') {
            $this->opSubActionDimFilter = Request::$r->_dimGroupTypeRestriction;
        }

        if ( IsMultiEntityCompany() && GetContextLocation() ) {
            $this->isNotMegaSharedEntity = 0;
        }

        $title = ($this->opSubAction == 'dimension') ? "IA.DIMENSION_REPORT_STRUCTURES":"IA.ACCOUNT_GROUPS" ;
        $label = ($this->opSubAction == 'dimension') ? "IA.DIMENSION_REPORT_STRUCTURE_TYPE" :"IA.ACCOUNT_GROUP_TYPE";
        if ($this->opSubAction == 'dimension' && $this->opSubActionDimFilter != '') {
            $this->dimEntity = self::overrideTitle($title, $label, $this->opSubActionDimFilter);
        }

        if ( $this->opSubAction == 'dimension' ) {
            $fields = array('NAME', 'TITLE', 'MEMBERTYPE',"'FINREPORTSURL'", "'FINGRAPHSURL'");
            if ( $this->dimEntity != 'CUSTDIM') {
                $fields = array('NAME', 'TITLE','MEMBERTYPE', "'HIERARCHY'", "'FINREPORTSURL'", "'FINGRAPHSURL'");
            }

            // only for task group or cost type group listers suppress the show private..
            $suppressPrivate = ($this->dimEntity == 'task' || $this->dimEntity == 'costtype' ? true : false);

            parent::__construct(
                array(
                    'entity' => 'glacctgrp',                    
                    'title' => $title,
                    'fields' => $fields,
                    'nonencodedfields' => array('RECORD_URL', "'HIERARCHY'","'FINREPORTSURL'", "'FINGRAPHSURL'"),
                    'nofilteronthesefields' => ['MEMBERTYPE'],
                    'entitynostatus' => 1,
                    'suppressPrivate' => $suppressPrivate,
                    'helpfile' => 'Viewing_and_Managing_Dimension_Structures',
                    'importtype' => (IsMCMESubscribed() && GetContextLocation()) ? '' : 'dimstructure',
                    'customexport' => true,
                )
            );
            $this->addLabelMapping('MEMBERTYPE', $label, true);
        } else {
            parent::__construct(
                array(
                    'entity' => 'glacctgrp',
                    'title' => $title,
                    'fields' => array('NAME', 'TITLE', 'GLACCTGRPPURPOSEID','MEMBERTYPE',  "'HIERARCHY'", "'FINREPORTSURL'", "'FINGRAPHSURL'"),
                    'nonencodedfields' => array('RECORD_URL',"'HIERARCHY'", "'FINREPORTSURL'", "'FINGRAPHSURL'"),
                   // 'fieldlabels' => array(_('Name'), _('Display as'), _('Account group purpose'), $label, '', '', ''),
                    'entitynostatus' => 1,
                    'helpfile' => 'Viewing_and_Managing_Account_Groups',
                    'customexport' => true,
                )
            );
        }

        $this->addLabelMapping('TITLE', 'IA.DISPLAY_AS', true);
        $this->addLabelMapping('MEMBERTYPE', $label, true);

        $action = Request::$r->_action;

        if ( $action == 'deliver' ) {

            //Getting all the filters needed for exporting
            $this->Init();
            $filters = $this->CalcFiltersLite();
            $glAcctGrpMgr = $gManagerFactory->getManager('glacctgrp');
            $this->dimFields = $glAcctGrpMgr->GetDimensionFields();
            $type = Request::$r->_type;
            $records = array();

            //Check for include private
            if ($this->showPrivate) {
                SetReportViewContext();
            }

            if ($filters) {
                $filter = array(
                    'selects' => array('RECORDNO'),
                    'filters' => array($filters)
                );

                $res = $glAcctGrpMgr->GetList($filter);
                if ($res) {
                    foreach($res as $record) {
                        $records[] = $record['RECORDNO'];
                    }
                } else {
                    $records[] = -1;
                }
            }

            $this->PrintAcctGrpExport($type, $records);
            exit();
        }
    }

    /**
     * overrideTitle
     * This overrides the default title and replaces that with title for selected dimension...
     *
     * @param string $title
     * @param string $label
     * @param string $opSubActionDimFilter
     *
     * @return string
     */
    public static function overrideTitle(&$title, &$label, $opSubActionDimFilter)
    {
        $dimEntity = '';
        $dims = IADimensions::getAllDimensions(!util_isPlatformDisabled());

        foreach ( $dims as $dim ) {
            if ( $dim['componentval'] == $opSubActionDimFilter ) {
                if ( $dim['standard'] === false ) {
                    $dimEntity = 'CUSTDIM';   
                } else {
                    $dimEntity = $dim['entity'];
                }

                if ($dim['udd'] == 1 && $dim['udd'] == true) {
                    $title = I18N::getSingleToken('IA.UDD_REPORT_STRUCTURES', [
                        ['name' => 'UDDNAME', 'value' => $dim[ 'label' ]]
                        ]);
                    //labels does not support placeholders, so resolving label here.
                    $label = I18N::getSingleToken('IA.UDD_REPORT_STRUCTURE_TYPE', [
                        ['name' => 'UDDNAME', 'value' => $dim[ 'label' ]]
                    ]);
                } else {
                    $title = I18N::getSingleToken("IA." . strtoupper($dim[ 'entity' ]) . "_REPORT_STRUCTURES");
                    $label = "IA." . strtoupper($dim[ 'entity' ]) . "_REPORT_STRUCTURE_TYPE";
                }

                break;
            }                
        }

        return $dimEntity;
    }

    /**
     * @return string
     */
    protected function getPageHelpContent()
    {
        $params = array('helpid' => $this->opSubAction == 'dimension' ? 'Viewing_and_Managing_Dimension_Structures': 'Viewing_and_Managing_Account_Groups');
        $helpContentScript = HelpContentEngine::getHelpContentJavaScript($params);
        return $helpContentScript;
    }


    /**
     * @return string
     */
    function genGlobs()
    {
        $ret = parent::genGlobs();
        
        if ($this->opSubAction) {
            $ret .= "<g name='.opSubAction'>".isl_htmlspecialchars($this->opSubAction)."</g>";
        }
        if ($this->opSubActionDimFilter) {
            $ret .= "<g name='.dimGroupTypeRestriction'>".isl_htmlspecialchars($this->opSubActionDimFilter)."</g>";
        }
        return $ret;
    }

    /**
     * Override to change the required values
     */
    function BuildTable()
    {
        $gManagerFactory = Globals::$g->gManagerFactory;        
        $_sess = Session::getKey();
        /** @noinspection PhpUnusedLocalVariableInspection */
        $op = GetOperationId('gl/lists/glacctgrp');

        parent::BuildTable();
        $table = &$this->table;
        /** @noinspection PhpUnusedLocalVariableInspection */
        $p = &$this->_params;

        $repOp = GetOperationId('gl/reports/glacctgrp');
        for ( $i = 0; $i < count($table); $i++ ) {
            $rec = &$table[$i];
            /** @noinspection PhpUnusedLocalVariableInspection */
            $recno = urlencode($table[$i]['NAME']);

            $acctgrpkey = $table[$i]['RECORDNO']; // Get the record# of the GL Account Group.
            if ( !isl_strcasecmp($table[$i]['MEMBERTYPE'], 'GROUP') ) {
                $table[$i]['MEMBERTYPE'] = GT($this->textMap ,"IA.GROUP_OF_ACCOUNT_GROUPS");
            } else if ($this->opSubAction == 'dimension') {
                $mungedType = $this->dimNameTypeMap[$this->map[$this->values[$i]['MEMBERTYPE']]];
                if (!empty($mungedType)) {
                    $table[$i]['MEMBERTYPE'] = $mungedType;
                }
            }

            if ( isset($this->dimEntity) && $this->dimEntity !='' ) {
                if (  $this->dimEntity != 'CUSTDIM' ) {
                    
                    $dimMgr = $gManagerFactory->getManager($this->dimEntity);
                    $mod = $dimMgr->GetHomeModule();
                    $dimOpKey = $mod.'/lists/'.$this->dimEntity;
                    $dimOp = GetOperationId($dimOpKey);
                    /** @noinspection PhpUnusedLocalVariableInspection */
                    $hierLabel = "Members";
                    $memberURL = 'lister.phtml?.fromGLAcctGrp=1&.op='.$dimOp.'&.showprivate='.$this->showPrivate.'&.dimComp='.urlencode($table[$i]['NAME']);
                    $memberURL = ProcessSess($memberURL);
                    $aTarget = '';
                    if (!$this->isQuixote) {
                        $aTarget = " target='".$this->dimEntity."' ";
                    }
                    $table[$i]["'HIERARCHY'"] = "<a href=\"" . $memberURL ."\" ".$aTarget.'>' . I18N::getSingleToken('IA.MEMBERS') . "</a>";
                }
            } else {
                $hierLabel = I18N::getSingleToken('IA.HIERARCHY');
                if ( !isl_strcasecmp($this->values[$i]['MEMBERTYPE'], 'C') ) {
                    $hierLabel = I18N::getSingleToken('IA.FORMULA_DETAILS');
                }
                $table[$i]["'HIERARCHY'"] = "<a href=\"javascript:Launch('reporteditor.phtml?.sess=$_sess&.op=$repOp&RNO=" .
                    $rec['RECORDNO'] . "&.p=1&.type=html&.drillfilter=1','AccGrpListHirarchy', 900, 550)\">" .
                    $hierLabel . "</a>";
            }
            // Do we have the location/department group value set ? If it is then use it else take the normal
            // department/location value.
            if ( isset($table[$i]['LOCGRPID']) && $table[$i]['LOCGRPID'] != '' ) {
                $table[$i]['LOCNO'] = $table[$i]['LOCGRPID'];
            }
            if ( isset($table[$i]['DEPTGRPID']) && $table[$i]['DEPTGRPID'] != '' ) {
                $table[$i]['DEPTNO'] = $table[$i]['DEPTGRPID'];
            }

            if ( $this->isNotMegaSharedEntity
                && (isset($table[$i]['GLACCTGRP.MELOCATIONKEY']) && $table[$i]['GLACCTGRP.MELOCATIONKEY'] != '')
            ) {
                $table[$i]["'FINREPORTSURL'"] = '';
                $table[$i]["'FINGRAPHSURL'"] = '';
            } else {
                $financialreportsop = GetOperationId('gl/reports/financialreport'); // op value for Financial Reports.
                // href link for Financial Reports.
                $financialreportshref = " href=\"lister.phtml?.op=" . $financialreportsop . "&.acctgrpkey=" .
                    $acctgrpkey . "&" . OptDone(ScriptRequest()) . "\" ";
                $financialgraphsop = GetOperationId('gl/reports/graphs'); // op value for Financial Graphs.
                // href link for Financial Graphs.
                $financialgraphshref = " href=\"lister.phtml?.op=" . $financialgraphsop . "&.acctgrpkey=" .
                    $acctgrpkey . "&" . OptDone(ScriptRequest()) . "\" ";
                // Build drill down for Financial reports and graphs.
                $table[$i]["'FINREPORTSURL'"] = "<a tabIndex=\"-1\" " . $financialreportshref . "> " . I18N::getSingleToken('IA.REPORTS') . " </a>";
                $table[$i]["'FINGRAPHSURL'"] = "<a tabIndex=\"-1\" " . $financialgraphshref . ">" . I18N::getSingleToken('IA.GRAPHS') . "</a>";
              

                if ($this->opSubAction == 'dimension') {                    
                    $table[$i]["'FINGRAPHSURL'"] = "";
                }
            }
        }

        if($this->type == kShowHTML) {
            //russ: I copied this from the invoice lister to make sure this crap is
            //put into the correct place in the XML generated file. As far as I can
            //tell this code was always broken!
            ob_start();
            InitJSGlobals();
            $this->showScripts();
            $this->extraHtmlHeaderContent .= ob_get_clean();
        }
    }

    /**
     * @return string
     */
    protected function getExtraHtmlHeadContent()
    {
        return $this->extraHtmlHeaderContent;
    }

    /**
     * @return array Query spec
     */
    public function BuildQuerySpec()
    {
        $val = &Request::$r->{"F_MEMBERTYPE"};
        $filterTest = isl_strtolower($val);
        if (strpos($filterTest, 'gr') !== false ) {
            //becuase the member type for account groups is 'Group', but the display value is 'Group of Account Groups'
            //have this hack to fix up the string. No other have the characters gr in them so while the user could type garbage
            //as long as it has a gr we will match to this.
            $val =  'groups';
        }

        $querySpec = parent::BuildQuerySpec();

        $querySpec['selects'][] = 'RECORDNO';

        // This is a hack to make department/location group showing up in the lister since we use the same field to
        // select group and non-group but we have different DB column. This code should be cleaned up.
        // What we do is just adding the group is the user select the location or deparment
        // In the BuildTable function is the group value is set we will use it else we will use 
        // the department/location value
        if ( in_array('LOCNO', $querySpec['selects']) ) {
            $querySpec['selects'][] = 'LOCGRPID';
        }
        if ( in_array('DEPTNO', $querySpec['selects']) ) {
            $querySpec['selects'][] = 'DEPTGRPID';
        }

        // Same logic for the filters. If location or department is selected we will add the groups to the filter also
        if ( array_key_exists('filters', $querySpec) ) {

            foreach ( $querySpec['filters'][0] ?? [] as $key => $filter ) {

                if ( empty($filter) ) {
                    break;
                }

                // Add an OR condition for the group on the original filter
                // We are checking for ILIKE because this will create an oracle error.
                if ( ( $filter[0] == 'LOCNO' || $filter[0] == 'DEPTNO' ) && $filter[1] != 'ILIKE' ) {

                    $path = ( $filter[0] == 'LOCNO' ) ? 'LOCGRPID' : 'DEPTGRPID';
                    $grpfilter = $filter;
                    $grpfilter[0] = $path;

                    $newfilter = array(
                        'operator' => 'OR',
                        'filters' => array(
                            $filter,
                            $grpfilter
                        )
                    );

                    $querySpec['filters'][0][$key] = $newfilter;
                }
            }
        }

        $accountComponents = array('Accounts', 'Groups', 'Statistical Accounts', 'Computation', 'Category', 'Statistical Category');
        if ($this->opSubAction == 'dimension') {
            if ($this->opSubActionDimFilter != '') {
                $dims = IADimensions::getAllDimensions();
                $dimFilter = [];
                $this->dimNameTypeMap = [];
                //Pre load tokens
                $tokens = [ 'IA.HIERARCHY_OF_OBJDEF' ];
                foreach ( $dims as $dim ) {
                    $tokens[] = $dim['label'] . '_GROUPS';
                }
                $tokenMap = I18N::getTokensForArray(I18N::tokenArrayToObjectArray($tokens));
                foreach ($dims as $dim) {
                    $plLabel = $dim['internal_label_plural'];
                    $dimFilter[$dim['componentval']] = array($plLabel, 'Group of '.$plLabel);
                    //Resolve labels
                    $this->dimNameTypeMap[$plLabel] = GT($tokenMap,$dim['label'] . '_GROUPS');
                    $this->dimNameTypeMap['Group of '. $plLabel] = I18N::getSingleToken('IA.HIERARCHY_OF_OBJDEF', I18N::mapToPlaceholderArray(
                            ['OBJECT_DEFINITION_NAME' => $this->dimNameTypeMap[$plLabel]]
                    ));
                }
                // Merge custom dimensions
                $custDims = IADimensions::getCustomDimensionObjectProperties();
                foreach( $custDims as $key => $dim ) {
                    $label = $dim['label'];
                    $dimFilter[ isl_strtoupper($key) ] = array( $label, 'Group of ' . $label );

                    $this->dimNameTypeMap[$label] = isl_ucfirst($dim['label'].' groups');
                    $this->dimNameTypeMap['Group of ' . $label] = 'Hierarchy of '.$dim['label'].' groups';
                }
                
                // TODO: XSS Alert - SQL injection hole!!
                $querySpec['filters'][0][] = array('MEMBERTYPE', 'IN', $dimFilter[$this->opSubActionDimFilter]);
            } else {
                $querySpec['filters'][0][] = array('MEMBERTYPE', 'NOT IN', $accountComponents);
            }
        } else {
            $querySpec['filters'][0][] = array('MEMBERTYPE', 'IN', $accountComponents);
        }

        // Same logic for the orders. If location or department is selected we will add the groups to the filter also
        if ( array_key_exists('orders', $querySpec) ) {
            foreach ( $querySpec['orders'] as $order ) {

                if ( empty($order) ) {
                    break;
                }

                $path = trim($order[0]);

                // Add an order in the list for the groups
                if ( $path == 'LOCNO' || $path == 'DEPTNO' ) {

                    $newpath = ( $path == 'LOCNO' ) ? ' LOCGRPID' : ' DEPTGRPID';

                    $neworder = $order;
                    $neworder[0] = $newpath;

                    $querySpec['orders'][] = $neworder;
                }
            }
        }

        return $querySpec;
    }

    /**
     * overrding the base function from NLister.cls
     *
     * @return string top panel
     */
    function genTopPanel()
    {
        $ret = parent::genTopPanel();
        if ( $this->opSubAction == 'account' ) {             
            $ret = "<b id='flip'/>" . $ret;
            if ( $this->isNotMegaSharedEntity ) {
                $ret = $ret . "<b id='import'/>";
                $ret = $ret . "<b id='exportgroups'/>";
            }
        }

        $ret = "<b id='iaacctgrp'/>" .$ret;
        return $ret;
    }

    /**
     * overrding the base function from NLister.cls
     * 
     * @return string bottom panel
     */
    function genBotPanel()
    {
        $ret = parent::genBotPanel();
        if ( $this->opSubAction == 'account' ) {             
            $ret = "<b id='flip'/>" . $ret;
            if ( $this->isNotMegaSharedEntity ) {
                $ret = $ret . "<b id='import'/>";
                $ret = $ret . "<b id='exportgroups'/>";
            }
        }

        $ret = "<b id='iaacctgrp'/>" . $ret;
        return $ret;
    }

    /**
     * overrding the base function from NLister.cls
     *
     * @return string All buttons
     */
    function genAllButtons()
    {
        $ret = parent::genAllButtons();
        $p = &$this->_params;
        $industry = GetMyIndustryType();

        if ( $this->opSubAction == 'account' ) {
            $ret = "<b id='flip'>" . $this->calcFlipUrl() . "</b>" . $ret;
            if ( $this->isNotMegaSharedEntity ) {
                $ret = $ret . "<b id='import'>" . $this->calcImportAcctGrpUrl() . "</b>";
                $ret = $ret . "<b id='exportgroups'>" . $this->calcExportAcctGrpUrl() . "</b>";
            }
        }

        $lib = true;
        if (IsMultiEntityCompany() && GetContextLocation()) {
            $lib = false;
        }

        // show ia account group button if the industry is not null
        // and user has permission to add/edit the account group
        if ($industry != '' && $lib && ($p['_op']['create'] != '' || $p['_op']['edit'] != '')
            && $this->dimEntity != 'CUSTDIM') {
            //To show IA Account group
            $ret = $ret . "<b id='iaacctgrp'>" . $this->calcIAAcctGrp($this->opSubAction) . "</b>";
        }

        return $ret;
    }

    /**
     * @param int    $setupOP
     * @param string $entity
     * @param array  $param
     *
     * @return string
     */
    public function getImportURL($setupOP, $entity, $param)
    {
        $url = parent::getImportURL($setupOP, $entity, $param);
        $url = $url . "&.postprocess_arg=" . isl_htmlspecialchars($this->opSubActionDimFilter);
       
        return $url;
    }

    /**
     * Add Javascript and CSS to the page.  Subclasses really should call back to their parent.
     *
     * @param bool $addYuiCss  include the YUI css files
     */
    function showScripts($addYuiCss = true)
    {
        if (!$this->isQuixote) {
            parent::showScripts($addYuiCss);
        ?>
            <?php echo IALayoutManager::getThemeStylesheetHtml(); ?>
        <? } ?>
        <SCRIPT LANGUAGE="JavaScript">
            <!--
            // same as BlackoutBackground() in resources/js/editor.js 
            function BlackoutBackground(dis) {
                var blackoutdivObj = document.getElementById('page_screen0');
                if (!blackoutdivObj) {
                    return false;
                }
                if (dis) {
                    blackoutdivObj.style.height = screen.height + 'px';
                    blackoutdivObj.style.width = screen.width + 'px';
                    blackoutdivObj.style.display = 'block';
                } else {
                    blackoutdivObj.style.display = 'none';
                }
            }

            // similar to showDeliverPage() in resources/js/editor.js
            function showDeliverImportAcctGrpPage(show) {
                var delObj = document.getElementById('deliverImportAcctGrpSelect');

                if (delObj) {
                    if(globalIs.ie) {
                        //When buttons are on the bottom: sometimes IE scrools to the top for an unknown reason (not reproducible on all machines.
                        // So I jusr scroll it first but only in IE..
                        if (document.documentElement) {
                            document.documentElement.scrollTop = '0px';
                        } else {
                            document.body.scrollTop = '0px'
                        }
                    }


                    BlackoutBackground(show);
                    if (show) {

                        var myWidth ;
                        var myHeight ;
                        var scrollTop = 0;
                        var scrollLeft = 0;

                        myWidth = jq(window).width();
                        myHeight = jq(window).height();

                        if(document.documentElement ) {
                            scrollTop = parseInt(Math.max(parseInt(document.documentElement.scrollTop), parseInt(document.body.scrollTop)));
                            scrollLeft = parseInt(Math.max(parseInt(document.documentElement.scrollLeft), parseInt(document.body.scrollLeft)));
                        } else {
                            scrollTop = parseInt(document.body.scrollTop);
                            scrollLeft = parseInt(document.body.scrollLeft);
                        }
                                                
                        delObj.style.top = myHeight/2 - ((myHeight/2 > 150) ? 150 : 0) + scrollTop +  'px';
                        delObj.style.left = myWidth/2 - ((myWidth/2 > 100) ? 100 : 0) + scrollLeft + 'px';
                        delObj.style.display = 'block';
                        document.getElementsByTagName('BODY')[0].style.overflow = 'hidden';

                    } else {
                        delObj.style.display = 'none';
                        var overflow;
                        if(globalIs.ie) {
                            overflow = 'auto';
                        } else {
                            overflow = 'visible';
                        }
                        document.getElementsByTagName('BODY')[0].style.overflow = overflow;
                    }
                }
            }

            function showDeliverExportAcctGrpPage(show) {
                var delObj = document.getElementById('deliverExportAcctGrpSelect');

                if (delObj) {
                    BlackoutBackground(show);
                    if (show) {
                        if(globalIs.ie) {
                            //When buttons are on the bottom: sometimes IE scrools to the top for an unknown reason (not reproducible on all machines.
                            // So I jusr scroll it first but only in IE..
                            if (document.documentElement) {
                                document.documentElement.scrollTop = '0px';
                            } else {
                                document.body.scrollTop = '0px'
                            }
                        }

                        var myWidth ;
                        var myHeight ;
                        var scrollTop = 0;
                        var scrollLeft = 0;

                        myWidth = jq(window).width();
                        myHeight = jq(window).height();

                        if(document.documentElement ) {
                            scrollTop = parseInt(Math.max(parseInt(document.documentElement.scrollTop), parseInt(document.body.scrollTop)));
                            scrollLeft = parseInt(Math.max(parseInt(document.documentElement.scrollLeft), parseInt(document.body.scrollLeft)));
                        } else {
                            scrollTop = parseInt(document.body.scrollTop);
                            scrollLeft = parseInt(document.body.scrollLeft);
                        }

                        delObj.style.top = myHeight/2 - ((myHeight/2 > 150) ? 150 : 0) + scrollTop +  'px';
                        delObj.style.left = myWidth/2 - ((myWidth/2 > 100) ? 100 : 0) + scrollLeft + 'px';
                        delObj.style.display = 'block';
                        document.getElementsByTagName('BODY')[0].style.overflow = 'hidden';

                    } else {
                            delObj.style.display = 'none';
                            var overflow;
                            if(globalIs.ie) {
                                overflow = 'auto';
                            } else {
                                overflow = 'visible';
                            }
                            document.getElementsByTagName('BODY')[0].style.overflow = overflow;
                    }
                }
            }

            // similar to setDeliver() in resources/js/editor.js
            function setDeliverImportAcctGrp(type) {
        <?php
        $_sess = Session::getKey();
        $op = GetOperationId('co/setup/import');

        $url = "import_wizard_lite.phtml?.sess=$_sess&.op=$op&.importtype=";
        $params = "width=640,status=yes,height=340,scrollbars=no,dependent,left=100,top=100";
        ?>
                if (type == 'G') {
                    strImporttype = 'acctgrpinfo';
                } else if (type == 'M') {
                    strImporttype = 'acctgrpmembers';
                }
                window.open('<?php echo $url ?>' + strImporttype, 'csv', '<?php echo $params ?>');
                if (PAGE_LAYOUT_TYPE == 'Q') {
                    jq('#deliverImportAcctGrpSelect').modal('hide');
                } else {
                    showDeliverImportAcctGrpPage(false);
                }

            }

            function setDeliverExportAcctGrp(type) {

                //Get the advance filters
                var fp = encodeURIComponent(document.getElementById(".FPaths").value).replace(/%20/g,'+');
                var op = encodeURIComponent(document.getElementById(".FOprs").value).replace(/%20/g,'+');
                var val = encodeURIComponent(document.getElementById(".FVals").value).replace(/%20/g,'+');

                //Check for top or bottom filter
                var filtertype = (document.ff.elements['.filterPosition'].value == 'top' ? topFilter : bottomFilter);

                //Get all the filters
                var filters = filtertype.getElementsByTagName("input");
                var filterstring = "";
                if (filters.length > 0) {
                    for (var i = 0; i < filters.length; i++) {
                        filterstring += "&" + filters[i].getAttribute("name") + "=" +  filters[i].value;
                    }
                }
                filterstring = filterstring.replace(/ /g,'+');

                <?php
                    $_sess = Session::getKey();
                    $op = GetOperationId('gl/lists/glacctgrp');
                    $printUrl = "lister.phtml?.op=" . $op . "&.action=deliver&.type=' + type + '&.sess=" . $_sess ."&.showprivate=".$this->showPrivate.
                                "&.FPaths=' + fp + '&.FOprs=' + op + '&.FVals=' + val + filterstring +'";

                ?>

                window.open(
                '<?php echo $printUrl ?>','csv',
                'width=525,height=125,scrollbars=no,dependent,left=317,top=300');
                if (PAGE_LAYOUT_TYPE == "Q") {
                    jq("#deliverExportAcctGrpSelect").modal('hide');
                } else {
                    showDeliverExportAcctGrpPage(false);
                }
            }
            //-->
        </SCRIPT>
        <?php
    }

    /**
     * Flip URL
     *
     * @return string URL
     */
    function calcFlipUrl()
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $p = &$this->_params; //???
        /** @noinspection PhpUnusedLocalVariableInspection */
        $t = &$this->table;

        $text = I18N::getSingleToken("IA.FLIPVIEW");
        $dst = 'lister.phtml';
        $tip = I18N::getSingleToken("IA.FLIP_VIEW");
        $do = 'edit';
        $op = GetOperationId('gl/lists/glacctgrpmap');

        $ret = "<a href=\"" .
            $this->U($dst, ".do=$do&.r=&.op=$op&.type=U", $this->LCALL) . "\" >" .
            $text .
            "</a>";

        return $ret;
    }

    /**
     * Import URL
     *
     * @return string URL
     */
    function calcImportAcctGrpUrl()
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $p = &$this->_params; //???
        /** @noinspection PhpUnusedLocalVariableInspection */
        $t = &$this->table;

        $text =  I18N::getSingleToken("IA.IMPORT");
        $tip = 'ImportAcctGrp';

        if ($this->isQuixote) {
            $ret = "<a href=\"#deliverImportAcctGrpSelect\" data-toggle=\"modal\" >" .
                $text .
                "</a>";
        } else {
            $ret = "<a id=\"exportgroups\" href=\"javascript:showDeliverImportAcctGrpPage(true)\" >" .
                $text .
                "</a>";
        }

        return $ret;
    }

    /**
     * Export URL
     *
     * @return string URL
     */
    function calcExportAcctGrpUrl()
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $p = &$this->_params; //???
        /** @noinspection PhpUnusedLocalVariableInspection */
        $t = &$this->table;

        $text = I18N::getSingleToken("IA.EXPORT");
        $tip = 'ExportAcctGrp';

        if ($this->isQuixote) {
            $ret = "<a href=\"#deliverExportAcctGrpSelect\" data-toggle=\"modal\" >" .
                $text .
                "</a>";
        } else {
            $ret = "<a id=\"exportgroups\" href=\"javascript:showDeliverExportAcctGrpPage(true)\" >" .
                $text .
                "</a>";
        }
        return $ret;
    }

    /**
     * Popup
     *
     * @return string popup div
     */
    function CalcHeadIsland()
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $divTag = '';
        if (!$this->isQuixote) {
            $divTag = '
			<div id="page_screen0">&nbsp;</div>
			<div id="deliverImportAcctGrpSelect" class="deliverselect" 
                onkeydown="if(event.keyCode==27){ showDeliverImportAcctGrpPage(false); }">
			<table border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td>
						<table border=0 cellpadding=4 cellspacing=0 width="200px" class="deliverselect_header">
							<tr>
								<td><b>'.I18N::getSingleToken( 'IA.PLEASE_SELECT_AN_OPTION').'</b></td>
								<td tabindex=-1 style="cursor:pointer;"	onClick="showDeliverImportAcctGrpPage(false);">
									<img src="' . IALayoutManager::getCSSButtonPath("close_button.png") . '">
								</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td valign=middle align=center>
						<table border="1" cellpadding="1" cellspacing="0" width="200" class="field_list_data_deliver" >
							<tr><td valign="middle">
							<table border="0" cellpadding="4" cellspacing="0" width="100%">
								<tr>
									<td valign="middle" style="padding-left:25px">
										<a href=#skip onclick="javascript:setDeliverImportAcctGrp(\'G\');">
                                        <B>'.I18N::getSingleToken( 'IA.ACCOUNT_GROUPS').'</B></a> ('.I18N::getSingleToken( 'IA.IMPORT').')
									</td>
								</tr>
							</table>
						</td></tr></table>
						<table border="0" cellpadding="1" cellspacing="0" width="200" class="field_list_data_deliver" >
							<tr><td valign="middle">
							<table border="0" cellpadding="4" cellspacing="0" width="100%">
								<tr>
									<td valign="middle" style="padding-left:25px">
										<a href=#skip onclick="javascript:setDeliverImportAcctGrp(\'M\');">
                                        <B>'.I18N::getSingleToken( 'IA.GROUP_MEMBERS').'</B></a> ('.I18N::getSingleToken( 'IA.IMPORT').')
									</td>
								</tr>
							</table>
						</td></tr></table>
					</td>
				</tr>
			</table>
			</div>
			<div id="deliverExportAcctGrpSelect" class="deliverselect" 
                onkeydown="if(event.keyCode==27){ showDeliverExportAcctGrpPage(false); }">
			<table border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td>
						<table border=0 cellpadding=4 cellspacing=0 width="200px" class="deliverselect_header">
							<tr>
								<td><b>'.I18N::getSingleToken( 'IA.PLEASE_SELECT_AN_OPTION').'</b></td>
								<td tabindex=-1 style="cursor:pointer;"	onClick="showDeliverExportAcctGrpPage(false);">
									<img src="' . IALayoutManager::getCSSButtonPath("close_button.png") . '">
								</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td valign=middle align=center>
						<table border="1" cellpadding="1" cellspacing="0" width="200" class="field_list_data_deliver" >
							<tr><td valign="middle">
							<table border="0" cellpadding="4" cellspacing="0" width="100%">
								<tr>
									<td valign="middle" style="padding-left:25px">
										<a href=#skip onclick="javascript:setDeliverExportAcctGrp(\'G\');">
                                        <B>'.I18N::getSingleToken( 'IA.ACCOUNT_GROUPS').'</B></a> ('.I18N::getSingleToken( 'IA.EXPORT').')
									</td>
								</tr>
							</table>
						</td></tr></table>
						<table border="0" cellpadding="1" cellspacing="0" width="200" class="field_list_data_deliver" >
							<tr><td valign="middle">
							<table border="0" cellpadding="4" cellspacing="0" width="100%">
								<tr>
									<td valign="middle" style="padding-left:25px">
										<a href=#skip onclick="javascript:setDeliverExportAcctGrp(\'M\');">
                                        <B>'.I18N::getSingleToken( 'IA.GROUP_MEMBERS').'</B></a> ('.I18N::getSingleToken( 'IA.EXPORT').')
									</td>
								</tr>
							</table>
						</td></tr></table>
					</td>
				</tr>
			</table>
			</div>
		';
        } else {
            $divTag =
                '<div class="quixote-modal modal" id="deliverImportAcctGrpSelect" tabindex="-1" role="dialog">
                <div class="modal-dialog center" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">'.I18N::getSingleToken( 'IA.PLEASE_SELECT_AN_OPTION').'</h4>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="text-center">
                                <div class="form-group">
                                    <a href=#skip onclick="javascript:setDeliverImportAcctGrp(\'G\');">'.I18N::getSingleToken( 'IA.ACCOUNT_GROUPS').'</a> ('.I18N::getSingleToken( 'IA.IMPORT').')
                                </div>
                                <div class="form-group">
                                    <a href=#skip onclick="javascript:setDeliverImportAcctGrp(\'M\');">'.I18N::getSingleToken( 'IA.GROUP_MEMBERS').'</a> ('.I18N::getSingleToken( 'IA.IMPORT').')
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>';
            $divTag .=
                '<div class="quixote-modal modal" id="deliverExportAcctGrpSelect" tabindex="-1" role="dialog">
                <div class="modal-dialog center" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">'.I18N::getSingleToken( 'IA.PLEASE_SELECT_AN_OPTION').'</h4>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="text-center">
                                <div class="form-group">
                                    <a href=#skip onclick="javascript:setDeliverExportAcctGrp(\'G\');">'.I18N::getSingleToken( 'IA.ACCOUNT_GROUPS').'</a> ('.I18N::getSingleToken( 'IA.EXPORT').')
                                </div>
                                <div class="form-group">
                                    <a href=#skip onclick="javascript:setDeliverExportAcctGrp(\'M\');">'.I18N::getSingleToken( 'IA.GROUP_MEMBERS').'</a> ('.I18N::getSingleToken( 'IA.EXPORT').')
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>';
        }

        return $divTag;
    }

    /**
     * Account group export
     *
     * @param string $type Member type
     * @param array $records Record list
     *
     * @return array|false Array of account group
     */
    function GetAcctGrpExport($type, $records)
    {
        $arrReturn = array();

        $gManagerFactory = Globals::$g->gManagerFactory;
        $glAcctGrpMgr = $gManagerFactory->getManager('glacctgrp');

        // get the Account Group Map by type ('G' or 'M')
        $arrAcctGrpMap = $glAcctGrpMgr->BuildAcctGrpDetails($type, $records);

        if ( !$arrAcctGrpMap ) {
            return false;
        }

        if ( $type == 'G' ) {
            $dimFields = $this->dimFields;
            $allDims = IADimensions::getAllDimensionObjectProperties();
            
            foreach ( $arrAcctGrpMap as $k => $v ) {
                $group = $v;

                unset($group['RECORD#']);
                unset($group['CALC_AMOUNT']);
                unset($group['APPLY_BUDGET']);
                unset($group['MEMBERS']);
                unset($group['RANGES']);
                unset($group['CATEGORY']);
                unset($group['COMPUTATION']);

                foreach ($this->dimHeader as $dimKey) {
                    if ( !$dimFields[$dimKey] ) {
                        $unsetGroupDimKey = "F_" . strtoupper($allDims[$dimKey]['internalpath'] ?? $dimKey);
                        unset($group[$unsetGroupDimKey. "_TYPE"]);
                        unset($group[$unsetGroupDimKey]);
                    }
                }
               
                $arrReturn[$k] = $group;
            }
        } else if ( $type == 'M' ) {
            foreach ( $arrAcctGrpMap as $k => $v ) {
                $group = $v;

                if ( $group['MEMBERS'] ) {
                    $arrMember = $group['MEMBERS'];
                    for ( $i = 0; $i < count($arrMember); $i++ ) {
                        $arrMember[$i] = '""' . $arrMember[$i] . '""';
                    }
                    $group['MEMBERS'] = implode(',', $arrMember);
                }

                if ( $group['RANGES'] ) {
                    $arrRange = $group['RANGES'];
                    for ( $i = 0; $i < count($arrRange); $i++ ) {
                        $arrRange[$i] = '""' . $arrRange[$i] . '""';
                    }
                    $group['MEMBERS'] = implode(',', $arrRange);
                }

                if ( $group['CATEGORY'] ) {
                    $arrCategory = $group['CATEGORY'];
                    for ( $i = 0; $i < count($arrCategory); $i++ ) {
                        $arrCategory[$i] = '""' . $arrCategory[$i] . '""';
                    }
                    $group['CATEGORY'] = implode(',', $arrCategory);
                }

                if ( $group['COMPUTATION'] ) {
                    $arrComputation = $group['COMPUTATION'];
                    unset($arrComputation['COMP_RECORD#']);

                    foreach ( $arrComputation as $kComp => $vComp ) {
                        $group[$kComp] = $vComp;
                    }
                }

                unset($group['RECORD#']);
                unset($group['HEADING_TITLE']);
                unset($group['TOTAL_TITLE']);
                unset($group['ACCOUNT_GROUP_PURPOSE']);
                unset($group['ACCOUNT_GROUP_MANAGER']);
                unset($group['NORMAL_BALANCE']);
                unset($group['MEMBER_TYPE']);
                unset($group['RANGES']);
                unset($group['COMPUTATION']);
                unset($group['FILTER_DBCR']);
                unset($group['F_DEPARTMENT_TYPE']);
                unset($group['F_DEPARTMENT']);
                unset($group['F_LOCATION_TYPE']);
                unset($group['F_LOCATION']);
                unset($group['F_VENDOR_TYPE']);
                unset($group['F_VENDOR']);
                unset($group['F_CUSTOMER_TYPE']);
                unset($group['F_CUSTOMER']);
                unset($group['F_PROJECT_TYPE']);
                unset($group['F_PROJECT']);
                unset($group['F_EMPLOYEE_TYPE']);
                unset($group['F_EMPLOYEE']);
                unset($group['F_ITEM_TYPE']);
                unset($group['F_ITEM']);
                unset($group['F_CLASS_TYPE']);
                unset($group['F_CLASS']);
                unset($group['F_CONTRACT_TYPE']);
                unset($group['F_CONTRACT']);
                unset($group['F_TASK_TYPE']);
                unset($group['F_TASK']);
                unset($group['F_WAREHOUSE_TYPE']);
                unset($group['F_WAREHOUSE']);
                unset($group['F_COSTTYPE_TYPE']);
                unset($group['F_COSTTYPE']);

                $arrReturn[$k] = $group;
            }
        }

        return $arrReturn;
    }

    /**
     * Head string
     *
     * @param array $header header
     * 
     * @return string formatted string
     */
    function GetHeaderString($header)
    {
        // TODO: join() wasn't good enough?
        $strHeader = "";
        for ( $i = 0; $i < count($header); $i++ ) {
            if ( $i == 0 ) {
                $strHeader .= $header[$i];
            } else {
                $strHeader .= "\",\"" . $header[$i];
            }
        }
        return $strHeader;
    }

    /**
     *  Used to construct base url parms
     *
     * @param int   $i    row index
     * @param array $vals input params of the format:
     *                      $val['dst'] => destination script
     *                      $val['urlargs'] => array of key=>value parms to be emitted in the 'urlargstring' output
     *                      $val['text'] => href text that is to be displayed
     *                      $val['tip'] => mousover tip
     *
     * @return array the output arguments to be used to generate an href of the format:
     *             $dst['dst'] => Destination script
     *             $args['urlstring'] => string in the form "key1=value1&key2=value2&key3=value3...."
     *                                   where key,value[1..n] come from $val['urlargs'] array
     *             $args['text'] => text of the href
     *             $args['tip'] => mouseover tip
     */
    function _calcUrlParms( $i, $vals )
    {
        $vals['urlargs']['opSubAction'] = $this->opSubAction;
        $vals['urlargs']['dimGroupTypeRestriction'] =  isl_htmlspecialchars($this->opSubActionDimFilter);
        return parent::_calcUrlParms($i, $vals);
    }

    /**
     * Print export
     * 
     * @param string $type Member type
     * @param array $records Record list
     *
     * @return bool
     */
    function PrintAcctGrpExport($type, $records)
    {
        $hdrString = "\"";
        $detailString = "";
        /** @noinspection PhpUnusedLocalVariableInspection */
        $masterString = "";

        if ( !in_array($type, array('G', 'M')) ) {
            $filename = 'AcctGrpExport_WRONG_TYPE.csv';
            header("Content-Disposition: attachment; filename=\"$filename\"");
            header("Content-type: application/vnd.ms-excel");
            echo "Wrong Type of Groups";
            return true;
        }

        /** @noinspection PhpUnusedLocalVariableInspection */
        $companyId = GetMyCompany();
        $filename = 'AcctGrpExport_' . $type . '.csv';
        header("Content-Disposition: attachment; filename=\"$filename\"");
        header("Content-type: application/vnd.ms-excel");

        /** @noinspection PhpUnusedLocalVariableInspection */
        $csvHeader = array();
        switch ( $type ) {
        case 'G':
            $csvHeader = array(
                'GROUP_NAME', 'HEADING_TITLE', 'TOTAL_TITLE','ACCOUNT_GROUP_PURPOSE','ACCOUNT_GROUP_MANAGER', 'NORMAL_BALANCE', 'MEMBER_TYPE',
                'FILTER_DBCR', 'F_DEPARTMENT_TYPE', 'F_DEPARTMENT', 'F_LOCATION_TYPE', 'F_LOCATION'
            );

            $dimFields = $this->dimFields;
            $dimTemp = [];
            foreach ($this->dimHeader as $dimKey) {
                if($dimFields[$dimKey]){
                    $dimArr = "F_" . strtoupper($dimFields[$dimKey]['internalpath'] ?? $dimKey);
                    $dimTemp[] = $dimArr;
                    $csvHeader[] = $dimArr . "_TYPE" ;
                }
            }
           
            $csvHeader = array_merge($csvHeader, $dimTemp);
            
            break;
        case 'M':
            $csvHeader = array(
                'GROUP_NAME', 'CALC_AMOUNT', 'APPLY_BUDGET', 'MEMBERS', 'CATEGORY',
                'LEFT_OPERAND_CONSTANT', 'LEFT_OPERAND_ACCOUNT_GROUP', 'LEFT_OPERAND_ACCOUNT',
                'LEFT_OPERAND_CALC_AMT', 'OPERATOR', 'RIGHT_OPERAND_CONSTANT', 'RIGHT_OPERAND_ACCOUNT_GROUP',
                'RIGHT_OPERAND_ACCOUNT', 'RIGHT_OPERAND_CALC_AMT',
                'PRECISION', 'DISPLAY_AS', 'UNIT_OF_MEASURE', 'ALIGNMENT'
            );
            break;
        default:
            return false;
        }

        $hdrString .= $this->GetHeaderString($csvHeader);
        $hdrString .= "\"\n\"";

        $acctRecs = $this->GetAcctGrpExport($type, $records);

        if ( !$acctRecs ) {
            $detailString .= 'No Data';
        } else {
            foreach ( $acctRecs as $row ) {
                foreach ( $row as $column ) {
                    $detailString .= escapeChars($column) . "\",\"";
                }
                $detailString .= "\"\n\"";
            }
        }

        $masterString = $hdrString . $detailString;
        echo $masterString;
        return true;
    }

    /**
     * @param string $subAction
     *
     * @return string
     */
    function calcIAAcctGrp($subAction)
    {
        if ($subAction === 'account') {
            $text = I18N::getSingleToken("IA.ACCOUNT_GROUPS_LIBRARY");
            $tip = I18N::getSingleToken("IA.SHOW_ACCOUNT_GROUPS_LIBRARY"); 
        } else {
            
            $text = I18N::getSingleToken("IA." .strtoupper($this->dimEntity)."_REPORT_STRUCTURES_LIBRARY");
            $tip = I18N::getSingleToken("IA." .strtoupper($this->dimEntity)."_SHOW_LIBRARY");
            
        }

        $dst = 'lister.phtml';
        $do = 'edit';
        $op = GetOperationId('gl/lists/iaglacctgrplibrary');

        $ret = "<a href=\"" .
            $this->U(
                $dst,
                ".do=$do&.r=&.op=$op&.type=U&.title=$text&.dimGroupTypeRestriction="
                . isl_htmlspecialchars($this->opSubActionDimFilter) . "&.opSubAction=$this->opSubAction",
                $this->LCALL
            ) . "\" >" .
            $text .
            "</a>";

        return $ret;
    }

    /**
     * @param string $this->subAction
     * @return bool
     * Added for IA-229147 Account group NextGen lister issue in dimension structure
     * need to modify/remove while creating ng-list for Dimensions
     */
    public function hasXGList():bool{
        if ( $this->opSubAction == 'dimension' ) {
            return false;
        }
        return parent::hasXGList();
    }
}


