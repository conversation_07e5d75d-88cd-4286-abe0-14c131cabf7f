<?xml version="1.0" encoding='UTF-8'?>
<ROOT assoc="T">
	<entity>graphs</entity>
	<title>IA.GENERAL_LEDGER_GRAPH_WIZARD</title>
	<helpfile></helpfile>
	<pages>
		<page assoc="T">
			<helpfile>Creating_and_Editing_Graphs</helpfile>
			<title>IA.CHOOSE_YOUR_GRAPH_DISPLAY</title>
			<path>GRAPHTYPE</path>
			<instruction>Choose your Graph Display</instruction>
			<fields>
				<field assoc="T">
					<path>RECORDNO</path>
					<hidden>1</hidden>
				</field>
				<field>RE_TYPE</field>
				<SinglelineLayout assoc="T"  key="hbox" >
					<fullname>IA.REPORTING_BOOK</fullname>
					<hidden>1</hidden>
					<columns>
						<column assoc="T" >
							<path>REPORTINGBOOK</path>
						</column>
						<column assoc="T" >
							<path>PROMPTBOOK</path>
						</column>
					</columns>
					<_func>SinglelineLayout</_func>
				</SinglelineLayout>
                <field>ADJUSTMENTBOOK</field>
                <fiel>INCLUDEREPBOOK</fiel>
				<field assoc="T">
					<path>DUMMYSPACE1</path>
				</field>
				<field>PRIMARYSERIES</field>
				<field>SECONDARYSERIES</field>
			</fields>
		</page>
		<page assoc="T">
			<helpfile>Creating_and_Editing_Graphs_Step2</helpfile>
			<title>IA.SELECT_YOUR_GRAPH_DATA</title>
			<path>GRAPHDATA</path>
			<instruction>Select your Graph Data</instruction>
			<fields>
				<field>GROUPS</field>
				<SinglelineLayout assoc="T"  key="hbox" >
					<fullname>IA.REPORTING_PERIODS</fullname>
					<required>true</required>
					<columns>
						<vbox assoc="T"  key="column" >
							<_func>vbox</_func>
							<_args>
							<_arg assoc="T" >
								<path>PERIODS</path>
								<valign>top</valign>
								<rowspan>2</rowspan>
							</_arg>
							<_arg assoc="T" >
								<noTD>true</noTD>
							</_arg>
                            <_arg assoc="T" >
								<path>REPORTINGPERIODHELP</path>
                                <valign>top</valign>
                                <rowspan>2</rowspan>
							</_arg>
							</_args>
						</vbox>
						<vbox assoc="T"  key="column" >
							<_func>vbox</_func>
							<_args>
							<_arg assoc="T" >
								<path>TRENDINGTITLE</path>	
								<valign>top</valign>
								<align>left</align>
							</_arg>
                            <_arg assoc="T" >
								<path>TRENDING</path>	
								<valign>top</valign>
								<align>right</align>
							</_arg>
                                <_arg assoc="T" >
								<path>PERIODOFFSETBY</path>	
								<valign>top</valign>
								<align>right</align>
							</_arg>
                            <_arg assoc="T" >
								<noTD>true</noTD>
							</_arg>    
							<_arg assoc="T" >
								<path>EXPANDBY</path>
								<valign>top</valign>
								<align>right</align>
							</_arg>
							<_arg assoc="T" >
								<path>NOPERIODHEADER</path>
								<valign>top</valign>
								<align>right</align>
							</_arg>
							</_args>
						</vbox>	
						<vbox assoc="T"  key="column" >
							<_func>vbox</_func>
							<_args>
                            <_arg assoc="T" >
								<path>DUMMYSPACE3</path>						
								<valign>top</valign>
							</_arg>
							<_arg assoc="T" >
								<path>TRENDINGHELP</path>						
								<valign>top</valign>
							</_arg>
                            <_arg assoc="T" >
								<path>PERIODOFFSETBYHELP</path>						
								<valign>top</valign>
							</_arg>
                            <_arg assoc="T" >
								<path>LINEHELP</path>
								<valign>top</valign>
							</_arg>      
							<_arg assoc="T" >
								<path>EXPANDBYHELP</path>
								<valign>top</valign>
							</_arg>
							<_arg assoc="T" >
								<path>NOPERIODHEADERHELP</path>
								<valign>top</valign>
							</_arg>
							</_args>
						</vbox>	
					</columns>
					<_func>SinglelineLayout</_func>
				</SinglelineLayout>
				<field>BUDGETS</field>
				<field assoc="T">
					<path>DUMMYSPACE2</path>
				</field>
				<SinglelineLayout assoc="T"  key="hbox" >
					<fullname>IA.KEYKEY_DEPARTMENT</fullname>
					<renameable>true</renameable>
					<columns>
						<column assoc="T" >
							<path>DEPARTMENT</path>
						</column>
						<column assoc="T" >
							<path>DEPT_IR</path>
						</column>
						<column assoc="T" >
							<path>PROMPTDEPARTMENT</path>
						</column>
						<column assoc="T" >
							<path>EXCLSUBS_FOR_DEPARTMENT</path>
						</column>
					</columns>
					<_func>SinglelineLayout</_func>
				</SinglelineLayout>
				<SinglelineLayout assoc="T"  key="hbox" >
					<fullname>IA.KEYKEY_LOCATION</fullname>
					<renameable>true</renameable>
					<columns>
						<column assoc="T" >
							<path>LOCATION</path>
						</column>
						<column assoc="T" >
							<path>LOCATION_IR</path>
						</column>
						<column assoc="T" >
							<path>PROMPTLOCATION</path>
						</column>
						<column assoc="T" >
							<path>EXCLSUBS_FOR_LOCATION</path>
						</column>
					</columns>
					<_func>SinglelineLayout</_func>
				</SinglelineLayout>
			</fields>
		</page>

		<page assoc="T">
			<helpfile>Creating_and_Editing_Graphs_Step3</helpfile>
			<title>IA.SELECT_YOUR_GRAPH_PREFERENCES</title>
			<path>GRAPHPREFERENCE</path>
			<instruction>Select your Graph Preferences</instruction>
			<fields>
				<field>NAME</field>
				<field>TITLE</field>
				<field>TITLE2</field>
				<field>TITLECOMMENT</field>
				
				<SinglelineLayout assoc="T"  key="hbox" >
					<fullname>IA.AS_OF_DATE</fullname>
					<columns>
						<column assoc="T" >
							<path>ASOFDATE</path>
						</column>
						<column assoc="T" >
							<path>PROMPTDATE</path>
						</column>
					</columns>
					<_func>SinglelineLayout</_func>
				</SinglelineLayout>
				<field>ROUNDING</field>
                <SinglelineLayout assoc="T"  key="hbox" >
                    <fullname>IA.HIDE_ZERO_NUMBERS</fullname>
                    <columns>
                        <column assoc="T" >
                            <path>HIDEZEROBALCOL</path>
                        </column>
                        <column assoc="T" >
                            <path>HIDEZEROBALCOLHELP</path>
                        </column>
                    </columns>
                    <_func>SinglelineLayout</_func>
                </SinglelineLayout>
				<field>STATUS</field>
			</fields>
		</page>
	</pages>
</ROOT>