<?php
/**
 * Entity definition for gl account balance
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

$kSchemas['glaccountbalance'] = array(
    'children' => array(
        'location' => array(
            'fkey' => 'location#', 'invfkey' => 'record#',
            'table' => 'location', 'join' => 'outer'
        ),
        'glaccount' => array(
            'fkey' => 'account#', 'invfkey' => 'record#',
            'table' => 'baseaccount', 'join' => 'outer'
        ),
        'department' => array(
            'fkey' => 'dept#', 'invfkey' => 'record#',
            'table' => 'department', 'join' => 'outer'
        ),
    ),
    'object' => array(
        'RECORDNO',
        'BOOKID', 'CURRENCY', 'PERIOD',
        'OPENBAL', 'TOTDEBIT', 'TOTCREDIT', 'TOTADJDEBIT', 'TOTADJCREDIT', 'FOR<PERSON>L', 'ENDBAL',
        'ACCOUNTREC', 'ACCOUNTNO', 'ACCOUNTTITLE',
        'DEPARTMENTDIMKEY', 'DEPARTMENTID', 'DEPARTMENTTITLE',
        'LOCATIONDIMKEY', 'LOCATIONID', 'LOCATIONNAME',
        'ENDBAL', 'WHENCREATED', 'WHENMODIFIED', 'CREATEDBY', 'MODIFIEDBY','LOCATIONNO'
    ),
    'publish' => array(
        'BOOKID', 'CURRENCY',
        'OPENBAL', 'TOTDEBIT', 'TOTCREDIT', 'TOTADJDEBIT', 'TOTADJCREDIT', 'FORBAL', 'ENDBAL',
        'ACCOUNTREC', 'ACCOUNTNO', 'ACCOUNTTITLE',
        'DEPARTMENTDIMKEY', 'DEPARTMENTID', 'DEPARTMENTTITLE',
        'LOCATIONDIMKEY', 'LOCATIONID', 'LOCATIONNAME',
    ),
    'schema' => array(
        'RECORDNO' => 'record#',
        'BOOKID' => 'bookid',
        'CURRENCY' => 'currency',
        'OPENBAL' => 'openbal',
        'TOTDEBIT' => 'totdebit',
        'TOTCREDIT' => 'totcredit',
        'TOTADJDEBIT' => 'totadjdebit',
        'TOTADJCREDIT' => 'totadjcredit',
        'FORBAL' => 'forbal',
        'ENDBAL' => 'endbal',
        'ACCOUNTREC' => 'account#',
        'ACCOUNTNO' => 'glaccount.acct_no',
        'ACCOUNTTITLE' => 'glaccount.title',
        'LOCATIONDIMKEY' => 'location#',
        'LOCATIONID' => 'location.location_no',
        'LOCATIONNAME' => 'location.name',
        'DEPARTMENTDIMKEY' => 'dept#',
        'DEPARTMENTID' => 'department.dept_no',
        'DEPARTMENTTITLE' => 'department.title',
        'GLACCOUNT' => array('glaccount.*' => 'glaccount.*'),
        'DEPARTMENT' => array('department.*' => 'department.*'),
        'LOCATION' => array('location.*' => 'location.*'),
        'WHENCREATED' => 'whencreated',
        'WHENMODIFIED' => 'whenmodified',
        'CREATEDBY' => 'createdby',
        'MODIFIEDBY' => 'modifiedby',
        'LOCATIONNO' => 'location#',
    ),
    'nexus' => array(
        'glaccount' => array('object' => 'glaccount', 'relation' => ONE2MANY, 'field' => 'account#'),
        'department' => array('object' => 'department', 'relation' => ONE2MANY, 'field' => 'dept#'),
        'location' => array('object' => 'location', 'relation' => ONE2MANY, 'field' => 'location#'),
    ),
    'fieldinfo' => array(
        array(
            'id' => 1, 'path' => 'ACCOUNTNO', 'fullname' => 'IA.ACCOUNT_NO',
            'type' => array('ptype' => 'ptr', 'type' => 'text', 'entity' => 'baseaccountpick'),
        ),
        array(
            'id' => 2, 'path' => 'ACCOUNTTITLE', 'fullname' => 'IA.ACCOUNT_TITLE',
            'type' => array('ptype' => 'text', 'type' => 'text'),
        ),
        array(
            'id' => 3, 'path' => 'DEPARTMENTID', 'fullname' => 'IA.DEPARTMENT_ID',
            'type' => array('ptype' => 'ptr', 'type' => 'text', 'entity' => 'departmentpick'),
        ),
        array(
            'id' => 4, 'path' => 'LOCATIONID', 'fullname' => 'IA.LOCATION_ID',
            'type' => array('ptype' => 'ptr', 'type' => 'text', 'entity' => 'locationpick'),
        ),
        array(
            'id' => 5, 'path' => 'BOOKID', 'fullname' => 'IA.REPORTING_METHOD',
            'type' => array('ptype' => 'ptr', 'type' => 'text', 'entity' => 'glbook', 'maxlength' => 20 )
        ),
        array(
            'id' => 6, 'path' => 'CURRENCY', 'fullname' => 'IA.CURRENCY',
            'type' => array('ptype' => 'text', 'type' => 'text', 'maxlength' => 12 )
        ),
        array(
            'id' => 7, 'path' => 'PERIOD', 'fullname' => 'IA.PERIOD',
            'type' => array( 'type' => 'text' )
        ),
        array(
            'id' => 8, 'path' => 'OPENBAL', 'fullname' => 'IA.OPENING_BALANCE',
            'type' => array('ptype' => 'decimal', 'type' => 'decimal')
        ),
        array(
            'id' => 9, 'path' => 'TOTDEBIT', 'fullname' => 'IA.TOTAL_DEBIT',
            'type' => array('ptype' => 'decimal', 'type' => 'decimal')
        ),
        array(
            'id' => 10, 'path' => 'TOTCREDIT', 'fullname' => 'IA.TOTAL_CREDIT',
            'type' => array('ptype' => 'decimal', 'type' => 'decimal')
        ),
        array(
            'id' => 11, 'path' => 'TOTADJDEBIT', 'fullname' => 'IA.TOTAL_ADJUSTMENT_DEBIT',
            'type' => array('ptype' => 'decimal', 'type' => 'decimal')
        ),
        array(
            'id' => 12, 'path' => 'TOTADJCREDIT', 'fullname' => 'IA.TOTAL_ADJUSTMENT_CREDIT',
            'type' => array('ptype' => 'decimal', 'type' => 'decimal')
        ),
        array(
            'id' => 13, 'path' => 'FORBAL', 'fullname' => 'IA.FOR_THE_PERIOD_BALANCE',
            'type' => array('ptype' => 'decimal', 'type' => 'decimal')
        ),
        array(
            'id' => 14, 'path' => 'ENDBAL', 'fullname' => 'IA.ENDING_BALANCE',
            'type' => array('ptype' => 'decimal', 'type' => 'decimal')
        ),
        array(
            'fullname'     => 'IA.GL_ACCOUNT_RECORD_NUMBER',
            'required'     => false,
            'hidden' => true,
            'type' => array (
                'type' => 'integer',
                'ptype' => 'sequence',
                'maxlength' => 8,
                'size' => 8,
                'format' => $gRecordNoFormat,
            ),
            'path'        => 'ACCOUNTREC',
            'id' => 15
        ),
        array(
            'fullname' => 'IA.RECORD_NO',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
            'path' => 'RECORDNO',
            'id' => 16,
        ),
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
        array(
            'fullname' => 'IA.LOCATION_NO',
            'type' => array(
                'type' => 'integer',
            ),
            'path' => 'LOCATIONNO',
            'id' => 17,
        ),
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENCREATED',
        'WHENMODIFIED',
    ),
    'table' => 'glaccountbalance',
    'printas' => 'IA.GL_ACCOUNT_BALANCE',
    'pluralprintas' => 'IA.GL_ACCOUNT_BALANCES',
    'vid' => 'RECORDNO',
    'module' => 'gl',
    'nosysview' => true,
    'auditcolumns' => true,
    'hasdimensions' => true,
    'sortbyrecordno' => true,
    'api' => array(
        'PERMISSION_MODULES' => array('gl'),
        'PERMISSION_READ' => 'lists/glbatch/view'
    ),
    'allowDDS' => true,
    'description' => 'IA.ACCOUNT_BALANCES_FOR_GL_AND_STATISTICAL_ACCOUNTS',

);

