<?php
/**
 * Utility class to build GL header and entry details from the template.
 *
 * <AUTHOR>
 * @copyright 2021 Sage Intacct Inc., All Rights Reserved
 */
class JournalEntryBuilder
{
    /** @var array $glbatchcustomFields */
    private $glbatchcustomFields;
    /** @var array $headercustomFields */
    private $headercustomFields;
    /** @var array $glentrycustomFields */
    private $glentrycustomFields;
    /** @var array $entrycustomFields */
    private $entrycustomFields;
    /** @var string $entryEntity */
    private $entryEntity;
    /**
     * @param string    $headerEntity
     * @param string    $entryEntity
     */
    public function __construct($headerEntity, $entryEntity)
    {
        // Populate the custom fields
        $this->glbatchcustomFields = $this->GetCustomFields('glbatch');
        $this->headercustomFields = $this->GetCustomFields($headerEntity);
        $this->glentrycustomFields = $this->GetCustomFields('glentry');
        $this->entrycustomFields = $this->GetCustomFields($entryEntity);
        $this->entryEntity = $entryEntity;
    }

    /**
     * Build the data for the GL transaction to post
     *
     * @param string  $createAsOfDate the creation date
     * @param array   $template       the recurring template data
     * @param bool $reversal       true if reversal else false
     *
     * @return array the GL Batch Data
     */
    public function buildGLBatchValues($createAsOfDate, &$template, $reversal)
    {
        $values = array();
        $this->buildGLBatchHeader($createAsOfDate, $template, $values, $reversal);
        $this->buildGLBatchDetail($template, $values, $reversal);
        return $values;
    }

    /**
     * Build the header data of the GL transaction
     *
     * @param string  $createAsOfDate the creation date
     * @param array   $template       the recurring template data
     * @param array   &$values        the GL transation data
     * @param bool $reversal       true if reversal else false
     */
    private function buildGLBatchHeader($createAsOfDate, $template, &$values, $reversal)
    {
        // set reversal based on AUTOREPEATBY, AUTOREPEATINTERVAL
        if ( isset($template['AUTOREPEATBY']) && $template['AUTOREPEATBY'] != '' ) {
            $values['REVERSEDATE'] = $this->calculateReverseDate($createAsOfDate, $template);
        }

        // Copy the values
        $values['BATCH_DATE'] = $createAsOfDate;
        $values['BATCH_TITLE'] = $template['DESCRIPTION'];

        // Transform other values for the txn from the bank rule
        if (isset($template['TRANSACTIONSOURCE']) && $template['TRANSACTIONSOURCE'] == 'BANK') {
            $values['STATE'] = $template['STATE'];
            $values['TRANSACTIONSOURCE'] = $template['TRANSACTIONSOURCE'];
            $values['REQUESTNO'] = $template['REQUESTNO'];
        }

        if ( isset($template['REVERSEDATE']) && $template['REVERSEDATE'] != '' ) {
            $values['REVERSEDATE']    = $template['REVERSEDATE'];
        }

        $values['SUPDOCID'] = $template['SUPDOCID'];
        $values['REFERENCENO'] = ($reversal ? 'Reversed:' : '') . $template['REFERENCENO'];
        $values['REFERENCENO'] = isl_substr($values['REFERENCENO'], 0, 39);
        $values['JOURNAL'] = $template['JOURNAL'];
        $values['MODULE'] = $template['MODULE'];
        $values['SCHOPKEY'] = ($reversal == 0 ? $template['SCHOPKEY'] : $template['REVERSALSCHOPKEY']);

        // this value is set only if auto IET is turned ON in GL and if there any IET generated
        if ( isset($template['BASELOCATION_NO']) && $template['BASELOCATION_NO'] != '' ) {
            $values['BASELOCATION_NO'] = $template['BASELOCATION_NO'];
        }

        // Copy the custom fields
        foreach ( $this->headercustomFields as $desc => $id) {
            if (isset($this->glbatchcustomFields[$desc]) && $this->glbatchcustomFields[$desc] != '') {
                $values[strtoupper($this->glbatchcustomFields[$desc])] = $template[$desc];
            }
        }
    }

    /**
     * Build the entry data of the GL transaction
     *
     * @param array   $template the recurring template data
     * @param array   &$values  the GL transation data
     * @param bool $reversal true if reversal else false
     */
    private function buildGLBatchDetail($template, &$values, $reversal)
    {
        //impp("buildGLBatchDetail:".pp($template));

        $standardObjectMap = & Util_StandardObjectRegistry::getStandardObjectMap();
        $recurglentryObjDefId = $standardObjectMap[$this->entryEntity];
        $glentryObjDefId = $standardObjectMap['glentry'];

        $recurglRelDefs = Pt_RelationshipDefManager::getGLDimsByObjectDef($recurglentryObjDefId);
        $recurglRelFieldNames = array();
        foreach($recurglRelDefs as $relDef) {
            $recurglRelFieldNames[$relDef->getObjectDefId2()]  = $relDef->getRelationshipName();
        }
        //impp("recurglRelFieldNames:".pp($recurglRelFieldNames));

        $glentryRelDefs = Pt_RelationshipDefManager::getGLDimsByObjectDef($glentryObjDefId);
        $glentryRelDefIds = [];
        foreach($glentryRelDefs as $glentryRelDef) {
            $glentryRelDefIds[] = $glentryRelDef->getId();
        }
        //impp("glentryRelDefIds:".pp($glentryRelDefIds));

        $glentryObjDef = Pt_DataObjectDefManager::getById($glentryObjDefId);
        $fields = $glentryObjDef->getFields();
        $glentryRelFieldNames = array();
        foreach($fields as $field) {
            $props = $field->getProperties();
            if (isset($props['relId']) && in_array($props['relId'], $glentryRelDefIds)) {
                $relDef = Pt_RelationshipDefManager::getById($props['relId']);
                $glentryRelFieldNames[$relDef->getObjectDefId2()] = isl_strtoupper($field->getFieldName());
            }
        }
        //impp("glentryRelFieldNames:".pp($glentryRelFieldNames));

        foreach ( $template['ENTRIES'] as $recurglentry ) {

            // Copy the values
            $glEntry = array();
            $glEntry['DEPARTMENT'] = $recurglentry['DEPARTMENT'] ?? '';
            $glEntry['LOCATION'] = $recurglentry['LOCATION'] ?? '';
            $glEntry['ALLOCATION'] = $recurglentry['ALLOCATION'] ?? '';
            $glEntry['LINE_NO'] = $recurglentry['LINE_NO'];
            $glEntry['TR_TYPE'] = ($reversal == 0 ? $recurglentry['TR_TYPE'] : $recurglentry['TR_TYPE'] * -1);
            $glEntry['DOCUMENT'] = $recurglentry['DOCUMENT'];
            $glEntry['DESCRIPTION'] = $recurglentry['MEMO'];
            $glEntry['ACCOUNTNO'] = $recurglentry['ACCOUNTNO'];
            $glEntry['AMOUNT'] = $recurglentry['AMOUNT'];
            $glEntry['TRX_AMOUNT'] = $recurglentry['TRX_AMOUNT'];
            $glEntry['CURRENCY'] = $recurglentry['CURRENCY'];
            $glEntry['BASECURR'] = $recurglentry['BASECURR'];
            $glEntry['EXCH_RATE_DATE'] = $values['BATCH_DATE'];
            $glEntry['EXCH_RATE_TYPE_ID'] = $recurglentry['EXCH_RATE_TYPE_ID'];
            $glEntry['BILLABLE'] = $recurglentry['BILLABLE'];

            // Copy the dimensions
            IADimensions::CopyIADimensionFKIDs($recurglentry, $glEntry);

            // Copy the custom fields
            foreach ( $this->entrycustomFields as $desc => $id) {
                if (isset($this->glentrycustomFields[$desc]) && $this->glentrycustomFields[$desc] != '') {
                    $glEntry[strtoupper($this->glentrycustomFields[$desc])] = $recurglentry[$desc];
                }
            }

            // Copy custom dimensions
            foreach($recurglRelFieldNames as $objDefId => $fname) {
                $glEntry[$glentryRelFieldNames[$objDefId]] = $recurglentry[$fname];
            }

            $values['ENTRIES'][] = $glEntry;
        }
        impp("new values:".pp($values),null);
    }

    /**
     * Calculate the REVERSEDATE based on AUTOREPEATBY, AUTOREPEATINTERVAL
     *
     * @param string $createAsOfDate the creation date
     * @param array  $template       the recurring template data
     *
     * @return string next execution date for reversal
     */
    private function calculateReverseDate($createAsOfDate, $template)
    {
        // to collect auto reversal values
        $repeat = $template['AUTOREPEATBY'];
        $interval = $template['AUTOREPEATINTERVAL'];

        // Compute the next execution date for reversal
        $nextExecDate = $createAsOfDate;
        if ( $repeat == 'D' ) {
            $nextExecDate = AddDays($nextExecDate, $interval);
        } else if ( $repeat == 'W' ) {
            $nextExecDate = AddDays($nextExecDate, ibcmul($interval, '7'));
        } else if ( $repeat == 'M' ) {
            $nextExecDate = AddMonths($nextExecDate, $interval);
        } else if ( $repeat == 'E' ) {
            $nextExecDate = AddMonths($nextExecDate, $interval);
            $nextExecDate = GetLastDateOfMonth($nextExecDate);
        } else if ( $repeat == 'Y' ) {
            $nextExecDate = AddYears($nextExecDate, $interval);
        }

        return $nextExecDate;
    }

    /**
     * Get the list of custom field for a entity
     *
     * @param string $object the entity name
     *
     * @return array the list of custom field
     */
    private function GetCustomFields($object)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $objectMgr = $gManagerFactory->getManager($object);

        $customFields = array();
        foreach ( $objectMgr->customFields as $val ) {
            $customFields[$val->customFieldID] = $val->customFieldID;
        }
        return $customFields;
    }
}
