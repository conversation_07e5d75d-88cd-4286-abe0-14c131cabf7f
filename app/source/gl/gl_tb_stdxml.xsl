<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format">
<xsl:import href="../../private/xslinc/report_helpers.xsl"/>
	<xsl:output encoding="UTF-8"/>
<!--    <xsl:variable name="report_format" select="/reportdata/tbreport/@report_format" /> -->

	<xsl:template match="tbreport">
               <report showHeader="{@showHeader}" department="{@DEPTNAME}" location="{@LOCNAME}" origLocation="{@LOCNAME}" rptAcctSet="{@rptAcctSet}"
			rptAcctFooterText="{@rptAcctFooterText}" reportingBook="{@REPORTINGBOOK}" orientation="{@orientation}" report_date="{@reportdate}" report_time="{@reporttime}" align_currency="left" page_number="Y"
        action="reporteditor.phtml" sess="{@sess}" done="{@done}" wrap="Y" footer_allpages="Y" report_format="{$report_format}">
			<xsl:if test="(@orientation = 'Portrait')">
				<xsl:attribute name="maxfit">Y</xsl:attribute>
			</xsl:if>
			<company s="2">
				<xsl:value-of select="@CO"/>
			</company>
			<title s="3" titleNum="1">
				<xsl:value-of select="@TITLE"/>
			</title>
			<title s="3" titleNum="2">
				<xsl:value-of select="@TITLE2"/>
			</title>
			<footer s="5" lines="1" footerNum="1">
				<xsl:value-of select="@TITLECOMMENT"/>
			</footer>
			<footer s="5"/>
			<cpafooter s="5"/>
			<logo align="left"/>
            <xsl:for-each select="rtdim">
            	<rtdim s="4" name="@name">
            		<name><xsl:value-of select="@name"/></name>
					<value><xsl:value-of select="@value"/></value>
				</rtdim>
            </xsl:for-each>
			<xsl:call-template name="buildheader"/>
			<body s="1">
                <!-- JPC - removed extra blank row below header for xls cleanup -->
                <!-- 
				<xsl:if test="(0 and $report_format!='_excel')">
			        <row s="12">
				    	<col s="19" >
                        <xsl:if  test="(0 and $report_format!='_excel')">
                            <xsl:attribute name="colspan"><xsl:value-of select="/reportdata/report/@noofcolumns"/></xsl:attribute>
  		                </xsl:if>
                        </col>
                        <xsl:if  test="($report_format='_excel')">
                            <xsl:call-template name="add_empty_col">
                                <xsl:with-param name="i" select="/reportdata/report/@noofcolumns - 1"/>
                            </xsl:call-template>
  		                </xsl:if>
				    </row>
				</xsl:if> -->
				<xsl:choose>
					<xsl:when test="(@orientation = 'Portrait')">
						<xsl:apply-templates mode="Portrait"/>
					</xsl:when>
					<xsl:otherwise>
						<xsl:apply-templates mode="Landscape"/>
					</xsl:otherwise>
				</xsl:choose>
				<xsl:call-template name="totalrow"/>
			</body>
			<xsl:call-template name="generatestyles"/>
			<xsl:call-template name="script"/>
		</report>
	</xsl:template>
	<xsl:template name="buildheader">
		<header>
			<xsl:choose>
				<xsl:when test="(/reportdata/tbreport/@showdetail = 'T')">					
					<hrow s="7">
                        <xsl:choose>
                            <xsl:when test="number($narrow_format)=1">
						        <hcol id="0" s="22" width="10"><xsl:value-of select="/reportdata/tbreport/@account_pdfheader"/></hcol>
                            </xsl:when>
                            <xsl:otherwise>
						        <hcol id="0" s="22" width="10"><xsl:value-of select="/reportdata/tbreport/@account_header"/></hcol>
						        <hcol id="0" s="22" width="10">IA.ACCOUNT_NAME</hcol>
                            </xsl:otherwise>
                        </xsl:choose>

						<hcol id="0" s="22" width="10">IA.DATE</hcol>
<!--						<hcol id="1" s="22" width="27">Doc</hcol> -->
						<hcol id="1" s="22" width="17">IA.DOC</hcol>
						<hcol id="2" s="22" width="40">IA.MEMO</hcol>
						<hcol id="3" width="20" s="8">IA.AMOUNT<xsl:if test="(/reportdata/report/@ISMCMESUBSCR = 'true')">(<xsl:value-of select="/reportdata/report/@BASECURR"/>)</xsl:if>
						</hcol>
					</hrow>
				</xsl:when>
				<xsl:otherwise>
					<xsl:choose>
						<xsl:when test="(/reportdata/tbreport/@DEBITCREDITBALANCE = 1)">
					<hrow s="6">
                        <hcol id="0" s="22" >
                                <xsl:attribute name="width">
                                <xsl:choose>
                                        <xsl:when test="/reportdata/tbreport/@includeTaxCode = 1">
                                                <xsl:value-of select= "6"/>
                                        </xsl:when>
                                        <xsl:otherwise>
                                                <xsl:value-of select= "8"/>
                                        </xsl:otherwise>
                                </xsl:choose>
                                </xsl:attribute>
                        <xsl:value-of select="/reportdata/tbreport/@tbaccount_header1"/></hcol>
                        <hcol id="1" s="22">
                                <xsl:attribute name="width">
                                <xsl:choose>
                                        <xsl:when test="/reportdata/tbreport/@includeTaxCode = 1">
                                                <xsl:value-of select= "15"/>
                                        </xsl:when>
                                        <xsl:otherwise>
                                                <xsl:value-of select= "20"/>
                                        </xsl:otherwise>
                                </xsl:choose>
                                </xsl:attribute>
                        <xsl:text>IA.ACCOUNT</xsl:text></hcol>
						<hcol id="2" width="8" s="8">
							<xsl:value-of select="/reportdata/tbreport/@ob_header"/>
                        </hcol>
						<hcol id="3" width="8" s="8">
							<xsl:text>IA.ON </xsl:text>
							<format type="date">
								<xsl:value-of select="/reportdata/tbreport/@STARTDATE"/>
							</format>
						</hcol>
                        <hcol id="4" s="8">
                                <xsl:attribute name="width">
                                <xsl:choose>
                                        <xsl:when test="/reportdata/tbreport/@includeTaxCode = 1">
                                                <xsl:value-of select= "7"/>
                                        </xsl:when>
                                        <xsl:otherwise>
                                                <xsl:value-of select= "10"/>
                                        </xsl:otherwise>
                                </xsl:choose>
                                </xsl:attribute>
                        </hcol>
                        <hcol id="5" s="8">
                                <xsl:attribute name="width">
                                <xsl:choose>
                                        <xsl:when test="/reportdata/tbreport/@includeTaxCode = 1">
                                                <xsl:value-of select= "7"/>
                                        </xsl:when>
                                        <xsl:otherwise>
                                                <xsl:value-of select= "10"/>
                                        </xsl:otherwise>
                                </xsl:choose>
                                </xsl:attribute>
                        </hcol>
						<xsl:if test="/reportdata/tbreport/@EXCLUDEADJENTRIES != 1">
							<hcol id="6" s="8">
								<xsl:attribute name="width">
									<xsl:choose>
										<xsl:when test="/reportdata/tbreport/@includeTaxCode = 1">
											<xsl:value-of select= "7"/>
										</xsl:when>
										<xsl:otherwise>
											<xsl:value-of select= "10"/>
										</xsl:otherwise>
									</xsl:choose>
								</xsl:attribute>
							</hcol>
							<hcol id="7" s="8">
								<xsl:attribute name="width">
									<xsl:choose>
										<xsl:when test="/reportdata/tbreport/@includeTaxCode = 1">
											<xsl:value-of select= "8"/>
										</xsl:when>
										<xsl:otherwise>
											<xsl:value-of select= "11"/>
										</xsl:otherwise>
									</xsl:choose>
								</xsl:attribute>
							</hcol>
						</xsl:if>
                        <hcol id="8" width="8" s="8" >
							<xsl:value-of select="/reportdata/tbreport/@cb_header"/>
						</hcol>
						<hcol id="9" width="8" s="8">
							<xsl:text>IA.ON </xsl:text>
							<format type="date">
								<xsl:value-of select="/reportdata/tbreport/@ENDDATE"/>
							</format>
						</hcol>
						<xsl:if test="/reportdata/tbreport/@includeTaxCode = 1">
							<hcol id="10" width="9">IA.TAX_RETURN</hcol>
							<hcol id="11" width="9">IA.M3_RETURN</hcol>
						</xsl:if>
					</hrow>
				</xsl:when>
				<xsl:otherwise>
					<hrow s="6">
						<hcol id="0" s="22">
							<xsl:attribute name="width">
								<xsl:choose>
									<xsl:when test="/reportdata/tbreport/@includeTaxCode = 1">
										<xsl:value-of select= "8"/>
									</xsl:when>
									<xsl:otherwise>
										<xsl:value-of select= "10"/>
									</xsl:otherwise>
								</xsl:choose>
							</xsl:attribute>
							<xsl:value-of select="/reportdata/tbreport/@tbaccount_header1"/></hcol>
						<hcol id="1" s="22">
							<xsl:attribute name="width">
								<xsl:choose>
									<xsl:when test="/reportdata/tbreport/@includeTaxCode = 1">
										<xsl:value-of select= "17"/>
									</xsl:when>
									<xsl:otherwise>
										<xsl:value-of select= "22"/>
									</xsl:otherwise>
								</xsl:choose>
							</xsl:attribute>
							<xsl:text>IA.ACCOUNT</xsl:text></hcol>
						<hcol id="2" width="12" s="8">
							<xsl:value-of select="/reportdata/tbreport/@ob_header"/></hcol>
						<hcol id="3" s="8">
							<xsl:attribute name="width">
								<xsl:choose>
									<xsl:when test="/reportdata/tbreport/@includeTaxCode = 1">
										<xsl:value-of select= "8"/>
									</xsl:when>
									<xsl:otherwise>
										<xsl:value-of select= "11"/>
									</xsl:otherwise>
								</xsl:choose>
							</xsl:attribute>
						</hcol>
						<hcol id="4" s="8">
							<xsl:attribute name="width">
								<xsl:choose>
									<xsl:when test="/reportdata/tbreport/@includeTaxCode = 1">
										<xsl:value-of select= "8"/>
									</xsl:when>
									<xsl:otherwise>
										<xsl:value-of select= "11"/>
									</xsl:otherwise>
								</xsl:choose>
							</xsl:attribute>
						</hcol>
						<xsl:if test="/reportdata/tbreport/@EXCLUDEADJENTRIES != 1">
							<hcol id="5" s="8">
								<xsl:attribute name="width">
									<xsl:choose>
										<xsl:when test="/reportdata/tbreport/@includeTaxCode = 1">
											<xsl:value-of select= "8"/>
										</xsl:when>
										<xsl:otherwise>
											<xsl:value-of select= "11"/>
										</xsl:otherwise>
									</xsl:choose>
								</xsl:attribute>
							</hcol>
							<hcol id="6" s="8">
								<xsl:attribute name="width">
									<xsl:choose>
										<xsl:when test="/reportdata/tbreport/@includeTaxCode = 1">
											<xsl:value-of select= "8"/>
										</xsl:when>
										<xsl:otherwise>
											<xsl:value-of select= "11"/>
										</xsl:otherwise>
									</xsl:choose>
								</xsl:attribute>
							</hcol>
						</xsl:if>
						<hcol id="7" width="12" s="8">
							<xsl:value-of select="/reportdata/tbreport/@cb_header"/>
                        </hcol>
                        <xsl:if test="/reportdata/tbreport/@includeTaxCode = 1">
                            <hcol id="8" width="9">IA.TAX_RETURN</hcol>
                            <hcol id="8" width="9">IA.M3_RETURN</hcol>
                        </xsl:if>
					</hrow>
				</xsl:otherwise>
			</xsl:choose>
			<xsl:choose>
				<xsl:when test="(/reportdata/tbreport/@DEBITCREDITBALANCE = 1)">
					<hrow s="7">
						<xsl:variable name="adjDebitColName">
							<xsl:choose>
								<xsl:when test="$report_format='_excel' or $report_format='_html'">
									<xsl:if test="/reportdata/tbreport/@EXCLUDEADJENTRIES != 1">
										<xsl:call-template name="string-replace">
											<xsl:with-param name="haystack" select="'IA.ADJUSTING_HEADER'" />
											<xsl:with-param name="replace" select="'${HEADER}'" />
											<xsl:with-param name="by" select="/reportdata/tbreport/@debit_header" />
										</xsl:call-template>
									</xsl:if>
								</xsl:when>
								<xsl:otherwise>
									<xsl:if test="/reportdata/tbreport/@EXCLUDEADJENTRIES != 1">
										<xsl:call-template name="string-replace">
											<xsl:with-param name="haystack" select="'IA.ADJ_HEADER'" />
											<xsl:with-param name="replace" select="'${HEADER}'" />
											<xsl:with-param name="by" select="/reportdata/tbreport/@debit_header" />
										</xsl:call-template>
									</xsl:if>
								</xsl:otherwise>
							</xsl:choose>
						</xsl:variable>
						<xsl:variable name="adjCreditColName">
							<xsl:choose>
								<xsl:when test="$report_format='_excel' or $report_format='_html'">
									<xsl:if test="/reportdata/tbreport/@EXCLUDEADJENTRIES != 1">
										<xsl:call-template name="string-replace">
											<xsl:with-param name="haystack" select="'IA.ADJUSTING_HEADER'" />
											<xsl:with-param name="replace" select="'${HEADER}'" />
											<xsl:with-param name="by" select="/reportdata/tbreport/@credit_header" />
										</xsl:call-template>
									</xsl:if>
								</xsl:when>
								<xsl:otherwise>
									<xsl:if test="/reportdata/tbreport/@EXCLUDEADJENTRIES != 1">
										<xsl:call-template name="string-replace">
											<xsl:with-param name="haystack" select="'IA.ADJ_HEADER'" />
											<xsl:with-param name="replace" select="'${HEADER}'" />
											<xsl:with-param name="by" select="/reportdata/tbreport/@credit_header" />
										</xsl:call-template>
									</xsl:if>
								</xsl:otherwise>
							</xsl:choose>
						</xsl:variable>
						<hcol id="0" s="22"><xsl:value-of select="/reportdata/tbreport/@tbaccount_header2"/></hcol>
						<hcol id="1" s="22">IA.NAME</hcol>
						<hcol id="2" s="8"><xsl:value-of select="/reportdata/tbreport/@debit_header"/></hcol>
						<hcol id="3" s="8"><xsl:value-of select="/reportdata/tbreport/@credit_header"/></hcol>
						<hcol id="4" s="8">
							<xsl:value-of select="/reportdata/tbreport/@debit_header"/>
						</hcol>
						<hcol id="5" s="8">
							<xsl:value-of select="/reportdata/tbreport/@credit_header"/>
						</hcol>
						<xsl:if test="/reportdata/tbreport/@EXCLUDEADJENTRIES != 1">
							<hcol id="6" s="8">
								<xsl:value-of select="$adjDebitColName"/>
							</hcol>
							<hcol id="7" s="8">
								<xsl:value-of select="$adjCreditColName"/>
							</hcol>
						</xsl:if>
						<hcol id="8" s="8"><xsl:value-of select="/reportdata/tbreport/@debit_header"/></hcol>
						<hcol id="9" s="8"><xsl:value-of select="/reportdata/tbreport/@credit_header"/></hcol>
						<xsl:if test="/reportdata/tbreport/@includeTaxCode = 1">
							<hcol id="10" width="10">IA.CODE</hcol>
							<hcol id="11" width="10">IA.CODE</hcol>
						</xsl:if>
					</hrow>
				</xsl:when>
				<xsl:otherwise>
					<hrow s="7">
                        <xsl:variable name="adjDebitColName">
                            <xsl:choose>
                                <xsl:when test="$report_format='_excel' or $report_format='_html'">
									<xsl:if test="/reportdata/tbreport/@EXCLUDEADJENTRIES != 1">
										<xsl:call-template name="string-replace">
											<xsl:with-param name="haystack" select="'IA.ADJUSTING_HEADER'" />
											<xsl:with-param name="replace" select="'${HEADER}'" />
											<xsl:with-param name="by" select="/reportdata/tbreport/@debit_header" />
										</xsl:call-template>
									</xsl:if>
								</xsl:when>
                                <xsl:otherwise>
									<xsl:if test="/reportdata/tbreport/@EXCLUDEADJENTRIES != 1">
										<xsl:call-template name="string-replace">
											<xsl:with-param name="haystack" select="'IA.ADJ_HEADER'" />
											<xsl:with-param name="replace" select="'${HEADER}'" />
											<xsl:with-param name="by" select="/reportdata/tbreport/@debit_header" />
										</xsl:call-template>
									</xsl:if>
                                </xsl:otherwise>
                            </xsl:choose>
                        </xsl:variable>
                        <xsl:variable name="adjCreditColName">
                            <xsl:choose>
                                <xsl:when test="$report_format='_excel' or $report_format='_html'">
									<xsl:if test="/reportdata/tbreport/@EXCLUDEADJENTRIES != 1">
										<xsl:call-template name="string-replace">
											<xsl:with-param name="haystack" select="'IA.ADJUSTING_HEADER'" />
											<xsl:with-param name="replace" select="'${HEADER}'" />
											<xsl:with-param name="by" select="/reportdata/tbreport/@credit_header" />
										</xsl:call-template>
									</xsl:if>
                                </xsl:when>
                                <xsl:otherwise>
									<xsl:if test="/reportdata/tbreport/@EXCLUDEADJENTRIES != 1">
										<xsl:call-template name="string-replace">
											<xsl:with-param name="haystack" select="'IA.ADJ_HEADER'" />
											<xsl:with-param name="replace" select="'${HEADER}'" />
											<xsl:with-param name="by" select="/reportdata/tbreport/@credit_header" />
										</xsl:call-template>
									</xsl:if>
                                </xsl:otherwise>
                            </xsl:choose>
                        </xsl:variable>
						<hcol id="0" s="22"><xsl:value-of select="/reportdata/tbreport/@tbaccount_header2"/></hcol>
						<hcol id="1" s="22">IA.NAME</hcol>
						<hcol id="2" s="8">
							<xsl:text>on </xsl:text>
							<format type="date">
								<xsl:value-of select="/reportdata/tbreport/@STARTDATE"/>
							</format>
						</hcol>
						<hcol id="3" s="8">
							<xsl:value-of select="/reportdata/tbreport/@debit_header"/>
						</hcol>
						<hcol id="4" s="8">
							<xsl:value-of select="/reportdata/tbreport/@credit_header"/>
						</hcol>
						<xsl:if test="/reportdata/tbreport/@EXCLUDEADJENTRIES != 1">
							<hcol id="5" s="8">
								<xsl:value-of select="$adjDebitColName"/>
							</hcol>
							<hcol id="6" s="8">
								<xsl:value-of select="$adjCreditColName"/>
							</hcol>
						</xsl:if>
						<hcol id="7" s="8">
							<xsl:text>on </xsl:text>
							<format type="date">
								<xsl:value-of select="/reportdata/tbreport/@ENDDATE"/>
							</format>
						</hcol>
                        <xsl:if test="/reportdata/tbreport/@includeTaxCode = 1">
                                <hcol id="8" width="10">IA.CODE</hcol>
                                <hcol id="8" width="10">IA.CODE</hcol>
                        </xsl:if>
					</hrow>
				</xsl:otherwise>
			</xsl:choose>
		</xsl:otherwise>
	</xsl:choose>
		</header>
	</xsl:template>
	<xsl:template match="NODATA" mode="Portrait">
		<xsl:if test="string(@NODATA)=1">
			<row s="9">
				<col id="0" s="10" colspan="20">IA.NO_DATA_FOR_SELECTED_FILTERS</col>
			</row>
		</xsl:if>
	</xsl:template>
	<xsl:template name="TXN">
		<row s="9">
            <col id="0"/>
            <xsl:choose>
                <xsl:when test="number($narrow_format) != 1">
                    <col id="0"/>
                </xsl:when>
                <xsl:otherwise>
                </xsl:otherwise>
            </xsl:choose>

			<col id="0" s="10">
				<xsl:value-of select="@GLPOSTDATE"/>
			</col>
			<col id="2" s="10">
				<xsl:value-of select="@DOC"/>
			</col>
			<col id="3" s="10">
				<xsl:value-of select="@MEMO"/>
			</col>
			<col id="4" s="11">
				<xsl:value-of select="@AMOUNT"/>
			</col>
		</row>
	</xsl:template>
	<xsl:template name="emptyrow">
		<row s="9">
			<col s="13" id="0">
			<xsl:choose>
				<xsl:when test="($report_format='_html')"><xsl:text>&amp;nbsp;</xsl:text></xsl:when>
				<xsl:otherwise> </xsl:otherwise>
			</xsl:choose>
			</col>
            <col id="1" s="13" />
            <col id="2" s="13" />
            <col id="3" s="13" />
		</row>
	</xsl:template>
	<xsl:template match="trans" mode="Portrait">
		<xsl:choose>
			<xsl:when test="(/reportdata/tbreport/@showdetail = 'T')">

				<row s="">
                    <xsl:choose>
                        <xsl:when test="$narrow_format != '1'">
					        <col id="0" s="10">
					        	<xsl:value-of select="@ACCTNO"/>
					        </col>
					        <col id="0" s="10">
					        	<xsl:value-of select="@TITLE"/>
					        </col>
                        </xsl:when>
                        <xsl:otherwise>
					        <col id="0" s="10">
                                <!-- PDF doesn't resize it's columns due to width restrictions we must span smartly -->
                                <xsl:if test="$report_format = '_pdf'">
                                    <xsl:attribute name="colspan">3</xsl:attribute>
                                </xsl:if>
					        	<xsl:value-of select="@ACCTNO"/>--<xsl:value-of select="@TITLE"/>
					        </col>
                        </xsl:otherwise>
                    </xsl:choose>

					<col id="1" s="10">
						<xsl:text>IA.OPENING_BALANCE_ON </xsl:text>
						<format type="date">
							<xsl:value-of select="/reportdata/tbreport/@STARTDATE"/>
						</format>
                    </col>
                    <xsl:if test="$report_format != '_pdf'">
                        <col id="1"/>
                        <col id="1"/>
                    </xsl:if>
					<col id="1" s="11">
						<xsl:value-of select="@ACCFWD"/>
					</col>
				</row>
				<xsl:call-template name="emptyrow"/>
				<xsl:if test="(@ACCDB != '0.00')">
					<row s="17">
                        <col id="0"/>
						<col id="0" s="10"> 
                            <xsl:if test="$report_format = '_pdf'">
                                <xsl:attribute name="colspan">2</xsl:attribute>
                            </xsl:if>
							<xsl:text>IA.DEPOSITS_RECEIPTS</xsl:text>
						</col>
					</row>
					<xsl:for-each select="./ACCDBTXNS/TXN">
						<xsl:call-template name="TXN"/>
					</xsl:for-each>
					<row s="17">
                        <col id="0"/>
                        <xsl:choose>
                            <xsl:when test="number($narrow_format) = 1">
						        <col id="0" s="13" colspan="2">
						        	<xsl:text>IA.TOTAL_DEPOSITS_RECEIPTS</xsl:text>
						        </col>
                            </xsl:when>
                            <xsl:otherwise>
						        <col id="0" s="13"> 
						        	<xsl:text>IA.TOTAL_DEPOSITS_RECEIPTS</xsl:text>
						        </col>
                                <col id="0"/>
                                <col/>
                            </xsl:otherwise>
                        </xsl:choose>

                        <col/>
						<col id="1" s="14">
							<xsl:value-of select="@ACCDB "/>
						</col>
					</row>
					<xsl:call-template name="emptyrow"/>
				</xsl:if>
				<xsl:if test="(@ACCCR != '0.00')">
					<row s="17">
                        <col />
						<col id="0" s="10">
                            <xsl:if test="$report_format = '_pdf'">
                                <xsl:attribute name="colspan">2</xsl:attribute>
                            </xsl:if>
							<xsl:text>IA.CHECKS_DISBURSEMENTS</xsl:text>
						</col>
                        <col id="1" s="13" />
					</row>
					<xsl:for-each select="./ACCCRTXNS/TXN">
						<xsl:call-template name="TXN"/>
					</xsl:for-each>
					<row s="17">
                        <col />
                        <xsl:choose>
                            <xsl:when test="number($narrow_format) = 1">
						        <col id="0" s="13" colspan="2">
						        	<xsl:text>IA.TOTAL_CHECKS_DISBURSEMENTS</xsl:text>
						        </col>
                            </xsl:when>
                            <xsl:otherwise>
						        <col id="0" s="13"> 
						        	<xsl:text>IA.TOTAL_CHECKS_DISBURSEMENTS</xsl:text>
						        </col>
                                <col id="0"/>
                                <col/>
                            </xsl:otherwise>
                        </xsl:choose>

                        <col />
						<col id="1" s="14">
							<xsl:value-of select="@ACCCR"/>
						</col>
					</row>
					<xsl:call-template name="emptyrow"/>
				</xsl:if>
				<xsl:if test="(@ADJDB!= '0.00')">
					<row s="17">
                        <col />
						<col id="0" s="10"> 
                            <xsl:if test="$report_format = '_pdf'">
                                <xsl:attribute name="colspan">2</xsl:attribute>
                            </xsl:if>
							<xsl:text>IA.TRANSFERS_AND_ADJUSTMENTS</xsl:text>
						</col>
                        <xsl:if test="$report_format != '_pdf'">
                            <col/>
                        </xsl:if>
                        <col />
					</row>
					<xsl:for-each select="./ACCADJDBTXNS/TXN">
						<xsl:call-template name="TXN"/>
					</xsl:for-each>
					<row s="17">
                        <col />
                        <xsl:if test="$narrow_format != '1'">
                            <col id="0"/>
                        </xsl:if>
						<col id="0" s="13">
                            <xsl:if test="$report_format = '_pdf'">
                                <xsl:attribute name="colspan">2</xsl:attribute>
                            </xsl:if>
							<xsl:text>IA.TOTAL_TRANSFERS_AND_ADJUSTMENTS</xsl:text>
						</col>
                        <xsl:if test="$report_format != '_pdf'">
                            <col/>
                        </xsl:if>
                        <col />
						<col id="1" s="14">
							<xsl:value-of select="@ADJDB"/>
						</col>
					</row>
					<xsl:call-template name="emptyrow"/>
				</xsl:if>
				<xsl:if test="(@ADJCR!= '0.00')">
					<row s="9">
                        <col />
						<col id="0" s="10">
                            <xsl:if test="$report_format = '_pdf'">
                                <xsl:attribute name="colspan">2</xsl:attribute>
                            </xsl:if>
							<xsl:text>IA.ADJUSTMENT_CHECKS_DISBURSEMENTS</xsl:text>
						</col>
                        <xsl:if test="$report_format != '_pdf'">
                            <col/>
                        </xsl:if>
                        <col />
					</row>
					<xsl:for-each select="./ACCADJCRTXNS/TXN">
						<xsl:call-template name="TXN"/>
					</xsl:for-each>
					<row s="17">
                        <col />
                        <xsl:if test="$narrow_format != '1'">
                            <col id="0"/>
                        </xsl:if>
						<col id="0" s="13">
                            <xsl:if test="$report_format = '_pdf'">
                                <xsl:attribute name="colspan">2</xsl:attribute>
                            </xsl:if>
							<xsl:text>IA.TOTAL_ADJUSTMENT_CHECKS_DISBURSEMENTS</xsl:text>
						</col>
                        <xsl:if test="$report_format != '_pdf'">
                            <col/>
                        </xsl:if>
                        <col />
						<col id="1" s="14">
							<xsl:value-of select="@ADJCR"/>
						</col>
					</row>
					<xsl:call-template name="emptyrow"/>
				</xsl:if>
				<row s="18" pagebreak='Y'>
                    <col/>
                    <xsl:if test="$narrow_format != '1'">
                        <col id="0"/>
                    </xsl:if>
					<col id="0" s="13">
                        <xsl:if test="$report_format = '_pdf'">
                            <xsl:attribute name="colspan">2</xsl:attribute>
                        </xsl:if>
						<xsl:text>IA.ENDING_BALANCE_ON </xsl:text>
						<format type="date">
							<xsl:value-of select="/reportdata/tbreport/@ENDDATE"/>
						</format>
					</col>
                    <xsl:if test="$report_format != '_pdf'">
                        <col/>
                    </xsl:if>
                    <col/>
					<col id="1" s="23">
						<xsl:value-of select="@ACCBAL"/>
					</col>
				</row>
                <xsl:if test="number($narrow_format) != 1">
    				<xsl:call-template name="emptyrow"/>
                </xsl:if>
			</xsl:when>
		<xsl:otherwise>
		<xsl:choose>
			<xsl:when test="(/reportdata/tbreport/@DEBITCREDITBALANCE = 1)">
				<row s="9">
					<col id="0" s="10">
						<xsl:value-of select="@ACCTNO"/>
					</col>
					<col id="1" s="10">
						<xsl:value-of select="@TITLE"/>
					</col>
					<col id="2" s="11"><xsl:value-of select="@ACCBEGDB"/></col>
					<col id="3" s="11"><xsl:value-of select="@ACCBEGCR"/></col>
					<col id="4" s="11">
						<xsl:if test="(@ACCDB != '0.00')">
							<xsl:attribute name="href"><xsl:text>javascript:tb_gldrill('</xsl:text><xsl:value-of select="@RECNO"/><xsl:text>',</xsl:text><xsl:value-of select="@ISSTAT"/><xsl:text>);</xsl:text></xsl:attribute>
						</xsl:if>
						<xsl:value-of select="@ACCDB"/>
					</col>
					<col id="5" s="11">
						<xsl:if test="(@ACCCR!= '0.00')">
							<xsl:attribute name="href"><xsl:text>javascript:tb_gldrill('</xsl:text><xsl:value-of select="@RECNO"/><xsl:text>',</xsl:text><xsl:value-of select="@ISSTAT"/><xsl:text>);</xsl:text></xsl:attribute>
						</xsl:if>
						<xsl:value-of select="@ACCCR"/>
					</col>
					<xsl:if test="/reportdata/tbreport/@EXCLUDEADJENTRIES != 1">
						<col id="6" s="11">
							<xsl:if test="(@ADJDB!= '0.00')">
								<xsl:attribute name="href"><xsl:text>javascript:tb_gldrill('</xsl:text><xsl:value-of select="@RECNO"/><xsl:text>',</xsl:text><xsl:value-of select="@ISSTAT"/><xsl:text>);</xsl:text></xsl:attribute>
							</xsl:if>
							<xsl:value-of select="@ADJDB"/>
						</col>
						<col id="7" s="11">
							<xsl:if test="(@ADJCR!= '0.00')">
								<xsl:attribute name="href"><xsl:text>javascript:tb_gldrill('</xsl:text><xsl:value-of select="@RECNO"/><xsl:text>',</xsl:text><xsl:value-of select="@ISSTAT"/><xsl:text>);</xsl:text></xsl:attribute>
							</xsl:if>
							<xsl:value-of select="@ADJCR"/>
						</col>
					</xsl:if>
					<col id="8" s="11"><xsl:value-of select="@ACCENDDB"/></col>
					<col id="9" s="11"><xsl:value-of select="@ACCENDCR"/></col>
					<xsl:if test="/reportdata/tbreport/@includeTaxCode = 1">
						<col id="10" s="10"><xsl:value-of select="@TAXCODE"/></col>
						<col id="11" s="10"><xsl:value-of select="@MRCCODE"/></col>
					</xsl:if>
				</row>
			</xsl:when>
			<xsl:otherwise>
				<row s="9">
					<col id="0" s="10">
						<xsl:value-of select="@ACCTNO"/>
					</col>
					<col id="1" s="10">
						<xsl:value-of select="@TITLE"/>
					</col>
					<col id="2" s="11">
						<xsl:value-of select="@ACCFWD"/>
					</col>
					<col id="3" s="11">
						<xsl:if test="(@ACCDB != '0.00')">
							<xsl:attribute name="href"><xsl:text>javascript:tb_gldrill('</xsl:text><xsl:value-of select="@RECNO"/><xsl:text>',</xsl:text><xsl:value-of select="@ISSTAT"/><xsl:text>);</xsl:text></xsl:attribute>
						</xsl:if>
						<xsl:value-of select="@ACCDB"/>
					</col>
					<col id="4" s="11">
						<xsl:if test="(@ACCCR!= '0.00')">
							<xsl:attribute name="href"><xsl:text>javascript:tb_gldrill('</xsl:text><xsl:value-of select="@RECNO"/><xsl:text>',</xsl:text><xsl:value-of select="@ISSTAT"/><xsl:text>);</xsl:text></xsl:attribute>
						</xsl:if>
						<xsl:value-of select="@ACCCR"/>
					</col>
					<xsl:if test="/reportdata/tbreport/@EXCLUDEADJENTRIES != 1">
						<col id="5" s="11">
							<xsl:if test="(@ADJDB!= '0.00')">
								<xsl:attribute name="href"><xsl:text>javascript:tb_gldrill('</xsl:text><xsl:value-of select="@RECNO"/><xsl:text>',</xsl:text><xsl:value-of select="@ISSTAT"/><xsl:text>);</xsl:text></xsl:attribute>
							</xsl:if>
							<xsl:value-of select="@ADJDB"/>
						</col>
						<col id="6" s="11">
							<xsl:if test="(@ADJCR!= '0.00')">
								<xsl:attribute name="href"><xsl:text>javascript:tb_gldrill('</xsl:text><xsl:value-of select="@RECNO"/><xsl:text>',</xsl:text><xsl:value-of select="@ISSTAT"/><xsl:text>);</xsl:text></xsl:attribute>
							</xsl:if>
							<xsl:value-of select="@ADJCR"/>
						</col>
					</xsl:if>
					<col id="7" s="11">
						<xsl:value-of select="@ACCBAL"/>
					</col>
                    <xsl:if test="/reportdata/tbreport/@includeTaxCode = 1">
                            <col id="8" s="10"><xsl:value-of select="@TAXCODE"/></col>
                            <col id="9" s="10"><xsl:value-of select="@MRCCODE"/></col>
                    </xsl:if>
				</row>
			</xsl:otherwise>
		</xsl:choose>
		</xsl:otherwise>
		</xsl:choose>
	</xsl:template>
	<xsl:template match="trans" mode="Landscape">
		<xsl:choose>
			<xsl:when test="(/reportdata/tbreport/@showdetail = 'T')">
				<row s="">
                    <xsl:choose>
                        <xsl:when test="$narrow_format != '1'">
					        <col id="0" s="10">
					        	<xsl:value-of select="@ACCTNO"/>
					        </col>
					        <col id="0" s="10">
					        	<xsl:value-of select="@TITLE"/>
					        </col>
                        </xsl:when>
                        <xsl:otherwise>
					        <col id="0" s="10">
                                <!-- PDF doesn't resize it's columns due to width restrictions we must span smartly -->
                                <xsl:if test="$report_format = '_pdf'">
                                    <xsl:attribute name="colspan">3</xsl:attribute>
                                </xsl:if>
					        	<xsl:value-of select="@ACCTNO"/>--<xsl:value-of select="@TITLE"/>
					        </col>
                        </xsl:otherwise>
                    </xsl:choose>
					<col id="0" s="l3">
						<xsl:text>IA.OPENING_BALANCE_ON </xsl:text>
						<format type="date">
							<xsl:value-of select="/reportdata/tbreport/@STARTDATE"/>
						</format>
					</col>
                    <xsl:if test="$report_format != '_pdf'">
                        <col/>
                        <col/>
                    </xsl:if>
					<col id="1" s="24">
						<xsl:value-of select="@ACCFWD"/>
					</col>
				</row>
				<xsl:call-template name="emptyrow"/>
				<xsl:if test="(@ACCDB != '0.00')">
					<row s="17">
                        <col/>
						<col id="0" s="10">
                            <xsl:if test="$report_format = '_pdf'">
                                <xsl:attribute name="colspan">2</xsl:attribute>
                            </xsl:if>
							<xsl:text>IA.DEPOSITS_RECEIPTS</xsl:text>
						</col>
                        <xsl:if test="$report_format != '_pdf'">
                            <col/>
                        </xsl:if>
                        <col/>
					</row>
					<xsl:for-each select="./ACCDBTXNS/TXN">
						<xsl:call-template name="TXN"/>
					</xsl:for-each>
					<row s="17">
                        <col/>
                        <xsl:if test="$narrow_format != '1'">
                            <col id="0"/>
                        </xsl:if>
						<col id="0" s="13">
                            <xsl:if test="$report_format = '_pdf'">
                                <xsl:attribute name="colspan">2</xsl:attribute>
                            </xsl:if>
							<xsl:text>IA.TOTAL_DEPOSITS_RECEIPTS</xsl:text>
						</col>
                        <xsl:if test="$report_format != '_pdf'">
                            <col/>
                        </xsl:if>
                        <col/>
						<col id="1" s="14">
							<xsl:value-of select="@ACCDB "/>
						</col>
					</row>
					<xsl:call-template name="emptyrow"/>
				</xsl:if>
				<xsl:if test="(@ACCCR != '0.00')">
					<row s="17">
                        <col/>
						<col id="0" s="10">
                            <xsl:if test="$report_format = '_pdf'">
                                <xsl:attribute name="colspan">2</xsl:attribute>
                            </xsl:if>
							<xsl:text>IA.CHECKS_DISBURSEMENTS</xsl:text>
						</col>
                        <xsl:if test="$report_format != '_pdf'">
                            <col/>
                        </xsl:if>
                        <col/>
                        <col/>
					</row>
					<xsl:for-each select="./ACCCRTXNS/TXN">
						<xsl:call-template name="TXN"/>
					</xsl:for-each>
					<row s="17">
                        <col/>
                        <xsl:if test="$narrow_format != '1'">
                            <col id="0"/>
                        </xsl:if>
						<col id="0" s="13">
                            <xsl:if test="$report_format = '_pdf'">
                                <xsl:attribute name="colspan">2</xsl:attribute>
                            </xsl:if>
							<xsl:text>IA.TOTAL_CHECKS_DISBURSEMENTS</xsl:text>
						</col>
                        <xsl:if test="$report_format != '_pdf'">
                            <col/>
                        </xsl:if>
                        <col/>
						<col id="1" s="14">
							<xsl:value-of select="@ACCCR"/>
						</col>
					</row>
					<xsl:call-template name="emptyrow"/>
				</xsl:if>
				<xsl:if test="(@ADJDB!= '0.00')">
					<row s="17">
                        <col/>
						<col id="0" s="13">
                            <xsl:if test="$report_format = '_pdf'">
                                <xsl:attribute name="colspan">2</xsl:attribute>
                            </xsl:if>
							<xsl:text>IA.TRANSFERS_AND_ADJUSTMENTS</xsl:text>
						</col>
                        <xsl:if test="$report_format != '_pdf'">
                            <col/>
                        </xsl:if>
                        <col/>
                        <col/>
					</row>
					<xsl:for-each select="./ACCADJDBTXNS/TXN">
						<xsl:call-template name="TXN"/>
					</xsl:for-each>
					<row s="17">
                        <col/>
                        <xsl:if test="$narrow_format != '1'">
                            <col id="0"/>
                        </xsl:if>
						<col id="0" s="13">
                            <xsl:if test="$report_format = '_pdf'">
                                <xsl:attribute name="colspan">2</xsl:attribute>
                            </xsl:if>
							<xsl:text>IA.TOTAL_TRANSFERS_AND_ADJUSTMENTS</xsl:text>
						</col>
                        <xsl:if test="$report_format != '_pdf'">
                            <col/>
                        </xsl:if>
                        <col/>
						<col id="1" s="14">
							<xsl:value-of select="@ADJDB"/>
						</col>
					</row>
					<xsl:call-template name="emptyrow"/>
				</xsl:if>
				<xsl:if test="(@ADJCR!= '0.00')">
					<row s="17">
                        <col/>
						<col id="0" s="13">
                            <xsl:if test="$report_format = '_pdf'">
                                <xsl:attribute name="colspan">2</xsl:attribute>
                            </xsl:if>
							<xsl:text>IA.ADJUSTMENT_CHECKS_DISBURSEMENTS</xsl:text>
						</col>
                        <xsl:if test="$report_format != '_pdf'">
                            <col/>
                        </xsl:if>
                        <col/>
                        <col/>
					</row>
					<xsl:for-each select="./ACCADJCRTXNS/TXN">
						<xsl:call-template name="TXN"/>
					</xsl:for-each>
					<row s="17">
                        <col/>
                        <xsl:if test="$narrow_format != '1'">
                            <col id="0"/>
                        </xsl:if>
						<col id="0" s="13"> 
							<xsl:text>IA.TOTAL_ADJUSTMENT_CHECKS_DISBURSEMENTS</xsl:text>
						</col>
                        <col/>
                        <col/>
						<col id="1" s="14">
							<xsl:value-of select="@ADJCR"/>
						</col>
					</row>
					<xsl:call-template name="emptyrow"/>
				</xsl:if>
				<row s="18" pagebreak='Y'>
                    <col/>
                    <xsl:if test="$narrow_format != '1'">
                        <col id="0"/>
                    </xsl:if>
					<col id="0" s="13">
                        <xsl:if test="$report_format = '_pdf'">
                            <xsl:attribute name="colspan">2</xsl:attribute>
                        </xsl:if>
						<xsl:text>IA.ENDING_BALANCE_ON </xsl:text>
						<format type="date">
							<xsl:value-of select="/reportdata/tbreport/@ENDDATE"/>
						</format>
					</col>
                    <xsl:if test="$report_format != '_pdf'">
                        <col/>
                    </xsl:if>
                    <col/>
					<col id="1" s="23">
						<xsl:value-of select="@ACCBAL"/>
					</col>
				</row>
                <xsl:if test="number($narrow_format) = 0">
    				<xsl:call-template name="emptyrow"/>
                </xsl:if>
			</xsl:when>
			<xsl:otherwise>
				<xsl:choose>
				<xsl:when test="(/reportdata/tbreport/@DEBITCREDITBALANCE = 1)">
					<row s="9">
						<col id="0" s="10">
							<xsl:value-of select="@ACCTNO"/>
						</col>
						<col id="1" s="10">
							<xsl:value-of select="@TITLE"/>
						</col>
						<col id="2" s="11"><xsl:value-of select="@ACCBEGDB"/></col>
						<col id="3" s="11"><xsl:value-of select="@ACCBEGCR"/></col>
						<col id="4" s="11">
							<xsl:if test="(@ACCDB != '0.00')">
								<xsl:attribute name="href"><xsl:text>javascript:tb_gldrill('</xsl:text><xsl:value-of select="@RECNO"/><xsl:text>');</xsl:text></xsl:attribute>
							</xsl:if>
							<xsl:value-of select="@ACCDB"/>
						</col>
						<col id="5" s="11">
							<xsl:if test="(@ACCCR!= '0.00')">
								<xsl:attribute name="href"><xsl:text>javascript:tb_gldrill('</xsl:text><xsl:value-of select="@RECNO"/><xsl:text>');</xsl:text></xsl:attribute>
							</xsl:if>
							<xsl:value-of select="@ACCCR"/>
						</col>
						<xsl:if test="/reportdata/tbreport/@EXCLUDEADJENTRIES != 1">
							<col id="6" s="11">
								<xsl:if test="(@ADJDB!= '0.00')">
									<xsl:attribute name="href"><xsl:text>javascript:tb_gldrill('</xsl:text><xsl:value-of select="@RECNO"/><xsl:text>');</xsl:text></xsl:attribute>
								</xsl:if>
								<xsl:value-of select="@ADJDB"/>
							</col>
							<col id="7" s="11">
								<xsl:if test="(@ADJCR!= '0.00')">
									<xsl:attribute name="href"><xsl:text>javascript:tb_gldrill('</xsl:text><xsl:value-of select="@RECNO"/><xsl:text>');</xsl:text></xsl:attribute>
								</xsl:if>
								<xsl:value-of select="@ADJCR"/>
							</col>
						</xsl:if>
						<col id="8" s="11"><xsl:value-of select="@ACCENDDB"/></col>
						<col id="9" s="11"><xsl:value-of select="@ACCENDCR"/></col>
						<xsl:if test="/reportdata/tbreport/@includeTaxCode = 1">
							<col id="10" s="10"><xsl:value-of select="@TAXCODE"/></col>
							<col id="11" s="10"><xsl:value-of select="@MRCCODE"/></col>
						</xsl:if>
					</row>
				</xsl:when>
					<xsl:otherwise>
						<row s="9">
					<col id="0" s="10">
						<xsl:value-of select="@ACCTNO"/>
					</col>
					<col id="1" s="10">
						<xsl:value-of select="@TITLE"/>
					</col>
					<col id="2" s="11">
						<xsl:value-of select="@ACCFWD"/>
					</col>
					<col id="3" s="11">
						<xsl:if test="(@ACCDB != '0.00')">
							<xsl:attribute name="href"><xsl:text>javascript:tb_gldrill('</xsl:text><xsl:value-of select="@RECNO"/><xsl:text>');</xsl:text></xsl:attribute>
						</xsl:if>
						<xsl:value-of select="@ACCDB"/>
					</col>
					<col id="4" s="11">
						<xsl:if test="(@ACCCR!= '0.00')">
							<xsl:attribute name="href"><xsl:text>javascript:tb_gldrill('</xsl:text><xsl:value-of select="@RECNO"/><xsl:text>');</xsl:text></xsl:attribute>
						</xsl:if>
						<xsl:value-of select="@ACCCR"/>
					</col>
					<xsl:if test="/reportdata/tbreport/@EXCLUDEADJENTRIES != 1">
						<col id="5" s="11">
							<xsl:if test="(@ADJDB!= '0.00')">
								<xsl:attribute name="href"><xsl:text>javascript:tb_gldrill('</xsl:text><xsl:value-of select="@RECNO"/><xsl:text>');</xsl:text></xsl:attribute>
							</xsl:if>
							<xsl:value-of select="@ADJDB"/>
						</col>
						<col id="6" s="11">
							<xsl:if test="(@ADJCR!= '0.00')">
								<xsl:attribute name="href"><xsl:text>javascript:tb_gldrill('</xsl:text><xsl:value-of select="@RECNO"/><xsl:text>');</xsl:text></xsl:attribute>
							</xsl:if>
							<xsl:value-of select="@ADJCR"/>
						</col>
					</xsl:if>		
					<col id="7" s="11">
						<xsl:value-of select="@ACCBAL"/>
					</col>
                    <xsl:if test="/reportdata/tbreport/@includeTaxCode = 1">
                            <col id="8" s="10"><xsl:value-of select="@TAXCODE"/></col>
                            <col id="9" s="10"><xsl:value-of select="@MRCCODE"/></col>
                    </xsl:if>
				</row>
			</xsl:otherwise>
				</xsl:choose>
			</xsl:otherwise>
		</xsl:choose>
	</xsl:template>
	<xsl:template name="totalrow">
		<xsl:if test="string(/reportdata/tbreport/@showdetail) != 'T'">
			<xsl:if test="string(/reportdata/tbreport/NODATA/@NODATA)!=1">
				<xsl:variable name="totals" select="/reportdata/tbreport"/>
				<xsl:choose>
					<xsl:when test="(/reportdata/tbreport/@DEBITCREDITBALANCE = 1)">
						<row s="12">
							<col id="0" s="13">
								<xsl:value-of select="$totals/@TOTALS"/>
							</col>
							<col id="1" s="13"/>
							<col id="2" s="23">
								<xsl:value-of select="$totals/@TOTOPDB"/>
							</col>
							<col id="3" s="23">
								<xsl:value-of select="$totals/@TOTOPCR"/>
							</col>
							<col id="4" s="23">
								<xsl:value-of select="$totals/@TOTDB"/>
							</col>
							<col id="5" s="23">
								<xsl:value-of select="$totals/@TOTCR"/>
							</col>
							<xsl:if test="/reportdata/tbreport/@EXCLUDEADJENTRIES != 1">
								<col id="6" s="23">
									<xsl:value-of select="$totals/@ADJDB"/>
								</col>
								<col id="7" s="23">
									<xsl:value-of select="$totals/@ADJCR"/>
								</col>
							</xsl:if>
							<col id="8" s="23">
								<xsl:value-of select="$totals/@TOTCLODB"/>
							</col>
							<col id="9" s="23">
								<xsl:value-of select="$totals/@TOTCLOCR"/>
							</col>
							<xsl:if test="/reportdata/tbreport/@includeTaxCode = 1">
								<col id="10" s="13"/>
								<col id="11" s="13"/>
							</xsl:if>
						</row>
					</xsl:when>
					<xsl:otherwise>
						<row s="12">
							<col id="0" s="13">
								<xsl:value-of select="$totals/@TOTALS"/>
							</col>
							<col id="1" s="13"/>
							<col id="2" s="23">
								<xsl:value-of select="$totals/@TOTBEG"/>
							</col>
							<col id="3" s="23">
								<xsl:value-of select="$totals/@TOTDB"/>
							</col>
							<col id="4" s="23">
								<xsl:value-of select="$totals/@TOTCR"/>
							</col>
							<xsl:if test="/reportdata/tbreport/@EXCLUDEADJENTRIES != 1">
								<col id="5" s="23">
									<xsl:value-of select="$totals/@ADJDB"/>
								</col>
								<col id="6" s="23">
									<xsl:value-of select="$totals/@ADJCR"/>
								</col>
							</xsl:if>
							<col id="7" s="23">
								<xsl:value-of select="$totals/@TOTEND"/>
							</col>
							<xsl:if test="/reportdata/tbreport/@includeTaxCode = 1">
									<col id="8" s="13"/>
									<col id="9" s="13"/>
							</xsl:if>
						</row>
					</xsl:otherwise>
			</xsl:choose>
			</xsl:if>
		</xsl:if>
	</xsl:template>
	<xsl:template name="generatestyles">
        <!-- Be careful.  This is a mess.  Some style is here and others in the CSS files. -->
		<stylegroups>
			<stylegroup id="1" name="body" class="BODY" font="Helvetica" size="8" weight="normal" style="normal" color="black"/>
			<stylegroup id="2" name="company" class="W" font="Helvetica" size="12" weight="bold" style="normal" color="black"/>
			<stylegroup id="3" name="title" class="W" font="Helvetica" size="12" weight="bold" style="normal" color="black"/>
			<stylegroup id="4" name="title2" class="W" font="Helvetica" size="12" weight="bold" style="normal" color="black"/>
			<stylegroup id="5" name="footer" class="FOOT" font="Helvetica" size="9" weight="bold" style="normal" color="black" alignment="M"/>
			<stylegroup id="6" name="header_row" class="HEAD" font="Helvetica" size="9" weight="bold" style="normal" color="black"/>
			<stylegroup id="7" name="header_row1" class="HEAD" font="Helvetica" size="9" weight="bold" style="normal" color="black" underline_type="1"/>
            <!-- Header shading for numbers -->
    		<stylegroup id="8" name="header_CN" class="REPCOLHDR" type="number" />
			<stylegroup id="9" name="trans_row" font="Helvetica" size="8" style="normal" weight="normal" color="black"/>
			<stylegroup id="10" name="trans_CT" class="W1" font="Helvetica" size="9" style="normal" weight="normal" color="black" type="textonly" align = "left" />
			<stylegroup id="11" name="trans_CN" class="R" type="currency"/>
			<stylegroup id="12" name="total_row" font="Helvetica" size="9" style="normal" weight="bold" color="black" underline_type="2" totals_line="Y"/>
			<stylegroup id="13" name="total_CT" class="DGB" /> 
		    <stylegroup id="14" name="total_CN" class="REPTOTR" type="currency" />
			<stylegroup id="15" name="dateformat" type="date"/>
			<stylegroup id="16" name="total_row1" font="Helvetica" size="9" style="normal" weight="bold" color="black" underline_type="1" />
			<stylegroup id="17" name="total_row2" font="Helvetica" size="9" style="normal" weight="bold" color="black" underline_type="1" totals_line="Y"/>
			<stylegroup id="18" name="total_row3" font="Helvetica" size="9" style="normal" weight="bold" color="black" underline_type="2" />
			<stylegroup id="19" name="total_row4" font="Helvetica" size="9" style="normal" weight="bold" color="black" totals_line="Y" />
            <stylegroup id="20" name="total_CT" class="DGB" type="textonly"/>
            <stylegroup id="21" name="trans_CT" class="W" type="textonly"/>

		    <!--<xsl:choose>
              <xsl:when test="($report_format='_excel')">
                <stylegroup id="21" name="trans_CT" class="W" type="textonly"/>
  		      </xsl:when>
  		      <xsl:otherwise>
                <stylegroup id="21" name="trans_CT" class="W1" type="textonly"/>
  		      </xsl:otherwise>
		    </xsl:choose>-->

    		<stylegroup id="22" name="header_col_text" class="REPCOLHDL" type="text" />
		    <stylegroup id="23" name="grand_total_CN"  class="REPGTOTR" type="currency" />
			<stylegroup id="24" name="total_CT" class="DGB2" aligh="right" type="currency" />
		</stylegroups>
	</xsl:template>
<xsl:template name="script">
<script>
<!-- Drill Down function which called from drill down template down below... -->
// all js global vars for functions to work
// vars are initialized with values from source xsl
var gSess = '<xsl:value-of select="/reportdata/tbreport/@sess"/>';

var curloc = '<xsl:value-of select="/reportdata/tbreport/@locationcontext"/>'; 
var ismegl = '<xsl:value-of select="/reportdata/tbreport/@ismegl"/>'; 
var companyid = '<xsl:value-of select="/reportdata/tbreport/@companyid"/>';

var op = '<xsl:value-of select="/reportdata/tbreport/@op"/>';

var jsonfilter = '<xsl:value-of select="/reportdata/tbreport/@jsonfilter"/>';

var dept = '<xsl:value-of select="/reportdata/tbreport/@DEPTNAME"/>';
var loc = '<xsl:value-of select="/reportdata/tbreport/@LOCNAME1"/>';
var selPeriodIndex = '<xsl:value-of select="/reportdata/tbreport/@PERIOD"/>';
var asofdate = '<xsl:value-of select="/reportdata/tbreport/@ASOFDATE"/>';
var startdate = '<xsl:value-of select="/reportdata/tbreport/@STARTDATE"/>';
var enddate = '<xsl:value-of select="/reportdata/tbreport/@ENDDATE"/>';
var ytdb = '<xsl:value-of select="/reportdata/tbreport/@YTDBAL"/>';
ytdb = (ytdb == '1') ? true : false;
var reportingBook = '<xsl:value-of select="/reportdata/tbreport/@REPORTINGBOOK"/>';
</script>
</xsl:template>

</xsl:stylesheet>
