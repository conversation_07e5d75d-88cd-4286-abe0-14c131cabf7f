<?
/**
 * File RenewalBaseHandler.cls
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2015 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */


abstract class RenewalBaseHandler
{
    const FUTURE = 'FUTURE';
    const PAST = 'PAST';
    const NONE = 'NONE';

    /**
     * @var bool $isMCPEnabled
     */
    protected $isMCPEnabled;

    /**
     * @var string $basecurr
     */
    protected $basecurr;

    /**
     * @var array|null $renewalMacroObj
     */
    private $renewalMacroObj;

    /**
     * @var IAEmail $emailObj
     */
    private $emailObj;

    /**
     * @var array $emailTemplate
     */
    private $emailTemplate;

    /**
     * @var array $objForEmailInjection
     */
    private $objForEmailInjection;

    /**
     * @var Injector $injector
     */
    private $injector;

    /** @var bool $needEmailNotification */
    private $needEmailNotification = true;

    public function __construct()
    {
        $this->renewalMacroObj = null;
        $this->isMCPEnabled = ContractUtil::isMCPEnabled();

        if ($this->isMCPEnabled) {
            $this->basecurr = GetBaseCurrency();
        }
    }

    /**
     * @return bool
     */
    public function isNeedEmailNotification()
    {
        return $this->needEmailNotification;
    }

    /**
     * @param bool $needEmailNotification
     */
    public function setNeedEmailNotification($needEmailNotification)
    {
        $this->needEmailNotification = $needEmailNotification;
    }

    /**
     * @return array|null
     */
    protected function getRenewalMacroObj()
    {
        return $this->renewalMacroObj;
    }

    /**
     * @param array $renewalMacroObj
     */
    public function initRenewalMacroObj($renewalMacroObj)
    {
        $this->renewalMacroObj = $renewalMacroObj;
    }

    /**
     * @param int $renewalMacroKey
     *
     * @return array|false
     */
    protected function initRenewalMacroObjWithKey($renewalMacroKey)
    {
        $retVal = $this->renewalMacroObj;
        if ($retVal !== null) {
            // ensure the same key if not, just clear the data member
            if ($retVal['MACROID'] != $renewalMacroKey) {
                $retVal = null;
            }
        }
        if ($retVal === null) {
            $mgr = Globals::$g->gManagerFactory->getManager('renewalmacro');
            $retVal = $mgr->get($renewalMacroKey);
            if ($retVal !== false) {
                //  The BLOB columns are not exposed to the Select statement
                //  SO need to make this call.
                if (!isArrayValueProvided($retVal, 'CUSTOMEREMAILTEMPLATE')) {
                    $retVal['EMAILCONTACTBLOB'] = $mgr->GetEmailContactBlob($retVal['RECORDNO']);
                }
                if (!isArrayValueProvided($retVal, 'ALERTEMAILTEMPLATE')) {
                    $retVal['EMAILALERTBLOB'] = $mgr->GetEmailAlertBlob($retVal['RECORDNO']);
                }
            }
        }
        $this->renewalMacroObj = $retVal;
        return $retVal;
    }

    /**
     * @param string      $scheduleStartDate
     * @param string      $prop
     * @param string|null &$eventDate
     *
     * @return string self::FUTURE if need to create future scheduler entry for eventDate.  self::PAST if need to
     *                create past schedule entry for eventDate.  self::NONE otherwise
     */
    protected function checkCreateSchedulerEntry($scheduleStartDate, $prop, &$eventDate)
    {
        $retVal = self::NONE;
        $eventDate = null;
        $this->checkCreateSchedulerEntryHelper($scheduleStartDate, $prop, $eventDate);
        if ($eventDate !== null) {
            if (SysDateCompare($eventDate, GetCurrentDate()) <= 0) {
                // If schedules are in past date then bundling into single schedules.
                $retVal = self::PAST;
            } else {
                $retVal = self::FUTURE;
            }
        }

        return $retVal;
    }

    /**
     * @param string      $scheduleStartDate
     * @param string      $prop
     * @param string|null &$eventDate
     * @param bool        $isEvergreen
     */
    protected function checkCreateSchedulerEntryHelper($scheduleStartDate, $prop, &$eventDate, $isEvergreen=false)
    {
        if ($prop !== null) {
            $renewalMacroObj = $this->renewalMacroObj;
            if (isset($renewalMacroObj[$prop]) && RenewalMacroManager::isCreateTransactionSelected($renewalMacroObj[$prop], $isEvergreen)) {
                //create schedule
                $propWhen = $prop . 'WHEN';
                $propDays = $prop . 'DAYS';
                $days = $renewalMacroObj[$propWhen];
                $afterOrBefore = $renewalMacroObj[$propDays];

                $noOfDaysToAdd = self::decideRenewalNoOfDays($days, $afterOrBefore);
                $tmpDate = AddDays($scheduleStartDate, $noOfDaysToAdd);
                if ($tmpDate === false) {
                    $eventDate = null;
                } else {
                    assert(is_string($tmpDate));
                    $eventDate = $tmpDate;
                }
            }
        } else {
            $eventDate = $scheduleStartDate;
        }
    }

    /**
     * @param string $entity
     * @param string $action
     * @param string $description
     * @param array  $bodyData
     * @param string $eventDate
     *
     * @return int|null scheduled operation record# if successfull, null otherwise
     */
    public function createSchedulerEntry($entity, $action, $description, $bodyData, $eventDate)
    {
        // Schedule IMS jobs as system user to prevent potential permission issue when user became inactive later
        static $systemUserKey;
        if (!isset($systemUserKey)) {
            // System user should exist in all companies but check to make sure
            $userInfoMgr = Globals::$g->gManagerFactory->getManager('userinfo');
            $res = $userInfoMgr->GetList(['usemst' => true, 'selects' => ['RECORDNO'],
                                          'filters' => [[['RECORDNO', '=', ContractUtil::SYSTEM_USER_ID]]]]);
            $systemUserKey = $res[0]['RECORDNO'] ?? '';
        }
        $origUserId = Globals::$g->_userid;
        try {
            if ($systemUserKey) {
                [, $restOfUserId] = explode('@', $origUserId, 2);
                Globals::$g->_userid = $systemUserKey . '@' . $restOfUserId;
            }
            $retVal = null;
            $ok = $this->createSchedule($description, $eventDate, $schedule, $bodyData['CONTRACTID'] ?? '');
            if ($ok) {
                $ok = $this->createOperation($bodyData, $entity, $action, $schedule, $operation);
                if ($ok) {
                    $ok = $this->createScheduledOperation($schedule, $operation, $scheduledOperation);
                    if ($ok) {
                        $retVal = $scheduledOperation[':record#'];
                    }
                }
            }
            return $retVal;
        } finally {
            Globals::$g->_userid = $origUserId; // restore original user
        }
    }

    /**
     * @param int    $days
     * @param string $afterOrBefore
     *
     * @return int
     */
    public static function decideRenewalNoOfDays($days, $afterOrBefore)
    {
        if ( $afterOrBefore == RenewalMacroManager::SALES_TRANS_BEFORE ) {
            return $days * -1;
        } else if ( $afterOrBefore == RenewalMacroManager::SALES_TRANS_AFTER ) {
            return $days * 1;
        }
        throw new IAException("Internal error. Invalid value for afterOrBefore: $afterOrBefore");
    }

    /**
     * @return bool|string
     */
    private function generateUniqueName()
    {
        // For generating unique name for operation, schedule, scheduledoperation, document
        srand((double)microtime() * 10000000);
        [$msec, $sec] = explode(' ', microtime());
        $date = getdate($sec);
        extract($date);
        /** @noinspection PhpUndefinedVariableInspection */
        $timestamp = "$year.$mon.$mday $hours:$minutes:$seconds $msec";
        $timestamp = isl_substr($timestamp, 0, isl_strlen($timestamp) - 2);
        //eppp_p($timestamp);
        return $timestamp;
    }

    /**
     * @param array &$schedule
     */
    protected function tweakScheduleObject(&$schedule)
    {
        // Allow subclass to tweak the schedule object
    }

    /**
     * @param string $description
     * @param string $eventDate
     * @param array  &$schedule
     * @param string $contractId
     * @return bool
     */
    private function createSchedule($description, $eventDate, &$schedule, $contractId)
    {
        $userid = GetMyUserid(1);
        $curdate = GetCurrentDate();
        $UniqueName = $this->generateUniqueName();

        $maxLen = 100;
        $contractIdStr = empty($contractId) ? '' : ($contractId . '-');
        $schName = $UniqueName . '-' . $contractIdStr . $description;
        $schedule['NAME'] = substr($schName, 0, $maxLen);
        $schedule['DESCRIPTION'] = $description;
        $schedule['USERNO'] = $userid;
        $schedule['WHENCREATED'] = $curdate;
        $schedule['STARTDATE'] = $eventDate;
        $schedule['EXECTYPE'] = 'Automatic';

        if (DateCompare($eventDate, $curdate) < 0) {
            // When Event Date is in past, scheduler runs in nonblocking mode immediately, 
            // hence mode is immediate
            $schedule['EXECTYPE'] = 'Immediate';
        }

        $schedule['ENDDATE'] = $eventDate;
        $schedule['REPEATBY'] = 'None';
        $schedule['REPEATDAYS'] = '';
        $schedule['REPEATMONTH'] = '';
        $schedule['REPEATWEEK'] = '';

        $schedule['REPEATINTERVAL'] = '1';
        $schedule['REPEATCOUNT'] = '1';

        $schedule['LASTEXECDATE'] = '';
        $schedule['EXECCOUNT'] = 0;
        $schedule['STATUS'] = 'active';

        $this->tweakScheduleObject($schedule);

        $schedMgr = Globals::$g->gManagerFactory->getManager('schedule');
        $ok = $schedMgr->add($schedule);
        return $ok;
    }

    /**
     * @param array  $bodyData
     * @param string $entity
     * @param string $action
     * @param array  $schedule
     * @param array  &$operation
     *
     * @return bool
     */
    private function createOperation($bodyData, $entity, $action, $schedule, &$operation)
    {

        $operation = [];
        $operation['CNY#'] = $schedule['CNY#'];
        $operation['NAME'] = $schedule['NAME'];
        $operation['DESCRIPTION'] = $schedule['DESCRIPTION'];
        $operation['USERNO'] = $schedule['USERNO'];
        $operation['ENTITY'] = $entity;
        $operation['ACTION'] = $action;
        $operation['BODYDATA'] = $bodyData;
        $operation['STATUS'] = 'active';
        $operation['LOCATIONKEY'] = Profile::getProperty('LOCATIONKEY');

        $OperMgr = Globals::$g->gManagerFactory->getManager('operation');
        $ok = $OperMgr->add($operation);
        return $ok;
    }

    /**
     * @param array $schedule
     * @param array $operation
     * @param array &$scheduledOperation
     *
     * @return bool
     */
    private function createScheduledOperation($schedule, $operation, &$scheduledOperation)
    {
        $schedKey = $schedule[':record#'];
        $operKey = $operation[':record#'];

        $scheduledOperation = array();
        $scheduledOperation['NAME'] = $schedule['NAME'];
        $scheduledOperation['OPERATION']['NAME'] = $schedule['NAME'];
        $scheduledOperation['SCHEDULE']['NAME'] = $schedule['NAME'];
        $scheduledOperation['DESCRIPTION'] = $schedule['DESCRIPTION'];
        $scheduledOperation['USERNO'] = $schedule['USERNO'];
        $scheduledOperation['CNY#'] = $schedule['CNY#'];
        $scheduledOperation['STATUS'] = 'active';
        $scheduledOperation['SCHEDULE#'] = $schedKey;
        $scheduledOperation['OPERATION#'] = $operKey;
        $scheduledOperation[':schedule#'] = $schedKey;
        $scheduledOperation[':operation#'] = $operKey;

        $SchedOperMgr = Globals::$g->gManagerFactory->getManager('scheduledoperation');
        $ok = $SchedOperMgr->add($scheduledOperation);
        return $ok;

    }

    /**
     * This fuctions is execute renewal schedules one by in sequence under single process
     * Its mainly done to bring all schedules under single ims process for past date schedules
     * Takes bodyarray of schedule operation and perform execution for all past schedules.
     *
     * @param array $msg
     *
     * @return bool combined success of each schedule.
     */
    public function ExecutePastRenewals($msg)
    {

        $pastSchedules = $msg['OPERATION']['BODYDATA']['PASTSCHEDULES'];
        $ok = true;

        foreach ($pastSchedules as $pastSched) {
            $status = $this->{$pastSched['FUNCTION']}($msg);
            //Overall Status (some may fail or success), still we need to show status for each schedules which got failed 
            //during this process, so IMS Log shows Error happened during this process.
            $ok = $ok && $status;
        }
        return $ok;
    }

    /**
     * @return IAEmail
     */
    protected function initEmailObject()
    {
        $emailObj = new IAEmail();

        //  This is required to send an HTML Email.
        $emailObj->contenttype = 'text/html; charset="' . isl_get_charset() . '"';
        $this->emailObj = $emailObj;
        return $emailObj;
    }

    /**
     * @return IAEmail
     */
    public function getEmailObj()
    {
        return $this->emailObj;
    }


    /**
     * @param string $entityId
     *
     * @return string
     */
    protected function getCustomerRepEmail($entityId)
    {
        $cny = GetMyCompany();
        $sql = [];
        //  Get the TD Customer Details
        $sql[0] = "SELECT CUSTOMERID||'--'||NAME CUSTOMER_NAME FROM CUSTOMER WHERE CNY#=:1 AND ENTITY=:2";
        $sql[1] = $cny;
        $sql[2] = 'C' . $entityId;

        $customer = QueryResult($sql);
        $customerID = $customer[0]['CUSTOMER_NAME'];
        $customerMgr = Globals::$g->gManagerFactory->getManager('customer');
        $customer = $customerMgr->get($customerID);

        $empMgr = Globals::$g->gManagerFactory->getManager('employee');
        $custRep = $empMgr->get($customer['CUSTREPID']);

        $custRepEmail = ($custRep['PERSONALINFO']['EMAIL1'] ?: $custRep['PERSONALINFO']['EMAIL2']);
        return $custRepEmail;
    }

    /**
     * @return Injector
     */
    protected function getInjector()
    {
        if (!isset($this->injector)) {
            $this->injector = new Injector();
        }
        return $this->injector;
    }

    /**
     * @param string $emailText
     * @param array  $obj
     * @param string $objIdKey
     *
     * @return bool
     */
    protected function populateEmailBody($emailText, $obj, $objIdKey)
    {
        $ok = true;
        $emailObj = $this->getEmailObj();

        //  Use Injector to Replace Injections inside the email text uploaded by the user.
        $injector = $this->getInjector();
        $actualEmailContent = $injector->inject($emailText, $obj);

        //  WE NEED TO HAVE A WAY TO VERIFY THE INJECTORS AT THE TIME OF UPLOADING ONLY
        if ($actualEmailContent == INVALID_INJECTOR) {
            $objId = $obj[$objIdKey];
            $emailObj->add_attachment($actualEmailContent, $objId . '.html', 'text/xml');
            $emailObj->body = 'Invalid Injectors Used<br>';
            $ok = false;
        } else {
            $emailObj->body = $actualEmailContent;
        }
        return $ok;
    }

    /**
     * @param string $emailTemplateId
     * @param array  $obj
     *
     * @return bool
     */
    public function populateEmailObjFromEmailTemplate($emailTemplateId, $obj)
    {
        $ok = true;
        $emailObj = $this->getEmailObj();

        $emailTemplateMgr = Globals::$g->gManagerFactory->getManager('emailtemplate');
        $emailTemplate = $emailTemplateMgr->get($emailTemplateId);
        $this->emailTemplate = $emailTemplate;
        $this->objForEmailInjection = $obj;

        $toEmail = $this->getEmailTemplateElement('TOADDRESS');
        $ccEmail = $this->getEmailTemplateElement('CCADDRESS');
        $bccEmail = $this->getEmailTemplateElement('BCCADDRESS');
        $sendersEmail = $this->getEmailTemplateElement('FROMADDRESS');
        $sendersName = $this->getEmailTemplateElement('FROMNAME');
        if (!empty($sendersName)) {
            $mailFrom = $sendersName . " <" . $sendersEmail . ">";
        } else {
            $mailFrom = $sendersEmail;
        }
        $subject = $this->getEmailTemplateElement('SUBJECT');

        $body = $this->getEmailTemplateElement('BODY');
        EmailTemplateManager::replaceNewLineWithBR($body, false);
        if (EmailTemplateManager::addLogoImage($emailTemplate, $body)) {
            $ok = EmailTemplateManager::getLogoAsEmbeddedContent($emailTemplate, $embeddedContent);

            if ($ok && $embeddedContent !== null) {
                $emailObj->set_embedded_content($embeddedContent);
            }
        }

        if (!isset($toEmail) || $toEmail == '') {
            $msg = "Email to address is not specified in the email template {$emailTemplate['EMAILTEMPLATENAME']}";
            Globals::$g->gErr->addIAError('SO-0044', __FILE__ . ':' . __LINE__,"",[], $msg,["EMAIL_TEMPLATE" => $emailTemplate['EMAILTEMPLATENAME']]);
            $this->sendErrorNotification();
            $ok = false;
        } else {
            $emailObj->to = $toEmail;
            $emailObj->cc = $ccEmail;
            $emailObj->bcc = $bccEmail;
            $emailObj->_from = $mailFrom;
            $emailObj->_reply_to = $mailFrom;
            $emailObj->subject = $subject;
            $emailObj->body = $body;
        }

        return $ok;
    }

    /**
     * @param string $element
     *
     * @return string
     */
    private function getEmailTemplateElement($element)
    {
        $injector = $this->getInjector();
        $emailTemplate = $this->emailTemplate;
        $obj = $this->objForEmailInjection;
        $retval = $injector->inject($emailTemplate[$element], $obj);

        return $retval;
    }

    /**
     * @param string $emailType
     *
     * @return bool
     */
    protected function sendEmail($emailType)
    {
        $source = "RenewalBaseHandler::sendEmail";
        $emailobj = $this->getEmailObj();

        $this->addWarning();
        $ok = $emailobj->send();
        if (!$ok) {
            if (is_array($emailobj->to)) {
                $emailAddresses = implode(', ', $emailobj->to);
            } else {
                $emailAddresses = $emailobj->to;
            }
            $emailTypeMsg = "Error sending $emailType renewal email to ($emailAddresses) in $source";
            Globals::$g->gErr->addIAError('SO-0045', __FILE__ . ':' . __LINE__,"",[], $emailTypeMsg,["EMAIL_TYPE" => $emailType,"EMAIL_ADDRESSES" => $emailAddresses,"SOURCE" => $source]);
            $this->sendErrorNotification();
        }
        return $ok;
    }

    /**
     * This adds the warning messages to every email sent to the receipent.
     */
    protected function addWarning()
    {
        $emailobj = $this->getEmailObj();
        $bottomMsg = "<br>________________________________________________________________________________________________<br>";
        $bottomMsg .= "<br>This is a system generated alert. We request you not to reply to this message.";
        $bottomMsg .= "<br>This email is confidential and may also be privileged.";
        $bottomMsg .= "<br>If you are not the intended recipient, please notify us immediately.";
        $bottomMsg .= "<br>you should not copy or use it for any purpose, nor disclose its contents to any other person.";
        $bottomMsg .= "<br>________________________________________________________________________________________________<br>";
        $emailobj->body .= $bottomMsg;
    }

    /**
     * Called in conjunction with IMS processing in the envent
     *
     * Send error notification to designated recipient in the event the scheduled
     * event fails. This method will only send errors
     * designated with 'BL' in the error number and there must be an email address
     * value configured with the scheduler for the message to be created and sent.
     *
     * @return bool
     */
    public function sendErrorNotification()
    {
        $cnt = 0;
        $errlines = '';
        $errWarnList = '';
        $errary = array();
        Globals::$g->gErr->GetErrList($errary);

        if (count($errary) > 0) {
            foreach ($errary as $item) {
                $cnt++;
                $txt = "  Error:        " . $item['NUMBER'] . "\n  Description:  " . $item['CDESCRIPTION'] . "\n\n";
                $errlines .= $txt;
            }
            if ($cnt > 0) {
                $errWarnList = "\nFollowing errors detected:-\n--------------------------\n${errlines}";
            }
        }
        $warnings = [];
        Globals::$g->gErr->getWarningsList($warnings);
        if (!empty($warnings)) {
            $warninglines = '';
            foreach ($warnings as $warning) {
                $cnt++;
                $txt = '  Warning:        ' . $warning['NUMBER'] . "\n  Description:  ";
                if (!empty($warning['DESCRIPTION'])) {
                    $txt .= $warning['DESCRIPTION'] . ': ';
                }
                $txt .= $warning['CDESCRIPTION'] . "\n\n";
                $warninglines .= $txt;
            }
            if ($cnt > 0) {
                $errWarnList .= "\nFollowing warnings detected:-\n--------------------------\n${warninglines}";
            }
        }

        if ($cnt <= 0) {
            return true;
        }

        $SendingTo = null;
        // First priority is to send email to company email
        $coEmail = Profile::getCompanyCacheProperty('company', 'CONTACTEMAIL');
        if ($coEmail) {
            $SendingTo = isl_trim($coEmail);
        }

        if (!isset($SendingTo) || $SendingTo == '') {
            //Get the email id of the User who scheduled it.
            $SendingTo = isl_trim(GetMyContactEmail());
            if (!isset($SendingTo) || $SendingTo == '') {
                //Get the contact email id from the
                //Company Information >> General Information page.
                //Which is a mandatory field.
                $SendingTo = isl_trim(GetMyContactEmail(true));
            }
        }

        $emailobj = new IAEmail($SendingTo);
        $this->setErrorNotificationMsg($emailobj, $errWarnList);
        $emailobj->_deliverylog_additional_attributes = ['DUMMY_ATTRIBUTE' => 'DUMMY_VALUE'];   // just to log the body
        $ok = $emailobj->send();

        return $ok;
    }

    /**
     * @param IAEmail $emailobj
     * @param string  $errorList
     */
    abstract protected function setErrorNotificationMsg($emailobj, $errorList);

    /**
     * This cleans up any unwanted characters coming from the UI component where the email address were collected.
     *
     * @param string[] &$SendingTo
     */
    protected function cleanEmailAddresses(&$SendingTo)
    {
        $emailAdds = array();
        for ($index = 0; $index < count($SendingTo); $index++) {
            if ($SendingTo[$index] != '') {
                $emailAdds[] = isl_trim($SendingTo[$index]);
            }
        }
        $SendingTo = $emailAdds;
    }

    /**
     * @param string $source
     *
     * @return bool
     */
    protected function beginTransaction($source)
    {
        return Globals::$g->gQueryMgr->beginTrx($source);
    }

    /**
     * @param string $source
     * @param bool   $noError
     *
     * @return bool
     */
    protected function closeTransaction($source, $noError)
    {
        $ok = $noError && Globals::$g->gQueryMgr->commitTrx($source);
        if (!$ok) {
            Globals::$g->gQueryMgr->rollbackTrx($source);
        }
        return $ok;
    }
}
