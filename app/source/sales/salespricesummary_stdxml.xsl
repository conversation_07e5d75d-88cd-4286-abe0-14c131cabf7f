<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format">

<xsl:variable name="varGroupByCustomer">
	<xsl:value-of select="/reportdata/report/@GROUPBYCUSTOMER"/>
</xsl:variable>

<xsl:variable name="varShowTransactions">
	<xsl:value-of select="/reportdata/report/@SHOWTRANSACTIONS"/>
</xsl:variable>

<xsl:variable name="varGroupByLocation">
	<xsl:value-of select="/reportdata/report/@GROUPBYLOCATION"/>
</xsl:variable>

<xsl:variable name="varMCEnabled">
	<xsl:value-of select="/reportdata/report/@ISMCENABLED"/>
</xsl:variable>

<xsl:variable name="varPriceBasedOn">
    <xsl:value-of select="/reportdata/report/@PRICEBASEDON"/>
</xsl:variable>

<xsl:include href="../../private/xslinc/stylegroup_inc.xsl"/> 
<xsl:include href="../../private/xslinc/inventoryjs_inc.xsl"/> 

	<xsl:template match="report">

		<report
			department 		= "{@department}"
			location 		= "{@location}"
			orientation 	= "Landscape"
			report_date		= "{@reportdate}"
			report_time		= "{@reporttime}"
			align_currency 	= "left"
			page_number 	= "Y"
			action 			= ""
			sess			= "{@sess}"
			done			= "{@done}"
			footer_allpages	= "Y"
		>

			<xsl:if test="(@orientation = 'Portrait')">
				<xsl:attribute name="maxfit">Y</xsl:attribute>
			</xsl:if>
			<company s="2"><xsl:value-of select="@co"/></company>
			<title s="3"><xsl:value-of select="@title"/></title>
			<title s="3"><xsl:value-of select="@title2"/></title>
			<footer s="footer" lines="1"><xsl:value-of select="@titlecomment"/></footer>

			<xsl:call-template name="buildheaderrows"/>
			<xsl:apply-templates/>
			<xsl:call-template name="stylegroups"/>
			<script language="javascript">
			  <xsl:apply-templates select="@javascript"/>
			  <xsl:call-template name="script"/>		
			</script>
</report>
</xsl:template>

<xsl:template name="buildheaderrows">
	<header>
	  <xsl:choose>
			<xsl:when test="$varShowTransactions='Y'">
			  <hrow s="6">
				<hcol id="0" width="15">IA.ITEM</hcol>
				<xsl:if test="$varGroupByCustomer='N'">
				  <hcol id="1" width="15">IA.CUSTOMER_NAME</hcol>
				</xsl:if>
				<xsl:if test="$varGroupByCustomer='T'">
				  <hcol id="1" width="15">IA.CUSTOMER_TYPE</hcol>
				</xsl:if>
				<xsl:if test="$varGroupByLocation='Y'">
				  <hcol id="1" width="15">IA.LOCATION</hcol>
				</xsl:if>
				<hcol id="4" width="10">IA.CURRENCY</hcol>
				<hcol id="4" width="10">IA.UNIT</hcol>
				<hcol id="4" width="10">IA.TRANSACTION_DATE</hcol>
				<hcol id="3" width="17">IA.DOCUMENT_ID</hcol>
				<hcol id="5" width="10">IA.SALES_PRICE</hcol>
				<xsl:if test="$varMCEnabled='Y'">
				  <hcol id="5" width="10">IA.BASE_PRICE</hcol>
				</xsl:if>
			  </hrow>
			  </xsl:when>
			<xsl:otherwise>
			  <hrow s="6">
				<hcol id="0" width="15">IA.ITEM</hcol>
				<xsl:if test="$varGroupByCustomer='N'">
				  <hcol id="1" width="15">IA.CUSTOMER_NAME</hcol>
				</xsl:if>
				<xsl:if test="$varGroupByCustomer='T'">
				  <hcol id="1" width="15">IA.CUSTOMER_TYPE</hcol>
				</xsl:if>
				<xsl:if test="$varGroupByLocation='Y'">
				  <hcol id="1" width="15">IA.LOCATION</hcol>
				</xsl:if>
				<hcol id="4" width="10">IA.CURRENCY</hcol>
				<hcol id="4" width="10">IA.UNIT</hcol>
				<xsl:if test="$varPriceBasedOn='M'">
				  <hcol id="4" width="10">IA.MEDIAN</hcol>
				</xsl:if>
				<xsl:if test="$varPriceBasedOn='A'">
				  <hcol id="4" width="10">IA.AVERAGE</hcol>
				</xsl:if>
				<hcol id="3" width="10">IA.MINIMUM</hcol>
				<hcol id="5" width="10">IA.MAXIMUM</hcol>
				<hcol id="5" width="10">IA.PERCENTAGE_SALES_WITHIN_LIMITS</hcol>
			  </hrow>
			</xsl:otherwise>
		</xsl:choose>
	</header>
</xsl:template>

<xsl:template match="NODATA">
    <xsl:if test="string(@NODATA)=1">
	<body s="1">
		<row s="9">
			<col id="0" s="10" colspan="20">IA.NO_DATA_FOUND_FOR_SELECTED_FILTERS</col>
		</row>
	</body>
    </xsl:if>
</xsl:template>

	<xsl:template match="SALESSUMMARY">
	  <body s="1">
		<xsl:apply-templates/>
      </body>
	</xsl:template>

	<xsl:template match="SALESSUBTOTAL">
	  <xsl:apply-templates/>
	  <xsl:choose>
			<xsl:when test="$varShowTransactions='Y'">
			  <row s="12">
				<col id="1" s="23"><xsl:value-of select="@ITEMNAME"/></col>
				<xsl:if test="$varGroupByCustomer='N' or $varGroupByCustomer='T'">
				  <col id="1" s="23"><xsl:value-of select="@ENTITY"/></col>
				</xsl:if>
				<xsl:if test="$varGroupByLocation='Y'">
				  <col id="1" s="23"><xsl:value-of select="@LOCATION"/></col>
				</xsl:if>
				<col id="1" s="23"><xsl:value-of select="@CURRENCY"/></col>
				<col id="1" s="23"><xsl:value-of select="@UNIT"/></col>
				<col id="1" s="23"><xsl:value-of select="@WHENCREATED"/></col>
				<col id="1" s="23"><xsl:value-of select="@DOCID"/></col>
				<col id="1" s="18"><xsl:value-of select="@TRX_PRICE"/></col>
				<xsl:if test="$varMCEnabled='Y'">
				  <col id="1" s="18"><xsl:value-of select="@UIPRICE"/></col>
				</xsl:if>
			  </row>
			  </xsl:when>
			<xsl:otherwise>
			  <row s="12">
				<col id="1" s="23"><xsl:value-of select="@ITEMNAME"/></col>
				<xsl:if test="$varGroupByCustomer='N' or $varGroupByCustomer='T'">
				  <col id="1" s="23"><xsl:value-of select="@ENTITY"/></col>
				</xsl:if>
				<xsl:if test="$varGroupByLocation='Y'">
				  <col id="1" s="23"><xsl:value-of select="@LOCATION"/></col>
				</xsl:if>
				<col id="1" s="23"><xsl:value-of select="@CURRENCY"/></col>
				<col id="1" s="23"><xsl:value-of select="@UNIT"/></col>
				<col id="1" s="23"><xsl:value-of select="@MEDIAN"/></col>
				<col id="1" s="23"><xsl:value-of select="@MIN"/></col>
				<col id="1" s="23"><xsl:value-of select="@MAX"/></col>
				<col id="1" s="23"><xsl:value-of select="@PCNT"/></col>
			  </row>
			  </xsl:otherwise>
		</xsl:choose>
    </xsl:template>

	<xsl:template match="SUMMARY">
	  <xsl:choose>
			<xsl:when test="$varShowTransactions='Y'">
			  <row s="12">
				<col id="1" s="23"><xsl:value-of select="@ITEMNAME"/></col>
				<xsl:if test="$varGroupByCustomer='N' or $varGroupByCustomer='T'">
				  <col id="1" s="23"><xsl:value-of select="@ENTITY"/></col>
				</xsl:if>
				<xsl:if test="$varGroupByLocation='Y'">
				  <col id="1" s="23"><xsl:value-of select="@LOCATION"/></col>
				</xsl:if>
				<col id="1" s="23"><xsl:value-of select="@CURRENCY"/></col>
				<col id="1" s="23"><xsl:value-of select="@UNIT"/></col>
				<col id="1" s="23"><xsl:value-of select="@WHENCREATED"/></col>
				<col id="1" s="23"><xsl:value-of select="@DOCID"/></col>
				<xsl:if test="@HIGHLIGHT='Y'">
				  <col id="1" s="55"><xsl:value-of select="@TRX_PRICE"/></col>
				</xsl:if>
				<xsl:if test="@HIGHLIGHT='N'">
				  <col id="1" s="56"><xsl:value-of select="@TRX_PRICE"/></col>
			    </xsl:if>
				<xsl:if test="$varMCEnabled='Y'">
				  <xsl:if test="@HIGHLIGHTBASE='Y'">
					<col id="1" s="55"><xsl:value-of select="@UIPRICE"/></col>
				  </xsl:if>
				  <xsl:if test="@HIGHLIGHTBASE='N'">
					<col id="1" s="56"><xsl:value-of select="@UIPRICE"/></col>
				  </xsl:if>
				</xsl:if>
			  </row>
			  </xsl:when>
			<xsl:otherwise>
			  <row s="12">
				<col id="1" s="23"><xsl:value-of select="@ITEMNAME"/></col>
				<xsl:if test="$varGroupByCustomer='N' or $varGroupByCustomer='T'">
				  <col id="1" s="23"><xsl:value-of select="@ENTITY"/></col>
				</xsl:if>
				<xsl:if test="$varGroupByLocation='Y'">
				  <col id="1" s="23"><xsl:value-of select="@LOCATION"/></col>
				</xsl:if>
				<col id="1" s="23"><xsl:value-of select="@CURRENCY"/></col>
				<col id="1" s="23"><xsl:value-of select="@UNIT"/></col>
				<col id="1" s="56"><xsl:value-of select="@MEDIAN"/></col>
				<col id="1" s="56"><xsl:value-of select="@MIN"/></col>
				<col id="1" s="56"><xsl:value-of select="@MAX"/></col>
				<xsl:if test="@HIGHLIGHT='Y'">
				  <col id="1" s="55"><xsl:value-of select="@PCNT"/></col>
				</xsl:if>
				<xsl:if test="@HIGHLIGHT='N'">
				  <col id="1" s="56"><xsl:value-of select="@PCNT"/></col>
				</xsl:if>
			  </row>
			  </xsl:otherwise>
		</xsl:choose>
    </xsl:template>
</xsl:stylesheet>
