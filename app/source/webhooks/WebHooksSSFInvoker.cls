<?php

/**
 *  WebHooksSSFInvoker - invoke BL instances to process the notification based on the issuer and authorized party existing
 * in the notification
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Intacct Corporation All, Rights Reserved
 */

class WebHooksSSFInvoker extends WebHooks
{

    //constants
    const WEBHOOKS_CONFIG_ISSUER_FIELD_NAME = "issuer";
    const WEBHOOKS_CONFIG_CLASS_FIELD_NAME = "component";
    const WEBHOOKS_SSF_INVOKER_CONFIG = "WEBHOOKS_SSF_INVOKER_CONFIG";
    const BL_COMPONENT_BY_ISSUER_AUTHORIZED_PARTY = "BL_COMPONENT_BY_ISSUER_AUTHORIZED_PARTY";
    const WEBHOOKS_CONFIG_AUTHORIZED_PARTY_FIELD_NAME = "authorizedParty";

    /**
     * Webhooks configuration file for issuer->certificate and issuer,authorized party -> BL class
     *
     * @var string $webHooksConfigFileName
     */
    private $webHooksConfigFileName;

    /**
     *  Array containing issuer - authorized party - class loaded from config file
     *
     * @var array $componentByIssuerAndAuthorizedParty
     */
    private $componentByIssuerAndAuthorizedParty = [];

    /**
     * WebHooksSSFInvoker constructor.
     *
     */
    protected function __construct()
    {
        $this->webHooksConfigFileName = $this->webHooksConfig()[self::WEBHOOKS_SSF_INVOKER_CONFIG];
        $sectionName = $this->webHooksConfig()[self::BL_COMPONENT_BY_ISSUER_AUTHORIZED_PARTY];
        $this->componentByIssuerAndAuthorizedParty =
            json_decode(file_get_contents(ia_cfg::getCfgDir() . $this->webHooksConfigFileName), true)[$sectionName];
    }

    /**
     * Based on the issuer and authorized party from notification return the class instance which will process the notification
     *
     * @param string $issuer
     * @param string $authorizedParty
     *
     * @return ISSFNotificationHandler|null
     */
    public function invokeSSFClass(string $issuer, string $authorizedParty) : ?ISSFNotificationHandler
    {
        $result = null;
        //search for class name in webhooks.json.cfg by issuer and authorized party
        $searchResult = ArrayUtils::multiDimmensionArraySearchByMultipleKeys($this->componentByIssuerAndAuthorizedParty,
                                                                             [ self::WEBHOOKS_CONFIG_ISSUER_FIELD_NAME   => $issuer,
                                                                               self::WEBHOOKS_CONFIG_AUTHORIZED_PARTY_FIELD_NAME => $authorizedParty ]);
        if ( ! $searchResult ) {
            self::getWebHooksHTTPResponse()
                ->setHTTPResponseCode(500)
                ->setHTTPResponseBody(WHServerError,
                'Could not find component based on issuer and authorized party');
            self::sendResponse();

            return $result;
        }

        if ( count($searchResult) > 1 ) {
            self::getWebHooksHTTPResponse()
                ->setHTTPResponseCode(500)
                ->setHTTPResponseBody(WHServerError,
                'Too many components found based on issuer and authorized party');
            self::sendResponse();

            return $result;
        }

        $index = $searchResult[0];

        $className = $this->componentByIssuerAndAuthorizedParty[$index][self::WEBHOOKS_CONFIG_CLASS_FIELD_NAME];

        if ( ! class_exists($className) ) {
            logToFileError("Class " . $className . " could not be found ");

            self::getWebHooksHTTPResponse()
                ->setHTTPResponseCode(500)
                ->setHTTPResponseBody(WHServerError,
                                      'Invoked class could not be found');
            self::sendResponse();

            return $result;
        }

        //instantiate the class
        $result = new $className();
        if ( ! $result instanceof ISSFNotificationHandler ) {
            self::getWebHooksHTTPResponse()
                ->setHTTPResponseCode(500)
                ->setHTTPResponseBody(WHServerError,
                'Invalid instance');
            self::sendResponse();

            $result = null;

            return $result;
        }

        return $result;
    }
}

