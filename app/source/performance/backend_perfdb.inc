<?php
/**
 * @package PerformanceLogs
 */

//=============================================================================
//
//	FILE:           backend.perfdb.inc
//	AUTHOR:         bharris
//	DESCRIPTION:    Misc functions to manage db connection to Performance Console DB.
//
//
//	(C)2006, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information herein
//	may not be used, copied or disclosed in whole or part without prior
//	written consent from Intacct Corporation.
//
//=============================================================================

/**
 * Global Performance DB Connection variable
 *
 */
/* @var resource|false|null $gperfdb_connection */
global $gperfdb_connection;
/* @var bool|null $gperfdb_usedefault */
global $gperfdb_usedefault;
/* @var array|null $PerfDBServerCache */
global $PerfDBServerCache;
/* @var int $gPerfDBNumServers */
global $gPerfDBNumServers;
$gperfdb_connection = null;
$gperfdb_usedefault = null;
$PerfDBServerCache = null;
$gPerfDBNumServers = 0;

/**
 * Establish and returns a connection to the Performance Console DB.
 *
 * @deprecated not used anymore
 *
 * @param bool $usedefault
 *
 * @return resource|false|null the connection resource to the Oracle Performance DB.
 */
function GetPerfConnection($usedefault = false)
{
    global $gperfdb_connection, $gperfdb_usedefault;
    /** @noinspection PhpUnusedLocalVariableInspection */
    $conn = null;

    //check if connection has not already been created or
    // $usedefault has not been saved or is different...
    if ( ! isset($gperfdb_connection) || ! isset($gperfdb_usedefault) || $gperfdb_usedefault !== $usedefault ) {
        list($dbu, $dbp, $db) = GetPerfDBLogin($usedefault);

        if ( $_SERVER['SCRIPT_URL'] && isl_substr($_SERVER['SCRIPT_URL'], 0, 9) == '/ia/acct/' ) {
            # avoid plogon from /ia2, /ia3 and ~user sandboxes to keep the total number of oracle connections low
            $conn = oci_pconnect($dbu, $dbp, $db);
            LogToFile("GetPerfConnection: ociplogon logging in as '$dbu@$db'\n");
        } else {
            $conn = oci_connect($dbu, $dbp, $db);
            LogToFile("GetPerfConnection: ocilogon logging in as '$dbu@$db'\n");
        }

        $gperfdb_connection = $conn;        //save the new conn ref in $GLOBAL for later calls
        $gperfdb_usedefault = $usedefault;    //save the new usedefault ref in $GLOBAL for later calls
        LogToFile("GetPerfConnection(new): Connected to Performance Schema\n");
    } else {
        $conn = $gperfdb_connection;
        LogToFile("GetPerfConnection(existing): Connected to Performance Schema\n");
    }

    return $conn;
}

/**
 * Establishes the default login string for development and production.
 *
 * @param bool $usedefault
 *
 * @return array
 */
function GetPerfDBLogin($usedefault = false)
{
    global $PerfDBServerCache;

    if ( ! $PerfDBServerCache && ! $usedefault ) {
        $PerfDBServerCache = InitPerfConnectionCache();
    }

    $login = $PerfDBServerCache['00'];

    if ( ! isset($login) ) {               # try a constant
        $login = GetPerfDefaultLoginString();
    }

    preg_match('/([^\/@]+)\/([^\/@]+)\@([^\/@]+)/', $login, $matches);
    array_shift($matches);

    return $matches;
}

/**
 * Set and return default login value
 *
 * @return string
 */
function GetPerfDefaultLoginString()
{
    global $islive;
    if ( ! $islive ) {
        //we are in development
        $login = 'perf_owner_01/badsin@dev02';
    } else {
        //we are in production
        $login = 'perf_owner_01/badsin@db05';
    }

    return $login;
}

/**
 * Attempts to set the Performance Console DB login in a manner similar
 * to how Intacct App is doing it.
 *
 * @return array A list of connection strings.
 */
function InitPerfConnectionCache()
{
    global $gPerfDBNumServers, $islive;
    $srvConn = [];
    $loginString = '';
    $fd = null;

    //test for application specific environment variable settings.
    if ( isl_preg_match('!/(app\d*)/!i', $_SERVER['SCRIPT_FILENAME'], $matches) ) {
        $app = isl_strtoupper($matches[1]);
        if ( getenv("IA_PERFDB_LOGIN_LIST_${app}") ) {             # try our environment for IA_DB_LOGIN_appX
            $loginString = TrivialCrypt(getenv("IA_PERFDB_LOGIN_LIST_${app}"), 0);
        }
    }

    if ( ! isset($loginString) || $loginString == '' ) {
        if ( getenv("IA_PERFDB_LOGIN_LIST") ) {            # try our environment for IA_DB_LOGIN
            $loginString = TrivialCrypt(getenv("IA_PERFDB_LOGIN_LIST"), 0);
        }
    }

    //else retrieve from a confiuration file.
    if ( ! isset($loginString) || $loginString == '' ) {               # try a config file

        // for production the config file is going to be under /home/<USER>/etc
        if ( $islive ) {
            $filename = SYSETCDIR . 'perflogin.cfg';
            $fd = @fopen($filename, "r");
        }

        // for nonproduction and if config is not found, then fall back on regular
        if ( ! $fd || ! $islive ) {
            $filename = ETCDIR . 'perflogin.cfg';
            $fd = @fopen($filename, "r");
        }

        if ( $fd ) {
            while ( ! feof($fd) ) {
                /** @noinspection PhpUndefinedVariableInspection */
                $values .= fgets($fd, 4096);
            }
            /** @noinspection PhpUndefinedVariableInspection */
            $values = str_replace('IA_PERFDB_LOGIN_LIST=', '', $values);
            $loginString = TrivialCrypt($values, 0);
        }
    }

    $loginString = isl_trim($loginString);
    $databases = explode("\n", $loginString);
    if ( $databases[0] ) {
        $gPerfDBNumServers = count($databases);
    }

    //First attempt to read from env vars, they should be set for production
    for ( $i = 0; $i < $gPerfDBNumServers; $i++ ) {
        $dbstring = isl_trim($databases[$i]);
        if ( $dbstring ) {
            $dbVals = preg_split("/[ ]+/", $dbstring);
            $srvConn[$dbVals[0]] = $dbVals[1];
        }
    }

    //If env were not set, then read from the database table, ideally for development use only
    if ( ! $srvConn || count($srvConn) == 0 ) {
        $srvConn['00'] = GetPerfDefaultLoginString();
        if ( ! $databases || count($databases) == 0 || ! $databases[0] ) {
            $databases = QueryResult("select databaseid, connectstring from database", 0, '', GetPerfConnection(true));
        }
        foreach ( $databases as $DB ) {
            $srvConn[$DB['DATABASEID']] = $DB['CONNECTSTRING'];
        }
    }

    return $srvConn;
}


