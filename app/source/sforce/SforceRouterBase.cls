<?php

/**
 * class SforceRouterBase to route the UI calls from Salesforce to Intacct
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

require_once 'ObjectStoreManager.cls';
require_once 'ims_publish_1.cls';
require_once 'backend_sforce.inc';

abstract class SforceRouterBase
{
    const PROJECT_RECORD_NO_FIELD_KEY = 'RECORD_NO';
    const PROJECT_ID_FIELD_KEY = 'PROJECT_ID';

    /* @var array $sfdcvalues */
    var $sfdcvalues;

    /* @var IAIssueHandler $err */
    var $err;

    /* @var ims_publish_1 $_imspub */
    var $_imspub;

    /* @var ims_publish_1 $_imspubasync */
    var $_imspubasync;

    /* @var string $_sender */
    var $_sender;

    /* @var string $_sforcekey */
    var $_sforcekey;

    /* @var string $_endpoint */
    var $_endpoint;

    /* @var Throttle $throttle */
    protected $throttle;

    function __construct()
    {
        global $gErr; $this->err = $gErr;
        $this->_imspub = new ims_publish_1(
            IMS_MODE_BLOCKING,
            IMS_PROCESS_LOCAL,
            IMS_MODE_QUEUED
        );

        $this->_imspubasync = new ims_publish_1(
            IMS_MODE_NONBLOCKING,
            IMS_PROCESS_REMOTE,
            IMS_MODE_QUEUED
        );
        $this->_sender = 'SFORCEROUTER';

        $_endpoint = &Request::$r->_endpoint;
        $this->_endpoint = $_endpoint;
        $this->throttle = new Throttle();
    }

    function __destruct()
    {
        if( $this->throttle ) {
            unset($this->throttle);
        }
    }

    /**
     * @return SalesforceSetupManager|SforceSetupManager
     */
    abstract protected function getSetupManager();

    /**
     * @param string[] $sfpreferences
     * @param bool     $includeBillToShipTo
     *
     * @return stdClass
     */
    abstract protected function cacheOpportunityDetails($sfpreferences = array(), $includeBillToShipTo = false);

    /**
     * @return stdClass
     */
    abstract protected function cacheSforceIdDetails();

    /**
     * @param string $doctype
     *
     * @return bool
     */
    abstract protected function syncRequiredForDocType($doctype);

    /**
     * @return string
     */
    abstract protected function getModuleID();

    /**
     * Implementations of salesforce router must correctly retrieve and return the prime field
     * value for the project object.
     *
     * @param array $dataSource
     *
     * @return int|string
     */
    abstract protected function getProjectPrimeField($dataSource);

    /**
     * @param string $callId
     * @return string | null
     */
    protected function getfunctionName($callId)
    {
        $functionList = array(
            'SHOW_ARLEDGER' => 'ShowARLedger',
            'SHOW_ARAGING' => 'ShowARAging',
            'SHOW_SHIPMENTS' => 'ShowShipments',
            'SHOW_ORDERANALYSIS' => 'ShowOrderAnalysis',
            'SHOW_SALESANALYSIS' => 'ShowSalesAnalysis',
            'SHOW_ORDERSTATUS' => 'ShowOrderStatus',
            'SHOW_CUSTBACKLOG' => 'ShowCustBacklog',
            'CREATE_SODOCUMENT' => 'CreateSodocument',
            'VIEW_SODOCUMENT' => 'ViewSodocument',
            'DELETE_SODOCUMENT' => 'DeleteSodocument',
            'SHOW_SALESREGISTER' => 'ShowSalesReg',
            'SHOW_INVSTATUS' => 'ShowInvStatus',
            'PRINT_SODOCUMENT' => 'PrintSodocument',
            'CREATE_PROJECT' => 'CreateProject',
            'VIEW_PROJECT' => 'ViewProject',
        );
        return $functionList[$callId] ?? null;
    }

    /**
     * @param string $action
     * @param string $sforcekey
     *
     * @return bool
     */
    public function RouteAction($action, $sforcekey)
    {
        // Check configuration status
        $sfSetupMgr = $this->getSetupManager();
        if ( $sfSetupMgr->IsConfigured() == false ) {
            return false;
        }

        // Get the post cache
        $objstore = new ObjectStoreManager();
        $obj = $objstore->get($sforcekey);
        $this->sfdcvalues = $obj['OBJECTDATA'];

        $this->_sforcekey = $sforcekey;

        $fp = $this->getfunctionName($action);
        if ( empty($fp) ) {
            $gErr = Globals::$g->gErr;
            $gErr->addIAError("SFDC-1208", __FILE__ . '.' . __LINE__, $action . " is not a supported action.",['ACTION'=>$action]);
            $ok = false;
        } else {
            $ok = $this->$fp();
        }
        return $ok;
    }

    /**
     * Add a value to the cache.  This is used for passing values from one page to another without using the URL
     * @access private
     *
     * @param string $field  Field name to set
     * @param string $value  value to set
     */
    protected function _AddValuetoCache($field, $value)
    {
        $objstore = new ObjectStoreManager();
        $obj = $objstore->get($this->_sforcekey);
        $obj['OBJECTDATA'][isl_strtoupper($field)] = $value;
        $objstore->set($obj);
    }

    /**
     * @param string $custId
     *
     * @return bool
     */
    protected function CustomerExist($custId)
    {
        global $gManagerFactory, $gErr;

        if ( $custId == '' ) {
            $gErr->addError(
                'SFDC-1209', __FILE__ . '.' . __LINE__,
                'The Salesforce.com account is not synchronized. Please synchronize the account.');
            return false;
        }

        /** @var CustomerManager $CustMgr */
        $CustMgr = $gManagerFactory->getManager('customer');
        $CustArr = $CustMgr->Get($custId);
        if ( $CustArr === false || !array_key_exists('CUSTOMERID', $CustArr) ) {
            $gErr->addIAError('SFDC-1210', __FILE__ . '.' . __LINE__,
                'The Salesforce.com account references an Intacct customer ID '
                . $custId . ', which does not exist.', ['CUST_ID' => $custId]);
            return false;
        }
        return true;
    }

    /**
     * @param string $page
     * @param array  $args
     * @param int    $op
     */
    protected function GoToPage($page, $args, $op)
    {
        $_oid = Request::$r->_oid;
        $url = "$page?.op=$op&.oid=$_oid";
        foreach ($args as $argkey => $argval) {
            $url .= "&$argkey=" . urlencode(URLCleanParams::insert($argkey, $argval));
        }
        //		echo "url: $url"; exit;
        Fwd(
            $url, "sforceresponse.phtml?.showresult=1&.sforcekey="
            . URLCleanParams::insert('.sforcekey', $this->_sforcekey)
        );
    }

    /**
     * @param string $id
     *
     * @return bool
     */
    protected function isDefinitionOwned($id)
    {
        if ( !IsMultiEntityCompany() ) {
            return true;
        }
        global $gManagerFactory, $gErr;
        $docparMgr = $gManagerFactory->getManager('documentparams');
        $docparArr = $docparMgr->GetLatestRaw($id);
        if (!isset($docparArr[0]['DOCID']) || $docparArr[0]['DOCID'] == '') {
            $gErr->addIAError(
                'SFDC-1211', __FILE__ . '.' . __LINE__,
                "Either the transaction definition '$id' is invalid or it is not owned by the context used for this access.", ['ID' => $id]);
            return false;
        }

        return true;
    }

    /**
     * @param string $docID
     * @return bool
     */
    protected function IsDocumentExists($docID)
    {
        $cny = GetMyCompany();
        $qry = "select 1 from dochdrlite where cny# = :1 and docid = :2";
        $res = QueryResult(array($qry,$cny,$docID));
        if ( !isset($res[0]) && $res[0] == '' ) {
            return false;
        }
        return true;
    }

    /**
     * @param string $toplevelentity
     * @param string $sharedentity
     *
     * @return bool|string
     */
    protected function GetMultiSharedEntityKey($toplevelentity, $sharedentity)
    {
        global $gErr, $gManagerFactory;
        // to retrieve cny# for object entity
        $clientMgr = $gManagerFactory->getManager('client');
        // query shared entity location# for ME shared cny#
        $args = array($toplevelentity, $sharedentity);
        $resLoc = $clientMgr->DoQuery('QRY_CLIENT_SELECT_LOCATION_REC', $args);
        $destLocation = $resLoc[0]['RECORD#'];
        if ( !isset($destLocation) || $destLocation == '' ) {
            // Invalid ME shared entity
            $gErr->addIAError("SFDC-1212", __FILE__ . '.' . __LINE__, "Invalid multi shared entity. No such active entity '$sharedentity' found under multi shared company '$toplevelentity'",
                ['SHAREDENTITY' => $sharedentity, 'TOPLEVELENTITY' => $toplevelentity]);
            return false;
        }
        return $destLocation;
    }

    /**
     * @return bool
     */
    protected function ShowARLedger()
    {
        $customerid = $this->sfdcvalues['CUSTOMERID'];
        if ( !$this->CustomerExist($customerid) ) {
            return false;
        }
        $op = GetOperationId('ar/reports/arledger');
        $args = array('.type' => 'html',
            'FROM' => $customerid,
            'TO' => $customerid,
            'SOT' => 'E',
            'BASEDONDATE' => 'D',
            'PERIOD' => 'Current Year',
            'reporttitle' => 'AR Ledger');
        if ( IsMultiEntityCompany() ) {
            $args['LOCATION'] = $this->sfdcvalues['RPT_ENTITY'];
        }
        $this->GoToPage("reporteditor.phtml", $args, $op);

        return true;
    }

    /**
     * @return bool
     */
    protected function ShowARAging()
    {
        $customerid = $this->sfdcvalues['CUSTOMERID'];
        if ( !$this->CustomerExist($customerid) ) {
            return false;
        }

        include_once 'backend_dates.inc';
        $op = GetOperationId('ar/reports/customeraging_report');
        $args = array('.type' => 'html',
            'AGINGPERIODS' => '0-30,31-60,61-90,91-120',
            'REPORTASOF' => 'Current',
            'SELECTEDDATE' => GetCurrentDate(),
            'BASEDON' => 'Invoicedate',
            'FROMCUSTOMERID' => $customerid,
            'TOCUSTOMERID' => $customerid,
            'REPORTTYPE' => 'Summary',
            'SHOWVENDORSWITHZEROBALANCE' => 'yes',
            'SHOWPAIDINVOUTSTANDINGRETAINAGE' => 'no',
            'reporttitle' => 'Customer Aging Report');
        if ( IsMultiEntityCompany() ) {
            $args['LOCATION'] = $this->sfdcvalues['RPT_ENTITY'];
        }
        if(IsMCMESubscribed()){
            $args['REPORTINGCURRENCY'] = $this->sfdcvalues['RPT_CURRENCY'];
        }
        $this->GoToPage("reporteditor.phtml", $args, $op);

        return true;
    }

    /**
     * @return bool
     */
    protected function ShowShipments()
    {
        $customerid = $this->sfdcvalues['CUSTOMERID'];
        if ( !$this->CustomerExist($customerid) ) {
            return false;
        }

        $op = GetOperationId('so/reports/invcustshiphist');
        $args = array('.type' => 'html',
            'FROMCUSTOMERID' => $customerid,
            'TOCUSTOMERID' => $customerid,
            'reporttitle' => 'Shipment History');
        if ( IsMultiEntityCompany() ) {
            $args['LOCATION'] = $this->sfdcvalues['RPT_ENTITY'];
        }
        $this->GoToPage("reporteditor.phtml", $args, $op);

        return true;
    }

    /**
     * @return bool
     */
    protected function ShowOrderAnalysis()
    {
        $customerid = $this->sfdcvalues['CUSTOMERID'];
        if ( !$this->CustomerExist($customerid) ) {
            return false;
        }

        $op = GetOperationId('so/reports/invorderanalysis');
        $args = array('.type' => 'html',
            'FROMCUSTOMERID' => $customerid,
            'TOCUSTOMERID' => $customerid,
            'reporttitle' => 'Order Analysis');
        if ( IsMultiEntityCompany() ) {
            $args['LOCATION'] = $this->sfdcvalues['RPT_ENTITY'];
        }
        $this->GoToPage("reporteditor.phtml", $args, $op);

        return true;
    }

    /**
     * @return bool
     */
    protected function ShowSalesAnalysis()
    {
        $customerid = $this->sfdcvalues['CUSTOMERID'];
        if ( !$this->CustomerExist($customerid) ) {
            return false;
        }

        $op = GetOperationId('so/reports/soroyaltyanalysis');
        $args = array('.type' => 'html',
            'FROMCUSTOMERID' => $customerid,
            'TOCUSTOMERID' => $customerid,
            'reporttitle' => 'Sales Analysis');
        if ( IsMultiEntityCompany() ) {
            $args['LOCATION'] = $this->sfdcvalues['RPT_ENTITY'];
        }
        $this->GoToPage("reporteditor.phtml", $args, $op);

        return true;
    }

    /**
     * @return bool
     */
    protected function ShowOrderStatus()
    {
        $customerid = $this->sfdcvalues['CUSTOMERID'];
        if ( !$this->CustomerExist($customerid) ) {
            return false;
        }

        $op = GetOperationId('so/reports/sostatus');
        $args = array('.type' => 'html',
            'FROMCUSTOMERID' => $customerid,
            'TOCUSTOMERID' => $customerid,
            'reporttitle' => 'Order Entry Status');
        $this->GoToPage("reporteditor.phtml", $args, $op);

        return true;
    }

    /**
     * @return bool
     */
    protected function ShowCustBacklog()
    {
        $customerid = $this->sfdcvalues['CUSTOMERID'];
        if ( !$this->CustomerExist($customerid) ) {
            return false;
        }

        $op = GetOperationId('so/reports/invcustbacklog');
        $args = array('.type' => 'html',
            'FROMCUSTOMERID' => $customerid,
            'TOCUSTOMERID' => $customerid,
            'reporttitle' => 'Customer Backlog');
        $this->GoToPage("reporteditor.phtml", $args, $op);

        return true;
    }

    /**
     * @return bool
     */
    protected function ShowSalesReg()
    {
        $customerid = $this->sfdcvalues['CUSTOMERID'];
        if ( !$this->CustomerExist($customerid) ) {
            return false;
        }

        $op = GetOperationId('ar/reports/salesregister');
        $args = array('.type' => 'html',
            'CUSTOMER' => $customerid,
            'reporttitle' => 'Sales Register');
        $this->GoToPage("reporteditor.phtml", $args, $op);

        return true;
    }

    /**
     */
    protected function ShowInvStatus()
    {
        $itemid = $this->sfdcvalues['ITEMID'];

        $op = GetOperationId('inv/reports/invstatus');
        $args = array('.type' => 'html',
            'FROMITEMID' => $itemid,
            'TOITEMID' => $itemid,
            'reporttitle' => 'Inventory Status');
        if ( IsMultiEntityCompany() ) {
            $rpt_ent = $this->sfdcvalues['RPT_ENTITY'];
            $args['LOCATION'] = $rpt_ent;
            if($rpt_ent != '') {
                //Below line of code is to pass the as of date field to work for entity based filter
                //If we get better fix in InvStatusReporter.cls then do there.
                $args['ASOFDATE'] = date("m/d/Y");
            }
        }
        $this->GoToPage("reporteditor.phtml", $args, $op);
    }

    /**
     * @return bool
     */
    protected function PrintSodocument()
    {
        $documentid = $this->sfdcvalues['DOCUMENTTYPE'] . '-' . $this->sfdcvalues['DOCUMENTID'];

        if ( !$this->IsDocumentExists($documentid) ) {
            global $gErr;
            $errMsg = "The document '$documentid' does not exist.";
            $gErr->addIAError("SFDC-1213", __FILE__ . '.' . __LINE__, $errMsg, ['DOCUMENTID' => $documentid]);
            return false;
        }

        $op = GetOperationId('so/lists/sodocument/view');
        $args = array('.r' => $documentid,
            '.it' => 'sodocument',
            '.action' => 'deliver',
            '.state' => 'deliver',
            '.ppdf' => '1',
            '.deliverymethod' => 'pdf',
            '.dt' => $this->sfdcvalues['DOCUMENTTYPE']);
        $this->GoToPage("editor.phtml", $args, $op);


        return true;
    }

    /**
     * @return bool
     */
    protected function CreateProject()
    {
        if ( !$this->IsSyncProjectTaskSubscribed() ) {
            return false;
        }

        $args = array('.action' => 'new');
        $projectid = $this->sfdcvalues['PROJECTID'];
        if ( isset($projectid) && $projectid != '' ) {
            if ( !$this->ProjectExist($projectid) ) {
                return false;
            }
            $args = array('.action' => 'copy', '.r' => $projectid);
        }

        // Customer is not a mandatory field for project so if SFDC request has CUSTOMERID, check for existence in Intacct
        $customerid = $this->sfdcvalues['CUSTOMERID'];
        if ( isset($customerid) && $customerid != '' ) {
            if ( !$this->CustomerExist($customerid) ) {
                return false;
            }
        }

        $this->cacheSforceIdDetails();

        // forward the user.
        $op = GetOperationId('pa/lists/project/create');

        $authorized = CheckAuthorization($op, '', true);
        if(!$authorized){
            $op = GetOperationId('ar/lists/project/create');
        }

        $this->GoToPage("editor.phtml", $args, $op);

        return true;
    }

    /**
     * @return bool
     */
    protected function ViewProject()
    {
        if ( !$this->IsSyncProjectTaskSubscribed() ) {
            return false;
        }

        $projectid = $this->sfdcvalues['PROJECTID'];
        if ( !$this->ProjectExist($projectid, true) ) {
            return false;
        }

        // forward the user.
        $op = GetOperationId('pa/lists/project/view');

        $authorized = CheckAuthorization($op, '', true);
        if(!$authorized){
            $op = GetOperationId('ar/lists/project/view');
        }

        $args = array('.action' => 'view', '.r' => $projectid);
        $this->GoToPage("editor.phtml", $args, $op);

        return true;
    }

    /**
     * @return mixed
     */
    protected function getModulePreferences()
    {
        $kSFORCEid = $this->getModuleID();
        GetModulePreferences($kSFORCEid, $sfpreferences);
        return $sfpreferences;
    }

    /**
     * @return bool
     */
    protected function IsSyncProjectTaskSubscribed()
    {
        $gErr = Globals::$g->gErr;
        $sfpreferences = $this->getModulePreferences();

        if( $sfpreferences['SYNCPROJECTTASK'] != 'true' ) {
            $gErr->addError('SFDC-1214', __FILE__ . '.' . __LINE__, "Unable to synchronize project. Salesforce configuration for 'Synchronize Projects & Tasks' is not enabled for this company");
            return false;
        }
        return true;
    }

    /**
     * @param string $projId
     * @param bool   $sync
     *
     * @return bool
     */
    protected function ProjectExist($projId, $sync = false)
    {
        $gErr = Globals::$g->gErr;

        if( $projId == '' ) {
            $gErr->addError('SFDC-1215', __FILE__ . '.' . __LINE__, 'Project ID is missing.');
            return false;
        }

        $cny = GetMyCompany();
        $qry = "select RECORD# from PROJECT where PROJECTID = :1 and CNY# = :2";
        $res = QueryResult(array($qry, $projId, $cny));

        if ( empty($res) ) {
            $gErr->addIAError('SFDC-1216', __FILE__ . '.' . __LINE__,
                'The Project "' . $projId
                . '" you have selected in Salesforce is not a valid Project ID in Intacct. Please verify the Project ID in both systems match, or select a different project.', ['PROJ_ID' => $projId]);
            return false;
        }

        if ( $sync ) {
            $dataSource =
                [ self::PROJECT_ID_FIELD_KEY => $projId, self::PROJECT_RECORD_NO_FIELD_KEY => $res[0]['RECORD#'] ];
            $primeFieldValue = $this->getProjectPrimeField($dataSource);

            $objIMSSub = new IMSSubscriptionManager();
            if ( !$objIMSSub->GetExternalId($primeFieldValue, 'PROJECT', 'SFORCE') ) {
                $gErr->addIAError(
                    'SFDC-1217', __FILE__ . '.' . __LINE__,
                    $projId . ' is not synced with project in SFDC.', ['PROJ_ID' => $projId]);
                return false;
            }
        }

        return true;
    }

/**
     * @param string[] $call_results
     * @param bool     $valid_empty
     * @param string   $default_message
     *
     * @return bool
     */
    public function sforceRouterErrorHandler($call_results, $valid_empty = false, $default_message = "Internal Error.")
    {
        global $gErr;
        $show_error = false;
        if ( count($call_results) > 0 ) {
            $call_result = $call_results['CALL_RESULT'];
            //$result_type = $call_results['RESULT_TYPE'];
            $reason = $call_results['REASON'] ?? null;
            if ( $call_result == 'ERROR' ) {
                $gErr->addError('SFDC-1218', __FILE__ . '.' . __LINE__, $reason);
                $show_error = true;
                logStackTraceBrief();
            } else if ( $call_result == 'EXCEPTION' ) {
                /** @noinspection PhpUndefinedVariableInspection */
                if ( $results['EXCEPTION_MESSAGE'] != null ) {
                    $gErr->addError('SFDC-1219', __FILE__ . '.' . __LINE__, $results['EXCEPTION_MESSAGE']);
                }
                $gErr->addError('SFDC-1218', __FILE__ . '.' . __LINE__, $reason);
                $show_error = true;
                logStackTraceBrief();
            }
        } else {
            if ( $valid_empty == false ) {
                $gErr->addError('SFDC-1220', __FILE__ . '.' . __LINE__, $default_message);
                $show_error = true;
                logStackTraceBrief();
            }
        }
        return $show_error;
    }

    /**
     * @return bool
     */
    protected function CreateSodocument()
    {
        global $gErr;

        // If attempting to call ADD/SET/DELETE for SODOCUMENT,
        // for salesforce sync purpose, check if the document type,
        // requires to be sync'ed according to SFDC module config

        if ( !$this->syncRequiredForDocType($this->sfdcvalues['DOCTYPE']) ) {
            $gErr->addIAError(
                'SFDC-1130',
                __FILE__.'.'.__LINE__,
                "Either the document type is invalid or document type '".$this->sfdcvalues['DOCTYPE']."' is not selected to sync with SFDC.",
                ['DOC_PAR_ID' => $this->sfdcvalues['DOCTYPE']],
                "Go to Company-->Services-->Subscription, click on 'Configure' link for 'Intacct-Salesforce Integration' and check the preference value for 'Synchronize Document Types' under 'Transaction Synchronization' tab",
                []
            );
            return false;
        }

        $sfpreferences = $this->getModulePreferences();

        if(!$this->validateSoDocSync($sfpreferences) ) {
            return false;
        }
        //complete code has been taken out into new function, to store information
        /**
        /* @var stdClass $opp
         */
        $opp = $this->cacheOpportunityDetails($sfpreferences, true);

        // if the customer doesn't exist, and the company has turned on customer sync,
        // sync it now before we create the sales order
       
        if ( !$this->syncCustomerFromTrx($opp, $sfpreferences) ) {
            return false;
        }

        if ( !$this->syncContactsFromTrx($opp, $sfpreferences) ) {
            return false;
        }

        $customerid = $this->sfdcvalues['CUSTOMERID'];
        if ( !$this->CustomerExist($customerid) ) {
            return false;
        }

        // For ME shared, check if transaction definition is owned
        // error in case root is trying to access TD owned by entity
        // error in case entity1 is trying to access TD owned by entity2

        if ( !$this->isDefinitionOwned($this->sfdcvalues['DOCTYPE']) ) {
            return false;
        }

        // forward the user.
        $op = GetOperationId('so/activities/sodocument/create');
        $args = array('.dt' => $this->sfdcvalues['DOCTYPE'], '.action' => 'new', '.origin' => 'sforce');
        $this->GoToPage("editor.phtml", $args, $op);

        return true;
    }
    
    /**
     * @return bool
     */
    protected function ViewSodocument()
    {
        $gManagerFactory = Globals::$g->gManagerFactory;

        // If attempting to call ADD/SET/DELETE for SODOCUMENT,
        // for salesforce sync purpose, check if the document type,
        // requires to be sync'ed according to SFDC module config

        //Validation removed for Ticket : 61449
        /*if ( !$this->syncRequiredForDocType($this->sfdcvalues['DOCUMENTTYPE']) ) {
            $gErr->AddError(
                'SFDC Sync',
                __FILE__.'.'.__LINE__,
                "Either the document type is invalid or document type '".$this->sfdcvalues['DOCTYPE']."' is not selected to sync with SFDC.", "Go to Company-->Services-->Subscription, click on 'Configure' link for 'Intacct-Salesforce Integration' and check the preference value for 'Synchronize Document Types' under 'Transaction Synchronization' tab"
            );
            return false;
        }*/

        $docparMgr = $gManagerFactory->getManager('documentparams');
        $docparArr = $docparMgr->GetLatestRaw($this->sfdcvalues['DOCUMENTTYPE']);
        $documentid = $this->sfdcvalues['DOCUMENTTYPE'] . '-' . $this->sfdcvalues['DOCUMENTID'];
        if ( !$this->IsDocumentExists($documentid) ) {
            global $gErr;
            $placeholder = [ 'DOCUMENTID' => $documentid];
            if ( $docparArr[0]['SALE_PUR_TRANS'] == 'T' ) {
                $errorCode = 'SFDC-1228';
                $errMsg = "The TB Document '$documentid' does not exist.";
            } else {
                $errorCode = 'SFDC-1229';
                $errMsg = "The SO Document '$documentid' does not exist.";
            }
            $gErr->addIAError($errorCode, __FILE__ . '.' . __LINE__, $errMsg, $placeholder);
            return false;
        }

        if ( $docparArr[0]['SALE_PUR_TRANS'] != 'T' ) {
            $op = GetOperationId('so/lists/sodocument/view');
            $do = 'view';
            if ( !CheckAuthorization($op, 'XML') ) {
                $op = GetOperationId('so/lists/sodocument/edit');
                $do = 'edit';
            }
            $this->cacheOpportunityDetails();
            $args = array('.do' => $do,
                '.r' => $documentid,
                '.it' => 'sodocument',
                '.dt' => $this->sfdcvalues['DOCUMENTTYPE'],
                '.origin' => 'sforce');
            $this->GoToPage("editor.phtml", $args, $op);
        }

        return true;
    }

    /**
     * @return bool
     */
    protected function DeleteSodocument()
    {
        global $gManagerFactory,$gErr;

        // If attempting to call ADD/SET/DELETE for SODOCUMENT,
        // for salesforce sync purpose, check if the document type,
        // requires to be sync'ed according to SFDC module config

        if ( !$this->syncRequiredForDocType($this->sfdcvalues['DOCUMENTTYPE']) ) {
            $gErr->addIAError(
                'SFDC-1130',
                __FILE__.'.'.__LINE__,
                "Either the document type is invalid or document type '".$this->sfdcvalues['DOCTYPE']."' is not selected to sync with SFDC.",
                ['DOC_PAR_ID' => $this->sfdcvalues['DOCTYPE']],
                "Go to Company-->Services-->Subscription, click on 'Configure' link for 'Intacct-Salesforce Integration' and check the preference value for 'Synchronize Document Types' under 'Transaction Synchronization' tab",
                []
            );
            return false;
        }

        $docparMgr = $gManagerFactory->getManager('documentparams');
        $docparArr = $docparMgr->GetLatestRaw($this->sfdcvalues['DOCUMENTTYPE']);
        $documentid = $this->sfdcvalues['DOCUMENTTYPE'] . '-' . $this->sfdcvalues['DOCUMENTID'];
        if ( !$this->IsDocumentExists($documentid) ) {
            global $gErr;
            $placeholder = [ 'DOCUMENTID' => $documentid];
            if ( $docparArr[0]['SALE_PUR_TRANS'] == 'T' ) {
                $errorCode = 'SFDC-1228';
                $errMsg = "The TB Document '$documentid' does not exist.";
            } else {
                $errorCode = 'SFDC-1229';
                $errMsg = "The SO Document '$documentid' does not exist.";
            }
            $gErr->addIAError($errorCode, __FILE__ . '.' . __LINE__, $errMsg, $placeholder);
            return false;
        }

        $ret = false;
        if ( $docparArr[0]['SALE_PUR_TRANS'] != 'T' ) {
            // make sure the user has rights to delete the document
            $op = GetOperationId('so/lists/sodocument/delete');
            if ( !CheckAuthorization($op, 'XML') ) {
                $gErr->addError("SFDC-1222", __FILE__ . '.' . __LINE__, "You are not authorized to delete Sales Documents.");
                return false;
            }

            // instantiate the entity manager and delete it.
            global $gManagerFactory;
            $objSodoc =  $gManagerFactory->getManager('sodocument');
            $ret = $objSodoc->Delete($documentid);

            if ( $ret ) {
                // show the result
                include_once 'UIUtils.cls';
                $objUI = new UIUtils();
                $objUI->ShowMessage("Sales Document " . $documentid . ' deleted successfully.', '', 'left');
            }
        }
        return $ret;
    }

    /**
     * Used to build the mail address
     * @param array $response      contains the response
     * @param array $addressFields contains the address fields
     * @return mixed
     */
    protected function BuildMailaddress($response, $addressFields)
    {
        foreach ($addressFields as $fieldKey => $fieldValue) {
            $address[$fieldKey] = $response[$fieldValue];
        }

        /** @noinspection PhpUndefinedVariableInspection */
        return $address;
    }

    /**
     * @param string $sforcekey
     * @param string $endpoint
     * @param string $login
     * @param string $companyid
     * @param string $userKey
     * @param string $mega
     * @param string $root
     * @param string $userentity
     * @param string $objectentity
     * @param string $assertion
     * @param string $iasessiontoken
     *
     * @return string[]
     */
    public function getIntacctCompanyAndUser(
        $sforcekey, $endpoint, &$login, &$companyid, &$userKey, $mega = '',
        $root = '', $userentity = '', $objectentity = '', $assertion = '',
        $iasessiontoken = ''
    ) {
        $results = array();
        $continue = true;

        if ( $endpoint && !$this->validateEndpoint($endpoint) ) {
            $results['CALL_RESULT'] = 'ERROR';
            $results['RESULT_TYPE'] = 'SFDC_LOGIN';
            $results['REASON'] = 'The SOAP endpoint value \''.$endpoint.'\' is not legitimate Salesforce SOAP URI';
            $continue = false;
        }

        if ( $continue == true ) {
            if ( $mega =='client') {
                if( $root != $userentity && $objectentity != $userentity ) {
                    $results['CALL_RESULT'] = 'ERROR';
                    $results['RESULT_TYPE'] = 'SFDC_LOGIN';
                    $results['REASON'] = 'Invalid Action. This object belongs to '.$objectentity.' Entity and User belong to '.$userentity;
                    $continue = false;
                }

                if ( $objectentity == '' && $continue ) {
                    $results['CALL_RESULT'] = 'ERROR';
                    $results['RESULT_TYPE'] = 'SFDC_LOGIN';
                    $results['REASON'] = 'Invalid Action. This object does not assigned to any entity. Assign Intacct Entity before taking action';
                }
            }
        }

        include_once 'IntacctSalesforceClient.php';

        if ( $continue == true ) {

            try {
                GetModulePreferences($this->getModuleID(), $preferences);
                $sandboxInUse = SalesforceUtil::isSalesforceSandboxInUse($preferences['USESFSANDBOX'] ?? null);

                $sfdcClient = new IntacctSalesforceClient($sforcekey, $sandboxInUse, 180);
                if ( $this->_endpoint) {
                    $sfdcClient->setEndpoint($this->_endpoint);
                }
                // get the userinfo using this sforcekey.  If we can do this, the sforcekey is valid
                $userInfo = $sfdcClient->getUserInfo();

                if ( isset($userInfo) && isset($iasessiontoken) && '' !== $iasessiontoken && strlen($iasessiontoken) > 7) {
                    // The salesforce client is valid and the request contains
                    // the Intacct token. Get the intacct session from memcache

                    $podIdStr = substr($iasessiontoken, -7);
                    if ( is_numeric($podIdStr) ) {
                        $podId = (int) $podIdStr;
                        if ( $podId != Globals::$g->gPODManager->getCurrentPOD()->getId() ) {
                            $sessionToken = Globals::$g->gPODManager->podRPC(
                                $podId,
                                ['SforceSyncProcessor', 'fetchInfoForSessionToken'],
                                [$iasessiontoken]
                            );
                        } else {
                            $sessionToken = SforceSyncProcessor::fetchInfoForSessionToken($iasessiontoken);
                        }
                    } else {
                        $sessionToken = false;
                    }
                    if ( $sessionToken ) {
                        $results['CALL_RESULT'] = 'SUCCESS';
                        $companyid = $sessionToken->getCompany();
                        $login = $sessionToken->getLogin();
                        $continue = false;
                    } else {
                        // Cannot find the Intacct session in the memchche
                        // for the given token. ERROR CONDITION
                        $results['CALL_RESULT'] = 'ERROR';
                        $results['RESULT_TYPE'] = 'SFDC_LOGIN';
                        $results['REASON'] = 'Invalid Action. The user cannot be authenticated.';
                    }

                } else if ( isset($userInfo) && isset($assertion) && '' !== $assertion ) {
                    // NO INTACCT TOKEN. PARSE assertion and return company and user login

                    // The salesforce client is valid and there is a JWT
                    // authentication in the header. Read company and user id
                    $oauth = OAuthFactory::createPartnerUserOauth($assertion);
                    $jwtCompanyId = $oauth->getCompanyId();
                    $jwtPartnerUserId = $oauth->getPartnerUserId();
                    if (substr($jwtPartnerUserId, 0, 15) === substr($userInfo->userId, 0, 15) ) {
                        // The SF internal user ID which comes in the assertion
                        // matches to the one queried from the Salesforce
                        $results['CALL_RESULT'] = 'SUCCESS';
                        $companyid = (string)$jwtCompanyId;
                        $login = $oauth->getIntacctUserLoginId();
                        $continue = false;
                    } else {
                        // The JWT in the request header is corrupted.
                        $results['CALL_RESULT'] = 'ERROR';
                        $results['RESULT_TYPE'] = 'SFDC_LOGIN';
                        $results['REASON'] = 'Invalid Action. The user cannot be authenticated.';
                    }

                }
            } catch (Exception $e) {
                $results['CALL_RESULT'] = 'EXCEPTION';
                $results['RESULT_TYPE'] = 'SFDC_LOGIN';
                $results['EXCEPTION_MESSAGE'] = $e->getMessage();
                $results['REASON'] = 'Unable to validate Salesforce.com session is active.';
                $continue = false;
            }
        }
        if ( $continue == true ) {
            try {
                // now that we have the userid, get the user credentials.
                /** @noinspection PhpUndefinedVariableInspection */
                $result = $sfdcClient->retrieve("Intacct_Company_ID__c, Intacct_Login_ID__c, Intacct_User_Key__c", 'User', $userInfo->userId);

            } catch (Exception $e) {
                $results['CALL_RESULT'] = 'EXCEPTION';
                $results['RESULT_TYPE'] = 'SFDC_LOGIN';
                $results['REASON'] = 'Unable to retrieve Salesforce.com user information.';
            }

            $results['CALL_RESULT'] = 'SUCCESS';
            /** @noinspection PhpUndefinedVariableInspection */
            $login = $result[0]->fields->Intacct_Login_ID__c;
            $companyid = $result[0]->fields->Intacct_Company_ID__c;
            $userKey = $result[0]->fields->Intacct_User_Key__c;
        }
        return $results;
    }

    // this should be a static function
    
    /**
     * @param string              $sforcekey
     * @param string              $login
     * @param string              $companyid
     * @param string              $userKey
     * @param string              $mega
     * @param string              $root
     * @param string              $userentity
     * @param string              $objectentity
     * @param string              $accountentity
     * @param string[]            $results
     * @param string              $assertion
     * @param IntacctSession|null $intacctSession
     *
     * @return bool
     * @throws IAException
     */
    public function ValidateUser($sforcekey, $login, $companyid, $userKey,
                                 $mega, $root, $userentity, $objectentity, $accountentity,
                                 &$results, $assertion, IntacctSession $intacctSession = null
    ) {
        $_oid = Request::$r->_oid;
        global $_userid;
        global $kSALESFORCE2id, $kSFORCEid;

        $module = $kSFORCEid;

        if( !$_oid ) {
            logToFileError( "OID ERROR: " . date("Y-m-d H:i:s") . " No OID set for sforcerouter requests comming from " . $_SERVER['REMOTE_ADDR'] . ", company $companyid, user $_userid\n");
        }

        $ok = SetDBSchema($companyid, "loginid");
        if ( !$ok ) {
            return false;
        }

        $destLocation = '';

        $checkForUserKey = true;
        if ( isset($intacctSession) && null !== $intacctSession ) {
            $ok = isset($login);
            if ($ok) {
                // read location
                $destLocation = $intacctSession->getLocation();

                // It is authenticated. Do not check for userKey
                $checkForUserKey = false;
                // All the validations do against Sforce integration v.2 module
                $module = $kSALESFORCE2id;
            }
        } else if ( isset($assertion) && '' !== $assertion ) {
            // Assertion is provided. Implement OAuth authentication
            $oauth = OAuthFactory::createPartnerUserOauth($assertion);
            $login = $oauth->getIntacctUserLoginId();
            $destLocation = $oauth->getLocationId();

            $ok = $oauth->authenticate();
            if ($ok) {
                // It is authenticated. Do not check for userKey
                $checkForUserKey = false;

                // All the validations do against Sforce integration v.2 module
                $module = $kSALESFORCE2id;
            } else {
                logDiagnostics('OAUTH_ERROR', "Oath failed - Assertion: $assertion  Salesforce session: $sforcekey");
                $results['CALL_RESULT'] = 'ERROR';
                $results['RESULT_TYPE'] = 'SETUP';
                $results['REASON'] = 'Cannot authenticate the user. Check your Salesforce.com user settings.';
                return false;
            }
        }
        $ok = $ok && GetUserid($login, $companyid, $_userid);
        if ( !$ok ) {
            $results['CALL_RESULT'] = 'ERROR';
            $results['RESULT_TYPE'] = 'SETUP';
            $results['REASON'] = 'Intacct User ID or Company ID is invalid.  Check your Salesforce.com user settings.';
            return false;
        }

        /* Please remove the unused var from the list rather than noinspecting it. noinspection PhpUnusedLocalVariableInspection */
        [$userrec] = explode('@', $_userid);
        if( ! $this->throttle->setThrottle(array($userrec . '_sforcerouter', 'sforcerouter')) ) {
            $results['CALL_RESULT'] = 'ERROR';
            $results['RESULT_TYPE'] = '**********';
            $results['REASON'] = 'There are too many operations running for this company ';
            return false;
        }
        if ( $checkForUserKey ) {
            // for the given login and company, make sure the sforce user is allowed to access.
            $qry = "select VALUE, USERINFO.STATUS as USERSTATUS from USERPREF, USERINFO, COMPANY where PROPERTY = 'SFORCEUSERKEY' " .
                "and USERPREF.USERREC = USERINFO.RECORD# and USERINFO.CNY# = COMPANY.RECORD# " .
                "and USERPREF.CNY# = USERINFO.CNY# " .
                "and COMPANY.TITLE = :1 and USERINFO.LOGINID = :2";
            $res = QueryResult(array($qry, $companyid, $login));

            if ( $res[0]['VALUE'] != $userKey ) {
                $results['CALL_RESULT'] = 'ERROR';
                $results['RESULT_TYPE'] = 'SFDC_LOGIN';
                $results['REASON'] = 'User key is invalid.';
                return false;
            }
            /// Checking for User Status whether it is inactive
            if ( $res[0]['USERSTATUS'] == 'F' ) {
                $results['CALL_RESULT'] = 'ERROR';
                $results['RESULT_TYPE'] = 'LOGIN';
                $results['REASON'] = 'This user is in INACTIVE state, contact to Administrator';
                return false;
            }
        }
        // make sure the subscription process is complete.
        include_once "backend_sforce.inc";
        if ( !IsSFORCEConfigured($module) ) {
            $results['CALL_RESULT'] = 'ERROR';
            $results['RESULT_TYPE'] = 'SFDC_LOGIN';
            $results['REASON'] = 'The Salesforce.com implementation process is not complete.';
            return false;
        }

        // If multi-entity shared standalone SFDC instance,
        // account entity can be left blank and hence derive from Intacct company,
        // configured at SFDC user level,
        // if Opportunity entity is not same as SFDC Intacct company,
        // its a request to slide into shared entity,
        // if slide request, fetch location record# to slide into
        if ( $mega == 'shared' ) {
            $toplevelentity = $companyid;
            $sharedentity = $objectentity;
            if ( isset($toplevelentity) && $toplevelentity != ''
                && isset($sharedentity) && $sharedentity != ''
                && $toplevelentity != $sharedentity
            ) {
                $destLocation = $this->GetMultiSharedEntityKey($toplevelentity, $sharedentity);
                if (!$destLocation) {
                    $results['CALL_RESULT'] = 'ERROR';
                    $results['RESULT_TYPE'] = 'SFDC_LOGIN';
                    $results['REASON'] = 'Invalid Entity Sharing';
                    return false;
                }
            }
        }
        
        // Create the Intacct Session
        $session = IASessionHandler::setupSession('', $_userid, '', $destLocation);
        $profileHandler = $session->getProfileHandler();
        $profileHandler->setProperty('SFORCEKEY', $sforcekey);
        
        $this->addPermissiontoProfile($profileHandler);

        if ( $mega =='client' ) {
            //check Console subscription is enabled when they are trying to sync
            GetModulePreferences('38.SFDC', $conprefs);
            if ( $conprefs['MCONSOLECLIENT'] != 'true' ) {
                $results['CALL_RESULT'] = 'ERROR';
                $results['RESULT_TYPE'] = 'SETUP';
                $results['REASON'] = "Make sure Console($root) have enabled M-Console Subscription on Integration Setup at Intacct";
                return false;
            }
            ///Console User -- trying to slide to Entity
            if ( $root == $userentity && $objectentity != $root && $objectentity != '' ) {
                global $gManagerFactory;
                // to retrieve cny# for object entity
                $clientMgr = $gManagerFactory->getManager('client');

                //Get Source Console Cny#
                /* please remove the unused var from the list rather than noinspecting it - noinspection PhpUnusedLocalVariableInspection */
                [ , $srccny]  = explode('@', $_userid);
                // Since this is M-Console slide into its client, if account is configured,
                // for console only ($root == $accountentity), its NOT shared entity slide
                // If object entity (e.g. Intacct Entity field at Opportunity SF page),
                // and account entity (i.e. Intacct Entity field on Account SF page),
                // are not identical, it must be ME shared case,
                // i.e. we're tring to create sales transaction at entity level,
                // from SF opportunity page
                if ( isset($accountentity)
                    && $accountentity != ''
                    && $accountentity != $root
                    && $accountentity != $objectentity
                ) {
                    $toplevelentity = $accountentity;
                    $sharedentity = $objectentity;
                }
                else {
                    $toplevelentity = $objectentity;
                    $sharedentity = '';
                }
                // query top level cny#
                $args = array($toplevelentity);
                $res = $clientMgr->DoQuery('QRY_CLIENT_SELECT_SINGLE_TITLE', $args);
                if ( $res ) {
                    if ( isset($sharedentity) && $sharedentity != '' ) {
                        $destLocation = $this->GetMultiSharedEntityKey($toplevelentity, $sharedentity);
                        if ( !$destLocation ) {
                            // Invalid ME shared entity
                            return false;
                        }
                    }

                    $destcny = $res[0]['ENTITYCNY#'];
                    $ok = $gManagerFactory->PurgeManagerInstances();
                    include_once 'slide.inc';
                    //Slide into Client Company
                    $ok = $ok && SFSlideCompany($mega, $srccny, $destcny, $destLocation);
                    if( !$ok ) {
                        $results['CALL_RESULT'] = 'ERROR';
                        $results['RESULT_TYPE'] = 'SFDC_LOGIN';
                        $results['REASON'] = "Invalid Action. User might not have proper permission setup to slide into Client $toplevelentity";
                        return false;
                    }

                    SetDBSchema($destcny, "cny#");
                    $profileHandler = ProfileHandler::getInstance();
                    $profileHandler->setProperty('SFORCEKEY', $sforcekey);
                    $profileHandler->saveProfile();
                    return true;
                } else {
                    //throws error saying client company not exist under this Console Company
                    $results['CALL_RESULT'] = 'ERROR';
                    $results['RESULT_TYPE'] = 'SFDC_LOGIN';
                    $results['REASON'] = "Invalid Entity. No such $toplevelentity Entity found under Console $root";
                    return false;
                }
            }

            GetModulePreferences('38.SFDC', $entprefs);

            if ( $entprefs['MCONSOLECLIENT'] != 'true' ) {
                $results['CALL_RESULT'] = 'ERROR';
                $results['RESULT_TYPE'] = 'SETUP';
                $results['REASON'] = "Make sure Client($objectentity) have enabled M-Console Subscription on Integration Setup at Intacct";
                return false;
            }

        }

        return true;
    }
    
    /**
     * addPermissiontoProfile
     * 
     * @param ProfileHandler $profileHandler
     */
    protected function addPermissiontoProfile($profileHandler)
    {
        AddAllowOps($profileHandler);

        // add any forceful permissions for already granted permissions so far
        AddForcedElementsOnly($profileHandler);

        $profileHandler->saveProfile();

        $profileHandler->reload(); // regenerate the cache in the profile
    }

    /**
     * @param string[] $results
     *
     * @return bool
     */
    public function ProcessVisibilityRestrictions(&$results)
    {
        if ( !IsMultiEntityCompany() ) {
            return true;
        }

        $entity = 'customer';
        // read SF account ID
        $custid = $_GET['_customerid'] ?? '';

        // entity slide?
        $ctx = GetContextLocation();
        // multivisibility subscribed for AR?
        $mvsub = IsMultiVisibilitySubscribed($entity);

        // If restricted user,
        if ( IsRestrictedUser() ) {
            // If restricted to ONLY one entity, can not process customer and item sync
            if ( GetRestrictedUserLocation() ) {
                $_action = Request::$r->_action;
                /* please remove the unused var from the list rather than noinspecting it noinspection PhpUnusedLocalVariableInspection */
                [ , $obj] = explode('_', $_action);
                if ( $obj == 'CUSTOMER' || $obj == 'PRODUCT' ) {
                    $results['CALL_RESULT'] = 'ERROR';
                    $results['RESULT_TYPE'] = 'VISIBILITY_RESTRICTIONS';
                    $results['REASON'] = "The user is restricted to single entity. Can not sync Account/ Product at entity level.";
                    return false;
                }
            }

            // If restricted to multiple entities,
            // If request is to switch to shared entity,
            // Check if shared entity is within user entity restriction
            if ( $ctx ) {
                $restrictedLocs =  GetRestrictedUserLocs();
                if ( $restrictedLocs && !in_array($ctx, $restrictedLocs) ) {
                    $results['CALL_RESULT'] = 'ERROR';
                    $results['RESULT_TYPE'] = 'VISIBILITY_RESTRICTIONS';
                    $results['REASON'] = "The user restrictions do not allow access to this entity.";
                    return false;
                }
            }

            // validate customer visibility restrictions against user visibility restrictions
            if ( $mvsub && $custid && !IsObjectOwned($custid, $entity) ) {
                $results['CALL_RESULT'] = 'ERROR';
                $results['RESULT_TYPE'] = 'VISIBILITY_RESTRICTIONS';
                $results['REASON'] = "The user restrictions along with customer visibility restrictions do not allow this access.";
                return false;
            }

        }

        // check if context location is within customer visibility restrictions
        if ( $ctx && $mvsub && $custid ) {
            $lineRestrictedValue = GetContextLocation(true).'--'.GetContextLocationName();
            GetMultiVisibleObjects($object, $custid, $entity);
            $strRestrictedValues = $object['RESTRICTEDLOCATIONS'];
            if ( isset($strRestrictedValues) && $strRestrictedValues != '' ) {
                $restrictedValues = explode('#~#', $strRestrictedValues);
                if ( !in_array($lineRestrictedValue, $restrictedValues) ) {
                    // Extract only the locationID from the $restrictedValues
                    $restrictedLocIDs = array_map(function($value) {
                                            $parts = explode('--', $value);
                                            return $parts[0];
                                        }, $restrictedValues);
                    $locationManager = Globals::$g->gManagerFactory->getManager('location');
                    $query = [
                                'selects' => ['ENTITY'],
                                'filters' => [[['LOCATIONID', 'IN', $restrictedLocIDs]]],
                                'usemst' => true
                    ];
                    $locGetList = $locationManager->GetList($query);
                    $entityOfLocation = [];
                    if (Util::countOrZero($locGetList) > 0) {
                        foreach ($locGetList as $locationData) {
                            if (!in_array($locationData['ENTITY'], $entityOfLocation)) {
                                array_push( $entityOfLocation, $locationData['ENTITY'] );
                            }
                        }
                    }
                    if (!in_array($ctx, $entityOfLocation)) {
                        $results['CALL_RESULT'] = 'ERROR';
                        $results['RESULT_TYPE'] = 'VISIBILITY_RESTRICTIONS';
                        $results['REASON'] = "Customer visibility restrictions do not allow access for entity '$lineRestrictedValue'";
                        return false;
                    }
                }
            }
        }

        return true;
    }
    
    
    /**
     * @param stdClass $oppertunityDetails
     * @param string[] $sfpreferences
     *
     * @return bool
     */
    protected function syncCustomerFromTrx($oppertunityDetails, $sfpreferences)
    {
        if ( (!array_key_exists('CUSTOMERID', $this->sfdcvalues) || $this->sfdcvalues['CUSTOMERID'] == '') && ($sfpreferences['SFORCEENABLEACCOUNTSYNC'] == 'true')
        ) {

            include_once 'SforceSynchronizer.cls';
            $sforceSync = new SforceSynchronizer();
            global $gErr;

            if ( $oppertunityDetails->AccountId == '' ) {
                $gErr->addError(
                    'SFDC-1223', __FILE__ . '.' . __LINE__, "This opportunity is not related to an Account record.  Please choose an Account Name for this Opportunity"
                );
                return false;
            }

            if ( !$sforceSync->SyncIntacctCust($oppertunityDetails->AccountId, $objCust) ) {
                $gErr->errors = INTACCTarray_merge($gErr->errors, $sforceSync->myErrors->errors);
                $gErr->addError(
                    'SFDC-1224', __FILE__ . '.' . __LINE__, "Unable to create a customer record for this account.  Click details for more errors."
                );
                return false;
            }
            $this->sfdcvalues['CUSTOMERID'] = $objCust['CUSTOMERID'];
            // we also need to add this to the object cache
            $this->_AddValuetoCache('CUSTOMERID', $objCust['CUSTOMERID']);
        }

        return true;
    }

    /**
     * @param stdClass $opportunityDetails
     * @param string[] $sfpreferences
     *
     * @return bool
     */
    protected function syncContactsFromTrx(
        /** @noinspection PhpUnusedParameterInspection */ $opportunityDetails,
        /** @noinspection PhpUnusedParameterInspection */ $sfpreferences)
    {
        return true;
    }

    /**
     * @param string $endpoint
     *
     * @return bool|int
     */
    abstract protected function validateEndpoint($endpoint);

    /**
     * @param string $endpoint
     *
     * @return string
     */
    public function replaceSoapApiVersion(&$endpoint)
    {
        return $endpoint;
    }

    /**
     * @param string[] $sfpreferences
     *
     * @return bool
     */
    protected function validateSoDocSync($sfpreferences)
    {
        $gErr = Globals::$g->gErr;
        //Validation to check if 1.) one2one relationship is enabled and 2.) 'To SF' syncback is disabled
        if (SalesforceUtil::isFromSFOne2OneDocSyncMap($sfpreferences, $this->sfdcvalues['DOCTYPE'])) {
            // check if the sales document for this TD already exists
            if ($this->isDocTypeExists()) {
                $gErr->addIAError(
                    'SFDC-1225', __FILE__ . '.' . __LINE__,
                    'There is another transaction created for this type: ' . $this->sfdcvalues['DOCTYPE'],
                    ['SFDCVALUES_DOCTYPE' => $this->sfdcvalues['DOCTYPE']]
                );
                return false;
            }
        }
        return true;
    }

    /**
     * @return bool
     */
    private function isDocTypeExists()
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $soDocManager   = $gManagerFactory->getManager('sodocument');
        $cny = GetMyCompany();
        $records = $soDocManager->DoQuery('QRY_SAMEDOCPAR_SELECT', array($cny, $this->sfdcvalues['DOCTYPE'],$cny, $this->sfdcvalues['SFORCEID']));
        return !isEmptyArray($records);
    }

}

