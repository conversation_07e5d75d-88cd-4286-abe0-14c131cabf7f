<?php
//=============================================================================
//
//	FILE:			SforceDispatcher.cls
//	<AUTHOR> <<EMAIL>>
//	DESCRIPTION:	Sforce Dispatcher Class
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information herein
//	may not be used, copied or disclosed in whole or part without prior
//	written consent from Intacct Corporation.
//
//=============================================================================

final class SforceDispatcher extends FIFODispatcher
{
    use SforceTrait;

    /**
     * Return the dispatcher type (the value to store in DB, singe char)
     *
     * @return string
     */
    public function getType() : string
    {
        return FIFODispatcher::DISPATCHER_TYPES['sforcequeue'];
    }

    /**
     * @return string
     */
    public function getConfigSectionName(): string
    {
        return 'SFORCE_DISPATCHER';
    }

    /**
     * @param DBSchemaInfo $dbInfo
     *
     * @return int
     */
    public function getDBBandwidth(DBSchemaInfo $dbInfo): int
    {
        return $dbInfo->getSforceWorkflowBandwidth();
    }

    /**
     * @param DBSchemaInfo $dbInfo
     *
     * @return int
     */
    public function getDBTimeLimit(DBSchemaInfo $dbInfo): int
    {
        return $dbInfo->getSforceWorkflowTimeLimit();
    }

    /**
     * @return string
     */
    public function getControlLockPrefix(): string
    {
        return 'sfDispatch';
    }
}