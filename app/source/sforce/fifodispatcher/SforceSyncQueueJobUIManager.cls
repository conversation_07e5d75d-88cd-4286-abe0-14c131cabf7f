<?php

/**
 * File description
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 Intacct Corporation, All Rights Reserved
 */

class SforceSyncQueueJobUIManager extends FIFODispatcherQueueUIManager
{
    const SFORCE = 'SFORCE';

    /** Constants that define the state of each sync job */
    const log_info= 'i';
    const log_warning = 'w';
    const log_error = 'e';
    const LOG_MESSAGE_TYPE_MAP = [
        self::log_info  => 'info',
        self::log_warning => 'warning',
        self::log_error    => 'error'
    ];
    /**
     * @param array $obj
     *
     * @return array
     */
    protected function decompressObjectData(array $obj) : array
    {
        $obj = parent::decompressObjectData($obj);
        if (isset($obj[self::SFORCE]) && count($obj[self::SFORCE]) > 0) {
            foreach ($obj[self::SFORCE] as &$sforceobj) {
                $log = json_decode(databaseStringUncompress($sforceobj[SforceQueueHistoryManager::DETAILS]),true);
                $log['type']=self::LOG_MESSAGE_TYPE_MAP[$sforceobj['CATEGORY']];
                $sforceobj=$log;
            }
        }
        return $obj;
    }
}