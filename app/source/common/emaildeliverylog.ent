<?php
/**
 *  FILE: emaildeliverylog.ent
 *
 *  @description entity definition for emaildeliverylog object
 *
 *  <AUTHOR> <<EMAIL>>
 *  @copyright 2012 Intacct Corporation, All Rights Reserved
 */
global $gRecordNoFormat;

$kSchemas['emaildeliverylog'] = array (
    // object
    'object' => array (
                    'RECORDNO',
                    'STATUS_V',
                    'XTENANT',
                    'SENDER',
                    'TO',
                    'SUBJECT',
                    'RESULT',
                    'RESULTDETAIL',
                    'WHENSENT',
                    'WHENUPDATED',
                    'EMAILBODY',
                    'MODULEID',
                    'EMAILTEMPLATEKEY',
                    'CUSTOMERKEY',
                    'VENDORKEY',
                    'PRRECORDKEY',
                    'DOCHDRKEY',
                    'DELIVERYLOGKEY',
                    'LOCATIONKEY'
    ),
    // schema
    'schema' => array (
                    'RECORDNO'  => 'record#',
                    'STATUS_V' => 'status',
                    'XTENANT' => 'xtenant',
                    'SENDER' => 'sender',
                    'TO' => 'emailto',
                    'SUBJECT' => 'subject',
                    'RESULT' => 'result',
                    'RESULTDETAIL' => 'resultdetail',
                    'WHENSENT' => 'whensent',
                    'WHENUPDATED' => 'whenupdated',
                    'EMAILBODY' => 'emailbody',
                    'MODULEID' => 'moduleid',
                    'EMAILTEMPLATEKEY' => 'emailtemplatekey',
                    'CUSTOMERKEY' => 'customerkey',
                    'VENDORKEY' => 'vendorkey',
                    'PRRECORDKEY' => 'prrecordkey',
                    'DOCHDRKEY' => 'dochdrkey',
                    'DELIVERYLOGKEY'   => 'deliverylogkey',
                    'LOCATIONKEY'   => 'locationkey',
                    'SI_UUID'     => 'si_uuid',
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array ( 'WHENSENT', 'WHENUPDATED' ),

    // fieldinfo
    'fieldinfo' => array (
        array (
            'path' => 'RECORDNO',
            'desc' => 'IA.RECORD_NUMBER',
            'fullname' => 'IA.RECORD_KEY',
            'hidden' => false,
            'type' => array (
                'type' => 'integer',
                'ptype' => 'sequence',
                'maxlength' => 8,
                'size' => 8,
                'format' => $gRecordNoFormat,
            ),
            'id' => 1
        ),
        array (
            'path'         =>    'STATUS_V',
            'fullname'     =>    'IA.STATUS',
            'desc'         =>    'IA.EMAIL_STATUS',
            'readonly'    =>    true,
            'type'        =>    array (
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array ('IA.SENT', 'IA.WARNING', 'IA.ERROR', 'IA.DELIVERED',),
                'validvalues' => array (
                    'Sent', 'Warning', 'Error','Delivered',
                ),
                '_validivalues' => array (
                    'S','W','X','D'
                )
            ),
        ),
        array(
            'path'        => 'XTENANT',
            'readonly'    =>    true,
            'fullname'     => 'IA.XTENANT',
            'hidden' => true,
            'type'        => array(    'ptype' => 'text', 'type' => 'text',
                            'maxlength' => 256 ),
            'desc'        => 'IA.XTENANT',
        ),
        array(
            'path'        => 'SENDER',
            'readonly'    =>    true,
            'fullname'     => 'IA.SENDER',
            'type'        => array(    'ptype' => 'text', 'type' => 'text',
                            'maxlength' => 256),
            'desc'        => 'IA.RESULT',
        ),
        array(
            'path'        => 'SUBJECT',
            'readonly'    =>    true,
            'fullname'     => 'IA.SUBJECT',
            'type'        => array(    'ptype' => 'text', 'type' => 'text',
                            'maxlength' => 256),
            'desc'        => 'IA.RESULT',
        ),
        array(
            'path'        => 'RESULT',
            'readonly'    =>    true,
            'fullname'     => 'IA.RESULT',
            'type'        => array(    'ptype' => 'text', 'type' => 'text',
                            'maxlength' => 256),
            'desc'        => 'IA.RESULT',
        ),
        array(
            'path'        => 'RESULTDETAIL',
            'readonly'    =>    true,
            'fullname'     => 'IA.RESULT_DETAILS',
            'isHTML'    => false,
            'type'        => array(    'ptype' => 'textarea', 'type' => 'textarea',
                            'maxlength' => 4000),
            'desc'        => 'IA.RESULT_DETAILS',
        ),                    
                    
        array(
            'fullname'    => 'IA.SENT',
            'readonly'    => true,
            'type' => array(
                            'ptype' => 'timestamp',
                            'type' => 'timestamp',
                            'size' => 22,
            ),
            'path' => 'WHENSENT',
        ),                    
        array(
            'fullname'    => 'IA.UPDATED',
            'readonly'    => true,
            'type' => array(
                            'ptype' => 'timestamp',
                            'type' => 'timestamp',
                            'size' => 22,
            ),
            'path' => 'WHENUPDATED',
        ),
        array(
            'fullname' => 'IA.EMAIL_BODY',
            'type' => array (
                            
                            'ptype' => 'textarea',
                            'type' => 'text'),
            'path' => 'EMAILBODY'
        ),                    
        array(
            'path'        => 'REPLY_TO',
            'readonly'    =>    true,
            'fullname'     => 'IA.REPLY_TO',
            'type'        => array(    'ptype' => 'text', 'type' => 'text',
                            'maxlength' => 256),
            'desc'        => 'IA.REPLY_TO',
        ),
        array(
            'path'        => 'TO',
            'readonly'    =>    true,
            'fullname'     => 'IA.TO',
            'type'        => array(    'ptype' => 'text', 'type' => 'text',
                            'maxlength' => 256),
            'desc'        => 'IA.TO',
        ),
        array(
            'path'        => 'CC',
            'readonly'    =>    true,
            'fullname'     => 'IA.CC',
            'type'        => array(    'ptype' => 'text', 'type' => 'text',
                            'maxlength' => 256),
            'desc'        => 'IA.CC',
        ),
        array(
            'path'        => 'BCC',
            'readonly'    =>    true,
            'fullname'     => 'IA.BCC',
            'type'        => array(    'ptype' => 'text', 'type' => 'text',
                            'maxlength' => 256),
            'desc'        => 'IA.BCC',
        ),
        array(
            'path'        => 'SUBJECT',
            'readonly'    =>    true,
            'fullname'     => 'IA.SUBJECT',
            'type'        => array(    'ptype' => 'text', 'type' => 'text',
                            'maxlength' => 256),
            'desc'        => 'IA.SUBJECT',
        ),                                        
        array(
            'path'        => 'BODY',
            'readonly'    =>    true,
            'fullname'     => 'IA.BODY',
            'isHTML'    => true,
            'type'        => array(    'ptype' => 'text', 'type' => 'text',
                            'maxlength' => 4000),
            'desc'        => 'IA.BODY',
        ),
        $gSiUuidFieldInfo
    ),
    'table' => 'emaildeliverylog',
    'printas' => 'IA.EMAIL_DELIVERY_HISTORY',
    'pluralprintas' => 'IA.EMAIL_DELIVERY_LOGS',
    'sicollaboration' => true,
    'vid' => 'RECORDNO',
    'autoincrement' => 'RECORDNO',
    'module' => 'co',
    'nosysview' => true,
);

