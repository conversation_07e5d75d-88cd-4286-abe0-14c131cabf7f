<?

$ktplformatQueries['QRY_TPLFORMAT_GETLIST'] = array(
    'QUERY' => "SELECT xslformat.description, xslformat.doctype, xslformat.record# FROM v_iadocxsl xslformat WHERE xslformat.moduleid =? and xslformat.cny# in (0,?) ORDER by xslformat.isdefault desc, xslformat.doctype",
    'ARGTYPES' => array('text','integer'),
);
$ktplformatQueries['QRY_TPLFORMAT_GETCOUNT'] = array(
    'QUERY' => "SELECT xslformat.description, xslformat.doctype, xslformat.record# FROM v_iadocxsl xslformat WHERE xslformat.moduleid =? and xslformat.cny# in (0,?) ORDER by xslformat.doctype",
    'ARGTYPES' => array('text','integer'),
);
$ktplformatQueries['QRY_TPLFORMAT_SELECT_RAW_VID'] = array(
    'QUERY' => 'SELECT * FROM v_iadocxsl xslformat WHERE xslformat.description =? and xslformat.cny# in (0, ?)',
        'ARGTYPES' => array('text' ,'integer' ),
);
$ktplformatQueries['QRY_TPLFORMAT_SELECT_RAW_FROM_RECORDNO'] = array(
    'QUERY' => 'SELECT * FROM v_iadocxsl xslformat WHERE xslformat.record# =? and xslformat.cny# in (0, ?)',
        'ARGTYPES' => array('integer' ,'integer' ),
);
$ktplformatQueries['QRY_TPLFORMAT_SELECT_SINGLE_VID'] = array(
    'QUERY' => 'SELECT xslformat.record#,xslformat.moduleid,xslformat.doctype,xslformat.description FROM v_iadocxsl xslformat WHERE   xslformat.description = ?  and xslformat.cny# in (0, ?)',
    'ARGTYPES' => array('text', 'integer'),
);
$ktplformatQueries['QRY_TPLFORMAT_SELECT_RAW_FROM_RECORDNO_MCP'] = array(
    'QUERY' => "SELECT NVL(xslformat2.RECORD#, xslformat.RECORD#) RECORD#, NVL(xslformat2.description, xslformat.description) DESCRIPTION, NVL(xslformat2.MODULEID, xslformat.MODULEID) MODULEID, NVL( xslformat2.DOCTYPE, xslformat.DOCTYPE) DOCTYPE, NVL(xslformat2.XSL, xslformat.XSL) XSL FROM v_iadocxsl xslformat, v_iadocxsl xslformat2 WHERE xslformat.RECORD# =?  AND xslformat.cny# IN (0, ?) AND xslformat2.DESCRIPTION(+)=xslformat.DESCRIPTION || ' (MCP)' AND xslformat2.cny#(+)=xslformat.cny#",
        'ARGTYPES' => array('integer' ,'integer' ),
);