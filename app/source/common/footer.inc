<?  
//=============================================================================
//
//	FILE:			footer.inc
//	AUTHOR:			
//	DESCRIPTION:	
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================
//  require_once('util.inc');
//  Init();
$layout = GetMyLayoutType();
Pendo::insertPendoSnippet();
if($layout!='C') {
    return true;
}
  global $gInstalledModules, $gSecurity, $islive;
  $_cpaassoc = Request::$r->_cpaassoc;
  $_op = Request::$r->_op;
  $_sess = Session::getKey();

    $privacypolicylink = GetValueForIACFGProperty("IA_PRIVACYLINK");

  echo DisplayLogout();
?>
<style>
table.footerTable td a{
color:#000;
}
table.footerTable td {
color:#fff;
}

</style>
<!--<div class="footertop"></div>-->
<TABLE class="NAVBAR" cellpadding="0" cellspacing="0" border="0" width="100%" style="border-top:1px dashed #999;">
<TR valign="bottom"><TD><img src="../resources/images/ia-app/backgrounds/spacer.gif" width="100%" height="6px" border=0></TD></TR></TABLE>
<TABLE class="footerTable" border="0" cellpadding="0" cellspacing="0" width="100%"><TR CLASS="Task" valign="top">
<TD width=33% align="right" ><a tabindex=-1 href="<?echo $privacypolicylink;?>" target="new_intacct_window"  CLASS="FooterLink" <? echo HREFUpdateStatus('Privacy Policy'); ?>>Privacy Policy</A> &nbsp; &nbsp; </TD><TD ALIGN="center" WIDTH="33%" ><FONT face="Verdana, Arial" size="-2" color="#666666"><span class="f" >&copy; 1999-<script> document.write(new Date().getFullYear()); </script><span></font> <a tabindex=-1 href="http://www.sageintacct.com" CLASS="FooterLink" target="new_intacct_window">Sage Intacct, Inc.</a></FONT></TD><TD ALIGN="left" WIDTH="33%"><FONT CLASS="poweredby">Powered by Intacct</FONT></TD></TR><TR><TD COLSPAN="4" HEIGHT="100%"/>
<? if (isset($_op) && !$islive) { ?>
	<FONT face="Verdana, Arial" size="-2"><? echo "Page: $_op"; ?></font><br>
<? 
} ?>
</td>
<? if($_cpaassoc) {
    $powerdBy = '<td align="right" nowrap><img src="../resources/images/ia-app/logos/poweredby.gif" alt="poweredby.gif" >&nbsp;&nbsp;</td>';
}

    $affiliation = Profile::getProperty('AFFILIATION');
    $returnArrayAccountant = getAffiliationTypeValues("Accountant");
if ($affiliation == $returnArrayAccountant['type']) {
    $footerFile = $returnArrayAccountant['footerfile'];
    $powerdBy = '<td align="right" nowrap><img src="../resources/images/'.$footerFile.'" alt="poweredby.gif" >&nbsp;&nbsp;</td>';
} 
    echo($powerdBy);
if (isset($_COOKIE[CSPERF_PAGE_POST_TIME]) && isl_preg_match("/[^\.0-9-]/", $_COOKIE[CSPERF_PAGE_POST_TIME])) {
    $PerfPagePostTime = $_COOKIE[CSPERF_PAGE_POST_TIME];
} else {
    $PerfPagePostTime = '';
}

    ?>
</tr>
</table>
<script language="JavaScript" src="../resources/js/polyfill/promise.js"></script>
<script language="JavaScript" src="../resources/js/qrequest.js"></script>
<script language="JavaScript" src="../resources/js/csPerf.js"></script>
<script type="text/javascript" language="javascript" >
  if( ! this.gPerfPageId ) var gPerfPageId = '<? echo Globals::$g->perfdata->getSerialnbr();?>';
  if( ! this.gPerfPagePostTime ) var gPerfPagePostTime = '<?= $PerfPagePostTime; ?>';
  csPerfAddLoadEvent();
</script>