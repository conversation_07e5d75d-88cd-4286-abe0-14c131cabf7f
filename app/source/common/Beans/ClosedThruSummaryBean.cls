<?php

/**
 *    ClosedThruSummaryBean.cls
 *
 * <AUTHOR> <jana<PERSON>anan.r<PERSON><PERSON><PERSON>@sage.com>
 * @copyright 2021, Intacct Corporation, All Rights Reserved
 */

class ClosedThruSummaryBean extends DashboardLister
{

    /**
     * @var ClosedThruBuilder $builder
     */
    protected $builder;

    /**
     * @param array|null $_params
     */
    public function __construct($_params = null)
    {
        Bean::__construct($_params);
    }

    /**
     * @return string
     */
    public function getViewType()
    {
        return $this->paramsArr['viewType'] ?? '';
    }

    /**
     * @return bool
     */
    public function InitializeBean()
    {
        $this->builder = new ClosedThruBuilder($this->getViewType());
        $this->SetFields($this->builder->getFields());
        $this->SetFieldlabels($this->builder->getFieldLabels());
        $this->SetHeight($this->paramsArr['compheight']);

        return $this->BuildTable();
    }

    /**
     * Build the Summary table
     *
     * @return bool
     */
    public function BuildTable()
    {
        $this->builder->buildTable();
        $this->SetTable($this->builder->getTable());

        return true;
    }

    /**
     * Overridden GetDisplayList
     *
     * @return array
     */
    public function GetDisplayList()
    {
        $entityListerInfo = [
            'fieldLabels' => $this->builder->getFieldLabels(),
            'fieldTypes'  => $this->builder->getFieldTypes(),
            'entityList'  => $this->table,
        ];

        return $entityListerInfo;
    }

    /**
     * No need to render the Extra icons
     */
    public function GenerateCompTypeButtons()
    {
        //Do not render the lens icon
    }

    /**
     * To set the individual attributes for the div tag rending this component.
     * The width parameter is very important to be set for the lister component should fit into the column.
     *
     * @return true we dont need to stop rendering if this fails.
     */
    function SetDivClassAttributes()
    {
        return Bean::SetDivClassAttributes();
    }

    /**
     * To format the values to be displayed. any currency/mask or in future any type formats for display should be done
     * here.
     *
     * @param string $fieldType
     * @param string $fieldValue
     *
     * @return string
     */
    function FormatForDisplay($fieldType, $fieldValue)
    {
        if ( preg_match('/^<[aA]/', $fieldValue) && $fieldType !== 'href' ) {
            $arr = StripHtmlTag($fieldValue);
            if ( is_array($arr) && isset($arr[2]) ) {
                $fieldValue = $arr[2];
            }
        }

        return $fieldValue;
    }
}