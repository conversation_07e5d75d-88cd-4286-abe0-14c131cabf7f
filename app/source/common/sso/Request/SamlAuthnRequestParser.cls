<?php

/**
 * Class SamlAuthnRequestParser
 */
class SamlAuthnRequestParser extends SamlRequestParser implements SamlAuthnRequestParserInterface
{

    /** @var string|null $nameId */
    private $nameId;

    /** @var string|null $nameIdPolicyFormat */
    private $nameIdPolicyFormat;

    /** @var string|null $nameIdPolicySPNameQualifier */
    private $nameIdPolicySPNameQualifier;

    /** @var bool|null $nameIdPolicyAllowCreate */
    private $nameIdPolicyAllowCreate;

    /**
     * @param string $request
     */
    public function __construct(string $request)
    {
        parent::__construct($request);
        $this->validate();
        $this->parseSubject();
        $this->parseNameIDPolicy();
    }

    /**
     * @return string|null
     */
    public function getConsumerServiceUrl(): ?string
    {
        $value = self::$rootRequestElement->getAttribute('AssertionConsumerServiceURL');
        return ('' === $value) ? null : $value;
    }

    /**
     * @return string|null
     */
    public function getProtocolBinding() : ?string
    {
        $value = self::$rootRequestElement->getAttribute('ProtocolBinding');
        return ('' === $value) ? null : $value;
    }

    /**
     * @return string|null
     */
    public function getNameId() : ?string
    {
        return $this->nameId;
    }

    /**
     * @return string|null
     */
    public function getNameIdPolicyFormat() : ?string
    {
        return $this->nameIdPolicyFormat;
    }

    /**
     * @return string|null
     */
    public function getNameIdPolicySPNameQualifier() : ?string
    {
        return $this->nameIdPolicySPNameQualifier;
    }

    /**
     * @return bool|null
     */
    public function getNameIdPolicyAllowCreate() : ?bool
    {
        return $this->nameIdPolicyAllowCreate;
    }

    /**
    * @throws Exception
    */
    private function validate() : void
    {
        if (self::$rootRequestElement->localName !== 'AuthnRequest'
            && self::$rootRequestElement->localName !== 'samlp:AuthnRequest'
        ) {
            throw new FatalException(
                sprintf(
                    'SAML Request - expected AuthnRequest element, %s provided',
                    var_export(self::$rootRequestElement->localName, true)
                )
            );
        }
        $this->validateRootElement();
        $this->validateIssuer();
    }

    private function parseSubject() : void
    {
        $subjectNodeList = $this->xpCache->query('./saml_assertion:Subject', self::$rootRequestElement);
        if ($subjectNodeList->length === 0) {
            return;
        }
        $subjectNode = $subjectNodeList->item(0);

        $nameIdNode = $this->xpCache->query(
            './saml_assertion:BaseID | ./saml_assertion:NameID',
            $subjectNode
        );
        if ($nameIdNode->length !== 1) {
            throw new FatalException(
                sprintf(
                    'Error parsing <saml:Subject>: one of (%s) should be provided, found %s',
                    '<saml:BaseID> | <saml:NameID>',
                    $nameIdNode->length ?? 0
                )
            );
        }
        $nameId = $nameIdNode->item(0)->nodeValue;
        if (empty($nameId)) {
            throw new FatalException('Error parsing <saml:Subject>: empty name id');
        }
        $this->nameId = $nameId;
    }

    private function parseNameIDPolicy() : void
    {
        $results = $this->xpCache->query('./saml_protocol:NameIDPolicy', self::$rootRequestElement);
        if ($results->length === 0) {
            return;
        }
        $node = $results->item(0);
        if ($node->hasAttribute('Format')) {
            $this->nameIdPolicyFormat = $node->getAttribute('Format');
        }
        if ($node->hasAttribute('SPNameQualifier')) {
            $this->nameIdPolicySPNameQualifier = $node->getAttribute('SPNameQualifier');
        }
        if ($node->hasAttribute('AllowCreate')) {
            $this->nameIdPolicyAllowCreate = filter_var(
                $node->getAttribute('AllowCreate'), FILTER_VALIDATE_BOOLEAN);
        }
    }
}
