<?php

/**
 * Class SsoRegisterIdpController
 */
class SsoRegisterIdpController extends AbstractBaseSsoIdpController
{

    /** @var string|null $sp */
    private $sp;

    /** @var AbstractRegisterHandler $idpEntityProvider */
    private $idpEntityProvider;

    /** @var AbstractRegisterHandler $registerManager */
    private $registerManager;

    /** @var IdPLoggerInterface $logger */
    private $logger;

    /**
     * SsoRegisterIdpController constructor.
     *
     * @param IdPEntityProvider  $idpEntityProvider
     * @param IdPLoggerInterface $logger
     */
    public function __construct(IdPEntityProvider $idpEntityProvider, IdPLoggerInterface $logger)
    {
        $this->idpEntityProvider = $idpEntityProvider;
        $this->logger = $logger;
    }

    public function run(): void
    {
        try {
            $this->sp = $this->getServiceProvider();
            $this->registerManager = $this->getRegisterManager();
            $this->validatePreconditions();
            $this->registerManager->validateRequest();
            $this->registerManager->setAttributes();
            if( true !== $this->registerManager->loginIdp->getValidator()->validate(
                $this->registerManager->getCompanyCacheHandler(),
                $this->registerManager->getUserCacheHandler()
            )) {
                $messages = $this->registerManager->loginIdp->getValidator()->getValidationMessages();
                throw new ValidationFailedException(
                    $this->gtFromOneToken(
                        array_shift($messages),
                        [
                            ['name' => 'PLACEHOLDER', 'value' => AbstractIdP::SERVICE_PROVIDERS_TAG_TO_LABEL_MAPPING[$this->sp]],
                        ]
                    )
                );
            }
            $this->registerManager->processRequest();
        } catch (RegisterIsBlockedException  $e) {
            $this->setCompanyContext();
            $this->logger->critical($e->getMessage() . ' (Trace:' . $e->getTraceAsString() . ')');
            echo $this->registerManager->getTemplateProvider()->getPage(
                AbstractRegisterHandler::BLOCKED
            );
        } catch (RegisterUserIsAlreadyRegisteredException  $e) {
            $this->setCompanyContext();
            $this->logger->critical($e->getMessage() . ' (Trace:' . $e->getTraceAsString() . ')');
            echo $this->registerManager->getTemplateProvider()->getPage(
                AbstractRegisterHandler::USER_ALREADY_REGISTERED
            );
        } catch (RegisterUserNotRegisteredCannotUnregisterException  $e) {
            $this->setCompanyContext();
            $this->logger->critical($e->getMessage() . ' (Trace:' . $e->getTraceAsString() . ')');
            echo $this->registerManager->getTemplateProvider()->getPage(
                AbstractRegisterHandler::USER_NOT_REGISTERED_CANNOT_UNREGISTER
            );
        } catch (RegisterInvalidIdentityException  $e) {
            $this->setCompanyContext();
            $this->logger->critical($e->getMessage() . ' (Trace:' . $e->getTraceAsString() . ')');
            echo $this->registerManager->getTemplateProvider()->getPage(
                AbstractIdP::INVALID_IDENTITY
            );
        } catch (NotEnabledException $e) {
            $this->setCompanyContext();
            $this->logger->critical($e->getMessage() . ' (Trace:' . $e->getTraceAsString() . ')');
            echo $this->registerManager->getTemplateProvider()->getPage(
                AbstractIdP::NOT_ENABLED
            );
        } catch (ValidationFailedException $e) {
            $this->setCompanyContext();
            $this->logger->critical($e->getMessage() . ' (Trace:' . $e->getTraceAsString() . ')');
            echo $this->registerManager->getTemplateProvider()
                ->setErrorMessage($e->getMessage())
                ->getPage(AbstractIdP::VALIDATION_FAILED);
        } catch (InvalidUserAttributesException $e) {
            $this->setCompanyContext();
            $this->logger->critical($e->getMessage() . ' (Trace:' . $e->getTraceAsString() . ')');
            echo $this->registerManager->getTemplateProvider()
                ->setErrorMessage($e->getMessage())
                ->setShowUserPreferencesButton($this->registerManager->getShowUserPreferencesButton())
                ->setUserPreferencesUrl(
                    $this->getUserPreferencesUrl(
                        $this->registerManager->identities[$this->registerManager->ssoIdentity]['SESSION#']
                    )
                )
                ->getPage(AbstractIdP::INVALID_USER_ATTRIBUTES);
        } catch (RegisterInvalidServiceProviderException $e) {
            $this->setCompanyContext();
            $this->logger->critical($e->getMessage() . ' (Trace:' . $e->getTraceAsString() . ')');
            echo $this->getDefaultTemplateProvider()->getPage(
                AbstractRegisterHandler::ERROR
            );
        } catch (\Exception $e) {
            $this->setCompanyContext();
            $this->logger->critical($e->getMessage() . ' (Trace:' . $e->getTraceAsString() . ')');
            echo $this->registerManager->getTemplateProvider()->getPage(
                AbstractRegisterHandler::ERROR
            );
        }
    }

    /**
     * @throws NotEnabledException
     */
    protected function validatePreconditions(): void
    {
        if (true != $this->idpEntityProvider->getEnabled()) {
            throw new NotEnabledException('IdP is not enabled');
        }
    }

    /**
     * @codeCoverageIgnore
     * @return AbstractRegisterHandler
     * @throws RegisterInvalidServiceProviderException
     */
    protected function getRegisterManager(): AbstractRegisterHandler
    {
        $subsystemInfo = (new SubsystemManager())
            ->getByTypeAndPropertiesTag(AbstractIdP::SUBSYSTEM_TYPE, $this->sp);
        switch ($this->sp) {
            case AbstractIdP::SP_LMS_TAG:
                return new LmsRegisterHandler(
                    new LoginIdP(
                        new IdPEntityProvider(),
                        new IdPLogger(new MetricSSOIdpSsoIdp()),
                        new SubsystemManager(),
                        new IdPCookieProvider(),
                        new SamlAuthnResponseProvider(),
                        new RegisterValidator(
                            (new SPEntityProvider($subsystemInfo ?? []))
                        )
                    ),
                    new SsoCacheHandler(),
                    new SsoRepository($this->sp),
                    new LmsTemplateProvider(),
                    new SsoMemcacheHandler(CacheClient::getInstance(CacheClient::GLOBAL_POOL)),
                    (new LmsApiClient(new LmsConfigProvider(), new LmsCountryCodeProvider()))->init()
                );
            case AbstractIdP::SP_MASTERCLASS_TAG:
                return new MasterclassRegisterHandler(
                    new LoginIdP(
                        new IdPEntityProvider(),
                        new IdPLogger(new MetricSSOIdpSsoIdp()),
                        new SubsystemManager(),
                        new IdPCookieProvider(),
                        new SamlAuthnResponseProvider(),
                        new RegisterValidator(
                            (new SPEntityProvider($subsystemInfo ?? []))
                        )
                    ),
                    new SsoCacheHandler(),
                    new SsoRepository($this->sp),
                    new MasterclassTemplateProvider(),
                    new SsoMemcacheHandler(CacheClient::getInstance(CacheClient::GLOBAL_POOL))
                );
            case AbstractIdP::SP_GENERIC_SERVICE_TAG:
                return new GenericServiceRegisterHandler(
                    new LoginIdP(
                        new IdPEntityProvider(),
                        new IdPLogger(new MetricSSOIdpSsoIdp()),
                        new SubsystemManager(),
                        new IdPCookieProvider(),
                        new SamlAuthnResponseProvider(),
                        new RegisterValidator(
                            (new SPEntityProvider($subsystemInfo ?? []))
                        )
                    ),
                    new SsoCacheHandler(),
                    new SsoRepository($this->sp),
                    new GenericServiceTemplateProvider(),
                    new SsoMemcacheHandler(CacheClient::getInstance(CacheClient::GLOBAL_POOL))
                );
        }
        throw new RegisterInvalidServiceProviderException('Invalid Service Provider');
    }

    /**
     * @return string
     * @throws FatalException
     */
    protected function getServiceProvider(): string
    {
        $sp = $_REQUEST['_sso_sp'] ?? null;
        if (null === $sp
            || (null !== $sp && !in_array($sp, AbstractIdP::SUPPORTED_SERVICE_PROVIDERS))
        ) {
            throw new RegisterInvalidServiceProviderException('Invalid Service Provider');
        }
        return $sp;
    }

    protected function setCompanyContext()
    {
        if (null !== $this->registerManager
            && $this->registerManager instanceof AbstractRegisterHandler
            && null !== $this->registerManager->ssoIdentity
            && null !== ($cnyCacheHandler = $this->registerManager->getCompanyCacheHandler())
            && null !== ($userCacheHandler = $this->registerManager->getUserCacheHandler())
            && null !== ($cny = $cnyCacheHandler->getData()['company']['RECORD#'] ?? null)
            && null !== ($userId = $userCacheHandler->getUserRec()  ?? null)
        ) {
            $this->setGlobalUserId($userId, $cny);
        }
    }

    /**
     * @codeCoverageIgnore
     * @param string $userId
     * @param string $cny
     */
    protected function setGlobalUserId(string $userId, string $cny)
    {
        Globals::$g->_userid = sprintf('%s@%s@A', $userId, $cny);
    }
    
    /**
     * @codeCoverageIgnore
     * @return DefaultTemplateProvider
     */
    protected function getDefaultTemplateProvider(): AbstractTemplateProvider
    {
        return new DefaultTemplateProvider();
    }
}
