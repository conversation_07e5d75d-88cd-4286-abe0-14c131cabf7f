<?php

class SimpleHandShake<PERSON><PERSON><PERSON> extends <PERSON>hake<PERSON><PERSON><PERSON>
{

    /**
     * Processes ajax requests from ajax.phtml
     *
     * @param QRequestAjax $ajaxHandler  Used to communicate status back to ajax.phtml
     */
    public function processAjaxRequest($ajaxHandler)
    {
        $cmd = Request::$r->_cmd;
        switch ( $cmd ) {
            case "validateSISession":
                $this->validateSISession($ajaxHandler);
                break;
            case "getAuditPopupURL":
                $entityName = Request::$r->_ent;
                $recNo = Request::$r->_recNo;
                $operation = GetOperationId('co/lists/audittrail/view');
                $url = sprintf("editor.phtml?.popup=1&.ydialog=1&.sess=%s&.op=%s&.showgrid=1&.auditEntity=%s&.auditEntityKey=%s",
                    Session::getKey(), $operation, $entityName, urlencode($recNo));
                $returnURL = URLEncryption::transformUrl($url);
                $result[] = $returnURL;
                $ajaxHandler->setResult($result);
                break;
            default:
                $ajaxHandler->addError("Invalid command");
                break;
        }
    }


    /**
     * @param QRequestAjax $ajaxHandler  Used to communicate status back to ajax.phtml
     */
    public function validateSISession(QRequestAjax $ajaxHandler): bool {
        $sessionToValidate = Request::$r->_hSess;
        $sessionHandler = IASessionHandler::getInstance($sessionToValidate);
        $ok = true;
        $result = [];
        if (!$sessionHandler->isValidSession()) {
            $result[] = "invalid session";
            $ok = false;
        } else {
            $result[] = "valid session";
        }
        $ajaxHandler->setResult($result);
        return $ok;
    }

    public function getEmbeddedApplication()
    {
        HandshakeHandler::logMessageWhenDebug("no implementation");
    }

    public function getEndPointFromCFG()
    {
        HandshakeHandler::logMessageWhenDebug("no implementation");
    }

    public function getUIEndPointFromCFG()
    {
        HandshakeHandler::logMessageWhenDebug("no implementation");
    }

    public function constructTargetWithParameter(string $accessToken)
    {
        HandshakeHandler::logMessageWhenDebug("no implementation");
    }

    public function getAuthTarget()
    {
        HandshakeHandler::logMessageWhenDebug("no implementation");
    }

    public function getAuthEndPoint()
    {
        HandshakeHandler::logMessageWhenDebug("no implementation");
    }

    public function getAuthPayload()
    {
        HandshakeHandler::logMessageWhenDebug("no implementation");
    }

    public function getAuthRequestMethod()
    {
        HandshakeHandler::logMessageWhenDebug("no implementation");
    }

    public function getCSP()
    {
        HandshakeHandler::logMessageWhenDebug("no implementation");
    }
}