<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format">


<xsl:template name="deliveryscripts">

function statementWindow(start, end, message,markettext, period_date, start_date, end_date, op, printformat, stmtformat, groupby, showalltrans, basedOnDate, showreversedpayments, printtype, statementdate, showexternalcredit) {
	var re = / /g;
	start = start.replace(re, '');
	end = end.replace(re, '');

	var URL = 'statement.phtml?.op='+op+'&amp;.start_customer='+start+'&amp;.end_customer='+end+'&amp;.message='+escape(message)+
				'&amp;.printformat='+printformat+'&amp;.markettext='+escape(markettext)+'&amp;.period='+period_date+'&amp;.start_date='+start_date+'&amp;.end_date='+end_date+'&amp;.stmtformat='+stmtformat+'&amp;.groupby='+groupby+'&amp;.showalltrans='+showalltrans+'&amp;.basedOnDate='+basedOnDate+'&amp;.showreversedpayments='+showreversedpayments+'&amp;.printtype='+printtype+'&amp;.statementdate='+statementdate+'&amp;.showexternalcredit='+showexternalcredit+'&amp;.sess=<xsl:value-of select="/reportdata/report/@sess"/>';
    
    params = 'width=800,height=650,scrollbars=yes,dependent' ;
	var sWnd = window.open(URL, 'gl', params);
    if (sWnd == null) {return;}
	if (sWnd.opener == null){
		sWnd.opener = self;
	}
	sWnd.focus();
}

function invoiceWindow(op, r, message, markettext, doctype, invformat) {
	var URL = 'invoice.phtml'+'?.op='+op+'&amp;.r='+r
		+'&amp;.message='+escape(message)+'&amp;.markettext='+escape(markettext)
		+'&amp;.doctype='+escape(doctype)+'&amp;.invformat='+escape(invformat)
		+'&amp;.baseobject=arinvoice'+'&amp;.sess=<xsl:value-of select="/reportdata/report/@sess"/>';
	params = 'width=800,height=650,scrollbars=yes,dependent,resizable=yes' ;
	var iWnd = window.open(URL, 'invoice', params);
	if (iWnd == null) {return;}
	if (iWnd.opener == null){iWnd.opener = self;}
	iWnd.focus();
}
</xsl:template>

</xsl:stylesheet>
