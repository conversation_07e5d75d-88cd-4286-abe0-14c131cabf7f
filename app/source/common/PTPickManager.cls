<?
/**
*   FILE:         PT_PickManager.cls
*   AUTHOR:         rpn
*   DESCRIPTION: custom picker class for PT Objects and its Views
*
*   (C) 2000, Intacct Corporation, All Rights Reserved
*
*   This document contains trade secret data that belongs to Intacct
*   Corporation and is protected by the copyright laws.  Information
*   herein may not be used, copied or disclosed in whole or in part
*   without prior written consent from Intacct Corporation.
*/

class PTPickManager extends EntityManager
{
    /**
     * @param string $srcEntity
     *
     * @return string
     */
    public static function prepareSubstituteTable($srcEntity='')
    {
        $substituteTable = '';

        if ( $srcEntity != '' ) {
            $objDef = Pt_DataObjectDefManager::getByName($srcEntity);

            if ( $objDef ) {
                $myCny = GetMyCompany();
                $objDefId = $objDef->getId();

                $selects = array();
                $lookupTemplateFields = $objDef->getLookupTemplateFields();

                foreach ( $lookupTemplateFields as $field ) {
                    /** @noinspection PhpUnusedLocalVariableInspection */
                    $extName = $field->getFieldName();
                    $colName = $field->getColumnName();
                    $selects[] = $colName;
                }

                // subs_table
                $substituteTable = "(
					SELECT cny#, record#, 'R' as type,  OBJ_NAME  as OBJ_NAME, obj_def_id 
					FROM pt_obj_data
					WHERE cny# = $myCny and obj_def_id = $objDefId
					UNION ALL 
					SELECT cny#, record#, 'G' as type, VIEW_NAME || '--Group' as OBJ_NAME, obj_def_id 
					FROM pt_list_view 
					WHERE cny# = $myCny and obj_def_id = $objDefId and properties like '%isReportView=1%'
					ORDER BY 3, 4
					) ";

                if ( count($selects) > 1 ) {
                    $newFieldStr = " (".$selects[0] . " || '--' || to_char(" . $selects[1].")) ";
                    // IMPORTANT NOTE:
                    // we are heaveliy dependant on the string match for replacing look-up template fields
                    // when you change the query above please be mindfull of this, '  OBJ_NAME  ' should be
                    // maintained the way it is defined...
                    $substituteTable = str_replace("  OBJ_NAME  ", $newFieldStr, $substituteTable);
                }
            }
        }

        return $substituteTable;
    }

    /**
     * @param array $params
     * @param bool  $_crosscny
     * @param bool  $nocount
     *
     * @return array|null   null if you don't pass in any params
     */
    public function prepareGetList(&$params = [], $_crosscny = false, $nocount = true)
    {
        if ( !isset($params['SUBS_TABLE']) || !is_array($params['SUBS_TABLE']) ) {
            $srcEntity = ( $params['orig_entity'] != '' ? $params['orig_entity'] : Request::$r->_orig_entity );

            $substituteTable = self::prepareSubstituteTable($srcEntity);

            $params['SUBS_TABLE'] = array('pt_obj_data' => $substituteTable);
        }

        return parent::prepareGetList($params, $_crosscny, $nocount);
    }

}
