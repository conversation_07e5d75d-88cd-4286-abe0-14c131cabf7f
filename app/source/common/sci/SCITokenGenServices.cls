<?php
//
// FILE:            SCITokenGenerator.cls
// AUTHOR:          <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
// DESCRIPTION:
//
// (C)2000, Intacct Corporation, All Rights Reserved
//
// Intacct Corporation Proprietary Information.
// This document contains trade secret data that belongs to Intacct
// corporation and is protected by the copyright laws. Information herein
// may not be used, copied or disclosed in whole or part without prior
// written consent from Intacct Corporation.
//

class SCITokenGenServices
{
    const SCI_LOG_KEYNAME = "SCI_TOKENGEN_LOG";

    /**
     * @return \CacheClient
     */
    public function getCacheClientInstance()
    {
        return \CacheClient::getInstance();
    }

    /**
     * @param SCIGrantTypeInterface $sciGrantType
     * @param string|false $response
     * @return false|int
     */
    public function generateToken(SCIGrantTypeInterface $sciGrantType, &$response)
    {
        $theRetHeaders = null;
        $curlInfo = null;

        $this->logToFile("Generating Token");

        $responseCode = \Util::httpCall($sciGrantType->getEndPointURL(), $sciGrantType->getBody(),
            $response, false, null, null, false, null,
            null, null, false, $theRetHeaders, false,
            $curlInfo, $sciGrantType->getTimeout());
        return $responseCode;
    }

    /**
     * @param string $msg
     * @param string $level
     */
    public function logToFile($msg, $level = \LogManager::INFO)
    {
        LogToFile(self::SCI_LOG_KEYNAME . " :: " . $msg, LOG_FILE, false, $level);
    }

}
