<?php
require_once 'util.inc';
$ok = InitProfile(false, sessionPeekOnly: true, logAccess: true);
if (!$ok) {
    logToFileError("HelpAI: Could not initialize profile on window", true);
    //Fwd("noauthorization.phtml", basename($_SERVER['HTTP_REFERER']));
}

InitEnvironment();
InitGlobals();
InitModules();
SetDbCharset();
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <title>Search help</title>
    <script src="<?= URLReplace::replaceRelativeURL('../resources/qx/js/i18n.js'); ?>"></script>
    <script defer="defer"
            src="<?= URLReplace::replaceRelativeURL('../resources/thirdparty/helpai/static/js/helpaiui-v0.0.3.js'); ?>"></script>
    <link href="<?= URLReplace::replaceRelativeURL('../resources/thirdparty/helpai/static/css/helpaiui-v0.0.3.css'); ?>"
          rel="stylesheet">
    <style>
        <?php
        $fonts = [
            ['font' => 'SageUI-Regular.otf', 'weight' => 'normal'],
            ['font' => 'SageUI-Medium.otf', 'weight' => '500'],
            ['font' => 'SageUI-Bold.otf', 'weight' => 'bold']
        ];
        foreach ($fonts as $font) {
            echo "@font-face {
                font-family: 'SageUI';
                src: url('{$assetPath}/{$font['font']}') format('opentype');
                font-weight: {$font['weight']};
                font-style: normal;
            }\n";
        }

        $localTokens = ['IA.PRIVACY_NOTICE', 'IA.AI_GENERATED_CONTENT', 'IA.SEND', 'IA.GET_HELP_WITH_SAGE', 'IA.TRY_DIFFERENT_WAY', 'IA.SEARCH_HELP', 'IA.CLEAR_DATA', 'IA.CHARACTERS_LIMIT_ALLOWED', 'IA.CHARACTERS_LIMIT', 'IA.CHARACTERS_LIMIT_REMAINED', 'IA.FORMATTING_ISSUE_REPHRASE_QUERY', 'IA.TECHNICAL_ISSUE_TRY_AGAIN', 'IA.RETRY', 'IA.SUPPORT_ID', 'IA.WAS_THIS_HELPFUL', 'IA.THANKS_FOR_FEEDBACK', 'IA.YES', 'IA.NO', 'IA.ADDITIONAL_FEEDBACK', 'IA.WRONG_HELP_LINK', 'IA.NOT_RELEVANT', 'IA.NOT_ACCURATE', 'IA.TELL_US_WHY', 'IA.SUBMIT', 'IA.PROCESSING', 'IA.LOADING', 'IA.CANCEL', 'IA.GREETING', 'IA.GREETING_COMMA_DOT', 'IA.GREETING_WELCOME_BACK', 'IA.LOAD_HISTORY_HEADER_TEXT', 'IA.EMPTY_HISTORY_LIST_HEADER_TEXT', 'IA.CHATWINDOW_LOGGEDOUT_MSG'];
        I18N::addTokens(I18N::tokenArrayToObjectArray($localTokens));
        $textMap = I18N::getText();
        $textJSON = json_encode($textMap);
        ?>
        .App {
            background-color: #fff;
            display: grid;
            grid-template-rows: auto 1fr auto;
            height: 100vh;
            overflow: hidden;
            width:100%;
        }
        .header-container {
            display: none;
        }
    </style>
    <script>
        var textMap = <?= $textJSON ?>;
        const div = document.querySelector('.headerButtons-container');
        if (div) {
            div.style.display = 'none'; // Hides the div
        }

        if(localStorage.getItem('ajaxData')){
            const ajaxDataKey = localStorage.getItem('ajaxData');
            const ajaxDataContextKey = localStorage.getItem('ajaxDataContext');
            window['master-data'] = JSON.parse(ajaxDataKey);
            window['context-data'] = JSON.parse(ajaxDataContextKey);
        }

        window.addEventListener('react-app-event', function(event){
            if(event.detail.type == "copilot-search-send-enter"){
                pendo.track(
                    event.detail.type,
                    event.detail.data
                );
            }
        });
    </script>
    <?php if (FeatureConfigManagerFactory::getInstance()->isFeatureEnabled("ENABLE_PENDO")) {
        Pendo::insertPendoSnippet();
    } ?>

</head>
<!-- Container for your application content -->
<body>
<div id="root">
</body>
</html>
