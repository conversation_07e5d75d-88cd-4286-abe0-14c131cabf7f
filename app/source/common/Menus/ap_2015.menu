<?php
/**
 * ap_2015.menu
 *
 * Accounts Payable
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2014 Intacct Corporation -- All Rights Reserved.
 */

/**
 * See MenuConstants.inc for a description of all allowed keys.
 *
 * The array key for a menu item should never be changed. It is used
 * to persist the sort order of a user's favorite menu items. It is
 * not used as the name of the menu in the UI anymore (that's what
 * the MENU_NAME key is for). E.g.:
 *
 * 'FinancialReports' => [                                 // Array key, DO NOT CHANGE
 *     'ReportsHeader' => [                                // Array key, DO NOT CHANGE
 *         MENU_TYPE => MENU_SECTION,                      // Menu section w/icon
 *         MENU_NAME => 'IA.FINANCIAL_REPORTING',          // Internationalized text
 *         MENU_SECTION_ICON => 'icon-financialreports'    // Icon glyph from iafonts.css
 *     ],
 *     'My Stored Reports' => [                            // Array key, do not change
 *         MENU_NAME => 'IA.MY_STORED_REPORTS',            // Internationalized text
 *         MENU_SCRIPT => 'lister.phtml',                  // URL
 *         MENU_KEY => 'gl/reports/reportstore'            // Key from security.inc
 *     ],*
 *
 *  [...]
 */
$ap_menu = [
    'Tasks' => [
        'Tasks' => [
            MENU_TYPE => MENU_SECTION,
            MENU_NAME_HIDDEN => true
        ],
        'Vendors' => [
            MENU_TYPE => MENU_CATEGORY,
            MENU_NAME => 'IA.VENDORS',
            MENU_SCRIPT => 'lister.phtml',
            MENU_KEY => 'ap/lists/vendor',
            MENU_RENAMEABLE => true,
            MENU_CATEGORY_TITLE => true,
            MENU_CATEGORY_ITEMS => [
                'Approve Vendors' => [
                    MENU_NAME => 'IA.APPROVE_VENDORS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'ap/activities/approvevendor',
                ],
            ],
        ],
        'Automated transactions' => [
            MENU_NAME => 'IA.AUTOMATED_TRANSACTIONS',
            MENU_SCRIPT => 'lister.phtml?.userviewid=DP',
            MENU_KEY => 'ap/lists/stxautomation',
            MENU_NO_ADD_RECORD => true,
            MENU_CATEGORY_TITLE => true,
        ],
        'Bills' => [
            MENU_TYPE => MENU_CATEGORY,
            MENU_NAME => 'IA.BILLS',
            MENU_SCRIPT => 'lister.phtml?.recordtype=pi&.it=apbill',
            MENU_KEY => 'ap/lists/apbill',
            MENU_CATEGORY_TITLE => true,
            MENU_CATEGORY_ITEMS => [
                'Approve Bills' => [
                    MENU_NAME => 'IA.APPROVE_BILLS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'ap/activities/approveapbill',
                ],
                'Pay Bills' => [
                    MENU_NAME => 'IA.PAY_BILLS',
                    MENU_SCRIPT => 'editor.phtml',
                    MENU_KEY => 'ap/lists/appymt/create',
                ],
                'Recurring Bills' => [
                    MENU_NAME => 'IA.RECURRING_BILLS',
                    MENU_FAVNAME => 'IA.RECURRING_BILLS',
                    MENU_SCRIPT => 'lister.phtml?.recordtype=pi&.it=aprecurbill',
                    MENU_KEY => 'ap/lists/aprecurbill',
                ],
            ],
        ],
        'Checks' => [
            MENU_TYPE => MENU_CATEGORY,
            MENU_NAME => 'IA.CHECKS',
            MENU_CATEGORY_HEADER => true,
            MENU_CATEGORY_ITEMS => [
                'Check Reconciliation' => [
                    MENU_NAME => 'IA.CHECK_RECONCILIATION',
                    MENU_SCRIPT => 'editor.phtml?.type=chk',
                    MENU_KEY => 'cm/lists/bankacctrecon/create',
                ],
                'Print Checks' => [
                    MENU_NAME => 'IA.PRINT_CHECKS',
                    MENU_SCRIPT => 'editor.phtml?.done=' . insertDone('iaflash.phtml?.id=3&.mod=ap'),
                    MENU_KEY => 'ap/activities/apprintchecks',
                ],
                'Check Runs' => [
                    MENU_NAME => 'IA.CHECK_RUN',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'ap/lists/checkrun',
                ],
                'Add to Check Run' => [
                    MENU_NAME => 'IA.CHECK_RUN_ADD_TO',
                    MENU_SCRIPT => 'reporteditor.phtml?.mod=3.AP',
                    MENU_KEY => 'ap/activities/checkrunfilter',
                ],
            ]
        ],
        '********' => [
            MENU_TYPE => MENU_CATEGORY,
            MENU_NAME => 'IA.1099_MENU',
            MENU_CATEGORY_HEADER => true,
            MENU_CATEGORY_ITEMS => [
                'Form 1096' => [
                    MENU_NAME => 'IA.PRINT_1096_FORM',
                    MENU_SCRIPT => 'reporteditor.phtml?.object=vendor',
                    MENU_KEY => 'ap/reports/1096',
                ],
                'Form 1099' => [
                    MENU_NAME => 'IA.PRINT_1099_FORM',
                    MENU_SCRIPT => 'reporteditor.phtml?.object=vendor',
                    MENU_KEY => 'ap/reports/1099',
                ],
                'Create 1099 File' => [
                    MENU_NAME => 'IA.EXPORT_1099_FILE',
                    MENU_SCRIPT => 'reporteditor.phtml',
                    MENU_KEY => 'ap/reports/1099file',
                ],
                'Create 1099 E-File' => [
                    MENU_NAME => 'IA.1099_E_FILE',
                    MENU_SCRIPT => 'editor.phtml',
                    MENU_KEY => 'ap/lists/file1099submissionlog/create',
                ],
            ]
        ],
        'TPAR' => [
            MENU_TYPE => MENU_CATEGORY,
            MENU_NAME => 'IA.TPAR',
            MENU_CATEGORY_HEADER => true,
            MENU_CATEGORY_ITEMS => [
                'Export TPAR File' => [
                    MENU_NAME => 'IA.EXPORT_TPAR_FILE',
                    MENU_SCRIPT => 'reporteditor.phtml',
                    MENU_KEY => 'ap/reports/tparfile',
                ],
            ]
        ]
    ],
    'Payments' => [
        'PaymentsSection' => [
            MENU_TYPE => MENU_SECTION,
            MENU_NAME_HIDDEN => true,
        ],
        'Payments' => [
            MENU_TYPE => MENU_CATEGORY,
            MENU_NAME => 'IA.PAYMENTS',
            MENU_CATEGORY_HEADER => true,
            MENU_CATEGORY_ITEMS => [
                'Account Adjustments' => [
                    MENU_NAME => 'IA.ADJUSTMENTS',
                    MENU_SCRIPT => 'lister.phtml?.recordtype=pa',
                    MENU_KEY => 'ap/lists/apadjustment',
                ],
                'Advances' => [
                    MENU_NAME => 'IA.ADVANCES',
                    MENU_SCRIPT => 'lister.phtml?.recordtype=pr',
                    MENU_KEY => 'ap/lists/apadvance',
                ],
                'Approve Payments' => [
                    MENU_NAME => 'IA.APPROVE_PAYMENTS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'ap/activities/approvepayments',
                ],
                'Manual Payment' => [
                    MENU_NAME => 'IA.MANUAL_PAYMENT',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'ap/lists/apquickpay',
                ],
                'Posted Payments' => [
                    MENU_NAME => 'IA.POSTED_PAYMENTS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'ap/lists/appostedpayment',
                ],
                'Payment Copies' => [
                    MENU_NAME => 'IA.PRINT_PAYMENT_COPIES',
                    MENU_SCRIPT => 'reporteditor.phtml?.mod=3.AP',
                    MENU_KEY => 'ap/activities/apcheckcopy',
                ],
                'Payments files ' => [
                    MENU_NAME => 'IA.PAYMENT_FILES',
                    MENU_POPUPMENUS => [
                        'ACH Payment File' => [
                            MENU_NAME => 'IA.ACH_PAYMENT_FILE',
                            MENU_SCRIPT => 'lister.phtml',
                            MENU_KEY => 'cm/lists/achpaymentfile'
                        ],
                        'Generate ACH File' => [
                            MENU_NAME => 'IA.ACH_FILE_GENERATION',
                            MENU_SCRIPT => 'lister.phtml?.mod=11.CM',
                            MENU_KEY => 'cm/activities/achfilegenerator'
                        ],
                        'Generate bank payment files' => [
                            MENU_NAME => 'IA.BANK_FILE_GENERATION',
                            MENU_SCRIPT => 'lister.phtml?.mod=11.CM',
                            MENU_KEY => 'cm/activities/bankfilegenerator'
                        ],
                        'Bank payment files' => [
                            MENU_NAME => 'IA.BANK_PAYMENT_FILES',
                            MENU_SCRIPT => 'lister.phtml',
                            MENU_KEY => 'cm/lists/bankfile'
                        ],

                    ]
                ],
                'Release Bills to Pay' => [
                    MENU_NAME => 'IA.RELEASE_BILLS_TO_PAY',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'ap/lists/releasetopay',
                ],
                'Select Bills to Pay' => [
                    MENU_NAME => 'IA.SELECT_BILLS_TO_PAY',
                    MENU_SCRIPT => 'selecttopay_fs.phtml?.mod=3.AP',
                    MENU_KEY => 'ap/activities/payments',
                ],
                'Vendor Payment Processing' => [
                    MENU_NAME => 'IA.VENDOR_PAYMENT_SERVICES',
                    MENU_SCRIPT => 'editor.phtml?.done=' . urlencode('iaflash.phtml?.id=3&.mod=ap'),
                    MENU_KEY => 'ap/activities/apoutsourcedchecks',
                    MENU_RENAMEABLE => true,
                ],
                'Payment Requests' => [
                    MENU_NAME => 'IA.PAYMENT_REQUESTS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'ap/lists/appaymentrequest',
                ],
                'Wells Fargo Payment Manager' => [
                    MENU_NAME => 'IA.WELLS_FARGO_PAYMENT_MANAGER_SERVICE',
                    MENU_FAVNAME => 'IA.WELLS_FARGO_PMGR',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'ap/lists/wfpmbatch',
                ],
            ],
        ],
        'Retainage' => [
            MENU_NAME => 'IA.RETAINAGE',
            MENU_FAVNAME => 'IA.AP_RETAINAGE',
            MENU_SCRIPT => 'lister.phtml',
            MENU_KEY => 'ap/lists/apretainagerelease',
            MENU_RENAMEABLE => true,
            MENU_CATEGORY_TITLE => true
        ],
        'Subledger' => [
            MENU_TYPE => MENU_CATEGORY,
            MENU_NAME => 'IA.SUBLEDGER',
            MENU_CATEGORY_HEADER => true,
            MENU_CATEGORY_ITEMS => [
                'Close Subledger' => [
                    MENU_NAME => 'IA.CLOSE',
                    MENU_FAVNAME => 'IA.CLOSE_SUBLEDGER',
                    MENU_SCRIPT => 'editor.phtml',
                    MENU_KEY => 'ap/lists/apclosesummary/edit',
                ],
                'Open Subledger' => [
                    MENU_NAME => 'IA.OPEN',
                    MENU_FAVNAME => 'IA.OPEN_SUBLEDGER',
                    MENU_SCRIPT => 'editor.phtml',
                    MENU_KEY => 'ap/lists/apopensummary/edit',
                ],
                'Summary List' => [
                    MENU_NAME => 'IA.SUMMARIES',
                    MENU_FAVNAME => 'IA.SUBLEDGER_SUMMARIES',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'ap/activities/apbatch',
                ],
            ],
        ],
        'More' => [
            MENU_TYPE => MENU_CATEGORY,
            MENU_NAME => 'IA.MORE',
            MENU_CATEGORY_HEADER => true,
            MENU_CATEGORY_ITEMS => [
                'Loans' => [
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'ap/lists/loan',
                    MENU_NAME => 'IA.LOANS'
                ]
            ]
        ],
    ],
    'Reports' => [
        'ReportsSection' => [
            MENU_TYPE => MENU_SECTION,
            MENU_NAME_HIDDEN => true,
        ],
        'Custom Views' => [
            MENU_CATEGORY_HEADER => true,
            MENU_NAME => 'IA.CUSTOM_VIEWS',
        ],
        'Reports' => [
            MENU_NAME => 'IA.REPORTS',
            MENU_TYPE => MENU_CATEGORY,
            MENU_CATEGORY_HEADER => true,
            MENU_CATEGORY_LINEBREAK => true,
            MENU_CATEGORY_ITEMS => [
                'My Stored Reports' => [
                    MENU_NAME => 'IA.MY_STORED_REPORTS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'ap/reports/reportstore',
                ],
                'Memorized Reports' => [
                    MENU_NAME => 'IA.MEMORIZED_REPORTS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'ap/lists/memorizedreports',
                    MENU_NO_ADD_RECORD => true,
                ],
                'Custom Reports' => [
                    MENU_NAME => 'IA.CUSTOM_REPORTS',
                ],
            ],
        ],
        'ReportsList' => [
            MENU_TYPE => MENU_CATEGORY,
            MENU_CATEGORY_ITEMS => [
                '********' => [
                    MENU_NAME => 'IA.1099_MENU',
                    MENU_POPUPMENUS => [
                        '1099 Reports' => [
                            MENU_NAME => 'IA.1099_REPORT',
                            MENU_SCRIPT => 'reporteditor.phtml',
                            MENU_KEY => 'ap/reports/report1099',
                        ],
                        'Form DE542' => [
                            MENU_NAME => 'IA.DE542_FORM',
                            MENU_SCRIPT => 'reporteditor.phtml',
                            MENU_KEY => 'ap/reports/de542',
                        ],
                    ],
                ],
                'AP Ledger' => [
                    MENU_NAME => 'IA.AP_LEDGER',
                    MENU_SCRIPT => 'reporteditor.phtml',
                    MENU_KEY => 'ap/reports/apledger',
                ],
                'Lettrage' => [
                    MENU_NAME => 'IA.VENDOR_LETTRAGE_REPORT',
                    MENU_SCRIPT => 'reporteditor.phtml',
                    MENU_KEY => 'ap/reports/vendorlettrage',
                ],
                'AP Open Items Revaluation Report' => [
                    MENU_NAME => 'IA.AP_OPEN_ITEMS_REVALUATION',
                    MENU_FAVNAME => 'IA.AP_OPEN_ITEMS_REVALUATION_REPORT',
                    MENU_SCRIPT => 'reporteditor.phtml',
                    MENU_KEY => 'ap/reports/apreval',
                    MENU_MCP_KEY => 'ap/reports/apreval',
                ],
                'AP Recurring Report' => [
                    MENU_NAME => 'IA.AP_RECURRING',
                    MENU_FAVNAME => 'IA.AP_RECURRING_REPORT',
                    MENU_SCRIPT => 'reporteditor.phtml',
                    MENU_KEY => 'ap/reports/aprecur',
                ],
                'Bills Analysis' => [
                    MENU_NAME => 'IA.BILLS_ANALYSIS',
                    MENU_POPUPMENUS => [
                        'Bills Analysis Graph' => [
                            MENU_NAME => 'IA.GRAPH',
                            MENU_FAVNAME => 'IA.BILL_ANALYSIS_GRAPH',
                            MENU_SCRIPT => 'reporteditor.phtml',
                            MENU_KEY => 'ap/reports/billsanalysisgraph',
                        ],
                        'Bills Analysis' => [
                            MENU_NAME => 'IA.REPORT',
                            MENU_FAVNAME => 'IA.BILL_ANALYSIS_REPORT',
                            MENU_SCRIPT => 'reporteditor.phtml',
                            MENU_KEY => 'ap/reports/billsanalysis',
                        ],
                    ]
                ],
                'Reclassification Report' => [
                    MENU_NAME => 'IA.RECLASSIFICATION',
                    MENU_FAVNAME => 'IA.RECLASSIFICATION_REPORT',
                    MENU_SCRIPT => 'reporteditor.phtml',
                    MENU_KEY => 'ap/reports/apreclass',
                ],
                'Recurring Transaction Status' => [
                    MENU_NAME => 'IA.RECURRING_TRANSACTION_STATUS',
                    MENU_FAVNAME => 'IA.RECURRING_TRANSACTION_STATUS_REPORT',
                    MENU_SCRIPT => 'reporteditor.phtml',
                    MENU_KEY => 'ap/reports/recurtxnappostatus'
                ],
                'Registers' => [
                    MENU_NAME => 'IA.REGISTERS',
                    MENU_POPUPMENUS => [
                        'Bills Register' => [
                            MENU_NAME => 'IA.BILLS',
                            MENU_FAVNAME => 'IA.BILLS_REGISTER',
                            MENU_SCRIPT => 'reporteditor.phtml',
                            MENU_KEY => 'ap/reports/billsregister',
                        ],
                        'Check Register' => [
                            MENU_NAME => 'IA.CHECK',
                            MENU_FAVNAME => 'IA.CHECK_REGISTER',
                            MENU_SCRIPT => 'reporteditor.phtml',
                            MENU_KEY => 'ap/reports/checkregister',
                        ],
                    ]
                ],
                'Vendor aging' => [
                    MENU_NAME => 'IA.VENDOR_AGING',
                    MENU_RENAMEABLE => true,
                    MENU_POPUPMENUS => [
                        'Vendor Aging Graphs' => [
                            MENU_NAME => 'IA.GRAPH',
                            MENU_FAVNAME => 'IA.VENDOR_AGING_GRAPH',
                            MENU_SCRIPT => 'reporteditor.phtml',
                            MENU_KEY => 'ap/reports/apagegraph',
                            MENU_RENAMEABLE => true,
                        ],
                        'Vendor Aging Reports' => [
                            MENU_NAME => 'IA.REPORT',
                            MENU_FAVNAME => 'IA.VENDOR_AGING_REPORT',
                            MENU_SCRIPT => 'reporteditor.phtml',
                            MENU_KEY => 'ap/reports/vendor_aging',
                            MENU_RENAMEABLE => true,
                        ],
                    ]
                ],

                'Vendor List Report' => [
                    MENU_NAME => 'IA.VENDOR_LIST',
                    MENU_FAVNAME => 'IA.VENDOR_LIST_REPORT',
                    MENU_SCRIPT => 'reporteditor.phtml',
                    MENU_KEY => 'ap/reports/entitylist',
                    MENU_RENAMEABLE => true,
                ],
                'Vendor Insurance Report' => [
                    MENU_NAME => 'IA.VENDOR_INSURANCE',
                    MENU_FAVNAME => 'IA.VENDOR_INSURANCE_REPORT',
                    MENU_SCRIPT => 'reporteditor.phtml',
                    MENU_KEY => 'ap/reports/vendorinsurance',
                    MENU_RENAMEABLE => true,
                ],
                'Taxable payments annual report (TPAR)' => [
                    MENU_NAME    => 'IA.TPAR_REPORT',
                    MENU_FAVNAME => 'IA.TPAR_REPORT',
                    MENU_SCRIPT  => 'reporteditor.phtml',
                    MENU_KEY     => 'ap/reports/tparreport',
                ],
                'T5018' => [
                    MENU_NAME => 'IA.T5018_MENU',
                    MENU_POPUPMENUS => [
                        'T5018 Report for Canada' => [
                            MENU_NAME => 'IA.T5018_REPORT',
                            MENU_SCRIPT => 'reporteditor.phtml',
                            MENU_KEY => 'ap/reports/t5018report',
                        ],
                        'T5018 export file' => [
                            MENU_NAME => 'IA.EXPORT_T5018_XML',
                            MENU_SCRIPT => 'reporteditor.phtml',
                            MENU_KEY => 'ap/reports/t5018xmlfile',
                        ],
                    ],
                ],
                'AP Amortization Forecast Report' => [
                    MENU_NAME    => 'IA.AMORTIZATION_FORECAST_REPORT',
                    MENU_FAVNAME => 'IA.AMORTIZATION_FORECAST_REPORT',
                    MENU_SCRIPT  => 'reporteditor.phtml',
                    MENU_KEY     => 'ap/reports/apamortizationforecastreport',
                ]
            ]
        ]
    ],
    'Setup' => [
        'Setup' => [
            MENU_TYPE => MENU_SECTION,
            MENU_NAME_HIDDEN => true
        ],
        'Configuration' => [
            MENU_NAME => 'IA.CONFIGURATION',
            MENU_FAVNAME => 'IA.ACCOUNTS_PAYABLE_CONFIGURATION',
            MENU_MODULE_CONFIG => 'ap',
            MENU_TYPE => MENU_CATEGORY,
            MENU_CATEGORY_TITLE => true
        ],
        'AP Account Labels' => [
            MENU_NAME => 'IA.ACCOUNT_LABELS',
            MENU_SCRIPT => 'lister.phtml',
            MENU_KEY => 'ap/lists/apaccountlabel',
            MENU_TYPE => MENU_CATEGORY,
            MENU_CATEGORY_TITLE => true
        ],
        'AP Amortization Template' => [
            MENU_NAME => 'IA.AMORTIZATION_TEMPLATE',
            MENU_SCRIPT => 'lister.phtml',
            MENU_KEY => 'ap/lists/apamortizationtemplate',
            MENU_TYPE => MENU_CATEGORY,
            MENU_CATEGORY_TITLE => true
        ],
        'Vendors' => [
            MENU_TYPE => MENU_CATEGORY,
            MENU_NAME => 'IA.VENDORS',
            MENU_SCRIPT => 'lister.phtml',
            MENU_KEY => 'ap/lists/vendor',
            MENU_RENAMEABLE => true,
            MENU_CATEGORY_TITLE => true,
            MENU_CATEGORY_ITEMS => [
                'Vendor Types' => [
                    MENU_NAME => 'IA.TYPES',
                    MENU_FAVNAME => 'IA.VENDOR_TYPES',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'ap/lists/vendtype',
                    MENU_RENAMEABLE => true,
                ],
                'Vendor Visibility' => [
                    MENU_NAME => 'IA.VISIBILITY',
                    MENU_FAVNAME => 'IA.VENDOR_VISIBILITY',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'ap/lists/vendorvisibility',
                    MENU_RENAMEABLE => true,
                    MENU_ENTITY => 'vendor',
                ],
                'Vendor Groups' => [
                    MENU_NAME => 'IA.GROUPS',
                    MENU_FAVNAME => 'IA.VENDOR_GROUPS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'ap/lists/vendorgroup',
                    MENU_RENAMEABLE => true,
                ],
            ]
        ],
        'Tax' => [
            MENU_NAME => 'IA.TAX',
            MENU_TYPE => MENU_CATEGORY,
            MENU_CATEGORY_HEADER => true,
            MENU_CATEGORY_ITEMS => [
                'Tax Detail' => [
                    MENU_NAME => 'IA.DETAILS',
                    MENU_FAVNAME => 'IA.TAX_DETAILS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'ap/lists/potaxdetail'
                ],
                'Tax Schedule' => [
                    MENU_NAME => 'IA.SCHEDULE_MENU',
                    MENU_FAVNAME => 'IA.TAX_SCHEDULES',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'ap/lists/potaxschedule'
                ],
                'Tax Schedule Map' => [
                    MENU_NAME => 'IA.SCHEDULE_MAPS',
                    MENU_FAVNAME => 'IA.TAX_SCHEDULE_MAPS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'ap/lists/aptaxschedmap'
                ],
            ],
        ],
        'More' => [
            MENU_TYPE => MENU_CATEGORY,
            MENU_NAME => 'IA.MORE',
            MENU_CATEGORY_HEADER => true,
            MENU_CATEGORY_ITEMS => [
                'Loan Types' => [
                    MENU_NAME => 'IA.LOAN_TYPES',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'ap/lists/loantype',
                ],
                'AP Terms' => [
                    MENU_NAME => 'IA.TERMS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'ap/lists/apterm',
                ],
            ]
        ]
    ],
];
