<?php

/**
 * Manager class for the Storage Options screen for Report Groups
 *
 * <AUTHOR> <<EMAIL>>

 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */



/**
 * Manager class for the Storage Options screen
 */

class StorageOptionsRptGrpManager extends EntityManager
{

    /**
     * @param array $params the default parameters
     */

    public function __construct($params = array())
    {

        parent::__construct($params);

    }


    /**
     * No add method
     *
     * @param array $values
     *
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        echo '<pre>';
        print_r(array(__FILE__.":". __LINE__, $values));
        echo '</pre>';
        dieFL(__FILE__.":". __LINE__);
        return false;

    }

}
