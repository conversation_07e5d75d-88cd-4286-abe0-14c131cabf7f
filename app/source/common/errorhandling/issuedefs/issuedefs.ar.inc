<?php


$IAi18ErrorDef['ar'] = [
    'AR-0001' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PLEASE_ENTER_THE_VALID_DEFERRED_REVENUE_ACCOUNT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000007',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0002' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THIS_TRANSACTION_CANNOT_BE_EDITED_BECAUSE_IT_HA',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.ACCOUNT_RECEIVEABLE_IS_CONFIGURED_TO_DISABLE_ED',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.CHECK_WITH_YOUR_ADMINISTRATOR',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL0001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0003' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THIS_TRANSACTION_IS_IN_DRAFT_STATE_AND_CAN_NOT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0004' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_CREDIT_CARD_PAYMENT_METHOD_CAN_ONLY_BE_DEPO',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000207',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['The credit card payment method can only be deposited into a bank account. Select a bank account and try again.' => 'The credit card payment method can only be deposited into a bank account. Select a bank account and try again.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0005' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.RECORD_TRANSFER_PAYMENTS_CAN_ONLY_BE_DEPOSITED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000207',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Record transfer payments can only be deposited into a bank account. Select a bank account and try again.' => 'Record transfer payments can only be deposited into a bank account. Select a bank account and try again.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0006' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.YOU_CAN_NOT_EDIT_THE_AUTO_BATCH',
        IAIssueDef::KEY_DESCRIPTION2_ID => '',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL02000094',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0007' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UNDO_THE_BANK_ACCOUNT_RECONCILIATION_THAT_INCLU',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01000137',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0008' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.VOID_OR_DELETE_THE_THE_GENERATED_ACH_FILE_FOR_T',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000205',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0009' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.A_CHECK_WAS_CREATED_FOR_THIS_PAYMENT_VOID_THE_C',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000205',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['A check was created for this payment. Void the check, then delete the payment request.' => 'A check was created for this payment. Void the check, then delete the payment request.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0010' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_A_VALID_PAYMENT_KEY_THEN_TRY_AGAIN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000205',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0011' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THIS_PAYMENT_IS_CONFIRMED_YOU_CAN_VOID_THE_PAYM',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01000137',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0012' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_CURRENCY_FOR_PAYMENT_METHOD',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.CURRENTLY_ONLY_USD_IS_SUPPORTED_FOR_PAYMENT_MET',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0013' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.INVALID_GATEWAY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000081',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0014' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_THE_AMOUNT_TO_BE_PAID_BY_THE_SELECTED_BAN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000051',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0015' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THERE_IS_NO_DEFAULT_CARD_SET_FOR_THE_SELECTED_C',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000071',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0016' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUSTOMER_CHARGE_CARD_TYPE_IS_A_REQUIRED_FIELD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000071',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0017' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.EXPIRY_DATE_IS_A_REQUIRED_FIELD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000071',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0018' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CREDIT_CARD_VALIDATION_FAILED',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PLEASE_CHECK_THE_CARD_NUMBER_AND_EXPIRY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000071',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0019' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUSTOMER_CHARGE_CARD_NUMBER_IS_A_REQUIRED_FIELD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000071',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0020' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THERE_IS_NO_DEFAULT_ACCOUNT_SET_FOR_THE_SELECTE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000071',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0021' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BANK_ACCOUNT_IS_A_REQUIRED_FIELD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000071',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0022' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BANK_ACCOUNT_TYPE_IS_A_REQUIRED_FIELD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000071',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0023' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BANK_ACCOUNT_NUMBER_IS_A_REQUIRED_FIELD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000071',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0024' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BANK_ROUTING_NUMBER_IS_A_REQUIRED_FIELD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000071',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0025' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BANK_ACCOUNT_HOLDER_IS_A_REQUIRED_FIELD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000071',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0026' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THIS_COMPANY_IS_NOT_AUTHORIZED_FOR_ONLINE_TRANS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000071',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0027' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ONLINE_ACH_DEBIT_PAYMENTS_HAVE_BEEN_DISABLED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL04002015',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0028' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ONLINE_CHARGE_CARD_PAYMENTS_HAVE_BEEN_DISABLE_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL04002015',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Online Charge Card payments have been disabled.' => 'Online credit card payments were disabled.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0029' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.INVALID_GATEWAY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000077',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0030' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.AR_PAYMENT_CANNOT_BE_CREATED_FOR_FOREIGN_CURREN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['AR payment cannot be created for foreign currency invoices as the batch currency is not setup.' => 'AR payment cannot be created for foreign currency invoices because the batch currency is not setup.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0031' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.AR_ERROR_WITH_VALUE_FOR_FIELD_GIVEN_VALUE_EXPECTED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0032' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.INVALID_OVERPAYMENT_LOCATION_ID',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.IN_THE_OVERPAYMENT_LOCATION_FIELD_ENTER_THE_BAN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0033' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PLEASE_ENTER_VALID_A_RECEIPT_DATE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Please enter valid a Receipt Date' => 'Enter a valid receipt date'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0034' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_PAYMENT_DATE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000015',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0035' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PAYMENT_DATE_CANNOT_BE_BEFORE_INVOICE_CREATIO_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Payment date cannot be before Invoice creation date.' => 'The payment date cannot be earlier than the invoice creation date.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0036' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.GL_ACCOUNT_FOR_OVERPAYMENT_IN_AR_PREFERENCES_IS',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PLEASE_CHOOSE_A_VALID_OVERPAYMENT_LOCATION',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0037' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.INVALID_OVERPAYMENT_DEPARTMENT_ID',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PLEASE_CHOOSE_A_VALID_OVERPAYMENT_DEPARTMENT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0038' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.GL_ACCOUNT_FOR_OVERPAYMENT_IN_AR_PREFERENCES__1',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PLEASE_CHOOSE_A_VALID_OVERPAYMENT_DEPARTMENT_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0039' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.OVERPAYMENT_LOCATION_AT_ROOT_FOR_MULTIPLE_BASE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0040' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.OVERPAYMENT_CURRENCY_AT_ROOT_FOR_MULTIPLE_BASE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Overpayment currency at root for multiple base currency company must be the same as the bank currency and invoice base currency.' => 'Overpayment currency at root for multiple base currency company must be the same as the bank currency and invoice base currency.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0041' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CANNOT_CREATE_FOREIGN_CURRENCY_OVERPAYMENT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000098',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0042' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.YOU_ARE_RECORDING_AN_OVERPAYMENT_TO_A_BANK_WITH',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.TO_RECORD_THIS_PAYMENT_ASSIGN_A_LOCATION_TO_YOU',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0043' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNTS_RECEIVABLE_PAYMENT_REQUEST_DID_NOT_POS',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.A_VALID_PAYMENT_ACCOUNT_TYPE_HAS_NOT_BEEN_SPECI',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PROVIDE_A_VALID_BANK_ACCOUNT_OR_AN_UNDEPOSITED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0044' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_BASECURRENCY_FIELD_IS_REQUIRED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000023',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0045' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_BASE_CURRENCY_IN_REQUEST_DOES_NOT_MATCH_WIT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000023',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0046' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_CURRENCY_FIELD_IS_REQUIRED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000023',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0047' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_CURRENCY_FIELD_SHOULD_BE_EXACTLY_3_CHARACTE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000004',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0048' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THIS_TRANSACTION_CURRENCY_DOES_NOT_EXIST_FOR_TH',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.PLEASE_SELECT_THE_CURRENCY_FROM_THE_EXISTING_TR',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000004',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0049' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.AUTO_PAYMENT_SUMMARY_DID_NOT_POST',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.YOU_CANNOT_USE_AN_AUTO_PAYMENT_SUMMARY_WHEN_A_M',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PROVIDE_A_MANUAL_PAYMENT_SUMMARY_WITH_THIS_PAYM',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [7, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Auto payment summary did not post.' => 'Auto payment summary did not post.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['You cannot use an auto payment summary when a manual payment summary is required.' => 'An auto payment summary cannot be used when a manual payment summary is required.'],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Provide a manual payment summary with this payment or update the payment summary frequency within the accounts receivable configuration.' => 'Provide a manual payment summary with this payment or update the payment summary frequency within the Accounts Receivable configuration.']
        ]
    ],
    'AR-0050' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.AMOUNTTOPAY_VALUE_ENTERED_IS_NOT_VALID',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.ENTER_A_VALID_NUMBER_AND_TRY_AGAIN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000098',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0051' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.TRX_AMOUNTTOPAY_VALUE_ENTERED_IS_NOT_VALID',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.ENTER_A_VALID_NUMBER_AND_TRY_AGAIN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000098',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0052' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.AMOUNT_ON_CHECK_CANNOT_BE_LESS_THAN_THE_AMOUNT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Amount on Check cannot be less than the amount selected for check payment.' => 'Amount on Check cannot be less than the amount selected for check payment.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0053' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PAYMENT_AMOUNT_EXCEEDS_TRANSACTION_AMOUNT_TO_PA',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.IF_THIS_AN_OVERPAYMENT_ENTER_THE_AMOUNT_AS_AN_O',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0054' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PAYMENT_METHODS_CHARGE_CARD_AND_ACH_ARE_NOT_ALL',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0055' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CREDIT_CARD_AMOUNT_SHOULD_BE_GREATER_THAN_0',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000071',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0056' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.ONLINE_ACH_DEBIT_PAYMENTS_HAVE_BEEN_DISABLED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL04002015',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0057' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.ONLINE_CHARGE_CARD_PAYMENTS_HAVE_BEEN_DISABLE_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL04002015',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0058' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_CURRENCY_FOR_PAYMENT_METHOD_ONLINE_CHAR',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.CURRENTLY_ONLY_USD_IS_SUPPORTED_FOR_PAYMENT_MET',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0059' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_CURRENCY_FOR_PAYMENT_METHOD_ONLINE_ACH',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.CURRENTLY_ONLY_USD_IS_SUPPORTED_FOR_PAYMENT_M_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0060' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CREDIT_CARD_AMOUNT_SHOULD_BE_GREATER_THAN_0',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000071',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0061' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PLEASE_SELECT_A_CREDIT_CARD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000071',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0062' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THIS_TRANSACTION_CANNOT_BE_EDITED_BECAUSE_IT_HA',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.ACCOUNT_RECEIVEABLE_IS_CONFIGURED_TO_DISABLE_ED',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.CHECK_WITH_YOUR_ADMINISTRATOR',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL0001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0063' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THIS_TRANSACTION_IS_IN_DRAFT_STATE_AND_CAN_NOT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0064' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_PRINT_THE_TRANSACTION',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0065' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ARPAYMENT_BATCH_RECORD_MUST_HAVE_A_VALID_ACCOUN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['ARPayment Batch record must have a valid ACCOUNTNOKEY' => 'ARPayment Batch record must have a valid ACCOUNTNOKEY'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0066' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNTS_RECEIVABLE_PAYMENT_REQUEST_DID_NOT_POS',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.THE_PAYMENT_SUMMARY_PROVIDED_IS_INVALID_OR_EMPT',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PROVIDE_A_VALID_PAYMENT_BATCH_KEY_WITH_THIS_PAY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [2, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['The payment summary provided is invalid or empty.' => 'The payment summary provided isn’t valid or is empty.'],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0067' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNTS_RECEIVABLE_PAYMENT_REQUEST_DID_NOT_POS',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.THE_PAYMENT_SUMMARY_PROVIDED_IS_INVALID_OR_EMPT',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PROVIDE_A_VALID_PAYMENT_BATCH_KEY_WITH_THIS_PAY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [2, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['The payment summary provided is invalid or empty.' => 'The payment summary provided isn’t valid or is empty.'],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0068' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNTS_RECEIVABLE_PAYMENT_REQUEST_DID_NOT_POS',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.THE_PAYMENT_SUMMARY_BATCH_PROVIDED_IS_CLOSED',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PROVIDE_A_VALID_PAYMENT_BATCH_KEY_WITH_THIS_PAY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [2, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['The payment summary batch provided is closed.' => 'The payment summary batch provided is closed.'],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0069' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_CREATE_PAYMENT_DEFAULTS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0070' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_UPDATE_PAYMENT_DEFAULTS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0071' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THIS_TRANSACTION_CURRENCY_DOES_NOT_EXIST_FOR_TH',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.PLEASE_SELECT_THE_CURRENCY_FROM_THE_EXISTING_TR',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000004',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0072' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.YOU_HAVE_TO_SELECT_A_PAYMENT_BATCH_BEFORE_SAVIN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000208',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0073' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.YOU_CANNOT_USE_A_QUICK_DEPOSIT_BATCH_FOR_NORMAL',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000208',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0074' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.YOU_CAN_NOT_SAVE_CHARGE_CARD_PAYMENTS_IN_AN_UND',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000207',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['You can not save Charge Card Payments in an Undeposited Funds Batch.' => 'Credit card payments cannot be saved in Undeposited Funds.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0075' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.YOU_CAN_NOT_SAVE_RECORD_TRANSFER_PAYMENTS_IN_AN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000207',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0076' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THIS_INVOICE_COULD_NOT_BE_PAID_BECAUSE_THE_DISC',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000098',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['This invoice could not be paid because the discounted total amount due is not greater than zero.' => 'This invoice could not be paid because the discounted total amount due is not greater than zero.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0077' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_INVOICE_LINE_ITEM_PAYMENT_AMOUNT_THE_IN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0078' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_PAYMENT_AMOUNT_THE_PAYMENT_AMOUNT_CANNO',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0079' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_PAYMENT_AMOUNT_THE_PAYMENT_AMOUNT_CAN_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0080' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PAYMENT_AMOUNT_IS_LESS_THAN_AMOUNT_BEING_APPLIE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'XL03000008',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0081' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_TRANSLATED_AMOUNT_THE_TRANSLATED_AMOUNT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0082' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PLEASE_PROVIDE_BANK_ACCOUNT_OR_UNDEPOSITED_FUND',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0083' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PLEASE_PROVIDE_A_PAYMENT_BATCH_KEY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0084' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BATCH_KEY_SPECIFIED_IS_INVALID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL02000046',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0085' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_BATCH_IS_CLOSED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL02000087',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0086' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.WE_CAN_NOT_PAY_A_FOREIGN_CURRENCY_INVOICE_USING',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000051',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['We can not pay a Foreign Currency Invoice using another Foreign Currency Batch.' => 'We cannot pay a Foreign Currency Invoice using another Foreign Currency Batch.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0087' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.WE_NEED_TO_PASS_THE_TRANSLATED_BASE_AMOUNT_FOR',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000051',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0088' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ONE_OR_MORE_INVOICES_ARE_IN_DRAFT_STATUS_POST_T',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0089' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.XSL_TRANSLATION_FAILED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['XSL Translation failed' => 'XSL Translation failed'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0090' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_PRINT_THE_TRANSACTION',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0091' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_PRINT_THE_TRANSACTION',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0092' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.EMAIL_LOGO_IS_NOT_SET_IN_COMPANY_SETUP',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000185',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0093' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ERROR_RETRIEVING_EMAIL_LOGO_FROM_COMPANY_SETUP',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000185',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0094' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ERROR_RETRIEVING_MIME_TYPE_OF_EMAIL_LOGO',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000185',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0095' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_RETRIEVE_DOCUMENT_ATTACHMENTS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000185',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0096' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_RETRIEVE_DOCUMENT_ATTACHMENTS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000185',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0097' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THIS_OBJECT_IS_NOT_AVAILABLE_TO_THIS_COMPANY_PL',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000155',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0098' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THERE_IS_NO_PAYMENT_PROVIDED_TO_PROCESS',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.PLEASE_PROVIDE_PAYMENT_DETAILS_TO_PROCESS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0099' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THIS_OBJECT_IS_NOT_AVAILABLE_TO_THIS_COMPANY_PL',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000155',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0100' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNTS_RECEIVABLE_PAYMENT_REQUEST_DID_NOT_POS',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PROVIDE_A_VALID_UNDEPOSITED_FUNDS_ACCOUNT_NUM_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [5, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Accounts receivable payment request did not post.' => 'Accounts Receivable payment request did not post.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Provide a valid undeposited funds account number or bank account or the batch with this payment. ' => 'Provide a valid undeposited funds account number or bank account or the batch with this payment. ']
        ]
    ],
    'AR-0101' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.YOU_CANNOT_USE_A_QUICK_DEPOSIT_BATCH_FOR_NORMAL',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000208',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0102' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.NO_GENERAL_LEDGER_ACCOUNT_RECORDS_COULD_BE_FETC',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.INVALID_UNDEPOSITED_FUNDS_ACCOUNT_NUMBER_PROVID',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PLEASE_PROVIDE_A_VALID_UNDEPOSITED_FUNDS_ACCOUN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0103' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.A_BANK_OR_UNDEPOSITED_FUNDS_ACCOUNT_MUST_BE_SEL',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL02000047',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0104' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_UNDEPOSITED_FUNDS_ACCOUNT_SELECTED_IS_INVAL',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL02000047',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0105' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_BANK_ACCOUNT_YOU_HAVE_SELECTED_DOES_NOT_HAV',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0106' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_TRANSACTION_CAN_T_BE_CREATED_BECAUSE_THE_NE',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.THE_NET_TRANSACTION_AMOUNT_FOR_A_MANUAL_DEPOSIT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002073',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0107' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THERE_WAS_AN_ERROR_PROCESSING_YOUR_REQUEST_PLEA',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002146',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0108' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_A_INVOICE_NUMBER_AND_TRY_AGAIN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000023',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Enter a Invoice number, and try again' => 'Enter an invoice number, and try again'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0109' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_CHARGE_CARD_SELECTED_WILL_EXPIRE_BEFORE_ANY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000014',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['The Charge Card selected will expire before any invoices are created' => 'The selected credit card will expire before any invoices are created.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0110' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ZERO_AMOUNT_RECURRING_INVOICES_CANNOT_BE_PAID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002118',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Zero amount recurring invoices cannot be paid' => 'Zero amount recurring invoices cannot be paid'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0111' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.NEGATIVE_RECURRING_INVOICES_CANNOT_BE_PAID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002118',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Negative recurring invoices cannot be paid' => 'Negative recurring invoices cannot be paid'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0112' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.RECURRING_INVOICES_CANNOT_BE_OVERPAID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002119',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Recurring invoices cannot be overpaid' => 'Recurring invoices cannot be overpaid'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0113' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PAYMENT_AMOUNT_MUST_BE_GREATER_THAN_ZERO',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000098',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0114' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_CURRENCY_FOR_PAYMENT_METHOD_ONLINE_CHAR',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.CURRENTLY_ONLY_USD_IS_SUPPORTED_FOR_PAYMENT_MET',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [3, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Invalid currency for payment method Online Charge Card' => 'Use a valid currency for payment method Online Credit Card, then try again.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['Currently, only \'USD\' is supported for payment method Online Charge Card.' => 'Currently, only USD is supported for payment method Online Credit Card.'],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0115' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_CURRENCY_FOR_PAYMENT_METHOD_ONLINE_ACH',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.CURRENTLY_ONLY_USD_IS_SUPPORTED_FOR_PAYMENT_M_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0116' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PAYMENT_AMOUNT_IS_REQUIRED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0117' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.YOU_HAVE_TO_SELECT_BOTH_REVALUATION_AS_OF_DATE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0118' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_ADVANCE_HAS_ALREADY_BEEN_APPLIED_AND_CANNOT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000202',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0119' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.TOP_LEVEL_INVOICES_OVERPAYMENT_IS_APPLIED_AGAIN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000201',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0120' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_FIND_THE_FINANCIAL_ENTITY_S_OFFSET_AC',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03001981',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0121' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_FIND_THE_UNDEPOSITED_FUNDS_ACCOUNT_WH',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PLEASE_CONTACT_CUSTOMER_SUPPORT_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002019',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0122' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_UPDATE_THE_OFFSET_ACCOUNT_FOR_REVERSE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03001981',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0123' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_SET_THE_FINANCIALENTITY_FOR_THE_REVER',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL02000059',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Could not set the financialentity for the reversed receipt.' => 'Could not set the financialentity for the reversed receipt.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0124' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CLOSE_TO_ACCOUNTS_CAN_NOT_BE_SELECTED_AS_THE_RE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [' "Close to" accounts can not be selected as the Retainage Receivable account.' => ' "Close to" accounts cannot be selected as the Retainage Receivable account.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0125' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.RETAINAGE_RECEIVABLE_ACCOUNT_IS_REQUIRED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Retainage Receivable account is required.' => 'Retainage Receivable account is required.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0126' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CLOSE_TO_ACCOUNTS_CAN_NOT_BE_SELECTED_AS_THE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PCRB00001',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [' "Close to" accounts can not be selected as the Retainage release clearing account.' => ' "Close to" accounts can not be selected as the Retainage release clearing account.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0127' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.NON_CLOSING_ACCOUNTS_CAN_NOT_BE_SELECTED_AS_THE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PCRB00002',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [' "Non-closing" accounts can not be selected as the Retainage release clearing account.' => ' "Non-closing" accounts can not be selected as the Retainage release clearing account.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0128' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.RETAINAGE_RELEASE_CLEARING_ACCOUNT_IS_REQUIRED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PCRB00003',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Retainage release clearing account is required.' => 'Retainage release clearing account is required.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0129' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.RETAINAGE_RELEASE_INVOICE_ID_IS_REQUIRED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Retainage release invoice ID is required.' => 'Retainage release invoice ID is required.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0130' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ADVANCED_TAX_CANNOT_BE_USED_IN_ACCOUNT_RECEIVAB',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.SINCE_YOU_HAVE_INSTALLED_THE_TAXES_APPLICATION',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.UNCHECK_ADVANCED_TAX_SCHEDULES_AND_THEN_SAVE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '',
        IAIssueDef::KEY_LEGACY_META => [7, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [' "Advanced Tax" cannot be used in Account Receivable.' => ' "Advanced Tax" cannot be used in Account Receivable.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [' Since you have installed the Taxes Application. ' => ' Since you installed the Taxes Application. '],
            IALegacyMeta::CORR_GETTEXT_MAP => [' Uncheck "Advanced Tax schedules" and then save. ' => ' Uncheck "Advanced Tax schedules" and then save. ']
        ]
    ],
    'AR-0131' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ADVANCED_TAX_OR_SIMPLE_TAX_CANNOT_BE_USED_IN_AC',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.SINCE_YOU_HAVE_INSTALLED_TAXES_APPLICATION',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.UNCHECK_ADVANCED_TAX_SCHEDULES_AND_TAX_AND_SUBT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '',
        IAIssueDef::KEY_LEGACY_META => [7, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [' "Advanced Tax" or "Simple Tax" cannot be used in Account Receivable.' => ' "Advanced Tax" or "Simple Tax" cannot be used in Account Receivable.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [' Since you have installed Taxes Application ' => ' Since you installed Taxes Application '],
            IALegacyMeta::CORR_GETTEXT_MAP => [' Uncheck "Advanced Tax schedules" and "Tax and Subtotals" and then save. ' => ' Uncheck "Advanced Tax schedules" and "Tax and Subtotals" and then save. ']
        ]
    ],
    'AR-0132' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ERROR_INTER_COMPANY_BILL_BACK',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.FOR_INTER_COMPANY_BILL_BACK_ACCOUNT_LABELS_MUST',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL0000999',
        IAIssueDef::KEY_LEGACY_META => [3, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Error Inter-Company Bill Back' => 'Error Inter-Company Bill Back'],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['For Inter-Company Bill Back  Account Labels must not be on either for AR or AP.' => 'For Inter-Company Bill Back, Account Labels cannot be on for AR or AP.'],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0133' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.YOUR_COMPANY_IS_VAT_ENABLED_SO_INTER_ENTITY_BIL',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL0000999',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Your company is VAT enabled, so inter-entity bills can only be created as drafts to add tax' => 'Your company is VAT enabled, so inter-entity bills can only be created as drafts to add tax'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0134' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_CUSTOMER_SEQUENCE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL0000999',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Invalid Customer Sequence' => 'The Customer Sequence is not valid.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0135' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.WARN_ON_DUPLICATE_INVOICE_AND_ADJUSTMENT_NUMBER',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL04002004',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Warn on duplicate Invoice and Adjustment Numbers must be set to Disallow if Avalara is enabled for Accounts Receivable' => 'Warn on duplicate Invoice and Adjustment Numbers must be set to Disallow if Avalara is enabled for Accounts Receivable'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0136' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.MANUAL_BATCHING_CANNOT_BE_ENABLED_FOR_PAYMENTS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002121',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Manual batching cannot be enabled for payments if at least one Order Entry Transactions Denfintion allows payments' => 'Manual batching cannot be enabled for payments if at least one Order Entry Transactions Denfinition allows payments.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0137' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.MANUAL_BATCHING_CANNOT_BE_ENABLED_FOR_PAYMENTS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002121',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Manual batching cannot be enabled for payments if at least one Order Entry Transactions Denfintion allows payments' => 'Manual batching cannot be enabled for payments if at least one Order Entry Transactions Denfinition allows payments.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0138' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.MANUAL_BATCHING_CANNOT_BE_ENABLED_FOR_PAYMENT_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002121',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Manual batching cannot be enabled for payments if at least one Order Entry Reccurring Template a with payment is scheduled' => 'Manual batching cannot be enabled for payments if at least one Order Entry Reccurring Template a with payment is scheduled'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0139' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_FORMAT_SELECTED_IS_NOT_A_VALID_INVOICE_FORM',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['The format selected is not a valid Invoice format ' => 'The format selected is not a valid Invoice format '],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0140' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_FORMAT_SELECTED_IS_NOT_A_VALID_STATEMENT_FO',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['The format selected is not a valid Statement format ' => 'The format selected is not a valid Statement format '],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0141' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PLEASE_SELECT_VALID_EMAIL_TEMPLATE_FOR_INVOICE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Please select valid email template for Invoice.' => 'Select a valid email template for Invoice.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0142' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PLEASE_SELECT_VALID_EMAIL_TEMPLATE_FOR_STATEMEN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Please select valid email template for Statement.' => 'Select a valid email template for Statement.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0143' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENABLE_CONTRACT_DIMENSIONS_IN_AR_CONFIG',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL0000999',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Enable the Contract dimension in Accounts Receivable configuration.' => 'Enable the Contract dimension in Accounts Receivable configuration.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0144' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ERROR_REV_REC',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.IF_REV_REC_IS_ENABLED_DEFERED_REVENUE_ACCOUNT_I',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL0000999',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0145' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ERROR_REV_REC',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.IF_REV_REC_IS_ENABLED_REVENUE_RECOGNITION_JOURN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL0000999',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0146' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ERROR_REV_REC',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.FOR_REV_RECOGNITION_YOU_CANNOT_SELECT_CASH_JOUR',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL0000999',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0147' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_ACCOUNT_LABEL',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.YOU_CANNOT_USE_AVALARA_SALES_TAX_LABEL_FOR_ENTE',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PLEASE_SELECT_ANOTHER_TAX_LABEL_WHICH_IS_NOT_US',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01002040',
        IAIssueDef::KEY_LEGACY_META => [2, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['You cannot use Avalara sales tax label for entering manual tax amount' => 'Avalara sales tax label cannot be used for entering manual tax amount'],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0148' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CHOOSE_OR_ENTER_ANOTHER_ACCOUNT_LABEL_SUBTOTAL',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01002040',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0149' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_LINE_ITEM_REQUIRES_THE_FIELD_ACCOUNT_LABEL',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.ENSURE_ALL_REQUIRED_FIELDS_ARE_COMPLETED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01002041',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0150' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_LINE_ITEM_REQUIRES_THE_FIELD_ACCOUNT_LABEL',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.ENSURE_ALL_REQUIRED_FIELDS_ARE_COMPLETED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01002041',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0151' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.INSERTED_VALUE_IS_TOO_LARGE_FOR_THE_FIELD',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.CONSULT_THE_ONLINE_MANUAL_FOR_THE_MAXIMUM_ALLOW',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000065',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0152' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.RECEIPT_DATE_IS_NOT_A_VALID_DATE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000006',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Receipt Date ${RECEIPTDATE} is not a valid date.' => 'Receipt Date ${RECEIPTDATE} is not a valid date.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0153' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.TOP_LEVEL_ADVANCE_PAYMENTS_CAN_ONLY_BE_DEPOSITE',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.ASSOCIATE_THE_ADVANCE_PAYMENT_SUMMARY_WITH_A_BA',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [5, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Top-level advance payments can only be deposited into a bank account. Select a bank account and try again.' => 'Top-level advance payments can only be deposited into a bank account. Select a bank account and try again.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Associate the advance payment summary with a bank account and try again.' => 'Associate the advance payment summary with a bank account and try again.']
        ]
    ],
    'AR-0154' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_TRANSACTION_CURRENCY_AND_BASE_CURRENCY_MUST',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.ASSOCIATE_THE_ADVANCE_PAYMENT_SUMMARY_WITH_A_BA',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [5, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['The transaction currency and base currency must be the same to deposit the advance into an undeposited funds account.' => 'The transaction currency and base currency must be the same to deposit the advance into an undeposited funds account.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Associate the advance payment summary with a bank account and try again.' => 'Associate the advance payment summary with a bank account and try again.']
        ]
    ],
    'AR-0155' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_CLOSE_AR_SUBLEDGER',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Could not close AR Subledger!' => 'Could not close AR Subledger.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0156' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_PAYMENT_METHOD_VALID_PAYMENT_METHODS_AR',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0157' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.YOU_CANNOT_SELECT_A_PAYMENT_BATCH_WITH_A_FOREIG',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0158' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CARD_HAS_CROSSED_THE_EXPIRY_DATE_ON_MM_YYYY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0159' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CANNOT_BE_USED_FOR_ADVANCES',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000098',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0160' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CANNOT_BE_USED_FOR_OVERPAYMENT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000098',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0161' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ONLINE_ACH_DEBIT_PAYMENTS_HAVE_BEEN_DISABLED_FO',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL04002015',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0162' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ONLINE_CHARGE_CARD_PAYMENTS_HAVE_BEEN_DISABLE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL04002015',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Online Charge Card payments have been disabled for %s' => 'Online credit card payments were disabled for '],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0163' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.YOU_CANNOT_MAKE_PAYMENT_FOR_FOREIGN_CURRENCY_IN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['You cannot make payment for foreign currency invoice for payment method \'%s\' using Undeposited Fund Account.' => 'The Undeposited Fund Account cannot be used to make payment for foreign currency invoice for payment method \'\' .'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0164' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_INLINE_NEGATIVE_TRANSACTION_KEY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0165' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THIS_CREDIT_CARD_DOES_NOT_BELONG_TO_CUSTOMER',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000071',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0166' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_CONFIRM_EXCHANGEGAINLOSS_ARECORD_WITH',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.UNABLE_TO_CONFIRM_PAYMENT',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.CHECK_FOR_OTHER_ERRORS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Could not confirm Exchangegainloss arecord with ID ${PAYMENT_RECORDNO}!' => 'Could not confirm Exchangegainloss arecord with ID ${PAYMENT_RECORDNO}!'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0167' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_CREATE_AR_SUMMARY_RECORD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0168' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_OPEN_AR_SUBLEDGER',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Could not open AR Subledger!' => 'Could not open AR Subledger.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0169' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_CREATE_ARPAYMENT_BATCH_RECORD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Could not create ARPayment Batch record!' => 'Could not create ARPayment Batch record.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0170' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.OOPS_WE_VE_ENCOUNTERED_A_GLITCH_REVIEW_YOUR_TRA',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0171' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CANNOT_BE_USED_IN_ACCOUNT_RECEIVABLE',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.SINCE_YOU_HAVE_ENABLED_INDIRECT_TAX_YOU_CANNOT',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.DISABLE_TO_CONTINUE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0172' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNTS_RECEIVABLE_PAYMENT_REQUEST_DID_NOT_POS',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.THE_PAYMENT_SUMMARY_PROVIDED_IS_INVALID_OR_EMPT',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PROVIDE_A_VALID_BANK_ACCOUNT_OR_THE_BATCH_WITH',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [7, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Accounts receivable payment request did not post. ' => 'Accounts Receivable payment request did not post. '],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['The payment summary provided is invalid or empty. ' => 'The payment summary provided isnâ€™t valid or is empty. '],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Provide a valid bank account or the batch with this payment. ' => 'Provide a valid bank account or the batch with this payment. ']
        ]
    ],
    'AR-0173' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNTS_RECEIVABLE_PAYMENT_REQUEST_DID_NOT_POS',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.THE_PAYMENT_SUMMARY_PROVIDED_IS_INVALID_OR_EMPT',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PROVIDE_A_VALID_UNDEPOSITED_FUNDS_ACCOUNT_NUMBE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [7, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Accounts receivable payment request did not post. ' => 'Accounts Receivable payment request did not post. '],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['The payment summary provided is invalid or empty. ' => 'The payment summary provided isnâ€™t valid or is empty. '],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Provide a valid undeposited funds account number or the batch with this payment. ' => 'Provide a valid undeposited funds account number or the batch with this payment. ']
        ]
    ],
    'AR-0174' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNTS_RECEIVABLE_PAYMENT_REQUEST_DID_NOT_POS',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.THE_PAYMENT_SUMMARY_PROVIDED_IS_INVALID_OR_EMPT',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PROVIDE_A_VALID_UNDEPOSITED_FUNDS_ACCOUNT_NU',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [7, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Accounts receivable payment request did not post. ' => 'Accounts Receivable payment request did not post. '],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['The payment summary provided is invalid or empty. ' => 'The payment summary provided isnâ€™t valid or is empty. '],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Provide a valid undeposited funds account number or valid bank account or the batch with this payment. ' => 'Provide a valid undeposited funds account number, or valid bank account, or the batch with this payment. ']
        ]
    ],
    'AR-0175' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNTS_RECEIVABLE_PAYMENT_REQUEST_DID_NOT_POS',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.THE_PAYMENT_SUMMARY_PROVIDED_IS_INVALID_OR_EMPT',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PROVIDE_A_VALID_BANK_ACCOUNT_OR_THE_BATCH_WI',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [7, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Accounts receivable payment request did not post. ' => 'Accounts Receivable payment request did not post. '],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['The payment summary provided is invalid or empty. ' => 'The payment summary provided isnâ€™t valid or is empty. '],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Provide a valid bank account or the batch with this payment. ' => 'Provide a valid bank account or the batch with this payment. ']
        ]
    ],
    'AR-0176' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => '',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.YOU_ARE_TRYING_TO_ADD_AR_ACCOUNT_THAT_REQUIRES',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0177' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNTS_RECEIVABLE_PAYMENT_REQUEST_DID_NOT_POS',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.THE_PAYMENT_SUMMARY_PROVIDED_IS_INVALID_OR_EMPT',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PROVIDE_A_VALID_UNDEPOSITED_FUNDS_ACCOUNT_NUMBE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [7, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Accounts receivable payment request did not post. ' => 'Accounts Receivable payment request did not post. '],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['The payment summary provided is invalid or empty. ' => 'The payment summary provided isnâ€™t valid or is empty. '],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Provide a valid undeposited funds account number or the batch with this payment. ' => 'Provide a valid undeposited funds account number or the batch with this payment. ']
        ]
    ],
    'AR-0178' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BANK_ACCOUNT_IS_REQUIRED',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.YOU_DO_NOT_HAVE_ANY_BANK_ACCOUNTS_WITH_A_CURREN',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.CREATE_A_BANK_ACCOUNT_IN_CASH_MANAGEMENT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0179' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.IS_NOT_A_VALID_GL_ACCOUNTNO',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000010',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0180' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_GIVEN_AR_PAYMENT_KEY_IS_INVALID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0181' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_GIVEN_AR_PAYMENT_KEY_HAS_BEEN_REVERSED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0182' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_GIVEN_AR_PAYMENT_KEY_MUST_BE_A_CREDIT_MEMO',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0183' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_GIVEN_AR_PAYMENT_KEY_IS_ALREADY_FULLY_APPLI',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0184' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_PAYMENT_METHOD_VALID_PAYMENT_METHODS_AR',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0185' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.YOU_CANNOT_MAKE_PAYMENT_FOR_FOREIGN_CURRENCY_IN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0186' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.YOU_CANNOT_SELECT_A_PAYMENT_BATCH_WITH_A_FOREIG',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0187' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.NO_RECORD_EXITS_FOR_THE_INVOICEKEY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0188' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVOICE_CURRENCY_DOES_NOT_MATCH_THE_GIVEN_CURRE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0189' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.NO_CURRENCY_GIVEN_AND_INVOICE_CURRENCY_DOES_NOT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0190' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_PAYMENT_AMOUNT_IS_BIGGER_THAN_THE_ADVANCE_T',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0191' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_PAYMENT_AMOUNT_IS_BIGGER_THAN_THE_ADVANCE_E',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0192' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_PAYMENT_AMOUNT_IS_BIGGER_THAN_THE_ADVANCE_T',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0193' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_PAYMENT_AMOUNT_IS_BIGGER_THAN_THE_ADVANCE_E',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0194' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_CONFIRM_RECORD_WITH_ID',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.UNABLE_TO_CONFIRM_PAYMENT',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.CHECK_FOR_OTHER_ERRORS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Could not confirm  record with ID !' => 'Could not confirm  record with ID !'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0195' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTITY_CONFIGURATION_LIMITATION_CHANGE_YOUR_CON',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0196' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.OOPS_WE_VE_ENCOUNTERED_A_GLITCH_REVIEW_YOUR_PAY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0197' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.OOPS_WE_VE_ENCOUNTERED_A_GLITCH_REVIEW_YOUR_PAY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0198' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.RECEIPT_DATE_MUST_BE_ON_OR_AFTER',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000088',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Receipt Date must be on or after %s' => 'Receipt Date must be on or later than '],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0199' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.RECEIPT_DATE_MUST_BE_ON_OR_AFTE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000088',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Receipt Date must be on or after %s' => 'Receipt Date must be on or later than '],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0200' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_CUSTOMER_YOU_SPECIFIED_IS_NOT_FOUND',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0201' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.MULTIVISIBILITY_IS_NOT_SUBSCRIBED_CANNOT_SET_UP',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002125',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0202' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.YOUR_COMPANY_IS_SUBSCRIBED_TO_AVALARA_TO_COMPUT',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.YOU_ARE_RESPONSIBLE_FOR_COMPUTING_TAX_ON_YOUR_I',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL02000085',
        IAIssueDef::KEY_LEGACY_META => [5, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Your company is subscribed to Avalara to compute tax on your invoices. You cannot upload AR invoices that post to GL.' => 'Your company is subscribed to Avalara to compute tax on your invoices. AR invoices that post to GL cannot be uploaded.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => ['You are responsible for computing tax on your invoices. Do not choose to post to GL when uploading AR invoices. You must upload your GL journal entries separately.' => 'You are responsible for computing tax on your invoices. Don’t choose to post to GL when uploading AR invoices. Upload your GL journal entries separately.']
        ]
    ],
    'AR-0203' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_SUM_OF_REV_REC_SCHEDULE_ENTRY_AMOUNTS_SHOUL',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0204' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_SUM_OF_REV_REC_SCHEDULE_ENTRY_SHOULD_NOT_BE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0205' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.IN_CASE_OF_NEGATIVE_LINE_ITEM_AMOUNT_THE_SUM_OF',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0206' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.TERM_NAME_DOES_NOT_CONTAIN_A_VALID_TERM_NAME',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0207' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUST_REP_ID_DOES_NOT_CONTAIN_A_VALID_EMPLOYEE_I',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0208' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.B_CONTACT_NAME_IS_NOT_UNIQUE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000019',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0209' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.S_CONTACT_NAME_IS_NOT_UNIQUE_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000019',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0210' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.DEFAULTING_CURRENCY_IS_NOT_IN_THE_TRANSACTION_C',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000019',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0211' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PRICE_LIST_IS_NOT_IN_THE_PRICE_LIST_OF_THE_COMP',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.INVALID_PRICE_LIST',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.ENTER_VALID_PRICE_LIST',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002101',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0212' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.MULTIVISIBILITY_IS_NOT_SUBSCRIBED_CANNOT_SET_UP',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002125',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0213' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUSTOMER_MUST_HAVE_A_CONTACT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000019',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0214' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.TERM_NAME_DOES_NOT_CONTAIN_A_VALID_TERM_NAME',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0215' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUSTTYPE_NAME_DOES_NOT_CONTAIN_A_VALID_CUSTOMER',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0216' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.SHIP_TO_METHOD_DOES_NOT_CONTAIN_A_VALID_SHIP_ME',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0217' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.GL_ACCTNO_IS_NOT_VALID_ACCOUNT_NO',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0218' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PARENT_ID_IS_NOT_VALID_CUSTOMER',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0219' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CONTACT_NAME_IS_NOT_UNIQUE_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000019',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0220' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_REV_REC_ACCOUNT_IS_INVALID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0221' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_REV_REC_JOURNAL_IS_INVALID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0222' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.OVERRIDING_OF_GL_POSTING_DATE_IS_NOT_ENABLED',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.ENABLE_OVERRIDING_OF_GL_POSTING_DATE_AND_TRY_AG',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01000079',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0223' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_ACCOUNT_LABEL_IS_NOT_A_SUBTOTAL_ACCOUNT_LAB',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01002041',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0224' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_EMAIL_ADDRESS',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.ENTER_A_VALID_EMAIL_ADDRESS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000019',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0225' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.SENDER_EMAIL_IS_NOT_A_VALID_EMAIL_ADDRESS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000019',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Sender email' => 'Sender email:\'\' is not a valid Email address'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0226' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNT_LABELS_MUST_BE_ON_FOR_USING_ADVANCED_TA',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PLEASE_TURN_ON_ACCOUNT_LABELS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL05002034',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0227' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNT_LABELS_MUST_BE_ON_FOR_USING_TAX_AND_SUB',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PLEASE_TURN_ON_ACCOUNT_LABELS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01002045',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0228' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ERROR_REV_REC',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.IF_REV_REC_IS_ENABLED_DEFERED_REVENUE_ACCOUNT_I',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL0000999',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0229' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ERROR_REV_REC',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.IF_REV_REC_IS_ENABLED_REVENUE_RECOGNITION_JOURN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL0000999',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0230' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ERROR_REV_REC',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.FOR_REV_RECOGNITION_YOU_CANNOT_SELECT_CASH_JOUR',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL0000999',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0231' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.SELECTED_CUSTOMER_SEQUENCE_IS_HAVING_ILLEGAL_FO',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL0000999',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Selected Customer Sequence is having illegal format for Customer ID : %s.' => 'Selected Customer Sequence has an illegal format for Customer ID : .'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0232' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THIS_PAYMENT_RECEIPT_HAS_ALREADY_BEEN_DEPOSIT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000201',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0233' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BANK_ACCOUNT_IS_REQUIRED',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.YOU_HAVE_NOT_ENTERED_A_BANK_ACCOUNT_OR_YOU_DO_N',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.CREATE_A_BANK_ACCOUNT_IN_CASH_MANAGEMENT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0234' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.IS_NOT_A_VALID_GL_ACCOUNTNO',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000010',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0235' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THIS_CREDIT_CARD_DOES_NOT_BELONG_TO_CUSTOMER',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000071',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['This Credit card does not belong to customer \'%s\'' => 'This credit card does not belong to customer \'\''],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0236' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNT_LABEL_PLACEHOLDER',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01002039',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0237' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THIS_ACH_ACCOUNT_DOES_NOT_BELONG_TO_CUSTOMER',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000186',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0238' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.LINE_NO_INVALID_ACCOUNT_LABEL',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0239' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.LINE_NO_REV_REC_TEMPLATE_AND_DEFERRED_REV_ACCOU',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0240' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.LINE_NO_INVALID_DEFERRED_REVENUE_ACCOUNT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0241' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.LINE_NO_INVALID_REV_REC_TEMPLATE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0242' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.LINE_NO_REV_REC_START_DATE_IS_REQUIRED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0243' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.LINE_NO_REV_REC_END_DATE_IS_REQUIRED_FOR_CALCUL',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0244' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.LINE_NO_INVALID_REV_REC_END_DATE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0245' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.LINE_NO_REV_REC_START_DATE_IS_GREATER_THAN_REV',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0246' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.LINE_NO_INVALID_REV_REC_START_DATE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0247' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.YOU_CANNOT_RECEIVE_A_PAYMENT_IN_A_FOREIGN_CURRE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002120',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0248' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUSTOMER_IS_INVALID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Customer %s is invalid' => 'Customer  is not valid'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0249' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UNABLE_TO_GET_RECURRING_INVOICES_RECORD_FOR_CRE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Unable to get recurring invoices record for creating invoice, resubmit the request' => 'Unable to get recurring invoices record for creating the invoice; resubmit the request.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0250' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_CREATE_INVOICE_RECORD_RESUBMIT_THE_RE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000214',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Could not create invoice record, resubmit the request' => 'Could not create invoice record, resubmit the request'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0251' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.GL_ACCOUNT_IS_LINKED_TO_FINANCIAL_ACCOUNT_AND_T',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.PLEASE_SELECT_ANOTHER_GL_ACCOUNT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL05000068',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0252' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.OOPS_WE_VE_ENCOUNTERED_A_GLITCH_REVIEW_YOUR_TRA',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0253' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_EITHER_ALL_POSITIVE_OR_ALL_NEGATIVE_AMOU',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002159',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0254' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_EITHER_ALL_POSITIVE_OR_ALL_NEGATIVE_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002159',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0255' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_A_VALID_AND_TRY_AGAIN_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002158',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0256' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_A_VALID_IN_TRANSACTION_AND_TRY_AGAIN_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002158',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0257' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNTS_RECEIVABLE_ADVANCE_PAYMENT_REQUEST_DID',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.THE_ADVANCE_PAYMENT_SUMMARY_PROVIDED_IS_INVALID',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PROVIDE_A_VALID_SUMMARY_OR_BANK_OR_UNDEPOSIT_AC',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [7, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Accounts receivable advance payment request did not post.' => 'Accounts Receivable advance payment request did not post.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['The advance payment summary provided is invalid.' => 'The advance payment summary provided is not valid.'],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Provide a valid summary or bank or undeposit account with this advance payment.' => 'Provide a valid summary or bank or undeposit account with this advance payment.']
        ]
    ],
    'AR-0258' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNTS_RECEIVABLE_ADVANCE_PAYMENT_REQUEST_DID',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.A_PAYMENT_SUMMARY_IS_REQUIRED_FOR_THE_ADVANCE',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.ADD_A_PAYMENT_SUMMARY_BATCH_TO_THE_ADVANCE_AND',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [7, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Accounts receivable advance payment request did not post.' => 'Accounts Receivable advance payment request did not post.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['A payment summary is required for the advance.' => 'A payment summary is required for the advance.'],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Add a payment summary batch to the advance and try again. or update the advance payment summary frequency from accounts receivable configuration.' => 'Add a payment summary batch to the advance and try again. or update the advance payment summary frequency from accounts receivable configuration.']
        ]
    ],
    'AR-0259' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNTS_RECEIVABLE_ADVANCE_PAYMENT_REQUEST_DID',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.A_PAYMENT_SUMMARY_IS_REQUIRED_FOR_THE_ADVANCE',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.SELECT_A_VALID_BANK_ACCOUNT_OR_UNDEPOSITED_FUND',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [7, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Accounts receivable advance payment request did not post.' => 'Accounts Receivable advance payment request did not post.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['A payment summary is required for the advance.' => 'A payment summary is required for the advance.'],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Select a valid bank account or undeposited funds account for this advance payment and try again.' => 'Select a valid bank account or undeposited funds account for this advance payment and try again.']
        ]
    ],
    'AR-0260' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNTS_RECEIVABLE_ADVANCE_PAYMENT_REQUEST_DID',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.THE_ADVANCE_PAYMENT_SUMMARY_PROVIDED_IS_INVALID',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.SELECT_EITHER_A_BANK_ACCOUNT_OR_AN_UNDEPOSITED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [7, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Accounts receivable advance payment request did not post.' => 'Accounts Receivable advance payment request did not post.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['The advance payment summary provided is invalid.' => 'The advance payment summary provided is not valid.'],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Select either a bank account or an undeposited funds account for this advance payment and try again.' => 'Select either a bank account or an undeposited funds account for this advance payment and try again.']
        ]
    ],
    'AR-0261' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNTS_RECEIVABLE_ADVANCE_PAYMENT_REQUEST_DID',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.THE_ADVANCE_PAYMENT_SUMMARY_PROVIDED_IS_INVALID',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.SELECT_A_VALID_BANK_ACCOUNT_FOR_THIS_ADVANCE_PA',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [7, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Accounts receivable advance payment request did not post.' => 'Accounts Receivable advance payment request did not post.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['The advance payment summary provided is invalid.' => 'The advance payment summary provided is not valid.'],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Select a valid bank account for this advance payment and try again.' => 'Select a valid bank account for this advance payment and try again.']
        ]
    ],
    'AR-0262' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNTS_RECEIVABLE_ADVANCE_PAYMENT_REQUEST_DID',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.THE_ADVANCE_PAYMENT_SUMMARY_PROVIDED_IS_INVALID',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.SELECT_A_VALID_UNDEPOSITED_FUNDS_ACCOUNT_FOR_TH',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [7, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Accounts receivable advance payment request did not post.' => 'Accounts Receivable advance payment request did not post.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['The advance payment summary provided is invalid.' => 'The advance payment summary provided is not valid.'],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0263' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.YOU_CANNOT_CHANGE_THE_PAYMENT_SUMMARY_BECAUSE_Y',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PROVIDE_A_VALID_BANK_OR_UNDEPOSITED_FUNDS_ACCOU',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000019',
        IAIssueDef::KEY_LEGACY_META => [5, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['You cannot change the payment summary because your company is configured for automatically batched payment summaries.' => 'You cannot change the payment summary because your company is configured for automatically batched payment summaries.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Provide a valid bank or undeposited funds account for this advance payment and try again.' => 'Provide a valid bank or undeposited funds account for this advance payment and try again.']
        ]
    ],
    'AR-0264' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNTS_RECEIVABLE_ADVANCE_PAYMENT_REQUEST_DID',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.THE_ADVANCE_PAYMENT_SUMMARY_PROVIDED_IS_INVALID',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PROVIDE_A_VALID_BANK_ACCOUNT_OR_SUMMARY_WITH_TH',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [7, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Accounts receivable advance payment request did not post.' => 'Accounts Receivable advance payment request did not post.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['The advance payment summary provided is invalid.' => 'The advance payment summary provided is not valid.'],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Provide a valid bank account or summary with this advance payment.' => 'Provide a valid bank account or summary with this advance payment.']
        ]
    ],
    'AR-0265' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNTS_RECEIVABLE_ADVANCE_PAYMENT_REQUEST_DID',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.THE_ADVANCE_PAYMENT_SUMMARY_PROVIDED_IS_INVALID',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PROVIDE_A_VALID_UNDEPOSITED_FUNDS_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [7, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Accounts receivable advance payment request did not post.' => 'Accounts Receivable advance payment request did not post.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['The advance payment summary provided is invalid.' => 'The advance payment summary provided is not valid.'],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Provide a valid undeposited funds account number or summary with this advance payment.' => 'Provide a valid undeposited funds account number or summary with this advance payment.']
        ]
    ],
    'AR-0266' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNTS_RECEIVABLE_ADVANCE_PAYMENT_REQUEST_DID',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.THE_ADVANCE_PAYMENT_SUMMARY_PROVIDED_IS_INVALID',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PROVIDE_A_VALID_SUMMARY_WITH_THIS_ADVANCE_PAYME',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [7, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Accounts receivable advance payment request did not post.' => 'Accounts Receivable advance payment request did not post.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['The advance payment summary provided is invalid.' => 'The advance payment summary provided is not valid.'],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Provide a valid summary with this advance payment.' => 'Provide a valid summary with this advance payment.']
        ]
    ],
    'AR-0267' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNTS_RECEIVABLE_ADVANCE_PAYMENT_REQUEST_DID',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.THE_ADVANCE_PAYMENT_SUMMARY_PROVIDED_IS_INVALID',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PROVIDE_A_VALID_BANK_ACCOUNT_OR_SUMMARY_WITH_TH',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [7, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Accounts receivable advance payment request did not post.' => 'Accounts Receivable advance payment request did not post.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['The advance payment summary provided is invalid.' => 'The advance payment summary provided is not valid.'],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Provide a valid bank account or summary with this advance payment.' => 'Provide a valid bank account or summary with this advance payment.']
        ]
    ],
    'AR-0268' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNTS_RECEIVABLE_ADVANCE_PAYMENT_REQUEST_DID',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.THE_ADVANCE_PAYMENT_SUMMARY_PROVIDED_IS_INVALID',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PROVIDE_A_VALID_UNDEPOSITED_FUNDS_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [7, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Accounts receivable advance payment request did not post.' => 'Accounts Receivable advance payment request did not post.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['The advance payment summary provided is invalid.' => 'The advance payment summary provided is not valid.'],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Provide a valid undeposited funds account number or summary with this advance payment.' => 'Provide a valid undeposited funds account number or summary with this advance payment.']
        ]
    ],
    'AR-0269' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.SELECT_A_PAYMENT_DATE_WITHIN_THE_CURRENT_PERIOD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000061',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0270' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.REQUESTED_WARNING_THE_PAYMENT_POSTING_DATE_OCCU',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000061',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0271' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.INVALID_OVERPAYMENT_LOCATION_ID',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PLEASE_CHOOSE_A_VALID_OVERPAYMENT_LOCATION',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0272' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.YOU_ARE_RECORDING_AN_OVERPAYMENT_TO_A_BANK_OWNE',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.TO_RECORD_THIS_OVERPAYMENT_SELECT_A_BANK_OWNED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0273' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CAN_NOT_DELETE_CONFIRMED_PAYMENT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000205',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0274' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THIS_PAYMENT_IS_ALREADY_VOID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000205',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0275' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CURRENTLY_WE_CAN_T_CREATE_THE_TRANSACTION',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.CHECK_THE_TRANSACTION_FOR_ERRORS_OR_INCONSISTEN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [4, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Check the transaction for errors or inconsistencies, then try again.' => 'Check the transaction for errors or inconsistencies, then try again.']
        ]
    ],
    'AR-0276' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CURRENTLY_WE_CAN_T_CREATE_THE_TRANSACTI',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.CHECK_THE_TRANSACTION_FOR_ERRORS_OR_INCONSISTEN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [4, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Check the transaction for errors or inconsistencies, then try again.' => 'Check the transaction for errors or inconsistencies, then try again.']
        ]
    ],
    'AR-0277' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_A_VALID_BANK_ACCOUNT_AND_TRY_AGAIN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002176',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0278' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_A_VALID_BANK_ACCOUNT_IN_TRANSACTION_AND_T',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002176',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0279' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CURRENTLY_WE_CAN_T_CREATE_THE_TRANSACTION',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.CHECK_THE_TRANSACTION_FOR_ERRORS_OR_INCONSISTEN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0280' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CURRENTLY_WE_CAN_T_CREATE_THE_TRANSACTI',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.CHECK_THE_TRANSACTION_FOR_ERRORS_OR_INCONSISTEN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0281' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CURRENTLY_WE_CAN_T_EDIT_THE_TRANSACTION',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002073',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0282' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CURRENTLY_WE_CAN_T_CREATE_THE_TRANSACTI',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002073',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0283' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_TRANSACTION',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.OOPS_WE_VE_ENCOUNTERED_A_GLITCH_REVIEW_YOUR_TRA',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002074',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0284' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_A_VALID_AND_TRY_AGAIN_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002138',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0285' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_A_VALID_AND_TRY_AGAIN_IN_TRANSACTION_AND',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002138',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0286' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_REVENUE_RECOGNITION_TEMPLATE_IS_INVALID',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.ENTER_A_VALID_REVENUE_RECOGNITION_TEMPLATE_ON_L',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002139',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0287' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_REVENUE_RECOGNITION_TEMPLATE_IS_INVALID',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.ENTER_A_VALID_REVENUE_RECOGNITION_TEMPLATE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002139',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0288' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_DEFERRED_REVENUE_RECOGNITION_ACCOUNT_IS_INV',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.ENTER_A_VALID_DEFERRED_REVENUE_RECOGNITION_ACCO',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002141',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0289' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_DEFERRED_REVENUE_RECOGNITION_ACCOUNT_IS_INV',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.ENTER_A_VALID_DEFERRED_REVENUE_RECOGNITION_ACC',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002141',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0290' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.YOU_CAN_EITHER_DEFER_REVENUE_OR_ALLOCATE_THE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002142',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0291' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_A_REVENUE_RECOGNITION_START_DATE_ON_LINE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002143',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0292' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_A_REVENUE_RECOGNITION_START_DATE_ON',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002143',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0293' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THIS_INVOICE_CAN_T_BE_DELETED_BECAUSE_ITS_REVEN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002136',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['This invoice ' => 'This invoice can\'t be deleted because its revenue is deferred.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0294' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVOICE_CAN_T_BE_DELETED_BECAUSE_ITS_REVENUE_IS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002136',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Invoice \'%s\' ' => 'Invoice \'\' can\'t be deleted because its revenue is deferred.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0295' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_A_REVENUE_RECOGNITION_END_DATE_ON_LINE_AN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002144',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0296' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_A_REVENUE_RECOGNITION_END_DATE_ON_LINE_IN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002144',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0297' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_A_REVENUE_RECOGNITION_START_DATE_THAT_IS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002145',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0298' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_A_REVENUE_RECOGNITION_START_DATE_THAT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002145',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0299' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.OOPS_WE_VE_ENCOUNTERED_A_GLITCH_THE_BASE_AMOUNT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002152',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0300' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.OOPS_WE_VE_ENCOUNTERED_A_GLITCH_THE_BASE_AMOUNT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002152',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0301' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ERROR_RETRIEVING_CUSTOMER',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000129',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0302' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ERROR_RETRIEVING_BILL_TO_CONTACT_INFORMATION_FO',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000151',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Error retrieving bill to contact information for the customer %s' => 'Error retrieving bill to contact information for the customer '],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0303' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UNABLE_TO_CREATE_BILLBACK_TEMPLATE_RECS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Unable to create BillBack Template Recs' => 'Unable to create Bill Back Template Recs'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0304' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ALREADY_EXISTS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000061',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['${VALUES_NAME} already exists.' => '${VALUES_NAME} already exists.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0305' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUSTOMER_MESSAGE_CANNOT_EXCEED_5_LINES',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Customer Message cannot exceed 5 lines.' => 'Customer Message cannot exceed 5 lines.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0306' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUSTOMER_MESSAGE_CANNOT_EXCEED_50_CHARACTERS_IN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Customer Message cannot exceed 50 characters in 1 line.' => 'Customer Message cannot exceed 50 characters in 1 line.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0307' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_CREATE_CUSTMESSAGEMANAGER_RECORD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Could not create CustMessageManager record!' => 'Could not create CustMessageManager record.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0308' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.FETCHING_FAILED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Fetching ${ENTITY} \'${OBJECTID}\' failed' => 'Fetching ${ENTITY} \'${OBJECTID}\' failed'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0309' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INTER_ENTITY_RELATIONSHIP_BETWEEN_ENTITIES_AND',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0310' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_BILL_BACK_TEMPLATE_IS_INVALID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002147',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['The bill back template \'${BILLBACK_TEMPLATE}\' is invalid.' => 'The bill back template \'${BILLBACK_TEMPLATE}\' is invalid.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0311' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.YOUR_COMPANY_IS_VAT_ENABLED_SO_INTER_ENTITY_BIL',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.UPDATE_YOUR_ACCOUNTS_RECEIVABLE_CONFIGURATION_F',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL0000999',
        IAIssueDef::KEY_LEGACY_META => [3, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Your company is VAT enabled, so inter-entity bills can only be created as drafts to add tax' => 'Your company is VAT enabled, so inter-entity bills can only be created as drafts to add tax'],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['Update your Accounts Receivable configuration for inter-entity bill back to Bill created as draft (required for VAT enabled companies).' => 'Update your Accounts Receivable configuration for inter-entity bill back to Bill created as draft (required for VAT enabled companies).'],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0312' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.OOPS_WE_VE_ENCOUNTERED_A_GLITCH_REVIEW_OR_RECRE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002146',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0313' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_BILL_BACK_TEMPLATE_IS_INVALID',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.ENTER_A_VALID_BILL_BACK_TEMPLATE_IN_TRANSACTION',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002147',
        IAIssueDef::KEY_LEGACY_META => [5, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['The bill back template \'${BILLBACK_TEMPLATE}\' is invalid.' => 'The bill back template \'${BILLBACK_TEMPLATE}\' is invalid.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Enter a valid bill-back template, in transaction \'${UNIQUE_ID}\'' => 'Enter a valid bill-back template, in transaction \'${UNIQUE_ID}\'']
        ]
    ],
    'AR-0314' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_BILL_BACK_TEMPLATE_IS_INVALID',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.ENTER_A_VALID_BILL_BACK_TEMPLATE_BUT_NOT_BOTH',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002147',
        IAIssueDef::KEY_LEGACY_META => [5, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['The bill back template \'${BILLBACK_TEMPLATE}\' is invalid.' => 'The bill back template \'${BILLBACK_TEMPLATE}\' is invalid.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Enter a valid bill-back template, but not both.' => 'Enter a valid bill back template, but not both.']
        ]
    ],
    'AR-0315' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_INVOICE_ALREADY_HAS_A_BILLBACK_RECORD_ASSOC',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.ADD_A_NEW_INVOICE_AND_TRY_AGAIN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002148',
        IAIssueDef::KEY_LEGACY_META => [5, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['The invoice already has a billback record associated with it' => 'The invoice already has a bill back record associated with it'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Add a new invoice and try again.' => 'Add a new invoice and try again.']
        ]
    ],
    'AR-0316' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.USE_THE_SAME_LOCATION_FOR_ALL_LINE_ITEMS_IN_TRA',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002149',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Use the same location for all line items in transaction \'${UNIQUE_ID}\', for bill back template \'${VALUES_BILLBACKTEMPLATE}\'.' => 'Use the same location for all line items in transaction \'${UNIQUE_ID}\', for bill back template \'${VALUES_BILLBACKTEMPLATE}\'.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0317' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.USE_THE_SAME_LOCATION_FOR_ALL_LINE_ITEMS_FOR_BI',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002149',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Use the same location for all line items, for bill back template \'${VALUES_BILLBACKTEMPLATE}\'.' => 'Use the same location for all line items, for bill back template \'${VALUES_BILLBACKTEMPLATE}\'.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0318' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.IS_THE_ONLY_YOU_CAN_SELECT_FOR_TO_CREATE_A_BILL',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002150',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['${VENDOR_TEXT} \'${LOCATION_VENDOR}\' is the only ${VENDOR_TEXT} you can select for ${CUSTOMER_TEXT} \'${VALUES_CUSTOMERID}\' to create a bill back in transaction \'${UNIQUE_ID}\'.' => '${VENDOR_TEXT} \'${LOCATION_VENDOR}\' is the only ${VENDOR_TEXT} you can select for ${CUSTOMER_TEXT} \'${VALUES_CUSTOMERID}\' to create a bill back in transaction \'${UNIQUE_ID}\'.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0319' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.IS_THE_ONLY_YOU_CAN_SELECT_FOR_TO_CREATE_A_BILL',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002150',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['${VENDOR_TEXT} \'${LOCATION_VENDOR}\' is the only ${VENDOR_TEXT} you can select for ${CUSTOMER_TEXT} \'${VALUES_CUSTOMERID}\' to create a bill back in transaction \'${UNIQUE_ID}\'.' => '${VENDOR_TEXT} \'${LOCATION_VENDOR}\' is the only ${VENDOR_TEXT} you can select for ${CUSTOMER_TEXT} \'${VALUES_CUSTOMERID}\' to create a bill back in transaction \'${UNIQUE_ID}\'.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0320' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ONE_OR_MORE_ACCOUNTS_IN_INVOICE_LINE_ITEM_IS_NO',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.CORRECT_INVOICE_AND_TRY_AGAIN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002148',
        IAIssueDef::KEY_LEGACY_META => [5, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['one or more accounts in invoice line item is not defined in billback template \n ${MISMATCHEDBBLINEIMPORT}' => 'one or more accounts in invoice line item is not defined in billback template \n ${MISMATCHEDBBLINEIMPORT}'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Correct invoice and try again.' => 'Correct the invoice and try again.']
        ]
    ],
    'AR-0321' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UNABLE_TO_RETRIEVE_MODULE_PREFERENCES_FOR',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL02000058',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0322' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_DATA_DISPLAYED_IS_NO_LONGER_UP_TO_DATE_BECA',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.PLEASE_RETRY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL01000112',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0323' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.UNABLE_TO_ASSIGN_TERM_TO_BILL_OR_INVOICE',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.REPORT_THIS_ERROR_MESSAGE_TO_INTACCT_CUSTOMERS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL02000176',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0324' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UNABLE_TO_UPDATE_PR_BATCH',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002052',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0325' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UNABLE_TO_UPDATE_PR_BATCH_KEY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002052',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0326' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_LINE_ITEM_MEMO_FIELD_CANNOT_CONTAIN_MORE_TH',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000004',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0327' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_OFFSET_ACCOUNT_FOR_OVERPAYMENT_HAS_NOT_BEEN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000137',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0328' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UNABLE_TO_CREATE_PRPAYMENT_RECORD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0329' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.YOU_CANNOT_CREATE_A_RECORD_WITH_A_DIFFERENT_BAS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0330' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UNABLE_TO_GET_THE_INVOICE_BATCH',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002052',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['unable to get the invoice batch.' => 'unable to get the invoice batch.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0331' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UNABLE_TO_SET_PR_RECORD_FOR_RECLASSIFICATION',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002052',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0332' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BILL_IN_PAYMENT_PROCESS_CANNOT_BE_MODIFIED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000166',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Bill in payment process cannot be modified.' => 'Bill in payment process cannot be modified.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0333' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THIS_BILL_REPRESENTS_A_PAY_OFF_OF_CHARGES_AGAIN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000195',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['This bill represents a pay off of charges against a credit card liability account.' => 'This bill represents a pay off of charges against a credit card liability account.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0334' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.GL_ACCOUNT_SHOULD_NOT_BE_BLANK_FOR_LINE_ITEM_SI',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01002036',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0335' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CANNOT_CREATE_A_BILL_BACK_TEMPLATE_AT_THE_ENTIT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Cannot create a bill back template at the entity level for an multi-entity company.' => 'Cannot create a bill back template at the entity level for an multi-entity company.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0336' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BILL_GL_ACCOUNT_IS_REQUIRED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Bill GL Account is required' => 'Bill GL Account is required'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0337' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_INVOICE_GL_ACCOUNT_ENTERED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000023',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Invalid Invoice GL Account entered.' => 'A valid Invoice GL Account is required.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0338' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_BILL_GL_ACCOUNT_ENTERED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000023',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Invalid Bill GL Account entered.' => 'A valid Bill GL Account is required.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0339' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_DEPARTMENT_ENTERED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000023',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0340' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_HEADER_REQUIRES_THE_FIELD_TEMPLATE_ID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000023',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0341' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_HEADER_REQUIRES_THE_FIELD_DESCRIPTION',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000023',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0342' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_LINE_ITEM_REQUIRES_THE_FIELD_INVOICE_GL_ACC',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000023',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['The line item requires the field Invoice GL Account' => 'The line item requires the field Invoice GL Account'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0343' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_LINE_ITEM_REQUIRES_THE_FIELD_BILL_GL_ACCOUN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000023',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['The line item requires the field Bill GL Account' => 'The line item requires the field Bill GL Account'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0344' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BILL_BACK_TEMPLATE_IS_MISSING_REQUIRED_FIELD',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.THE_BILL_BACK_TEMPLATE_REQUIRES_BOTH_THE_FIELDS',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.ENSURE_ALL_REQUIRED_FIELDS_ARE_PROVIDED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000023',
        IAIssueDef::KEY_LEGACY_META => [3, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Bill Back Template is missing required field.' => 'Bill Back Template is missing required field.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['The Bill Back Template requires both the fields \'Bill GL Account\' and \'Invoice GL Account\'' => 'The Bill Back Template requires both the fields Bill GL Account and Invoice GL Account'],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0345' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVOICE_GL_ACCOUNT_NO_IS_ASSOCIATED_TO_THE_BANK',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.PLEASE_SELECT_ANOTHER_GL_ACCOUNT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL05000068',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Invoice GL Account no. \'${ITEM_INVACCOUNTKEY}\' is associated to the Bank Account \'${CASHACCTID}\' and cannot be used in the line items.' => 'Invoice GL Account no. \'${ITEM_INVACCOUNTKEY}\' is associated to the Bank Account \'${CASHACCTID}\' and cannot be used in the line items.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0346' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BILL_GL_ACCOUNT_NO_IS_ASSOCIATED_TO_THE_BANK_AC',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.PLEASE_SELECT_ANOTHER_GL_ACCOUNT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL05000068',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Bill GL Account no. \'${ITEM_BILLACCOUNTKEY}\' is associated to the Bank Account \'${CASHACCTID}\' and cannot be used in the line items.' => 'Bill GL Account no. \'${ITEM_BILLACCOUNTKEY}\' is associated to the Bank Account \'${CASHACCTID}\' and cannot be used in the line items.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0347' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.USE_A_UNIQUE_TEMPLATE_ID_VALUE_INSTEAD_OF',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000061',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0348' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UNABLE_TO_CREATE_BILLBACK_TEMPLATE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Unable to create BillBack Template' => 'Unable to create Bill Back Template'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0349' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UNABLE_TO_EDIT_BILLBACK_TEMPLATE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Unable to edit BillBack Template' => 'Unable to edit Bill Back Template'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0350' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UNABLE_TO_DELETE_BILLBACK_TEMPLATE_LINE_ITEM',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Unable to delete BillBack Template Line Item' => 'Unable to delete Bill Back Template Line Item'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0351' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UNABLE_TO_DELETE_BILLBACK_TEMPLATE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Unable to delete BillBack Template' => 'Unable to delete Bill Back Template'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0352' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUSTOMER_RECORD_COULD_NOT_BE_SAVED',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.A_TRANSACTION_DEFINITION_HAS_BEEN_SELECTED_FOR',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.CHECK_THE_SELECTED_EMAIL_TEMPLATE_TYPES_AND_TRY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0353' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_TRANSACTION_DEFINITION_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0354' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_EMAIL_TEMPLATE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0355' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNT_LABEL_PLACEHOLDER',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01002047',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0356' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_MAXIMUM_DAYS_OVERDUE_SHOULD_BE_GREATER_THAN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0357' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_MAXIMUM_DAYS_OVERDUE_SHOULD_BE_GREATER_THAN_UNIQUE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0358' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_MAXIMUM_AMOUNT_DUE_SHOULD_BE_GREATER_THAN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0359' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_MAXIMUM_AMOUNT_DUE_SHOULD_BE_GREATER_THAN_UNIQUE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0360' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.MAKE_SURE_THAT_YOU_ENTER_AT_LEAST_A_MINIMUM_DAY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0361' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.MAKE_SURE_THAT_YOU_ENTER_AT_LEAST_A_MINIMUM_DAY_UNIQUE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0362' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_NOTICE_SEQUENCE_NUMBER_IS_NOT_VALID_SELECT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0363' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_NOTICE_SEQUENCE_NUMBER_IS_NOT_VALID_SELECT_U',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0364' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_THE_BASE_CURRENCY_THEN_TRY_AGAIN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL02000103',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0365' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_THE_BASE_CURRENCY_THEN_TRY_AGAIN_U',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL02000103',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0366' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_THE_TRANSACTION_CURRENCY_THEN_TRY_AGAIN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL02000103',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0367' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_THE_TRANSACTION_CURRENCY_THEN_TRY_AGAIN_U',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL02000103',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0368' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.USE_THE_3_LETTER_CURRENCY_CODE_FOR_THE_BASE_CUR',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002191',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0369' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.USE_THE_3_LETTER_CURRENCY_CODE_FOR_THE_BASE_CUR_U',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002191',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0370' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.USE_THE_3_LETTER_CURRENCY_CODE_FOR_THE_TRANSACT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002191',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0371' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.USE_THE_3_LETTER_CURRENCY_CODE_FOR_THE_TRANSACT_U',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002191',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0372' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_BASE_CURRENCY_IS_NOT_SUPPORTED_UPDATE_THE_BASE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002189',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0373' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_BASE_CURRENCY_IS_NOT_SUPPORTED_UPDATE_THE_BASE_U',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002189',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0374' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_TRANSACTION_CURRENCY_IS_NOT_SUPPORTED_UPDATE_THE_CURRENCY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002189',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0375' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_TRANSACTION_CURRENCY_IS_NOT_SUPPORTED_UPDATE_THE_CURRENCY_U',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002189',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0376' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PRINTED_DOCUMENT_TEMPLATE_DOES_NOT_EXIST_UPDATE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL02000103',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0377' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PRINTED_DOCUMENT_TEMPLATE_DOES_NOT_EXIST_UPDATE_U',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL02000103',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0378' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_ADVANCE_HAS_BEEN_SELECTED_FOR_PAYMENT_CANT_REVERSE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
    ],
    'AR-0379' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_TRANSACTION_HAS_BEEN_SELECTED_CANT_REVERSE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
    ],
    'AR-0380' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_ACTION,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ONLINE_PAYMENT_METHODS_ARE_NOT_SUPPORTED_DRAFT_PAYMENT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
    ],
    'AR-0572' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.FAILED_TO_PROCESS_THE_PAYMENT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0381' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTITY_ISN_T_ASSOCIATED_WITH_ANY',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.ADD_THE_AND_TRY_AGAIN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002148',
        IAIssueDef::KEY_LEGACY_META => [5, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Entity ${INVOICE_LOCATION} isn\'t associated with any ${VENDOR_TEXT}' => 'Entity ${INVOICE_LOCATION} isn\'t associated with any ${VENDOR_TEXT}'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Add the ${VENDOR_TEXT} and try again.' => 'Add the ${VENDOR_TEXT} and try again.']
        ]
    ],
    'AR-0382' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ANOTHER_CUSTOMERTYPE_IS_REFERING_THIS_CUSTOMERT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01000123',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0383' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUSTOMERTYPE_CANNOT_BE_DELETED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL05000067',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Customertype \'${ID}\' cannot be deleted !' => 'Customertype \'${ID}\' cannot be deleted !'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0384' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_PARENT_CUSTOMER_TYPE_DOES_NOT_EXIST',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000120',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['The parent customer type does not exist' => 'The parent customer type does not exist'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0385' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_ADD_AN_INACTIVE_CUSTOMER_TYPE_AS_THE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0386' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_CREATE_CUSTTYPE_RECORD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Could not create custtype record!' => 'Could not create custtype record.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0387' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_PARENT_CUSTOMER_TYPE_DOES_NOT_EXIST',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000120',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['The parent customer type does not exist' => 'The parent customer type does not exist'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0388' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_ADD_AN_INACTIVE_CUSTOMER_TYPE_AS_THE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0389' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THIS_CUSTOMERTYPE_HAS_SOME_CUSTOMERS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL05000067',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0390' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_CREATE_CUSTOMER_GROUP_RECORD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0391' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_UPDATE_CUSTOMER_GROUP_RECORD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0392' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_CREATE_SHIPMETHOD_RECORD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Could not create ShipMethod record!' => 'Could not create ShipMethod record.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0393' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.A_SHIPPING_METHOD_WITH_THIS_ID_ALREADY_EXISTS',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.ENTER_A_UNIQUE_VALUE_FOR_SHIPPING_METHOD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000061',
        IAIssueDef::KEY_LEGACY_META => [3, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['A shipping method with this ID already exists' => 'A shipping method with this ID already exists'],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['Enter a unique value for shipping method' => 'Enter a unique value for shipping method'],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0394' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BASE_CURRENCY_IS_NOT_SET_IN_THE_COMPANY',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.PLEASE_GO_TO_COMPANY_INFORMATION_PAGE_TO_SPECIF',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0395' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNTS_RECEIVABLE_PAYMENT_DID_NOT_POST',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.INVALID_CURRENCY_SELECTED',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.ENTER_A_VALID_TRANSACTION_CURRENCY_FOR_THIS_COM',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002121',
        IAIssueDef::KEY_LEGACY_META => [7, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Accounts receivable payment did not post.' => 'Accounts receivable payment didn’t post.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['Invalid currency selected.' => 'The selected currency is not valid.'],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Enter a valid transaction currency for this company.' => 'Enter a valid transaction currency for this company.']
        ]
    ],
    'AR-0396' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.YOU_CANNOT_SELECT_A_PAYMENT_SUMMARY_WITH_A_FORE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['You cannot select a payment summary with a foreign currency bank for payment method \'%s\'.' => 'You cannot select a payment summary with a foreign currency bank for payment method \'\'.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0397' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ONLINE_CHARGE_CARD_PAYMENTS_HAVE_BEEN_DISABLED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL04002015',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Online Charge Card payments have been disabled for %s.' => 'Online Credit Card payments have been disabled for .'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0398' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ONLINE_ACH_DEBIT_PAYMENTS_HAVE_BEEN_DISABLED_FO',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL04002015',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0399' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.NO_PAYMENTS_WERE_APPLIED_TO_ANY_OUTSTANDING_INV',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000098',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['No payments were applied to any outstanding invoice for this Customer : %s. If this is an advance, please use Receive Advances screen to record it.' => 'No payments were applied to any outstanding invoice for this Customer : . If this is an advance, use Receive Advances screen to record it.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0400' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_A_CHECK_OR_TRANSFER_AMOUNT_THEN_TRY_AGAIN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL05000072',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Enter a check or transfer amount, then try again.' => 'Enter a check or transfer amount, then try again.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0401' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_THE_AS_OF_DATE_THEN_TRY_AGAIN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL02000103',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0402' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_THE_BASED_ON_VALUE_THEN_TRY_AGAIN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL02000103',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0403' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_BASED_ON_VALUE_IS_NOT_VALID_ENTER_A_VALID_B',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0404' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_SENDER_EMAIL_ADDRESS_IS_MISSING_ENTER_THE_S',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL02000103',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0405' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PRINTED_DOCUMENT_TEMPLATE_IS_A_REQUIRED_FIELD',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.SELECT_A_PRINTED_DOCUMENT_TEMPLATE_THEN_TRY_AGA',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL02000103',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0406' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PRINTED_DOCUMENT_TEMPLATE_DOES_NOT_EXIST_UPDATE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0407' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUSTOMER_DETAILS_ARE_MISSING_ENTER_A_CUSTOMER_T',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL02000103',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0408' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PROVIDED_CUSTOMER_NOT_FOUND_ENTER_A_DIFFERENT_C',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0409' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.VALUE_FOR_CUSTOMER_TYPE_IS_NOT_VALID_ENTER_VALI',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0410' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_CREATE_RECORD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Could not create  record!' => 'Could not create  record!'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0411' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_EMAIL_ID_PASSED_TO_ON_LINE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0412' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.EMAIL_TEMPLATE_DOES_NOT_EXISTS_UPDATE_THE_PRINT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0413' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUSTOMER_IS_MISSING_FOR_LINE_ENTER_A_CUSTOMER_T',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL02000103',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0414' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUSTOMER_ID_ON_LINE_DOES_NOT_EXIST_ENTER_A_DIFF',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0415' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.RECIPIENT_EMAIL_ADDRESS_IS_MISSING_FOR_ENTER_AN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0416' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.NO_CUSTOMERS_SELECTED_FOR_PRINT_OR_EMAIL_SELECT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0417' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_EMAIL_ID_PASSED_TO',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0418' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.RECORD_NOT_FOUND_COULD_NOT_DELETE_RECORD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001974',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Record  not found. Could not delete record.' => 'Record  not found. Could not delete record.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0419' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.YOU_CANNOT_DELETE_THIS_DUNNING_LEVEL_BECAUSE_IT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001974',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['You cannot delete this dunning level because it\'s being used for dunning notices.' => 'You cannot delete this dunning level because it\'s being used for dunning notices.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0420' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.DUNNING_LEVEL_IS_A_REQUIRED_FIELD_SELECT_A_DUNN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL02000103',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0421' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.DUNNING_LEVEL_DOES_NOT_EXIST_ENTER_A_DIFFERENT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0422' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUSTOMER_DOES_NOT_EXIST_ENTER_A_DIFFERENT_CUSTO',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0423' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.NO_TRANSACTIONS_FOUND_FOR_DUNNING_NOTICE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0424' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUSTOMER_DETAILS_DOES_NOT_EXISTS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0425' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVOICE_DETAILS_ARE_MISSING_ENTER_INVOICE_INFOR',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0426' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UNABLE_TO_PUBLISH_THE_MESSAGE',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.CHECK_YOUR_FUNCTION_ARGUMENTS_TO_MAKE_SURE_CORR',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0427' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.EMAIL_LOGO_IS_NOT_SET_IN_COMPANY_SETUP',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000185',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0428' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ERROR_RETRIEVING_EMAIL_LOGO_FROM_COMPANY_SETUP',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000185',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0429' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ERROR_RETRIEVING_MIME_TYPE_OF_EMAIL_LOGO',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000185',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0430' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UNABLE_TO_INSTALL_CUSTOMER_GROUP',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000061',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0431' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.FAILED_WHILE_COPYING_CUSTOMER_DIMENSION_GROUPS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000108',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0432' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.A_CUSTOMER_GROUP_WITH_THE_NAME_ALREADY_EXISTS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL04000082',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0433' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_FORMAT_SELECTED_IS_NOT_A_VALID_INVOICE_FORM',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['The format selected is not a valid Invoice format ' => 'The format selected is not a valid Invoice format '],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0434' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ANOTHER_IS_REFERING_THIS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000182',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0435' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_DELETE_RECORD_WITH_ID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Could not delete ${ENTITY} record with ID ${ID}!' => 'Could not delete ${ENTITY} record with ID ${ID}!'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0436' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_UPDATE_RECORD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Could not update ${CTITLE} record!' => 'Could not update ${CTITLE} record!'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0437' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PARENT_CANNOT_REFER_TO_SELF',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Parent cannot refer to self.' => 'Parent cannot refer to self.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0438' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_PARENT_NAME_DOES_NOT_EXIST_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0439' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_ADD_AN_INACTIVE_AS_THE_PARENT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0440' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_CONTACT_DOES_NOT_EXIST',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.CHOOSE_A_VALID_CONTACT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000019',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0441' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.DOCUMENT_DOES_NOT_EXIST',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL01000112',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0442' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BATCH_IS_CLOSED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000038',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0443' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BATCH_IS_INACTIVE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000175',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0444' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BATCH_DOES_NOT_EXIST',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000057',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0445' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BATCH_IS_ONLY_FOR_HISTORICAL_TRANSACTIONS_IMPOR',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000094',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0446' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BILL_IN_PAYMENT_PROCESS_CANNOT_BE_DELETED_PLEAS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000166',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Bill in payment process cannot be deleted. Please UNSELECT.' => 'Bill in payment process cannot be deleted. Deselect it.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0447' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PLEASE_OPEN_THIS_BATCH_BEFORE_DELETING_THE_RECO',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000036',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0448' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.YOU_MUST_FIRST_OPEN_THE_BATCH',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000037',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0449' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_DATE_IS_INVALID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0450' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_DUE_DATE_IS_INVALID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0451' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_RECOMMENDED_PAYMENT_DATE_IS_INVALID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0452' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_DUE_DATE_CANNOT_BE_LESS_THAN_DATE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000078',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0453' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_SELECTED_BATCH_IS_CLOSED_PLEASE_OPEN_IT_BEF',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000037',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0454' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTRY_HAS_AN_INVALID_DEPARTMENT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000008',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0455' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.NAME_IS_REQUIRED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000004',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0456' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTRY_HAS_AN_INVALID_LOCATION',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000010',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0457' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTRY_HAS_AN_INVALID_AMOUNT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000001',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0458' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PLEASE_EITHER_UNSELECT_THE_BILL_IN_THE_SELECT_T',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL05000121',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Please either unselect the bill in the "select to pay" module (or) void or confirm it in the quick check screen.' => 'Either deselect the bill in the Pay Bills page or void or confirm it in the quick check screen.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0459' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PLEASE_EITHER_UNSELECT_THE_BILL_IN_THE_SELECT_T',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL05000121',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Please either unselect the bill in the "select to pay" module (or) void or confirm it in the quick check screen.' => 'Either deselect the bill in the Pay Bills page or void or confirm it in the quick check screen.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0460' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.RECORD_NO_NOT_FOUND_ENTER_A_DIFFERENT_RECORD_NO',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01000003',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0461' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.RESEND_EMAIL_NOT_APPLICABLE_FOR_RECORD_NO',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01000004',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0462' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_CUSTOMERID_SHOULD_NOT_CONTAIN_ANY_SPECIAL_C',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000062',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0463' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PROVIDE_A_VALID_ATTACHMENT_ID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0464' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_CREATE_RECORD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Could not create  record!' => 'Could not create  record!'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0465' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.YOU_CANNOT_UPDATE_A_ROOT_LEVEL_CUSTOMER_FROM_AN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['You cannot update a root level customer from an entity/restricted user with one entity' => 'A root level customer cannot be updated from an entity/restricted user with one entity'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0466' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.YOU_HAVE_SPECIFIED_AN_INVALID_RETAINAGE_PERCENT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['You have specified an invalid retainage percentage. Please specify a value between 0 and 100.' => 'The retainage percentage is not valid. Specify a value between 0 and 100.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0467' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PROVIDE_A_VALID_ATTACHMENT_ID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Please provide a valid attachment ID' => 'Provide a valid attachment ID'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0468' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_CURRENCY_FIELD_SHOULD_BE_EXACTLY_3_CHARACTE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000004',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0469' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_FAX_NUMBER_CANNOT_BE_EMPTY_FOR_THE_BILLING',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['The fax number cannot be empty for the billing contact if fax is a delivery option.' => 'The fax number cannot be empty for the billing contact if fax is a delivery option.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0470' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_EMAIL_ADDRESS_CANNOT_BE_EMPTY_FOR_THE_BILLI',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['The email address cannot be empty for the ${CTITLE} / billing contact if email is a delivery option.' => 'The email address cannot be empty for the ${CTITLE} / billing contact if email is a delivery option.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0471' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BILLING_CONTACT_ADDRESS_FIELDS_CAN_T_BE_EMPTY_I',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000156',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0472' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PLEASE_ENROLL_FOR_INTACCT_S_ONLINE_DOCUMENT_DEL',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000145',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0473' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_CUSTOMER_PRICE_ENTERED',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.THE_CUSTOMER_PRICE_DOES_NOT_EXIST',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.CHOOSE_A_VALID_CUSTOMER_PRICE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [7, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Invalid Customer Price entered' => 'Invalid Customer Price entered'],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['The Customer Price \'${PRICELIST}\' does not exist.' => 'The Customer Price \'${PRICELIST}\' does not exist.'],
            IALegacyMeta::CORR_GETTEXT_MAP => ['Choose a valid Customer Price.' => 'Choose a valid Customer Price.']
        ]
    ],
    'AR-0474' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CANNOT_SET_THE_ACCEPTS_EMAILED_INVOICES_OPTION',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.THE_FIELD_CAN_BE_SET_TO_TRUE_ONLY_IF_YOUR_COMPA',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [2, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['The field can be set to true only if your company is subscribed to Taxes and the customer country is South Africa.' => 'The field can be set to true only if your company is subscribed to Taxes and the customer country is South Africa.'],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0475' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUSTOMER_DOES_NOT_EXIST',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Customer ${CUSTOMERID} does not exist.' => 'Customer ${CUSTOMERID} does not exist.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0476' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ERROR_WHILE_SAVING_THE_DISPLAY_CONTACT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Error While Saving the Display Contact' => 'Error While Saving the Display Contact'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0477' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_ENTERED',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.THE_DOES_NOT_EXIST',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.CHOOSE_A_VALID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000019',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0478' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UNABLE_TO_SET_THE_DOCUMENT_NUMBER_USING_THESEQ',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.PLEASE_CHECK_YOUR_MODULE_AND_SEQUENCE_CONFIGUR',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000023',
        IAIssueDef::KEY_LEGACY_META => [2, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => ['Please check your module and sequence configuration.' => 'Check your module and sequence configuration.'],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0479' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.NOT_ENOUGH_ASSOCIATED_PLEASE_ASSOCIATE_FOR_WHIC',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0480' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUSTOMER_IS_LINKED_TO_ENTITY_FOR_BILL_BACK_FUNC',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Customer \'${CUSTOMERID}\' is linked to entity \'${ENTITY}\' for Bill Back functionality. You can not remove visibility for this Customer until you first remove the link.' => 'Customer \'${CUSTOMERID}\' is linked to entity \'${ENTITY}\' for Bill Back functionality. You can not remove visibility for this Customer until you first remove the link.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0481' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.NOT_ENOUGH_ASSOCIATED_PLEASE_ASSOCIATE_FOR_WHIC',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0482' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THIS_TRANSACTION_CURRENCY_DOES_NOT_EXIST_FOR_TH',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.PLEASE_SELECT_THE_CURRENCY_FROM_THE_EXISTING_TR',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000004',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0483' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UNABLE_TO_RETRIEVE_THE_MODULE_OPEN_DATE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL02000034',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0484' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UNABLE_TO_DELETE_RECORD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Unable to delete record' => 'Unable to delete record'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0485' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.AN_ATTEMPT_WAS_MADE_TO_DUPLICATE_THIS_INVOICE_B',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['An attempt was made to duplicate this Invoice/Bill %s' => 'An attempt was made to duplicate this Invoice/Bill '],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0486' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UNABLE_TO_CREATE_RECORD_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Unable to create record' => 'Unable to create record'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0487' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UNABLE_TO_SAVE_RECORD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Unable to save record' => 'Unable to save record'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0488' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_RECORD_REQUIRES_THE_FIELD_BASE_CURRENCY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000023',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0489' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_RECORD_REQUIRES_THE_FIELD_CURRENCY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000023',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0490' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_CURRENCY_FIELD_SHOULD_BE_EXACTLY_3_CHARACTE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000004',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0491' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.AMOUNT_CANNOT_BE_EMPTY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000004',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0492' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BASE_CURRENCY_HAS_TO_BE_THE_SAME_AS_LOCATION',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000004',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0493' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THIS_TRANSACTION_CURRENCY_DOES_NOT_EXIST_FOR_TH',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.PLEASE_SELECT_THE_CURRENCY_FROM_THE_EXISTING_TR',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000004',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0494' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_RECORD_REQUIRES_THE_FIELD_EXCHANGE_RATE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000023',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0495' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.SELECTED_VENDOR_IS_AN_OWNER_OWNERS_CANNOT_BE_SE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000004',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Selected vendor is an owner. Owners cannot be selected for foriegn currency transactions.' => 'Selected vendor is an owner. Owners cannot be selected for foriegn currency transactions.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0496' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_DESCRIPTION_CANNOT_BE_MORE_THAN_1000_CHARAC',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000004',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0497' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_TERM_NAME_FIELD_IS_NOT_VALID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000004',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0498' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_BILL_AMOUNT_IS_LESS_THAN_THE_DISCOUNT_AMOUN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'PL03000001',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['The Bill Amount \'%1\' is less than the Discount Amount \'%2\'' => 'The Bill Amount \'\' is less than the Discount Amount \'\''],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0499' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CAN_ONLY_RECLASSIFY_AP_BILL_AP_ADJUSTMENT_AR_IN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002052',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Can only reclassify AP Bill, AP Adjustment, AR Invoice or AR Adjustment' => 'Can only reclassify AP Bill, AP Adjustment, AR Invoice or AR Adjustment'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0500' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UNABLE_TO_GET_CUSTOMER_DEFAULTS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Unable to get Customer Defaults' => 'Unable to get Customer Defaults'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0501' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.FETCHING_FAILED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Fetching  \'\' failed' => 'Fetching  \'\' failed'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0502' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CREATING_FAILED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Creating  failed' => 'Creating  failed'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0503' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UPDATING_FAILED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['Updating  \'\' failed' => 'Updating  \'\' failed'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0504' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_AT_LEAST_ONE_LINE_ITEM_FOR_TRANSACTION_AN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002184',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0505' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_AT_LEAST_ONE_LINE_ITEM_FOR_THE_TRANSACTIO',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002184',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0506' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.RECLASSIFICATION_ERROR_YOU_CAN_T_RECLASSIFY_PAY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002052',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0507' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.RECLASSIFICATION_ERROR_YOU_CAN_T_RECLASSIFY_P',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002052',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0508' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_REQUEST_PLEASE_RETRY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002184',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0509' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUSTOMER_RECORD_COULD_NOT_BE_SAVED',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.VERIFY_THE_EMAIL_TEMPLATE_SETTINGS_AND_TRY_SAVI',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0510' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUSTOMER_RECORD_COULD_NOT_BE_SAVED',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.A_PURCHASING_TRANSACTION_EMAIL_TEMPLATE_NAME_HA',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.VERIFY_THE_SELECTED_EMAIL_TEMPLATE_TYPES_AND',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0511' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUSTOMER_RECORD_COULD_NOT_BE_SAVED',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.THE_CUSTOMER_CAN_ONLY_HAVE_ONE_EMAIL_TEMPLATE_F',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.VERIFY_THE_SELECTED_EMAIL_TEMPLATE_TYPES_AND',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0512' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUSTOMER_RECORD_COULD_NOT_BE_SAVED',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.AN_INVALID_EMAIL_TEMPLATE_NAME_HAS_BEEN_DETECTE',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.VERIFY_THE_EMAIL_TEMPLATE_SETTINGS_AND_TRY_SAVI',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0513' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.VENDOR_RECORD_COULD_NOT_BE_SAVED',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.WE_VE_ENCOUNTERED_A_GLITCH_PLEASE_PROVIDE_THE_E',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.VERIFY_THE_SELECTED_EMAIL_TEMPLATE_TYPES_AND_TR',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0514' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUSTOMER_RECORD_COULD_NOT_BE_SAVED',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.DUPLICATE_EMAIL_TEMPLATE_AND_TRANSACTION_DEFINI',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.VERIFY_THE_EMAIL_TEMPLATE_SETTINGS_AND_TRY_SAVI',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0515' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_FOLLOWING_DOCUMENTS_WERE_NOT_SENT_DUE_TO_ER',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PLEASE_TRY_AGAIN_OR_CONTACT_CUSTOMER_SERVICE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000185',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0516' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_INVOICE_HAS_AN_ASSOCIATED_QUICKCHECK_THAT_H',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000158',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['The invoice has an associated QuickCheck %s that has been printed but neither confirmed nor voided.' => 'The invoice has an associated QuickCheck  that was printed but neither confirmed nor voided.'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0517' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.A_PAYMENT_HAS_BEEN_MADE_AGAINST_THIS_BILL_ADJUS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000166',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['A payment has been made against this Bill / Adjustment' => 'A payment was made against this bill/adjustment'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0518' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.A_PAYMENT_HAS_BEEN_MADE_AGAINST_THIS_INVOICE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000166',
        IAIssueDef::KEY_LEGACY_META => [1, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['A payment has been made against this Invoice / Adjustment' => 'A payment has been made against this invoice/adjustment'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0519' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_RECORD_SUBMITTED_PLEASE_USE_THE_VALID_P',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000205',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0520' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CAN_NOT_SUBMIT_NON_DRAFT_PAYMENT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000205',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0521' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_A_DEFERRED_REVENUE_RECOGNITION_ACCOUNT_ON',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002140',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0522' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_A_DEFERRED_REVENUE_RECOGNITION_ACCOUNT_O',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002140',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0523' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.EMAIL_TEMPLATE_IS_NOT_FOUND',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0524' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ACCOUNT_LABEL_PLACEHOLDER',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01002044',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0525' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.RETRIEVE_DOCUMENT_FAILED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01000004',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0526' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.VALUE_FOR_CUSTOMER_GROUP_IS_NOT_VALID_ENTER_VAL',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0527' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_CUSTOMER_FILTERS_YOU_CAN_PASS_ONLY_ONE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002063',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0528' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.AR_AUTO_MATCH_SEQUENCE_CANNOT_BE_EMPTY',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.ENTER_VALID_AR_AUTO_MATCH_SEQUENCE_ID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL0000999',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0529' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_AR_AUTO_MATCH_SEQUENCE_SEQUENCE_ID',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.ENTER_VALID_AR_AUTO_MATCH_SEQUENCE_SEQUENCE_ID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL0000999',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0530' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_AR_AUTO_MATCH_SEQUENCE_SEQUENCE_ID',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.ENTER_ALPHA_AR_AUTO_MATCH_SEQUENCE_SEQUENCE_ID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL0000999',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0531' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.DOCUMENT_SEQUENCE_IS_REQUIRED_IN_CASE_OF_MULTIPLE_CUSTOMER_PAYMENT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0532' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::UNABLE_TO_UPDATE_RECORD,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ANOTHER_PROCESS_TO_ASSIGN_PAYMENT_ID_TO_EXISTING_PAYMENTS_IS_RUNNING',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0533' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ALPHA_DOCUMENT_SEQUENCE_TYPE_IS_NOT_SUPPORTED_FOR_PAYMENTS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0534' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::UNABLE_TO_UPDATE_RECORD,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UPDATING_THE_PAYMENT_ID_FOR_EXISTING_TRANSACTION_WAS_UNSUCCESSFUL_TRY_AGAIN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0535' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.SELECT_A_VALID_ENDING_NUMBER_OF_DOCUMENT_SEQUENCE_FOR_PAYMENTS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0536' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PAYMENT_IS_PART_OF_MULTIPLE_CUSTOMER_PAYMENT_CAN_ONLY_BE_REVERSED_FROM_PAYMENT_LIST',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0537' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.MULTIPLE_CUSTOMER_PAYMENT_CAN_BE_REVERSED_FROM_PAYMENT_LIST',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0538' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ONLINE_PAYMENT_METHODS_ARE_NOT_SUPPORTED_FOR_MULTI_CUSTOMER_PAYMENT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0539' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_A_PAYER_NAME_FOR_THIS_PAYMENT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0540' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENTER_A_VALID_CUSTOMER_ID_FOR_THE_OVERPAYMENT_AMOUNT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0541' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PART_OF_MULTIPLE_PAYMENT_VIEW_ONLY_ENTIRE_PAYMENT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0542' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::VALIDATION_ERROR,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_MAIL_ADDRESS_VALUE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0543' => [
        IAIssueDef::KEY_CATEGORY           => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID    => 'IA.REVERSE_PAYMENTS_NOT_HAVE_CREDITS',
        IAIssueDef::KEY_HTTP_STATUS        => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000202',
        IAIssueDef::KEY_LEGACY_META        => [ 0, 0 ],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => [],
        ],
    ],
    'AR-0544' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::OPERATION_FAILED,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THIS_OPERATION_IS_NOT_ALLOWED_ON_AR_SETUP',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PLEASE_USE_NEWLY_PUBLISHED_API_OR_NEXTGEN_API_OF_ARSETUP',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01001973',
        IAIssueDef::KEY_LEGACY_META => [0,0],
    ],
    'AR-0545' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INCORRECT_FORMAT_FOR_PRINTAS_INVOICE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0546' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PAYMENT_RECORD_NOT_FOUND',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.PAYMENT_RECORD_YOU_ARE_TRYING_TO_DELETE_IS_NOT_FOUND',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0547' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PROBLEM_UPDATING_PAYMENT_RECORD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0548' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.NO_TRANSACTIONS_FOUND_FOR_THE_PAYMENT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0549' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CANNOT_CHANGE_MULTI_TO_SINGLE_CUSTOMER_PAYMENT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0550' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CANNOT_CHANGE_SINGLE_TO_MULTI_CUSTOMER_PAYMENT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0551' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CHILD_PAYMENTS_NOT_FOUND_FOR_MULTI_CUSTOMER_PAYMENT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0552' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.NO_TRANSACTIONS_FOUND_FOR_THE_PAYMENT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0553' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CANNOT_DELETE_THE_CHILD_PAYMENT',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.PART_OF_MULTI_CUSTOMER_PAYMENT_CANNOT_BE_DELETED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0554' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ASK_YOUR_ADMINISTRATOR_FOR_POST_PERMISSION_OR_C',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
    ],
    'AR-0555' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.NO_TRANSACTIONS_FOUND_FOR_THE_PAYMENT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
    ],
    'AR-0556' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.OOPS_WE_VE_ENCOUNTERED_A_GLITCH_REVIEW_YOUR_PAY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
    ],
    'AR-0557' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PAYMENT_RECORD_NOT_FOUND',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.PAYMENT_RECORD_YOU_ARE_TRYING_TO_DELETE_IS_NOT_FOUND',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0558' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PLEASE_PROVIDE_VALID_VALUE_FOR_JOURNAL',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0559' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PLEASE_PROVIDE_VALID_VALUE_FOR_ACCOUNT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0560' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PLEASE_PROVIDE_VALID_VALUE_FOR_SEQUENCE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0561' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PLEASE_PROVIDE_VALID_BANK_ACCOUNT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0562' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PLEASE_PROVIDE_VALID_UNDEPOSIT_ACCOUNT_OR_CHANGE_THE_OFFLINE_PAYMENT_ACCOUNT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0563' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PLEASE_PROVIDE_VALID_BANK_ACCOUNT_OR_CHANGE_THE_OFFLINE_PAYMENT_ACCOUNT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0564' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::VALIDATION_ERROR,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.DISCOUNTTOAPPLY_NO_LONGER_ENABLED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
    ],
    'AR-0566' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_CUSTMESSAGE_ENTERED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '',
        IAIssueDef::KEY_LEGACY_META => [0,0]

    ],
    'AR-0567' => [
       IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUSTOMER_REFUND_IS_NOT_ENABLED_FOR_YOUR_COMPANY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '',
        IAIssueDef::KEY_LEGACY_META => [0,0]
    ],
    'AR-0568' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID =>  'IA.ERROR_IN_EMAIL',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '',
        IAIssueDef::KEY_LEGACY_META => [0,0]
    ],
    'AR-0569' => [
         IAIssueDef::KEY_CATEGORY           => IAIssueCategory::NOT_FOUND,
        IAIssueDef::KEY_DESCRIPTION1_ID    => 'IA.NO_EMAIL_TEMPLATE_WITH_ID_TEMPLATEID_CAN_BE_FOU',
        IAIssueDef::KEY_HTTP_STATUS        => HttpStatus::NOT_FOUND,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '',
        IAIssueDef::KEY_LEGACY_META        => [ 0, 0 ]
    ],
    'AR-0570' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_SET_CUSTOMER_VISIBILITY_RECORD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '',
        IAIssueDef::KEY_LEGACY_META => [0,0]

    ],
    'AR-0571' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.NO_RESTRICTIONS_PROVIDED_FOR_RESTRICTED_OBJECT',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.PLEASE_PROVIDE_ATLEAST_ONE_RESTRICTION',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03002125',
        IAIssueDef::KEY_LEGACY_META => [0,0]
    ],
    'AR-0573' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PLEASE_SELECT_VALID_RESTRICTED_LOCATIONS_GROUPS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '',
        IAIssueDef::KEY_LEGACY_META => [0,0]
    ],
    'AR-0574' => [
       IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PLEASE_SELECT_VALID_RESTRICTED_DEPARTMENTS_GROUPS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '',
        IAIssueDef::KEY_LEGACY_META => [0,0]
    ],
    'AR-0575' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_FORMAT_SELECTED_IS_NOT_A_VALID_STATEMENT_FO',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [0, 0]
    ],
    'AR-0576' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_START_DATE_IS_INVALID_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [0, 0]
    ],
    'AR-0577' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_END_DATE_IS_INVALID_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [0, 0]
    ],
    'AR-0578' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.NO_STATEMENTS_WERE_SELECTED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000113',
        IAIssueDef::KEY_LEGACY_META => [0, 0]
    ],
    'AR-0579' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.THE_FOLLOWING_STATEMENTS_WERE_NOT_EMAILED_DUE_T',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.PLEASE_TRY_AGAIN_OR_CONTACT_CUSTOMER_SERVICE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL03000185',
        IAIssueDef::KEY_LEGACY_META => [0, 0]
    ],
    'AR-0580' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.REQUEST_HAS_BEEN_ALTERED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '',
        IAIssueDef::KEY_LEGACY_META => [0,0]
    ],
    'AR-0581' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.USER_DOES_NOT_HAVE_PERMISSION_TO_POST_ADJUSTMEN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01000079',
        IAIssueDef::KEY_LEGACY_META => [1,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['User does not have permission to post adjustments into the system' => 'User doesn’t have permission to post adjustments into the system'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0582' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.USER_DOES_NOT_HAVE_PERMISSION_TO_POST_INVOICE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL01000079',
        IAIssueDef::KEY_LEGACY_META => [1,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => ['User does not have permission to post invoices into the system' => 'User doesn’t have permission to post invoices into the system'],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0583' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.TRANSACTION_HAS_ALREADY_BEEN_REFUNDED_VOID_THE_PRIOR_REFUND',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
    ],
    'AR-0584' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::NO_DATA_FOUND,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.NO_DATA_FOUND_TO_UPDATE_DATE_ON_REVERSAL',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.REVERSAL_BATCH_KEYS_MUST_BE_SAME_FOR_LINKED_PAYMENTS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::NOT_FOUND
    ],
    'AR-0585' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.MUST_SELECT_AT_LEAST_ONE_CREDIT_TRANSACTION_FOR_A_REFUND',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0586' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.ENCOUNTERED_A_GLITCH_WHILE_VOIDING_REVIEW_YOUR_REQUEST',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0587' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.NO_REFUND_DETAILS_FOUND_IN_THE_REQUEST',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0588' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PASSED_PROPERTY_IS_NOT_VALID_RESEND_THE_REQUEST',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0589' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.REFUND_AMOUNT_MUST_BE_GREATER_THAN_0',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0590' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.MULTIPLE_OR_CONFLICTING_CREDIT_KEYS_FOUND_IN_DETAIL',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0591' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CREDIT_DETAILS_ARE_REQUIRED_TO_REFUND',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0592' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.NO_TRANSACTION_KEYS_FOUND_IN_THE_REQUEST',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0593' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BANK_ACCOUNT_FINANCIAL_ENTITY_IS_NOT_VALID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0594' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.TRANSACTION_KEYS_PROVIDED_ARE_EITHER_NOT_VALID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0595' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.TRANSACTION_DOES_NOT_CONTAIN_VALID_LINE_ITEMS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0596' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.REFUND_DETAIL_CREATION_FAILED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0597' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_STATE_FOR_REFUND_DETAILS_CREATION',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0598' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.MISSING_REQUIRED_KEY_IN_REFUND_DETAILS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0599' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.MISSING_CREDIT_KEY_OR_ENTRY_KEY_IN_REFUND_DETAILS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0600' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.DUPLICATE_REFUND_DETAILS_RECORD_CREATED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0601' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_REFUND_FOR_REFUNDKEY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0602' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_TRANSACTION_FOR_TXNKEY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0603' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_TRANSACTION_ENTRY_FOR_ENTRYKEY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0604' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_OPERATION_USE_AN_OPERATION_VALID_FOR_CUSTOMER_REFUNDS',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0605' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_RECORD_PROVIDE_A_VALID_REFUND_RECORD_FOR_OPTEXT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0606' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CANNOT_PERFORM_THE_FOLLOWING_ON_A_DRAFT_REFUND_OPTEXT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0607' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CANNOT_PERFORM_THE_FOLLOWING_ON_A_CLEARED_REFUND_OPTEXT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0608' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PROVIDE_A_VALID_STATE_SUCH_AS_DRAFT_OR_SUBMIT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0609' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PROVIDE_A_VALID_REFUND_SEQUENCE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0610' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UNABLE_TO_GENERATE_SEQUENCE_VALIDATE_THE_REFUND_SEQUENCE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0611' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.REFUND_DATE_IS_NOT_VALID_REFUNDDATE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0612' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PROVIDE_A_VALID_PAYMENT_METHOD_AND_TRY_AGAIN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0613' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PROVIDE_A_VALID_CUSTOMER_AND_TRY_AGAIN',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0614' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PROVIDE_A_VALID_PAY_TO_CONTACT_KEY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0615' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.REFUND_DATE_CANNOT_OCCUR_BEFORE_THE_OPEN_PERIOD_DATE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0616' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BASE_CURRENCY_FIELD_IS_REQUIRED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0617' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BASE_CURRENCY_FOR_REFUND_DOES_NOT_MATCH_THE_COMPANY_S_BASE_CURRENCY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0618' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BASE_CURRENCY_FOR_REFUND_DOES_NOT_MATCH_THE_CREDITS_BASE_CURRENCY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0619' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BANK_ACCOUNT_IS_REQUIRED_TO_PROCESS_THE_REFUND',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0620' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BANK_CURRENCY_IS_REQUIRED_TO_PROCESS_THE_REFUND',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0621' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BANK_CURRENCY_MUST_MATCH_EITHER_TRANSACTION_OR_BASE_CURRENCY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0622' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.SELECT_BANK_WITH_SAME_BASE_CURRENCY_AS_FOR_INTER_ENTITY_TRANSACTION',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0623' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_CURRENCY_COMBINATION',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0624' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.TOTAL_AMOUNT_DOES_NOT_MATCH_AMOUNT_TO_BE_REFUNDED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0625' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CORRECT_TRANSACTION_AMOUNT_TO_BE_REFUNDED_BY_SELECTED_BANK',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0626' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CORRECT_AMOUNT_TO_BE_REFUNDED_BY_SELECTED_BANK',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0627' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.FOLLOWING_TRANSACTION_KEY_IS_NOT_FOUND_TRANSACTIONKEY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0628' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PROVIDE_A_VALID_TRANSACTION_KEY_OR_REFUND_AMOUNT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0629' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.DUPLICATE_REFUND_REQUEST_FOUND_OR_ENTRY_IS_CONFLICTING_WITH_THE_BASE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0630' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_TRANSACTION_CUSTOMER_MISMATCH',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0631' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.FEW_TRANSACTIONS_IN_DRAFT_STATE_AND_NOT_REFUNDED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0632' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.TRANSACTION_IS_NOT_VALID_CREDIT_FOR_TXNKEY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0633' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_TRANSACTION_CURRENCY_MISMATCH_BASE_CURRENCY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0634' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.REFUND_DATE_CANNOT_BEFORE_TXN_CREATION_DATE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0635' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.REFUND_DATE_WITHIN_CURRENT_PERIOD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0636' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_TRANSACTION_KEY_NO_LINE_ITEMS_FOUND',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0637' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_ENTRY_KEY_ENTRYKEY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0638' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.REFUND_AMOUNT_CANNOT_BE_GREATER_THAN_DUE_AMOUNT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0639' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.REFUND_CANNOT_BE_PROCESSED_FEW_LINEITEM_ALREADY_PAID',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0640' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.POSITIVE_LINE_ITEMS_CANNOT_PROCESSED_FOR_REFUND',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0641' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_REQUEST_AMOUNT_REQUIRED_FOR_THE_TRANSACTION',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0642' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BASE_AMOUNT_BASEAMOUNT_MUST_BE_GREATER_THAN_0',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0643' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BASE_AMOUNT_BASEAMOUNT_DOES_NOT_MATCH_THE_CALCULATED_AMOUNT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0644' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.BASE_AMOUNT_BASEAMOUNT_DOES_NOT_MATCH_THE_CALCULATED_AMOUNT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0645' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CREDIT_TYPE_IS_MISSING',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0646' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PROVIDE_A_VALID_CREDIT_TYPE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0647' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PROVIDE_VALID_RECORD_ID_FOR_CREDIT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0648' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.PROVIDE_VALID_REFUND_AMOUNT',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0649' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.CUSTOMER_REFUND_RECORD_NOT_FOUND_FOR_THE_KEY',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
    'AR-0650' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVOICE_TERM_CHANGE_FAILURE',
        IAIssueDef::KEY_CORRECTION_ID => 'IA.INVOICE_TERM_CHANGE_CORRECTION',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
    ],
    'AR-0651' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::UNABLE_TO_UPDATE_RECORD,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_UPDATE_AR_SUMMARY_RECORD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '',
        IAIssueDef::KEY_LEGACY_META => [0,0]
    ],
    'AR-0652' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.COULD_NOT_DELETE_AR_SUMMARY_RECORD',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '',
        IAIssueDef::KEY_LEGACY_META => [0,0]
    ],
    'AR-0653' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::OPERATION_FAILED,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.UPDATING_FAILED_1',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ]
    ],
    'AR-0654' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::OPERATION_FAILED,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.FETCHING_DESCRIPTION_FAILED',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => '**********',
        IAIssueDef::KEY_LEGACY_META => [0,0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP  => []
        ],
    ],
    'AR-0655' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INFORMATION_MISSING,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.INVALID_DATE',
        IAIssueDef::KEY_DESCRIPTION2_ID => 'IA.ENTER_REVERSAL_DATE_WHICH_IS_ON_OR_AFTER_THE_ORIGINAL_TRANSACTION_DATE',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST,
        IAIssueDef::KEY_LEGACY_ERROR_NO_ID => 'BL34000062',
        IAIssueDef::KEY_LEGACY_META => [0, 0],
        IAIssueDef::KEY_LEGACY_GETTEXT_MAP => [
            IALegacyMeta::DESC1_GETTEXT_MAP => [],
            IALegacyMeta::DESC2_GETTEXT_MAP => [],
            IALegacyMeta::CORR_GETTEXT_MAP => []
        ]
    ],
    'AR-0656' => [
        IAIssueDef::KEY_CATEGORY => IAIssueCategory::INVALID_REQUEST,
        IAIssueDef::KEY_DESCRIPTION1_ID => 'IA.NO_MCP_REFUND_SUPPORTED_FOR_EARLY_ADOPTER',
        IAIssueDef::KEY_HTTP_STATUS => HttpStatus::BAD_REQUEST
    ],
];
