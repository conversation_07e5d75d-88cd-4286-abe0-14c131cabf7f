<?php

/**
 * Each error code is componsed of 2 elements
 *
 * # Error Code Format
 *      {module-name}-{4-digit-code-number}
 *
 * # ELEMENT
 *      {module-name}: 2 to 5 characters all-cap module name, e.g. AP, AR, CM, REST, etc
 *
 *      {4-digit-code-number}:
 *           - 9000 ~ 9999 should be used for internal errors, 9xxxx error will not be returned to customer,
 *           - other than the last 1k 9xxx numbers are for internal errors, the other 9k numbers (0000~8999) are left for
 *             module owner to decide their conventions, e.g., to subdivide the digits for projects, severities, or owing teams, etc
 *           - error framework will not look into them for literal meaning other than filter out 9xxxx codes when sending to customers.
 *
 *           for your reference, REST module uses the 1st of the 4 digits for sub-areas;
 *           0000~0999 is for CRUD, 1000~1999 is for core service, 2000~2999 is authentication, etc.
 *           Each sub-areas can further divide into more granular areas like, validaiton, biz error, etc
 *
 * # Example
 *      BLAH-0001 is an error code following the format, where
 *      - "BLAH" is the moduel name,
 *      - 00001 is the code number
 *
 */
class IAIssueDef
{
    // ===============================
    //   constants
    // ===============================
    public const INVALID_ERROR_CODE_HINT    = '[>>> invalid error code] ';
    public const ERROR_ID_FORMAT            = '/^([A-Z]{2,5})-([0-9]{4})$/';
    public const ERROR_ID_INTERNAL_FORMAT   = '/^([A-Z]{2,5})-INT-([0-9]{4})$/';
    public const ERROR_ID_LEGACY_FORMAT     = '/^([A-Z]{2,5})[0-9]{8}$/';
    public const FILENAME_SEARCH            = '${module}';
    public const FILENAME_TEMPLATE          = 'issuedefs.' . self::FILENAME_SEARCH . '.inc';
    public const KEY_LEGACY_ERROR_NO_ID     = 'legacyErrorNo';
    public const KEY_DESCRIPTION1_ID        = 'description1';
    public const KEY_DESCRIPTION2_ID        = 'description2';
    public const KEY_CORRECTION_ID          = 'correction';
    public const KEY_UX_MESSAGE_ID          = 'uxMessage';
    public const KEY_HTTP_STATUS            = 'httpStatus';
    public const KEY_CATEGORY               = 'category';
    public const KEY_LEGACY_META            = 'legacyMeta'; // use IALegacyMeta to generate & parse, for content team
    public const KEY_LEGACY_GETTEXT_MAP     = 'legacyGetTextMap';
    public const KEY_LEGACY_REST_KEY        = 'legacyRestKey';

    public const KEY_LEGACY_ERROR_DEF_MODULE_LIST = [
        'BL', // BUSINESS LAYER errors
        'DL', // DATA LAYER errors
        'PL', // PRESENTATION LAYER errors
        'PT', // PLATFORM errors
        'XL', // XML LAYER errors
    ];

    /** @var array[] $emptyGetTextMap */
    public static $emptyGetTextMap = [
        IALegacyMeta::DESC1_GETTEXT_MAP => [],
        IALegacyMeta::DESC2_GETTEXT_MAP => [],
        IALegacyMeta::CORR_GETTEXT_MAP  => []
    ];

    // ===============================
    //   attributes
    // ===============================
    /** @var string $code */
    private $code;

    /** @var string $legacyErrorNo */
    protected $legacyErrorNo;

    /** @var ?string $description1Id */
    private $description1Id;

    /** @var ?string $description2Id */
    private $description2Id;

    /** @var ?string $correctionId */
    private $correctionId;

    /** @var ?string $uxMessageId */
    private $uxMessageId;

    /** @var HttpStatus $httpStatus */
    private $httpStatus;

    /** @var IAIssueCategory $category */
    private $category; // short error title, category, e.g. Invalid Configuration, Invalid Transaction

    /** @var IALegacyMeta $legacyMeta */
    private $legacyMeta; // stores legacy error meta for content team

    /**
     * IAIssueDef constructor.
     *
     * @param array $def error definition in associative array format
     */
    private function __construct(array $def)
    {
        $this->setDescription1IdByArray($def)
             ->setDescription2IdByArray($def)
             ->setLegacyErrorNoByArray($def)
             ->setCorrectionIdByArray($def)
             ->setUxMessageIdByArray($def)
             ->setHttpStatusByArray($def)
             ->setLegacyMetaByArray($def)
             ->setCategoryByArray($def);
    }

    /**
     * @param string          $errorCode
     * @param IAIssueData     $errorData
     * @param IAIssueCodeType &$iaIssueCodeType
     *
     * @return IAIssueDef|null
     * @throws IAException
     */
    public static function load(string $errorCode, IAIssueData $errorData, IAIssueCodeType &$iaIssueCodeType = IAIssueCodeType::UNKNOWN) : ?IAIssueDef
    {
        // parse $errorId to get error def file name
        if (preg_match(self::ERROR_ID_FORMAT, $errorCode, $errorIdMatches) !== 1
            || count($errorIdMatches) != 3) {
            if (preg_match(self::ERROR_ID_LEGACY_FORMAT, $errorCode, $legacyIdMatches) === 1
                && count($legacyIdMatches) === 2
                && in_array($legacyIdMatches[1], self::KEY_LEGACY_ERROR_DEF_MODULE_LIST)) {
                // legacy error
                // todo - IA-39139 deprecate this workaround after 22R4
                if (IAIssueUtil::forceLegacyErrorToFail()) {
                    throw IADeprecateErrorDefException::instantiate(GetFL(), 'legacy error code ' . $errorCode);
                } else {
                    $iaIssueCodeType = IAIssueCodeType::LEGACY;
                    return null;
                }
            }
            if (preg_match(self::ERROR_ID_FORMAT, $errorCode, $errorIdMatches) !== 1
                || count($errorIdMatches) != 3) {
                // Internal error
                $iaIssueCodeType = IAIssueCodeType::INTERNAL;
                return null;
            }
            if (IAIssueUtil::forceLegacyErrorToFail()) {
                throw IAInvalidErrorCodeException::instantiate(GetFL(), 'invalid error id format ' . $errorCode);
            } else {
                // unknown error
                $iaIssueCodeType = IAIssueCodeType::INVALID;
                return null;
            }
        }
        $defFileName = str_replace(
            self::FILENAME_SEARCH,
            $moduleName = strtolower($errorIdMatches[1]),
            self::FILENAME_TEMPLATE);
        if (stream_resolve_include_path($defFileName)) {
            include $defFileName;
        } else {
            // error definition file does not exist, or not its file name is not following naming convention
            if (IAIssueUtil::forceLegacyErrorToFail()) {
                throw new RuntimeException($defFileName . ' does not exist for error code ' . $errorCode);
            } else {
                $iaIssueCodeType = IAIssueCodeType::MISSING;
                return null;
            }
        }
        /** @noinspection PhpUndefinedVariableInspection */ // $IAi18ErrorDef is defined in $defFileName
        if (empty($errorDef = $IAi18ErrorDef[$moduleName][$errorCode] ?? null)) {
            if (IAIssueUtil::forceLegacyErrorToFail()) {
                throw IAErrorDefMissing::instantiate(GetFL(), $errorCode . ' does not exist in ' . $defFileName);
            } else {
                $iaIssueCodeType = IAIssueCodeType::MISSING;
                return null;
            }
        }
        $iaIssueCodeType = IAIssueCodeType::I18N;
        return (new IAIssueDef($errorDef))->setCode($errorCode);
    }

    // ===============================
    //   helpers
    // ===============================

    /**
     * caller provide common seperated domain name(s) as query parameter
     * this function will load issue defintion definined in those domains into json format
     * then compress and encode the defintion
     *
     * return null when empty domain array is provided
     *
     * @param array $domains
     *
     * @return string|null
     */
    public static function getCompressedIssueDefinitions(array $domains) : ? string
    {
        foreach ( $domains as $moduleName) {
            $filePath = str_replace(
                self::FILENAME_SEARCH,
                $moduleName = strtolower($moduleName),
                self::FILENAME_TEMPLATE);
            if ( stream_resolve_include_path($filePath) ) {
                include $filePath;
            } else {
                throw (new APIException())->setAPIError(APIError::getInstance(
                    APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0030,["PARAMETER" => 'domains'], true)->setTarget($filePath));
            }
        }

        /** @noinspection PhpUndefinedVariableInspection */
        if (empty($IAi18ErrorDef)) {
            return null;
        }
        $fileContents = json_encode($IAi18ErrorDef ?? '', JSON_UNESCAPED_SLASHES);
        $zipPath = tempnam(sys_get_temp_dir(), 'zip');
        $zip = new \ZipArchive();
        $zip->open($zipPath, \ZipArchive::CREATE);
        $zip->addFromString('issuedefs.json', $fileContents);
        $zip->close();

        // Read the zipped file contents
        $zippedContents = file_get_contents($zipPath);
        $base64Encoded = base64_encode($zippedContents);
        unlink($zipPath);
        return $base64Encoded;
    }

    // ===============================
    //   accessors
    // ===============================

    /**
     * @return string
     */
    public function getCode() : string
    {
        return $this->code;
    }

    /**
     * @param string $code
     *
     * @return IAIssueDef
     */
    private function setCode(string $code) : IAIssueDef
    {
        $this->code = $code;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getLegacyErrorNo() : ?string
    {
        return $this->legacyErrorNo ?? null;
    }

    /**
     * @param array $errorDefArray
     *
     * @return IAIssueDef
     */
    public function setLegacyErrorNoByArray(array $errorDefArray) : IAIssueDef
    {
        $this->legacyErrorNo = $errorDefArray[self::KEY_LEGACY_ERROR_NO_ID] ?? null;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getDescription1Id() : ?string
    {
        return $this->description1Id;
    }

    /**
     * @param array $errorDefArray
     *
     * @return $this
     */
    private function setDescription1IdByArray(array $errorDefArray) : IAIssueDef
    {
        $this->description1Id = $errorDefArray[self::KEY_DESCRIPTION1_ID] ?? null;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getDescription2Id() : ?string
    {
        return $this->description2Id;
    }

    /**
     * @param array $errorDefArray
     *
     * @return $this
     */
    private function setDescription2IdByArray(array $errorDefArray) : IAIssueDef
    {
        $this->description2Id = $errorDefArray[self::KEY_DESCRIPTION2_ID] ?? null;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getCorrectionId() : ?string
    {
        return $this->correctionId;
    }

    /**
     * @return string|null
     */
    public function getUxMessageId() : ?string
    {
        return $this->uxMessageId;
    }

    /**
     * @param array $errorDefArray
     *
     * @return $this
     */
    private function setCorrectionIdByArray(array $errorDefArray) : IAIssueDef
    {
        $this->correctionId = $errorDefArray[self::KEY_CORRECTION_ID] ?? null;
        return $this;
    }

    /**
     * @param array $errorDefArray
     *
     * @return IAIssueDef
     */
    private function setUxMessageIdByArray(array $errorDefArray) : IAIssueDef
    {
        $this->uxMessageId = $errorDefArray[self::KEY_UX_MESSAGE_ID] ?? null;
        return $this;
    }

    /**
     * @return HttpStatus
     */
    public function getHttpStatus() : HttpStatus
    {
        return $this->httpStatus;
    }

    /**
     * @param array $errorDefArray
     *
     * @return $this
     */
    private function setHttpStatusByArray(array $errorDefArray) : IAIssueDef
    {
        $this->httpStatus = $errorDefArray[self::KEY_HTTP_STATUS] ?? null;
        return $this;
    }

    /**
     * @return IAIssueCategory
     */
    public function getCategory() : IAIssueCategory
    {
        return $this->category;
    }

    /**
     * @param array $errorDefArray
     *
     * @return IAIssueDef
     */
    private function setCategoryByArray(array $errorDefArray) : IAIssueDef
    {
        $this->category = $errorDefArray[self::KEY_CATEGORY] ?? '';
        return $this;
    }

    /**
     * @return IALegacyMeta
     */
    public function getLegacyMeta() : IALegacyMeta
    {
        return $this->legacyMeta;
    }

    /**
     * @param array $errorDefArray
     *
     * @return $this
     */
    public function setLegacyMetaByArray(array $errorDefArray) : IAIssueDef
    {
        $this->legacyMeta = IALegacyMeta::initLegacyMeta($errorDefArray[self::KEY_LEGACY_META] ?? IALegacyMeta::DEFAULT_META_NOTION);
        $this->legacyMeta->setGetTextMap($errorDefArray[self::KEY_LEGACY_GETTEXT_MAP] ?? self::$emptyGetTextMap );

        return $this;
    }
}