<?xml version='1.0' encoding='UTF-8'?>
<ROOT>
    <entity>iadashboardproperties</entity>
    <title>IA Dashboard Properties</title>
    <view>
        <pages>
            <page id="permissions" className="columnSetupPadding">
                <title>IA.PERMISSIONS</title>
                <!--
                <section>
                    <child>
                        <row>
                            <field>
                                <path>DASHBOARDOWNER</path>
                                <events>
                                    <change>SetReportOwner();</change>
                                </events>
                            </field>
                        </row>
                    </child>
                </section>
                -->
                <section id="USERGROUP_section">
                    <child>
                        <row>
                            <field path="FAKETEXT" isHTML="1">
                                <type type="textlabel" ptype="textlabel"></type>
                                <default></default>
                                <helpText>IA.SELECT_USERS_OR_GROUPS_OF_USERS_TO_WHICH_YOU</helpText>
                            </field>
                        </row>
                    </child>
                    <child id="USERGROUP_child" className="hidelabel">
                        <row>
                            <field path="USERGROUP">
                                <events>
                                    <change>changeUserGroup(this.meta);</change>
                                </events>
                            </field>
                            <field>GROUP</field>
                            <field hidden="true">USER</field>
                            <button>
                                <!-- I18N: TODO -->
                                <name>IA.SELECT</name>
                                <events>
                                    <click>addToGrid();</click>
                                </events>
                            </button>
                        </row>
                    </child>
                </section>
                <section>
                    <child>
                        <grid clazz="PermGrid" showDelete="true" noDragDrop="true" noNewRows="true" noPagination="true">
                            <path>PERMLIST</path>
                            <column className="access_rights">
                                <field path="ALLOWACCESS" hidden="true"></field>
                                <field path="ALLOWACCESS2">
                                    <events>
                                        <change>setAccessRights();</change>
                                    </events>
                                </field>
                            </column>
                            <column>
                                <field path="PERMLISTMEMBER"></field>
                            </column>
                        </grid>
                    </child>
                </section>
            </page>
            <page id="properties" className="columnSetupPadding">
                <title>IA.DASHBOARD_PROPERTIES</title>
                <section>
                    <child>
                        <field>RECORDNO</field>
                        <field>TITLE</field>
                        <field>DESCRIPTION</field>
                        <field>NUMCOLUMNS
                            <events>
                                <change>ResetNumColumnsSelection(this.meta);</change>
                            </events>
                        </field>
                        <field>MAXWIDTHON
                            <events>
                                <change>ResetMaxWidthSelection(this.meta);</change>
                            </events>
                        </field>
                        <field hidden="true">
                            <path>ONECOLUMN</path>
                            <events>
                                <change>ResetColumnSelection(this.meta);</change>
                            </events>
                        </field>
                    </child>
                    <child>
                        <row id="TWOCOLUMNS" hidden="true" label="IA.TWO_COLUMNS_MAXIMUM_WIDTH_ON">
                            <field rightSideLabel="true">
                                <path>TWOCOLUMNSLEFT</path>
                                <events>
                                    <change>ResetColumnSelection(this.meta);</change>
                                </events>
                            </field>
                            <field rightSideLabel="true">
                                <path>TWOCOLUMNSRIGHT</path>
                                <events>
                                    <change>ResetColumnSelection(this.meta);</change>
                                </events>
                            </field>
                            <field rightSideLabel="true">
                                <path>TWOCOLUMNSEQUAL</path>
                                <events>
                                    <change>ResetColumnSelection(this.meta);</change>
                                </events>
                            </field>
                        </row>
                        <row id="THREECOLUMNS" hidden="true" label="IA.THREE_COLUMNS_MAXIMUM_WIDTH_ON">
                            <field rightSideLabel="true">
                                <path>THREECOLUMNSLEFT</path>
                                <events>
                                    <change>ResetColumnSelection(this.meta);</change>
                                </events>
                            </field>
                            <field rightSideLabel="true">
                                <path>THREECOLUMNSMIDDLE</path>
                                <events>
                                    <change>ResetColumnSelection(this.meta);</change>
                                </events>
                            </field>
                            <field rightSideLabel="true">
                                <path>THREECOLUMNSRIGHT</path>
                                <events>
                                    <change>ResetColumnSelection(this.meta);</change>
                                </events>
                            </field>
                            <field rightSideLabel="true">
                                <path>THREECOLUMNSEQUAL</path>
                                <events>
                                    <change>ResetColumnSelection(this.meta);</change>
                                </events>
                            </field>
                        </row>
                    </child>
                    <child>
                        <field>
                            <path>THEME</path>
                            <events>
                                <change>ValidateStyle(this.meta);setFocusOnField('THEME');</change>
                            </events>
                        </field>
                        <field>CUSTOMBACKGROUNDCOLOR</field>
                        <field>CUSTOMFONTCOLOR</field>
                        <field>INDUSTRYCODE</field>
                        <field>USERTYPE</field>
                        <field>APPTYPE</field>
                        <field>COL1TYPE</field>
                        <field>COL2TYPE</field>
                        <field>COL3TYPE</field>
                        <field>PARENTDASHBOARD</field>
                        <field>COMPANYDASHBOARD</field>
                    </child>
                    <child>
                        <field>
                            <path>DASHBOARDGROUPPOPUP</path>
                            <events>
                                <change>ChangeDashboardGroup(this.meta);</change>
                            </events>
                            <!-- I18N: TODO -->
                            <helpText>IA.FOR_ORGANIZING_INTO_SUBMENUS</helpText>
                        </field>
                        <field hidden="true">
                            <path>DASHBOARDGROUP</path>
                            <events>
                                <change>ChangeDashboardGroup2(this.meta);</change>
                            </events>
                        </field>
                        <field hidden="true">DEFAULTDASHBOARD</field>
                    </child>
                </section>
            </page>
        </pages>
    </view>
    <helpfile>Creating_a_New_Dashboard</helpfile>
</ROOT>
