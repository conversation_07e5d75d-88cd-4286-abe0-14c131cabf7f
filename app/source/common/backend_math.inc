<?php

//=============================================================================
//
//	FILE:			backend_math.inc
//	AUTHOR:			<PERSON>
//	DESCRIPTION:	Contains functions for wrapping bcmath.  The BC math 
//                  functions built into PHP truncate operand digits to the 
//                  right of the decimal place to the bcscale.  This is 
//                  incorrect.  It should round the result and not touch
//                  the operands.
//
//	(C)2002, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================

/**
 * @param string|int|float $leftoperand
 * @param string|int|float $rightoperand
 * @param int              $precision
 * @param bool             $round
 *
 * @return string
 */
function ibcmul($leftoperand, $rightoperand, $precision = 2, $round = false)
{
    if (! Globals::$g->islive) {
        bcMathCheckAndLogBadOperand('leftoperand', __FUNCTION__, $leftoperand);
        bcMathCheckAndLogBadOperand('rightoperand', __FUNCTION__, $rightoperand);
    }
    $res = isValidBcMathPayload($leftoperand, $rightoperand) ? safeBcmul($leftoperand, $rightoperand, 14) : '0';
    if ($round) {
        return iround($res, $precision);
    } else {
        return bcadd($res, '0', $precision);
    }
}

/**
 * @param string $leftoperand
 * @param string $rightoperand
 * @param int    $precision
 * @param bool   $round
 *
 * @return string
 */
function ibcdiv($leftoperand, $rightoperand, $precision = 2, $round = false)
{
    if (! Globals::$g->islive) {
        bcMathCheckAndLogBadOperand('leftoperand', __FUNCTION__, $leftoperand);
        bcMathCheckAndLogBadOperand('rightoperand', __FUNCTION__, $rightoperand);
    }
    $res = isValidBcMathPayload($leftoperand, $rightoperand) ? safeBcdiv($leftoperand, $rightoperand, 14) : '0';
    if ($round) {
        return iround($res, $precision);
    } else {
        return bcadd($res, '0', $precision);
    }
}

/**
 * @param string $leftoperand
 * @param string $rightoperand
 * @param int    $precision
 * @param bool   $round
 *
 * @return string
 * @access public
 */
function ibcadd($leftoperand, $rightoperand, $precision = 2, $round = false)
{
    if (! Globals::$g->islive) {
        bcMathCheckAndLogBadOperand('leftoperand', __FUNCTION__, $leftoperand);
        bcMathCheckAndLogBadOperand('rightoperand', __FUNCTION__, $rightoperand);
    }
    
    $res = isValidBcMathPayload($leftoperand, $rightoperand) ? safeBcadd($leftoperand, $rightoperand, 14) : '0';
    if ($round) {
        return iround($res, $precision);
    } else {
        return bcadd($res, '0', $precision);
    }
}

/**
 * @param string $leftoperand
 * @param string $rightoperand
 * @param int    $precision
 * @param bool   $round
 *
 * @return string
 * @access public
 */
function ibcsub($leftoperand, $rightoperand, $precision = 2, $round = false)
{
    if (! Globals::$g->islive) {
        bcMathCheckAndLogBadOperand('leftoperand', __FUNCTION__, $leftoperand);
        bcMathCheckAndLogBadOperand('rightoperand', __FUNCTION__, $rightoperand);
    }
    $res = isValidBcsubPayload($leftoperand, $rightoperand) ? safeBcsub($leftoperand, $rightoperand, 14) : '0';
    if ($round) {
        return iround($res, $precision);
    } else {
        return bcadd($res, '0', $precision);
    }
}

/**
 * 0 if leftoperand is equal to the rightoperand
 * 1 if leftoperand is greater than the rightoperand
 * -1 if leftoperand is smaller than the rightoperand
 * null if payload is not acceptable(array, object, non numeric strings)
 *
 * @param string $leftoperand
 * @param string $rightoperand
 * @param int    $precision
 *
 * @return int|null
 */
function ibccomp($leftoperand, $rightoperand, $precision = 14) : ?int
{
    if (! Globals::$g->islive) {
        bcMathCheckAndLogBadOperand('leftoperand', __FUNCTION__, $leftoperand);
        bcMathCheckAndLogBadOperand('rightoperand', __FUNCTION__, $rightoperand);
    }
    return isValidBcMathPayload($leftoperand, $rightoperand) ? safeBccomp($leftoperand, $rightoperand, $precision) : null;
}


/**
 * round input to specified precision rounding
 *
 * TODO: Make string when we restore the throw
 * @param mixed $number
 * @param int   $precision
 *
 * @return string
 * @access public
 */
function iround($number, $precision)
{
    if (!Globals::$g->islive && !is_numeric($number)) {
        logBcMath('Invalid datatype (' . gettype($number) . ' ' . var_export($number, true) . ') passed to ' . __FUNCTION__);
    }
    // array and objects cannot be allowed due to errors in PHP 8.1(Unsupported operand types: array * string)
    if (php7eq0($number) || !is_scalar($number) || true === $number) {
        return '0';
    }
    $precision = (int) $precision;
    // !is_string($number) in this case means: is_int($number) || is_float($number)
    if (!is_string($number) || $precision < 0) {
        $powPrecision = pow(10, $precision);
        $ret = round(($number ?: 0) * $powPrecision) / $powPrecision;
        if (strchr((string)$ret, 'E') !== false) {
            // exp2int
            [$mantissa, $exponent] = preg_split("/e/i", $ret);
            bcscale($precision);
            $ret = bcmul($mantissa, bcpow("10", $exponent));
        }

        return $ret;

    }

    static $roundingMap = [
        '1',
        '0.1',
        '0.01',
        '0.001',
        '0.0001',
        '0.00001',
        '0.000001',
        '0.0000001',
        '0.00000001',
        '0.000000001',
        '0.0000000001',
        '0.00000000001',
        '0.000000000001',
        '0.0000000000001',
        '0.00000000000001',
        '0.000000000000001',
        '0.0000000000000001',
        '0.00000000000000001',
        '0.000000000000000001',
        '0.0000000000000000001',
        '0.00000000000000000001',
        '0.000000000000000000001',
        '0.0000000000000000000001',
        '0.00000000000000000000001',
        '0.000000000000000000000001',
        '0.0000000000000000000000001',
        '0.00000000000000000000000001',
        '0.000000000000000000000000001',
        '0.0000000000000000000000000001',
        '0.00000000000000000000000000001',
        '0.000000000000000000000000000001',
        '0.0000000000000000000000000000001',
        '0.00000000000000000000000000000001',
        '0.000000000000000000000000000000001',
        '0.0000000000000000000000000000000001',
        '0.00000000000000000000000000000000001',
        '0.000000000000000000000000000000000001',
        '0.0000000000000000000000000000000000001',
        '0.00000000000000000000000000000000000001',
        '0.000000000000000000000000000000000000001',
        '0.0000000000000000000000000000000000000001',
    ];

    if ($precision < 0 || $precision > 40) {
        throw new Exception('Invalid precision argument passed to ' . __FUNCTION__);
    }

    $d = strpos($number, '.');
    if ($d === false) {
        return _reformatibc($number);
    }

    $l = strlen($number);
    if ($l - $d - 1 > $precision) {

        if ( $number[$d + $precision + 1] >= '5' ) {
            if ($number[0] === '-') {
                $extra = '-' . $roundingMap[$precision];
            } else {
                $extra = $roundingMap[$precision];
            }
        } else {
            $extra = null;
        }

        if ( $precision === 0 ) {
            $l = $d;
        } else {
            $l = $d + 1 + $precision;
        }

        if ($l === 0) {
            $number = '0';
            //$l = 1;
        } else {
            $number = substr($number, 0, $l);
        }

        if ( $extra !== null ) {
            $numberStr = (string) $number;
            if($numberStr !== '') {
                $number = ibcadd($numberStr, $extra, $precision);
            }
        }

    }

    // Preserve old behavior of truncating trailing 0s.
    return _reformatibc($number);

}

/**
 * removes leading and trailing zeros and converts various flavors of zero to 0.
 *
 * @param string $number
 *
 * @return string
 */
function _reformatibc($number)
{
    if (ibccomp($number, 0) == 0) {
        // Handles all kinds of -0, 00, 0.000, ".", "-"
        return '0';
    }

    $length = strlen($number);

    $isNegative = $number[0] === '-';
    $originalStart = $isNegative ? 1 : 0;

    // Truncate leading zeros
    for ($newStart = $originalStart; $newStart < $length; $newStart++) {
        $c = $number[$newStart];
        if ($c != '0') {
            if ( $c === '.' && $newStart > $originalStart ) {
                // Leave a leading zero before the decimal point
                $newStart--;
            }
            break;
        }
    }

    // Truncate trailing zeros after the decimal point including the decimal point if necessary
    $truncateAt = -1;
    $orignalEnd = $length - 1;
    $newEnd = $orignalEnd;
    for ($i = $newEnd; $i >= $newStart; $i--) {
        $c = $number[$i];
        if ($c === '.') {
            if ($truncateAt == -1) {
                $newEnd = $i - 1;
            } else {
                $newEnd = $truncateAt;
            }
            break;
        } else if ($c !== '0') {
            if ($truncateAt === -1) {
                $truncateAt = $i;
            }
        }
    }

    // Perform truncation
    if ( $newStart > $originalStart || $newEnd < $orignalEnd ) {
        if ( $newEnd < $newStart ) {
            $number = '0';
        } else if ( $isNegative ) {
            if ( $number[1] === '.' ) {
                $number = '-0' . substr($number, $newStart, 1 + $newEnd - $newStart);
            } else {
                $number = '-' . substr($number, $newStart, 1 + $newEnd - $newStart);
            }
        } else {
            if ( $number[0] === '.' ) {
                $number = '0' . substr($number, $newStart, 1 + $newEnd - $newStart);
            } else {
                $number = substr($number, $newStart, 1 + $newEnd - $newStart);
            }
        }
    } else if ( $isNegative ) {
        // Catch non-truncated input that didn't have a leading 0 before the decimal point
        if ( $number[1] === '.' ) {
            $number = '-0' . substr($number, 1);
        }
    } else {
        // Catch non-truncated input that didn't have a leading 0 before the decimal point
        if ( $number[0] === '.' ) {
             $number = '0' . $number;
        }
    }

    return $number;
}


/**
 * Distributes roundoff amoung its components
 *
 * // TODO: make string someday
 * @param float|string|int $totalamt
 * @param float|string|int $distamt
 * @param array            $amounts AMOUNT fields to be corrected
 * @param bool             $skipZero
 */
function CorrectRoundOffError($totalamt, $distamt, &$amounts, $skipZero = false)
{
    if (!Globals::$g->islive) {
        if ( ! is_string($totalamt) || ! preg_match('/^-?([0-9]+(\.[0-9]*)?|\.[0-9]+)$/', $totalamt) ) {
            logBcMath('Invalid totalamt datatype (' . gettype($totalamt) . ' ' . $totalamt . ') passed to '
                           . __FUNCTION__);
        }
        if ( ! is_string($distamt) || ! preg_match('/^-?([0-9]+(\.[0-9]*)?|\.[0-9]+)$/', $distamt) ) {
            logBcMath('Invalid distamt datatype (' . gettype($distamt) . ' ' . $distamt . ') passed to '
                           . __FUNCTION__);
        }
        if ( $amounts && ! is_string($amounts[0]) || ! preg_match('/^-?([0-9]+(\.[0-9]*)?|\.[0-9]+)$/', $amounts[0]) ) {
            logBcMath('Invalid amounts distamt datatype (' . gettype($amounts[0]) . ' ' . $amounts[0]
                           . ') passed to ' . __FUNCTION__);
        }
    }

    if ($totalamt == 0 || !$amounts) {
        return;
    }

    $amountsCount = count($amounts);

    // Check for rounding differences
    $diff = ibcsub($totalamt, $distamt, Util::AMOUNT_PRECISION, true);
    if (ibcabs($diff) * 100 > $amountsCount) {
        return;
    }

    if ($diff != 0) {

        // Sign represents -ve for roundup and +ve for rounddown
        $sign = ibcsgn($diff);

        if ($skipZero) {
            // If all the values in the $amounts array are 0's, then do not skip zeros -- distribute evenly
            $allZeros = true;
            foreach ($amounts as $amt) {
                if ($amt != 0) {
                    $allZeros = false;
                    break;
                }
            }
            if ($allZeros) {
                $skipZero = false;
            }
        }
        //	Assign the first penny roundoff to the first entity
        //	Assign the second penny roundoff to the next entity, etc.
        //  This way, the pennies get distributed.
        while ($diff != 0) {
            for ($i = 0; $i < $amountsCount; $i++) {
                if ($skipZero && $amounts[$i] == 0) {
                    continue;
                }
                // penny at a time
                $amounts[$i] = ibcadd($amounts[$i], ibcmul($sign, '0.01', Util::AMOUNT_PRECISION, true), Util::AMOUNT_PRECISION, true);

                $diff = ibcsub($diff, ibcmul($sign, '0.01', Util::AMOUNT_PRECISION, true), Util::AMOUNT_PRECISION, true);
                if ((($sign > 0) && ($diff <= 0)) || (($sign < 0) && $diff >= 0)) {
                    break;
                }
            }
        }

    }
}

/**
 * @param string $val
 *
 * @return int
 */
function numberOfDecimals($val)
{
    $numOfDec = 0;
    $length = strlen($val);
    if ($length > 0) {
        $pos = strpos($val, "."); // zero-based counting.
        if ($pos !== false) {
            $numOfDec = ($length - $pos) - 1; // -1 to compensate for the zero-based count in strpos()
        }
    }
    return $numOfDec;
}

/**
 * Intacct "wrapper" for the missing bcabs function.
 *
 * TODO: Make string when we restore the throw
 * @param string|float|int $number
 *
 * @return string
 * @throws Exception on invalid input data
 */
function ibcabs($number)
{
    if (!is_string($number) || ! preg_match('/^-?([0-9]+(\.[0-9]*)?|\.[0-9]+)$/', $number)) {
        if (!Globals::$g->islive) {
            logBcMath('Invalid datatype (' . gettype($number) . ' ' . $number . ') passed to ' . __FUNCTION__);
        }
        // TODO: Someday throw new Exception("Invalid datatype passed to " . __FUNCTION__);
        return is_numeric($number) ? abs($number): 0;
    }

    // String comparison is 8x faster than ord($strNum) === 45
    if ( $number[0] === '-' ) {
        return _reformatibc(substr($number, 1));
    }

    return _reformatibc($number);
}

/**
 * returns a negative version of the input parameter.  Like -abs() would do.
 *
 * TODO: Make string when we restore the throw
 * @param string|float|int $number
 *
 * @return string|float|int
 * @throws Exception on invalid input data
 */
function ibcnegabs($number)
{
    if ($number == 0) {
        return '0';
    }

    if (!is_string($number) || ! preg_match('/^-?([0-9]+(\.[0-9]*)?|\.[0-9]+)$/', $number)) {
        if (!Globals::$g->islive) {
            logBcMath('Invalid datatype (' . gettype($number) . ' ' . $number . ') passed to ' . __FUNCTION__);
        }
        // TODO: Someday throw new Exception("Invalid datatype passed to " . __FUNCTION__);
        return -abs($number);
    }

    // String comparison is 8x faster than ord($strNum) === 45
    if ( $number[0] !== '-' ) {
        return _reformatibc('-' . $number);
    }

    return _reformatibc($number);
}

/**
 * returns a negative version of the input parameter.  Like -abs() would do.
 *
 * TODO: Make string when we restore the throw
 * @param string|int|float $number
 *
 * @return string|float|int
 * @throws Exception on invalid input data
 */
function ibcnegate($number)
{
    if (!is_string($number) || ! preg_match('/^-?([0-9]+(\.[0-9]*)?|\.[0-9]+)$/', $number)) {
        if (!Globals::$g->islive) {
            logBcMath('Invalid datatype (' . gettype($number) . ' ' . $number . ') passed to ' . __FUNCTION__);
        }
        // TODO: Someday throw new Exception("Invalid datatype passed to " . __FUNCTION__);
        return -1 * $number;
    }

    if ($number == 0) {
        return '0';
    }

    // String comparison is 8x faster than ord($strNum) === 45
    if ( $number[0] === '-' ) {
        return _reformatibc(substr($number, 1));
    }

    return _reformatibc('-' . $number);
}

/**
 * returns the sign of the input parameter.  -1 for negative numbers, 1 for 0 and positive numbers.
 *
 * TODO: Make string when we restore the throw
 * @param string|float|int $number
 *
 * @return string
 * @throws Exception
 */
function ibcsgn($number)
{
    if (!is_string($number) || ! preg_match('/^-?([0-9]+(\.[0-9]*)?|\.[0-9]+)$/', $number)) {
        if (!Globals::$g->islive) {
            logBcMath('Invalid datatype (' . gettype($number) . ' ' . $number . ') passed to ' . __FUNCTION__);
        }
        // TODO: Someday throw new Exception("Invalid datatype passed to " . __FUNCTION__);
        return $number < 0 ? '-1' : ( $number == 0 ? '0' : '1');
    }

    if ($number == 0) {
        return '0';
    }

    // String comparison is 8x faster than ord($strNum) === 45
    return $number[0] === '-' ? '-1' : ( $number == 0 ? '0' : '1');
}

/**
 * logs a message to bcmath.log if BCMATH_LOGGING is enabled in ia_init(.local).cfg
 *
 * @param string $message
 */
function logBcMath($message)
{
    static $loggingEnabled = null;

    if ( $loggingEnabled === null ) {
        $loggingEnabled = (bool) GetValueForIACFGProperty('BCMATH_LOGGING');
    }

    if ( $loggingEnabled ) {
        logDiagnostics('BCMATH', $message, true, BCMATH_LOGFILE);
    }
}

/**
 * Check whether $num is a valid number for passing to bcmath functions without causing TypeError.
 * Validation is done based on BCMath number format as specified in {@link https://www.php.net/manual/en/intro.bc.php}
 *
 * @param mixed|null $num
 *
 * @return bool
 */
function isValidNumber($num) : bool
{
    $isNumber = isset($num) && is_numeric($num);
    if ($isNumber && is_string($num)) {
        // Validate against well-formed BCMath number format
        if (! preg_match('/^[+-]?[0-9]*(\.[0-9]*)?$/', $num)) {
            $isNumber = false;
        }
    }
    return $isNumber;
}

/**
 * @param string $operandName
 * @param string $callerName
 * @param mixed  $value
 *
 * @return void
 * @throws Exception
 */
function bcMathCheckAndLogBadOperand(string $operandName, string $callerName, $value) : void
{
    if ( !(is_string($value) || is_int($value) || is_float($value)) || !preg_match('/^-?([0-9]+(\.[0-9]*)?|\.[0-9]+)$/', $value) ) {
        logBcMath(
            sprintf(
                'Invalid datatype for %s (%s %s) passed to %s',
                $operandName,
                gettype($value),
                var_export($value, true),
                $callerName
            )
        );
    }
}

/**
 * @param mixed $leftoperand
 * @param mixed $rightoperand
 *
 * @return bool
 */
function isValidBcMathPayload($leftoperand, $rightoperand) : bool
{
    if (is_array($leftoperand) || is_array($rightoperand)
        || is_object($leftoperand) || is_object($rightoperand)
    ) {
        return false;
    }
    return true;
}

/**
 * 1 - all good
 * null - bad payload type(same output as PHP 7.4)
 * 0 - non-numeric string(same output as PHP 7.4)
 * @param mixed $leftoperand
 * @param mixed $rightoperand
 *
 * @return int|null
 */
function validateBccompPayload($leftoperand, $rightoperand) : ?int
{
    if (is_array($leftoperand) || is_array($rightoperand) || is_object($leftoperand) || is_object($rightoperand)) {
        return null;
    }
    if ((is_string($leftoperand) && !preg_match('/^-?([0-9]+(\.[0-9]*)?|\.[0-9]+)$/', $leftoperand))
       || (is_string($rightoperand) && !preg_match('/^-?([0-9]+(\.[0-9]*)?|\.[0-9]+)$/', $rightoperand))
    ) {
        return 0;
    }
    return 1;
}

/**
 * 1 - all good
 * null - bad payload type(same output as PHP 7.4)
 * 0 - non-numeric string(same output as PHP 7.4)
 * @param mixed $leftoperand
 * @param mixed $rightoperand
 *
 * @return bool
 */
function isValidBcsubPayload($leftoperand, $rightoperand) : bool
{
    if (is_array($leftoperand) || is_array($rightoperand) || is_object($leftoperand) || is_object($rightoperand)) {
        return false;
    }
    return true;
}

/**
 * @param mixed $value
 * @param int   $scale
 *
 * @return mixed
 */
function eNotationToString($value, int $scale = 14)
{
    if ( !empty($value) && preg_match('/-?[0-9]*.[0-9]*[eE]+[-|+]+[0-9]*$/', $value)) {
        return sprintf("%.{$scale}F", $value);
    }
    return $value;
}

/**
 * @param mixed $leftoperand
 * @param mixed $rightoperand
 * @param int   $scale
 *
 * @return string
 */
function safeBcadd($leftoperand, $rightoperand, int $scale = 14) : string
{
    $leftoperand = ($leftoperand === null ? '' : eNotationToString($leftoperand, $scale));
    $rightoperand = ($rightoperand === null ? '' : eNotationToString($rightoperand, $scale));
    if (version_compare(PHP_VERSION, '8.0.0') >= 0) { // PHP 8.0+
        try {
            $result = bcadd($leftoperand, $rightoperand, $scale);
        } catch (\ValueError $e) {
            if (substr_count($e->getMessage(), 'Argument #1 ($num1) is not well-formed') > 0) {
                try {
                    // left operand is not well-formed. right operand could also be not well-formed
                    $result = bcadd('0', $rightoperand, $scale);
                } catch (\ValueError $e) {
                    // Both arguments are not well-formed, result is 0
                    $result = '0';
                }
            } else {
                // only right operand is not well-formed
                $result = bcadd($leftoperand, '0', $scale);
            }
        }
    } else {  // PHP 7.4
        $result = bcadd($leftoperand, $rightoperand, $scale);
    }
    return $result;
}

/**
 * @param mixed $leftoperand
 * @param mixed $rightoperand
 * @param int   $scale
 *
 * @return string
 */
function safeBcdiv($leftoperand, $rightoperand, int $scale = 14) : string
{
    $leftoperand = eNotationToString($leftoperand, $scale);
    $rightoperand = eNotationToString($rightoperand, $scale);
    if (version_compare(PHP_VERSION, '8.0.0') >= 0) { // PHP 8.0+
        try {
            $result = ((float)$rightoperand == 0) ? '0' : bcdiv($leftoperand, $rightoperand, $scale);
        } catch (\ValueError $e) {
            $result = '0';
        }
    } else {  // PHP 7.4
        $result = ((float)$rightoperand == 0) ? '0' : bcdiv($leftoperand, $rightoperand, $scale);
    }
    return $result ?? '0';
}

/**
 * @param mixed $leftoperand
 * @param mixed $rightoperand
 * @param int   $scale
 *
 * @return string
 */
function safeBcmul($leftoperand, $rightoperand, int $scale = 14) : string
{
    $leftoperand = eNotationToString($leftoperand, $scale);
    $rightoperand = eNotationToString($rightoperand, $scale);
    if (version_compare(PHP_VERSION, '8.0.0') >= 0) { // PHP 8.0+
        try {
            $result = bcmul($leftoperand, $rightoperand, $scale);
        } catch (\ValueError $e) {
            $result = '0.00000000000000';
        }
    } else {  // PHP 7.4
        $result = bcmul($leftoperand, $rightoperand, $scale);
    }
    return $result;
}

/**
 * @param mixed $leftoperand
 * @param mixed $rightoperand
 * @param int   $scale
 *
 * @return int
 */
function safeBccomp($leftoperand, $rightoperand, int $scale = 14) : int
{
    $leftoperand = eNotationToString($leftoperand, $scale);
    $rightoperand = eNotationToString($rightoperand, $scale);
    if (version_compare(PHP_VERSION, '8.0.0') >= 0) { // PHP 8.0+
        try {
            $result = bccomp($leftoperand, $rightoperand, $scale);
        } catch (\ValueError $e) {
            if (substr_count($e->getMessage(), 'Argument #1 ($num1) is not well-formed') > 0) {
                try {
                    // left operand is not well-formed. right operand could also be not well-formed
                    $result = bccomp('0', $rightoperand, $scale);
                } catch (\ValueError $e) {
                    // Both arguments are not well-formed, result is 0
                    $result = 0;
                }
            } else {
                // only right operand is not well-formed
                $result = bccomp($leftoperand, '0', $scale);
            }
        }
    } else {  // PHP 7.4
        $result = bccomp($leftoperand, $rightoperand, $scale);
    }
    return $result;
}

/**
 * @param mixed $leftoperand
 * @param mixed $rightoperand
 * @param int   $scale
 *
 * @return string
 */
function safeBcsub($leftoperand, $rightoperand, int $scale = 14) : string
{
    $leftoperand =  ($leftoperand === null ? '' : eNotationToString($leftoperand, $scale));
    $rightoperand = ($rightoperand === null ? '' : eNotationToString($rightoperand, $scale));
    if (version_compare(PHP_VERSION, '8.0.0') >= 0) { // PHP 8.0+
        try {
            $result = bcsub($leftoperand, $rightoperand, $scale);
        } catch (\ValueError $e) {
            if (substr_count($e->getMessage(), 'Argument #1 ($num1) is not well-formed') > 0) {
                try {
                    // left operand is not well-formed. right operand could also be not well-formed
                    $result = bcsub('0', $rightoperand, $scale);
                } catch (\ValueError $e) {
                    // Both arguments are not well-formed, result is 0
                    $result = '0';
                }
            } else {
                // only right operand is not well-formed
                $result = bcsub($leftoperand, '0', $scale);
            }
        }
    } else {  // PHP 7.4
        $result = bcsub($leftoperand, $rightoperand, $scale);
    }
    return $result;
}

/**
 * Empty will take care of values like: '', "", null,  false, '0', 0
 * Type casting to float will take care of strings which are non numeric
 *
 * @param mixed $value
 *
 * @return bool
 */
function php7eq0($value) : bool
{
    return !is_array($value) && (empty($value) || (is_string($value) && (0.0 === (float) $value)));
}

/**
 * @param float|int|mixed|string $amount
 *
 * @return float|int|mixed|string
 */
function validateAndReformatAmount($amount)
{
    if (is_null($amount)) {
        return $amount;
    }
    $round = !FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('DISABLE_ROUNDING');
    if(is_string($amount) && !empty($amount)) {
        $t = explode('.', $amount);
        $cents = $t[1] ?? null;
        if (isl_strlen($cents) > 2) {
            $amount = ibcadd($amount, 0, 2, $round);
        }
    } else if(is_countable($amount)) {
        LogToFile('INVALIDPAYMENTINPUTAMOUNT');
        LogToFile(pp($amount));
    }
    return $amount;
}

