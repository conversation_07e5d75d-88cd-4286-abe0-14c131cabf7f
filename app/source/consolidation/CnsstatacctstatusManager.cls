<?

/**
 * Class CnsstatacctstatusManager
 */
class CnsstatacctstatusManager extends EntityManager
{
    /** @var CnsperiodManager $cnsperiodMgr */
    var $cnsperiodMgr;
    /** @var string $requestsMap */
    var $requestsMap;
    /** @var array $subsidiaryMap  */
    var $subsidiaryMap;
    /**
     * @var SubsidiaryManager $subsidiaryMgr
     */
    var $subsidiaryMgr;


    /**
     * @param array $params
     */
    function __construct($params = array())
    { 
        $gManagerFactory = Globals::$g->gManagerFactory;
        parent::__construct($params);
        $this->cnsperiodMgr = $gManagerFactory->getManager('cnsperiod');
        $this->subsidiaryMgr = $gManagerFactory->getManager('subsidiary');
    }


    /**
     * Custom Refresh action in the lister
     *
     * custom action of refreshing lister with new request data as well as adding new periods if required
     *
     * @access private
     *
     * @param string $_verb
     * @param array $_object
     * @param bool $clearcount
     * @return bool
     */
    function Submit($_verb, $_object, $clearcount = false) 
    {
        global $gErr;
        switch ($_verb) {
        case 'delete': 
            $ID = $this->GetKeyValue($_object);
            $ok = $this->Delete($ID);
            break;
        case 'refresh':                        
            if ($clearcount) {
                $this->_QM->DoQuery('QRY_CNSSTATACCTSTATUS_CLEARCOUNT', array($this->_cny), true);
            }

            //If refresh has been selected, then compare the cnsperiods and cnsstatacctstatus counts to determine if updates are needed
            $periodcount = $this->cnsperiodMgr->_QM->DoQuery('QRY_CNSPERIOD_SELECT_COUNT', array($this->_cny));
            $requestscount = $this->_QM->DoQuery('QRY_CNSSTATACCTSTATUS_SELECT_COUNT', array($this->_cny));
            

            //If updates to the list elements are needed, find the needed additions and/or subtractions and execute
            if ($periodcount[0] != $requestscount[0]) {
                $periodkeys = $this->cnsperiodMgr->_QM->DoQuery('QRY_CNSPERIOD_SELECT_KEYS', array($this->_cny), true);
                if (count($periodkeys)) {
                    $requestskeys = $this->_QM->DoQuery('QRY_CNSSTATACCTSTATUS_SELECT_KEYS', array($this->_cny), true);
                    foreach ($requestskeys as $requestkey) {
                        $requestMap[$requestkey['SUBSIDIARYKEY']][$requestkey['PERIODKEY']] = true;
                    }
                        
                    //Add the items that exist in the cnsperiod list, but not in the refreshlist
                    foreach ($periodkeys as $periodkey) {
                        if (!(isset($periodkey['SUBSIDIARYKEY']) && isset($periodkey['RECORD#'])
                              && isset($requestMap[$periodkey['SUBSIDIARYKEY']][$periodkey['RECORD#']]))) {
                            //ADD THIS CNSPERIOD INFO TO THE REQUESTS LIST
                            $values = array(
                            'RECORDNO' => $this->GetNextRecordKey(),    
                            'SUBSIDIARYKEY' => $periodkey['SUBSIDIARYKEY'], 
                            'PERIODKEY' => $periodkey['RECORD#'],
                            ':statistical' => 'T',
                            );
                            $source = "CnsstatacctstatusManager::Submit::Refresh::Add";
                            $this->_QM->beginTrx($source);
                            eppp($values);
                            $ok = $this->add($values);
                            $ok = $ok && $this->_QM->commitTrx($source);
                            if ( ! $ok ) {
                                //log an error that the refreshlist add failed
                                $gErr->addIAError(
                                    'CNS-0133', __FILE__ . ":" . __LINE__, '', [],
                                    "Failed to add subsidiary '" . $periodkey['SUBSIDIARYKEY'] . "' - period '"
                                    . $periodkey['RECORD#'] . "' to the Refresh Status List",
                                    [
                                        'SUBSIDIARY_KEY' => $periodkey['SUBSIDIARYKEY'],
                                        'PERIOD_KEY_RECORD' => $periodkey['RECORD#']
                                    ]
                                );
                                $this->_QM->rollbackTrx($source);
                                continue;
                            }

                            //UPDATE THE REQUESTMAP
                            $requestMap[$periodkey['SUBSIDIARYKEY']][$periodkey['RECORD#']] = true;
                        }
                    }
                }
            }
                
            $subsidiaryKey = Request::$r->_subsidiaryKey;

            if ($_object != 'api') {
                //Refresh the lister
                $url = CallUrl("lister.phtml?.op=" . GetOperationId('cs/lists/cnsstatacctstatus') . SessionParam('&'));
                $url .= "&.subsidiaryKey=$subsidiaryKey";                    
                Redirect($url);
                return true;
            }
            return true;
            default:
            /** @noinspection PhpUndefinedVariableInspection */
            dieFL("EntityManager::Submit: Don't know how to handle verb '$verb'");
            break;
        }

        /** @noinspection PhpUndefinedVariableInspection */
        return $ok;
    }


    /**
     * @param string $date
     * @param array  $info
     * @return string
     */
    function _ItoEdate($date, $info = [])
    {
        // <kludge>
        // remove the timestamp
        
        $arrExplode = explode(" ", $date);
        /** @noinspection PhpUnusedLocalVariableInspection */
        $justdate = array_shift($arrExplode);
        // </kludge>

        $userFmt = GetUserDateFormat();
        return ($this->_dbDateFormat == $userFmt) ? $date : ReformatDate($date, $this->_dbDateFormat, $userFmt);
    }


    /**
     * @param string $subsidiarykey
     * @param string $periodkey
     * @return bool
     */
    function GetRecordFromKeys($subsidiarykey, $periodkey)
    {
        global $gErr;
        $recordkey = $this->_QM->DoQuery('QRY_CNSSTATACCTSTATUS_SELECT_VID_FROM_KEYS', array($this->_cny, $subsidiarykey, $periodkey));
        if (!$recordkey[0]['RECORD#']) {
            $gErr->addIAError(
                'CNS-0134', __FILE__ . ":" . __LINE__, '', [],
                "Failed to find subsidiary '" . $subsidiarykey . "' - period '" . $periodkey
                . "' entry in the Refresh Status List",
                ['SUBSIDIARY_KEY' => $subsidiarykey, 'PERIOD_KEY' => $periodkey]
            );
            return false;
        }
        return $recordkey[0]['RECORD#'];
    }

}

