<?
import('ModuleSetupManager');

/**
 * Class CSSetupManager
 */
class CSSetupManager extends ModuleSetupManager
{

    /**
     * @param array $params
     */
    function __construct($params = array())
    {
        $params['moduleKey'] = Globals::$g->kCSid;
        parent::__construct($params);
    }

    /**
     * @param string        $id
     * @param string[]|null $fields
     * 
     * @return array|false
     */
    function get($id, $fields = null)
    {
        // $id is always pointing to vid, get doesn't return was if it is blank or null
        if (isNullOrBlank($id)) {
            $id = Globals::$g->kCSid;
        }
        $values = parent::get($id, $fields);
        return $values;
    }

    /**
     * Set CS setup
     *
     * @param array $values
     *
     * @return bool
     *
     */
    protected function regularSet(&$values)
    {
        $source = "CSSetupManager::Set";
        $ok = $this->_QM->beginTrx($source);
        
        $ok = $ok && $this->validateNTranslate($values);
        $ok = $ok && (new ProfileHelper())->invalidateCompanyCache(GetMyCompany());
        $ok = $ok && parent::regularSet($values);
        $ok = $ok && $this->_QM->commitTrx($source);
        
        if (!$ok) {
            $msg = "Could not update distributed consolidation console preferences!";
            Globals::$g->gErr->addError('CNS-0259', __FILE__ . ':' . __LINE__, $msg);
            epp("$source: Error: $msg");
            $this->_QM->rollbackTrx($source);
        }
        
        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        $source = "CSSetupManager::Add";
        $ok = $this->_QM->beginTrx($source);
        
        $ok = $ok && $this->validateNTranslate($values);
        $ok = $ok && (new ProfileHelper())->invalidateCompanyCache(GetMyCompany());
        $ok = $ok && parent::regularAdd($values);
        $ok = $ok && $this->_QM->commitTrx($source);

        if (!$ok) {
            $msg = "Could not insert distributed consolidation console preferences!";
            Globals::$g->gErr->addError('CNS-0260', __FILE__ . ':' . __LINE__, $msg);
            $this->_QM->rollbackTrx($source);
        }
        return $ok;
    }

    /**
     * Validate and translate CSSETUP values
     *
     * @param array &$values
     *
     * @return bool
     */
    protected function validateNTranslate(&$values)
    {
        $ok = true;
        $gManagerFactory = Globals::$g->gManagerFactory;
        
        //Validate LOCATIONID
        if ($values['LOCATIONID']) {
            $locationMgr = $gManagerFactory->getManager('location');
            $params = [
                'selects' => [ 'RECORDNO'],
                'filters' => [
                    [
                        [ 'LOCATIONID', '=', $values['LOCATIONID'] ]
                    ],
                ],
            ];
            $locationRecord = $locationMgr->GetList($params);
            if (!isset($locationRecord[0]['RECORDNO'])) {
                $msg = "Could not find specified location";
                Globals::$g->gErr->addError('CNS-0261', __FILE__ . ':' . __LINE__, $msg);
                $ok = false;
            }

            $ok = $ok && ($values['LOCATIONKEY'] = $locationRecord[0]['RECORDNO']);
        }

        $baseAcctMgr = $gManagerFactory->getManager('baseaccount');
        //Validate LIABACCTNO
        if ($values['LIABACCTNO']) {
            $params = [
                'selects' => [ 'RECORDNO'],
                'filters' => [
                    [
                        [ 'ACCT_NO', '=', $values['LIABACCTNO'] ]
                    ],
                ],
            ];
            $liabAcctRecord = $baseAcctMgr->GetList($params);
            
            if (!isset($liabAcctRecord[0]['RECORDNO'])) {
                $msg = "Could not find specified liability account";
                Globals::$g->gErr->addError('CNS-0262', __FILE__ . ':' . __LINE__, $msg);
                $ok = false;
            }

            $ok = $ok && ($values['LIABACCTKEY'] = $liabAcctRecord[0]['RECORDNO']);
        }

        //Validate EQUITYACCTNO
        if ($values['EQUITYACCTNO']) {
            $params = [
                'selects' => [ 'RECORDNO'],
                'filters' => [
                    [
                        [ 'ACCT_NO', '=', $values['EQUITYACCTNO'] ]
                    ],
                ],
            ];
            $equityAcctRecord = $baseAcctMgr->GetList($params);
            if (!isset($equityAcctRecord[0]['RECORDNO'])) {
                $msg = "Could not find specified Equity account";
                Globals::$g->gErr->addError('CNS-0263', __FILE__ . ':' . __LINE__, $msg);
                $ok = false;
            }

            $ok = $ok && ($values['EQUITYACCTKEY'] = $equityAcctRecord[0]['RECORDNO']);
        }

        // When subscribing newly to consolidation setup, modulekey won't be set (regularAdd)
        $values['MODULEKEY'] = Globals::$g->kCSid;
        return $ok;
    }

    /**
     * SetPreference
     *
     * @param string $property  modulepref property
     * @param string $value     modulepref value
     * @param bool   $predelete delete and re-insert
     * @param string $mod       module symbol
     * @param string $ins_updt  upsert
     * @param bool   $forceRoot if true will always write pref to the root rather than the current entity
     *
     * @return bool
     */
    public function SetPreference($property, $value, $predelete = true, $mod = '', $ins_updt = '', $forceRoot = true)
    {
        // Block SetPreference for cssetup
        $msg = "This operation is not allowed on Distributed Consolidation Setup.";
        $corr = "Please use newly published API or NextGen API of cssetup object.";
        Globals::$g->gErr->addError("CNS-0264", __FILE__ . ":" . __LINE__, $msg, '', $corr);
        return false;
    }

    /**
     *
     * @return bool
     */
    protected function isIAMessageQueueInsertionRequired() : bool
    {
        return false;
    }
}

