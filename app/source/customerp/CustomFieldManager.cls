<?php

import('NexusDBInfo');

require_once('xmlutil.inc');
require_once('CustomERP.inc');

define("CUSTOMERP_MAXCUSTOMFIELDS", 25);

//IF THE VALUE OF CUSTOMERP_MAXCUSTOMRELATIONSHIP IS CHANGE PLEASE CHANGE ALSO THE REGEX FROM Pt_DataField::getColumnName()
define("CUSTOMERP_MAXCUSTOMRELATIONSHIP", 100);
define("CUSTOMERP_COMBINED_MAXCUSTOMFIELDS", 100);
define("CUSTOMERP_RELATIONSHIPFIELDTYPE", 'LOOKUP');
define("CUSTOMERP_RELATIONSHIPFIELDTYPE_DIMENSION", 'UDDLOOKUP');
define("CUSTOMERP_RELATIONSHIPFIELD_PREFIX", 'REL');

//IF THE VALUE OF CUSTOMERP_MAXCUSTOMRELATIONSHIP_DIMENSION IS CHANGE PLEASE CHANGE ALSO THE REGEX FROM Pt_DataField::getColumnName()
define("CUSTOMERP_MAXCUSTOMRELATIONSHIP_DIMENSION", 20);
define("CUSTOMERP_RELATIONSHIPFIELD_PREFIX_DIMENSION", 'UDDREL');


/**
 * Class CustomFieldManager
 */
class CustomFieldManager extends CustomComponentManager implements ICustomComponentManager
{

    const OBJECTTYPE = 'CustomField';
    
    // may be completed and will not result in data loss, but in some cases, Intacct will display less of the data.
    /**
     * @var string[][] $kGreenConversions
     */
    public $kGreenConversions = array(
        'CHECKBOX' => array('CHECKBOX', 'PICKLIST', 'PICKLISTMULTI', 'SEQUENCE', 'TEXT', 'TEXTAREA', 'URL'),
        'CURRENCY' => array('CURRENCY', 'NUMBER', 'PERCENT', 'SEQUENCE', 'TEXT', 'TEXTAREA'),
        'DATE' => array('DATE', 'SEQUENCE', 'TEXT', 'TEXTAREA'),
        'EMAIL' => array('EMAIL', 'SEQUENCE', 'TEXT', 'TEXTAREA'),
        'NUMBER' => array('NUMBER', 'CURRENCY', 'PERCENT', 'SEQUENCE', 'TEXT', 'TEXTAREA'),
        'PERCENT' => array('PERCENT', 'CURRENCY', 'NUMBER', 'SEQUENCE', 'TEXT', 'TEXTAREA'),
        'PICKLIST' => array('PICKLIST', 'PICKLISTMULTI', 'SEQUENCE', 'TEXT', 'TEXTAREA'),
        'PICKLISTMULTI' => array('PICKLISTMULTI', 'TEXT', 'TEXTAREA'),
        'SEQUENCE' => array('SEQUENCE', 'TEXT', 'TEXTAREA'),
        'TEXT' => array('TEXT', 'TEXTAREA'),
        'TEXTAREA' => array('TEXTAREA', 'TEXT'),
        'URL' => array('URL', 'SEQUENCE', 'TEXT', 'TEXTAREA'),
    );
    // but Intacct must delete all existing field values
    /**
     * @var string[][] $kRedConversions
     */
    public $kRedConversions = array(
        'CHECKBOX' => array('CURRENCY', 'DATE', 'EMAIL', 'NUMBER', 'PERCENT', 'URL'),
        'CURRENCY' => array('CHECKBOX', 'DATE', 'EMAIL', 'PICKLIST', 'PICKLISTMULTI', 'URL'),
        'DATE' => array('CHECKBOX', 'CURRENCY', 'EMAIL', 'NUMBER', 'PERCENT', 'PICKLIST', 'PICKLISTMULTI', 'URL'),
        'EMAIL' => array('CHECKBOX', 'CURRENCY', 'DATE', 'NUMBER', 'PERCENT', 'PICKLIST', 'PICKLISTMULTI', 'URL'),
        'NUMBER' => array('CHECKBOX', 'DATE', 'EMAIL', 'PICKLIST', 'PICKLISTMULTI', 'URL'),
        'PERCENT' => array('CHECKBOX', 'DATE', 'EMAIL', 'PICKLIST', 'PICKLISTMULTI', 'URL'),
        'PICKLIST' => array('CHECKBOX', 'CURRENCY', 'DATE', 'EMAIL', 'NUMBER', 'PERCENT', 'URL'),
        'PICKLISTMULTI' => array('CHECKBOX', 'CURRENCY', 'DATE', 'EMAIL', 'NUMBER', 'PERCENT', 'PICKLIST', 'SEQUENCE',
            'URL'),
        'SEQUENCE' => array('CHECKBOX', 'CURRENCY', 'DATE', 'EMAIL', 'NUMBER', 'PERCENT', 'PICKLIST', 'PICKLISTMULTI',
            'URL'),
        'TEXT' => array('CHECKBOX', 'CURRENCY', 'DATE', 'EMAIL', 'NUMBER', 'PERCENT', 'PICKLIST', 'PICKLISTMULTI',
            'URL'),
        'TEXTAREA' => array('CHECKBOX', 'CURRENCY', 'DATE', 'EMAIL', 'NUMBER', 'PERCENT', 'PICKLIST', 'PICKLISTMULTI',
            'SEQUENCE', 'URL'),
        'URL' => array('CHECKBOX', 'CURRENCY', 'DATE', 'EMAIL', 'NUMBER', 'PERCENT', 'PICKLIST', 'PICKLISTMULTI'),
    );

    /**
     * @var int[] $kNoOfCustomFields to be allowed for item object(default is 25 for other objects)
     */
    public $kNoOfCustomFields = array(
        'item' => 100,
        'vendor' => 100,
        'customer' => 100,
        'project' => 100,
        'contract' => 100,
        'contractdetail' => 100,
        'contractexpense' => 100,
        'glaccount' => 100,
        'stataccount' => 100,
        'invdocumententry' => 100,
        'podocumententry' => 100,
        'sodocumententry' => 100,
        'invdocument' => 100,
        'podocument' => 100,
        'sodocument' => 100,
        'invrecurdocumententry' => 100,
        'porecurdocumententry' => 100,
        'sorecurdocumententry' => 100,
        'invrecurdocument' => 100,
        'porecurdocument' => 100,
        'sorecurdocument' => 100,
        'geninvoicepolicy' => 100,
        'geninvoiceprebillline' => 100,
        'geninvoicepreviewline' => 100,
        'geninvoiceprebill' => 100,
        'geninvoicepreview' => 100,
        'eexpenses' => 100,
        'eppayment' => 100,
        'expenseadjustments' => 100,
        'eexpensesitem' => 100,
        'eppaymentitem' => 100,
        'expenseadjustmentsitem' => 100,
        'creditcardfee' => 100,
        'deposit' => 100,
        'bankfee' => 100,
        'fundstransfer' => 100,
        'chargepayoff' => 100,
        'otherreceipts' => 100,
        'cctransaction' => 100,
        'creditcardfeeentry' => 100,
        'depositentry' => 100,
        'bankfeeentry' => 100,
        'fundstransferentry' => 100,
        'chargepayoffentry' => 100,
        'otherreceiptsentry' => 100,
        'cctransactionentry' => 100,
        'apbill' => 100,
        'apadjustment' => 100,
        'apadvance' => 100,
        'appymt' => 100,
        'appayment' => 100,
        'apbillitem' => 100,
        'apadjustmentitem' => 100,
        'apadvanceitem' => 100,
        'appymtentry' => 100,
        'appaymentitem' => 100,
        'arinvoice' => 100,
        'aradjustment' => 100,
        'aradvance' => 100,
        'arpayment' => 100,
        'arinvoiceitem' => 100,
        'aradjustmentitem' => 100,
        'arpaymentitem' => 100,
        'aradvanceitem' => 100,
        'location' => 100,
        'entity' => 100,
        'compliancerecord' => 100,
    );

    /**
     * @param array $params
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
        foreach ( CustomFieldConfigurator::getAllObjectsWithCustomFields() as $objects) {
            foreach ($objects as $object) {
                $this->kNoOfCustomFields[$object] = CUSTOMERP_COMBINED_MAXCUSTOMFIELDS;
            }
        }
    }


    /**
     * Adds a custom field.
     *
     * @param array $values Standard Entity Manager values structure
     *
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        //eppp($values);
        //dieFL();

        $ok = true;
        $ok = $ok && $this->PrepValues($values);
        if ( ! $ok ) {
            return false;
        }

        $source = "CustomFieldManager::Add";
        $ok = $this->beginTrx($source);

        $doctypes = explode('#~#', $values['DOCTYPE']);

        // PURGE ANY EXISTING VALUES IN THIS NEW COLUMN
        foreach ( $doctypes as $doctype ) {
            $em = Globals::$g->gManagerFactory->getManager($values['OWNEROBJECT'], false,
                                                           [ 'DOCTYPE' => $doctype ]);

            // MAKE SURE THE CUSTOMFIELDID DOES NOT MATCH ANY EXISTING FIELD.
            if ( $em->isObjectField($values['CUSTOMFIELDID']) ) {
                Globals::$g->gErr->addIAError('CERP-0053', __FILE__ . '.' . __LINE__, "", [],
                                            'The CUSTOMFIELDID ' . $values['CUSTOMFIELDID'] . ' is already in use', ["CUSTOMFIELDID" =>
                        $values['CUSTOMFIELDID']]);
                $this->rollbackTrx($source);

                return false;
            }
        }

        if ( $values['FIELDREPLICATE'] === "true" ) {
            $ownerObject = $values['OWNEROBJECT'];
            $origValues = $values;

            if ( CustomFieldConfigurator::IsSCMObject($ownerObject) ) {
                $nexusdb = new NexusDBInfo($ownerObject);
                $doctypes = $nexusdb->GetAllDocType($ownerObject);
                foreach ( $doctypes as $doctype ) {
                    $values = $origValues;
                    $values['DOCTYPE'] = $doctype;

                    $em = Globals::$g->gManagerFactory->getManager($values['OWNEROBJECT'], false,
                                                                   array('DOCTYPE' => $values['DOCTYPE']));
                    if ( ! $em->isObjectField($values['CUSTOMFIELDID']) ) {
                        $ok = $ok && $em->PurgeCustomFieldColumn($values['OBJECTFIELDID']);
                        $ok = $ok && $this->RawAdd($values);
                    }
                    if ( $values['DOCTYPE'] == $doctype ) {
                        $updValues = $values;
                    }
                }
            } else {
                $objects = CustomFieldConfigurator::getMapFromObjectName($ownerObject);
                $customObjectsList = NexusUIInfo::GetCustomObjectsList();
                foreach ( $objects as $object ) {
                    if ( in_array($object, $customObjectsList['validvalues']) ) {
                        $values = $origValues;
                        $values['OWNEROBJECT'] = $object;

                        $em = Globals::$g->gManagerFactory->getManager(
                            $values['OWNEROBJECT'], false, array('DOCTYPE' => $values['DOCTYPE'])
                        );
                        if ( ! $em->isObjectField($values['CUSTOMFIELDID']) ) {
                            $ok = $ok && $em->PurgeCustomFieldColumn($values['OBJECTFIELDID']);
                            $ok = $ok && $this->RawAdd($values);

                            if ( $values['OWNEROBJECT'] == $ownerObject ) {
                                $updValues = $values;
                            }
                        }
                    }
                }
            }

            /** @noinspection PhpUndefinedVariableInspection */
            $values = $updValues;
        } else {
            if ( count($doctypes) > 1) {
                foreach ( $doctypes as $doctype ) {
                    $em = Globals::$g->gManagerFactory->getManager($values['OWNEROBJECT'], false,
                                                                   [ 'DOCTYPE' => $doctype ]);

                    $ok = $ok && $em->PurgeCustomFieldColumn($values['OBJECTFIELDID']);
                }
            } else {
                $em = Globals::$g->gManagerFactory->getManager($values['OWNEROBJECT'], false,
                                                               [ 'DOCTYPE' => $values['DOCTYPE'] ]);
                $ok = $ok && $em->PurgeCustomFieldColumn($values['OBJECTFIELDID']);
            }
            $ok = $ok && $this->RawAdd($values);
        }

        $ok = $ok && $this->commitTrx($source);
        if ( $ok ) {
            InValidateSession(ProfileHandler::COMPANY_CACHE_FLAG, Session::getKey());
            Reporting::refreshCRWDataModel();
        } else {
            $this->rollbackTrx($source);
        }

        $this->refreshPtCache($values);

        return $ok;
    }

    /**
     * Adds a custom field but it does not purge the data.
     *
     * @param array $values Standard Entity Manager values structure
     *
     * @return bool
     */
    private function addNoPurge(&$values)
    {
        $ok = true;
        $ok = $ok && $this->PrepValues($values);
        if ( ! $ok ) {
            return false;
        }

        $source = "CustomFieldManager::Add";
        $ok = $this->beginTrx($source);

        $doctypes = explode('#~#', $values['DOCTYPE']);

        // PURGE ANY EXISTING VALUES IN THIS NEW COLUMN
        foreach ( $doctypes as $doctype ) {
            $em = Globals::$g->gManagerFactory->getManager($values['OWNEROBJECT'], false,
                                                           [ 'DOCTYPE' => $doctype ]);

            // MAKE SURE THE CUSTOMFIELDID DOES NOT MATCH ANY EXISTING FIELD.
            if ( $em->isObjectField($values['CUSTOMFIELDID']) ) {
                Globals::$g->gErr->addIAError('CERP-0053', __FILE__ . '.' . __LINE__, "", [],
                                            'The CUSTOMFIELDID ' . $values['CUSTOMFIELDID'] . ' is already in use', ["CUSTOMFIELDID" => $values['CUSTOMFIELDID']]);
                $this->rollbackTrx($source);

                return false;
            }
        }

        if ( $values['FIELDREPLICATE'] === "true" ) {
            $ownerObject = $values['OWNEROBJECT'];
            $origValues = $values;

            if ( CustomFieldConfigurator::IsSCMObject($ownerObject) ) {
                $nexusdb = new NexusDBInfo($ownerObject);
                $doctypes = $nexusdb->GetAllDocType($ownerObject);
                foreach ( $doctypes as $doctype ) {
                    $values = $origValues;
                    $values['DOCTYPE'] = $doctype;

                    $em = Globals::$g->gManagerFactory->getManager($values['OWNEROBJECT'], false,
                                                                   array('DOCTYPE' => $values['DOCTYPE']));
                    if ( ! $em->isObjectField($values['CUSTOMFIELDID']) ) {
                        $ok = $ok && $this->RawAdd($values);
                    }
                    if ( $values['DOCTYPE'] == $doctype ) {
                        $updValues = $values;
                    }
                }
            } else {
                $objects = CustomFieldConfigurator::getMapFromObjectName($ownerObject);
                $customObjectsList = NexusUIInfo::GetCustomObjectsList();
                foreach ( $objects as $object ) {
                    if ( in_array($object, $customObjectsList['validvalues']) ) {
                        $values = $origValues;
                        $values['OWNEROBJECT'] = $object;

                        $em = Globals::$g->gManagerFactory->getManager(
                            $values['OWNEROBJECT'], false, array('DOCTYPE' => $values['DOCTYPE'])
                        );
                        if ( ! $em->isObjectField($values['CUSTOMFIELDID']) ) {
                            $ok = $ok && $this->RawAdd($values);

                            if ( $values['OWNEROBJECT'] == $ownerObject ) {
                                $updValues = $values;
                            }
                        }
                    }
                }
            }

            /** @noinspection PhpUndefinedVariableInspection */
            $values = $updValues;
        } else {
            $ok = $ok && $this->RawAdd($values);
        }

        $ok = $ok && $this->commitTrx($source);
        if ( $ok ) {
            InValidateSession(ProfileHandler::COMPANY_CACHE_FLAG, Session::getKey());
            Reporting::refreshCRWDataModel();
        } else {
            $this->rollbackTrx($source);
        }

        $this->refreshPtCache($values);

        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    private function RawAdd(&$values)
    {
        $ok = true;

        // FIELD
        $ok = $ok && parent::regularAdd($values);

        // COMPONENT
        $component = array(
            'COMPONENTTYPE' => 'C',
            'PATH' => $values['CUSTOMFIELDID'],
            'LABEL' => $values['CUSTOMCOMPONENT']['LABEL'] ?? $values['LABEL'],
            'HIDDEN' => $values['CUSTOMCOMPONENT']['HIDDEN'],
            'CUSTOMFIELDKEY' => $values['RECORDNO'],
            'CUSTOMLABELID' => $values['CUSTOMCOMPONENT']['CUSTOMLABELID'] ?? $values['CUSTOMLABELID']
        );
        $compMgr = Globals::$g->gManagerFactory->getManager('customcomponent');
        $ok = $ok && $compMgr->add($component);

        // LAYOUT COMPONENT
        $layoutMgr = Globals::$g->gManagerFactory->getManager('customlayout');
        $layout = $layoutMgr->GetLayout($values['OWNEROBJECT']);
        if (!$layout['RECORD#']) {
            $layout = array(
                'CUSTOMLAYOUTID' => $values['OWNEROBJECT'],
                'OWNEROBJECT' => $values['OWNEROBJECT'],
            );
            $ok = $ok && $layoutMgr->add($layout);
            if ($ok) {
                $layout['RECORD#'] = $layout['RECORDNO'];
            }
        }
        $layoutvalues = array(
            'PAGE' => $values['CUSTOMCOMPONENT']['CUSTOMLAYOUTCOMPONENT']['PAGE'],
            'FIELDSET' => $values['CUSTOMCOMPONENT']['CUSTOMLAYOUTCOMPONENT']['FIELDSET'],
            'CUSTOMCOMPONENTKEY' => $component['RECORDNO'],
            'CUSTOMLAYOUTKEY' => $layout['RECORD#'],
        );
        $layoutcompMgr = Globals::$g->gManagerFactory->getManager('customlayoutcomponent');
        $ok = $ok && $layoutcompMgr->add($layoutvalues);

        // INVALIDATE ALL CURRENT SESSIONS OF THE COMPANY, SO THAT IT COULD BE REFRESHED
        $ok = $ok && InValidateSession(ProfileHandler::COMPANY_CACHE_FLAG, Session::getKey());

        return $ok;
    }

    /**
     * Sets a custom field.
     *
     * @param array $values Standard Entity Manager values structure
     *
     * @return bool
     */
    protected function regularSet(&$values)
    {
        $ok = true;

        $customLabels = $values['CUSTOMLABELS'];
        unset($values['CUSTOMLABELS']);

        // Find out if the custom field is linked to some other record
        $existingObj = $this->GetRaw($values['RECORDNO']);
        if (isset($existingObj[0]['PACKAGEKEY']) && ($existingObj[0]['PACKAGEKEY'] != $values['PACKAGEKEY'])) {
            Globals::$g->gErr->addError('CERP-0064', __FILE__ . '.' . __LINE__, '');

            return false;
        }

        $source = 'CustomFieldManager::regularSet()';

        //$combinedObjects = CustomFieldConfigurator::getMapFromObjectName($existingObj[0]['OWNEROBJECT']);
        if ( ($values['OWNEROBJECT'] != $existingObj[0]['OWNEROBJECT']) || (strtoupper($values['TYPE']) != $existingObj[0]['TYPE']) ) {
            Globals::$g->gErr->addError(
                'CERP-0054', __FILE__.'.'.__LINE__,
                'Changing the object or type is not allowed. Delete and recreate the custom field.'
            );

            return false;
        }
        if (($values['CUSTOMFIELDID'] != $existingObj[0]['CUSTOMFIELDID'])) {
            Globals::$g->gErr->addError(
                'CERP-0055', __FILE__.'.'.__LINE__,
                'Changing the Field ID is not allowed.'
                .' Delete and recreate the custom field.'
            );

            return false;
        }

        $values['OBJECTFIELDID'] = $existingObj[0]['OBJECTFIELDID']; //keep the same column; this is a bug fix

        //  Since the code does a 'delete' and 'add', we want to disable the audit trail temporarily.
        //   We'll add a 'modify' audit trail event afterwards.
        $auditTrailSession = AuditTrailSession::getInstance();
        $auditTrailSession->disableAuditTrail(true);

        $ok = $ok && $this->beginTrx($source);

        if ( $values['FIELDREPLICATE'] === "true" ) {
            $objects = CustomFieldConfigurator::getMapFromObjectName($values['OWNEROBJECT']);
            $conditionObjects = "^(" . implode("|", $objects) . ")$";

            $similarFields = $this->DoQuery('QRY_CUSTOMFIELD_COMBINED_SIMILAR_FIELDS',
                                            [ $conditionObjects, $values['CUSTOMFIELDID'] ]);
            $origValues = $values;
            $origRecNo = $values['RECORDNO'];
            foreach ( $similarFields as $field ) {
                $values = $origValues;
                $values['RECORDNO'] = $field['RECORD#'];
                $values['OWNEROBJECT'] = $field['OWNEROBJECT'];
                $values['DOCTYPE'] = $field['DOCTYPE'];

                //$ok = $ok && $this->DoConversion($values); we DO NOT delete values on set, we force the same type - PDLC-1472-17
                $ok = $ok && parent::Delete($values['RECORDNO']);
                // Force app to rebuild objects's managers, or else the managers will remain outdated.
                // When a custom field is deleted, the manager must purge it from the entity's 'object' data.
                // Otherwise, RawAdd will believe the custom field is still in use.
                // This is a problem especially for SCM objects, where there are different managers for each DOCTYPE.
                ManagerFactory::PurgeObjectInstances();
                Globals::$g->gManagerFactory->PurgeManagerInstances();
                $ok = $ok && $this->PrepValues($values);
                $ok = $ok && $this->RawAdd($values);

                if ( $values['RECORDNO'] == $origRecNo ) {
                    $updValues = $values;
                }
            }
            /** @noinspection PhpUndefinedVariableInspection */
            $values = $updValues;
        } else {
            //$ok = $ok && $this->DoConversion($values); we DO NOT delete values on set, we force the same type - PDLC-1472-17
            $ok = $ok && parent::Delete($values['RECORDNO']);
            // Force app to rebuild objects's managers, or else the managers will remain outdated.
            // When a custom field is deleted, the manager must purge it from the entity's 'object' data.
            // Otherwise, RawAdd will believe the custom field is still in use.
            // This is a problem especially for SCM objects, where there are different managers for each DOCTYPE.
            ManagerFactory::PurgeObjectInstances();
            Globals::$g->gManagerFactory->PurgeManagerInstances();
            $ok = $ok && $this->PrepValues($values);
            $ok = $ok && $this->RawAdd($values);
        }

        // ww: Use regular set for owned objects so upsert is managed.
        if (empty($values['CUSTOMLABELS']) && !empty($customLabels)) {
            $values['CUSTOMLABELS'] = $customLabels;
            $ok = $ok && $this->setOwnedObjects($values);
        }

        //  Reenable audit trail, add a modify event.
        $auditTrailSession->disableAuditTrail(false);
        $entity = $this->getAuditEntity();
        $objectKey = $values['RECORDNO'];
        $auditTrailSession->addAuditEvent($entity, $objectKey, AuditTrail::AUDITTRAIL_EVENT_UPDATE,
            new AuditTrailPayload($entity, $objectKey, $values));

        $ok = $ok && $this->commitTrx($source);
        if (!$ok) {
            InValidateSession(ProfileHandler::COMPANY_CACHE_FLAG, Session::getKey());
            $this->rollbackTrx($source);
        } else {
            InValidateSession(ProfileHandler::COMPANY_CACHE_FLAG, Session::getKey());
            Reporting::refreshCRWDataModel();
        }

        $this->refreshPtCache($values);

        return $ok;
    }

    /**
     * @param string $str
     *
     * @return array
     */
    private function extractValuesFromXML($str)
    {
        // the xml is in the form: <dataDescription><picklist><pickValues><pickValue>Value 1</pickValue><pickValue>Value 2</pickValue><pickValue>Value 3</pickValue></pickValues><defaultValue>Value 1</defaultValue></picklist></dataDescription>
        $xml = simplexml_load_string($str);
        if (!$xml || !$xml->picklist || !$xml->picklist->pickValues || !$xml->picklist->pickValues) {
            return array();
        }

        $values = array();
        foreach ($xml->picklist->pickValues->pickValue as $v) {
            $values[] = (string)$v;
        }

        return $values;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    public function DoConversion($values)
    {
        $ok = true;
        $currObj = $this->GetRaw($values['RECORDNO']);
        $currObj = $currObj[0];
        //eppp($currObj);
        //dieFL();

        $em = Globals::$g->gManagerFactory->getManager($values['OWNEROBJECT'], false, array('DOCTYPE' => $values['DOCTYPE']));
        $types = $this->kRedConversions[$currObj['TYPE']];
        if (in_array($values['TYPE'], $types)) {
            //eppp('purge required');
            if ($this->getWarningValidation() && $this->shouldWarn(null, $values, [__FILE__ . ":" . __LINE__])) {
                Globals::$g->gErr->AddWarning("Your changes will delete the values and there are records using them.".
                    " Those record will have the values erased. Hit Save again to proceed with the changes.");
                $ok = false;
            } else {
                $ok = $ok && $em->PurgeCustomFieldColumn($currObj['OBJECTFIELDID']);
            }
        } else {
            $isNewPL = $values['TYPE'] == 'PICKLIST';
            $isNewMultiPL = $values['TYPE'] == 'PICKLISTMULTI';
            $isOldPL = $currObj['TYPE'] == 'PICKLIST';
            $isOldMultiPL = $currObj['TYPE'] == 'PICKLISTMULTI';

            if (($isOldPL || $isOldMultiPL) && ($isNewPL || $isNewMultiPL)) {
                $new = $this->extractValuesFromXML($values['DATADESCRIPTION']);
                $old =
                    $this->extractValuesFromXML($currObj['DATA']);  // this is not DATADESCRIPTION field, is the column from DB
                $diff = array_diff($old, $new);
                if ($diff && count($diff) > 0) {
                    $cnt = $em->countForCustomFieldColumnValues($currObj['OBJECTFIELDID'], $diff);
                    if ($cnt) {
                        if ($this->getWarningValidation() && $this->shouldWarn(null, $values, [__FILE__ . ":" . __LINE__])) {
                            Globals::$g->gErr->AddWarning("Your changes are removing the values [".implode(', ', $diff)
                                ."] while there ".
                                ($cnt == 1 ? 'is 1 record' : "are $cnt records")." using these values.".
                                " Those record will have the values erased. Hit Save again to proceed with the changes.");
                            $ok = false;
                        } else {
                            $ok = $ok && $em->PurgeCustomFieldColumn($currObj['OBJECTFIELDID'], $diff);
                        }
                    }

                }

            }

        }

        //dieFL();
        return $ok;
    }

    /**
     * @inheritDoc
     */
    public function getExistingComponents(array $values) : array
    {
        $result = $this->DoQuery('QRY_CUSTOMFIELD_CHECKEXISTS',
                       array($values['OWNEROBJECT'], $values['CUSTOMFIELDID'], GetMyCompany()));
        
        return $result === false ? [] : $result;
    }
    
    /**
     * @inheritDoc
     */
    protected function addNewComponent(array &$values) : bool
    {
        return $this->addNoPurge($values);
    }
    
    /**
     * @param array $values
     * @param array $currentComponent
     *
     * @return array
     */
    protected function prepareValuesForUpdate(array $values, array $currentComponent = []) : array
    {
        $values['OBJECTFIELDID'] = $currentComponent['OBJECTFIELDID'] ?? null;
        if (empty($values['OBJECTFIELDID'])) {
            logToFileCritical(self::OBJECTTYPE . ' missing OBJECTFIELDID when upserting: ' . $values['CUSTOMFIELDID'] ?? '' . '.');
        }
        return $values;
    }
    
    /**
     * @return string
     */
    public function getComponentType() : string
    {
        return self::OBJECTTYPE;
    }
    
    /**
     * Get the max number of custom fields allowed for an object.
     *
     * @param string $ownerobject the object name
     *
     * @return int the max number of custom fields allowed
     */
    private function getMaxCustomFieldCount($ownerobject)
    {
        $customerp_maxcustomfields = $this->kNoOfCustomFields[$ownerobject] ?? CUSTOMERP_MAXCUSTOMFIELDS;

        return $customerp_maxcustomfields;
    }

    /**
     * Get the max number of custom fields allowed for an object.
     *
     * @return int the max number of custom fields allowed
     */
    private function getMaxRelationshipFieldCount()
    {
        return CUSTOMERP_MAXCUSTOMRELATIONSHIP;
    }

    /**
     * Get the max number of custom fields allowed for an object.
     *
     * @return int the max number of custom fields allowed
     */
    private function getMaxRelationshipDimensionFieldCount()
    {
        return CUSTOMERP_MAXCUSTOMRELATIONSHIP_DIMENSION;
    }

    /**
     * Returns an unused ObjectfieldId value usable for new custom fields
     *
     * @param array $values Standard Entity Manager values structure
     *
     * @return bool|string
     */
    private function GetUnusedObjectfieldId($values)
    {
        $combinedObjects = CustomFieldConfigurator::getMapFromObjectName($values['OWNEROBJECT']);

        if ( isset($values['TYPE'])
             && ( $values['TYPE'] == CUSTOMERP_RELATIONSHIPFIELDTYPE
                  || $values['TYPE'] == CUSTOMERP_RELATIONSHIPFIELDTYPE_DIMENSION ) ) {
            if ( !$combinedObjects ) {
                return $this->GetUnusedRelationshipsObjectfieldId($values);
            } else {
                return $this->GetUnusedCombinedRelationshipObjectFieldId($values, $combinedObjects);
            }
        } else if ( !$combinedObjects ) {
            return $this->GetUnusedRegularObjectfieldId($values);
        } else {
            return $this->GetUnusedCombinedObjectfieldId($values, $combinedObjects);
        }
    }

    /**
     * Returns an unused ObjectfieldId value usable for new custom fields. Handling of regular objects.
     *
     * @param array $values Standard Entity Manager values structure
     *
     * @return bool|string
     */
    private function GetUnusedRegularObjectfieldId($values)
    {
        $ownerobject = $values['OWNEROBJECT'];
        $ret = $this->DoQuery('QRY_CUSTOMFIELD_RESERVEDOBJECTFIELDIDS', array($ownerobject));

        $aryMap = array_fill(1, $this->getMaxCustomFieldCount($ownerobject), false);

        foreach ($ret as $row) {
            $aryMap[isl_substr($row['OBJECTFIELDID'], 9)] = true;
        }

        foreach ($aryMap as $k => $x) {
            if ($x === false) {
                return 'custfield'.$k;
            }
        }

        $maxCount = $this->getMaxCustomFieldCount($ownerobject);
        Globals::$g->gErr->addIAError('CERP-0056', __FILE__ . '.' . __LINE__, "", [],
            'You have reached the maximum custom fields limit('.$maxCount
            .') for the '.$ownerobject.' object', ["OWNEROBJECT" => $maxCount, "LIMIT" => $ownerobject]);

        return false;
    }

    /**
     * Returns an unused ObjectfieldId value usable for new custom fields. Handling of regular objects.
     *
     * @param array $values Standard Entity Manager values structure
     *
     * @return false|string
     */
    private function GetUnusedRelationshipsObjectfieldId($values)
    {
        $ownerobject = $values['OWNEROBJECT'];
        $conditionObjects = $values['CONDITIONOBJECT'] ?? $ownerobject;

        if ( $values['TYPE'] == CUSTOMERP_RELATIONSHIPFIELDTYPE ) {
            $maxRelationshipField = $this->getMaxRelationshipFieldCount();
            $customerRelationshipFieldPrefix = CUSTOMERP_RELATIONSHIPFIELD_PREFIX;
        } else {
            $maxRelationshipField = $this->getMaxRelationshipDimensionFieldCount();
            $customerRelationshipFieldPrefix = CUSTOMERP_RELATIONSHIPFIELD_PREFIX_DIMENSION;
        }

        $aryMap = array_fill(1, $maxRelationshipField, false);

        $ret = $this->DoQuery('QRY_CUSTOMFIELD_RELATIONSHIP_RESERVEDOBJECTFIELDIDS',
                              [ $conditionObjects, $values['TYPE'] ]);
        if ( $ret === false ) {
            return $customerRelationshipFieldPrefix . 1;
        }

        foreach ( $ret as $row ) {
            unset($aryMap[isl_substr($row['OBJECTFIELDID'], strlen($customerRelationshipFieldPrefix))]);
        }

        foreach ( $aryMap as $k => $x ) {
            return $customerRelationshipFieldPrefix . $k;
        }

        Globals::$g->gErr->addIAError('CERP-0057', __FILE__ . '.' . __LINE__, "", [],
                                    'You have reached the maximum custom relationship limit('
                                      . $maxRelationshipField
                                      . ') for the ' . $ownerobject . ' object', ["LIMIT" => $maxRelationshipField, "OWNEROBJECT" => $ownerobject]);

        return false;
    }

    /**
     * Returns an unused ObjectfieldId value usable for new custom fields. Handling of special objects.
     * Case 1: multiple objects combined into one reporting object. ARRECORD, APRECORD etc.
     * Case 2: documents (one object with multiple transactions combined into one reporting object).
     *
     * @param array $values          Standard Customfield values array
     * @param array $combinedObjects An array containing all the related objects of the current object.
     *
     * @return bool|string
     */
    private function GetUnusedCombinedObjectfieldId($values, $combinedObjects)
    {
        $ownerobject = $values['OWNEROBJECT'];
        $conditionObjects = "^(".implode("|", $combinedObjects).")$";

        $typeFields =
            $this->DoQuery('QRY_CUSTOMFIELD_COMBINED_CHECKEXISTS', [ $conditionObjects, $values['CUSTOMFIELDID'] ]);
        if ( is_array($typeFields) && count($typeFields) > 0 ) {
            $goodType = true;
            foreach ( $typeFields as $field ) {
                if ( ( strtoupper($values['TYPE']) != $field['TYPE'] ) ) {
                    $goodType = false;
                    break;
                }
            }

            if ( $goodType ) {
                $goodFieldId = true;
                $idFields =
                    $this->DoQuery('QRY_CUSTOMFIELD_CHECK_ID', [ $conditionObjects, $typeFields[0]['OBJECTFIELDID'] ]);
                if (is_array($typeFields)) {
                    foreach ( $idFields as $field ) {
                        if ( ( strtoupper($values['CUSTOMFIELDID']) != $field['CUSTOMFIELDID'] ) ) {
                            $goodFieldId = false;
                            break;
                        }
                    }
                }
            }

            /** @noinspection PhpUndefinedVariableInspection */
            if ( $goodType && $goodFieldId ) {
                return $typeFields[0]['OBJECTFIELDID'];
            } else if ( !$goodType ) {
                if ( IsDocTypeOwnerObject($ownerobject) ) {
                    $conflictingObject = $typeFields[0]['DOCTYPE'];
                } else {
                    $kNexusObjectInfo = [];
                    include( 'nexusall.inc' );
                    //CRWGLACCTGROUP only supported in ICRW
                    unset($kNexusObjectInfo["CRWGLACCTGROUP"]);
                    //GLBUDGETGLENTRY only supported in ICRW
                    unset($kNexusObjectInfo["GLBUDGETGLENTRY"]);
                    $conflictingObject = $kNexusObjectInfo[strtoupper($typeFields[0]['OWNEROBJECT'])]['printas'];
                }
                Globals::$g->gErr->addIAError('CERP-0058', __FILE__ . '.' . __LINE__, "", [],
                                'The CUSTOMFIELDID ' . $values['CUSTOMFIELDID']
                                . ' is already in use with a different type on ' . $conflictingObject
                                . '. Change the field ID or use the same type.', ["CUSTOMFIELDID" => $values['CUSTOMFIELDID'], "OBJECT" => $conflictingObject]);

                return false;
            }
        }

        $ret = $this->DoQuery('QRY_CUSTOMFIELD_COMBINED_RESERVEDOBJECTFIELDIDS', array($conditionObjects));

        $aryMap = array_fill(1, $this->getMaxCustomFieldCount($ownerobject), false);

        foreach ($ret as $row) {
            $aryMap[isl_substr($row['OBJECTFIELDID'], 9)] = true;
        }

        foreach ($aryMap as $k => $x) {
            if ($x === false) {
                return 'custfield'.$k;
            }
        }

        $maxCount = $this->getMaxCustomFieldCount($ownerobject);
        $objName = CustomFieldConfigurator::GetCombinedObjectName($ownerobject);
        Globals::$g->gErr->addIAError('CERP-0056', __FILE__ . '.' . __LINE__, "", [],
                        'You have reached the maximum custom fields limit(' . $maxCount
                        . ') for the ' . $objName . ' object', ["LIMIT" => $maxCount, "OWNEROBJECT" => $objName]);
        return false;
    }

    /**
     * Returns an unused ObjectRelationsFieldId value usable for new fields. Handling of special objects.
     *
     * @param array    $values          Standard Customfield values array
     * @param string[] $combinedObjects An array containing all the objects stored in the same table
     *
     * @return string|false
     */
    private function GetUnusedCombinedRelationshipObjectFieldId($values, $combinedObjects)
    {
        $ownerobject = $values['OWNEROBJECT'];
        $customFieldId = $values['CUSTOMFIELDID'];

        $description = util_arrayFromText($values['DESCRIPTION']);
        $relatedObjDefId = $description['relatedObjDefId'];
        $isMultiple1 = $description['isMultiple1'];
        $isMultiple2 = $description['isMultiple2'];
        $ownerObjectDefinition = Pt_DataObjectDefManager::getByName($ownerobject);
        $ownerObjDefId = $ownerObjectDefinition->getId();
        $conditionObjects = "^(".implode("|", $combinedObjects).")$";

        $fields =
            $this->DoQuery('QRY_CUSTOMFIELD_COMBINED_RELATIONSHIP',
                           [ $conditionObjects, $customFieldId, $values['TYPE'] ]);

        $lookapFields = $fields ?? [];

        foreach ( $combinedObjects as $object ) {
            if ( $object != $ownerobject ) {
                $combineObjectDefinition = Pt_DataObjectDefManager::getByName($object);

                if ( $ownerObjDefId < $relatedObjDefId && $combineObjectDefinition->getId() > $relatedObjDefId ) {
                    $isMultiple = $isMultiple2;
                    $isMultiple2 = $isMultiple1;
                    $isMultiple1 = $isMultiple;
                }

                $typeFields =
                    $this->DoQuery('QRY_CUSTOMFIELD_COMBINED_CHECKEXISTS_RELATIONSHIP',
                                   [ $conditionObjects, $customFieldId,
                                     $values['TYPE'],
                                     util_arrayToText([ 'relatedObjDefId' => $relatedObjDefId,
                                                        'isMultiple1'     => (int) $isMultiple1,
                                                        'isMultiple2'     => (int) $isMultiple2 ]),
                                    ]);

                if ( $typeFields === false ) {
                    continue;
                }

                foreach ( $typeFields as $field ) {
                    if ( ( strtoupper($customFieldId) != $field['CUSTOMFIELDID'] )
                         && !in_array($field['OBJECTFIELDID'], $lookapFields) ) {
                        return $field['OBJECTFIELDID'];
                    }
                }
            }
        }

        $values['CONDITIONOBJECT'] = $conditionObjects;
        return $this->GetUnusedRelationshipsObjectfieldId($values);
    }

    /**
     * Prepare the values structure for posting to DB.
     *
     * @param array $values Standard entitymanager values structure.
     *
     * @return bool
     */
    public function PrepValues(&$values)
    {
        // Custom field IDs cannot be valid intacct object names
        $customfieldid = $values['CUSTOMFIELDID'];

        $kNexusObjectInfo = array();
        include('nexusall.inc');
        //CRWGLACCTGROUP not supported CRW
        unset($kNexusObjectInfo["CRWGLACCTGROUP"]);
        unset($kNexusObjectInfo["GLBUDGETGLENTRY"]);
        $allobjects = array_keys($kNexusObjectInfo);
        //eppp_p($allobjects);
        //dieFL();
        $doctypes = explode('#~#',$values['DOCTYPE']);
        foreach ( $doctypes as $doctype ) {
            $qry = "select * from customfield cf
                    where cf.customfieldid = :1
                      and cf.ownerobject = :2
                      and cf.cny# = :3
                      and '#~#'||cf.doctype||'#~#' like '%#~#'||:4||'#~#%'";

            $qryStmt = [
                $qry,
                $customfieldid,
                $values['OWNEROBJECT'],
                $this->_cny,
                $doctype,
            ];
            $result = QueryResult($qryStmt);
            if ( $result ) {
                $foundDoctypes = explode('#~#', $result[0]['DOCTYPE']);
                $doctypeError = isset($result[0]['DOCTYPE'])
                    ? implode('\', \'', array_intersect($doctypes, $foundDoctypes))
                    : "";
                Globals::$g->gErr->addIAError('CERP-0059', __FILE__ . '.' . __LINE__, "", [],
                                            "A custom field with id '$customfieldid' for '" . $values['OWNEROBJECT'] . "' with doctype(s): '"
                                            . $doctypeError . "' already exists.", ["CUSTOMFIELDID" => $customfieldid, "OWNEROBJECT" => $values['OWNEROBJECT'],
                        "DOCTYPE" => $doctypeError]);
                return false;
            }
        }

        if (in_array($customfieldid, $allobjects)) {
            Globals::$g->gErr->addIAError('CERP-0060', __FILE__ . '.' . __LINE__, "", [],
                                        "'$customfieldid' is invalid. Intacct object names cannot be used here. Please refer to Catalog for complete list of Intacct object names.", ["CUSTOMFIELDID" => $customfieldid]);

            return false;
        }

        if (IsDocTypeOwnerObject($values['OWNEROBJECT'])
            && (!isset($values['DOCTYPE']) || $values['DOCTYPE'] == '')) {
            Globals::$g->gErr->addIAError('CERP-0046', __FILE__ . '.' . __LINE__, "", [],
                'Document Type must be provided for \''.$values['OWNEROBJECT'].'\' object', ["OWNEROBJECT" => $values['OWNEROBJECT']]);

            return false;
        }

        // For Non-SCM Owner Objects Doctype is owner object value
        if (!IsDocTypeOwnerObject($values['OWNEROBJECT'])) {
            $values['DOCTYPE'] = '';
        }

        // map the owner object, if necessary
        epp("ownerobjectmap:");
        eppp($this->kOwnerobjectMap);
        if (array_key_exists($values['OWNEROBJECT'], $this->kOwnerobjectMap)) {
            $values['OWNEROBJECT'] = $this->kOwnerobjectMap[$values['OWNEROBJECT']];
        }

        // GET THE OBJECTFIELDID.
        if (!isset($values['OBJECTFIELDID']) || $values['OBJECTFIELDID'] == '') {
            if (($values['OBJECTFIELDID'] = $this->GetUnusedObjectfieldId($values)) === false) {
                return false;
            }
        }
        
        if (empty($values['CUSTOMLABELID'])) {
            $values['CUSTOMLABELID'] = CustomComponentManager::getUniqueID();
            $values['CUSTOMLABELS'][] = [
                'ID' => $values['CUSTOMLABELID'],
                'FIELDID' =>  $values['CUSTOMFIELDID'],
                'LABEL' => $values['CUSTOMCOMPONENT']['LABEL'] ?? $values['CUSTOMCOMPONENT.LABEL'],
                'LOCALE' => getCompanyLocale()
            ];
        }
        $values['TYPE'] = isl_strtoupper($values['TYPE']);
        if (in_array(isl_strtoupper($values['TYPE']), array('NUMBER', 'PERCENT', 'CURRENCY'))) {

            if (!isset($values['DATADESCRIPTION']) || $values['DATADESCRIPTION'] === '') {
                Globals::$g->gErr->addError('CERP-0062', __FILE__ . '.' . __LINE__, 'DATADESCRIPTION element is missing');

                return false;
            }

            $domXml = loadXmlDom($values['DATADESCRIPTION'],
                DOMXML_LOAD_PARSING + //0
                DOMXML_LOAD_COMPLETE_ATTRS + //8
                DOMXML_LOAD_SUBSTITUTE_ENTITIES + //4
                DOMXML_LOAD_DONT_KEEP_BLANKS //16
                , $error);
            if (count($error) > 0) {
                eppp($error);
            }

            $xpath = new DOMXPath($domXml);

            $leftOfDecimal =
                $this->getElementText($xpath, "//dataDescription/".isl_strtolower($values['TYPE'])."/leftOfDecimal");
            $rightOfDecimal =
                $this->getElementText($xpath, "//dataDescription/".isl_strtolower($values['TYPE'])."/rightOfDecimal");

            $totalDecimalPlaces = 0;
            if (is_numeric($leftOfDecimal)) {
                $totalDecimalPlaces += $leftOfDecimal;
            }
            if (is_numeric($rightOfDecimal)) {
                $totalDecimalPlaces += $rightOfDecimal;
            }

            if ($totalDecimalPlaces > 18) {
                Globals::$g->gErr->addError('CERP-0063', __FILE__ . '.' . __LINE__, '');

                return false;
            }
        }

        if (isset($values['DATADESCRIPTION'])) {
            $values['DATADESCRIPTION'] = databaseStringCompress($values['DATADESCRIPTION']);
        }

        return true;
    }

    /**
     * @param string $ownerobject
     *
     * @return array
     */
    public static function getDetails($ownerobject)
    {
        $qry = "select * from customfield where ownerobject = :1 and cny#=:2";
        $res = QueryResult(array($qry, $ownerobject, GetMyCompany()));
        $byID = array();
        foreach ($res as $r) {
            if (isset($r['DATADESCRIPTION'])) {
                $r['DATADESCRIPTION'] = databaseStringUncompress($r['DATADESCRIPTION']);
            }
            $byID[$r['CUSTOMFIELDID']] = $r;
        }

        return $byID;
    }

    /**
     * @param DOMXPath $xpathptr
     * @param string   $xpatheval
     *
     * @return bool
     */
    public function getElementText($xpathptr, $xpatheval)
    {
        $nodeset = $xpathptr->evaluate($xpatheval);
        if (!is_object($nodeset) || $nodeset->length == 0) {
            return false;
        }
        $node = $nodeset->item(0);

        return $node->nodeValue;
    }

    /**
     * @param array  $usedrecs
     * @param string $packagekey
     *
     * @return bool
     */
    public function deleteUnused($usedrecs, $packagekey)
    {
        if ((count($usedrecs) == 0) || ($packagekey == '')) {
            return true;
        }

        $recnotin = join(",", $usedrecs);
        XACT_BEGIN("CUSTOMFIELD::DELETEUNUSED");

        $qry = "delete from customfield where cny# = :1 and packagekey = :2 and record# not in ($recnotin)";
        $ok = ExecStmt(array($qry, GetMyCompany(), $packagekey));

        if ($ok) {
            XACT_COMMIT("CUSTOMFIELD::DELETEUNUSED");
        } else {
            XACT_ABORT("CUSTOMFIELD::DELETEUNUSED");
        }

        return $ok;
    }

    /**
     * Brings to display Valid Labels in View & Lister Mode
     */
    public function setDynaValidLabelsToValidValues()
    {
        $dynaFieldArr = array('OWNEROBJECT', 'TYPE');

        foreach ( $dynaFieldArr as $dynaField) {
            $fieldInfo = $this->GetFieldInfo($dynaField);
            $fieldInfo['type']['_validivalues'] = $fieldInfo['type']['validvalues'];
            $fieldInfo['type']['validvalues'] = $fieldInfo['type']['validlabels'];
            $this->SetFieldInfo($fieldInfo);
        }
    }

    /**
     * Custom Field ID and Owner object identify unique custom field record
     *
     * @param string $ownerobject
     * @param string $customfieldID
     *
     * @return array|false
     */
    public function getByID($ownerobject, $customfieldID)
    {
        $res = $this->DoQuery('QRY_CUSTOMFIELD_CHECKEXISTS', array($ownerobject, $customfieldID));
        $res = $res[0];
        $recno = $res['RECORD#'];
        //eppp_p($recno);

        $obj = $this->get($recno);

        //eppp_p($obj);

        return $obj;
    }

    /**
     * Delete the object.
     *
     * @param int|string $ID
     *
     * @return bool
     * @throws Exception
     */
    public function Delete($ID) : bool
    {
        $ok = true;
        $currObj = $this->GetRaw($ID);
        $currObj = $currObj[0];
        $em = Globals::$g->gManagerFactory->getManager($currObj['OWNEROBJECT'], false, array('DOCTYPE' => $currObj['DOCTYPE']));
        if ( $em instanceof LocationManager ) {
            $ok = $ok && $em->PurgeCustomFieldColumn($currObj['OBJECTFIELDID'], null, EntityManager::DELETEACTION);
        } else {
            $ok = $ok && $em->PurgeCustomFieldColumn($currObj['OBJECTFIELDID']);
        }
     
        $customLabels = $this->get($ID, ['CUSTOMLABELID']);
        if (!empty($customLabels['CUSTOMLABELS'])) {
            /** @var CustomLabelManager $customLabelManager */
            $customLabelManager = Globals::$g->gManagerFactory->getManager('customlabel');
            foreach ($customLabels['CUSTOMLABELS'] as $customLabel) {
                if (isset($customLabel['RECORDNO'])) {
                    if (!$customLabelManager->Delete($customLabel['RECORDNO'])) {
                        Globals::$g->gErr->addIAError(
                            'CERP-0092',
                            __FILE__ . ':' . __LINE__,
                            'Unable to delete custom labels for the custom field: ' . $currObj['CUSTOMFIELDID'],
                            ['CUSTOM_FIELD_ID' => $currObj['CUSTOMFIELDID']]
                        );
                        return false;
                    }
                }
            }
        }

        $ok = $ok && parent::Delete($ID);
        if ( $ok ) {
            Pt_AppLinkManager::deleteByRecord(self::OBJECTTYPE, (int) $ID);
        }

        if ( $ok ) {
            Reporting::refreshCRWDataModel();
        }
        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function afterDelete(&$values)
    {
        $this->refreshPtCache($values);

        return true;
    }

    /**
     *
     */
    public function MergeFieldInfo()
    {
        parent::MergeFieldInfo();

        $fieldInfo = &$this->GetFieldInfo('OWNEROBJECT');

        //$uinfo = new NexusUIInfo($this->getEntity());
        $customObjectsList = NexusUIInfo::GetCustomObjectsList();

        $fieldInfo['type']['validvalues'] = $customObjectsList['validvalues'];
        $fieldInfo['type']['validlabels'] = $customObjectsList['validlabels'];

        //eppp_p($customObjectsList);
        //dieFL();
    }

    /**
     * @param array $values
     */
    private function refreshPtCache($values)
    {
        if ( is_array($values[0]) ) {
            $toProcess = $values[0];
        } else {
            $toProcess = $values;
        }
        if ( isset($toProcess['DOCTYPE']) && $toProcess['DOCTYPE'] !== '' ) {
            $doctypes = explode('#~#', $toProcess['DOCTYPE']);
            foreach ( $doctypes as $doctype ) {
                 $objDefId = Util_StandardObjectMap::getObjectId(isl_strtolower($toProcess['OWNEROBJECT']), $doctype);
                if ( $objDefId > 0 ) {
                    Pt_Cache::updateStandardObjectDef($objDefId, $toProcess['OWNEROBJECT'], $doctype);
                }
            }
        } else {
            $objDefId = Util_StandardObjectMap::getObjectId(isl_strtolower($toProcess['OWNEROBJECT']));
            if ( $objDefId > 0 ) {
                Pt_Cache::removeObjDef($objDefId);
            }
        }
    }

    /**
     * @param array|int|string $_id
     *
     * @return bool|string[][]
     */
    public function GetRaw($_id)
    {
        $raw = parent::GetRaw($_id);
        if(isset($raw[0]['DATA'])) {
            $raw[0]['DATA'] = databaseStringUncompress($raw[0]['DATA']);
        }
        return $raw;
    }

    /**
     * @param string        $ID
     * @param string[]|null $fields
     *
     * @return array|false
     */
    public function get($ID, $fields = null)
    {
        $raw = parent::get($ID, $fields);
        if(isset($raw['DATADESCRIPTION'])){
            $raw['DATADESCRIPTION'] = databaseStringUncompress($raw['DATADESCRIPTION']);
        }

        return $raw;
    }

    /**
     * @param array $params
     * @param bool  $_crosscny
     * @param bool  $nocount
     *
     * @return array[]
     */
    function GetList($params = [], $_crosscny = false, $nocount = true)
    {
        $rows = parent::GetList($params, $_crosscny, $nocount);
        foreach ($rows as $key => $row) {
            if (isset($row['DATADESCRIPTION'])) {
                $rows[$key]['DATADESCRIPTION'] = databaseStringUncompress($row['DATADESCRIPTION']);
            }
        }
        return $rows;
    }

    /**
     * @param array $values Standard addLookup Customfield values array
     *
     * @return string|false
     */
    public function addLookup($values)
    {
        $objectFieldId = $this->GetUnusedObjectfieldId($values);

        if ( $objectFieldId === false ) {
            return false;
        }
        $code = 'QRY_' . strtoupper($this->_entity) . '_INSERT';
        $params[1] = $values['RECORDNO'];
        $params[2] = $values['CUSTOMFIELDID'];
        $params[3] = $values['TYPE'];
        $params[4] = $values['OWNEROBJECT'];
        $params[5] = $values['DOCTYPE'];
        $params[6] = $objectFieldId;
        $params[7] = '';
        $params[8] = '';
        $params[9] = '';
        $params[10] = 'T';
        $params[11] = $values['DESCRIPTION'];
        $params[12] = GetCurrentTimestamp();
        $params[13] = GetCurrentTimestamp();
        $params[14] = GetMyUserid();
        $params[15] = GetMyUserid();
        $params[16] = GetMyCompany();

        $data = $this->DoQuery($code, $params);
        if ( $data ) {
            return $objectFieldId;
        }

        return false;
    }

    /**
     * @return string
     */
    public static function getObjectType() : string
    {
        return self::OBJECTTYPE;
    }
    
    /**
     * @param $values
     *
     * @return bool
     */
    function addOwnedObjects(&$values): bool
    {
        return EntityManager::addOwnedObjects($values);
    }

}
