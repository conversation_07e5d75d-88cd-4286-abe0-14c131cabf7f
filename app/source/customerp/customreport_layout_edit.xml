<?xml version='1.0' encoding='UTF-8'?>
<ROOT assoc="T" >
    <entity>customreport</entity>
    <title>IA.CUSTOM_REPORT_WIZARD</title>
    <pages>
        <page assoc="T" >
			<helpfile>Choose_the_data_type_for_your</helpfile>
            <title>IA.SELECT_DATA_SOURCE</title>
            <path>ROOTOBJECTPAGE</path>
			<instruction>IA.SELECT_A_PRIMARY_DATA_SOURCE</instruction>
            <fields>
                <field>ROOT</field>
                <field>REPORTAUDIENCE</field>
                <field>REPORTTYPE</field>
                <field>DOCTYPE</field>
				<field>PACKAGEKEY</field>
            </fields>
        </page>

        <page assoc="T" >
			<helpfile>Select_the_columns_to_be_included</helpfile>
            <title>IA.ADD_COLUMNS</title>
            <path>COLUMNSPAGE</path>
			<instruction>IA.ADD_COLUMNS_TO_THE_REPORT</instruction>
            <fields>
                <field>FIELDS</field>
                <field>TEMPRPTNAME</field>
                <field>QUERYDEF</field>
				<field>USERTEMP</field>
                <field>SELECTEDPERIOD</field>
                <field>SELECTEDASOFDATE</field>
				<field>SELECTEDSTARTDATE</field>
				<field>SELECTEDENDDATE</field>
                <field>SELECTEDLOCATION</field>
                <field>APPLYLOCATIONRESTRICTION</field>
                <field>SELECTEDDEPARTMENT</field>
				<field>SELECTEDFILTERTRANSACTIONS</field>
				<field>SELECTEDBOOKID</field>
                <field>SELECTEDADJUSTMENTBOOK</field>
                <field>SELECTEDINCLUDEREPBOOK</field>
                <field>SELECTEDBASEDON</field>
                <field>SELECTEDCURRBASEDON</field>
                <field>SELECTEDTXNCURRENCY</field>
                <field>SELECTEDEXCHRATETYPE</field>
                <field>SELECTEDREPORTINGCURRENCY</field>
                <field>SELECTEDREVALUATIONTYPE</field>
				<field>SELECTEDSHOW_DETAILS</field>
				<field>TITLE1</field>
				<field>TITLE2</field>
				<field>TITLECOMMENT</field>
            </fields>
        </page>
        <!-- Add a page tag for calculated column page  -->
        <page assoc="T" >
			<helpfile>Adding_or_removing_a_calculated_column</helpfile>
            <title>IA.ADD_CALCULATED_COLUMNS</title>
            <path>CALCULATEDCOLUMNSPAGE</path>
			<instruction>IA.ADD_CALCULATED_COLUMNS</instruction>
            <fields>
                <field>CALCULATEDCOLUMNNAME</field>
                <field>CALCULATEDCOLUMNDATATYPE</field>
                <field>EXPRESSION</field>
                <field>CALCULATEDCOLUMNJSON</field>
            </fields>
        </page>
        <page assoc="T" >
            <helpfile>Select_column_sequence</helpfile>
            <title>IA.SELECT_COLUMN_SEQUENCE</title>
            <path>COLUMNSEQPAGE</path>
            <instruction>IA.SELECT_THE_COLUMN_SEQUENCE</instruction>
            <fields>
                <field>COLUMNSEQ</field>
            </fields>
        </page>
        <page assoc="T" >
            <helpfile>Select_Summary_Information</helpfile>
            <title>IA.ADD_MORE_TOTALS</title>
            <path>SUMMARYPAGE</path>
            <instruction>IA.ADD_MORE_COLUMN_TOTALS</instruction>
            <fields>
                <field>SUMMARY</field>
            </fields>
        </page>

        <page assoc="T" >
			<helpfile>Order_your_report_columns</helpfile>
            <title>IA.SORT_COLUMNS</title>
            <path>SORTORDERPAGE</path>
			<instruction>IA.SORT_COLUMNS_DRAG_AND_DROP_TO_SET_PRIORITY</instruction>
            <fields>
                <field>SORTBY</field>
            </fields>
        </page>
        <page assoc="T" >
			<helpfile>Select_filters</helpfile>
            <title>IA.FILTER_REPORT</title>
            <path>FILTERSPAGE</path>
			<instruction>IA.FILTER_YOUR_REPORT</instruction>
            <fields>
                <field>SHOWFILTERSINREPORTOUTPUT</field>
                <field>FILTERS</field>
            </fields>
        </page>
        <page assoc="T" >
			<helpfile>Select_advanced_filter</helpfile>
            <title>IA.REFINE_FILTERS</title>
            <path>ADVFILTERPAGE</path>
			<instruction>IA.REFINE_FILTERS_TO_GET_THE_RESULTS_YOU_WANT</instruction>
            <fields>
                <field>ADVANCEDFILTER</field>
            </fields>
        </page>
        <page assoc="T" >
            <helpfile>Choose_your_report_format</helpfile>
            <title>IA.ENABLE_GROUPING</title>
            <path>REPORTTYPEPAGE</path>
            <instruction>IA.OPTIONALLY_GROUP_DATA</instruction>
            <fields>
                <field>TYPE</field>
            </fields>
        </page>
        <page assoc="T" >
			<helpfile>Select_group_by_column</helpfile>
            <title>IA.GROUP_DATA</title>
            <path>GROUPBYPAGE</path>
			<instruction>IA.GROUP_DATA_BY_A_COLUMN_MAX_3_GROUPINGS</instruction>
            <fields>
                <field>SUBTOTALS</field>
            </fields>
        </page>

        <!-- page assoc="T" >
			<helpfile>CR110</helpfile>
            <title>IA.SELECT_SUMMARY_SEQUENCE</title>
            <path>SUMMARYSEQPAGE</path>
			<instruction>Select summary sequence</instruction>
            <fields>
                <field>SUMMARYSEQ</field>
            </fields>
        </page-->
        <page assoc="T" >
			<helpfile>Select_group_by_sequence</helpfile>
            <title>IA.ORDER_GROUPS</title>
            <path>GROUPBYSEQPAGE</path>
			<instruction>IA.ORDER_THE_GROUPS</instruction>
            <fields>
                <field>SUBTOTALSSEQ</field>
                <field>HIERARCHY</field>
            </fields>
        </page>
        <page assoc="T" >
            <helpfile>Run_report</helpfile>
            <title>IA.SET_REPORT_DATE_AND_TITLE</title>
            <path>RUNREPORTPAGE</path>
            <instruction>IA.SET_THE_DEFAULT_DATE_AND_ADD_A_TITLE_FOOTER</instruction>
            <detail></detail>
            <fields>
                <field>RUN</field>
            </fields>
        </page>
        <page assoc="T" >
			<helpfile>Select_the_type_of_chart</helpfile>
            <title>IA.DEFINE_CHART_OUTPUT</title>
            <path>GRAPHPAGE</path>
			<instruction>IA.DEFINE_OUTPUT_FOR_CHART_DISPLAY</instruction>
            <fields>
                <field>GRAPHTYPE</field>
				<field>YAXIS</field>
				<field>XAXIS</field>
				<field>GRAPHGROUPING</field>
				<field>XORIENT</field>
				<field>LEGEND</field>
				<field>FONTSIZE</field>
				<field>GRAPHSIZE</field>
				<field>WIDTH</field>
				<field>HEIGHT</field>
            </fields>
        </page>

        <page assoc="T" >
			<helpfile>Select_runtime_parameter_fields</helpfile>
            <title>IA.INCLUDE_ADDITIONAL_PROMPTS</title>
            <path>PARAMETERSPAGE</path>
			<instruction>IA.INCLUDE_ADDITIONAL_PROMPTS_WHEN_THE_REPORT_IS_RUN</instruction>
            <fields>
                <field>PARAMETERS</field>
            </fields>
        </page>
        <page assoc="T" >
			<helpfile>Select_Runtime_Parameter_Properties</helpfile>
            <title>IA.LABEL_AND_ORGANIZE_PROMPTS</title>
            <path>PARAMETERSEQPAGE</path>
			<instruction>IA.LABEL_AND_ORGANIZE_PROMPTS</instruction>
            <fields>
                <field>PARAMETERSEQ</field>
            </fields>
        </page>
        <page assoc="T" >
			<helpfile>Describe_your_report</helpfile>
            <title>IA.SAVE_AND_ADD_TO_MENU</title>
            <path>SAVEPAGE</path>
			<instruction>IA.SAVE_AND_ADD_TO_A_MENU</instruction>
			<detail></detail>
            <fields>
                <field>NAME</field>
                <field>DEPLOY</field>
                <field>MODULEKEY</field>
                <field>DESCRIPTION</field>
                <field>STATUS</field>
				<field>CANGRAPH</field>
            </fields>
        </page>
    </pages>
</ROOT>
