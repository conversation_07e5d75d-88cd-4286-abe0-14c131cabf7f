<?php

/**
 * File description
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Intacct Corporation, All Rights Reserved
 */

require 'smarteventjobqueueui.ent';

$kSchemas['cssmarteventqueueui'] = EntityManager::inheritEnts(
    $kSchemas['smarteventjobqueueui'],
    [
        'children' => [
            'company' => [
                'fkey' => 'cny#',
                'invfkey' => 'cny#',
                'table' => 'schemamap',
            ],
        ],
        'object' => [
            IMSPackageDetailManager::IMSPD_DB,
            IMSPackageDetailManager::IMSPD_CONTEXT,
            IMSPackageDetailManager::IMSPD_CNY,
        ],
        'schema' => [
            IMSPackageDetailManager::IMSPD_CONTEXT => 'company.title',
            IMSPackageDetailManager::IMSPD_CNY => 'company.cny#',
            IMSPackageDetailManager::IMSPD_DB => 'company.databaseid',
        ],
        'fieldinfo' => [
            [
                'path' => IMSPackageDetailManager::IMSPD_DB,
                'fullname' => 'IA.DB',
                'readonly' => true,
                'desc' => 'IA.TENANT_DB',
                'type' => [
                    'ptype' => 'integer',
                    'type' => 'integer',
                ],
            ],
            [
                'path' => IMSPackageDetailManager::IMSPD_CONTEXT,
                'fullname' => 'IA.CONTEXT',
                'readonly' => true,
                'hidden' => false,
                'desc' => 'IA.CONTEXT',
                'type' => [
                    'ptype' => 'text',
                    'type' => 'text',
                    'maxlength' => 80
                ]
            ],
            [
                'path' => IMSPackageDetailManager::IMSPD_CNY,
                'fullname' => 'IA.CNYKEY',
                'readonly' => true,
                'hidden' => false,
                'desc' => 'IA.COMPANY_ID',
                'type' => [
                    'ptype' => 'integer',
                    'type' => 'integer',
                    'maxlength' => 15
                ]
            ],
        ],
        'nosysview' => false,

        'global' => true,
        'joinCNY' => true,
        'readonly' => true,
        'url' => [
            'no_short_url' => true,
        ],
    ]
);
