<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XML Spy v4.0 U (http://www.xmlspy.com) by <PERSON><PERSON> (Intacct Corporation) -->
<customErpPackage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://dev01.intacct.com/users/aharris/cf3/customerp/CustomERPPackage.xsd">
	<packageDescription>
		<name>IA.VENDOR_INSURANCE</name>
		<description>Enables Vendor Insurance features, including insurance custom fields, smart rules to validate insurance rules, and the Vendor Insurance report.</description>
		<author>Intacct</author>
		<intacctPackageId>REALPAGE_VENDORINSURANCE</intacctPackageId>
	</packageDescription>
	<featureKeys>
		<featureKey>Vendor Insurance Report</featureKey>
	</featureKeys>
	<customFields>
		<customField>
			<customFieldId>INSURANCEREQUIRED</customFieldId>
			<type>checkbox</type>
			<ownerObject>vendor</ownerObject>
			<label>Insurance Required</label>
			<page>Insurance/License</page>
			<dataDescription>
				<checkbox>
					<checked>false</checked>
				</checkbox>
			</dataDescription>
			<description>This will denote if insurance or license is required for this vendor.</description>
		</customField>
		<customField>
			<customFieldId>VENDORINSURANCETYPE1</customFieldId>
			<type>picklist</type>
			<ownerObject>vendor</ownerObject>
			<label>Vendor Insurance Type</label>
			<fieldSet>Insurance 1</fieldSet>
			<page>Insurance/License</page>
			<dataDescription>
				<picklist>
					<pickValues>
						<pickValue>Workers Compensation</pickValue>
						<pickValue>Liability Insurance</pickValue>
						<pickValue>Contractor&apos;s License</pickValue>
						<pickValue>Auto Insurance</pickValue>
						<pickValue>Commercial</pickValue>
						<pickValue>1099</pickValue>
						<pickValue>Federal</pickValue>
						<pickValue>Casualty</pickValue>
						<pickValue>Exempt</pickValue>
						<pickValue>500,000,000 liability</pickValue>
						<pickValue>Tax ID</pickValue>
						<pickValue>General aggregate</pickValue>
						<pickValue>Umbrella Liability</pickValue>
						<pickValue>Broker&apos;s License</pickValue>
						<pickValue>Excess Property</pickValue>
						<pickValue>Worker&apos;s Comp Waiver</pickValue>
						<pickValue>Auto Insurance/Dishonesty</pickValue>
						<pickValue>Business Service Bond</pickValue>
						<pickValue>Business License</pickValue>
					</pickValues>
					<sort>false</sort>
				</picklist>
			</dataDescription>
			<description>Insurance types such as Liability, workers compensation, etc.  Can be contractor license info.  </description>
		</customField>
		<customField>
			<customFieldId>EXPIRATIONDATE1</customFieldId>
			<type>date</type>
			<ownerObject>vendor</ownerObject>
			<label>Expiration Date</label>
			<fieldSet>Insurance 1</fieldSet>
			<page>Insurance/License</page>
			<dataDescription>
				<date/>
			</dataDescription>
			<description>The date the insurance or license expires.</description>
		</customField>
		<customField>
			<customFieldId>CARRIERNAME1</customFieldId>
			<type>text</type>
			<ownerObject>vendor</ownerObject>
			<label>Carrier Name</label>
			<fieldSet>Insurance 1</fieldSet>
			<page>Insurance/License</page>
			<dataDescription>
				<text>
					<length>80</length>
				</text>
			</dataDescription>
			<description>Carrier Name</description>
		</customField>
		<customField>
			<customFieldId>POLICYNUMBER1</customFieldId>
			<type>text</type>
			<ownerObject>vendor</ownerObject>
			<label>Policy Number</label>
			<fieldSet>Insurance 1</fieldSet>
			<page>Insurance/License</page>
			<dataDescription>
				<text>
					<length>80</length>
				</text>
			</dataDescription>
			<description>Policy number (can include chars and number values.)</description>
		</customField>
		<customField>
			<customFieldId>VENDORINSURANCETYPE2</customFieldId>
			<type>picklist</type>
			<ownerObject>vendor</ownerObject>
			<label>Vendor Insurance Type</label>
			<fieldSet>Insurance 2</fieldSet>
			<page>Insurance/License</page>
			<dataDescription>
				<picklist>
					<pickValues>
						<pickValue>Workers Compensation</pickValue>
						<pickValue>Liability Insurance</pickValue>
						<pickValue>Contractor&apos;s License</pickValue>
						<pickValue>Auto Insurance</pickValue>
						<pickValue>Commercial</pickValue>
						<pickValue>1099</pickValue>
						<pickValue>Federal</pickValue>
						<pickValue>Casualty</pickValue>
						<pickValue>Exempt</pickValue>
						<pickValue>500,000,000 liability</pickValue>
						<pickValue>Tax ID</pickValue>
						<pickValue>General aggregate</pickValue>
						<pickValue>Umbrella Liability</pickValue>
						<pickValue>Broker&apos;s License</pickValue>
						<pickValue>Excess Property</pickValue>
						<pickValue>Worker&apos;s Comp Waiver</pickValue>
						<pickValue>Auto Insurance/Dishonesty</pickValue>
						<pickValue>Business Service Bond</pickValue>
						<pickValue>Business License</pickValue>
					</pickValues>
					<sort>false</sort>
				</picklist>
			</dataDescription>
			<description>Insurance types such as Liability, workers compensation, etc.  Can be contractor license info.</description>
		</customField>
		<customField>
			<customFieldId>EXPIRATIONDATE2</customFieldId>
			<type>date</type>
			<ownerObject>vendor</ownerObject>
			<label>Expiration Date</label>
			<fieldSet>Insurance 2</fieldSet>
			<page>Insurance/License</page>
			<dataDescription>
				<date/>
			</dataDescription>
			<description>The date the insurance or license expires.</description>
		</customField>
		<customField>
			<customFieldId>CARRIERNAME2</customFieldId>
			<type>text</type>
			<ownerObject>vendor</ownerObject>
			<label>Carrier Name</label>
			<fieldSet>Insurance 2</fieldSet>
			<page>Insurance/License</page>
			<dataDescription>
				<text>
					<length>80</length>
				</text>
			</dataDescription>
			<description>Carrier Name</description>
		</customField>
		<customField>
			<customFieldId>POLICYNUMBER2</customFieldId>
			<type>text</type>
			<ownerObject>vendor</ownerObject>
			<label>Policy Number</label>
			<fieldSet>Insurance 2</fieldSet>
			<page>Insurance/License</page>
			<dataDescription>
				<text>
					<length>80</length>
				</text>
			</dataDescription>
			<description>Policy number (can include chars and number values.)</description>
		</customField>
		<customField>
			<customFieldId>VENDORINSURANCETYPE3</customFieldId>
			<type>picklist</type>
			<ownerObject>vendor</ownerObject>
			<label>Vendor Insurance Type</label>
			<fieldSet>Insurance 3</fieldSet>
			<page>Insurance/License</page>
			<dataDescription>
				<picklist>
					<pickValues>
						<pickValue>Workers Compensation</pickValue>
						<pickValue>Liability Insurance</pickValue>
						<pickValue>Contractor&apos;s License</pickValue>
						<pickValue>Auto Insurance</pickValue>
						<pickValue>Commercial</pickValue>
						<pickValue>1099</pickValue>
						<pickValue>Federal</pickValue>
						<pickValue>Casualty</pickValue>
						<pickValue>Exempt</pickValue>
						<pickValue>500,000,000 liability</pickValue>
						<pickValue>Tax ID</pickValue>
						<pickValue>General aggregate</pickValue>
						<pickValue>Umbrella Liability</pickValue>
						<pickValue>Broker&apos;s License</pickValue>
						<pickValue>Excess Property</pickValue>
						<pickValue>Worker&apos;s Comp Waiver</pickValue>
						<pickValue>Auto Insurance/Dishonesty</pickValue>
						<pickValue>Business Service Bond</pickValue>
						<pickValue>Business License</pickValue>
					</pickValues>
					<sort>false</sort>
				</picklist>
			</dataDescription>
			<description>Insurance types such as Liability, workers compensation, etc.  Can be contractor license info.</description>
		</customField>
		<customField>
			<customFieldId>EXPIRATIONDATE3</customFieldId>
			<type>date</type>
			<ownerObject>vendor</ownerObject>
			<label>Expiration Date</label>
			<fieldSet>Insurance 3</fieldSet>
			<page>Insurance/License</page>
			<dataDescription>
				<date/>
			</dataDescription>
			<description>The date the insurance or license expires.</description>
		</customField>
		<customField>
			<customFieldId>CARRIERNAME3</customFieldId>
			<type>text</type>
			<ownerObject>vendor</ownerObject>
			<label>Carrier Name</label>
			<fieldSet>Insurance 3</fieldSet>
			<page>Insurance/License</page>
			<dataDescription>
				<text>
					<length>80</length>
				</text>
			</dataDescription>
			<description>Carrier Name</description>
		</customField>
		<customField>
			<customFieldId>POLICYNUMBER3</customFieldId>
			<type>text</type>
			<ownerObject>vendor</ownerObject>
			<label>Policy Number</label>
			<fieldSet>Insurance 3</fieldSet>
			<page>Insurance/License</page>
			<dataDescription>
				<text>
					<length>80</length>
				</text>
			</dataDescription>
			<description>Policy number (can include chars and number values.)</description>
		</customField>
	</customFields>
	<smartLinks>
		<smartLink>
			<smartLinkId>VALIDATE_INSURANCEDATE1</smartLinkId>
			<type>validate</type>
			<ownerObject>vendor</ownerObject>
			<events>
				<event>add</event>
				<event>set</event>
			</events>
			<renderDetails>
				<validate>
					<condition>{!VENDOR.VENDORINSURANCETYPE1!} == '' || ({!VENDOR.EXPIRATIONDATE1!} != '' &amp;&amp; {!VENDOR.EXPIRATIONDATE1!} &gt;= pastdate(1, "year"))</condition>
					<errorMessage>The expiration date 1 must have a value no earlier than one year before today&apos;s date.</errorMessage>
					<errorType>error</errorType>
				</validate>
			</renderDetails>
		</smartLink>
		<smartLink>
			<smartLinkId>VALIDATE_INSURANCEDATE2</smartLinkId>
			<type>validate</type>
			<ownerObject>vendor</ownerObject>
			<events>
				<event>add</event>
				<event>set</event>
			</events>
			<renderDetails>
				<validate>
					<condition>{!VENDOR.VENDORINSURANCETYPE2!} == '' || ({!VENDOR.EXPIRATIONDATE2!} != '' &amp;&amp; {!VENDOR.EXPIRATIONDATE2!} &gt;= pastdate(1, "year"))</condition>
					<errorMessage>The expiration date 2 must have a value no earlier than one year before today&apos;s date.</errorMessage>
					<errorType>error</errorType>
				</validate>
			</renderDetails>
		</smartLink>
		<smartLink>
			<smartLinkId>VALIDATE_INSURANCEDATE3</smartLinkId>
			<type>validate</type>
			<ownerObject>vendor</ownerObject>
			<events>
				<event>add</event>
				<event>set</event>
			</events>
			<renderDetails>
				<validate>
					<condition>{!VENDOR.VENDORINSURANCETYPE3!} == '' || ({!VENDOR.EXPIRATIONDATE3!} != '' &amp;&amp; {!VENDOR.EXPIRATIONDATE3!} &gt;= pastdate(1, "year"))</condition>
					<errorMessage>The expiration date 3 must have a value no earlier than one year before today&apos;s date.</errorMessage>
					<errorType>error</errorType>
				</validate>
			</renderDetails>
		</smartLink>
		<smartLink>
			<smartLinkId>VALIDATE_INSURANCEENTERED</smartLinkId>
			<type>validate</type>
			<ownerObject>vendor</ownerObject>
			<events>
				<event>add</event>
				<event>set</event>
			</events>
			<renderDetails>
				<validate>
					<condition>{!VENDOR.INSURANCEREQUIRED!} == '' || {!VENDOR.INSURANCEREQUIRED!} == false || (({!VENDOR.VENDORINSURANCETYPE1!} != '' &amp;&amp; {!VENDOR.EXPIRATIONDATE1!} != '') || ({!VENDOR.VENDORINSURANCETYPE2!} != '' &amp;&amp; {!VENDOR.EXPIRATIONDATE2!} != '') || ({!VENDOR.VENDORINSURANCETYPE3!} != '' &amp;&amp; {!VENDOR.EXPIRATIONDATE3!} != ''))</condition>
					<errorMessage>You have checked "Insurance Required".  You must supply at least one insurance type and expiration date.</errorMessage>
					<errorType>error</errorType>
				</validate>
			</renderDetails>
		</smartLink>
		<smartLink>
			<smartLinkId>VALIDATE_VENDORINSURANCE1</smartLinkId>
			<type>validate</type>
			<ownerObject>apbill</ownerObject>
			<events>
				<event>add</event>
			</events>
			<renderDetails>
				<validate>
					<condition>{!APBILL.VENDOR.VENDORINSURANCETYPE1!} == "" || {!APBILL.VENDOR.EXPIRATIONDATE1!} &gt;= today()</condition>
					<errorMessage>You are attempting to enter a bill for a vendor with an expired insurance policy.  The {!APBILL.VENDOR.VENDORINSURANCETYPE1!} policy expired {!APBILL.VENDOR.EXPIRATIONDATE1!}.  </errorMessage>
					<errorType>warning</errorType>
				</validate>
			</renderDetails>
		</smartLink>
		<smartLink>
			<smartLinkId>VALIDATE_VENDORINSURANCE2</smartLinkId>
			<type>validate</type>
			<ownerObject>apbill</ownerObject>
			<events>
				<event>add</event>
			</events>
			<renderDetails>
				<validate>
					<condition>{!APBILL.VENDOR.VENDORINSURANCETYPE2!} == "" || {!APBILL.VENDOR.EXPIRATIONDATE2!} &gt;= today()</condition>
					<errorMessage>You are attempting to enter a bill for a vendor with an expired insurance policy.  The {!APBILL.VENDOR.VENDORINSURANCETYPE2!} policy expired {!APBILL.VENDOR.EXPIRATIONDATE2!}.  </errorMessage>
					<errorType>warning</errorType>
				</validate>
			</renderDetails>
		</smartLink>
		<smartLink>
			<smartLinkId>VALIDATE_VENDORINSURANCE3</smartLinkId>
			<type>validate</type>
			<ownerObject>apbill</ownerObject>
			<events>
				<event>add</event>
			</events>
			<renderDetails>
				<validate>
					<condition>{!APBILL.VENDOR.VENDORINSURANCETYPE3!} == "" || {!APBILL.VENDOR.EXPIRATIONDATE3!} &gt;= today()</condition>
					<errorMessage>You are attempting to enter a bill for a vendor with an expired insurance policy.  The {!APBILL.VENDOR.VENDORINSURANCETYPE3!} policy expired {!APBILL.VENDOR.EXPIRATIONDATE3!}.  </errorMessage>
					<errorType>warning</errorType>
				</validate>
			</renderDetails>
		</smartLink>
		<smartLink>
			<smartLinkId>VALIDATEONPAY_VENDORINSURANCE1</smartLinkId>
			<type>validate</type>
			<ownerObject>appaymentrequest</ownerObject>
			<events>
				<event>add</event>
			</events>
			<renderDetails>
				<validate>
					<condition>{!APPAYMENTREQUEST.VENDOR.VENDORINSURANCETYPE1!} == "" || {!APPAYMENTREQUEST.VENDOR.EXPIRATIONDATE1!} &gt;= today()</condition>
					<errorMessage>You are attempting to pay a bill for a vendor with an expired insurance policy.  The {!APPAYMENTREQUEST.VENDOR.VENDORINSURANCETYPE1!} policy expired {!APPAYMENTREQUEST.VENDOR.EXPIRATIONDATE1!}.  </errorMessage>
					<errorType>warning</errorType>
				</validate>
			</renderDetails>
		</smartLink>
		<smartLink>
			<smartLinkId>VALIDATEONPAY_VENDORINSURANCE2</smartLinkId>
			<type>validate</type>
			<ownerObject>appaymentrequest</ownerObject>
			<events>
				<event>add</event>
			</events>
			<renderDetails>
				<validate>
					<condition>{!APPAYMENTREQUEST.VENDOR.VENDORINSURANCETYPE2!} == "" || {!APPAYMENTREQUEST.VENDOR.EXPIRATIONDATE2!} &gt;= today()</condition>
					<errorMessage>You are attempting to pay a bill for a vendor with an expired insurance policy.  The {!APPAYMENTREQUEST.VENDOR.VENDORINSURANCETYPE2!} policy expired {!APPAYMENTREQUEST.VENDOR.EXPIRATIONDATE2!}.  </errorMessage>
					<errorType>warning</errorType>
				</validate>
			</renderDetails>
		</smartLink>
		<smartLink>
			<smartLinkId>VALIDATEONPAY_VENDORINSURANCE3</smartLinkId>
			<type>validate</type>
			<ownerObject>appaymentrequest</ownerObject>
			<events>
				<event>add</event>
			</events>
			<renderDetails>
				<validate>
					<condition>{!APPAYMENTREQUEST.VENDOR.VENDORINSURANCETYPE3!} == "" || {!APPAYMENTREQUEST.VENDOR.EXPIRATIONDATE3!} &gt;= today()</condition>
					<errorMessage>You are attempting to pay a bill for a vendor with an expired insurance policy.  The {!APPAYMENTREQUEST.VENDOR.VENDORINSURANCETYPE3!} policy expired {!APPAYMENTREQUEST.VENDOR.EXPIRATIONDATE3!}.  </errorMessage>
					<errorType>warning</errorType>
				</validate>
			</renderDetails>
		</smartLink>
	</smartLinks>
	<signature>VEXbB0R/9UQA19GYGrjbYVs7+O5ZQkQMl8rFUdtrnIiVJO9/U13L5vKEBsDCojSqKjfaQfBEAhHfmd23Huq8YiWsnifZhbRv1jeRB7QFvFvm6oKdOg7rpDAaM4ZQYClqzfqzzP69h1YxCGkrPChTqL4TO8fdwu+mcYmq7xAKUDc=</signature>
</customErpPackage>
