<?php
/**
 * Copyright (c) 2016-2019 <PERSON>, licensed under MIT
 */

class AcceptedRule extends Rule
{
    protected bool $implicit = true;

    /**
     * Check the $value is accepted
     *
     * @param mixed $value
     * @return bool
     */
    public function check($value): bool
    {
        $column = $this->getAttributeAlias();
        $this->message = new ErrorMessage(
            sprintf("The %s must be accepted", $column),
            'DM-0002',
            ['COLUMN' => $column]
        );

        $allowed = ['yes', 'on', '1', 1, true, 'true'];
        
        return in_array($value, $allowed, true);
    }
}
