<?php

/**
 * Check if value exists in one of the departments or departmentGroups array provided as
 */
class ValidDepartmentRule extends Rule
{
    
    protected array $fillableParams = [ 'departments', 'departmentGroups' ];
    
    /**
     * Check if $value pass validation
     *
     * @param $value
     *
     * @return bool
     */
    public function check($value) : bool
    {
        $this->requireParameters($this->fillableParams);
        
        if ( $value === '' ) {
            return true;
        }
        
        list($deptId) = explode('--', $value);
        
        $departments = $this->parameter('departments');
        $departmentGroups = $this->parameter('departmentGroups');
        
        if ( !isset($departments[$deptId]) && !isset($departmentGroups[$deptId]) ) {
            $this->message = new ErrorMessage(
                sprintf("%s is not a valid department or department group", $deptId),
                'DM-0053',
                ['DEPT_ID' => $deptId]
            );
            return false;
        }
        
        return true;
    }
}
