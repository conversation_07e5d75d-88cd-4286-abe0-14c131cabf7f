<?php
/**
 * Copyright (c) 2016-2019 <PERSON>, licensed under MIT
 */

class JsonRule extends Rule
{


    /**
     * Check the $value is valid
     *
     * @param mixed $value
     * @return bool
     */
    public function check($value): bool
    {
        $column = $this->getAttributeAlias();
        $this->message = new ErrorMessage(
            sprintf("The %s must be a valid JSON string", $column),
            'DM-0030',
            ['COLUMN' => $column]
        );

        if (! is_string($value) || empty($value)) {
            return false;
        }
    
        json_decode($value);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return false;
        }

        return true;
    }
}
