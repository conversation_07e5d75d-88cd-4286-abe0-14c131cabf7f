<?php
/**
 */

class ReadOnlyRule extends Rule
{
    public function __construct()
    {
        $this->fillableParams = [ 'is_readonly' ];
        $this->isUnique = true;                      // Only latest instance of rule is used.
    }

    /**
     * Check the $value is valid
     *
     * @param mixed $value
     *
     * @return bool
     */
    public function check($value) : bool
    {
        $is_readonly = $this->parameter('is_readonly');

        if ( $value === null || $is_readonly == 'false' ) {
            return true;
        }

        $column = $this->getAttributeAlias();
        $this->message = new ErrorMessage(
            sprintf("The field is readonly: %s", $column),
            'DM-0096',
            ['COLUMN' => $this->getFieldNameMarker($column)]
        );
        return false;
    }
}
