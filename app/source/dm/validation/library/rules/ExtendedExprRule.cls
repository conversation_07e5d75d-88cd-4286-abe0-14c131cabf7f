<?php
/**
 */

class ExtendedExprRule extends Rule
{
    private static ?ExtendedRuleParser $parser = null;

    private static ?ErrorMessage $parseError = null;

    private static ?ExtendedRuleCode $code = null;

    public function __construct()
    {
        $this->fillableParams = ['expr'];
    }

    public function init()
    {
        try {
            if (self::$parser === null) {
                self::$parser = new ExtendedRuleParser("Rule Parser");
            }
            $ret = self::$parser->init();
            if ($ret !== true) {
                //dbg_log("Cannot init parser, error:");
                //dbg_log($ret);
                self::$parseError = $ret;
                return;
            }

            $ret = self::$parser->parse($this->parameter('expr'));
            if ($ret->getError() !== false) {
                self::$parseError = $ret->getError();
                return;
            }

            self::$code = $ret;
        } catch (Exception $e) {
            self::$parseError = new ErrorMessage("Error parsing extended expression rule:".$e->getMessage(), 'DM-0098');
        }
    }

    /**
     * Check the value is valid
     *
     * @param mixed $value
     * @return bool
     * @throws Exception
     */
    public function check($value): bool
    {
        $this->init();

        //  If there was some error with the expression parsing, pass the error.
        if (self::$parseError !== null) {
            $this->message = self::$parseError;
            return false;
        }

        $column = $this->getAttributeAlias();

        $objectType = $this->getObjectType();
        $dataRow = $this->validation->getInputs();
        $ret = self::$parser->executeOnObject(self::$code, $objectType, $dataRow, $column);
        if ($ret === true) {
            return true;
        } else if ($ret === false) {
            $this->message = new ErrorMessage("Extended expression rule should have custom error using errorArg",
                'DM-0098');
            return false;
        }

        $this->message = $ret;
        return false;
    }
}