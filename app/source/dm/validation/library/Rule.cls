<?php
/**
 * Copyright (c) 2016-2019 <PERSON>, licensed under MIT
 */

abstract class Rule
{
    const BOOLEAN_TRUE = "true";
    const BOOLEAN_FALSE = "false";

    protected ?string     $key            = null;
    protected ?AttributeValidation  $attribute      = null;
    protected ?Validation $validation     = null;
    protected bool        $implicit       = false; // if true and this rule fails, other rules are ignored
    protected array       $params         = [];
    protected array       $paramsTexts    = [];
    protected array       $fillableParams = [];     // Rule parameters.
    protected array       $fillableParamTypes = []; // Optional types of params. If supplied with fillableParams, types are checked.
    protected ?ErrorMessage $message = null;
    protected array       $retValues = [];
    protected string      $scope = Validator::RULE_SCOPE_ALL;   // One of the Validator::RULE_SCOPE defines.
    protected bool        $isUnique = false;                    // If true, only latest instance of rule is recognized.
    
    public ?ValidatorResources $validatorResources = null;

    /**
     * Check if $value pass validation
     *
     * @param $value
     *
     * @return bool
     */
    abstract public function check($value) : bool;

    /**
     * Called before check after full rule initialization.
     */
    public function preCheck()
    {
    }

    /**
     * Called after check - cannot be overridden.
     *
     * @param mixed $value
     * @param bool  $isValid
     */
    final function postCheck($value, $isValid)
    {
        //  If the valid is invalid, see if there is an overridding error and use it instead.
        if (!$isValid) {
            $message = $this->parameter('error');
            if ($message && $message instanceof ErrorMessage) {

                //  Custom error for rule, use given placeholders combined with record inputs.
                $msg = $message->getTranslatedMessage($this->validation->getInputs());
                $phs = array_merge($message->getPlaceholders(), $this->validation->getInputs());
                $message->setPlaceholders($phs);

                $this->message = $message;
            }
            if (! ($this instanceOf OwnedObjectRule)) {
                if (Validator::isDebugOn()) {
                    $alias = $this->getAttributeAlias();
                    if ($alias != '%s') {
                        $validator = $this->validation->getValidator();
                        Validator::debugOut($validator->getObjectType() . " rule FAILED on $alias value \\$value\\: " .
                         $this->getReadable());
                    }
                }
            }
        }
    }

    /**
     * Is a rule unique (should only one instance of it occur per attribute)?
     *
     * @return bool
     */
    public function isUniqueRule()
    {
        return $this->isUnique;
    }

    /**
     * Get Validation class instance
     *
     * @return Validation
     */
    public function getValidation()
    {
        return $this->validation;
    }

    /**
     * Set Validation class instance
     *
     * @param Validation $validation
     */
    public function setValidation(Validation $validation)
    {
        $this->validation = $validation;
    }

    /**
     * Get the overall validation engine.
     *
     * @return ValidationEngine
     */
    public function getValidationEngine() {
        return $this->validation->getValidator()->getValidationEngine();
    }

    /**
     * Get default object type (entity type).
     *
     * @return string
     */
    public function getObjectType() : string
    {
        return $this->validation ? $this->validation->getValidator()->getObjectType() : '';
    }

    /**
     * Get object alias (API object type).
     *
     * @return string
     */
    public function getObjectAlias() : string
    {
        return $this->validation ? $this->validation->getValidator()->getObjectAlias() : '';
    }

    /**
     * Set key
     *
     * @param string $key
     */
    public function setKey(string $key)
    {
        $this->key = $key;
    }

    /**
     * Get key
     *
     * @return string
     */
    public function getKey()
    {
        return $this->key ? : get_class($this);
    }

    /**
     * Set attribute
     *
     * @param AttributeValidation $attribute
     */
    public function setAttribute(AttributeValidation $attribute)
    {
        $this->attribute = $attribute;
    }
    
    /**
     * Get attribute
     *
     * @return AttributeValidation|null
     */
    public function getAttribute()
    {
        return $this->attribute;
    }

    /**
     * Get parameters
     *
     * @return array
     */
    public function getParameters() : array
    {
        return $this->params;
    }

    /**
     * Set params
     *
     * @param array $params
     *
     * @return Rule
     */
    public function setParameters(array $params) : Rule
    {
        $this->params = array_merge($this->params, $params);

        return $this;
    }

    /**
     * Converts any rule parameter to boolean, throws error if array/object.
     *
     * @param string $input Input value to convert to boolean.
     * @return bool
     */
    public function boolParameter($input)
    {
        if ($input !== null && is_scalar($input)) {
            if (is_string($input) && strtoupper($input)==='false') {
                return false;
            }
            return (bool)$input;
        }
        throw new Exception("Invalid boolean argument '$input'");
    }

    /**
     * Set parameters
     *
     * @param string $key
     * @param mixed  $value
     *
     * @return Rule
     */
    public function setParameter(string $key, $value) : Rule
    {
        $this->params[$key] = $value;

        return $this;
    }

    /**
     * Fill $params to $this->params.  Can be overwritten by rule classes.
     *
     * @param array &$params
     *
     * @return Rule
     */
    public function fillParameters(array &$params) : Rule
    {
        $types = $this->fillableParamTypes;
        foreach ( $this->fillableParams as $key ) {
            if (empty($params)) {
                break;
            }

            $this->params[$key] = array_shift($params);
            if ($this->params[$key] instanceof RuleArg) {
                $this->params[$key]->setParentRule($this);
            }

            //  If there is an optional type associated with the parameter, check it.
            $nextType = array_shift($types);
            switch ($nextType) {
                case 'array':
                    if (!is_array($this->params[$key])) {
                        throw new Exception("Rule parameter $key must be an array");
                    }
                    break;
                case 'number':
                    if (!is_numeric($this->params[$key])) {
                        throw new Exception("Rule parameter $key must be a number");
                    }
                    break;
                case 'required':
                    if (is_empty($this->params[$key])) {
                        throw new Exception("Rule parameter $key needs a value");
                    }
                    break;
            }
        }

        return $this;
    }

    /**
     * Decides if this arg is one of the optional type that can appear at the end of a rule.
     *
     * @param mixed $arg
     *
     * @return bool
     */
    public function isOptionalRuleArg($arg) : bool
    {
        if ($arg instanceof RuleErrorArg || $arg instanceof RuleTransformArg || $arg instanceof RuleOptionArg ||
         (($arg == Validator::RULE_SCOPE_POST) ||
         (is_array($arg) && $arg[0] == Validator::RULE_SCOPE_PATCH))) {
            return true;
        }
        return false;
    }

    public function fillRuleArgs(array &$params)
    {
        //  If there is are optional parameters, process them.
        foreach ($params ?? [] as $nextParam) {
            if ($nextParam instanceof RuleArg) {
                $nextParam->setParentRule($this);
            }
            if ($nextParam instanceof RuleErrorArg) {
                $this->params['error'] = $nextParam->resolveValue("");
                array_shift($params);
            } else if ($nextParam instanceof RuleTransformArg) {
                $this->params['transform'] = $nextParam;
                array_shift($params);
            } else if ($nextParam instanceof RuleOptionArg) {
                $this->params['options'][$nextParam->getOptionName()] = $nextParam;
                array_shift($params);
            } else if (($nextParam == Validator::RULE_SCOPE_POST) ||
             (is_array($nextParam) && $nextParam[0] == Validator::RULE_SCOPE_PATCH)) {
                $this->scope = $nextParam;
            }
        }
    }

    /**
     * Get an option on the rule (created via optionArg in the validation rules).
     *
     * @param string $optionName Name of option.
     *
     * @return mixed Will return an option-specific value, or null if the option is not set.
     */
    public function getOption(string $optionName) : mixed
    {
        if (isset($this->params['options'][$optionName])) {
            return $this->params['options'][$optionName]->getValue();
        }
        return null;
    }

    /**
     * Get parameter from given $key, return null if it not exists
     *
     * @param string $key
     *
     * @return mixed
     */
    public function parameter(string $key)
    {
        $value = $this->params[$key] ?? null;
        if ($value instanceof RuleArg) {
            $value = $value->resolveValue($value);
        }
        return $value;
    }

    /**
     * Set parameter text that can be displayed in error message using ':param_key'
     *
     * @param string $key
     * @param string $text
     */
    public function setParameterText(string $key, string $text)
    {
        $this->paramsTexts[$key] = $text;
    }

    /**
     * Get $paramsTexts
     *
     * @return array
     */
    public function getParametersTexts() : array
    {
        return $this->paramsTexts;
    }

    /**
     * Check whether this rule is implicit
     *
     * @return bool
     */
    public function isImplicit() : bool
    {
        return $this->implicit;
    }

    /**
     * Just alias of setMessage
     *
     * @param ErrorMessage $message
     *
     * @return Rule
     */
    public function message(ErrorMessage $message) : Rule
    {
        return $this->setMessage($message);
    }

    /**
     * Set message
     *
     * @param ErrorMessage $message
     *
     * @return Rule
     */
    public function setMessage(ErrorMessage $message) : Rule
    {
        $this->message = $message;

        return $this;
    }

    /**
     * Get message
     *
     * @return ErrorMessage
     */
    public function getMessage() : ErrorMessage|null
    {
        return $this->message;
    }
    
    /**
     * @return string
     */
    public function getMessageText() : string
    {
        return $this->message->getLogText();
    }
    
    /**
     * @return string
     */
    public function getTranslatedMessageText() : string
    {
        return $this->message->getTranslatedMessage();
    }

    /**
     * Check given $params must be exists
     *
     * @param array $params

     * @throws MissingRequiredParameterException
     */
    protected function requireParameters(array $params)
    {
        foreach ( $params as $param ) {
            if ( ! isset($this->params[$param])) {
                $rule = $this->getKey();
                throw new MissingRequiredParameterException("Missing required parameter '{$param}' on rule '{$rule}'");
            }
        }
    }
    
    /**
     * @return string
     */
    public function getAttributeAlias() : string
    {
        // called outside of Validation / in tests
        if ( ! $this->getAttribute()) {
            return '%s';
        }

        return $this->getAttribute()->getAlias() ? : $this->validation->resolveAttributeName($this->attribute);
    }
    
    /**
     * @return string
     */
    public function getEntity() : string
    {
        return $this->validation->getContext()->getEntity();
    }
    
    /**
     * @return bool
     */
    public function isUpdate() : bool
    {
        return $this->validation->getContext()->isUpdate();
    }
    
    /**
     * @return array
     */
    public function getUpdatedObject()
    {
        return $this->validation->getContext()->getUpdatedObject();
    }
    
    /**
     * Returns the object index (starts at 1) - only > 1 for child lines.
     * @return int
     */
    public function getObjectIndex()
    {
        return $this->validation->getContext()?->getSiblingIndex() ?? 1;
    }
    
    /**
     * @param array  $values
     * @param string $parameterText
     *
     * @return string
     */
    public function translateEnums(array $values, string $parameterText) : string
    {
        // $or = $this->validation ? $this->validation->getTranslation('or') : 'or'; // initial variant
        $or = _('or');
        $valuesText = ValidationHelper::join(ValidationHelper::wraps($values, "'"), ', ', ", $or ");
        $this->setParameterText($parameterText, $valuesText); // initial variant
        
        return $valuesText;
    }

    /**
     * @return array|string
     */
    public function __toString()
    {
        $key = $this->getKey();
        $params = $this->getParameters();
        
        if ( ! empty($params)) {
            $params = json_encode($params);
        }
        return "RULE: " . Validator::createRule($key, $params);
    }

    /**
     * @return string
     */
    public function getReadable() : string
    {
        $str = $this->getKey();
        $params = $this->getParameters();
        if (!empty($params)) {
            $str .= ": " . ppS($params);
        }
        return $str;
    }

    /**
     * Get returns, which are used in extended rules (a rule can return 1 or more values that are pushed
     *  on to the extended rule execution stack).
     *
     * @return array
     */
    public function getReturns() : array
    {
        return $this->retValues;
    }

    /**
     * Reset returns
     */
    public function resetReturns()
    {
        $this->retValues = [];
    }

    /**
     * Gets metadata about the rule.  Returns min/max num parameters, any defaults, etc.  Must be
     *  implemented in a rule class if that rule can be executed in an extended rule.  Should return:
     *   [ MIN_ARGS, MAX_ARGS, ARGS => [ TYPE, DEFAULT (optional) ] ]
     *
     * @return array|null
     */
    public function getMetadata() : array|null
    {
        return null;
    }

    /**
     *  convert filter args to a simple PHP filter array.
     *
     * @param array $ruleArgs Array of RuleArg objects.
     *
     * @return array
     */
    protected function filterArgsToFilters(array $ruleArgs) : array
    {
        $filters = [];
        foreach ($ruleArgs as $nextRule) {
            $theseFilters = $nextRule->getFilter();
            foreach ($theseFilters as $filter) {
                $filters[] = $filter;
            }
        }
        return $filters;
    }

    /**
     *  Filter (match) inputs by ruleArgs
     *
     * @param array $ruleArgs Array of RuleArg objects.
     *
     * @return bool
     */
    public function filterInputByArgs(array $ruleArgs) : bool
    {
        $filters = $this->filterArgsToFilters($ruleArgs);
        return $this->filterInputs($filters);
    }

    /**
     *  Turn rule filter args into a user-presentable condition expression (no need for i18n).
     *
     * @param array $ruleArgs Array of RuleArg objects.
     *
     * @return string
     */
    public function filterArgsToStr(array $ruleArgs) : string
    {
        $filters = $this->filterArgsToFilters($ruleArgs);
        $strs = [];
        foreach ($filters as $nextFilter) {
            $strs[] = $nextFilter['FIELD'] . " " . $nextFilter['OP'] . " " . $nextFilter['VALUE'];
        }
        return implode(" and ", $strs);
    }

    /**
     *  Table of built-in filter functions.
     */
    private static $builtInFilterFunctions = [
        'ISMECOMPANY' => [ 'method' => 'isMECompany' ],
        'ISMCMECOMPANY' => [ 'method' => 'isMCMECompany' ],
        'ISTOPMECOMPANY' => [ 'method' => 'isTopMECompany' ],
        'ISENTITYCOMPANY' => [ 'method' => 'isEntityCompany' ],
    ];

    private function isMECompany(string $args) : string
    {
        return IsMultiEntityCompany() ? self::BOOLEAN_TRUE : self::BOOLEAN_FALSE;
    }

    private function isMCMECompany(string $args) : string
    {
        return IsMCMESubscribed() ? self::BOOLEAN_TRUE : self::BOOLEAN_FALSE;
    }

    /**
     *  Return true if top level of ME company (false for non-ME or entity).
     */
    private function isTopMECompany(string $args) : string
    {
        return (IsMultiEntityCompany() && GetContextLocation() == '') ? self::BOOLEAN_TRUE : self::BOOLEAN_FALSE;
    }

    /**
     *  Return true if ME company but not top level (false for non-ME or top of ME).
     */
    private function isEntityCompany(string $args) : string
    {
        return (IsMultiEntityCompany() && GetContextLocation() != '') ? self::BOOLEAN_TRUE : self::BOOLEAN_FALSE;
    }

    private function executeBuiltinFunction(string $fieldName, mixed &$fieldValue) : bool
    {
        $pieces = explode('(' , rtrim($fieldName, ')'));
        if (count($pieces) == 1) {
            return false;
        }
        $funcName = strtoupper($pieces[0]);
        if (!isset(self::$builtInFilterFunctions[$funcName])) {
            throw new Exception("Cannot find function $funcName for FILTER rule");
        }

        $fieldValue = $this->$funcName($pieces[1]);
        Validator::debugOut("Built in filter function $fieldName returns value $fieldValue");
        return true;
    }

    /**
     * Filter (match) inputs by filter array (FIELD, OP, VALUE).
     *
     * @param array $filters Array of arrays (each has FIELD, OP, VALUE).
     *
     * @return bool
     */
    public function filterInputs(array $filters) : bool
    {
        $result = true;
        $checkFailed = false;
        foreach ($filters as $nextFilter) {
            $fieldName = $nextFilter['FIELD'];

            // Check to see if this is a builtin function which can derive the field value,
            //  else extract the field value.
            if (!$this->executeBuiltinFunction($fieldName, $fieldValue)) {
                $fieldValue = $this->validation->getValue($fieldName);
            }
            if (Validator::isDebugOn()) {
                Validator::debugOut("Applying filter to field $fieldName with value: $fieldValue");
                Validator::debugOut(ppS($nextFilter));
            }

            $op = $nextFilter['OP'];
            $value = $nextFilter['VALUE'];
            switch ($op) {
                case "=":
                    $checkFailed = ($value != $fieldValue);
                    break;
                case "!=":
                    $checkFailed = ($value == $fieldValue);
                    break;
                case "empty":
                    $checkFailed = !(!empty($fieldValue) && $value === 'true');
                    break;
    
                //  TODO: support more ops
                default:
                    $checkFailed = true;
                    break;
            }

            if (Validator::isDebugOn()) {
                Validator::debugOut("Filter resolves to:".($checkFailed?"false":"true"));
            }
            if ($checkFailed) {
                $result = false;
                break;
            }
        }

        Validator::debugOut("Filters returned ".($result?"true":"false"));
        return $result;
    }

    /**
     * Turn a field name into a marker that can be used later to substitute the external field name
     *  via subsFieldNameMarker.  This is done so that there is no conflict between a field name and
     *  other parts of the message that may contain the same word, e.g. 'journal'.
     *
     * @param string $fieldName
     *
     * @return string Field name with surrounding markers.
     */
    public function getFieldNameMarker(string $fieldName)
    {
        if($this->validation) {
            return $this->validation->getFieldNameMarker($fieldName);
        }

        return $fieldName;
    }

}
