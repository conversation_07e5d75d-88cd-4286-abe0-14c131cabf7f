<?php
/**
 * Translatable.cls
 * I18N wrapper
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Copyright (C)2022 Sage Intacct Corporation, All Rights Reserved
 */

trait Translatable
{

    /**
     * @throws I18NException
     */
    public function addToken(string $token, array $placeHolders = []) : void
    {
        I18N::addToken($token, $placeHolders);
    }

    /**
     * @throws I18NException
     */
    public function addTokens(array $tokens) : void
    {
        I18N::addTokens($tokens);
    }

    /**
     * @throws I18NException
     */
    private function addTokensFromJSON(string $json) : void
    {
        if(preg_match_all("/IA\.\w+/", $json, $matches)) {
            if($matches && $matches[0]) {
                foreach ($matches[0] as $token) {
                    $this->addToken($token);
                }
            }
        }
    }

    /**
     * @throws I18NException
     */
    public function getTranslatedTokens() : array
    {
        return I18N::getText();
    }

    /**
     * @throws I18NException
     */
    public function translateArray(array $array) : array
    {
        return json_decode($this->translateJSON(json_encode($array)), true);
    }

    /**
     * @throws I18NException
     */
    public function translateJSON(string $json) : string
    {
        $this->addTokensFromJSON($json);
        $translations = $this->getTranslatedTokens();

        return str_replace(array_keys($translations), array_values($translations), $json);
    }
}