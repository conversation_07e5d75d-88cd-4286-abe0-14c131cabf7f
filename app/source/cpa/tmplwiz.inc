<?  
//=============================================================================
//
//	FILE:			tmplwiz.inc
//	AUTHOR:			Ashish Vaishnav
//	DESCRIPTION:	global variables for template wizard
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================
define("TMPLWIZ", "tmplwiz.inc");
/* Values of text areas must have any returns removed before going to the DB. */
/* Including the text area variable name here ensures that this will happen. */
    $kSetupTextArea = array(
        'description'
    );
    /* define template wizard page */
    $kSetupPagesBase = array( 
        1 => array(
            'hasnext'=> '0',
            'hasprev'=> '0',
            'hasdone'=> '1',
            'title'  => 'Company Information', 
            'fields' => array('init','name','title','description','firstmonth','accrual','firstmonthtax','primacctnolen',
                        'acctnoseperator','subacctnolen','industry','currencycountry','currencyalignment',
                        'currencythousand_sep','currencydecimal_sep','currencysymbol','currencyname',
                        'currencysubname','industry', 'accounts',  'acctgroups', 'budgtypes',
                        'journals', 'reports', 'usrroles')
        )
    );

