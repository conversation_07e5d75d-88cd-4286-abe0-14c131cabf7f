<?php
/**
 * File description
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Intacct Corporation, All Rights Reserved
 */

/**
 * Class SubsystemsHandler
 */
class SubsystemsHandler
{

    /**
     * @var array $subsystems the map of subsystems by their type
     */
    private static $subsystems = [];
    
    /**
     * @param string|null $type
     *
     * @return void
     */
    public static function init(?string $type = null) : void
    {
        if (! defined('INTACCTCONTEXT')
            && (null === $type && [] === self::$subsystems
                || null !== $type && !array_key_exists($type, self::$subsystems))
        ) {
            /** @var SubsystemManager $mgr */
            $mgr = Globals::$g->gManagerFactory->getManager("subsystem");
            if (null !== $type && array_key_exists($type, SubsystemDefinition::$definitions)) {
                $list = $mgr->GetList(
                    [
                        'selects' => [
                            'RECORDNO', 'NAME', 'TYPE', 'STATUS', 'PROPERTIES', 'ACCEPT_TYPES',
                            'ACCEPT_NEW', 'MAINTENANCE'
                        ],
                        'filters' => [[['TYPE', '=', $type]]],
                    ]
                );
            } else {
                $list = $mgr->GetList();
            }
            
            if (!empty($list)) {
                foreach ($list as $row) {
                    $type = $row['TYPE'];
                    $row['PROPERTIES'] = $mgr->decodeAndDecryptProperties($row, $type);
                    self::$subsystems[$type][] = $row;
                }
            }
        }
    }

    /**
     *
     *
     * @param string $type
     * @param int    $specificSubsystemId
     *
     * @return array|null the allocated subsystem properties or null if none was found
     */
    public static function allocateSubsystem(string $type, $specificSubsystemId = null)
    {
        self::init($type);
        $list = arrayExtractValue(self::$subsystems, [$type]);
        if ( ! $list ) {
            return null;
        }

        $cnyCache = CompanyCacheHandler::getInstance();
        if ( $cnyCache === null ) {
            return null;
        }

        $companyType = $cnyCache->getProperty('company', 'TYPE');
        if ( ! $companyType ) {
            return null;
        }

        $markedCompanyType = SubsystemDefinition::VALUE_DELIMITER . $companyType
            . SubsystemDefinition::VALUE_DELIMITER;
        $matches = [];
        $specficMatch = [];
        if ($type == SubsystemDefinition::TYPE_OBIEE) {
            $cnyTitle = Request::$r->_cnytitle ?? Request::$r->_company_title;
            if (!empty(Request::$r->_newcnytitle)) {//request from dbacopy & copy tenant
                $cnyTitle = Request::$r->_newcnytitle;
            }
            $matches = SubsystemDefinition::getAllowedInstancesForCNY(
                GetCNYbyTitle($cnyTitle),
                strtolower(Request::$r->_module) === Reporting::CRW_REPORT_TYPE,
                strtolower(Request::$r->_module) === Reporting::DV_REPORT_TYPE);
            if ($matches['noInstanceAvailable'] === true) {
                $matches = SubsystemDefinition::getAllowedInstancesForCNY(GetCNYbyTitle($cnyTitle), true, true);
            }
        } else {
            foreach ($list as $item) {
                if ( empty($specficMatch) && $item['STATUS'] == 'active' && $item['ACCEPT_NEW'] == 'yes' ) {
                    $acceptTypes = SubsystemDefinition::VALUE_DELIMITER . $item['ACCEPT_TYPES']
                                   . SubsystemDefinition::VALUE_DELIMITER;
                    if ( strpos($acceptTypes, $markedCompanyType) !== false ) {
                        if ( $specificSubsystemId !== null && $specificSubsystemId === (int)$item['PROPERTIES']['ID'] ) {
                            $specficMatch = $item;
                        } else {
                            // already subscribed to IVE + add
                            $matches[] = $item;
                        }
                    }
                }
            }
        }

        $result = null;
        if ( ! empty($specficMatch) ) {
            $result = $specficMatch;
        } else {
            $cnt = count($matches);
            if ( $cnt > 0 ) {
                if ( $cnt == 1 ) {
                    $result = $matches[0];
                } else {
                    $pick = rand(0, $cnt - 1);
                    $result = $matches[$pick];
                }
            }
        }
        return $result;
    }
}
