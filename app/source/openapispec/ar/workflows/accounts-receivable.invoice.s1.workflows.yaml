info:
  title: accounts-receivable-invoice-workflows
  description: Workflows for AR invoice. Actions to manage state of an invoice.
accounts-receivable-invoice-actions-reverse-request:
  type: object
  x-mappedTo: arinvoice
  properties:
    key:
      type: string
      description: System-assigned key for AR invoice.
      x-mappedTo: RECORDNO
      example: '22'
    reversedDate:
      type: string
      format: date
      example: '2024-04-15'
      description: Date this transactions is reversed.
      x-mappedTo: VOIDDATE
    memo:
      type: string
      description: Notes or comments about the reason for the reverse of the invoice.
      x-mappedTo: DESCRIPTION
      example: Reversed the invoice for duplicate entry
  required:
    - key
    - reversedDate
accounts-receivable-invoice-actions-reverse-response:
  type: object
  x-mappedTo: arinvoice
  properties:
    key:
      type: string
      description: System-assigned unique record key for the new AR invoice.
      x-mappedTo: RECORDNO
      example: '23'
    id:
      type: string
      x-mappedTo: RECORDNO
      description: Unique identifier for the new AR invoice.
      example: '23'
    href:
      type: string
      description: URL endpoint for AR invoice.
      example: /objects/accounts-receivable/invoice/23
    state:
      type: string
      description: AR invoice state after reverse
      x-mappedTo: STATE
      example: reversal
accounts-receivable-invoice-actions-reclassify-request:
  type: object
  x-mappedTo: arinvoice
  properties:
    key:
      type: string
      description: System-assigned key for the AR invoice.
      x-mappedTo: RECORDNO
      example: '11'
    id:
      type: string
      description: Unique identifier for the AR invoice.
      x-mappedTo: RECORDNO
      example: '11'
    invoiceNumber:
      type: string
      description: Specify a unique invoice number while creating an invoice unless document sequencing is configured, in which case the number is auto-generated.
      x-mappedTo: RECORDID
      example: 'INV-001'
    referenceNumber:
      type: string
      description: Customer purchase order number or another reference number.
      x-mappedTo: DOCNUMBER
      example: 'PO6917'
    description:
      type: string
      description: Description of the invoice, which prints on the Customer Ledger report.
      x-mappedTo: DESCRIPTION
      example: Regular invoice
    term:
      type: object
      description: Term details for the invoice.
      x-object: accounts-receivable/term
      x-mappedTo: term
      properties:
        key:
          type: string
          description: System-assigned key for the term.
          x-mappedTo: TERMKEY
          example: '16'
        id:
          type: string
          description: Name of the term.
          x-mappedTo: TERMNAME
          example: '30 Days'
        href:
          type: string
          description: URL for the term.
          readOnly: true
          example: /objects/accounts-receivable/term/16
    dueDate:
      type: string
      format: date
      description: Date the invoice is due.
      x-mappedTo: WHENDUE
      example: '2022-12-31'
    attachment:
      type: object
      description: Supporting document attached to the invoice.
      x-object: company-config/attachment
      x-mappedTo: SUPDOCID
      properties:
        key:
          type: string
          description: System-assigned key for the supporting document.
          x-mappedTo: SUPDOCKEY
          example: '11'
        id:
          type: string
          description: System-assigned ID for the supporting document.
          x-mappedTo: SUPDOCID
          example: '11'
        href:
          type: string
          description: URL endpoint for the attachment.
          readOnly: true
          example: /objects/company-config/attachment/11
    href:
      type: string
      description: URL endpoint for this AR invoice.
      readOnly: true
      example: /objects/accounts-receivable/invoice/11
    lines:
      type: array
      description: Line items of the AR invoice.
      x-mappedTo: ITEMS
      x-object: accounts-receivable/invoice-line
      items:
        $ref: ../models/accounts-receivable.invoice-line.s1.schema.yaml
  required:
    - key
accounts-receivable-invoice-actions-reclassify-response:
  type: object
  x-mappedTo: arinvoice
  properties:
    key:
      type: string
      description: System-assigned key for the AR invoice.
      x-mappedTo: RECORDNO
      example: '132'
    id:
      type: string
      description: Unique identifier for the AR invoice.
      x-mappedTo: RECORDNO
      example: '132'
    href:
      type: string
      description: URL endpoint for this AR invoice.
      example: /objects/accounts-receivable/invoice/11
    state:
      type: string
      description: State of the invoice entry
      x-mappedTo: STATE
      example: posted
accounts-receivable-invoice-actions-submit-request:
  type: object
  x-mappedTo: arinvoice
  properties:
    key:
      type: string
      description: System-assigned key for the AR invoice.
      x-mappedTo: RECORDNO
      example: '11'
  required:
    - key
accounts-receivable-invoice-actions-submit-response:
  type: object
  x-mappedTo: arinvoice
  properties:
    key:
      type: string
      description: System-assigned key for the AR invoice.
      x-mappedTo: RECORDNO
      example: '11'
    id:
      type: string
      description: Unique identifier for the AR invoice.
      x-mappedTo: RECORDNO
      example: '11'
    href:
      type: string
      description: URL endpoint for this AR invoice.
      example: /objects/accounts-receivable/invoice/11
    state:
      type: string
      description: State of the invoice entry
      x-mappedTo: STATE
      example: posted