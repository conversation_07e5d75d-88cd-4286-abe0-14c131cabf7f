openapi: 3.0.0
info:
  title: accounts-receivable-payment-summary
  description: accounts-receivable.payment-summary API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON> Anjinappa
    email: <EMAIL>
tags:
  - name: Payment summaries
    description: AR payment summaries are collections of the same type of transactions grouped together for processing. AR payment summaries can be either open or closed. Payments can be added to open summaries.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/accounts-receivable/payment-summary:
    get:
      summary: List payment summaries
      description: Returns a collection with a key, ID, and link for each payment summary.
      tags:
        - Payment summaries
      operationId: list-accounts-receivable-payment-summary
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of ar-payment-summary objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of payment summaries:
                  value:
                    'ia::result':
                      - key: '110'
                        id: '110'
                        href: /objects/accounts-receivable/payment-summary/110
                      - key: '112'
                        id: '112'
                        href: /objects/accounts-receivable/payment-summary/112
                    'ia::meta':
                      totalCount: 2
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create a payment summary
      description: Creates a new payment summary.
      tags:
        - Payment summaries
      operationId: create-accounts-receivable-payment-summary
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/accounts-receivable-payment-summary'
                - $ref: '#/components/schemas/accounts-receivable-payment-summaryRequiredProperties'
            examples:
              Create a payment summary:
                value:
                  name: 'Reversed Receipts(Bank-BOA): 2023/04/12 Batch'
                  glPostingDate: 2024-06-18
                  status: active
                  state: open
                  preventGLPosting: false
                  bankAccount:
                    id: BOA
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New payment-summary
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                New payment summary:
                  value:
                    'ia::result':
                      key: '110'
                      id: '110'
                      href: /objects/accounts-receivable/payment-summary/110
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
  '/objects/accounts-receivable/payment-summary/{key}':
    parameters:
      - name: key
        description: System-assigned unique key for the payment summary.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a payment summary
      description: Returns detailed information for a specified payment summary.
      tags:
        - Payment summaries
      operationId: get-accounts-receivable-payment-summary-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the payment summary
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/accounts-receivable-payment-summary'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the payment summary:
                  value:
                    'ia::result':
                      key: '110'
                      id: '110'
                      name: 'Reversed Receipts(Bank-BOA): 2023/04/12 Batch'
                      glPostingDate: 2023-04-12
                      status: active
                      recordType: payment
                      totalAmount: '200'
                      state: open
                      parent:
                        key: null
                        id: null
                      preventGLPosting: false
                      bankAccount:
                        key: '1'
                        id: 'BOA'
                        currency: USD
                        href: /objects/cash-management/bank-account/1
                      undepositedGLAccount:
                        id: '1070'
                        key: '33'
                        href: /objects/general-ledger/account/33
                      summaryCreationType: manual
                      isQuickPaymentSummary: false
                      entity:
                        key: '1'
                        id: '1'
                        name: 'United States of America'
                        href: /objects/company-config/entity/1
                      audit:
                        createdDateTime: '2024-09-09T06:35:58Z'
                        modifiedDateTime: '2024-09-09T06:35:58Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      href: /objects/accounts-receivable/payment-summary/110
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update a payment summary
      description: Updates an existing payment summary by setting field values. Any fields not provided remain unchanged.
      tags:
        - Payment summaries
      operationId: update-accounts-receivable-payment-summary
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/accounts-receivable-payment-summary'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Update a payment summary:
                value:
                  name: paybatch 02
                  glPostingDate: 2024-06-19
                  status: active
                  state: open
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated ar-payment-batch
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Updated payment summary:
                  value:
                    ia::result:
                      key: '110'
                      id: '110'
                      href: /objects/accounts-receivable/payment-summary/110
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a payment summary
      description: Deletes a payment summary.
      tags:
        - Payment summaries
      operationId: delete-accounts-receivable-payment-summary
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    accounts-receivable-payment-summary:
      $ref: ../models/accounts-receivable.payment-summary.s1.schema.yaml
    accounts-receivable-payment-summaryRequiredProperties:
      type: object
      required:
        - name
        - glPostingDate
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml