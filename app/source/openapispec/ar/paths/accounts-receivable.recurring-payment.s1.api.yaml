openapi: 3.0.0
info:
  title: accounts-receivable-recurring-payment
  description: accounts-receivable.recurring-payment API
  version: '1.0'
  contact:
    name: <PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: AR recurring payments
    description: An AR recurring payment is an objects represent Recurring payments and used to create a payments for recurring Invoices.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/accounts-receivable/recurring-payment:
    get:
      summary: List AR recurring payments
      description: Returns a collection with a key, ID, and link for each AR recurring payment.
      tags:
        - AR recurring payments
      operationId: list-accounts-receivable-recurring-payment
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of ar-recurring-payment objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of AR recurring payments:
                  value:
                    'ia::result':
                      - key: "6"
                        id: "6"
                        href: "/objects/accounts-receivable/recurring-payment/6"
                      - key: "24"
                        id: "24"
                        href: "/objects/accounts-receivable/recurring-payment/24"
                      - key: "26"
                        id: "26"
                        href: "/objects/accounts-receivable/recurring-payment/26"
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
        '400':
          $ref: '#/components/responses/400error'
  /objects/accounts-receivable/recurring-payment/{key}:
    parameters:
      - name: key
        description: system-assigned unique key for the AR recurring payment.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a/an AR recurring payment
      description: Returns detailed information for a particular AR recurring payment.
      tags:
        - AR recurring payments
      operationId: get-accounts-receivable-recurring-payment-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the ar-recurring-payment
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/accounts-receivable-recurring-payment'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the AR recurring payment:
                  value:
                    'ia::result':
                      id: '6'
                      key: '6'
                      bankAccount:
                        id: '6'
                        key: BNYM
                        href: /objects/cash-management/bank-account/BNYM
                      glaccount:
                        id: '6'
                        key: 12
                      paymethod: cash
                      paymentAmount: '100.00'
                      creditCardType: null
                      payInFull: true
                      accountType: bank
                      href: /objects/accounts-receivable/recurring-payment/6
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    accounts-receivable-recurring-payment:
      $ref: ../models/accounts-receivable.recurring-payment.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
