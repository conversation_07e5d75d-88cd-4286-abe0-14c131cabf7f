key: accounts-receivable/payment-summary::systemARPaymentSummaryFW1
id: systemARPaymentSummaryFW1
object: accounts-receivable/payment-summary
name: IA.ALL
description: IA.ALL_ACTIVE_PAYMENT_SUMMARIES
query:
  object: accounts-receivable/payment-summary
  fields:
    - name
    - glPostingDate
    - summaryCreationType
    - totalAmount
    - bankAccount.id
    - bankAccount.currency
    - state
  filters:
    - $eq:
        status: active
  orderBy:
    - glPostingDate: desc
metadata:
  frozenColumnsCount: 2
  columns:
    - id: "name"
      format: "clip"
      size: 40
    - id: "glPostingDate"
      format: "clip"
      size: 40
    - id: "summaryCreationType"
      format: "clip"
      size: 40
    - id: "totalAmount"
      format: "clip"
      size: 40
    - id: "bankAccount.id"
      format: "clip"
      size: 40
    - id: "bankAccount.currency"
      format: "clip"
      size: 40
    - id: "state"
      format: "clip"
      size: 40
    - id: "computed.payments"
      format: "clip"
      size: 40
contexts:
  - __default