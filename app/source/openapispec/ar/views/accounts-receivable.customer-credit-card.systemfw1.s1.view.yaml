key: accounts-receivable/customer-credit-card::systemcustomer-credit-cardFW1
id: systemcustomer-credit-cardFW1
object: accounts-receivable/customer-credit-card
name: All
description: Specifies all active accounts-receivable/customer-credit-cards
query:
  object: accounts-receivable/customer-credit-card
  fields:
    - cardDetails.cardId
    - customer.id
    - cardDetails.cardType
    - cardDetails.description
    - isDefaultCard
    - status
  filters:
    - $eq:
        status: active
  orderBy:
    - customer.id: asc
contexts:
  - __default