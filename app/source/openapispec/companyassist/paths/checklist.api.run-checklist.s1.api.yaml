openapi: 3.0.0
info:
  title: checklist-api-run-checklist
  description: Close Assistant Run API
  version: '1.0'
  contact:
    name: <PERSON>
    email: ravi.shan<PERSON><PERSON><PERSON><PERSON><PERSON>@sage.com
tags:
  - name: run-checklist
    description: Run close assistant
servers:
  - url: 'http://localhost:3000'
paths:
  /services/checklist/api/run-checklist:
    get:
      summary: run-checklist
      description: run-checklist
      tags:
        - Run close assistant
      parameters:
        - name: checklistId
          description: The checklist command to execute.
          in: query
          required: true
          schema:
            type: string
            x-mappedTo: CHECKLISTID
            nullable: false
          example: closeassistant
        - name: period
          description: period to run the check list.
          in: query
          required: true
          schema:
            type: string
            x-mappedTo: PERIOD
            nullable: false
          example: Nov 2024
        - name: group
          description: Check List Task Group Name.
          in: query
          required: false
          schema:
            type: string
            enum:
              - accountsPayable
              - accountsReceivable
              - cashManagement
              - generalLedger
            x-mappedTo: GROUP
          example: accountsPayable
        - name: task
          description: Individual task name of the check list task group.
          in: query
          required: false
          schema:
              type: string
              x-mappedTo: TASK
              enum:
                - closeSummary
                - openTransaction
                - getEntityLists
                - runReconciliationReport
                - runOfflineReport
              example: openTransaction
        - name: filters
          description: Entity or Entity Group Filter.
          in: query
          required: false
          schema:
            type: string
            x-mappedTo: FILTER
            example: USA
        - name: processId
          description: Process ID for the Check List Task.
          in: query
          required: false
          schema:
            type: string
            x-mappedTo: PROCESSID
            nullable: false
          example: 3saa12312
      responses:
        '200':
          description: Executed
          content:
            application/json:
              schema:
                type: object
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/checklist-api-run-checklist-response'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                run-checklist:
                  value:
                    ia::result:
                      accountsPayable:
                        closeSummary:
                          taskStatusColorCode: green
                          drillParams: "/editor.phtml?&.it=apclosesummary&.mod=ap&.do=edit"
                          name: Close AP
                          description: Close Summary for Accounts Payable
                          taskType: indicator
                          refreshURL: v1-beta2/services/checklist/api/run-checklist?checklistId=closeassistant&period=Nov
                            2024&group=accountsPayable&task=closeSummary
                        openTransaction:
                          taskStatusColorCode: orange
                          drillParams: "/lister.phtml?&.it=apbill&.whencreated>=11/01/2024&.whencreated<=11/30/2024&.op=51&.recordtype=pi&.state
                            in [D,S]"
                          name: Review unposted bills
                          description: There are open transactions in Accounts Payable
                          taskType: indicator
                          refreshURL: v1-beta2/services/checklist/api/run-checklist?checklistId=closeassistant&period=Nov
                            2024&group=accountsPayable&task=openTransaction
                        groupName: Accounts Payable
                        refreshURL: v1-beta2/services/checklist/api/run-checklist?checklistId=closeassistant&period=Nov
                          2024&group=accountsPayable
                      accountsReceivable:
                        closeSummary:
                          taskStatusColorCode: green
                          drillParams: "/editor.phtml?&.it=arclosesummary&.mod=ap&.do=edit"
                          name: Close AR
                          description: Close Summary for Accounts Receivable
                          taskType: indicator
                          refreshURL: v1-beta2/services/checklist/api/run-checklist?checklistId=closeassistant&period=Nov
                            2024&group=accountsReceivable&task=closeSummary
                        openTransaction:
                          taskStatusColorCode: none
                          drillParams: "/lister.phtml?&.it=apbill&.whencreated>=11/01/2024&.whencreated<=11/30/2024&.op=51&.recordtype=ri&.state
                            in [D,S]"
                          name: Review unposted invoices
                          description: There are no open transactions in Accounts Receivable
                          taskType: indicator
                          refreshURL: v1-beta2/services/checklist/api/run-checklist?checklistId=closeassistant&period=Nov
                            2024&group=accountsReceivable&task=openTransaction
                        groupName: Accounts Receivable
                        refreshURL: v1-beta2/services/checklist/api/run-checklist?checklistId=closeassistant&period=Nov
                          2024&group=accountsReceivable
                      cashManagement:
                        closeSummary:
                          taskStatusColorCode: green
                          drillParams: "/editor.phtml?&.it=cmclosesummary&.mod=cm&.do=edit"
                          name: Close CM
                          description: Close Summary for Cash Management
                          taskType: indicator
                          refreshURL: v1-beta2/services/checklist/api/run-checklist?checklistId=closeassistant&period=Nov
                            2024&group=cashManagement&task=closeSummary
                        openTransaction:
                          taskStatusColorCode: orange
                          drillParams: "/lister.phtml?&.it=cm&.whencreated>=11/01/2024&.whencreated<=11/30/2024&.op=51&.recordtype=ci&.state
                            in [D,S]"
                          name: Review bank reconciliations
                          description: There are open transactions in Cash Management
                          taskType: indicator
                          refreshURL: v1-beta2/services/checklist/api/run-checklist?checklistId=closeassistant&period=Nov
                            2024&group=cashManagement&task=openTransaction
                        groupName: Cash Management
                        refreshURL: v1-beta2/services/checklist/api/run-checklist?checklistId=closeassistant&period=Nov
                          2024&group=cashManagement
                      generalLedger:
                        bvaInsights:
                          name: Run variance analysis
                          link: v1-beta2/services/general-ledger/insights/budget-vs-actual-variance?command=showBudgetVarianceAnalysisForManagerYTD&isOfflineType=false
                          taskType: analysis
                          kind: ytd_bva
                          businessId: Test@GAN_COPILOT-main
                          userId: Test@GAN_COPILOT-main
                          userLocale: en-US
                          bva:
                            user_type: controller
                        closeSummary:
                          taskStatusColorCode: green
                          drillParams: "/editor.phtml?&.it=glclosesummary&.mod=gl&.do=edit"
                          name: Close GL
                          description: Close Summary for General Ledger
                          taskType: indicator
                          refreshURL: v1-beta2/services/checklist/api/run-checklist?checklistId=closeassistant&period=Nov
                            2024&group=generalLedger&task=closeSummary
                        openTransaction:
                          taskStatusColorCode: none
                          drillParams: "/lister.phtml?&.it=gltrans&.whencreated>=11/01/2024&.whencreated<=11/30/2024&.op=51&.state
                            in [D,S]"
                          name: Review unposted journal entries
                          description: There are no open transactions in General Ledger
                          taskType: indicator
                          refreshURL: v1-beta2/services/checklist/api/run-checklist?checklistId=closeassistant&period=Nov
                            2024&group=generalLedger&task=openTransaction
                        groupName: General Ledger
                        refreshURL: v1-beta2/services/checklist/api/run-checklist?checklistId=closeassistant&period=Nov
                          2024&group=generalLedger
                      installedModules:
                        accountsPayable: 'true'
                        accountsReceivable: 'true'
                        cashManagement: 'true'
                        generalLedger: 'true'
                      refreshURL: v1-beta2/services/checklist/api/run-checklist?checklistId=closeassistant&period=Nov
                        2024
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    checklist-api-run-checklist-response:
      description: run-checklist response
      type: object
      x-mappedTo: __custom__
      properties:
        result:
          type: object
          description: Check List Task Results Details
          x-mappedTo: taskResults
          example: Check List Task Results Example
          properties:
            accountsPayable:
              type: object
              description: Account Payable Module
              x-mappedTo: taskResults.accountsPayable
              properties:
                openTransaction:
                  type: object
                  description: Account Payable Module
                  x-mappedTo: taskResults.accountsPayable.openTransaction
                  properties:
                    taskStatusColorCode:
                      type: string
                      description: Account Payable Transaction Task status color code
                      x-mappedTo: taskResults.accountsPayable.openTransaction.taskStatusColorCode
                      example: orange
                    drillParams:
                      type: string
                      description: Account Payable Drill Parameters
                      x-mappedTo: taskResults.accountsPayable.openTransaction.drillParams
                      example: /lister.phtml?&.it=apbill&.whencreated>=11/01/2024&.whencreated<=11/30/2024
                    taskType:
                      type: string
                      description: Account Payable Transaction Task Type
                      x-mappedTo: taskResults.accountsPayable.openTransaction.taskType
                      example: indicator
                reconciliations:
                  type: object
                  description: Prepare AP Reconciliation Task
                  x-mappedTo: taskResults.accountsPayable.reconciliations
                  properties:
                    taskStatusColorCode:
                      type: string
                      description: AP Reconciliation Task status color code
                      x-mappedTo: taskResults.accountsPayable.reconciliations.taskStatusColorCode
                      example: none
                    taskType:
                      type: string
                      description: Account Payable Transaction Task Type
                      x-mappedTo: taskResults.accountsPayable.reconciliations.taskType
                      example: analysis
                closeSummary:
                  type: object
                  description: Account Payable Close Summary
                  x-mappedTo: taskResults.accountsPayable.closeSummary
                  properties:
                    taskStatusColorCode:
                      type: string
                      description: Account Payable Summary Task status color code
                      x-mappedTo: taskResults.accountsPayable.closeSummary.taskStatusColorCode
                      example: green
                    drillParams:
                      type: string
                      description: Account Payable Summary Drill Parameters
                      x-mappedTo: taskResults.accountsPayable.closeSummary.drillParams
                      example: /editor.phtml?&.it='
                    taskType:
                      type: string
                      description: Account Payable Summary Task Type
                      x-mappedTo: taskResults.accountsPayable.closeSummary.taskType
                      example: indicator
            accountsReceivable:
              type: object
              description: Account Receivable Module
              x-mappedTo: taskResults.accountsReceivable
              properties:
                openTransaction:
                  type: object
                  description: Account Receivable Module
                  x-mappedTo: taskResults.accountsReceivable.openTransaction
                  properties:
                    taskStatusColorCode:
                      type: string
                      description: Account Receivable Transaction Task status color code
                      x-mappedTo: taskResults.accountsReceivable.openTransaction.taskStatusColorCode
                      example: orange
                    drillParams:
                      type: string
                      description: Account Receivable Drill Parameters
                      x-mappedTo: taskResults.accountsReceivable.openTransaction.drillParams
                      example: /lister.phtml?&.it=arinvoice&.whencreated>=11/01/2024&.whencreated<=11/30/2024
                    taskType:
                      type: string
                      description: Account Receivable Transaction Task Type
                      x-mappedTo: taskResults.accountsReceivable.openTransaction.taskType
                      example: indicator
                reconciliations:
                  type: object
                  description: Prepare AR Reconciliation Task
                  x-mappedTo: taskResults.accountsReceivable.reconciliations
                  properties:
                    taskStatusColorCode:
                      type: string
                      description: AR Reconciliation Task status color code
                      x-mappedTo: taskResults.accountsReceivable.reconciliations.taskStatusColorCode
                      example: none
                    taskType:
                      type: string
                      description: Account Receivable Transaction Task Type
                      x-mappedTo: taskResults.accountsReceivable.reconciliations.taskType
                      example: analysis
                closeSummary:
                  type: object
                  description: Account Receivable Close Summary
                  x-mappedTo: taskResults.accountsReceivable.closeSummary
                  properties:
                    taskStatusColorCode:
                      type: string
                      description: Account Receivable Summary Task status color code
                      x-mappedTo: taskResults.accountsReceivable.closeSummary.taskStatusColorCode
                      example: green
                    drillParams:
                      type: string
                      description: Account Receivable Summary Drill Parameters
                      x-mappedTo: taskResults.accountsReceivable.closeSummary.drillParams
                      example: /editor.phtml?&.it='
                    taskType:
                      type: string
                      description: Account Receivable Summary Task Type
                      x-mappedTo: taskResults.accountsReceivable.closeSummary.taskType
                      example: indicator
            cashManagement:
              type: object
              description: Cash Management Module
              x-mappedTo: taskResults.cashManagement
              properties:
                  openTransaction:
                    type: object
                    description: Cash Management Module
                    x-mappedTo: taskResults.cashManagement.openTransaction
                    properties:
                      taskStatusColorCode:
                        type: string
                        description: Cash Management Transaction Task status color code
                        x-mappedTo: taskResults.cashManagement.openTransaction.taskStatusColorCode
                        example: orange
                      drillParams:
                        type: string
                        description: Cash Management Drill Parameters
                        x-mappedTo: taskResults.cashManagement.openTransaction.drillParams
                        example: /lister.phtml?&.it=cm&.whencreated>=11/01/2024&.whencreated<=11/30/2024
                      taskType:
                        type: string
                        description: Cash Management Transaction Task Type
                        x-mappedTo: taskResults.cashManagement.openTransaction.taskType
                        example: Indicator
                  closeSummary:
                    type: object
                    description: Cash Management Close Summary
                    x-mappedTo: taskResults.cashManagement.closeSummary
                    properties:
                      taskStatusColorCode:
                        type: string
                        description: Cash Management Summary task status color code
                        x-mappedTo: taskResults.cashManagement.closeSummary.taskStatusColorCode
                        example: green
                      drillParams:
                        type: string
                        description: Cash Management Summary Drill Parameters
                        x-mappedTo: taskResults.cashManagement.closeSummary.drillParams
                        example: /editor.phtml?&.it='
                      taskType:
                        type: string
                        description: Cash Management Summary Task Type
                        x-mappedTo: taskResults.cashManagement.closeSummary.taskType
                        example: indicator
            generalLedger:
              type: object
              description: General Ledger Module
              x-mappedTo: taskResults.generalLedger
              properties:
                openTransaction:
                  type: object
                  description: General Ledger Module
                  x-mappedTo: taskResults.generalLedger.openTransaction
                  properties:
                    taskStatusColorCode:
                      type: string
                      description: General Ledger Transaction task status color code
                      x-mappedTo: taskResults.generalLedger.openTransaction.taskStatusColorCode
                      example: orange
                    drillParams:
                      type: string
                      description: General Ledger Drill Parameters
                      x-mappedTo: taskResults.generalLedger.openTransaction.drillParams
                      example: /lister.phtml?&.it=gltrans&.whencreated>=11/01/2024&.whencreated<=11/30/2024
                    taskType:
                      type: string
                      description: General Ledger Transaction Task Type
                      x-mappedTo: taskResults.generalLedger.openTransaction.taskType
                      example: indicator
                closeSummary:
                  type: object
                  description: General Ledger Close Summary
                  x-mappedTo: taskResults.generalLedger.closeSummary
                  properties:
                    taskStatusColorCode:
                      type: string
                      description: General Ledger Summary task status color Code
                      x-mappedTo: taskResults.generalLedger.closeSummary.taskStatusColorCode
                      example: green
                    drillParams:
                      type: string
                      description: General Ledger Summary Drill Parameters
                      x-mappedTo: taskResults.generalLedger.closeSummary.drillParams
                      example: /editor.phtml?&.it='
                    taskType:
                      type: string
                      description: General Ledger Task Type
                      x-mappedTo: taskResults.generalLedger.closeSummary.taskType
                      example: indicator
                bvaInsights:
                  type: object
                  description: Budget vs Actual Insights
                  x-mappedTo: taskResults.generalLedger.bvaInsights
                  properties:
                    link:
                      type: string
                      description: General Ledger Budget vs Actual Analysis Link
                      x-mappedTo: taskResults.generalLedger.bvaInsights.link
                      example: v1-beta2/services/general-ledger/insights/budget-vs-actual-variance?command=showBudgetVarianceAnalysisForManagerYTD&isOfflineType=false
                    taskType:
                      type: string
                      description: General Ledger Budget vs Actual Analysis Task Type
                      x-mappedTo: taskResults.generalLedger.bvaInsights.taskType
                      example: analysis
                    kind:
                      type: string
                      description: BVA Command Kind
                      x-mappedTo: taskResults.generalLedger.bvaInsights.kind
                      example: ytd_bva
                    businessId:
                      type: string
                      description: BVA Busniess ID
                      x-mappedTo: taskResults.generalLedger.bvaInsights.businessId
                      example: Test@GAN_COPILOT-main
                    userId:
                      type: string
                      description: BVA User ID
                      x-mappedTo: taskResults.generalLedger.bvaInsights.userId
                      example: Test@GAN_COPILOT-main
                    userLocale:
                      type: string
                      description: BVA User Locale
                      x-mappedTo: taskResults.generalLedger.bvaInsights.userLocale
                      example: en-US
                    bva:
                      type: object
                      description: BVA Details
                      x-mappedTo: taskResults.generalLedger.bvaInsights.bva
                      properties:
                        userType:
                          type: string
                          description: BVA Details User Type
                          x-mappedTo: taskResults.generalLedger.bvaInsights.bva.userType
                          example: controller
            installedModules:
              type: object
              x-mappedTo: taskResults.installedmodules
              properties:
                cashManagement:
                  type: string
                  description: Cash Management Module
                  x-mappedTo: taskResults.installedmodules.cashManagement
                  example: false
                generalLedger:
                  type: string
                  description: General Ledger Module
                  x-mappedTo: taskResults.installedmodules.generalLedger
                  example: true
                accountsReceivable:
                  type: string
                  description: Account Receivable Module
                  x-mappedTo: taskResults.installedmodules.accountsReceivable
                  example: true
                accountsPayable:
                  type: string
                  description: Account Payable Module
                  x-mappedTo: taskResults.installedmodules.accountsPayable
                  example: true
            closeBookOverview:
              type: object
              x-mappedTo: taskResults.closeBookOverview
              properties:
                drillParams:
                  type: string
                  description: Drill Params of Close Book Overview Lister
                  x-mappedTo: taskResults.closeBookOverview.drillParams
                  example: lister.phtml?&.it=closebookoverview
            runReconciliationReport:
              type: object
              description: Accounts Payable or Receivable Reconciliation Task Result
              x-mappedTo: taskResults.runReconciliationReport
              properties:
                reconReport:
                  type: object
                  description: Accounts Payable or Receivable Reconciliation Report
                  x-mappedTo: taskResults.runReconciliationReport.reconReport
                  properties:
                    group:
                      type: string
                      description: Accounts Payable or Receivable Reconciliation Group
                      x-mappedTo: taskResults.runReconciliationReport.reconReport.group
                      example: accountsReceivable
                    processId:
                      type: string
                      description: Accounts Payable or Receivable Reconciliation Process Id
                      x-mappedTo: taskResults.runReconciliationReport.reconReport.processId
                      example: x1234z
            runOfflineReport:
              type: object
              description: Accounts Payable or Receivable Reconciliation Task Result
              x-mappedTo: taskResults.runOfflineReport
              properties:
                reconReport:
                  type: object
                  description: Accounts Payable or Receivable Reconciliation Report
                  x-mappedTo: taskResults.runOfflineReport.reconReport
                  properties:
                    group:
                      type: string
                      description: Accounts Payable or Receivable Reconciliation Group
                      x-mappedTo: taskResults.runOfflineReport.reconReport.group
                      example: accountsReceivable
                    processId:
                      type: string
                      description: Accounts Payable or Receivable Reconciliation Process Id
                      x-mappedTo: taskResults.runOfflineReport.reconReport.processId
                      example: x1234z
                    storedReportListerDrillLink:
                      type: string
                      description: Accounts Payable or Receivable Reconciliation Process Id
                      x-mappedTo: taskResults.runOfflineReport.reconReport.storedReportListerDrillLink
                      example: reporter.phtml?.mod=ar
            getEntityLists:
              type: object
              x-mappedTo: taskResults.getEntityLists
              properties:
                requireEntityFilter:
                  type: string
                  description: Required entity or entity group filter
                  x-mappedTo: taskResults.getEntityLists.requireEntityFilter
                  example: true
                entity:
                  type: array
                  description: List of applicable entity or entity group
                  x-mappedTo: taskResults.getEntityLists.entity
                  items:
                    type: string
                    example:
                      - United States of America
                      - India
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
