fields:
  key:
    uiType: integer
    uiLabel: IA.RECORD_NUMBER
  id:
    uiType: text
    uiLabel: IA.TAX_SOLUTION_NAME
  description:
    uiType: multitext
    uiLabel: IA.DESCRIPTION
  status:
    uiType: enum
    uiLabel: IA.STATUS
    enumsLabels:
      -
        label: IA.ACTIVE
        value: active
      -
        label: IA.INACTIVE
        value: inactive
      -
        label: IA.NOT_CONFIGURED
        value: notConfigured
  taxSolutionType:
    uiType: radio
    uiLabel: IA.TYPE
    enumsLabels:
      -
        label: IA.STANDARD
        value: standard
      -
        label: IA.CUSTOM
        value: custom
  enableMultilineTax:
    uiType: enum
    uiLabel: IA.ENABLE_MULTIPLE_TAXES_PER_LINE_1
    enumsLabels:
      -
        label: IA.TRUE
        value: true
      -
        label: IA.FALSE
        value: false
  taxSubmissionStartDate:
    uiType: date
    uiLabel: IA.START_DATE_OF_FIRST_TAX_SUBMISSION_PERIOD
  taxCalculationMethod:
    uiType: enum
    uiLabel: IA.TAX_ENGINE
    enumsLabels:
      -
        label: IA.ADVANCED_TAX
        value: advancedTax
      -
        label: IA.VAT_OR_GST
        value: VAT
      -
        label: IA.AVALARA
        value: avaTax
      -
        label: IA.NONE
        value: noTax
      -
        label: IA.SIMPLE_TAX
        value: simpleTax
  alternativeSetup:
    uiType: enum
    uiLabel: IA.ALTERNATIVE_SETUP
    enumsLabels:
      -
        label: IA.TRUE
        value: true
      -
        label: IA.FALSE
        value: false
  lastUpdatedTaxDate:
    uiType: date
    uiLabel: IA.LAST_UPDATED
refs:
  arAdvanceOffsetGLAccount:
    uiLabel: IA.GL_ACCOUNT_AR_ADVANCED_OFFSET
    fields:
      key:
        uiType: sequence
        uiLabel: IA.OFFSET_GL_ACCOUNT_FOR_AR_ADVANCE_TAXES_LABEL
  purchaseGLAccount:
    uiLabel: IA.GL_ACCOUNT_PURCHASE
    fields:
      key:
        uiType: sequence
        uiLabel: IA.GL_ACCOUNT_FOR_INPUT_TAXES
  salesGLAccount:
    uiLabel: IA.GL_ACCOUNT_SALES
    fields:
      key:
        uiType: sequence
        uiLabel: IA.GL_ACCOUNT_FOR_OUTPUT_TAXES
