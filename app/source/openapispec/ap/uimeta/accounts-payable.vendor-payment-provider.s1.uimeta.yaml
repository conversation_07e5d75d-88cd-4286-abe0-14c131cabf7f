uiLabel: IA.VENDOR_PAYMENT_PROVIDER
fields:
  id:
    uiType: text
    uiLabel: IA.VENDOR_PAYMENT_PROVIDER_ID
  status:
    uiType: enum
    uiLabel: IA.STATUS
    enumsLabels:
      - label: IA.ACTIVE
        value: active
      - label: IA.INACTIVE
        value: inactive
refs:
  paymentProvider:
    fields:
      id:
        uiType: text
        uiLabel: IA.PROVIDER_ID
      name:
        uiType: text
        uiLabel: IA.PROVIDER_NAME
  vendor:
    fields:
      id:
        uiType: text
        uiLabel: IA.VENDOR_PAYMENTS_VENDOR_ID
      name:
        uiType: text
        uiLabel: IA.VENDOR_PAYMENTS_VENDOR_NAME
  preferredPaymentMethod:
    fields:
      id:
        uiType: text
        uiLabel: IA.PREFERRED_PAYMENT_METHOD_ID
      name:
        uiType: text
        uiLabel: IA.PREFERRED_PAYMENT_METHOD_NAME

