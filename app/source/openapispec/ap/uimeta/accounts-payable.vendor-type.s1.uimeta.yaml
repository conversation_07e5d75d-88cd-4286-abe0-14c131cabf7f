fields:
  id:
    uiType: text
    uiLabel: IA.NAME
  key:
    uiType: integer
    uiLabel: IA.RECORD_NUMBER
  status:
    uiType: enum
    uiLabel: IA.STATUS
    enumsLabels:
      -
        label: IA.ACTIVE
        value: active
      -
        label: IA.INACTIVE
        value: inactive
groups:
  audit:
    fields:
      createdBy:
        uiType: text
        uiLabel: IA.CREATED_BY
      createdDateTime:
        uiType: timestamp
        uiLabel: IA.WHEN_CREATED
      modifiedBy:
        uiType: text
        uiLabel: IA.MODIFIED_BY
      modifiedDateTime:
        uiType: timestamp
        uiLabel: IA.WHEN_MODIFIED
    refs:
      createdByUser:
        fields:
          key:
            uiType: integer
            uiLabel: IA.CREATED_BY
          id:
            uiType: text
            uiLabel: IA.CREATED_BY_LOGIN_ID
      modifiedByUser:
        fields:
          key:
            uiType: integer
            uiLabel: IA.MODIFIED_BY
          id:
            uiType: text
            uiLabel: IA.MODIFIED_BY_LOGIN_ID
  form1099:
    fields:
      box:
        uiType: text
        uiLabel: IA.FORM_1099_BOX
      type:
        uiType: text
        uiLabel: IA.FORM_1099_TYPE
refs:
  parent:
    fields:
      id:
        uiType: text
        uiLabel: IA.PARENT_TYPE
      key:
        uiType: integer
        uiLabel: IA.PARENT_RECORD_NUMBER
  entity:
    fields:
      id:
        uiType: text
        uiLabel: IA.ENTITY_ID
      key:
        uiType: integer
        uiLabel: IA.ENTITY_KEY
