openapi: 3.0.0
info:
  title: accounts-payable-amortization-template
  description: accounts-payable.amortization-template API
  version: '1.0'
  contact:
    name: <PERSON>
    email: <EMAIL>
tags:
  - name: AP amortization templates
    description: A predefined pattern used as an aid for setting up AP amortizations.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/accounts-payable/amortization-template:
    get:
      summary: List AP amortization templates
      description: Returns a collection with a key, ID, and link for each AP amortization template.
      tags:
        - AP amortization templates
      operationId: list-accounts-payable-amortization-template
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of AP amortization template objects.
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of AP amortization templates:
                  value:
                    'ia::result':
                      - key: '1'
                        id: AP Yearly CCA
                        href: /objects/accounts-payable/amortization-template/1
                      - key: '2'
                        id: AP Quarterly CCA
                        href: /objects/accounts-payable/amortization-template/2
                      - key: '3'
                        id: AP Monthly CCA
                        href: /objects/accounts-payable/amortization-template/3
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create an AP amortization template
      description: Creates a new AP amortization template.
      tags:
        - AP amortization templates
      operationId: create-accounts-payable-amortization-template
      requestBody:
        description: A JSON representation of the fields used to create the Accounts Payable amortization template instance.
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/accounts-payable-amortization-template'
                - $ref: '#/components/schemas/accounts-payable-amortization-templateRequiredProperties'
            examples:
              Creates an AP amortization template:
                value:
                  id: AP Yearly Acme Corp
                  description: Yealy amortization template
                  glAccount:
                    id: 1000Y
                  glJournal:
                    id: GJ
                  term: monthly
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New AP amortization template.
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to new AP amortization template:
                  value:
                    key: '123'
                    id: AP Yearly Acme Corp
                    href: /objects/accounts-payable/amortization-template/123
        '400':
          $ref: '#/components/responses/400error'
  '/objects/accounts-payable/amortization-template/{key}':
    parameters:
      - name: key
        description: System-assigned unique key for the AP amortization template.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get an AP amortization template
      description: Returns detailed information for a specified AP amortization template.
      tags:
        - AP amortization templates
      operationId: get-accounts-payable-amortization-template-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the AP amortization template.
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/accounts-payable-amortization-template'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the AP amortization template:
                  value:
                    key: '111'
                    id: Monthy 12 CCA
                    description: French CCA
                    templateType: cca
                    glJournal:
                      key: '123'
                      id: POJ
                      href: /objects/general-ledger/journal/123
                    glAccount:
                      key: '234'
                      id: '1300'
                      href: /objects/general-ledger/account/234
                    location:
                      key: '345'
                      id: 'San Jose Office'
                      href: /objects/general-ledger/account/345
                    term: monthly
                    status: active
                    audit:
                      createdDateTime: 2024-06-14T20:02:00Z
                      modifiedDateTime: 2024-06-14T20:02:00Z
                      createdBy: '1'
                      modfiedBy: '1'
                    href: /objects/accounts-payable/amortization-template/111
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update an AP amortization template
      description: Updates an existing AP amortization template by setting field values. Any fields not provided remain unchanged.
      tags:
        - AP amortization templates
      operationId: update-accounts-payable-amortization-template-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/accounts-payable-amortization-template'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Updates an AP amortization template:
                value:
                  description: Yealy amortization template primary
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated AP amortization template.
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              example:
                Updated amortization template:
                  'ia::result':
                    key: '111'
                    id: AP Yearly Acme Corp
                    href: /objects/accounts-payable/amortization-template/111
                  'ia::meta':
                    totalCount: 1
                    totalSuccess: 1
                    totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete an AP amortization template
      description: Deletes an AP amortization template.
      tags:
        - AP amortization templates
      operationId: delete-accounts-payable-amortization-template-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    accounts-payable-amortization-template:
      $ref: ../models/accounts-payable.amortization-template.s1.schema.yaml
    accounts-payable-amortization-templateRequiredProperties:
      type: object
      required:
        - id
        - description
        - glAccount
        - glJournal
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
