openapi: 3.0.0
info:
  title: inventory-control-warehouse-transfer
  description: inventory-control-warehouse-transfer API
  version: '1.0'
  contact:
    name: <PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Warehouse transfers
    description: |
     Use warehouse transfers to transfer items between warehouses or to transfer items between bins in the same warehouse. When you post a warehouse transfer, Sage Intacct creates the underlying system transactions, which includes automatically propagating the costs of the items.

     You cannot change the cost of the items when they move into the destination warehouse. For more information, see [Warehouse transfers overview](https://www.intacct.com/ia/docs/en_US/help_action/Default.htm#cshid=Warehouse_transfer_overview) in the Sage Intacct Help Center.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/inventory-control/warehouse-transfer:
    get:
      summary: List warehouse transfers
      description: Returns a collection with a key, ID, and link for each warehouse transfer. This operation is mostly for use in testing; use query to find objects that meet certain criteria and specify the properties that are returned.
      x-documentationFlags:
        subscription: Inventory Control
        userPermissions: 
          - userType: Business, Warehouse
            permissions: List, View Warehouse transfers
        configuration: Inventory Control is configured for warehouse transfers.
      tags:
        - Warehouse transfers
      operationId: list-inventory-control-warehouse-transfer
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of inventory-control-warehouse-transfer objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List warehouse transfers:
                  value:
                    'ia::result':
                      - key: '12'
                        id: 'IC:0358:doc'
                        href: /objects/inventory-control/warehouse-transfer/12
                      - key: '13'
                        id: 'IC:0356:doc'
                        href: /objects/inventory-control/warehouse-transfer/13
                      - key: '14'
                        id: 'IC:0359:doc'
                        href: /objects/inventory-control/warehouse-transfer/14
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create a warehouse transfer
      description: |
       Creates a new warehouse transfer. Fields required to create a warehouse transfer depend on the type of transfer, `immediate` or `inTransit`. And lines within the transfer must include one or more matched pair with one outgoing and one incoming line. 

       Read field descriptions carefully to ensure all requirements are met. 
      x-documentationFlags:
        subscription: Inventory Control
        userPermissions: 
          - userType: Business, Warehouse
            permissions: List, View, Add Warehouse transfers
        configuration: Inventory Control is configured for warehouse transfers.
      tags:
        - Warehouse transfers
      operationId: create-inventory-control-warehouse-transfer
      requestBody:
        description: Create a warehouse transfer
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/inventory-control-warehouse-transfer'
                - $ref: '#/components/schemas/inventory-control-warehouse-transferRequiredProperties'
            examples:
              Create a warehouse transfer:
                value:
                  transactionDate: '2024-03-01'
                  description: transfer to 10001-F per SO-0009
                  referenceNumber: null
                  exchangeRate:
                    date: null,
                    typeName: Intacct Daily Rate
                    outCurrency: USD
                    inCurrency: USD
                  transferType: immediate
                  action: draft
                  lines:
                    - transferDirection: out
                      item:
                        key: '249'
                      warehouse:
                        key: '1'
                      memo: transfer of 0 Lot
                      quantity: '1'
                      unit:
                        id: Each
                      trackingDetail:
                        - item:
                            key: '249'
                          quantity: '1'
                          serialNumber: null
                          lotNumber: LotABC
                          expirationDate: null
                      dimensions:
                        location:
                          key: '1'
                        department:
                          key: '6'
                        project:
                          key: '9'
                        customer:
                          key: '2'
                        vendor:
                          key: '47'
                        employee:
                          key: '27'
                        class:
                          key: '6'
                    - transferDirection: in
                      item:
                        key: '249'
                      quantity: '1'
                      unit:
                        id: Each
                      warehouse:
                        key: '50'
                      trackingDetail:
                        - item:
                            key: '249'
                          quantity: '1'
                          serialNumber: null
                          lotNumber: LotABC
                          expirationDate: null
                      dimensions:
                        location:
                          key: '1'
                        department:
                          key: '6'
                        project:
                          key: '9'
                        customer:
                          key: '2'
                        vendor:
                          key: '47'
                        employee:
                          key: '27'
                        class:
                          key: '6'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New inventory-control-warehouse-transfer
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to new warehouse transfer:
                  value:
                    'ia::result':
                      key: '19'
                      href: /objects/inventory-control/warehouse-transfer/19
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
  '/objects/inventory-control/warehouse-transfer/{key}':
    parameters:
      - name: key
        description: System-assigned unique key for the warehouse transfer.
        in: path
        example: '19'
        required: true
        schema:
          type: string
    get:
      summary: Get a warehouse transfer
      description: Returns detailed information for a specified warehouse transfer.
      x-documentationFlags:
        subscription: Inventory Control
        userPermissions: 
          - userType: Business, Warehouse
            permissions: List, View Warehouse transfers
        configuration: Inventory Control is configured for warehouse transfers.
      tags:
        - Warehouse transfers
      operationId: get-inventory-control-warehouse-transfer-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the inventory-control-warehouse-transfer
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/inventory-control-warehouse-transfer'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get a warehouse transfer:
                  value:
                    'ia::result':
                      key: '19'
                      id: IC:0360:doc
                      transactionDate: '2024-03-01'
                      outDate: '2024-03-01'
                      inDate: '2024-03-01'
                      description: transfer to 10001-F per SO-0009
                      referenceNumber: null
                      outDocument:
                        key: '350'
                        id: SYS-Warehouse Transfer Out-IC:0360:doc-Out
                        docid: SYS-Warehouse Transfer Out-IC:0360:doc-Out
                        href: /objects/inventory-control/document/350
                      inDocument:
                        key: '351'
                        id: SYS-Warehouse Transfer In-IC:0360:doc-In
                        href: /objects/inventory-control/document/351
                      inTransitDocument:
                        key: null
                        id: null
                      audit:
                        createdDateTime: '2024-03-13T01:11:36Z'
                        modifiedDateTime: '2024-03-13T01:11:36Z'
                        createdByUser:
                          key: '1'
                          href: /objects/company-config/user/1
                        createdBy: '1'
                        modifiedByUser:
                          key: '1'
                          href: /objects/company-config/user/1
                        modifiedBy: '1'
                      exchangeRate:
                        date: '2024-03-01'
                        typeName: Intacct Daily Rate
                        typeId: '-1'
                        rate: "1.0000000000"
                        outCurrency: USD
                        inCurrency: USD
                      transferState: draft
                      transferType: immediate
                      entity:
                        key: null
                        id: null
                        name: null
                      lines:
                        - id: '65'
                          key: '65'
                          lineNumber: '1'
                          warehouseTransfer:
                            key: '19'
                            id: IC:0360:doc
                            href: /objects/inventory-control/warehouse-transfer/19
                          inventoryDocument:
                            key: '350'
                            id: IC:0360:doc-Out
                            href: /objects/inventory-control/document/350
                          inventoryDocumentLine:
                            id: '397'
                            key: '397'
                            href: /objects/inventory-control/document-line/397
                          transferDirection: out
                          item:
                            key: '249'
                            id: 0 Lot
                            href: /objects/inventory-control/item/249
                          warehouse:
                            key: '1'
                            id: '1'
                            name: US TX Warehouse 10004
                            href: /objects/inventory-control/warehouse/1
                          memo: transfer of 0 Lot
                          quantity: '1.0000'
                          unit:
                            key: '3'
                            id: Each
                            href: /objects/inventory-control/unit-of-measure/3
                          cost: '0.0000000000'
                          extendedCost: '0.00'
                          standardCost: '0.0000000000'
                          extendedStandardCost: '0.00'
                          dimensions:
                            location:
                              key: '1'
                              id: '1'
                              href: /objects/company-config/location/1
                            department:
                              key: '6'
                              id: '6'
                              href: /objects/company-config/department/6
                            project:
                              key: '9'
                              id: '9'
                              href: /objects/projects/project/9
                            customer:
                              key: '2'
                              id: '2'
                              href: /objects/accounts-receivable/customer/2
                            vendor:
                              key: '47'
                              id: '201'
                              href: /objects/accounts-payable/vendor/47
                            employee:
                              key: '27'
                              id: '12'
                              href: /objects/company-config/employee/27
                            class:
                              key: '6'
                              id: '4'
                              href: /objects/company-config/class/6
                          audit:
                            createdDateTime: '2024-03-13T01:11:36Z'
                            modifiedDateTime: '2024-03-13T01:11:36Z'
                            createdByUser:
                              key: '1'
                              href: /objects/company-config/user/1
                            createdBy: '1'
                            modifiedByUser:
                              key: '1'
                              href: /objects/company-config/user/1
                            modifiedBy: '1'
                          trackingDetail:
                            - id: '260'
                              key: '260'
                              inventoryDocumentLine:
                                id: '397'
                                key: '397'
                                documentType: SYS-Warehouse Transfer Out
                                href: /objects/inventory-control/document-line::SYS-Warehouse%20Transfer%20Out/397
                              item:
                                id: 0 Lot
                                key: '249'
                                href: /objects/inventory-control/item/249
                              quantity: '1.0000000000'
                              serialNumber: null
                              lotNumber: LotABC
                              bin:
                                key: null
                                id: null
                              expirationDate: null
                              audit:
                                createdDateTime: '2024-03-13T01:11:35Z'
                                modifiedDateTime: '2024-03-13T01:11:35Z'
                                createdByUser:
                                  key: '1'
                                  href: /objects/company-config/user/1
                                createdBy: '1'
                                modifiedByUser:
                                  key: '1'
                                  href: /objects/company-config/user/1
                                modifiedBy: '1'
                              href: /objects/inventory-control/document-line-detail/260
                          href: /objects/inventory-control/warehouse-transfer-line/65
                        - id: '66'
                          key: '66'
                          lineNumber: '1'
                          warehouseTransfer:
                            key: '19'
                            id: IC:0360:doc
                            href: /objects/inventory-control/warehouse-transfer/19
                          inventoryDocument:
                            key: '351'
                            id: IC:0360:doc-In
                            href: /objects/inventory-control/document/351
                          inventoryDocumentLine:
                            id: '398'
                            key: '398'
                            href: /objects/inventory-control/document-line/398
                          transferDirection: in
                          item:
                            key: '249'
                            id: 0 Lot
                            href: /objects/inventory-control/item/249
                          warehouse:
                            key: '50'
                            id: 10001-F
                            name: Child of WH10001
                            href: /objects/inventory-control/warehouse/50
                          quantity: '1.0000'
                          unit:
                            key: '3'
                            id: Each
                            href: /objects/inventory-control/unit-of-measure/3
                          cost: '0.0000000000'
                          extendedCost: '0.00'
                          standardCost: '0.0000000000'
                          extendedStandardCost: '0.00'
                          dimensions:
                            location:
                              key: '1'
                              id: '1'
                              href: /objects/company-config/location/1
                            department:
                              key: '6'
                              id: '6'
                              href: /objects/company-config/department/6
                            project:
                              key: '9'
                              id: '9'
                              href: /objects/projects/project/9
                            customer:
                              key: '2'
                              id: '2'
                              href: /objects/accounts-receivable/customer/2
                            vendor:
                              key: '47'
                              id: '201'
                              href: /objects/accounts-payable/vendor/47
                            employee:
                              key: '27'
                              id: '12'
                              href: /objects/company-config/employee/27
                            class:
                              key: '6'
                              id: '4'
                              href: /objects/company-config/class/6
                          audit:
                            createdDateTime: '2024-03-13T01:11:36Z'
                            modifiedDateTime: '2024-03-13T01:11:36Z'
                            createdByUser:
                              key: '1'
                              href: /objects/company-config/user/1
                            createdBy: '1'
                            modifiedByUser:
                              key: '1'
                              href: /objects/company-config/user/1
                            modifiedBy: '1'
                          trackingDetail:
                            - id: '261'
                              key: '261'
                              inventoryDocumentLine:
                                id: '398'
                                key: '398'
                                documentType: SYS-Warehouse Transfer In
                                href: /objects/inventory-control/document-line::SYS-Warehouse%20Transfer%20In/398
                              item:
                                id: 0 Lot
                                key: '249'
                                href: /objects/inventory-control/item/249
                              quantity: '1.0000000000'
                              serialNumber: null
                              lotNumber: LotABC
                              bin:
                                key: null
                                id: null
                              expirationDate: null
                              audit:
                                createdDateTime:  '2024-03-13T01:11:36Z'
                                modifiedDateTime: '2024-03-13T01:11:36Z'
                                createdByUser:
                                  key: '1'
                                  href: /objects/company-config/user/1
                                createdBy: '1'
                                modifiedByUser:
                                  key: '1'
                                  href: /objects/company-config/user/1
                                modifiedBy: '1'
                              href: /objects/inventory-control/document-line-detail/261
                          href: /objects/inventory-control/warehouse-transfer-line/66
                      href: /objects/inventory-control/warehouse-transfer/19
                    'ia::meta':
                      "totalCount": 1
                      "totalSuccess": 1
                      "totalError": 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update a warehouse transfer
      description: |
       Updates an existing warehouse transfer by setting field values. Any field not provided remains unchanged.

       For `inTransit` transfers, edit the transfer when the inventory arrives at the destination warehouse to increase ONHAND quantities and value.

       Any warehouse transfer can be edited if the changes only affect inventory that still exists at the destination warehouse. 
      x-documentationFlags:
        subscription: Inventory Control
        userPermissions: 
          - userType: Business, Warehouse
            permissions: List, View, Edit Warehouse transfers
        configuration: Inventory Control is configured for warehouse transfers.
      tags:
        - Warehouse transfers
      operationId: update-inventory-control-warehouse-transfer-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/inventory-control-warehouse-transfer'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Update to transferIn state from transferOut state:
                value:
                  action: transferIn
                  lines:
                    - key: '59'
                      transferDirection: out
                      item:
                        key: '249'
                      warehouse:
                        key: '1'
                      quantity: '1'
                      unit:
                        id: Each
                      trackingDetail:
                        - item:
                            key: '249'
                          quantity: '1'
                          serialNumber: null
                          lotNumber: LotABC
                          bin:
                            key: null
                          expirationDate: null
                    - key: '60'
                      transferDirection: in
                      item:
                        key: '249'
                      quantity: '1'
                      unit:
                        id: Each
                      warehouse:
                        key: '50'
                      trackingDetail:
                        - item:
                            key: '249'
                          quantity: '1'
                          serialNumber: null
                          lotNumber: LotABC
                          bin:
                            key: null
                          expirationDate: null
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated inventory-control-warehouse-transfer
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to updated warehouse transfer:
                  value:
                    'ia::result':
                      key: '17'
                      id : IC:0358:doc
                      href: /objects/inventory-control/warehouse-transfer/17
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a warehouse transfer
      description: |
       Deletes a warehouse transfer. Warehouse transfers that are in `draft` or `inTransit` state can be deleted. 
       
       And, warehouse transfers that are in `posted` state can be deleted if the inventory that was transferred still exists at the destination warehouse.
      x-documentationFlags:
        subscription: Inventory Control
        userPermissions: 
          - userType: Business, Warehouse
            permissions: List, View, Delete Warehouse transfers
        configuration: Inventory Control is configured for warehouse transfers.
      tags:
        - Warehouse transfers
      operationId: delete-inventory-control-warehouse-transfer-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    inventory-control-warehouse-transfer:
      $ref: ../models/inventory-control.warehouse-transfer.s1.schema.yaml
    inventory-control-warehouse-transferRequiredProperties:
      type: object
      required:
        - transactionDate
        - transferType
        - action
      properties:
        lines:
          type: array
          items:
            required:
              - item
              - warehouse
              - transferDirection
              - unit
              - quantity
            properties:
              dimensions:
                type: object
                required:
                  - location
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml