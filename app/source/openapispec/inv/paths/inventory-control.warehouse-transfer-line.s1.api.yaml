openapi: 3.0.0
info:
  title: inventory-control-warehouse-transfer-line
  description: inventory-control-warehouse-transfer-line API
  version: '1.0'
  contact:
    name: <PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Warehouse transfer lines
    description: |
     Lines in a warehouse transfer represent items included in the transfer and details about those items. You can create or update warehouse transfer lines by creating or modifying the [warehouse transfer](inventory-control.warehouse-transfer).
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/inventory-control/warehouse-transfer-line:
    get:
      summary: List warehouse transfer lines
      description: Returns a collection with a key, ID, and link for each warehouse transfer line. This operation is mostly for use in testing; use query to find objects that meet certain criteria and to specify properties that are returned.
      x-documentationFlags:
        subscription: Inventory Control
        userPermissions: 
          - userType: Business, Warehouse
            permissions: List, View Warehouse transfers
      tags:
        - Warehouse transfer lines
      operationId: list-inventory-control-warehouse-transfer-line
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of inventory-control-warehouse-transfer-line objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List warehouse transfer lines:
                  value:
                    'ia::result':
                      - key: '5'
                        id: '5'
                        href: /objects/inventory-control/warehouse-transfer-line/5
                      - key: '6'
                        id: '6'
                        href: /objects/inventory-control/warehouse-transfer-line/6
                      - key: '7'
                        id: '7'
                        href: /objects/inventory-control/warehouse-transfer-line/7
                    'ia::meta':  
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
  '/objects/inventory-control/warehouse-transfer-line/{key}':
    parameters:
      - name: key
        description: System-assigned unique key for the warehouse transfer line.
        example: '78'
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a warehouse transfer line
      description: Returns detailed information for a specified warehouse transfer line.
      x-documentationFlags:
        subscription: Inventory Control
        userPermissions: 
          - userType: Business, Warehouse
            permissions: List, View Warehouse transfers
      tags:
        - Warehouse transfer lines
      operationId: get-inventory-control-warehouse-transfer-line-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the inventory-control-warehouse-transfer-line
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/inventory-control-warehouse-transfer-line'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get a warehouse transfer line:
                  value:
                    id: '65'
                    key: '65'
                    lineNumber: '1'
                    warehouseTransfer:
                      key: '19'
                      id: IC:0360:doc
                      href: /objects/inventory-control/warehouse-transfer/19
                    inventoryDocument:
                      key: '350'
                      id: IC:0360:doc-Out
                      href: /objects/inventory-control/document/350
                    inventoryDocumentLine:
                      id: '397'
                      key: '397'
                      href: /objects/inventory-control/document-line/397
                    transferDirection: out
                    item:
                      key: '249'
                      id: 0 Lot
                      href: /objects/inventory-control/item/249
                    warehouse:
                      key: '4'
                      id: 'WareHouse10004'
                      name: US TX Warehouse 10004
                      href: /objects/inventory-control/warehouse/4
                    memo: transfer of 0 Lot
                    quantity: '1.0000'
                    unit:
                      key: '3'
                      id: Each
                      href: /objects/inventory-control/unit-of-measure/3
                    cost: '0.0000000000'
                    extendedCost: '0.00'
                    standardCost: '0.0000000000'
                    extendedStandardCost: '0.00'
                    dimensions:
                      location:
                        key: '1'
                        id: '1'
                        href: /objects/company-config/location/1
                      department:
                        key: '6'
                        id: '6'
                        href: /objects/company-config/department/6
                      project:
                        key: '9'
                        id: '9'
                        href: /objects/projects/project/9
                      customer:
                        key: '2'
                        id: '2'
                        href: /objects/accounts-receivable/customer/2
                      vendor:
                        key: '47'
                        id: '201'
                        href: /objects/accounts-payable/vendor/47
                      employee:
                        key: '27'
                        id: '12'
                        href: /objects/company-config/employee/27
                      class:
                        key: '6'
                        id: '4'
                        href: /objects/company-config/class/6
                    audit:
                      createdDateTime: '2024-03-13T01:11:36Z'
                      modifiedDateTime: '2024-03-13T01:11:36Z'
                      createdByUser:
                          key: '1'
                          href: /objects/company-config/user/1
                      createdBy: '1'
                      modifiedByUser:
                          key: '1'
                          href: /objects/company-config/user/1
                      modifiedBy: '1'
                    trackingDetail:
                      - id: '260'
                        key: '260'
                        inventoryDocumentLine:
                          id: '397'
                          key: '397'
                          documentType: SYS-Warehouse Transfer Out
                          href: /objects/inventory-control/document-line::SYS-Warehouse%20Transfer%20Out/397
                        item:
                          id: 0 Lot
                          key: '249'
                          href: /objects/inventory-control/item/249
                        quantity: '1.0000000000'
                        serialNumber: null
                        lotNumber: LotABC
                        bin:
                          key: null
                          id: null
                        expirationDate: null
                        audit:
                          createdDateTime: "2024-03-13T01:11:35Z"
                          modifiedDateTime: "2024-03-13T01:11:35Z"
                          createdByUser:
                            key: '1'
                            href: /objects/company-config/user/1
                          createdBy: '1'
                          modifiedByUser:
                            key: '1'
                            href: /objects/company-config/user/1
                          modifiedBy: '1'
                        href: /objects/inventory-control/document-line-detail/260
                    href: /objects/inventory-control/warehouse-transfer-line/65
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    inventory-control-warehouse-transfer-line:
      $ref: ../models/inventory-control.warehouse-transfer-line.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml