openapi: 3.0.0
info:
  title: warehouse-group-member
  description: Member warehouses of a warehouse group.
  version: '1.0'
  contact:
    email: <PERSON><PERSON>.<EMAIL>
    name: <PERSON><PERSON> Shan
tags:
  - name: Warehouse group members
    description: Member warehouses of  warehouse group.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/inventory-control/warehouse-group-member:
    get:
      summary: List warehouse group members
      description: Returns up to 100 object references from the collection with a key, ID, and link for each warehouse group member. This operation is mostly for use in testing; use query to find objects that meet certain criteria and to specify properties that are returned.
      tags:
        - Warehouse group members
      operationId: list-inventory-control-warehouse-group-member
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of warehouse-group-member objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of warehouse group members:
                  value:
                    'ia::result':
                      - key: '17'
                        id: '17'
                        href: /objects/inventory-control/warehouse-group-member/17
                      - key: '15'
                        id: '15'
                        href: /objects/inventory-control/warehouse-group-member/15
                    'ia::meta':
                      totalCount: 2
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
         $ref: '#/components/responses/400error'
  '/objects/inventory-control/warehouse-group-member/{key}':
    parameters:
      - name: key
        description: System-assigned unique key for the warehouse group member.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a warehouse group member.
      description: Returns detailed information for a specific warehouse group member.
      tags:
        - Warehouse group members
      operationId: get-inventory-control-warehouse-group-member-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the warehouse-group-member.
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/inventory-control-warehouse-group-member'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get a warehouse group member:
                  value:
                    "ia::result":
                      "key": "10"
                      "id": "10"
                      "warehouseGroup":
                        "key": "4"
                        "id": "EastCoastWarehouses"
                        "href": "/objects/inventory-control/warehouse-group/4"
                      "warehouse":
                        "key": "9"
                        "id": "MCOM"
                        "name": "Mobile Components"
                        "status": "active"
                        "href": "/objects/inventory-control/warehouse/9"
                      "sortOrder": 1
                      "audit":
                        "createdDateTime": "2017-04-27T17:46:10Z"
                        "modifiedDateTime": "2017-04-27T17:46:10Z"
                        "createdBy": "1"
                        "modifiedBy": "1"
                      "href": "/objects/inventory-control/warehouse-group-member/10"
                    "ia::meta":
                      "totalCount": 1
                      "totalSuccess": 1
                      "totalError": 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a warehouse group member.
      description: Deletes a warehouse from a warehouse group.
      tags:
        - Warehouse group members
      operationId: delete-inventory-control-warehouse-group-member-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    inventory-control-warehouse-group-member:
      $ref: ../models/inventory-control.warehouse-group-member.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
