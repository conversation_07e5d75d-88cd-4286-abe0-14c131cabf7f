openapi: 3.0.0
info:
  title: inventory-control-cycle
  description: inventory-control.cycle API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Inventory cycles
    description: Create and use inventory cycles to keep track of when to take physical inventory.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/inventory-control/cycle:
    get:
      summary: List inventory cycles
      description: Returns a collection with a key, ID, and link for each inventory cycle.
      tags:
        - Inventory cycles
      operationId: list-inventory-control-cycle
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of inventory-cycle objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List inventory cycles:
                  value:
                    'ia::result':
                      - key: '145'
                        id: A12
                        href: /objects/inventory-control/cycle/45
                      - key: '146'
                        id: A14
                        href: /objects/inventory-control/cycle/46
                      - key: '147'
                        id: A16
                        href: /objects/inventory-control/cycle/47
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
        '400':
          $ref: '#/components/responses/400error'
  /objects/inventory-control/cycle/{key}:
    parameters:
      - name: key
        description: System-assigned unique key for the inventory cycle.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get an inventory cycle
      description: Returns detailed information for a specified inventory cycle.
      tags:
        - Inventory cycles
      operationId: get-inventory-control-cycle-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the inventory-cycle
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/inventory-control-cycle'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get an inventory cycle:
                  value:
                    'ia::result':
                      key: '45'
                      id: Weekly Cycle
                      description: Weekly inventory cycle
                      status: active
                      audit:
                        createdDateTime: '2024-05-07T01:15:23Z'
                        modifiedDateTime: '2024-05-07T01:15:23Z'
                        createdBy: '13'
                        modifiedBy: '13'
                      href: /objects/inventory-control/cycle/45
                    'ia::meta':
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete an inventory cycle
      description: Deletes an inventory cycle.
      tags:
        - Inventory cycles
      operationId: delete-inventory-control-inventory-cycle-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    inventory-control-cycle:
      $ref: ../models/inventory-control.cycle.s1.schema.yaml
    inventory-control-cycleRequiredProperties:
      type: object
      required:
        - id
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml