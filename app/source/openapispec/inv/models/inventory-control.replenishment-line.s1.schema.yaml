title: inventory-control-replenishment-line
x-mappedTo: ReplenishDetail
x-ownedBy: inventory-control/replenishment
type: object
description: Detail information for items available for replenishment.
properties:
  key:
    type: string
    description: System-assigned key for the inventory-control-replenishment-line.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '3701'
  id:
    type: string
    description: Replenishment detail ID.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '3701'
  href:
    type: string
    description: Endpoint for the inventory-control-replenishment-line.
    readOnly: true
    example: /objects/inventory-control/replenishment-line/3701
  parentReplenishment:
    type: object
    x-mappedTo: Replenishment
    x-object: inventory-control/replenishment
    description: Header information for items available for replenishment and selected for a run in inventory.
    properties:
      key:
        type: string
        description: System-assigned key for the inventory-control-replenishment snapshot.
        readOnly: true
        x-mappedTo: SUMMARY_RECORDNO
        example: '12'
      id:
        type: string
        description: Replenishment snapshot ID.
        readOnly: true
        x-mappedTo: SUMMARY_RECORDNO
        example: '12'
      href:
        type: string
        description: Endpoint for the inventory-control-replenishment snapshot.
        readOnly: true
        example: /objects/inventory-control/replenishment/12
      name:
        type: string
        readOnly: true
        description: Snapshot name.
        x-mappedTo: SNAPSHOT_NAME
        example: LEGO as as of 04/02/2024
  proposedPurchaseOrder:
    type: string
    description: Temporary PO number.
    x-mappedTo: PROPOSED_PO
    example: '1'
  item:
    type: object
    x-mappedTo: icitem
    x-object: inventory-control/item
    description: Item to be replenished.
    properties:
      key:
        type: string
        description: System-assigned key for the replenishment item.
        x-mappedTo: ITEMDIMKEY
        example: '34'
      id:
        type: string
        description: Unique Identifier of the replenishment item.
        x-mappedTo: ITEMID
        example: 10258LEGO
      href:
        type: string
        readOnly: true
        description: Endpoint for the replenishment item.
        example: /objects/inventory-control/item/34
      name:
        type: string
        readOnly: true
        description: Name of the replenishment item.
        x-mappedTo: ITEMNAME
        example: 10258LEGO
  vendor:
    type: object
    x-mappedTo: vendor
    x-object: accounts-payable/vendor
    description: Vendor for the item to be replenished.
    properties:
      key:
        type: string
        description: System-assigned key for the vendor.
        readOnly: true
        x-mappedTo: VENDORDIMKEY
        example: '48'
      id:
        type: string
        readOnly: true
        description: Unique Identifier of the vendor.
        x-mappedTo: VENDORID
        example: ABC
      href:
        type: string
        readOnly: true
        description: Endpoint for the vendor.
        example: /objects/accounts-payable/vendor/48
      name:
        type: string
        readOnly: true
        description: Name of the vendor.
        x-mappedTo: VENDORNAME
        example: ABC Distribution
  productLine:
    type: object
    description: Product line of this replenishment item.
    x-object: inventory-control/product-line
    x-mappedTo: productline
    title: productLineId
    properties:
      key:
        type: string
        description: System-assigned key for the product line.
        x-mappedTo: PRODUCTLINEKEY
        example: '28'
      id:
        type: string
        description: Unique identifier for the product line.
        x-mappedTo: PRODUCTLINEID
        example: Electronics
      href:
        type: string
        readOnly: true
        example: /objects/inventory-control/product-line/28
  replenishmentMethod:
    type: string
    readOnly: true
    description: |
      Default replenishment method of this item.Specifies how the amount to reorder is calculated.
      reorderPoint - base calculation on a specific reorder quantity and optional safety stock quantity.
      demandForecastBySingleValue - base calculation on lead time for the vendor Demand.
      demandForecastByFluctuatingValues - When the expected demand for an inventory item fluctuates greatly over time, fluctuating demand forecast si used to more accurately predict the inventory need.
    x-mappedTo: REPLENISHMENT_METHOD
    example: reorderPoint
    enum:
      - ''
      - demandForecastBySingleValue
      - reorderPoint
      - demandForecastByFluctuatingValues
    x-mappedToValues:
      - ''
      - FORECAST_DEMAND
      - REORDER_POINT
      - FORECAST_TABLE
    default: ''
  safetyStockQuantity:
    type: string
    format: decimal-precision-10
    readOnly: true
    description: The extra quantity of the item held in inventory to reduce the risk of stock outs due to uncertainty in supply and demand.
    x-mappedTo: SAFETY_STOCK
    example: '1.0000000000'
  currentOnHandQuantity:
    type: string
    format: decimal-precision-10
    readOnly: true
    description: On hand quantity based on the orders already placed.
    x-mappedTo: CURRENT_ON_HAND
    example: '0.0'
  currentOnOrderQuantity:
    type: string
    format: decimal-precision-10
    readOnly: true
    description: On order quantity based on the orders already placed.
    x-mappedTo: CURRENT_ON_ORDER
    example:  '0.0'
  currentOnHoldQuantity:
    type: string
    format: decimal-precision-10
    readOnly: true
    description: On hold quantity based on the orders already placed.
    x-mappedTo: CURRENT_ON_HOLD
    example: '0.0'
  currentNetInventoryQuantity:
    type: string
    format: decimal-precision-10
    readOnly: true
    description: Current net inventory.
    x-mappedTo: CURRENT_NET_INVENTORY
    example: '0.0'
  vendorUnitOfMeasure:
    readOnly: true
    type: object
    description: The unit of measure the vendor sells the item in.
    x-object: inventory-control/unit-of-measure
    x-mappedTo: uomdetail
    properties:
      key:
        type: string
        description: System-assigned key for the unit.
        x-mappedTo: UNITKEY
        example: '3'
      id:
        type: string
        description: Unique identifier for the unit.
        x-mappedTo: UNITS_OF_MEASURE
        example: Each
      href:
        type: string
        readOnly: true
        description: URL for the unit.
        example: /objects/inventory-control/unit-of-measure/3
  itemUnitOfMeasure:
    type: object
    description: Default unit of measure of the item.
    x-object: inventory-control/unit-of-measure
    x-mappedTo: uomdetail
    properties:
      key:
        type: string
        description: System-assigned key for the unit.
        x-mappedTo: ITEM_UNITKEY
        example: '3'
      id:
        type: string
        description: Unique identifier for the unit.
        x-mappedTo: ITEM_UNITS_OF_MEASURE
        example: Each
      href:
        type: string
        readOnly: true
        description: URL for the unit.
        example: /objects/inventory-control/unit-of-measure/3
  vendorStockNumber:
    type: string
    readOnly: true
    description: Vendor's Stock number for this item.
    x-mappedTo: STOCK_NUMBER
    example: '234'
  txnCurrency:
    type: string
    readOnly: true
    description:  The currency for transactions with the vendor.
    x-mappedTo: CURRENCY
    example: USD
  leadTimeDays:
    type: integer
    readOnly: true
    description: The number of days it takes the vendor to deliver the item after receiving the order.
    x-mappedTo: LEAD_TIME
    example: '10'
  forecastDemandQuantityInLeadTime:
    type: string
    format: decimal-precision-10
    readOnly: true
    description: Demand forecast during the lead time.
    x-mappedTo: FORECAST_DEMAND_IN_LEAD_TIME
    example: '5'
  inventoryNeedQuantity:
    type: string
    format: decimal-precision-10
    readOnly: true
    description: Inventory need is sum of Demand forecast + Safety stock.
    x-mappedTo: INVENTORY_NEED
    example: '2.0'
  futurePOTxnQuantity:
    type: string
    format: decimal-precision-10
    readOnly: true
    description: Net inventory activity for future Purchasing transactions for the item after the As of date and within the lead time.On hand + On Order - On hold.
    x-mappedTo: FUTURE_PO
    example: '0.0'
  futureOETxnQuantity:
    type: string
    format: decimal-precision-10
    readOnly: true
    description: Net inventory activity for future Order Entry transactions for the item after the As of date and within the lead time.On hand + On Order - On hold.
    x-mappedTo: FUTURE_OE
    example: '0.0'
  futureICTxnQuantity:
    type: string
    format: decimal-precision-10
    readOnly: true
    description: Net inventory activity for future Inventory Control transactions for the item after the As of date and within the lead time.On hand + On Order - On hold.
    x-mappedTo: FUTURE_IC
    example: '0.0'
  futureActivityQuantity:
    type: string
    format: decimal-precision-10
    readOnly: true
    description: Quantity for the future activity. Sum of futurePOTxnQuantity, futureOETxnQuantity and futureICTxnQuantity.
    x-mappedTo: FUTURE_ACTIVITY
    example: '0.0'
  kitNeedsQuantity:
    type: string
    format: decimal-precision-10
    readOnly: true
    description: The quantity of the item needed to build stockable kits that include the item as a component.
      If a stockable kit needs replenishing, Sage Intacct uses the replenishment information for each inventory item in the kit to suggest which items and how many to order.
    x-mappedTo: KIT_NEEDS
    example: '0.0'
  needToPurchaseQuantity:
    type: string
    format: decimal-precision-10
    readOnly: true
    description: Need to purchase quantity = inventoryNeedQuantity - currentNetInventoryQuantity - futureActivityQuantity + kitNeedsQuantity.
    x-mappedTo: NEED_TO_PURCHASE
    example: '2.0'
  economicOrderQuantity:
    type: string
    format: decimal-precision-10
    readOnly: true
    description: The quantity that makes the cost of reordering this item the most economical.
    x-mappedTo: ECONOMIC_ORDER_QTY
    example: '1.0'
  reorderQuantity:
    type: string
    format: decimal-precision-10
    readOnly: true
    description: The quantity to reorder when the item needs reordering.
    x-mappedTo: REORDER_QTY
    example: '1.0'
  roundedUpQuantity:
    type: string
    format: decimal-precision-10
    readOnly: true
    description:  A calculated field that ensures the suggested quantity to purchase is the economic reorder quantity or a multiple of the economic order quantity:.
    x-mappedTo: ROUND_UP
    example: '2.0'
  reorderPointQuantity:
    type: string
    format: decimal-precision-10
    readOnly: true
    description: The quantity of inventory of the item that you do not want the item to fall below. 
      When the currentNetInventoryQuantity falls to the reorder point plus the safety stock, the item is triggered for reorder.
    x-mappedTo: REORDER_POINT
    example: '10.0'
  minOrderQuantity:
    type: string
    format: decimal-precision-10
    readOnly: true
    description: The minimum quantity of this item the vendor is willing to sell.
    x-mappedTo: MIN_ORDER
    example: '0.0'
  maxOrderQuantity:
    type: string
    format: decimal-precision-10
    readOnly: true
    description: The maximum quantity that can be ordered.
    x-mappedTo: MAX_ORDER_QTY
    example: '0.0'
  quantityToPurchase:
    type: string
    format: decimal-precision-10
    description: The suggested number of units to reorder, which is in the UOM in which the vendor sells the item.
    x-mappedTo: QUANTITY_TO_PURCHASE
    example: '2.0'
  purchasePrice:
    type: string
    format: decimal-precision-10
    description: The number of base units in the selected unit of measure multiplied by the purchase price per unit identified for the item,
      after all the price lists have been evaluated.
    x-mappedTo: PURCHASE_PRICE
    example: '5.0'
  extendedPrice:
    type: string
    format: decimal-precision-10
    readOnly: true
    description: The Purchase price multiplied by the Quantity to purchase.
    x-mappedTo: EXTENDED_PRICE
    example: '10.0'
  exchangeRate:
    type: string
    format: decimal-precision-10
    readOnly: true
    description: Exchange rate for the base currency.
    x-mappedTo: EXCHANGE_RATE
    example: '1.0'
  extendedPriceBase:
    type: string
    format: decimal-precision-10
    readOnly: true
    description: Extended price amount in base currency.
    x-mappedTo: EXTENDED_PRICE_BASE
    example: '10.0'
  deliverToContact:
    type: object
    description: Deliver to contact for the purchase order.
    x-object: company-config/contact
    x-mappedTo: DELIVERTO
    properties:
      key:
        type: string
        description: System-assigned key for the contact.
        x-mappedTo: DELIVERTOSYSKEY
        example: '708'
      id:
        type: string
        description: Identifier for the deliverTo contact.
        x-mappedTo: DELIVERTOKEY
        example: KILEO
      href:
        type: string
        description: URL for the deliverTo contact.
        readOnly: true
        example: /objects/company-config/contact/708
  warehouse:
    type: object
    description: Warehouse for the Replenishment line item.
    x-object: inventory-control/warehouse
    x-mappedTo: WAREHOUSE
    properties:
      key:
        type: string
        description: Warehouse key.
        x-mappedTo: WAREHOUSEDIMKEY
        example: '48'
      id:
        type: string
        description: Warehouse ID.
        x-mappedTo: WAREHOUSE
        example: W1
      href:
        type: string
        description: Endpoint for the warehouse.
        readOnly: true
        example: /objects/inventory-control/warehouse/48
      name:
        type: string
        description: Warehouse name.
        x-mappedTo: WAREHOUSENAME
        example: W1--W1 - WH10001
  department:
    type: object
    description: Department for the Replenishment line item.
    x-object: company-config/department
    x-mappedTo: DEPT
    properties:
      key:
        type: string
        description: System-assigned key for the Department.
        x-mappedTo: DEPARTMENTDIMKEY
        example: '6'
      id:
        type: string
        description: Identifier for the Department.
        x-mappedTo: DEPARTMENT
        example: Marketing
      href:
        type: string
        description: Endpoint for the Department.
        readOnly: true
        example: /objects/company-config/department/6
  location:
    type: object
    description: Replenishment item warehouse location.
    x-object: company-config/location
    x-mappedTo: LOCATION
    properties:
      key:
        type: string
        description: System-assigned key for the Location.
        x-mappedTo: LOCATIONKEY
        example: '1'
      id:
        type: string
        description: Unique identifier for the Location.
        x-mappedTo: LOCATIONKEY
        example: '1'
      name:
        type: string
        description: Location Name.
        x-mappedTo: LOCATION_NAME
        example: 1--United States of America
      href:
        type: string
        description: Endpoint for the location.
        readOnly: true
        example: /objects/company-config/location/1
  vendorTerm:
    type: object
    description: The terms extended by the vendor.
    x-object: accounts-payable/term
    x-mappedTo: APTERM
    properties:
      key:
        type: string
        description: System generated key for the Vendor term.
        x-mappedTo: TERMSKEY
        nullable: true
        example: '32'
      id:
        type: string
        description: Unique identifier for the Vendor term.
        x-mappedTo: TERMNAME
        nullable: true
        example: Net 30
      href:
        type: string
        readOnly: true
        description: Endpoint for the Vendor term.
        example: /objects/accounts-payable/term/32
