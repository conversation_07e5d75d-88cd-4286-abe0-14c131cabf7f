title: inventory-control-warehouse-transfer
x-mappedTo: ictransfer
type: object
description: Use warehouse transfers to move items between warehouses or to transfer items between bins in the same warehouse.
properties:
  key:
    type: string
    description: System-assigned key for the warehouse transfer.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '19'
  id:
    type: string
    description: Document number for the warehouse transfer.
    readOnly: true
    x-mappedTo: DOCNO
    example: IC:0360:doc
  href:
    type: string
    description: URL endpoint for the warehouse transfer.
    readOnly: true
    example: /objects/inventory-control/warehouse-transfer/19
  transactionDate:
    type: string
    format: date
    description: The date of the transaction. For immediate transfers, the date is used as the transaction date for the system-generated transfer-out and transfer-in transactions.
    x-mappedTo: TRANSACTIONDATE
    example: '2024-03-01'
  description:
    type: string
    description: Information about the warehouse transfer. The description is displayed in the Warehouse Transfer list.
    x-mappedTo: DESCRIPTION
    example: transfer to  10001-F per SO-0007
  referenceNumber:
    type: string
    description: Reference number for the transfer that might be useful in searches or reports. The number is displayed in system-generated transfer-out and transfer-in transactions.
    x-mappedTo: REFERENCENO
    example: SO-0007
  exchangeRate:
    type: object
    description: Exchange rate information for the transfer transaction.
    properties:
      outCurrency:
        type: string
        description: Base currency of the warehouse from which items are to be transferred.
        x-mappedTo: OUT_CURRENCY
        example: GBP
      inCurrency:
        type: string
        description: Base currency of the warehouse to which items are to be transferred.
        x-mappedTo: IN_CURRENCY
        example: USD
      date:
        type: string
        format: date
        description: Exchange rate date for the receiving warehouse. Should be the same date as the `transactionDate`.
        x-mappedTo: EXCH_RATE_DATE
        example: '2024-03-01'
      rate:
        type: string
        format: decimal-precision-10
        description: Exchange rate to use for the transaction. Do not provide a value in this field if the exchange rate `typeID` and `typeName` are set.
        x-mappedTo: EXCHANGE_RATE
        example: '1.0000000000'
      typeId:
        type: string
        description: Unique ID for the exchange rate type. Do not provide a value in this field if `rate` is set. If there are no custom exchange rates for the company, you can specify the ID for the Intacct Daily Rate.
        readOnly: true
        x-mappedTo: EXCH_RATE_TYPE_KEY
        example: '-1'
      typeName:
        type: string
        description: Name of the exchange rate type. If there are no custom exchange rates for the company, you can specify the Intacct Daily Rate. Do not provide a value in this field if `rate` is set.
        x-mappedTo: EXCH_RATE_TYPE_ID
        example: Intacct Daily Rate
  transferType:
    type: string
    description: |
     Specify whether this is an immediate or in-transit transfer.
     * An `immediate` transfer does not accommodate for the time it takes to move inventory from one warehouse to another. With an immediate transfer, ONHAND quantities are decreased at the source warehouse (the from warehouse) and increased at the destination warehouse (the to warehouse) at the same time.
     * An `inTransit` transfer allows you to accommodate for the time it takes to move inventory between warehouses For example, warehouses may be across the city, across the country, or in different countries. While inventory is being moved, it's not in the ONHAND quantities at either the source warehouse (the from warehouse) or the destination warehouse (the to warehouse). Instead it's counted in the INTRANSIT quantity for the destination warehouse.
     For more information, see [Warehouse transfers overview](https://www.intacct.com/ia/docs/en_US/help_action/Default.htm#cshid=Warehouse_transfer_overview) in the Sage Intacct Help Center.
    x-mappedTo: TRANSFERTYPE
    example: immediate
    enum:
      - immediate
      - inTransit
    x-mappedToValues:
      - Immediate
      - In transit
  action:
    type: string
    description: |
      Sets the state that the transaction is in. If `transferType` is set to: 
      * `immediate`, valid values are `draft` or `post`.
      * `inTransit` and you are using a POST operation to create a warehouse transfer, valid values are `draft` or `transferOut`.
      * `inTransit` and you are using a PATCH operation to update a warehouse transfer with a `transferState` of `draft`, valid values for the `action` field are `draft` or `transferOut`.
      * `inTransit` and you are using a PATCH operation to update a warehouse transfer with a `transferState` of `inTransit`, the only valid value for the `action` field is `transferOut`.
      
      Note that you cannot use the `action` field to change the `transferState` of a transfer that is `posted`, and you cannot set an `inTransit` transfer back to `draft`.
    writeOnly: true
    x-mappedTo: ACTION
    example: draft
    enum:
      - draft
      - post
      - transferIn
      - transferOut
    x-mappedToValues:
      - Draft
      - Post
      - Transfer in
      - Transfer out
  transferState:
    type: string
    description: Shows the current state of the warehouse transfer.
    readOnly: true
    x-mappedTo: TRANSFERSTATE
    example: draft
    default: draft
    enum:
      - draft
      - inTransit
      - posted
    x-mappedToValues:
      - Draft
      - In transit
      - Posted
  outDate:
    type: string
    format: date
    description: Estimated date that the shipment will leave the originating warehouse. This date is required when `transferType` is set to `inTransit`. This date cannot be earlier than the `transactionDate`.
    x-mappedTo: OUTDATE
    example: '2024-03-01'
  inDate:
    type: string
    format: date
    description: Estimated date that the shipment arrives at the receiving warehouse. This date is required when `transferType` is set to `inTransit`. This date cannot be earlier than the `outDate`.
    x-mappedTo: INDATE
    example: '2024-03-01'
  inDocument:
    type: object
    x-mappedTo: DOCUMENT
    x-object: inventory-control/document
    description: Reference to the transfer-in inventory transfer document.
    properties:
      key:
        type: string
        readOnly: true
        description: Unique key for the transfer-in document.
        x-mappedTo: INDOCHDRKEY
        example: '351'
      id:
        type: string
        readOnly: true
        description: Unique ID for the transfer-in document.
        x-mappedTo: IN_DOCID
        example: SYS-Warehouse Transfer In-IC:0360:doc-In
      href:
        type: string
        description: URL endpoint for the transfer-in document.
        readOnly: true
        example: /objects/inventory-control/document/351
  inTransitDocument:
    type: object
    x-mappedTo: DOCUMENT
    x-object: inventory-control/document
    description: Reference to the in-transit inventory transfer document.
    properties:
      key:
        type: string
        readOnly: true
        description: Unique key for the in-transit document.
        x-mappedTo: INTRANSITDOCHDRKEY
        example: '346'
      id:
        type: string
        readOnly: true
        description: Unique ID for the in-transit document.
        x-mappedTo: INTRANSIT_DOCID
        example: SYS-Warehouse In Transit-IC:0358:doc-Intransit
      href:
        type: string
        description: URL endpoint for the in-transit document.
        readOnly: true
        example: /objects/inventory-control/document/346
  outDocument:
    type: object
    x-mappedTo: DOCUMENT
    x-object: inventory-control/document
    description: Reference to the transfer-out inventory transfer document.
    properties:
      key:
        type: string
        readOnly: true
        description: Unique key for the transfer-out document.
        x-mappedTo: OUTDOCHDRKEY
        example: '350'
      id:
        type: string
        readOnly: true
        description: Unique ID for the transfer-out document.
        x-mappedTo: OUT_DOCID
        example: SYS-Warehouse Transfer Out-IC:0360:doc-Out
      href:
        type: string
        readOnly: true
        description: URL endpoint for the transfer-out document.
        example: /objects/inventory-control/document/350
  lines:
    type: array
    description: Lines of the warehouse transfer document. Must be one or more matched pair with one outgoing and one incoming line. A combination of item, unit, and quantity are used to match each pair.
    x-mappedTo: ENTRIES
    x-object: inventory-control/warehouse-transfer-line
    items:
      $ref: ./inventory-control.warehouse-transfer-line.s1.schema.yaml
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml
  entity:
    $ref: ../../common/references/entity-ref.s1.schema.yaml
