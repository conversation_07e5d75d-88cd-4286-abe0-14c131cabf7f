fields:
  key:
    uiType: integer
    uiLabel: IA.RECORD_NUMBER
  invoiceRunDateTime:
    uiType: dateTime
    uiLabel: IA.INVOICE_RUN_DATE
  isOffline:
    uiType: enum
    uiLabel: IA.OFFLINE
    enumsLabels:
      - label: IA.TRUE
        value: true
      - label: IA.FALSE
        value: false
  state:
    uiType: enum
    uiLabel: IA.STATE
    enumsLabels:
      - label: IA.SUCCESS
        value: success
      - label: IA.FAILED
        value: failed
      - label: IA.IN_TRANSIT
        value: inTransit
      - label: IA.PARTIAL_SUCCESS
        value: partialSuccess

refs:
  entity:
    fields:
      id:
        uiType: text
        uiLabel: IA.ENTITY_ID
      key:
        uiType: integer
        uiLabel: IA.ENTITY_KEY
  invoiceTemplate:
    fields:
      name:
        uiType: text
        uiLabel: IA.INVOICE_TEMPLATE
  createdBy:
    fields:
      id:
        uiType: text
        uiLabel: IA.CREATED_BY
