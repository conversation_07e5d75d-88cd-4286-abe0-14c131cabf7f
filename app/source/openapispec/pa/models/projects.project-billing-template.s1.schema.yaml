title: projects-project-billing-template
x-mappedTo: billingtemplate
type: object
description: Project billing template.
properties:
  key:
    type: string
    description: System-assigned key for the project billing template.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  id:
    type: string
    description: Billing template ID (same as key).
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  href:
    type: string
    description: Endpoint for the project billing template.
    readOnly: true
    example: /objects/projects/project-billing-template/23
  name:
    type: string
    description: Unique name of the billing template.
    x-mappedTo: TEMPLATEID
    x-mutable: false
    example: Project Estimated Hours
  description:
    type: string
    description: Project billing template description.
    x-mappedTo: DESCRIPTION
    example: Template for billing by estimated project hours
    nullable: false
  billingMethod:
    type: string
    description: Billing method.
    x-mappedTo: BILLINGMETHOD
    example: percentCompleted
    enum:
      - 'percentCompleted'
      - 'milestone'
    x-mappedToValues:
      - 'Percent Completed'
      - 'Milestone'
    default: percentCompleted
  calculateOn:
    type: string
    description: Calculate on.
    x-mappedTo: CALCMETHOD
    example: project
    enum:
      - 'project'
      - 'task'
    x-mappedToValues:
      - 'Project'
      - 'Task'
    default: 'project'
  basedOn:
    type: string
    description: Based on.
    x-mappedTo: CALCHOURS
    example: 'plannedHours'
    enum:
      - 'plannedHours'
      - 'estimatedHours'
      - 'budgetedHours'
      - 'observedPercentCompleted'
    x-mappedToValues:
      - 'Planned Hours'
      - 'Estimated Hours'
      - 'Budgeted Hours'
      - 'Observed % Completed'
    default: 'estimatedHours'
  status:
    $ref: ../../common/models/status.s1.schema.yaml
  milestones:
    type: array
    description: Line items of the billing template.
    x-mappedTo: BILLINGTEMPLATEENTRY
    x-object: projects/project-billing-template-milestone
    items:
      $ref: projects.project-billing-template-milestone.s1.schema.yaml
  entity:
    $ref: ../../common/references/entity-ref.s1.schema.yaml