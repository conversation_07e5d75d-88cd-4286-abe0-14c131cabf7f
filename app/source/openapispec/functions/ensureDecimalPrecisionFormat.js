export default (item, _, ctx) => {

    const totalOrAmountRegex = /(total|amount)(?!count|success|error)/i;

    var isTotalOrAmountProperty = ctx.path[ctx.path.length - 1].match(totalOrAmountRegex);

    if (isTotalOrAmountProperty) {
        if (
            item.format !== undefined &&
            ( item.format === 'decimal-precision-2' || item.format === 'decimal-precision-4')
        )
        { return; }
        else {
            return [
                {
                    message: `${ctx.path ? ctx.path.join('.') : 'property'} property must have 'format: decimal-precision-\<N\>'`,
                }
            ];
        }
    }
};
