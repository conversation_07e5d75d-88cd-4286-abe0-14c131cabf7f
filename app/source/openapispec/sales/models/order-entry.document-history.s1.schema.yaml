title: order-entry-document-history
x-mappedTo: sodochistory
x-ownedBy: order-entry/document
type: object
description: History of workflow conversions associated with the Order Entry transaction.
properties:
  key:
    type: string
    description: System-assigned key for the Order Entry document history
    readOnly: true
    x-mappedTo: RECORDNO
    example: '11'
  id:
    type: string
    description: System-assigned ID for the Order Entry document history
    readOnly: true
    x-mappedTo: RECORDNO
    example: '11'
  href:
    type: string
    description: Endpoint for the Order Entry document history.
    readOnly: true
    example: /objects/order-entry/document-history/11
  convertedFrom:
    title: order-entry-document
    type: object
    description: The transaction from which the corresponding Order Entry transaction was converted
    x-mappedTo: sodocument
    readOnly: true
    x-object: order-entry/document
    x-isOwner: false
    properties:
      key:
        type: string
        description: System-assigned key for the Order Entry document.
        x-mappedTo: SOURCEDOCKEY
        readOnly: true
        example: '22'
      id:
        type: string
        description: System-assigned ID for the Order Entry document.
        x-mappedTo: SOURCEDOCUMENTID
        readOnly: true
        example: 'Sales Order-ORINV#0182#doc'
      documentType:
        type: string
        description: Type of the order entry document.
        x-mappedTo: SOURCEDOCPARID
        x-useForDocType: true
        example: 'Sales Order'
        readOnly: true
      href:
        type: string
        description: URL for the Order Entry document.
        readOnly: true
        example: /objects/order-entry-document::Sales%20Order/22
  convertedTo:
    title: order-entry-document
    type: object
    description: The associated transaction in a workflow history
    x-mappedTo: sodocument
    readOnly: true
    x-object: order-entry/document
    x-isOwner: false
    properties:
      key:
        type: string
        description: System-assigned key for the Order Entry document.
        x-mappedTo: DOCKEY
        readOnly: true
        example: '14'
      id:
        type: string
        description: System-assigned ID for the Order Entry document.
        x-mappedTo: DOCUMENTID
        readOnly: true
        example: 'Sales Invoice-SUBINV#0182#doc'
      documentType:
        type: string
        description: Type of the order entry document.
        x-mappedTo: DOCPARID
        x-useForDocType: true
        example: 'Sales Invoice'
        readOnly: true
      href:
        type: string
        description: URL for the Order Entry document.
        readOnly: true
        example: /objects/order-entry-document::Sales%20Invoice/14
  orderEntryDocument:
    title: order-entry-document
    type: object
    description: The Order Entry document with which the history information is associated with
    x-mappedTo: sodocument
    readOnly: true
    x-object: order-entry/document
    properties:
      key:
        type: string
        description: System-assigned key for the Order Entry document.
        x-mappedTo: BASEDOCKEY
        readOnly: true
        example: '23'
      id:
        type: string
        description: Name or other unique identifier for the Order Entry document.
        x-mappedTo: BASEDOCUMENTID
        readOnly: true
        example: 'Sales Order-ORINV#0182#doc'
      documentType:
        type: string
        description: Type of the order entry document.
        x-mappedTo: BASEDOCPARID
        x-useForDocType: true
        example: 'Sales Order'
        readOnly: true
      href:
        type: string
        description: Endpoint for the document.
        readOnly: true
        example: /objects/order-entry-document::Sales%20Order/22