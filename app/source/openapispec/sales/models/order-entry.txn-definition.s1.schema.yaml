title: order-entry-txn-definition 
x-mappedTo: sodocumentparams
type: object
description: An Order Entry transaction definition is the template for a sales transaction. It contains the accounting rules, workflow settings, and other configuration settings that determine a transaction's behavior.
properties:
  key:
    type: string
    description: System-assigned unique key for the transaction definition.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  id:
    type: string
    description: The name for the transaction definition.
    x-mappedTo: DOCID
    x-mutable: false
    example: Sales Quote
  href:
    type: string
    description: URL endpoint for the transaction definition.
    readOnly: true
    example: /objects/order-entry/txn-definition/23
  docClass:
    type: string
    description: Order Entry transaction template type, which determines whether certain fields appear on a transaction.
    x-mappedTo: DOCCLASS
    example: quote
    enum:
      - quote
      - order
      - list
      - invoice
      - adjustment
      - other
    x-mappedToValues:
      - Quote
      - Order
      - List
      - Invoice
      - Adjustment
      - Other
    default: quote
  workflowCategory:
    type: string
    description: The workflow category associates the transaction with the applicable transaction menu grouping in the Order Entry menu.
    x-mappedTo: CATEGORY
    example: quote
    enum:
      - quote
      - order
      - shipping
      - invoice
      - return
    x-mappedToValues:
      - Quote
      - Order
      - Shipping
      - Invoice
      - Return
    default: quote
  description:
    type: string
    description: A meaningful description of the transaction definition. 
    x-mappedTo: DESCRIPTION
    example: Sales Return RevRec Document
  enableUserOrGroupPermission:
    type: boolean
    description: By default, all users with the appropriate application permissions have access to transactions that use this transaction definition. To restrict access to certain users or groups, set this field to `true`.
    x-mappedTo: USERPERM
    example: true
    readOnly: true
    x-mappedToType: string
    default: false
  enableUserOrGroupPermmission:
    type: boolean
    description: This field has been deprecated. Use the `enableUserOrGroupPermission` field instead.
    x-mappedTo: USERPERM
    readOnly: true
    x-mappedToType: string
    default: false
    deprecated: true
  editPolicy:
    type: string
    description: |
     Sets the edit permission for this transaction type.
     * `noEdit` - The user cannot edit the transaction after it is created.
     * `beforePrinting` - The user can edit the transaction until it has been printed to PDF. After it has been printed, the transaction cannot be edited.
     * `editInDraftOnly`- The user can edit the transaction until it has been posted. After it has been posted, the transaction cannot be edited.
     `all` - The user can edit the transaction as allowed by the transaction's condition. See [Editing a sales transaction](https://www.intacct.com/ia/docs/en_US/help_action/Order_Entry/Using_Order_Entry/Transactions/OE-transaction-lists.htm) for editing restrictions. 
    x-mappedTo: EDITTYPE
    example: noEdit
    enum:
      - noEdit
      - beforePrinting
      - editInDraftOnly
      - all
    x-mappedToValues:
      - No Edit
      - Before Printing
      - Edit Draft
      - All
    default: all
  deletePolicy:
    type: string
    description: |
     Sets the delete permission for this transaction type. By default, a user cannot delete a transaction after it is created.
     * `noDelete` - The user cannot delete the transaction after it is created.
     * `beforePrinting` - The user can delete the transaction until it has been printed to PDF. After it has been printed, the transaction cannot be deleted.
     * `all` - The user can delete the transaction as allowed by the transaction's condition. See [Editing a sales transaction](https://www.intacct.com/ia/docs/en_US/help_action/Order_Entry/Using_Order_Entry/Transactions/OE-transaction-lists.htm) for editing restrictions.
    x-mappedTo: DELTYPE
    example: all
    enum:
      - noDelete
      - beforePrinting
      - all
    x-mappedToValues:
      - No Delete
      - Before Printing
      - All
    default: noDelete
  enableNumberingSequence:
    type: boolean
    description: Set to `true` to enable numbering sequences to be set for transactions.
    x-mappedTo: ENABLE_SEQNUM
    example: false
    default: false
    x-mappedToType: string
  preserveNumberingSequence:
    type: boolean
    description: Set to `true` to ensure that numbers in a sequence are never skipped. Enabling this option can affect performance when a large volume of transactions is entered at the same time.
    x-mappedTo: PRESERVE_SEQNUM
    example: false
    default: false
    x-mappedToType: string
  inheritDocumentNumber:
    type: boolean
    description: Set to `true` if transactions should inherit source document numbers. For example, a transaction could inherit a sales invoice number.
    x-mappedTo: INHERIT_SOURCE_DOCNO
    example: false
    default: false
    x-mappedToType: string
  inventoryUpdateType:
    type: string
    description: Specify if inventory will be affected by the transaction and, if so, whether to track the quantity, value, or both.
    x-mappedTo: UPDATES_INV
    enum:
      - 'no'
      - quantity
      - value
      - quantityAndValue
    x-mappedToValues:
      - 'No'
      - Quantity
      - Value
      - Quantity and Value
    example: no
    default: no
  increaseOrDecreaseInventory:
    type: string
    description: Specify whether the inventory running total is increased or decreased by the transaction.
    x-mappedTo: IN_OUT
    example: increase
    enum:
      - increase
      - decrease
    x-mappedToValues:
      - Increase
      - Decrease
    default: increase
  txnPostingMethod:
    type: string
    description: Specify whether the transaction post process creates a GL entry and, if so, whether it will post directly to the GL or to the AR subledger.
    x-mappedTo: UPDATES_GL
    example: toGL
    enum:
      - toAR
      - toGL
      - noPosting
    x-mappedToValues:
      - A
      - G
      - N
    default: noPosting
  disableTax:
    type: boolean
    description: Set to `true` to disable tax capture for the transaction.
    x-mappedTo: DISABLEVAT
    example: false
    default: false
    x-mappedToType: string
  enableFulfillment:
    type: boolean
    description: Set to `true` to enable the transaction definition for fulfillment.
    x-mappedTo: ENABLEFULFILLMENT
    example: false
    default: false
    x-mappedToType: string
  enableReservingAndPicking:
    type: boolean
    description: Set to `true` to enable inventory items for reserving and picking.
    x-mappedTo: ENABLEALLOCRESERVE
    example: false
    default: false
    x-mappedToType: string
  partialConvertMethod:
    type: string
    description: |
     Specify how the system processes transactions that are partially complete.
     * `leaveTransactionOpen` - When the user converts the transaction to another transaction type, the system leaves the original transaction open until all items are completed.
     * `closeOriginalAndCreateBackOrder` - When the user converts the transaction to another transaction type and enters an item quantity that's less than the original transaction quantity, the system closes the original transaction and creates a new transaction for the remaining item quantity.
     * `closeTransaction` - When the user converts the transaction to another transaction type, the system closes the original transaction regardless of whether the transaction is complete.
     See [Convert part of a transaction](https://www.intacct.com/ia/docs/en_US/help_action/Order_Entry/Using_Order_Entry/Transactions/Convert_transactions/convert-part-of-a-sales-transaction.htm) in the Sage Intacct Help Center for more information.
    x-mappedTo: CONVTYPE
    example: closeTransaction
    enum:
      - leaveTransactionOpen
      - closeOriginalAndCreateBackOrder
      - closeTransaction
    x-mappedToValues:
      - Leave Transaction Open
      - Close Original and Create Back Order
      - Close Transaction
    default: closeTransaction
  affectsCost:
    type: boolean
    description: If true the transaction will affect the costing of order entry.
    x-mappedTo: UPDATES_COST
    example: false
    default: false
    x-mappedToType: string
  overrideExchangeRateType:
    type: boolean
    description: Indicates whether users can edit the Exchange rate and/or Exchange rate type fields of the transaction.
    x-mappedTo: OVERRIDE_EXCH_RATE_TYPE
    example: false
    default: true
    x-mappedToType: string
  showBaseCurrency:
    type: boolean
    description: |
     Specifies whether the transaction amounts are displayed in both base currency and transaction currency. The following fields display amounts in the currency selected as the transaction currency on a transaction: Price, Extended price, Transaction value, and Transaction total.
     If `showBaseCurrency` is set to `true`, the transaction currency amount fields appear and the following fields also appear: Base price, Extended base price, Base value, and Base total. These additional fields display the amounts in the company's base currency. 
    x-mappedTo: DISPLAY_BASECURRENCY
    example: false
    default: false
    x-mappedToType: string
  overridePrice:
    type: boolean
    description: Set to `true` to allow users to override line item prices on the transaction.
    x-mappedTo: OVERRIDE_PRICE
    example: false
    default: true
    x-mappedToType: string
  trackDiscountAndSurcharge:
    type: boolean
    description: Indicates whether users can discount transaction line items.
    x-mappedTo: ENABLE_DISCOUNT_CHARGE
    example: false
    default: false
    x-mappedToType: string
  allowDiscountOnExtendedPrice:
    type: boolean
    description: Indicates whether the transaction definition can be used for contract invoices. This field is only applicable if the company is subscribed to Contracts.
    x-mappedTo: DISCOUNT_ON_EXTENDEDPRICE
    example: false
    default: false
    x-mappedToType: string
  requireMemoForDiscount:
    type: boolean
    description: Indicates whether users must provide notes for line items being discounted before saving a transaction.
    x-mappedTo: REQUIRE_DISCOUNT_MEMO
    example: false
    default: false
    x-mappedToType: string
  displayDraftsOnRevenueTxnEntriesPage:
    type: boolean
    description: Indicates whether the transaction can be displayed on the Revenue transaction entries page when in a draft state.
    x-mappedTo: DISP_ON_REV_TXN_ENTRIES_PAGE
    example: false
    default: false
    x-mappedToType: string
  freezeRecallValue:
    type: boolean
    description: Set to `true` to freeze recalled values.
    x-mappedTo: RECALLONLY
    example: false
    default: false
    x-mappedToType: string
  enableSubtotals:
    type: boolean
    description: Set to `true` to specify standard subtotals, such as tax or a discount, for the transaction.
    x-mappedTo: SHOW_TOTALS
    example: false
    default: false
    x-mappedToType: string
  showExpandedTaxDetails:
    type: boolean
    description: Set to `true` to show expanded tax details in the transaction user interface and in printed output. This field applies only to companies that use Avalara AvaTax or Sage Intacct Advanced Tax.
    x-mappedTo: SHOWEXPANDEDTOTALS
    example: false
    default: false
    x-mappedToType: string
  enableOverrideTax:
    type: boolean
    description: Indicates whether users can override whether a line item is taxable.
    x-mappedTo: ENABLEOVERRIDETAX
    example: false
    default: false
    x-mappedToType: string
  revenueRecognitionType:
    type: string
    description: Specifies whether revenue recognition applies to transactions created from this definition and, if so, whether to post the revenue recognition or only display it. This field applies only to companies subscribed to Revenue Management and is applicable only to Order Entry revenue recognition.
    x-mappedTo: ENABLEREVREC
    example: post
    enum:
      - post
      - displayOnly
      - none
    x-mappedToValues:
      - Post
      - Display Only
      - None
    default: none
  revrecEnablementType:
    type: string
    description: This field has been deprecated. Use the `revenueRecognitionType` field instead.
    x-mappedTo: ENABLEREVREC
    example: post
    enum:
      - post
      - displayOnly
      - none
    x-mappedToValues:
      - Post
      - Display Only
      - None
    default: none
    deprecated: true
  renewalEnablementType:
    type: string
    description: Specifies whether contract renewals apply to the transaction type and, if so, whether the renewal is set up to generate when the transaction posts or the renewal fields are displayed in the transaction so users can select a renewal transaction set.
    x-mappedTo: ENABLERENEWALS
    example: displayOnly
    enum:
      - generate
      - displayOnly
      - none
    x-mappedToValues:
      - Generate
      - Display Only
      - None
    default: none
  enableLineItemConversion:
    type: boolean
    description: Set to `true` to allow users to convert a sales transaction by line item into a scheduled recurring transaction.
    x-mappedTo: ENABLESCHEDULE
    example: false
    default: false
    x-mappedToType: string
  allowRenewConvertedLineOnly:
    type: boolean
    description: This field is only applicable if `renewalEnablementType` is set to `generate`. Set this field to `true` if the system should only renew line items that were converted in the original sales transaction. Set to `false` if all indicated line items should be renewed, regardless of conversion status.
    x-mappedTo: RENEWONLYCONVERTEDLINE
    example: false
    default: false
    x-mappedToType: string
  allowLocationOverride:
    type: boolean
    description: Set to 'true' to allow users to override the location set for the transaction.
    x-mappedTo: LOCOVERRIDE
    example: false
    default: false
    x-mappedToType: string
  allowDepartmentOverride:
    type: boolean
    description: Set to `true` to allow users to override the department set for the transaction.
    x-mappedTo: DEPTOVERRIDE
    example: false
    default: false
    x-mappedToType: string
  xslTemplate:
    type: string
    description: Document template to use for printed output (PDFs).
    x-mappedTo: XSLTEMPLATE
    example: 'sales'
  fixedMessage:
    type: string
    description: This message is displayed in the Print/Email document.
    x-mappedTo: FIXED_MESG
    example: For any inquiries or assistance, please contact our customer service.
  contactOneTitle:
    type: string
    description: Use this field to change the Bill-to labels on the transaction and on the printed document.
    x-mappedTo: CONTACTTITLE1
    example: Bill to
    default: Bill to
  contactTwoTitle:
    type: string
    description: Use this field to change the Ship-to labels on the transaction and on the printed document.
    x-mappedTo: CONTACTTITLE2
    example: Ship to
    default: Ship to
  printBillToContact:
    type: boolean
    description: Set to `true` to show the Bill-to contact in the printed document.
    x-mappedTo: SHOWTITLE1
    example: false
    default: false
    x-mappedToType: string
  printShipToContact:
    type: boolean
    description: Set to `true` to show the Ship-to contact in the printed document.
    x-mappedTo: SHOWTITLE2
    example: false
    default: false
    x-mappedToType: string
  allowEditingBillToContact:
    type: boolean
    description: Set to `true` to allow users to change the Bill-to contact during transaction entry.
    x-mappedTo: ALLOWEDITBILLTO
    example: false
    default: false
    x-mappedToType: string
  allowEditingShipToContact:
    type: boolean
    description: Set to `true` to allow users to change the Ship-to contact during transaction entry.
    x-mappedTo: ALLOWEDITSHIPTO
    example: false
    default: false
    x-mappedToType: string
  enableWarnOnLowQuantity:
    type: boolean
    description: Set to `true` to alert the user when the quantity needed is greater than the `ONHAND` total in the selected warehouse. The warning also shows the `ONORDER` and `ONHOLD` totals.
    x-mappedTo: WARNONLOWQTY
    example: false
    default: true
    x-mappedToType: string
  enableCreditLimitCheck:
    type: boolean
    description: Specifies whether to enforce customer credit limit restrictions in transactions created from this definition.
    x-mappedTo: CREDITLIMITCHECK
    example: false
    default: false
    x-mappedToType: string
  warehouseSelectionMethod:
    type: string
    description: Specifies how warehouses are presented to the user in the warehouse list in the transaction.
    x-mappedTo: WAREHOUSESELMETHOD
    example: sortByID
    enum:
      - sortByID
      - sortByName
      - warehouseWithAvailableInventory
      - useTheDefaultWarehouse
    x-mappedToValues:
      - Sort by ID
      - Sort by Name
      - Warehouse with Available Inventory
      - Use the default warehouse
    default: sortByID
  enablePayments:
    type: boolean
    description: Set to `true` to enable users to apply payments to an invoice in Order Entry and to set a recurring template to automatically charge a credit card when an invoice is generated (if the company is subscribed to Payment Services).
    x-mappedTo: ENABLEPAYMENTS
    example: false
    default: false
    x-mappedToType: string
  latestVersionKey:
    type: string
    description: System-assigned version key for the order entry transaction definition.
    readOnly: true
    x-mappedTo: LATESTVERSIONKEY
    example: '23'
  postToGL:
    type: boolean
    description: Set to `true` to enable additional posting to the General Ledger.
    x-mappedTo: POSTTOGL
    example: false
    default: false
    x-mappedToType: string
  requireSupplyOfPrices:
    type: boolean
    description: Set to `true` if a price must be supplied.
    x-mappedTo: FORCE_PRICES
    example: false
    default: false
    x-mappedToType: string
  enableCosting:
    type: boolean
    description: Set to `true` to enable costing for non-inventoried items.
    x-mappedTo: ENABLE_COSTING
    example: false
    default: false
    x-mappedToType: string
  documentConversionPolicy:
    type: string
    description: |
     Document conversion policy.
     * `newDocumentOnly` - The user can create the transaction as a standalone transaction (by selecting Add from a transaction list, or the plus icon next to the transaction definition in the Sage Intacct menu, or by copying an existing transaction, and so on).
     * `newDocumentOrConvert` - The user can create the transaction as a standalone transaction or the user can create the transaction by converting the previous transaction in the workflow.
     * `convertOnly` - The user can only create the transaction by converting the previous transaction in the workflow. For example, the user can only create a sales invoice by converting an existing sales order. 
    x-mappedTo: CREATETYPE
    example: convertOnly
    enum:
      - null
      - newDocumentOnly
      - newDocumentOrConvert
      - convertOnly
    x-mappedToValues:
      - ''
      - New document only
      - New document or Convert
      - Convert only
    nullable: true
    default: null
  multiEntityRuleForTransaction:
    type: string
    description: Sets the context for where users can create transactions from this transaction definition within a multi-entity, shared environment.
    x-mappedTo: TD_CREATION_RULE
    example: topLevelOnly
    enum:
      - topLevelOrEntity
      - topLevelOnly
      - entityOnly
    x-mappedToValues:
      - Top level or Entity
      - Top level only
      - Entity only
    default: topLevelOrEntity
  enableProjectActualBillings:
    type: boolean
    description: Set to `true` to enable display of actual billings on printed or emailed invoices.
    x-mappedTo: ENABLEPROJECTACTUALBILLINGS
    example: false
    default: false
    x-mappedToType: string
  lineLevelSimpleTax:
    type: boolean
    description: Set to `true` to allow the tax rate to be overridden at the line level when using Simple Tax. This option appears only if `enableSubtotals` is set to `true`.
    x-mappedTo: LINELEVELSIMPLETAX
    example: false
    default: false
    x-mappedToType: string
  customerVendorEditRule:
    type: string
    description: |
     Allow users to change the customer in Draft or Pending transactions if the transaction has not been converted to another transaction, the transaction date is in an open period, and the transaction's payment status is not `Paid` or `Partially paid`.
     This field follows any editing restrictions imposed by the `editPolicy` field and is only applicable if the edit policy is set to `all` or `beforePrinting`. 
     * `always` - The user can edit the customer in all applicable scenarios.
     * `exceptConvertedDocuments` - The user can edit the customer in the first transaction in a workflow but not in transactions created by conversion.
     * `never` - The user cannot edit the customer. 
    x-mappedTo: ALLOWEDITCUSTVEND
    example: always
    enum:
      - null
      - always
      - exceptConvertedDocuments
      - never
    x-mappedToValues:
      - ''
      - Always
      - Except converted documents
      - Never
    nullable: true
    default: null
  enableRetainage:
    type: boolean
    description: For companies subscribed to Construction, set to 'true' to enable retainage.
    x-mappedTo: ENABLE_RETAINAGE
    example: false
    default: false
    x-mappedToType: string
  enableAdditionalInformationScope:
    type: boolean
    description: For companies subscribed to Construction, set to 'true' to enable identification of work that is included and excluded, the reason why, and the terms.
    x-mappedTo: ENABLEADDINFOSCOPE
    example: false
    default: false
    x-mappedToType: string
  enableAdditionalInformationSchedule:
    type: boolean
    description: For companies subscribed to Construction, set to 'true' to enable identification of dates for work milestones.
    x-mappedTo: ENABLEADDINFOSCHEDULE
    example: false
    default: false
    x-mappedToType: string
  enableInternalReference:
    type: boolean
    description: For companies subscribed to Construction, set to 'true' to enable identification of who authorized work internally and when they authorized the work.
    x-mappedTo: ENABLEADDINFOINTERNALREF
    example: false
    default: false
    x-mappedToType: string
  enableExternalReference:
    type: boolean
    description: For companies subscribed to Construction, set to 'true' to enable identification of who authorized work externally and when they authorized the work.
    x-mappedTo: ENABLEADDINFOEXTERNALREF
    example: false
    default: false
    x-mappedToType: string
  enableBond:
    type: boolean
    description: For companies subscribed to Construction, set to 'true' to identify information about performance and payment bonds.
    x-mappedTo: ENABLEADDINFOBOND
    example: false
    default: false
    x-mappedToType: string
  documentChangeType:
    type: string
    description: Indicates whether to enable changes to the transaction definition and, if so, how to handle changes.
    x-mappedTo: ENABLEDOCCHANGE
    example: enableChange
    enum:
      - noChange
      - enableChange
      - changeOrder
    x-mappedToValues:
      - No Change
      - Enable Change
      - Change Order
    default: noChange
  reportingCategory:
    type: string
    description: Establishes a reporting category that best reflects the purpose of the transaction definition so that similar documents can be grouped when building reports. This field is applicable for use with Interactive Custom Report Writer or Interactive Visual Explorer subscriptions only.
    x-mappedTo: REPORTINGCATEGORY
    example: salesQuotes
    enum:
      - null
      - salesQuotes
      - salesOrders
      - salesOrderChangeOrders
      - salesOrderInvoices
      - salesReturns
      - salesShippers
      - salesCredits
      - salesDebits
      - salesClearingShippers
      - contractBids
      - contracts
      - contractChangeOrders
      - contractInvoicePreview
      - contractInvoices
      - projectInvoices
      - revRecActivation
      - forecastRevenue
      - pledges
      - giftsAndDonations
      - pledgeAndGiftInvoices
      - grantApplications
      - grantAwardInvoices
      - eventReservations
      - eventConfirmations
      - conferencesAndGatherings
      - eventInvoices
      - membershipRegistrations
      - membershipInvoices
      - invoicePreview
      - invoices
      - tuitionRegistrations
      - tuitionInvoices
      - sponsorships
      - sponsorshipInvoices
      - subscriptions
      - subscriptionInvoices
      - reservations
      - pointOfSale
    x-mappedToValues:
      - ''
      - Sales Quotes
      - Sales Orders
      - Sales Order Change Orders
      - Sales Order Invoices
      - Sales Returns
      - Sales Shippers
      - Sales Credits
      - Sales Debits
      - Sales Clearing Shippers
      - Contract Bids
      - Contracts
      - Contract Change Orders
      - Contract Invoice Preview
      - Contract Invoices
      - Project Invoices
      - Rev Rec Activation
      - Forecast Revenue
      - Pledges
      - Gifts and Donations
      - Pledge and Gift Invoices
      - Grant Applications
      - Grant Award Invoices
      - Event Reservations
      - Event Confirmations
      - Conferences and Gatherings
      - Event Invoices
      - Membership Registrations
      - Membership Invoices
      - Invoice Preview
      - Invoices
      - Tuition Registrations
      - Tuition Invoices
      - Sponsorships
      - Sponsorship Invoices
      - Subscriptions
      - Subscription Invoices
      - Reservations
      - Point of Sale
    nullable: true
    default: null
  enableContractBilling:
    type: boolean
    description: Set to `true` to display the project contract billing for transactions. This option also displays project contract billing details on Order Entry Contract invoices.
    x-mappedTo: ENABLECONTRACTBILLING
    example: false
    default: false
    x-mappedToType: string
  arPostingMethod:
    type: string
    description: Update method for project contract totals.
    x-mappedTo: ARPOSTINGMETHOD
    example: addition
    enum:
      - null
      - none
      - addition
    x-mappedToValues:
      - ''
      - None
      - Addition
    nullable: true
    default: null
  documentSequence:
    type: object
    x-mappedTo: seqnum
    description: Specify the document numbering sequence to use to automatically number transactions. Omit a numbering sequence if transactions are to be numbered manually or if they will inherit the source document number (`inheritDocumentNumber` is set to `true`). 
    x-object: company-config/document-sequence
    properties:
      key:
        type: string
        description: Unique key for the document numbering sequence.
        x-mappedTo: SEQNUMKEY
        example: "10"
      href:
        type: string
        description: URL endpoint for the document numbering sequence.
        readOnly: true
        example: /objects/company-config/document-sequence/10
      id:
        type: string
        description: ID of the document numbering sequence.
        x-mappedTo: SEQUENCE
        example: Adjustment Decrease
  initialPriceList:
    type: object
    x-mappedTo: sopricelist
    x-object: order-entry/price-list
    description: The first price list the system will evaluate when determining a suggested transaction price.
    properties:
      href:
        type: string
        description: URL endpoint for the price list.
        readOnly: true
        example: /objects/order-entry/price-list/6
      key:
        type: string
        description: Unique key for the price list.
        x-mappedTo: PRCLISTKEY
        example: '6'
      id:
        type: string
        description: ID for the price list.
        x-mappedTo: PRICELISTID
        example: Initial Price List
  updatePriceList:
    type: object
    x-mappedTo: sopricelist
    x-object: order-entry/price-list
    description: The price list that will be updated when a user saves a transaction.
    properties:
      href:
        type: string
        description: URL endpoint for the price list.
        readOnly: true
        example: /objects/order-entry/price-list/35
      key:
        type: string
        description: Unique key for the price list.
        x-mappedTo: UPDPRCLISTKEY
        example: '35'
      id:
        type: string
        description: The name of the price list.
        x-mappedTo: UPDATES_PRICELISTID
        example: OE Price List
  specialPriceList:
    type: object
    x-mappedTo: sopricelist
    x-object: order-entry/price-list
    description: If using layered price lists, this indicates a special price list (for example, a discounted price list or seasonal price list). The special price list takes precedence over the initial price list.
    properties:
      href:
        type: string
        description: URL endpoint for the price list.
        readOnly: true
        example: /objects/order-entry/price-list/15
      key:
        type: string
        description: Unique key for the price list.
        x-mappedTo: SPCLPRCLISTKEY
        example: '15'
      id:
        type: string
        description: ID for the price list.
        x-mappedTo: SPECIAL_PRICELISTID
        example: Special price list
  warehouse:
    type: object
    x-mappedTo: warehouse
    x-object: inventory-control/warehouse
    description: Specify the default warehouse. Applies only if the `warehouseSelectionMethod` is set to `useTheDefaultWarehouse`.
    properties:
      key:
        type: string
        description: Unique key for the warehouse.
        x-mappedTo: DEFAULTWHSEKEY
        example: '21'
      href:
        type: string
        description: URL endpoint for the warehouse.
        readOnly: true
        example: /objects/inventory-control/warehouse/21
      id:
        type: string
        description: Unique name or other ID for the warehouse.
        x-mappedTo: DEFAULT_WAREHOUSE
        example: Warehouse-001
  shippingMethod:
    type: object
    x-mappedTo: shipmethod
    x-object: accounts-receivable/shipping-method
    description: The default for how goods in the transaction are shipped to the customer. If a shipping method is specified for a customer, the customer shipping method takes precedence as the default in the transaction.
    properties:
      href:
        type: string
        description: URL endpoint for the shipping method.
        readOnly: true
        example: /objects/accounts-receivable/shipping-method/11
      key:
        type: string
        description: Unique key for the shipping method.
        x-mappedTo: SHIPVIAKEY
        example: '11'
      id:
        type: string
        description: Name of the shipping method.
        x-mappedTo: SHIPPINGMETHOD
        example: FedEx
  subtotalTemplate:
    type: object
    description: Specifies the template for populating the transaction subtotals table.
    x-mappedTo: sosubtotaltemplate
    x-object: order-entry/subtotal-template
    properties:
      href:
        type: string
        description: URL endpoint for subtotal template.
        readOnly: true
        example: /objects/order-entry/subtotal-template/22
      key:
        type: string
        description: Unique key for the subtotal template.
        x-mappedTo: SUBTOTALTEMPLATEKEY
        example: '22'
      id:
        type: string
        description: Name of the subtotal template.
        x-mappedTo: SUBTOTALTEMPLATE
        example: Subtotal template
  arTerm:
    type: object
    description: Accounts Receivable (AR) term, which determines the expiration date for quotes and the due date for invoices.
    x-mappedTo: arterm
    x-object: accounts-receivable/term
    properties:
      href:
        type: string
        description: URL endpoint for the AR term.
        readOnly: true
        example: /objects/accounts-receivable/term/41
      key:
        type: string
        description: Unique key for the AR term.
        x-mappedTo: TERMKEY
        example: '41'
      id:
        type: string
        description: The name of AR term.
        x-mappedTo: TERM_NAME
        example: 10 Days
  exchangeRateType:
    type: object
    description: Specify a custom exchange rate type if transactions should not default to the Sage Intacct Daily Rate.
    x-mappedTo: exchangeratetype
    x-object: company-config/exchange-rate-type
    properties:
      key:
        type: string
        description: Unique key for the exchange rate type.
        x-mappedTo: EXCH_RATE_TYPE_ID
        example: '-1'
      id:
        type: string
        description: The name of exchange rate type.
        x-mappedTo: EXCHRATETYPES.NAME
        example: Intacct Daily Rate
      href:
        type: string
        description: URL endpoint for the exchange rate type.
        readOnly: true
        example: /objects/company-config/exchange-rate-type/-1
  status:
    $ref: ../../common/models/status.s1.schema.yaml
  entity:
    $ref: ../../common/references/entity-ref.s1.schema.yaml
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml
  cogsGLDetail:
    type: array
    description: Specify the COGS GL accounts to debit and credit. For companies that do not use Inventory Control, these accounts are required when the `enableCosting` field is set to `true`.
    x-mappedTo: DOCPAR_INVGL
    x-object: order-entry/txn-definition-cogs-gl-detail
    items:
      $ref: order-entry.txn-definition-cogs-gl-detail.s1.schema.yaml
  COGSGLDetail:
    type: array
    description: This field has been deprecated. Use the `cogsGLDetail` field instead.
    x-mappedTo: DOCPAR_INVGL
    x-object: order-entry/txn-definition-cogs-gl-detail
    items:
      $ref: order-entry.txn-definition-cogs-gl-detail.s1.schema.yaml
    deprecated: true
  accountReceivableOrDirectGLDetail:
    type: array
    description: When the `txnPostingMethod` field is set to `toAR` or `toGL`, specify the AR or GL accounts to debit and credit.
    x-mappedTo: DOCPAR_PRGL
    x-object: order-entry/txn-definition-ar-direct-gl-detail
    items:
      $ref: order-entry.txn-definition-ar-direct-gl-detail.s1.schema.yaml
  additionalGLDetail:
    type: array
    description: When the `txnPostingMethod` field is set to `toAR` and the `postToGL` field is set to `true`, specify the GL account details for additional GL account posting. These accounts are used to reverse the previous transaction in the workflow.
    x-mappedTo: DOCPAR_ADDGL
    x-object: order-entry/txn-definition-additional-gl-detail
    items:
      $ref: order-entry.txn-definition-additional-gl-detail.s1.schema.yaml
  inventoryTotalDetail:
    type: array
    description: Specifies the inventory running total that will be affected by the transaction and how the total will be affected.
    x-mappedTo: DOCPAR_TOTALS
    x-object: order-entry/txn-definition-inventory-total-detail
    items:
      $ref: order-entry.txn-definition-inventory-total-detail.s1.schema.yaml
  subtotalDetail:
    type: array
    description: Provides details about the type of subtotals that are supported for the transaction.
    x-mappedTo: DOCPAR_SUBTOTAL
    x-object: order-entry/txn-definition-subtotal-detail
    items:
      $ref: order-entry.txn-definition-subtotal-detail.s1.schema.yaml
  sourceDocumentDetail:
    type: array
    description: Provides document conversion details for the transaction.
    x-mappedTo: DOCPAR_RECALLS
    x-object: order-entry/txn-definition-source-document-detail
    items:
      $ref: order-entry.txn-definition-source-document-detail.s1.schema.yaml
  entitySettingDetail:
    type: array
    description: Provides details about the entities that can create transactions, and also includes settings for each entity, such as numbering sequences and document templates.
    x-mappedTo: DOCPAR_ENTITY_PROPS
    x-object: order-entry/txn-definition-entity-setting-detail
    items:
      $ref: order-entry.txn-definition-entity-setting-detail.s1.schema.yaml
