openapi: 3.0.0
info:
  title: order-entry-renewal-template
  description: order-entry.renewal-template API
  version: '1.0'
  contact:
    name: <PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Renewal templates
    description: A renewal template defines a set of actions Sage Intacct will perform when a contract reaches its end date.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/order-entry/renewal-template:
    get:
      summary: List renewal templates
      description: Returns a collection with a key, ID, and link for each order entry renewal template.
      tags:
        - Renewal templates
      operationId: list-order-entry-renewal-template
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of renewal templates
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List renewal templates:
                  value:
                    ia::result:
                      - key: '15'
                        id: '15'
                        href: "/objects/order-entry/renewal-template/19"
                      - key: '16'
                        id: '16'
                        href: "/objects/order-entry/renewal-template/20"
                      - key: '17'
                        id: '17'
                        href: "/objects/order-entry/renewal-template/21"
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create a renewal template
      description: Creates a new order entry renewal template.
      tags:
        - Renewal templates
      operationId: create-order-entry-renewal-template
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/order-entry-renewal-template'
                - $ref: '#/components/schemas/order-entry-renewal-templateRequiredProperties'
            examples:
              Creates a renewal template:
                value:
                  name: Testing1212qw1
                  description: kk
                  salesTxnCreation:
                    enableTxnCreation: true
                    transactionDefinition:
                      id: null
                      key: null
                    daysBeforeAfter: 9
                    beforeOrAfterContractEnd: after
                    txnDateOnRenewedDocument: contractEndDatePlusOneDay
                    txnLineItemStartDate: sameAsDocumentDate
                  contractPricing:
                    pricingType: defaultPricing
                    markup: percentageMarkup
                    markupValue: null
                  renewalNotifications:
                    customerEmail:
                      enableNotification: false
                      from: null
                      to: customerContact
                      daysBeforeAfter: 0
                      beforeOrAfterContractRenewal: before
                      emailTemplate:
                        id: null
                        key: null
                    internalEmail:
                      enableNotification: false
                      from: null
                      to: null
                      daysBeforeAfter: 10
                      beforeOrAfterContractRenewal: before
                      emailTemplate:
                        id: null
                        key: null
                  salesforceOpportunity:
                    enableSalesforceOpportunity: false
                    daysBeforeAfter: 10
                    beforeOrAfterContractRenewal: before
                  status: active
                  transactionType: contract
                  defaultTerm:
                    length: 2
                    period: months
                  renewalState: inProgress
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New order-entry-renewal-template
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                New renewal template:
                  value:
                    ia::result:
                       key: '30'
                       id: '30'
                       href: "/objects/order-entry/renewal-template/30"
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
  /objects/order-entry/renewal-template/{key}:
    parameters:
      - name: key
        description: System-assigned unique key for the renewal template.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a renewal template
      description: Returns detailed information for a specified order entry renewal template.
      tags:
        - Renewal templates
      operationId: get-order-entry-renewal-template-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the renewal template
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/order-entry-renewal-template'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get a renewal template:
                  value:
                    ia::result:
                      id: 24
                      key: 24
                      name: "Testing1212qw"
                      description: "kk"
                      salesTxnCreation:
                        enableTxnCreation: "true"
                        transactionDefinition:
                          id:
                          key:
                        daysBeforeAfter: 9
                        beforeOrAfterContractEnd: "after"
                        txnDateOnRenewedDocument: "contractEndDatePlusOneDay"
                        txnLineItemStartDate: "sameAsDocumentDate"
                      contractPricing:
                        pricingType: "defaultPricing"
                        markup: "percentageMarkup"
                        markupValue:
                      renewalNotifications:
                        customerEmail:
                          enableNotification: "false"
                          from:
                          to: "customerContact"
                          daysBeforeAfter: 0
                          beforeOrAfterContractRenewal: "before"
                          emailTemplate:
                            id:
                            key:
                        internalEmail:
                          enableNotification: "false"
                          from:
                          to:
                          daysBeforeAfter: 10
                          beforeOrAfterContractRenewal: "before"
                          emailTemplate:
                            id:
                            key:
                      salesforceOpportunity:
                        enableSalesforceOpportunity: "false"
                        daysBeforeAfter: 10
                        beforeOrAfterContractRenewal: "before"
                        renewalName:
                        inheritProductsFromParent: "false"
                        stage:
                      latestVersion:
                      status: "active"
                      transactionType: "contract"
                      defaultTerm:
                        length: 2
                        period: "months"
                      renewalState: "inProgress"
                      entity:
                        key:
                        id:
                        name:
                      href: "/objects/order-entry/renewal-template/24"
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update a renewal template
      description: Updates an existing order entry renewal template by setting field values. Any fields not provided remain unchanged.
      tags:
        - Renewal templates
      operationId: update-order-entry-renewal-template-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/order-entry-renewal-template'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Updates a renewal template:
                value:
                  description: 'Subscription Invoice RevRec Creation Markup price.-New'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated renewal template
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Updated renewal template:
                  value:
                   ia::result:
                       key: '30'
                       id: '30'
                       href: "/objects/order-entry/renewal-template/30"
                   ia::meta:
                     totalCount: 1
                     totalSuccess: 1
                     totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a renewal template
      description: Deletes an order entry renewal template.
      tags:
        - Renewal templates
      operationId: delete-order-entry-renewal-template-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    order-entry-renewal-template:
      $ref: ../models/order-entry.renewal-template.s1.schema.yaml
    order-entry-renewal-templateRequiredProperties:
      type: object
      required:
        - id
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml