title: purchasing-recurring-document-line
x-mappedTo: porecurdocumententry
x-ownedBy: purchasing/recurring-document
description: Line items in a recurring document represent recurring transactions.
type: object
properties:
  key:
    type: string
    description: System-assigned unique key for the recurring document line item.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '88'
  id:
    type: string
    description: System-assigned ID for the recurring document line item. This value is the same as the key for this object.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '88'
  href:
    type: string
    description: URL endpoint for the recurring document line.
    readOnly: true
    example: /objects/purchasing/recurring-document-line/88
  lineNumber:
    type: integer
    description: Line number for the document line.
    x-mappedTo: LINE_NO
    readOnly: true
    example: 1
  item:
    type: object
    description: Item associated with the recurring document line.
    x-object: inventory-control/item
    properties:
      key:
        type: string
        description: Unique key for the item.
        x-mappedTo: ITEM.RECORDNO
        example: '10'
      id:
        type: string
        description: ID for the item.
        readOnly: true
        x-mappedTo: ITEMID
        example: 'MX001'
      href:
        type: string
        description: URL endpoint for the item.
        readOnly: true
        example: /objects/inventory-control/item/10
  memo:
    type: string
    description: Memo about the recurring document line item.
    x-mappedTo: MEMO
    example: 'Payment ACH'
  unit:
    type: string
    description: Unit of measure associated with the recurring document line item.
    x-mappedTo: UNIT
    example: 'Each'
  unitQuantity:
    type: string
    format: decimal-precision-10
    description: Unit quantity associated with the document line item.
    x-mappedTo: UIQTY
    example: '10.10'
  unitPrice:
    type: string
    format: decimal-precision-10
    description: Unit price associated with the line item.
    x-mappedTo: UIPRICE
    example: '10.50'
  dimensions:
    type: object
    allOf:
      - $ref: ../../common/references/dimension-ref.s1.schema.yaml
      - type: object
        properties:
          location:
            title: location
            type: object
            x-object: company-config/location
            description: Location associated with the recurring document line.
            properties:
              key:
                type: string
                description: Unique key for the location.
                x-mappedTo: LOCATIONKEY
                example: '22'
              id:
                type: string
                description: ID for the location.
                x-mappedTo: LOCATIONID
                example: India
              href:
                type: string
                description: URL endpoint for the location.
                readOnly: true
                example: /objects/company-config/location/22
          department:
            type: object
            description: Department associated with the recurring document line.
            x-object: company-config/department
            x-mappedTo: department
            title: department
            properties:
              key:
                type: string
                description: Unique key for the department.
                x-mappedTo: DEPTKEY
                readOnly: true
                example: '9'
              id:
                type: string
                description: ID for the department.
                x-mappedTo: DEPARTMENTID
                example: Accounting
              href:
                type: string
                description: URL endpoint for the department.
                readOnly: true
                example: /objects/company-config/department/9
          warehouse:
            title: warehouse
            description: Warehouse associated with the recurring document line.
            type: object
            x-object: inventory-control/warehouse
            properties:
              key:
                type: string
                description: Unique key for the warehouse.
                x-mappedTo: WAREHOUSE.RECORDNO
                example: 19'
              id:
                type: string
                description: Unique ID for the warehouse.
                x-mappedTo: WAREHOUSE.LOCATION_NO 
                example: WareHouse10004
              href:
                type: string
                description: URL endpoint for the warehouse.
                readOnly: true
                example: /objects/inventory-control/warehouse/19
  discountPercent:
    type: string
    format: decimal-precision-10
    description: Discount percentage to apply to the document line item.
    x-mappedTo: DISCOUNTPERCENT
    example: '10.50'
  retailPrice:
    type: string
    format: decimal-precision-10
    readOnly: true
    description: Suggested retail price for the line item.
    x-mappedTo: RETAILPRICE
    example: '10.44'
  discountMemo:
    type: string
    description: Memo about any discounts taken.
    x-mappedTo: DISCOUNT_MEMO
    example: 'Festival discount'
  isPriceProrated:
    type: boolean
    description: Indicates whether the line item price is prorated.
    x-mappedTo: PRORATEPRICE
    x-mappedToType: string
    example: true
    default: true
  priceInTxnCurrency:
    type: string
    format: decimal-precision-10
    readOnly: true
    description: Transaction price.
    x-mappedTo: TRX_PRICE
    example: '10.00'
  currency:
    type: string
    description: Defaults to the transaction currency associated with the selected vendor. This field applies only to multi-currency companies.
    x-mappedTo: CURRENCY
    readOnly: true
    example: 'CAD'
  isBillable:
    type: boolean
    description: Indicates whether the line item is billable.
    x-mappedTo: BILLABLE
    x-mappedToType: string
    example: true
    default: true
  enableTax:
    type: boolean
    description: Indicates whether the line item is taxable.
    x-mappedTo: OVERRIDETAX
    x-mappedToType: string
    default: false
    example: false
  conversionType:
    type: string
    description: Conversion type used for the transaction.
    x-mappedTo: CONVERSIONTYPE
    enum:
      - quantity
      - price
    x-mappedToValues:
      - Quantity
      - Price
    default: quantity
    example: quantity
  itemAlias:
    type: object
    description: Alternative name for the item for a specific vendor.
    x-object: inventory-control/item-cross-reference
    properties:
      key:
        type: string
        description: Unique key for the item alias.
        x-mappedTo: ITEMALIASKEY
        example: '18'
      id:
        type: string
        description: ID for the item alias.
        x-mappedTo: ITEMALIASID
        example: 'IXN'
      href:
        type: string
        description: URL endpoint for the item alias.
        readOnly: true
        example: /objects/inventory-control/item-cross-reference/18
  recurringStatus:
    type: string
    description: Recurrence status for the line item.
    x-mappedTo: LINESTATUS
    example: active
    enum:
      - active
      - inactive
      - ended
    x-mappedToValues:
      - Active
      - Inactive
      - Ended
    default: active
  status:
    $ref: ../../common/models/status.s1.schema.yaml
  recurringDocumentHeader:
    type: object
    description: Header lever details for the document line item.
    x-mappedTo: sorecurdocument
    x-object: purchasing/recurring-document
    properties:
      key:
        type: string
        description: Unique key for the recurring document.
        x-mappedTo: DOCHDRNO
        example: '55'
      id:
        type: string
        description: ID for the recurring document.
        x-mappedTo: DOCHDRNO
        example: '55'
      documentType:
        type: string
        description: Type of the recurring document.
        x-mappedTo: DOCPARID
        x-useForDocType: true
        example: 'Purchase Invoice'
        readOnly: true
      href:
        type: string
        description: URL endpoint for the recurring document.
        readOnly: true
        example: /objects/purchasing-document::Purchase%20Invoice/55