openapi: 3.0.0
info:
  title: purchasing.txn-definition-additional-gl-detail
  description: purchasing.txn-definition-additional-gl-detail API
  version: '1.0'
  contact:
    name: <PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Transaction definition additional GL account details
    description: |
      This object provides the GL account mapping for purchasing transaction definitions that are configured to post cost of goods (COGS) adjustments to the General Ledger. 
      The additional GL account detail is relevant when reversing or posting Cost of Goods for a purchasing transaction.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/purchasing/txn-definition-additional-gl-detail:
    get:
      summary: List transaction definition additional GL account details
      description: Returns a collection with a key, ID, and link for each transaction definition additional GL account detail object.
      tags:
        - Transaction definition additional GL account details
      operationId: list-purchasing-txn-definition-additional-gl-detail
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of purchasing transaction definition additional GL account detail objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List transaction definition additional GL account details:
                  value:
                    ia::result:
                      - key: '239'
                        id: '239'
                        href: /objects/purchasing/txn-definition-additional-gl-detail/239
                      - key: '240'
                        id: '240'
                        href: /objects/purchasing/txn-definition-additional-gl-detail/240
                    ia::meta:
                      totalCount: 2
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create a transaction definition additional GL account detail object
      description: Creates a new transaction definition additional GL account detail object.
      tags:
        - Transaction definition additional GL account details
      operationId: create-purchasing-txn-definition-additional-gl-detail
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/purchasing-txn-definition-additional-gl-detail'
                - $ref: '#/components/schemas/purchasing-txn-definition-additional-gl-detailRequiredProperties'
            examples:
              Create a new transaction definition additional GL account detail object:
                value:
                    purchasingTxnDefinition:
                        key: '91'
                    glAccount:
                        id: '1003'
                    txnType: credit
                    lineNumber: 1
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New purchasing-txn-definition-additional-gl-detail
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to new transaction definition additional GL account detail object:
                  value:
                    'ia::result':
                      key: '208'
                      id: '208'
                      href: /objects/purchasing/txn-definition-additional-gl-detail/208
                    'ia::meta':
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
  '/objects/purchasing/txn-definition-additional-gl-detail/{key}':
    parameters:
      - name: key
        description: System-assigned unique key for the transaction definition additional GL account detail object.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a transaction definition additional GL account detail object
      description: Returns detailed information for a specified transaction definition additional GL account detail object.
      tags:
        - Transaction definition additional GL account details
      operationId: get-purchasing-txn-definition-additional-gl-detail-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the purchasing-txn-definition-additional-gl-detail-key
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/purchasing-txn-definition-additional-gl-detail'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get a transaction definition additional GL account detail object:
                  value:
                    ia::result:
                      id: '240'
                      key: '240'
                      purchasingTxnDefinition:
                        key: '91'
                        id: Purchase Order
                        href: /objects/purchasing/txn-definition/91
                      itemGLGroup:
                        key: '6'
                        id: Auto GL Group
                        href: /objects/inventory-control/item-group/6
                      warehouse:
                        id: WH01
                        key: '6'
                        href: /objects/inventory-control/warehouse/6
                      isOffset: false
                      txnType: credit
                      moduleType: inventory
                      glAccount:
                        id: '1003'
                        key: '6'
                        href: /objects/general-ledger/account/6
                      lineNumber: 1
                      href: /objects/purchasing/txn-definition-additional-gl-detail/240
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update a transaction definition additional GL account detail object
      description: Updates an existing transaction definition additional GL account detail object by setting field values. Any fields not provided remain unchanged.
      tags:
        - Transaction definition additional GL account details
      operationId: update-purchasing-txn-definition-additional-gl-detail-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/purchasing-txn-definition-additional-gl-detail'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Update a transaction definition additional GL account detail object:
                value:
                  glAccount:
                    id: '1400'
                  purchasingTxnDefinition:
                    key: '64'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated Purchasing transaction definition additional GL account detail object.
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to updated transaction definition additional GL account detail object:
                  value:
                    ia::result:
                      key: '191'
                      id: '191'
                      href: "/objects/purchasing/txn-definition-additional-gl-detail/191"
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a transaction definition additional GL account detail object
      description: Deletes a transaction definition additional GL account detail object.
      tags:
        - Transaction definition additional GL account details
      operationId: delete-purchasing-txn-definition-additional-gl-detail-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    purchasing-txn-definition-additional-gl-detail:
      $ref: ../models/purchasing.txn-definition-additional-gl-detail.s1.schema.yaml
    purchasing-txn-definition-additional-gl-detailRequiredProperties:
      type: object
      required:
        - txnType
      properties:
        purchasingTxnDefinition:
          type: object
          required:
            - key
        glAccount:
          type: object
          required:
            - id
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml