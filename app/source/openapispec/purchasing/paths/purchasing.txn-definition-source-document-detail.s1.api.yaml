openapi: 3.0.0
info:
  title: purchasing-txn-definition-source-document-detail
  description:  purchasing-txn-definition-source-document-detail API.
  version: '1.0'
  contact:
    name: <PERSON><PERSON>
    email: An<PERSON>.<EMAIL>
tags:
  - name: Transaction definition source document details
    description: This object provides document conversion details for purchasing transactions.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/purchasing-txn-definition-source-document-detail:
    get:
      summary: List purchasing transaction source document detail objects
      description: Returns a collection with a key, ID, and link for each purchasing transaction source document detail object.
      tags:
        - Transaction definition source document details
      operationId: list-purchasing-txn-definition-source-document-detail
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of Source document detail for purchasing transaction definition objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List purchasing transaction source document details:
                  value:
                    ia::result:
                      - key: '23'
                        id: '23'
                        href: /objects/purchasing/txn-definition-source-document-detail/23
                      - key: '19'
                        id: '19'
                        href: /objects/purchasing/txn-definition-source-document-detail/19
                      - key: '16'
                        id: '16'
                        href: /objects/purchasing/txn-definition-source-document-detail/16
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create a transaction definition source document detail object
      description: Creates a new transaction definition source document detail object.
      tags:
        - Transaction definition source document details
      operationId: create-purchasing-txn-definition-source-document-detail
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/purchasing-txn-definition-source-document-detail'
                - $ref: '#/components/schemas/purchasing-txn-definition-source-document-detailRequiredProperties'
            examples:
              Create a transaction definition source document detail object:
                value:
                  purchasingTxnDefinition:
                    key: '96'
                  sourceDocument:
                    id: PO Receiver
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New purchasing-txn-definition-source-document-detail
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to new transaction definition source document detail object:
                  value:
                    ia::result:
                      key: '78'
                      id: '78'
                      href: /objects/purchasing/txn-definition-source-document-detail/78
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
  '/objects/purchasing/txn-definition-source-document-detail/{key}':
    parameters:
      - name: key
        description: System-assigned unique key for the transaction definition source document detail object.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a transaction definition source document detail object
      description: Returns detailed information for a specified transaction definition source document detail object.
      tags:
        -  Transaction definition source document details
      operationId: get-purchasing-txn-definition-source-document-detail-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the purchasing-txn-definition-source-document-detail
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/purchasing-txn-definition-source-document-detail'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get a transaction definition source document detail object:
                  value:
                    ia::result:
                      id: '76'
                      key: '76'
                      purchasingTxnDefinition:
                        key: '96'
                        id: DEMO1
                        href: /objects/purchasing/txn-definition/96
                      sourceDocument:
                        key: '6'
                        id: PO Receiver
                        href: /objects/purchasing/txn-definition/6
                      href: /objects/purchasing/txn-definition-source-document-detail/76
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update a transaction definition source document detail object
      description: Updates an existing transaction definition source document detail object by setting field values. Any fields not provided remain unchanged.
      tags:
        - Transaction definition source document details
      operationId: update-purchasing-txn-definition-source-document-detail-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/purchasing-txn-definition-source-document-detail'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Update a transaction definition source document detail object:
                value:
                  purchasingTxnDefinition:
                    key: '96'
                  sourceDocument:
                    id: PO Return
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated purchasing-txn-definition-source-document-detail
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to updated transaction definition source document detail object:
                  value:
                    ia::result:
                      key: '78'
                      id: '78'
                      href: /objects/purchasing/txn-definition-source-document-detail/78
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a transaction definition source document detail object
      description: Deletes a transaction definition source document detail object.
      tags:
        - Transaction definition source document details
      operationId: delete-purchasing-txn-definition-source-document-detail-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    purchasing-txn-definition-source-document-detail:
      $ref: ../models/purchasing.txn-definition-source-document-detail.s1.schema.yaml
    purchasing-txn-definition-source-document-detailRequiredProperties:
      type: object
      properties:
        purchasingTxnDefinition:
          type: object
          required:
            - key
        sourceDocument:
          type: object
          required:
            - id
      example:
        purchasingTxnDefinition:
          key: '96'
        sourceDocument:
          id: PO Receiver
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml