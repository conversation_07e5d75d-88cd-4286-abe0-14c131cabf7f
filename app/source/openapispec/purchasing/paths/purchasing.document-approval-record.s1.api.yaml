openapi: 3.0.0
info:
  title: purchasing-document-approval-record
  description: purchasing.document approval record API
  version: '1.0'
  contact:
    name: <PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Document approval records
    description: A purchasing document approval record contains one entry for each approved or declined transaction associated with the purchasing document.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/purchasing/document-approval-record:
    get:
      summary: List document approval records
      description: Returns a collection with a key, ID, and link for each document approval record.
      tags:
        - Document approval records
      operationId: list-purchasing-document-approval-record
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of document approval record objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of Document approval records:
                  value:
                    'ia::result':
                      - key: '2'
                        id: '2'
                        href: /objects/purchasing/document-approval-record/2
                      - key: '6'
                        id: '6'
                        href: /objects/purchasing/document-approval-record/6
                    'ia::meta':
                      totalCount: 2
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
  '/objects/purchasing/document-approval-record/{key}':
    parameters:
      - name: key
        description: System-assigned unique key for the document approval record.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a document approval record
      description: Returns detailed information for a specified document approval record.
      tags:
        - Document approval records
      operationId: get-purchasing-document-approval-record-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the document approval record
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/purchasing-document-approval-record'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the document approval record:
                  value:
                    'ia::result':
                      id: '2'
                      key: '2'
                      purchasingDocument:
                        key: '2'
                        id: Vendor Invoice-VI#0020#doc
                        href: /objects/purchasing/document/2
                      approvalStep: 2
                      approvalRuleType: userLevel
                      approvalLevel: '1'
                      approver:
                        key: '208'
                        id: MrAssigned Approver
                        href: /objects/company-config/user/208
                      subjectManager: employeeManager
                      originalApprover:
                        key: '2'
                        id: Aaron
                        href: /objects/company-config/user/2
                      enableDelegation: true
                      approverNumber: '1'
                      dimensions:
                        department:
                          key: '9'
                          id: DU-123
                          href: /objects/company-config/department/9
                      approvedBy:
                        key: null
                        id: null
                      approvalDate: '2022-02-11'
                      comments: ''
                      state: pendingApproval
                      submittedBy:
                        key: '1'
                        id: Admin
                        href: /objects/company-config/user/1
                      valueApprovalDetails:
                        amount: '7482.20'
                        currency: INR
                        exchangeRateDate: '2022-02-11'
                        exchangeRateTypeId: '-1'
                        exchangeRate: '74.822'
                      href: /objects/purchasing/document-approval-record/2
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    purchasing-document-approval-record:
      $ref: ../models/purchasing.document-approval-record.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml