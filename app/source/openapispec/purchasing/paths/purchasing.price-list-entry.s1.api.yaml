openapi: 3.0.0
info:
  title: purchasing-price-list-entry
  description: purchasing.price-list-entry API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Price list entries
    description: A purchasing price list entry contains the pricing for a selected item within a selected price list. You can create price list entries for items whose prices are determined based on an amount, a discount or mark up percentage, the quantity sold, and more.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/purchasing/price-list-entry:
    get:
      summary: List price list entries
      description: Returns a collection with a key, ID, and link for each price list entry.
      tags:
        - Price list entries
      operationId: list-purchasing-price-list-entry
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of price-list-entry objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of price list entries:
                  value:
                    'ia::result':
                      - key: '1'
                        id: '1'
                        href: /objects/purchasing/price-list-entry/1
                      - key: '2'
                        id: '2'
                        href: /objects/purchasing/price-list-entry/2
                    'ia::meta':
                      totalCount: 2
                      totalSuccess: 2
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create a price list entry
      description: Creates a new price list entry.
      tags:
        - Price list entries
      operationId: create-purchasing-price-list-entry
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/purchasing-price-list-entry'
                - $ref: '#/components/schemas/purchasing-price-list-entryRequiredProperties'
            examples:
              Creates a price list entry:
                value:
                  startDate: '2024-07-01'
                  endDate: '2024-09-31'
                  minimumQuantity: '1'
                  maximumQuantity: '9999999'
                  value: '1.00000000'
                  valueType: 'actual'
                  isFixedPrice: 'true'
                  status: 'active'
                  currency: 'USD'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New price-list-entry
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to new price list entry:
                  value:
                    'ia::result':
                      id: '421'
                      key: '421'
                      href: '/objects/purchasing/price-list-entry/421'
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
  '/objects/purchasing/price-list-entry/{key}':
    parameters:
      - name: key
        description: System-assigned unique key for the price list entry.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a price list entry
      description: Returns detailed information for a specified price list entry.
      tags:
        - Price list entries
      operationId: get-purchasing-price-list-entry-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the price-list-entry
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/purchasing-price-list-entry'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the price list entry:
                  value:
                    Details of the price list entry:
                      value:
                        ia::result:
                          id: '451'
                          key: '451'
                          priceList:
                            id: 'Base Price List'
                            key: '1'
                            href: '/objects/purchasing/price-list/1'
                          item:
                            key: null
                            id: null
                            name: null
                          productLine:
                            key: '19'
                            id: 'Accessories'
                            href: '/objects/purchasing/product-line/19'
                          startDate: '2024-07-01'
                          endDate: '2024-07-31'
                          minimumQuantity: '1'
                          maximumQuantity: '*********'
                          value: '10.00000000'
                          valueType: 'actual'
                          isFixedPrice: 'true'
                          status: 'active'
                          currency: 'USD'
                          employee:
                            key: '25'
                            id: '123'
                            href: '/objects/company-config/employee/25'
                          audit:
                            createdDateTime: 2024-12-12T04:46:56Z
                            modifiedDateTime: 2024-12-12T04:46:56Z
                            createdBy: Admin
                            modifiedBy: Admin
                          href: '/objects/purchasing/price-list-entry/451'
                          ia::meta:
                            totalCount: 1
                            totalSuccess: 1
                            totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update a price list entry
      description: Updates an existing price list entry by setting field values. Any fields not provided remain unchanged.
      tags:
        - Price list entries
      operationId: update-purchasing-price-list-entry-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/purchasing-price-list-entry'
                - type: object
                  properties:
                    id:
                      readOnly: true
                      example: '435'
            examples:
              Updates a price list entry:
                value:
                  startDate: '2018-02-01'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated price-list-entry
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to updated price list entry:
                  value:
                    ia::result:
                      id: '435'
                      key: '435'
                      href: '/objects/purchasing/price-list-entry/435'
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a price list entry
      description: Deletes a price list entry.
      tags:
        - Price list entries
      operationId: delete-purchasing-price-list-entry-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    purchasing-price-list-entry:
      $ref: ../models/purchasing.price-list-entry.s1.schema.yaml
    purchasing-price-list-entryRequiredProperties:
      type: object
      required:
        - startDate
        - endDate
        - value
        - valueType
        - priceList
        - item
        - productLine
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml