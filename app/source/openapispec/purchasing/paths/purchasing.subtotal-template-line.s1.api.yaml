openapi: 3.0.0
info:
  title: purchasing-subtotal-template-line
  description: purchasing.subtotal-template-lines API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Subtotal template lines
    description: |
      A transaction definition includes a subtotals section. You can enter the subtotal details directly in the subtotals
      table within the transaction definition. Or, if advanced workflow is enabled, you can use subtotal templates to define
      the subtotals once and then simply refer to them by template name within the transaction definition.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/purchasing/subtotal-template-line:
    get:
      summary: List subtotal template line objects
      description: Returns up to 100 object references from the collection with a key, ID, and link for each subtotal
        template details. This operation is mostly for use in testing; use query to find objects that meet certain criteria
        and to specify properties that are returned.
      tags:
        - Subtotal template lines
      operationId: list-purchasing-subtotal-template-line
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of subtotal template line objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List subtotal template lines:
                  value:
                    'ia::result':
                      - key: '28'
                        id: '28'
                        href: /objects/purchasing/subtotal-template-line/28
                      - key: '15'
                        id: '15'
                        href: /objects/purchasing/subtotal-template-line/15
                      - key: '1'
                        id: '1'
                        href: /objects/purchasing/subtotal-template-line/1
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create a subtotal template line object
      description: Creates a new subtotal template line object.
      tags:
        - Subtotal template lines
      operationId: create-purchasing-subtotal-template-line
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/purchasing-subtotal-template-line'
                - $ref: '#/components/schemas/purchasing-subtotal-template-lineRequiredProperties'
            examples:
              Create a new subtotal template line:
                value:
                  subtotalTemplate:
                    key: '30'
                  description: HandlingCharge
                  subtotalType: charge
                  valueType: amount
                  defaultValue: '20.50'
                  isApportioned: false
                  glAccount:
                    id: '1200'
                  offsetGLAccount:
                    id: '1200.01'
                  txnType: debit
                  applyToLineNumber: 0
                  isTax: false
                  isAvalaraTax: false
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New purchasing-subtotal-template-line
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to new subtotal template line object:
                  value:
                    ia::result:
                      key: '28'
                      id: '28'
                      href: /objects/purchasing/subtotal-template-line/28
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
  '/objects/purchasing/subtotal-template-line/{key}':
    parameters:
      - schema:
          type: string
        name: key
        in: path
        required: true
        example: '28'
        description: System-assigned key for the subtotal template line.
    get:
      summary: Get a subtotal template line object
      description: Returns detailed information for a specified subtotal template line object.
      tags:
        - Subtotal template lines
      operationId: get-objects-purchasing-subtotal-template-line-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/purchasing-subtotal-template-line'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get a subtotal template line:
                  value:
                    'ia::result':
                      key: '28'
                      id: '28'
                      description: HandlingCharge
                      subtotalTemplate:
                        key: '30'
                        id: '30'
                        href: /objects/purchasing/subtotal-template/30
                      subtotalType: charge
                      lineNumber: 0
                      valueType: amount
                      defaultValue: '20.50'
                      isApportioned: false
                      glAccount:
                        key: '36'
                        id: '1200'
                        href: /objects/general-ledger/account/36
                      offsetGLAccount:
                        key: '37'
                        id: '1200.01'
                        href: /objects/general-ledger/account/37
                      txnType: debit
                      applyToLineNumber: 0
                      isTax: false
                      isAvalaraTax: false
                      audit:
                        createdDateTime: '2024-05-15T16:46:11Z'
                        modifiedDateTime: '2024-05-15T16:46:11Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      href: /objects/purchasing/subtotal-template-line/28
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          description: Bad Request
    patch:
      summary: Update a subtotal template line object
      description: Updates an existing subtotal template line object by setting field values. Any fields not provided remain unchanged.
      tags:
        - Subtotal template lines
      operationId: update-purchasing-subtotal-template-line-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/purchasing-subtotal-template-line'
                - type: object
            examples:
              Update a subtotal template line object:
                value:
                  subtotalType: charge
                  defaultValue: '25.00'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated subtotal template line
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to updated subtotal template line object:
                  value:
                    'ia::result':
                      key: '28'
                      id: '28'
                      href: /objects/purchasing/subtotal-template-line/28
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a subtotal template line object
      description: Deletes a subtotal template line object.
      tags:
        - Subtotal template lines
      operationId: delete-purchasing-subtotal-template-line-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    purchasing-subtotal-template-line:
      $ref: ../models/purchasing.subtotal-template-line.s1.schema.yaml
    purchasing-subtotal-template-lineRequiredProperties:
      type: object
      required:
        - description
        - subtotalType
        - valueType
        - txnType
      properties:
        glAccount:
          type: object
          required:
            - id
        offsetGLAccount:
          type: object
          required:
            - id
        subtotalTemplate:
          type: object
          required:
            - key
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml