fields:
  id:
    uiType: text
    uiLabel: IA.SYMBOL
  isBillable:
    uiType: enum
    uiLabel: IA.ENABLE_PROJECT_BILLING_FLAG
    enumsLabels:
      -
        label: IA.TRUE
        value: true
      -
        label: IA.FALSE
        value: false
  key:
    uiType: integer
    uiLabel: IA.RECORD_NUMBER
  name:
    uiType: text
    uiLabel: IA.TITLE
  status:
    uiType: enum
    uiLabel: IA.STATUS
    enumsLabels:
      -
        label: IA.ACTIVE
        value: active
      -
        label: IA.INACTIVE
        value: inactive
  disallowDirectPosting:
    uiType: enum
    uiLabel: IA.DISALLOW_DIRECT_POSTING
    enumsLabels:
      - label: IA.TRUE
        value: true
      - label: IA.FALSE
        value: false
groups:
  audit:
    fields:
      createdBy:
        uiType: text
        uiLabel: IA.CREATED_BY
      createdDateTime:
        uiType: timestamp
        uiLabel: IA.WHEN_CREATED
      modifiedBy:
        uiType: text
        uiLabel: IA.MODIFIED_BY
      modifiedDateTime:
        uiType: timestamp
        uiLabel: IA.WHEN_MODIFIED
    refs:
      createdByUser:
        fields:
          key:
            uiType: integer
            uiLabel: IA.CREATED_BY
          id:
            uiType: text
            uiLabel: IA.CREATED_BY_LOGIN_ID
      modifiedByUser:
        fields:
          key:
            uiType: integer
            uiLabel: IA.MODIFIED_BY
          id:
            uiType: text
            uiLabel: IA.MODIFIED_BY_LOGIN_ID
