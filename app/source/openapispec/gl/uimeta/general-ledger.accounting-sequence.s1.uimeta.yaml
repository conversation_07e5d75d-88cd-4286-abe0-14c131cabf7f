fields:
  endDate:
    uiType: date
    uiLabel: IA.END_DATE
  id:
    uiType: sequence
    uiLabel: IA.RECORD_NUMBER
  startDate:
    uiType: date
    uiLabel: IA.START_DATE
groups:
  audit:
    fields:
      createdBy:
        uiType: text
        uiLabel: IA.CREATED_BY
      createdDateTime:
        uiType: timestamp
        uiLabel: IA.WHEN_CREATED
      modifiedBy:
        uiType: text
        uiLabel: IA.MODIFIED_BY
      modifiedDateTime:
        uiType: timestamp
        uiLabel: IA.WHEN_MODIFIED
    refs:
      createdByUser:
        fields:
          key:
            uiType: integer
            uiLabel: IA.CREATED_BY
          id:
            uiType: text
            uiLabel: IA.CREATED_BY_LOGIN_ID
      modifiedByUser:
        fields:
          key:
            uiType: integer
            uiLabel: IA.MODIFIED_BY
          id:
            uiType: text
            uiLabel: IA.MODIFIED_BY_LOGIN_ID
refs:
  entity:
    fields:
      id:
        uiType: ptr
        uiLabel: IA.ENTITY
      key:
        uiType: ptr
        uiLabel: IA.ENTITY_KEY

