title: construction-standard-cost-type
type: object
x-mappedTo: standardcosttype
description: Standard cost type
properties:
  key:
    type: string
    description: System-assigned unique key for the standard cost type.
    example: '3'
    x-mappedTo: RECORDNO
    readOnly: true
  id:
    type: string
    description: Unique identifier of the standard cost type. Used by the system to create the IDs of any cost types created from this standard cost type. 12 characters or less.
    example: EQ-Owned
    x-mappedTo: STANDARDCOSTTYPEID
    x-mutable: false
  href:
    type: string
    description: URL endpoint of the standard cost type.
    example: /objects/construction/standard-cost-type/3
    readOnly: true
  name:
    type: string
    description: Name of the standard cost type.
    example: EQ Owned
    x-mappedTo: NAME
  description:
    type: string
    description: Description of the standard cost type.
    example: EQ Owned
    x-mappedTo: DESCRIPTION
  costUnitDescription:
    type: string
    description: Describes the unit of measure used by this cost type. For example, for a labor cost type the cost unit represents an hour of labor. For a materials cost type, the cost unit could be "tons" or "feet" or similar.
    example: each
    nullable: true
    x-mappedTo: COSTUNITDESCRIPTION
  status:
    $ref: ../../common/models/status.s1.schema.yaml
  glAccount:
    type: object
    description: General ledger account associated with this cost type.
    x-object: general-ledger/account
    x-mappedTo: glaccount
    title: account
    properties:
      key:
        type: string
        description: Account key.
        x-mappedTo: ACCOUNTKEY
        example: '256'
      id:
        type: string
        description: Account ID.
        x-mappedTo: ACCOUNTNO
        example: '6252'
      name:
        type: string
        description: GL Account name.
        x-mappedTo: ACCOUNTTITLE
        readOnly: true
        example: Construction Labor
      href:
        type: string
        description: URL endpoint for the account.
        readOnly: true
        example: /objects/gl-account/256
  accumulationType:
    type: object
    description: Accumulation type associated with this cost type.
    x-object: construction/accumulation-type
    properties:
      key:
        type: string
        description: Accumulation type key.
        example: '1'
        x-mappedTo: ACCUMULATIONTYPEKEY
      id:
        type: string
        description: Accumulation type ID.
        example: Equipment
        x-mappedTo: ACCUMULATIONTYPENAME
      href:
        type: string
        example: /objects/construction/accumulation-type/1
        description: URL endpoint of the accumulation type.
        readOnly: true
  parent:
    type: object
    description: Reference to a parent standard cost type to group standard cost types in the catalog.
    x-object: construction/standard-cost-type
    x-mappedTo: PARENT
    properties:
      key:
        type: string
        description: Parent standard cost type key.
        example: '2'
        x-mappedTo: PARENTKEY
      id:
        type: string
        description: Parent standard cost type ID.
        example: EQ
        x-mappedTo: PARENTID
      name:
        type: string
        description: Name of the parent standard cost type.
        example: Equipment
        x-mappedTo: PARENTNAME
        readOnly: true
      href:
        type: string
        example: /objects/construction/standard-cost-type/2
        description: URL of the parent standard cost type.
        readOnly: true
  item:
    $ref: ../../common/references/item-ref.s1.schema.yaml
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml
