type: object
title: standard-task
description: Standard task associated with the object.
x-object: construction/standard-task
properties:
  key:
    type: string
    description: Standard task key.
    x-mappedTo: STANDARDTASKKEY
    example: '3'
    readOnly: true
  id:
    type: string
    description: Standard task ID.
    x-mappedTo: STANDA<PERSON><PERSON>SKID
    example: 1-000
  name:
    type: string
    description: Standard task name.
    x-mappedTo: STAN<PERSON>RDTASKNAME
    example: GENERAL CONDITIONS
    readOnly: true
  href:
    type: string
    description: URL endpoint of the standard task.
    readOnly: true
    example: /objects/construction/standard-task/3
