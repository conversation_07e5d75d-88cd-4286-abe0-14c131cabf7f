title: platform-txn-map
x-mappedTo: transactionmap
type: object
x-ownedBy: platform/package-tracking
description: platform-txn-map object
properties:
  key:
    type: string
    description: System-assigned key for the scm object mapping.
    x-mappedTo: RECORDNO
    readOnly: true
    example: '38'
  id:
    type: string
    description: Unique identifier. This value is the same as `key` for this object.
    x-mappedTo: RECORDNO
    readOnly: true
    example: '38'
  companyId:
    type: string
    description: Customer's company id
    x-mappedTo: COMPANYID
    example: '49200'
    x-mutable: false
    nullable: false
  applicationId:
    type: string
    description: Application original id.
    x-mappedTo: PACKAGE_ID
    example: 12345@1234565
    x-mutable: false
    nullable: false
  partnerDocumentType:
    type: string
    description: Partner's version of document type.
    x-mappedTo: PARTNER_DOCTYPE
    example: Sales Order
    nullable: false
  customerDocumentType:
    type: string
    description: Customer's version of document type.
    x-mappedTo: CUSTOMER_DOCTYPE
    example: Sales Order
    nullable: false
  packageTracking:
    title: package-tracking-ref
    type: object
    description: Reference to the package tracking associated with the transaction map.
    x-mappedTo: ITEMS
    x-object: platform/package-tracking
    properties:
      key:
        type: string
        description: System-assigned key for the package tracking.
        x-mappedTo: PARENT_ID
        x-mutable: false
        example: '38'
      id:
        type: string
        description: Unique identifier. This value is the same as `key` for this object.
        x-mappedTo: PARENT_ID
        x-mutable: false
        example: '38'
      href:
        type: string
        description: URL endpoint for package traking.
        readOnly: true
        example: /objects/accounts-payable/bill/19876
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml
  href:
    type: string
    description: Endpoint URL for the transaction map.
    readOnly: true
    example: objects/platform/txn-map/**************
