title: electronic-receipt-line
x-mappedTo: electronicreceiptsitem
x-ownedBy: expenses/electronic-receipt
type: object
description: An individual line on an employee electronic receipt.
properties:
  key:
    type: string
    description: System-assigned unique key for the electronic receipt line. 
    x-mappedTo: RECORDNO
    readOnly: true
    example: '312'
  id:
    type: string
    description: Unique identifier for the electronic receipt line. This value is the same as the key for this object. 
    x-mappedTo: RECORDNO
    readOnly: true
    example: '312'
  href:
    type: string
    readOnly: true
    description: URL endpoint for the electronic receipt line.
    example: /objects/expenses/electronic-receipt-line/312
  entryDate:
    type: string
    format: date
    description: Date filed.
    example: '2025-01-23'
    x-mappedTo: ENTRY_DATE
  currency:
    type: string
    description: Transaction currency of the receipt.
    x-mappedTo: ORG_CURRENCY
    example: INR
  txnAmount:
    type: string
    description: Transaction amount.
    x-mappedTo: ORG_AMOUNT
    format: decimal-precision-2
    example: '123.45'
    nullable: true
  baseCurrency:
    type: string
    description: Base currency of the receipt.
    x-mappedTo: BASECURR
    readOnly: true
    example: USD
  baseAmount:
    type: string
    description: Amount of the expense in base currency.
    x-mappedTo: AMOUNT
    readOnly: true
    format: decimal-precision-2
    example: '123.45'
  quantity:
    type: string
    description: Quantity for a rate-based receipt, such as for mileage.
    x-mappedTo: QUANTITY
    format: decimal-precision-2
    example: '5.75'
  unitRate:
    type: string
    description: Monetary amount for a rate-based receipt, such as for mileage.
    x-mappedTo: UNITRATE
    format: decimal-precision-2
    example: '20.00'
  paidTo:
    type: string
    description: Notes regarding to whom you paid the amount.
    x-mappedTo: DESCRIPTION
    example: 'Hotel Westin'
  paidFor:
    type: string
    description: Notes regarding what the receipt was for.
    x-mappedTo: DESCRIPTION2
    example: 'Attending conference'
  glAccount:
    type: object
    description: General ledger account associated with the line item. Used when no expense type is assigned.
    x-object: general-ledger/account
    x-mappedTo: glaccount
    properties:
      key:
        type: string
        description: Unique key for the GL account.
        x-mappedTo: ACCOUNTKEY
        example: '158'
      id:
        type: string
        description: Unique identifier for the GL account.
        x-mappedTo: ACCOUNTNO
        example: '158'
      name:
        type: string
        description: Name of the GL account.
        x-mappedTo: GLACCOUNTTITLE
        example: 'Travel'
        readOnly: true
      href:
        type: string
        description: URL endpoint for the GL account.
        readOnly: true
        example: /objects/general-ledger/account/158
  expenseType:
    type: object
    description: An expense type defined in the company.
    x-object: expenses/employee-expense-type
    x-mappedTo: acctlabel
    properties:
      key:
        type: string
        description: Unique key for the expense type.
        x-mappedTo: ACCOUNTLABELKEY
        example: '6000'
      id:
        type: string
        description: Unique identifier for the expense type. 
        x-mappedTo: ACCOUNTLABEL
        example: Meals
      href:
        type: string
        description: URL endpoint for the expense type.
        readOnly: true
        example: /objects/expenses/expense-type/6000
  lineNumber:
    type: integer
    description: Line number of the electronic receipt.
    x-mappedTo: LINE_NO
    readOnly: true
    example: 1
  state:
    type: string
    description: Status of the employee electronic receipt line.
    enum:
      - draft
      - used
      - review
      - analyzing
    x-mappedToValues:
      - Draft
      - Used
      - Review
      - Analyzing
    x-mappedTo: STATE
    readOnly: true
    example: draft
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml
  dimensions:
    type: object
    allOf:
      - $ref: ../../common/references/dimension-ref.s1.schema.yaml
      - type: object
        properties:
          location:
            type: object
            x-object: company-config/location
            properties:
              key:
                x-mappedTo: LOCATION#
                type: string
                description: Unique key for the location.
                example: '1'
                nullable: true
              id:
                x-mappedTo: LOCATIONID
                type: string
                description: Unique identifier for the location.
                example: '1'
                nullable: true
              name:
                x-mappedTo: LOCATIONNAME
                readOnly: true
                type: string
                description: Location name
                example: 'USA'
                nullable: true
              href:
                type: string
                description: URL endpoint for the location.
                readOnly: true
                example: /objects/company-config/location/1
          department:
            type: object
            x-object: company-config/department
            properties:
              key:
                type: string
                description: Unique key for the department.
                x-mappedTo: DEPT#
                example: '12'
                nullable: true
              id:
                type: string
                description: Unique identifier for the department. 
                x-mappedTo: DEPARTMENTID
                example: '12'
                nullable: true
              name:
                type: string
                description: Department name
                x-mappedTo: DEPARTMENTNAME
                readOnly: true
                example: 'IT'
                nullable: true
              href:
                type: string
                description: URL endpoint for the department.
                readOnly: true
                example: /objects/company-config/department/12
  electronicReceipt:
    type: object
    x-mappedTo: electronicreceipts
    x-object: expenses/electronic-receipt
    readOnly: true
    properties:
      id:
        type: string
        description: Unique identifier for the electronic receipt. 
        x-mappedTo: RECORDKEY
        readOnly: true
        example: '124'
      key:
        type: string
        description: Unique key for the electronic receipt.
        x-mappedTo: RECORDKEY
        readOnly: true
        example: '124'
      href:
        type: string
        readOnly: true
        description: URL endpoint for the electronic receipt.
        example: /objects/expenses/electronic-receipt/124