ia::definition:
  methodPermissions:
    GET:
      - cm/lists/otherreceipts/view
    POST:
      - cm/lists/otherreceipts/create
    PATCH:
      - cm/lists/otherreceipts/edit
    DELETE:
      - cm/lists/otherreceipts/delete
  workflowPermissions:
    reverse:
      - cm/lists/otherreceipts/reverse
s1:
  hash: '0'
  type: rootObject
  validator: OtherReceiptValidator
  systemViews:
    systemfw1:
      revision: s1
      hash: '0'
    systemfw2:
      revision: s1
      hash: '0'
  uiMetadataHash: '0'
  workflows:
    revision: s1
    hash: '0'

