openapi: 3.0.0
info:
  title: cash-management-bank-reconciliation
  description: Bank reconciliation API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Bank reconciliations
    description: "Reconcile a bank account against transaction records from a previously created bank feed.\n\nReconcile a bank account by having the system attempt to match and clear all the transactions (`automatch` mode), or review the system's proposed matches in the UI and reconcile the account from there (`automatchWithReview` mode). \nIn `automatch` mode, the entire reconciliation must clear, otherwise an error is reported. With `automatchWithReview` mode, a user must select **Use previous uploaded file** to access and review the reconciliation in Sage Intacct.\n\nWith `manual` mode, a user must manually match transactions in Sage Intacct as described in [Manually match transactions for reconciliation](https://www.intacct.com/ia/docs/en_US/help_action/Cash_Management/Reconcile/Matching_transactions/match-transactions.htm).\n\n `automatch` and `manual` modes can be completed from the API and in case of `automatchWithReview` the reconciliation can be created in the API, but must be completed in the UI"
servers:
  - url: 'https://dev09.intacct.com/users/neema.shetty/projects.bankFeedRestApi/api/v0'
paths:
  /objects/cash-management/bank-reconciliation:
    get:
      summary: List bank reconciliations
      description: Returns a collection with a key, ID, and link for each bank reconciliation. This operation is mostly for use in testing; use query to find objects that meet certain criteria and to specify properties that are returned.
      x-documentationFlags:
        subscription: Cash Management
        userPermissions:
          - userType: Business
            permissions: List, View Reconcile bank
      tags:
        - Bank reconciliations
      operationId: list-cash-management-bank-reconciliation
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: cash-management-bank-reconciliation collection
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List bank reconciliations:
                  value:
                    'ia::result':
                      - key: '16'
                        id: '16'
                        href: /objects/cash-management/bank-reconciliation/16
                      - key: '17'
                        id: '17'
                        href: /objects/cash-management/bank-reconciliation/17
                      - key: '18'
                        id: '18'
                        href: /objects/cash-management/bank-reconciliation/18
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create a bank reconciliation
      description: Creates a new bank reconciliation.
      x-documentationFlags:
        subscription: Cash Management
        userPermissions:
          - userType: Business
            permissions: Add, Edit Reconcile bank
      tags:
        - Bank reconciliations
      operationId: create-cash-management-bank-reconciliation
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/cash-management-bank-reconciliation'
                - $ref: '#/components/schemas/cash-management-bank-reconciliationRequiredProperties'
            examples:
              Create an automatchWithReview reconciliation:
                value:
                  bankAccount:
                    id: CDB
                  reconciliationDate: '2021-09-21'
                  cutoffDate: '2010-06-21'
                  endingBalance: '50000.35'
                  reconciliationMode: automatchWithReview
                  feedType: xml
              Create a manual reconciliation:
                value:
                  bankAccount:
                    id: SBME
                  reconciliationDate: '2020-07-31'
                  cutoffDate: '2018-06-21'
                  endingBalance: '12346.55'
                  reconciliationMode: manual
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New bank-reconciliation
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                New bank reconciliation:
                  value:
                    'ia::result':
                      key: '57'
                      id: '57'
                      href: /objects/cash-management/bank-reconciliation/57
                    'ia::meta':
                      totalCount: 1
        '422':
          description: Operation Failed
          content:
            application/json:
              schema:
                type: object
                properties: {}
              examples:
                Manual-mode reconciliation with error:
                  value:
                    'ia::result':
                      'ia::error':
                        code: operationFailed
                        message: '[POST] operation on [bank-reconciliation] object failed'
                        supportId: hCgxJ%7EYYnYrDEyVq-0mwK9YaKwOwAAABI
                        details:
                          - code: PL03000064
                            message: There is a difference between the ending book balance and the ending bank balance
                            correction: Ensure that the difference is zero.
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 0
                      totalError: 1
  /objects/cash-management/bank-reconciliation/{key}:
    parameters:
      - name: key
        description: System-assigned key for the bank reconciliation.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a bank reconciliation
      description: Returns detailed information for a specified bank reconciliation.
      x-documentationFlags:
        subscription: Cash Management
        userPermissions:
          - userType: Business
            permissions: List, View Reconcile bank
      tags:
        - Bank reconciliations
      operationId: get-cash-management-bank-reconciliation-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: cash-management-bank-reconciliation details
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/cash-management-bank-reconciliation'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get a reconciliation:
                  value:
                    'ia::result':
                      key: '52'
                      id: '52'
                      bankAccount:
                        id: SBME
                        key: '132'
                        href: /objects/cash-management/credit-card-account/132
                      reconciliationDate: '2020-01-31'
                      cutoffDate: '2018-06-21'
                      endingBalance: '12346.55'
                      reconciliationMode: manual
                      reconciliationStatus: reconciled
                      feedType: ''
                      audit:
                        createdDateTime: '2021-10-26T00:00:00Z'
                        modifiedDateTime: '2021-10-26T00:00:00Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      isReopened: false
                      attachment:
                        key: '19'
                        id: 'Attach-01'
                        href: /objects/company-config/attachment/19
                      reconciliationSourceRecords:
                        - key: '2250'
                          id: '2250'
                          bankReconciliation:
                            key: '52'
                            id: '52'
                            href: /objects/cash-management/bank-reconciliation/52
                          bankAccount:
                            id: SBME
                            key: '132'
                            href: /objects/cash-management/credit-card-account/132
                          txnInformation:
                            recordType: otherReceipts
                            subledgerRecord:
                              key: '2226'
                              id: '2226'
                              href: /objects/accounts-payable/subledger-record/2226
                            journalEntryLine:
                              key: null
                              id: null
                            intialOpenItem:
                              key: null
                              id: null
                            txnType: deposit
                            documentNumber: null
                            documentDate: '2020-01-01'
                            txnAmount: '11.00'
                            baseAmount: '11.00'
                            txnCurrency: USD
                            baseCurrency: USD
                            postingDate: '2020-01-01'
                            reconciliationInformation:
                              lastReconcileDate: '2020-01-31'
                              state: cleared
                            payee: V213
                            description: null
                            postingState: approved
                          audit:
                            createdDateTime: '2021-10-26T00:00:00Z'
                            modifiedDateTime: '2021-10-26T00:00:00Z'
                            createdBy: '1'
                            modifiedBy: null
                          href: /objects/cash-management/bank-reconciliation-record/2250
                      href: /objects/cash-management/bank-reconciliation/52
                    'ia::meta':
                      totalCount: 1
                Get a reopened reconciliation:
                  value:
                    'ia::result':
                      key: '40'
                      id: '40'
                      bankAccount:
                        id: CITI
                        key: '132'
                        href: /objects/cash-management/credit-card-account/132
                      reconciliationDate: '2021-01-31'
                      cutoffDate: '2018-01-01'
                      endingBalance: '-2930.00'
                      reconciliationMode: automatchWithReview
                      reconciliationStatus: reconciled
                      feedType: online
                      audit:
                        createdDateTime: '2021-07-14T00:00:00Z'
                        modifiedDateTime: '2021-10-28T00:00:00Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      isReopened: true
                      reconciliationSourceRecords: []
                      href: /objects/cash-management/bank-reconciliation/40
                    'ia::meta':
                      totalCount: 1
  /workflows/cash-management/bank-reconciliation/reopen:
    post:
      summary: reopen a bank reconciliation
      description: reopen a bank reconciliation
      tags:
        - Bank reconciliations
      operationId: reopen-cash-management-bank-reconciliation
      requestBody:
        content:
          application/json:
            schema:
              $ref: ../workflows/cash-management.bank-reconciliation.s1.workflows.yaml#/actions/reopen/request
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: ../workflows/cash-management.bank-reconciliation.s1.workflows.yaml#/actions/reopen/response
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    cash-management-bank-reconciliation:
      $ref: ../models/cash-management.bank-reconciliation.s1.schema.yaml
    cash-management-bank-reconciliationRequiredProperties:
      type: object
      required:
        - reconciliationMode
        - reconciliationDate
        - endingBalance
        - bankAccount
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
