title: company-config-entity
x-mappedTo: locationentity
type: object
description: Entity configuration.
properties:
  key:
    type: string
    description: System-assigned unique key for the entity.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '2'
  id:
    type: string
    description: Unique identifier of the entity. 20 characters max. The ID cannot be changed after the entity has been created.
    x-mappedTo: LOCATIONID
    x-mutable: false
    example: Lyon
  href:
    type: string
    description: URL endpoint for the entity.
    readOnly: true
    example: /objects/company-config/entity/2
  name:
    type: string
    description: Name of the entity.
    x-mappedTo: NAME
    example: California
  startDate:
    type: string
    format: date
    description: The date when the entity became operational.
    x-mappedTo: STARTDATE
    nullable: true
    example: '2022-02-02'
  endDate:
    type: string
    format: date
    description: The date when the entity was taken out of operation.
    x-mappedTo: ENDDATE
    nullable: true
    example: '2023-03-03'
  federalId:
    type: string
    description: The federal tax ID for the entity. This value is required but is only used if the company is configured to issue a separate 1099 for each US entity. 
    x-mappedTo: FEDERALID
    nullable: true
    example: '*********'
  firstFiscalMonth:
    type: string
    description: First fiscal month
    nullable: true
    allOf:
      - $ref: ../../common/models/month.s1.schema.yaml
    x-mappedTo: FIRSTMONTH
    default: null
  firstTaxMonth:
    type: string
    description: First tax month
    nullable: true
    allOf:
      - $ref: ../../common/models/month.s1.schema.yaml
    x-mappedTo: FIRSTMONTHTAX
    default: null
  weekStart:
    type: string
    description: The first day of each week. This enables the system to provide accurate data for reports that begin on the first day of a week.
    nullable: true
    allOf:
      - $ref: ../../common/models/weekdays.s1.schema.yaml
    x-mappedTo: WEEKSTART
    default: null
  contacts:
    type: object
    title: contacts
    description: Contact information for the entity.
    properties:
      primary:
        type: object
        description: Primary contact information. This could be for the entity itself and not for an individual.
        allOf:
          - $ref: ../../common/references/contact-ref.s1.schema.yaml
          - type: object
            x-object: company-config/contact
            x-mappedTo: contactinfo
            x-mappedToKey: CONTACTKEY
            x-mappedToPrefix: CONTACTINFO
      shipTo:
        type: object
        description: Contact information to use for shipping, if the entity uses a different address for receiving goods and services.
        allOf:
          - $ref: ../../common/references/contact-ref.s1.schema.yaml
          - type: object
            x-object: company-config/contact
            x-mappedTo: shipto
            x-mappedToKey: SHIPTOKEY
            x-mappedToPrefix: SHIPTO
      legalCategory:
        type: string
        description: Identifies the type of organization under French law, encompassing various recognized forms for both private and public entities.
        x-mappedTo: LEGAL_CATEGORY
        example: '24 Fiduciary'
      mainActivity:
        type: string
        description: Specifies the primary economic activities the business engages in, helping classify the nature of operations.
        x-mappedTo: MAIN_ACTIVITY
        example: '10.3 Transformation and conservation of fruits and vegetables'
      typeOfCompany:
        type: string
        description: Classifies the business entity based on legal structure, size, activities, or other key characteristics.
        x-mappedTo: TYPE_OF_COMPANY
        example: '03 Intermediate sized enterprises'
      registeredCapital:
        type: integer
        description: Represents the total capital invested by shareholders upon the company's formation.
        x-mappedTo: REGISTERED_CAPITAL
        example: '37 000'
      valueAddedTaxRegime:
        type: string
        description: Details the specific VAT rules and regulations applicable to the business, including requirements for VAT collection, application, and reporting within France.
        x-mappedTo: VAT_REGIME
        example: Monthly
  texts:
    type: object
    description: Texts used in invoices and reports.
    properties:
      message:
        type: string
        description: Message text
        x-mappedTo: MESSAGE_TEXT
        nullable: true
        example: Superior financial Applications. Real-time business visibility. Open, on-demand platform.
      marketing:
        type: string
        description: Marketing text
        x-mappedTo: MARKETING_TEXT
        nullable: true
        example: Intacct. A Better Way to Run Your Business
      footnote:
        type: string
        description: Footnote text that will appear on reports and invoices that are printed for the entity.
        x-mappedTo: FOOTNOTETEXT
        nullable: true
        example: All sales final.
      reportPrintAs:
        type: string
        description: The name of the entity as it will appear on reports and invoices that are printed for the entity.
        x-mappedTo: REPORTPRINTAS
        example: My Company LLC
      customTitle:
        type: string
        description: Entity information displayed in reports, in addition to the entity name. For example, an entity title might include the address and manager name.
        x-mappedTo: CUSTTITLE
        nullable: true
        example: San Jose, California
  isRoot:
    type: boolean
    description: Used in Cash Management (Checking Account) to indicate whether this entity is a working entity.
    x-mappedTo: ISROOT
    default: false
    readOnly: true
    enum:
      - true
      - false
    x-mappedToValues:
      - 'T'
      - 'F'
    example: false
  businessDays:
    type: array
    description: Work days in each week.
    x-delimiter: ','
    x-mappedTo: BUSINESSDAYS
    example: ['MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY']
    default: []
    items:
      type: string
  weekends:
    type: array
    description: Weekend days.
    x-delimiter: ','
    x-mappedTo: WEEKENDS
    example: ['SATURDAY', 'SUNDAY']
    default: []
    items:
      type: string
  taxId:
    type: string
    description: The state or territory tax ID, or VAT ID.
    x-mappedTo: TAXID
    nullable: true
    example: '*********'
  defaultCountryForAddresses:
    type: string
    description: Default country for addresses
    allOf:
      - $ref: ../../common/models/country.s1.schema.yaml
    x-mappedTo: ADDRESSCOUNTRYDEFAULT
    default: 'unitedStates'
  businessId:
    type: string
    description: A company's Business ID (SIRET) is a 14-digit number that starts with the SIREN (a 9-digit number that identifies the company itself) followed by 5 digits that identifies the company's physical location or branch.
    x-mappedTo: SIRET
    nullable: true
    example: '*********** 12345'
  openBooksStartDate:
    type: string
    format: date
    description: Open books from
    x-mappedTo: STARTOPEN
    nullable: true
    example: '2021-01-01'
  operatingCountry:
    type: string
    description: The country in which the entity operates.
    nullable: true
    allOf:
      - $ref: ../../common/models/country.s1.schema.yaml
    x-mappedTo: OPCOUNTRY
    default: null
  legalContact:
    type: object
    description: Legal contact to be used on 1099 form, 1096 form, and taxable payments annual report (TPAR).
    properties:
      enableOnTaxForms:
        type: boolean
        description: Set to `true` to include the legal name and address on forms 1099 and 1096 for this entity.
        x-mappedTo: ENABLELEGALCONTACT
        example: false
        x-mappedToType: string
        default: false
      enableOnTPAR:
        type: boolean
        description: Set to `true` to include the legal name and address on taxable payments annual report (TPAR) for Australian companies and entities.
        x-mappedTo: ENABLELEGALCONTACT_TPAR
        example: false
        x-mappedToType: string
        default: false
      name:
        type: string
        description: Legal name, to be printed on 1099 form, 1096 form and taxable payments annual report (TPAR).
        x-mappedTo: LEGALNAME
        nullable: true
        example: 'Expert Sage Intacct LTD'
      address:
        type: object
        description: Legal address to be used on 1099 form, 1096 form and taxable payments annual report (TPAR).
        properties:
          address1:
            type: string
            description: Legal address line 1
            x-mappedTo: LEGALADDRESS1
            nullable: true
            example: 300 Park Avenue
          address2:
            type: string
            description: Legal address line 2
            x-mappedTo: LEGALADDRESS2
            nullable: true
            example: Suite 1400
          address3:
            type: string
            description: Legal address line 3
            x-mappedTo: LEGALADDRESS3
            nullable: true
            example: Western industrial area
          city:
            type: string
            description: Legal city
            x-mappedTo: LEGALCITY
            nullable: true
            example: San Jose
          state:
            type: string
            description: Legal state or territory
            x-mappedTo: LEGALSTATE
            nullable: true
            example: California
          zipCode:
            type: string
            description: Legal ZIP or postal code
            x-mappedTo: LEGALZIPCODE
            nullable: true
            example: '95110'
          country:
            type: string
            description: Legal country
            x-mappedTo: LEGALCOUNTRY
            nullable: true
            example: United States
          countryCode:
            type: string
            description: Legal country code
            nullable: true
            allOf:
              - $ref: ../../common/models/country-code.s1.schema.yaml
            x-mappedTo: LEGALCOUNTRYCODE
            default: null
      tpar:
        type: object
        description: Taxable payments annual report (TPAR) form elements.
        properties:
          contact:
            type: object
            description: Legal contact for taxable payments annual report (TPAR).
            properties:
              name:
                type: string
                description: Legal contact name.
                x-mappedTo: LEGALCONTACTNAME
                nullable: true
                example: John Doe
              phone:
                type: string
                description: Legal contact phone.
                x-mappedTo: LEGALCONTACTPHONE
                nullable: true
                example: '1022223333'
              fax:
                type: string
                description: Legal contact fax on taxable payments annual report (TPAR).
                x-mappedTo: LEGALCONTACTFAX
                nullable: true
                example: '1009288888'
              email:
                type: string
                description: Legal contact email on taxable payments annual report (TPAR).
                x-mappedTo: LEGALCONTACTEMAIL
                nullable: true
                example: <EMAIL>
          branchNumber:
            type: string
            description: Legal branch number on taxable payments annual report (TPAR).
            x-mappedTo: LEGALBRANCHNUMBER
            nullable: true
            example: '001'
  isPartialExempt:
    type: boolean
    description: Enable partial VAT exemption. When set to `true`, users can enter bills with items that are eligible for partial exemption in agreement with the HMRC. Requires that tax solution is United Kingdom - VAT.
    x-mappedTo: PARTIALEXEMPT
    example: true
    x-mappedToType: string
    default: false
  isDefaultPartialExempt:
    type: boolean
    description: Set to `true` to mark items as partial exempt by default on bills and invoices for Accounts Payable and Purchasing with the following tax solutions - Australia GST (RITC), Canadian Sales Tax (ITC), UK VAT.
    x-mappedTo: DEFAULTPARTIALEXEMPT
    example: true
    x-mappedToType: string
    default: false
  accountingType:
    type: string
    description: Accounting type
    x-mappedTo: ACCOUNTINGTYPE
    example: entity
    nullable: true
    enum:
      - null
      - entity
      - fund
    x-mappedToValues:
      - ''
      - Entity
      - Fund
    default: entity
  statutoryReportingPeriodDate:
    type: string
    format: date
    description: Max lock date for the given entity, used for GL Setup Approval Configuration.
    x-mappedTo: STATUTORYREPORTINGPERIOD
    nullable: true
    example: '2021-01-01'
  customer:
    type: object
    description: Customer associated with the entity.
    title: customer
    x-mappedTo: customer
    x-object: accounts-receivable/customer
    properties:
      key:
        type: string
        description: Unique key of the associated customer.
        x-mappedTo: CUSTOMERKEY
        nullable: true
        example: '15'
      href:
        type: string
        description: URL endpoint for the customer.
        readOnly: true
        nullable: true
        example: /objects/accounts-receivable/customer/23
      id:
        type: string
        description: Unique identifier for the customer.
        x-mappedTo: CUSTOMERID
        example: C-00003
      name:
        type: string
        description: Customer name.
        x-mappedTo: CUSTOMERNAME
        readOnly: true
        nullable: true
        example: Logic Solutions
  vendor:
    type: object
    description: Vendor associated with the entity.
    x-object: accounts-payable/vendor
    x-mappedTo: vendor
    title: vendor
    properties:
      key:
        type: string
        description: Unique key of the vendor.
        x-mappedTo: VENDORKEY
        example: '15'
      href:
        type: string
        description: URL endpoint for the vendor.
        readOnly: true
        example: /objects/accounts-payable/vendor/15
      id:
        type: string
        description: Vendor ID.
        x-mappedTo: VENDORID
        example: V-00014
      name:
        type: string
        description: Vendor name
        x-mappedTo: VENDORNAME
        readOnly: true
        nullable: true
        example: Pac bell
  unrecoverableTaxAccount:
    type: object
    description: Unrecoverable tax account for this entity.
    title: unrecoverableTaxAccount
    x-mappedTo: glaccount
    x-object: general-ledger/account
    properties:
      key:
        type: string
        description: Unrecoverable tax account key
        x-mappedTo: UNRECOVERABLETAXACCTKEY
        nullable: true
        example: '23'
      id:
        type: string
        description: Unrecoverable tax account ID
        x-mappedTo: UNRECOVERABLETAXACCTNO
        nullable: true
        example: '123.45'
      href:
        type: string
        description: URL endpoint for the unrecoverable tax account.
        readOnly: true
        example: /objects/general-ledger/account/23
  enableInterEntityRelationships:
    type: boolean
    description: Set to `true` to enable different accounts based on the target entity.
    x-mappedTo: HAS_IE_RELATION
    default: false
    example: true
    x-mappedToType: string
  interEntityPayableGLAccount:
    type: object
    description: Inter-entity payable account that this entity is associated with. Only applicable if `enableInterEntityRelationships` = `true`.
    title: interEntityPayable
    x-mappedTo: glaccount
    x-object: general-ledger/account
    properties:
      key:
        type: string
        description: System-assigned unique key of the account for payable inter-entity transactions.
        x-mappedTo: IEPAYABLEACCTKEY
        nullable: true
        example: '89'
      id:
        type: string
        description: Inter-entity payable account ID.
        x-mappedTo: IEPAYABLE.ACCOUNT
        nullable: true
        example: '555.99'
      accountNumber:
        type: string
        description: Inter-entity payable account number.
        x-mappedTo: IEPAYABLE.NUMBER
        readOnly: true
        nullable: true
        example: '123436'
      href:
        type: string
        description: URL endpoint for the account.
        readOnly: true
        example: /objects/general-ledger/account/89
  interEntityReceivableGLAccount:
    type: object
    description: Inter-entity receivable account that this entity is associated with. Only applicable if `enableInterEntityRelationships` = `true`.
    title: interEntityReceivable
    x-mappedTo: glaccount
    x-object: general-ledger/account
    properties:
      key:
        type: string
        description: System-assigned unique key of the account for receivable inter-entity transactions.
        x-mappedTo: IERECEIVABLEACCTKEY
        nullable: true
        example: '876'
      id:
        type: string
        description: Inter-entity receivable account ID.
        x-mappedTo: IERECEIVABLE.ACCOUNT
        nullable: true
        example: '555.98'
      accountNumber:
        type: string
        description: Inter-entity receivable account number.
        x-mappedTo: IERECEIVABLE.NUMBER
        readOnly: true
        nullable: true
        example: '123436'
      href:
        type: string
        description: URL endpoint for the account.
        readOnly: true
        example: /objects/general-ledger/account/876
  manager:
    type: object
    description: The employee who manages the entity.
    title: manager
    x-mappedTo: employee
    x-object: company-config/employee
    properties:
      href:
        type: string
        description: URL endpoint for the manager employee.
        readOnly: true
        example: /objects/company-config/employee/81
      id:
        type: string
        description: Manager employee ID.
        x-mappedTo: SUPERVISORID
        example: jsmith
      key:
        type: string
        description: System-assigned unique key of the manager employee.
        x-mappedTo: SUPERVISORKEY
        example: '81'
      name:
        type: string
        description: Name of the manager.
        x-mappedTo: SUPERVISORNAME
        readOnly: true
        nullable: true
        example: John Smith
      email1:
        type: string
        description: Manager's primary email address.
        x-mappedTo: SUPERVISOREMAIL1
        readOnly: true
        nullable: true
        example: '<EMAIL>'
      email2:
        type: string
        description: Manager's secondary email address.
        x-mappedTo: SUPERVISOREMAIL2
        readOnly: true
        nullable: true
        example: '<EMAIL>'
  taxSolution:
    type: object
    description: The tax solution to be used by this entity. Only for companies subscribed to the Taxes application.
    title: taxSolution
    x-mappedTo: taxsolution
    x-object: tax/tax-solution
    properties:
      key:
        type: string
        description: Tax solution
        x-mappedTo: TAXSOLUTIONKEY
        example: '23'
      href:
        type: string
        description: URL endpoint for the tax solution.
        readOnly: true
        example: /objects/tax/tax-solution/23
      id:
        type: string
        description: Tax solution
        x-mappedTo: TAXSOLUTIONID
        example: Australia - GST
      taxMethod:
        type: string
        description: Tax solution method
        x-mappedTo: TAXSOLUTIONTAXMETHOD
        readOnly: true
        nullable: true
        example: Advanced tax
  isLimitedEntity:
    type: boolean
    description: Set to 'true' to indicate that this entity is a limited entity.
    x-mappedTo: ISLIMITEDENTITY
    x-mappedToType: string
    nullable: false
    default: false
    example: true
  enableEInvoicing:
    type: boolean
    description: Set to 'true' to indicate that this entity is e-invoice enabled.
    x-mappedTo: EINVOICEENABLED
    x-mappedToType: string
    nullable: false
    default: false
    example: true
  status:
    $ref: ../../common/models/status.s1.schema.yaml
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml
