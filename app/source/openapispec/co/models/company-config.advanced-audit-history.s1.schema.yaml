title: company-config-advanced-audit-history
x-mappedTo: advaudithistory
type: object
description: Provides information about changes for a given object.
properties:
  key:
    type: string
    description: System-assigned key for the audit history.
    readOnly: true
    x-mappedTo: RECORDNO
    format: base64url
    example: OTA6MjgwOTU4
  id:
    type: string
    description: Unique identifier for the audit history record
    readOnly: true
    x-mappedTo: ID
    example: 103:305:DISPLAYCONTACT.INITIAL:JohnDoe
  accessDetails:
    type: object
    properties:
      objectName:
        type: string
        description: Name of the object that was accessed or modified or advanced-audit-trail-access if the audit does not refer to an object but to accesing special fields
        readOnly: true
        x-mappedTo: OBJECTTYPE
        example: company-config/contact
        enum:
          - company-config/contact
          - accounts-payable/vendor
          - accounts-receivable/customer
          - null
        x-mappedToValues:
          - contact
          - vendor
          - customer
          - aat_access
      objectId:
        type: string
        description: Unique name field of the object.
        readOnly: true
        x-mappedTo: OBJECTKEY
        example: '23'
  accessSource:
    type: object
    properties:
      completedBy:
        type: string
        description: Login ID of the user who modified the object.
        readOnly: true
        x-mappedTo: USERID
        example: Admin
      accessDateTime:
        type: string
        description: Date time at which the object was accessed.
        x-mappedTo: ACCESSTIME
        format: date-time
        readOnly: true
        example: '2024-02-13T11:28:45Z'
      actionPerformed:
        type: string
        description: The type of the action performed on the object.
        readOnly: true
        x-mappedTo: ACCESSMODE
        example: personalData
        enum:
          - personalDataAccess
          - dataChange
        x-mappedToValues:
          - Personal data access
          - System audit
      workflowAction:
        type: string
        description: If the modification made was due to a workflow action, this field contains that action.
        readOnly: true
        x-mappedTo: WORKFLOWACTION
        example: '5'
      clientIPAddress:
        type: string
        description: IP address of the end user that performed the action
        readOnly: true
        x-mappedTo: IPADDRESS
        example: '************'
      source:
        type: string
        description: Source of the access.
        x-mappedTo: SOURCE
        example: userInterface
        readOnly: true
        enum:
          - userInterface
          - api
          - csvImport
          - system
          - smartEvent
          - importService
        x-mappedToValues:
          - ui
          - api
          - csv
          - system
          - sev
          - flatf