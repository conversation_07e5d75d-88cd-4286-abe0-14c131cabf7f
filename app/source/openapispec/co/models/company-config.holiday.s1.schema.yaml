title: company-config-holiday
x-mappedTo: holidays
x-ownedBy: company-config/holiday-schedule
type: object
description: The holiday object specifies a holiday established for a company and the schedule to which that holiday belongs.
properties:
  key:
    type: string
    description: System-assigned key for the holiday.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '3'
  id:
    type: string
    description: Holiday ID.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '3'
  href:
    type: string
    description: Endpoint for the holiday.
    readOnly: true
    example: /objects/company-config/holiday/3
  name:
    type: string
    description: The name of the holiday.
    x-mappedTo: NAME
    example: Diwali
  holidayDate:
    type: string
    format: date
    description: The date of the holiday.
    x-mappedTo: HDATE
    example: '2020-05-31'
  holidaySchedule:
    type: object
    description: The schedule to which the holiday belongs.
    x-mappedTo: HOLIDAYSCHEDULE
    x-object: company-config/holiday-schedule
    properties:
      key:
        type: string
        description: System-assigned key for the holiday schedule.
        x-mappedTo: HOLIDAYSCHEDKEY
        example: '2'
      id:
        type: string
        description: The name of the holiday schedule.
        x-mappedTo: HOLIDAYSCHEDULENAME
        example: 'Holiday 2023'
      href:
        type: string
        description: Endpoint for the holiday schedule.
        readOnly: true
        example: /objects/company-config/holiday-schedule/2
  timesheetRule:
    type: object
    x-mappedTo: TSRULES
    x-object: time/timesheet-rule
    properties:
      key:
        type: string
        description: System-assigned key for the timesheet rule.
        x-mappedTo: TSRULESKEY
        example: '1'
      id:
        type: string
        description: Timesheet rules ID.
        x-mappedTo: TSRULESKEY
        example: '1'
      href:
        type: string
        description: Endpoint for the timesheet rule.
        readOnly: true
        example: /objects/time/timesheet-rule/1
  entity:
    type: object
    description: The entity where this holiday is observed.
    x-mappedTo: entity
    x-object: company-config/entity
    properties:
      key:
        type: string
        description: System-assigned key for entity.
        x-mappedTo: LOCATIONKEY
        example: '4'
      id:
        type: string
        description: Unique identifier of the entity. 20 characters max. The ID cannot be changed after the entity has been created.
        x-mappedTo: LOCATIONID
        x-mutable: false
        example: Central Region
      name:
        type: string
        description: Entity Name.
        readOnly: true
        x-mappedTo: LOCATIONNAME
        example: Central Region
      href:
        type: string
        readOnly: true
        description: Endpoint for the entity.
        example: /objects/company-config/entity/4
