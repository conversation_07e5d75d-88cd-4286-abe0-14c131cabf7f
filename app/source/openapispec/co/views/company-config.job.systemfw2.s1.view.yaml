key: company-config/job::systemCompany-config/jobFW2
id: systemCompany-config/jobFW2
object: company-config/job
name: IA.JOB_HISTORY
description: Shows all processed jobs
query:
  object: company-config/job
  fields:
    - jobType
    - action
    - details
    - userId
    - processingStatus
    - queuedDateTime
    - waitingInQueueDuration
    - startDateTime
    - executionDuration
  orderBy:
      - executionRequestDateTime: asc
  filters:
    - $in:
        processingStatus: ['delivered', 'failed', 'deferred', 'canceled']
contexts:
  - __default