openapi: 3.0.0
info:
  title: company-config-employee-rate
  description: company-config.employee-rate API
  version: '1.0'
  contact:
    name: <PERSON>
    email: <EMAIL>
tags:
  - name: Employee rates
    description: The employee rate object provides information about compensation for a specified employee and date range. The rate can be either hourly or annual.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/company-config/employee-rate:
    get:
      summary: List employee rates
      description: Returns a collection with a key, ID, and link for each employee rate. This operation is mostly for use in testing; use query to find objects that meet certain criteria and to specify properties that are returned.
      x-documentationFlags:
        subscription: Company
        userPermissions:
          - userType: Business, Employee, Project Manager, Warehouse
            permissions: List, View Employees
      tags:
        - Employee rates
      operationId: list-projects-employee-rate
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of employee rate objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List employee rates:
                  value:
                    'ia::result':
                      - key: '1'
                        id: '1'
                        href: /objects/company-config/employee-rate/1
                      - key: '3'
                        id: '3'
                        href: /objects/company-config/employee-rate/3
                      - key: '5'
                        id: '5'
                        href: /objects/company-config/employee-rate/5
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
  /objects/company-config/employee-rate/{key}:
    parameters:
      - name: key
        description: System-assigned unique key for the employee rate.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get an employee rate
      description: Returns detailed information for a specified employee rate.
      x-documentationFlags:
        subscription: Company
        userPermissions:
          - userType: Business, Employee, Project Manager, Warehouse
            permissions: List, View Employees
      tags:
        - Employee rates
      operationId: get-projects-employee-rate-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the employee rate
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/company-config-employee-rate'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get an employee rate:
                  value:
                    "ia::result":
                      id: "111"
                      key: "111"
                      employee:
                        key: "1"
                        id: "1"
                        name: "1099 Int"
                        href: "/objects/company-config/employee/1"
                      hourlyRate: "50001"
                      annualSalary: null
                      startDate: "2023-01-01"
                      endDate: "2023-12-31"
                      href: "/objects/company-config/employee-rate/111"
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete an employee rate
      description: Deletes an employee rate.
      x-documentationFlags:
        subscription: Company
        userPermissions:
          - userType: Business
            permissions: List, View, Delete Employees
      tags:
        - Employee rates
      operationId: delete-company-config-employee-rate-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    company-config-employee-rate:
      $ref: ../models/company-config.employee-rate.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
