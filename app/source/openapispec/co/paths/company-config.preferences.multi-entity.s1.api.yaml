openapi: 3.0.0
info:
  title: company-config-preferences-multi-entity
  description: preferences.multi-entity API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Multi-entity preferences
    description: In a multi-entity shared company, entities represent a separate tax identification or a separately secured, fully balancing set of books. Entities typically represent divisions, franchises, affiliates, associations, locations, chapters, self-balancing funds, or subsidiaries, with a shared chart of accounts.
servers:
  - url: 'http://localhost:3000'
paths:
  '/services/company-config/preferences/multi-entity':
    get:
      deprecated: true
      summary: Get multi-entity preferences
      description: Returns current multi entity preference settings.
      tags:
        - Multi-entity preferences
      operationId: get-services-preferences.multi-entity
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Multi-entity preferences
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                Current multi-entity preferences:
                  value:
                    'ia::result':
                      enableMultipleBaseCurrency: true
                      approvalCurrency: CAD
                      isForm1099: false
                      restrictions:
                        disableEntitySlideIn: false
                        restrictSubledgerTxnToEntity: false
                        restrictGLTxnToEntity: false
                        enableCustomerRestrictions: true
                        enableVendorRestrictions: true
                        enableCheckingAccountRestrictions: false
                        enableSavingsAccountRestrictions: false
                      interEntityAccountMappingPlan: basic
                      interEntityTxns:
                        enableForJournalEntry: true
                        manuallyBalanceSubledgerTxns: false
                        manuallyBalanceSubledgerCredits: true
                        creditsJournal:
                          key: '10'
                          id: IEP
                          name: Inter Entity Payable
                          href: /objects/general-ledger/journal/10
                      balanceJournalEntryBy: entity
                      limitAPCredit: true
                      limitARCredit: false
                      isModuleConfigured: true
                      enableAffiliateEntity: true
                      audit:
                        createdDateTime: '2024-09-10T12:09:05Z'
                        modifiedDateTime: '2024-09-10T12:09:05Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      href: /services/company-config/preferences/multi-entity
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      deprecated: true
      summary: Update multi-entity preferences
      description: Update the multi-entity preferences for a company. Any fields not provided remain unchanged.
      tags:
        - Multi-entity preferences
      operationId: update-services-preferences.multi-entity
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/company-config-preferences-multi-entity'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Enable checking account restrictions:
                value:
                  restrictions:
                    enableCheckingAccountRestrictions: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated preferences.multi-entity
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
components:
  schemas:
    company-config-preferences-multi-entity:
      $ref: ../models/company-config.preferences.multi-entity.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
