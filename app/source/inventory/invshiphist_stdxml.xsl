<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format" version="1.0">
    <xsl:import href="../../private/xslinc/report_helpers.xsl"/>
    <xsl:include href="../../private/xslinc/stylegroup_inc.xsl"/>
    <xsl:include href="../../private/xslinc/inventoryjs_inc.xsl"/>
    <xsl:variable name="newcustvend">
        <xsl:value-of select="//report/@custvendnoi18n"/>
    </xsl:variable>
    <xsl:template match="/">
        <xsl:apply-templates/>
    </xsl:template>
    <xsl:template match="reportdata">
        <xsl:apply-templates/>
    </xsl:template>
    <xsl:template match="report">
        <report showHeader="{@showHeader}" department="{@department}" location="{@location}" orientation="Landscape" report_date="{@reportdate}" report_time="{@reporttime}" align_currency="left" page_number="Y" action="" sess="{@sess}" done="{@done}" footer_allpages="Y">
            <xsl:if test="(@orientation = 'Portrait')">
                <xsl:attribute name="maxfit">Y</xsl:attribute>
            </xsl:if>
            <company s="2">
                <xsl:value-of select="@co"/>
            </company>
            <title s="3" titleNum="1">
                <xsl:value-of select="@title"/>
            </title>
            <title s="3" titleNum="2">
                <xsl:value-of select="@title2"/>
            </title>
            <footer s="5" lines="1" footerNum="1">
                <xsl:value-of select="@titlecomment"/>
            </footer>
            <xsl:for-each select="rtdim">
                <rtdim s="4" name="@name">
                    <name>
                        <xsl:value-of select="@name"/>
                    </name>
                    <value>
                        <xsl:value-of select="@value"/>
                    </value>
                </rtdim>
            </xsl:for-each>
            <header>
            <xsl:if test="number($narrow_format) = 1">
                <hrow s="header">
                    <hcol width="10" s="txt"/>
                    <hcol width="20" s="txt"/>
                    <hcol width="15" s="txt"/>
                    <hcol s="txt"/>
                    <hcol s="txt"/>
                    <hcol s="txt"/>
                    <xsl:if test="(//report/@ismcpEnabled = '1')">
                        <hcol s="txt"/>
                        <hcol s="txt"/>
                        <hcol s="txt"/>
                    </xsl:if>
                    <hcol s="txt"/>
                    <hcol s="txt"/>
                </hrow>
                <hrow s="51">
                    <hcol id="0" s="17">
                        IA.ITEM
                    </hcol>

                    <hcol id="0" s="17">
                        <xsl:value-of select="@custvend"/>
                    </hcol>
                    <hcol id="0" s="17">IA.ORDER_HASH</hcol>
                    <hcol id="0" s="18">
                        <xsl:if test="$newcustvend='vend'">
                            <xsl:text>IA.RECEIPT_DATE</xsl:text>
                        </xsl:if>
                        <xsl:if test="$newcustvend='cust'">
                            <xsl:text>IA.SHIP_DATE</xsl:text>
                        </xsl:if>
                    </hcol>
                    <hcol id="0" s="18">
                        <xsl:if test="$newcustvend='vend'">
                            <xsl:text>IA.QTY_RECEIVED</xsl:text>
                        </xsl:if>
                        <xsl:if test="$newcustvend='cust'">
                            <xsl:text>IA.QTY_SHIPPED</xsl:text>
                        </xsl:if>
                    </hcol>
                    <xsl:if test="(//report/@ismcpEnabled = '1')">
                        <hcol id="0" s="17">IA.CURR</hcol>
                        <hcol id="0" s="18">IA.TXN_PRICE</hcol>
                        <hcol id="0" s="18">IA.TXN_EXTENDED_PRICE</hcol>
                    </xsl:if>
                    <hcol id="0" s="18">IA.UNIT_PRICE</hcol>
                    <hcol id="0" s="18">IA.EXTENDED_PRICE</hcol>
                </hrow>
            </xsl:if>
            <xsl:if test="number($narrow_format) != 1">
			    <hrow s="51">
                    <xsl:choose>
                        <xsl:when test="//report/@term_Item = 'UPC'">
			    	        <hcol id="0" s="17">IA.ITEM</hcol>
                        </xsl:when>
                        <xsl:otherwise>
			    	        <hcol id="0" s="17">IA.ITEM_ID</hcol>
			    	        <hcol id="0" s="17">IA.ITEM_NAME</hcol>
                        </xsl:otherwise>
                    </xsl:choose>

			    	<hcol id="0" s="17"><xsl:value-of select="@custvend"/></hcol>
			    	<hcol id="0" s="17">IA.ORDER_HASH</hcol>
			    	<hcol id="0" s="18">
			    		<xsl:if test="$newcustvend='vend'">
			    			<xsl:text>IA.RECEIPT_DATE</xsl:text>
			    		</xsl:if>
			    		<xsl:if test="$newcustvend='cust'">
			    			<xsl:text>IA.SHIP_DATE</xsl:text>
			    		</xsl:if>
			    	</hcol>
			    	<hcol id="0" s="18">
			    		<xsl:if test="$newcustvend='vend'">
			    			<xsl:text>IA.QTY_RECEIVED</xsl:text>
			    		</xsl:if>
			    		<xsl:if test="$newcustvend='cust'">
			    			<xsl:text>IA.QTY_SHIPPED</xsl:text>
			    		</xsl:if>
			    	</hcol>
			    	<xsl:if test="(//report/@ismcpEnabled = '1')">
			    		<hcol id="0" s="18">IA.CURR</hcol>
			    		<hcol id="0" s="18">IA.TXN_PRICE</hcol>
			    		<hcol id="0" s="18">IA.TXN_EXTENDED_PRICE</hcol>
			    	</xsl:if>
			    	<hcol id="0" s="18">IA.UNIT_PRICE</hcol>
			    	<hcol id="0" s="18">IA.EXTENDED_PRICE</hcol>
			    </hrow>
            </xsl:if>
            </header>
            <body s="body">
                <xsl:apply-templates/>
            </body>
            <xsl:call-template name="stylegroups"/>
            <script language="javascript">
                <xsl:apply-templates select="@javascript"/>
                <xsl:call-template name="script"/>
            </script>
        </report>
    </xsl:template>
    <xsl:template match="NODATA">
        <xsl:if test="string(@NODATA)=1">
            <row s="14">
                <col id="0" s="21" colspan="20">IA.NO_DATA_FOUND</col>
            </row>
        </xsl:if>
    </xsl:template>


    <xsl:template match="ITEMS">
        <xsl:choose>
            <xsl:when test="number($narrow_format) = 1"><xsl:call-template name="ITEMS_NARROW"/></xsl:when>
            <xsl:otherwise><xsl:call-template name="ITEMS_NORMAL"/></xsl:otherwise>
        </xsl:choose>
    </xsl:template>


	<xsl:template name="ITEMS_NARROW">
    	<xsl:variable name="colspanwidth">
		<xsl:choose>
			<xsl:when test="(//report/@ismcpEnabled = '1')">
				<xsl:value-of select="10"/>
			</xsl:when>
			<xsl:otherwise>
				<xsl:value-of select="7"/>
			</xsl:otherwise>
		</xsl:choose>
	</xsl:variable>

		<row s="12">
			<xsl:choose>
				<xsl:when test="(//report/@term_Item = 'UPC')">
					<col id="0" colspan="{$colspanwidth}" s="23">
						<xsl:attribute name="href">
							<xsl:value-of select="@ITEMHREF"/>
						</xsl:attribute>
						<xsl:value-of select="@UPC"/>
					</col>
				</xsl:when>
				<xsl:otherwise>
					<col id="0" colspan="{$colspanwidth}" s="23">
						<xsl:attribute name="href">
							<xsl:value-of select="@ITEMHREF"/>
						</xsl:attribute>
						<xsl:value-of select="@ITEMKEY"/>
							<xsl:text>--</xsl:text>
						<xsl:value-of select="@ITEMDESCR"/>
					</col>
				</xsl:otherwise>
			</xsl:choose>
			
		</row>
		<xsl:apply-templates mode="summary"/>
        <xsl:if test="count(ENTRIES) > 1">
		    <row s="13">
		    	<col id="0" s="59">
		    		<xsl:text>IA.TOTAL_FOR </xsl:text>
		    	</col>
		    	<xsl:choose>
		    		<xsl:when test="(//report/@term_Item = 'UPC')">
		    			<col id="0" s="59" colspan="2">
		    				<xsl:value-of select="@UPC"/>
		    			</col>
		    		</xsl:when>
		    		<xsl:otherwise>
		    			<col id="0" s="59" colspan="2">
		    				<xsl:value-of select="@ITEMKEY"/>
		    			</col>
		    		</xsl:otherwise>
		    	</xsl:choose>
		    	<col id="0" s="24"></col>
		    	
		    	<col id="0" s="30"><xsl:value-of select="@TOTALQUANTITY"/></col>
		    	<xsl:choose>
		    		<xsl:when test="(//report/@ismcpEnabled = '1')">
                        <col id="0" s="59" />
                        <col id="0" s="59" />
                        <col id="0" s="59" />
                        <col id="0" s="59" />
		    		<!--	<col id="0" s="30" ><xsl:value-of select="@TOTALUNITCOST"/></col> -->
		    			<col id="0" s="30" ><xsl:value-of select="@TOTALVALUE"/></col>
		    		</xsl:when>
		    		<xsl:otherwise>
                        <col id="0" s="59" />
		    			<!--<col id="0" s="30" ><xsl:value-of select="@TOTALUNITCOST"/></col>-->
		    			<col id="0" s="30" ><xsl:value-of select="@TOTALVALUE"/></col>
		    		</xsl:otherwise>
		    	</xsl:choose>
		    </row>
            <xsl:call-template name="add_blank_row" />
        </xsl:if>
        <xsl:choose>
            <xsl:when test="count(ENTRIES) = 1 and count(following-sibling::*[1]/ENTRIES) > 1">
                <xsl:call-template name="add_blank_row" />
            </xsl:when>
            <xsl:otherwise></xsl:otherwise>
        </xsl:choose>
	</xsl:template>


    <xsl:template name="ITEMS_NORMAL">
        <xsl:variable name="colspanwidth">
            <xsl:choose>
                <xsl:when test="(//report/@ismcpEnabled = '1')">
                    <xsl:value-of select="10"/>
                </xsl:when>
                <xsl:otherwise>
                    <xsl:value-of select="7"/>
                </xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:apply-templates mode="summary"/>
        <xsl:if test="count(ENTRIES) > 1">
            <row s="60">
                <col id="0" s="59">
                    <xsl:text>IA.TOTAL_FOR </xsl:text>
                    <xsl:choose>
                        <xsl:when test="(//report/@term_Item = 'UPC')">
                                <xsl:value-of select="@UPC"/>
                        </xsl:when>
                        <xsl:otherwise>
                                <xsl:value-of select="@ITEMKEY"/>
                        </xsl:otherwise>
                    </xsl:choose>
                </col>
                <xsl:choose>
                    <xsl:when test="//report/@term_Item = 'UPC' or number($narrow_format) = 1">
                        <col id="0" s="59"/>
                    </xsl:when>
                    <xsl:otherwise>
                        <col id="0" s="59"/>
                        <col id="0" s="59"/>
                    </xsl:otherwise>
                </xsl:choose>
                <col id="0" s="59"/>
                <col id="0" s="59"/>
                <col id="0" s="62">
                    <xsl:value-of select="@TOTALQUANTITY"/>
                </col>
                <xsl:choose>
                    <xsl:when test="(//report/@ismcpEnabled = '1')">
                        <col id="0" s="19" />
                        <col id="0" s="22" >
                        </col>
                        <col id="0" s="22" />
                        <col id="0" s="22" />
                        <col id="0" s="30" >
                            <xsl:value-of select="@TOTALVALUE"/>
                        </col>
                    </xsl:when>
                    <xsl:otherwise>
                        <col id="0" s="22"/>
                        <col id="0" s="30">
                            <xsl:value-of select="@TOTALVALUE"/>
                        </col>
                    </xsl:otherwise>
                </xsl:choose>
            </row>
            <xsl:call-template name="add_blank_row" />
        </xsl:if>
        <xsl:choose>
            <xsl:when test="count(ENTRIES) = 1 and count(following-sibling::*[1]/ENTRIES) > 1">
                <xsl:call-template name="add_blank_row" />
            </xsl:when>
            <xsl:otherwise></xsl:otherwise>
        </xsl:choose>

    </xsl:template>

    <xsl:template match="ENTRIES" mode="summary">
        <xsl:variable name="rownum">
            <xsl:number level="single"/>
        </xsl:variable>
        <row s="14">
            <xsl:choose>
                <xsl:when test="$rownum = '1' and number($narrow_format) = 0">
                    <xsl:choose>
                        <xsl:when test="(//report/@term_Item = 'UPC')">
                            <col id="0" s="24">
                                <xsl:attribute name="href">
                                    <xsl:value-of select="../@ITEMHREF"/>
                                </xsl:attribute>
                                <xsl:value-of select="../@UPC"/>
                            </col>
                        </xsl:when>
                        <xsl:when test="number($narrow_format) = 1">
                            <col id="0" s="24">
                                <xsl:attribute name="href">
                                    <xsl:value-of select="../@ITEMHREF"/>
                                </xsl:attribute>
                                <xsl:value-of select="../@ITEMKEY"/>
                                <xsl:text>--</xsl:text>
                                <xsl:value-of select="../@ITEMDESCR"/>
                            </col>
                        </xsl:when>
                        <xsl:otherwise>
                            <col id="0" s="24">
                                <xsl:attribute name="href">
                                    <xsl:value-of select="../@ITEMHREF"/>
                                </xsl:attribute>
                                <xsl:value-of select="../@ITEMKEY"/>
                            </col>
                            <col id="0" s="24">
                                <xsl:attribute name="href">
                                    <xsl:value-of select="../@ITEMHREF"/>
                                </xsl:attribute>
                                <xsl:value-of select="../@ITEMDESCR"/>
                            </col>
                        </xsl:otherwise>
                    </xsl:choose>
                </xsl:when>
                <xsl:otherwise>
                    <xsl:choose>
                        <xsl:when test="//report/@term_Item = 'UPC' or number($narrow_format) = 1">
                            <col id="0" s="24"/>
                        </xsl:when>
                        <xsl:otherwise>
                            <col id="0" s="24"/>
                            <col id="0" s="24"/>
                        </xsl:otherwise>
                    </xsl:choose>

                </xsl:otherwise>
            </xsl:choose>
            <col id="0" s="19">
                <xsl:attribute name="href">
                    <xsl:value-of select="@HREF"/>
                </xsl:attribute>
                <xsl:value-of select="@ID"/>
                <xsl:text>--</xsl:text>
                <xsl:value-of select="@NAME"/>
            </col>
            <col id="0" s="19">
                <xsl:attribute name="href">
                    <xsl:value-of select="@DOCIDHREF"/>
                </xsl:attribute>
                <xsl:value-of select="@DOCID"/>
            </col>
            <col id="0" s="46">
                <xsl:value-of select="@WHENCREATED"/>
            </col>
            <col id="0" s="22">
                <xsl:value-of select="@QUANTITY"/>
            </col>
            <xsl:if test="(//report/@ismcpEnabled = '1')">
                <col id="0" s="21">
                    <xsl:value-of select="@CURRENCY"/>
                </col>
                <col id="0" s="34">
                    <xsl:value-of select="@TRXPRICE"/>
                </col>
                <col id="0" s="34">
                    <xsl:value-of select="@TRX_VALUE"/>
                </col>
            </xsl:if>
            <col id="0" s="34">
                <xsl:value-of select="@PRICE"/>
            </col>
            <col id="0" s="34">
                <xsl:value-of select="@VALUE"/>
            </col>
        </row>
    </xsl:template>
</xsl:stylesheet>
