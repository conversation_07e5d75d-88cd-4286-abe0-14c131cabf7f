<?php

/**
 * Entity file for cost history
 */
$kSchemas['costchangehistory'] = array(
    'object' => array(
        'RECORDNO',
        'HLTHRUNKEY',               // parent record's key (record#)
        'COSTINGUPDATEDON',         // the time range in which the costing changed for the item/warehouse
        'PRIORCOSTINGUPDATEDON',

        'ISASUMMARY',               // summary means the SUM(docentrycost); if one docentrycost, then is both SUMMARY AND DETAIL
        'ISADETAIL',                // one docentrycost record

        'TRANSACTIONEFFECTIVEDATE', // the transaction's AS OF date
        'TRANSACTIONNAME',          // the TD name, like 'Sales Invoice'
        'TRANSACTIONID',            // the transaction ID part of the name, like 'ABC123'
        'ITEMID',
        'WAREHOUSEID',

        'QUANTITYBEFORE',           // BEFORE values are BEFORE costing code ran; AFTER are after it ran
        'QUANTITYAFTER',
        'QUANTITYQOH',              // running quantity on hand for quantity transactions using QUANTITYAFTER
        'VALUEQOH',                 // same for value transactions using QUANTITYAFTER
        'UNITCOSTBEFORE',           // base unit cost, not transaction unit cost
        'UNITCOSTAFTER',
        'VALUEBEFORE',              // QUANTITYBEFORE * UNITCOSTBEFORE if a value transaction
        'VALUEAFTER',               // QUANTITYAFTER  * UNITCOSTAFTER  if a value transaction
        'COGSPOSTEDBEFORE',         // may only appear on the summary line and ONE detail line
        'COGSPOSTEDAFTER',
        'COGSADJUSTMENTSPOSTED',   // adjustments made through the Cogs Closed Adjustments tool
        'COGSADJUSTMENTNEEDED',     // Adjustments needed through COGS CLOSED JE tool (Value After -  COGS Posted After - COGS Adjustments Posted) (SUMMARY ONLY)
        'COGSCLOSED',               // is the period or COGS batch closed (and so not updateable)?
        'SALEPURCHASEORINTERNAL',   // S, P, or I for Sales, Purchase, or Internal transaction (like a transfer or adjustment)
        'INCREMENTORDECREMENT',     // I or D; I increases QOH, D decreases QOH
        'QUANTITYORVALUE',          // N, Q, V, or QV (this mimics the existing transaction records), (N means NONE)
        'COSTMETHOD',               // (A)verage, (F)IFO, (L)IFO, or (S)tandard
        'SOURCETRANSACTIONBEFORE',  // like Purchase Receiver-ABX123
        'SOURCETRANSACTIONAFTER',   // like Purchase Receiver-ABX321
        'CONVERTEDFROMTRANSACTION', // transaction this transaction was converted FROM (like Sales Invoice-ABC1234)
        'STATE',                    // like 'ADDED' or 'DELETED' or 'CHANGED' or 'INBOUND'

        'TXNCREATEDDATE',           // when the transaction was created
        'TXNCREATEDBY',             // who created it
        'TXNMODIFIEDDATE',          // when the transaction was modified
        'TXNMODIFIEDBY',            // who modified it
        'NOTES',
    ),

    'publish' => array(

        'COSTINGUPDATEDON',         // the time range in which the costing changed for the item/warehouse
        'PRIORCOSTINGUPDATEDON',

        'ISASUMMARY',               // summary means the SUM(docentrycost); if one docentrycost, then is both SUMMARY AND DETAIL
        'ISADETAIL',                // one docentrycost record

        'TRANSACTIONEFFECTIVEDATE', // the transaction's AS OF date
        'TRANSACTIONNAME',          // the TD name, like 'Sales Invoice'
        'TRANSACTIONID',            // the transaction ID part of the name, like 'ABC123'
        'ITEMID',
        'WAREHOUSEID',

        'QUANTITYBEFORE',           // BEFORE values are BEFORE costing code ran; AFTER are after it ran
        'QUANTITYAFTER',
        'QUANTITYQOH',              // running quantity on hand for quantity transactions using QUANTITYAFTER
        'VALUEQOH',                 // same for value transactions using QUANTITYAFTER
        'UNITCOSTBEFORE',           // base unit cost, not transaction unit cost
        'UNITCOSTAFTER',
        'VALUEBEFORE',              // QUANTITYBEFORE * UNITCOSTBEFORE if a value transaction
        'VALUEAFTER',               // QUANTITYAFTER  * UNITCOSTAFTER  if a value transaction
        'COGSPOSTEDBEFORE',         // may only appear on the summary line and ONE detail line
        'COGSPOSTEDAFTER',
        'COGSADJUSTMENTSPOSTED',   // adjustments made through the Cogs Closed Adjustments tool
        'COGSADJUSTMENTNEEDED',     // Adjustments needed through COGS CLOSED JE tool (Value After -  COGS Posted After - COGS Adjustments Posted) (SUMMARY ONLY)
        'COGSCLOSED',               // is the period or COGS batch closed (and so not updateable)?
        'SALEPURCHASEORINTERNAL',   // S, P, or I for Sales, Purchase, or Internal transaction (like a transfer or adjustment)
        'INCREMENTORDECREMENT',     // I or D; I increases QOH, D decreases QOH
        'QUANTITYORVALUE',          // N, Q, V, or QV (this mimics the existing transaction records), (N means NONE)
        'COSTMETHOD',               // (A)verage, (F)IFO, (L)IFO, or (S)tandard
        'SOURCETRANSACTIONBEFORE',  // like Purchase Receiver-ABX123
        'SOURCETRANSACTIONAFTER',   // like Purchase Receiver-ABX321
        'CONVERTEDFROMTRANSACTION', // transaction this transaction was converted FROM (like Sales Invoice-ABC1234)
        'STATE',                    // like 'ADDED' or 'DELETED' or 'CHANGED' or 'INBOUND'

        'TXNCREATEDDATE',           // when the transaction was created
        'TXNCREATEDBY',             // who created it
        //'TXNCREATEDBYID',           // who created it
        'TXNMODIFIEDDATE',          // when the transaction was modified
        'TXNMODIFIEDBY',            // who modified it
        //'TXNMODIFIEDBYID',          // who modified it
        'NOTES',
    ),


    'schema' => array(
        'RECORDNO' => 'record#',
        'HLTHRUNKEY' => 'hlthrunkey',
        'COSTINGUPDATEDON' => 'costingupdatedon',
        'PRIORCOSTINGUPDATEDON' => 'priorcostingupdatedon',
        'ISASUMMARY' => 'isasummary',
        'ISADETAIL' => 'isadetail',
        'TRANSACTIONEFFECTIVEDATE' => 'transactioneffectivedate',
        'TRANSACTIONNAME' => 'transactionname',
        'TRANSACTIONID' => 'transactionid',
        'ITEMID' => 'itemid',
        'WAREHOUSEID' => 'warehouseid',
        'QUANTITYBEFORE' => 'quantitybefore',
        'QUANTITYAFTER' => 'quantityafter',
        'QUANTITYQOH' => 'quantityqoh',
        'VALUEQOH' => 'valueqoh',
        'UNITCOSTBEFORE' => 'unitcostbefore',
        'UNITCOSTAFTER' => 'unitcostafter',
        'VALUEBEFORE' => 'valuebefore',
        'VALUEAFTER' => 'valueafter',
        'COGSPOSTEDBEFORE' => 'cogspostedbefore',
        'COGSPOSTEDAFTER' => 'cogspostedafter',
        'COGSADJUSTMENTSPOSTED' => 'cogsadjustmentsposted',
        'COGSADJUSTMENTNEEDED' => 'cogsadjustmentneeded',
        'COGSCLOSED' => 'cogsclosed',
        'SALEPURCHASEORINTERNAL' => 'salepurchaseorinternal',
        'INCREMENTORDECREMENT' => 'incrementordecrement',
        'QUANTITYORVALUE' => 'quantityorvalue',
        'COSTMETHOD' => 'costmethod',
        'SOURCETRANSACTIONBEFORE' => 'sourcetransactionbefore',
        'SOURCETRANSACTIONAFTER' => 'sourcetransactionafter',
        'CONVERTEDFROMTRANSACTION' => 'convertedfromtransaction',
        'STATE' => 'state',
        'TXNCREATEDDATE' => 'txncreateddate',
        'TXNCREATEDBY' => 'txncreatedby',
        'TXNMODIFIEDDATE' => 'txnmodifieddate',
        'TXNMODIFIEDBY' => 'txnmodifiedby',
        'NOTES' => 'notes',
    ),

    // for now, publish is the same as object
    //'publish' => array(
    //),

    'children' => array(
        'item' => array(
            'fkey' => 'itemid', 'invfkey' => 'itemid', 'table' => 'icitem', 'join' => 'inner'
        ),
        'warehouse' => array(
            'fkey' => 'warehouseid', 'invfkey' => 'location_no', 'table' => 'icwarehouse', 'join' => 'inner'
        ),
        'userinfo'  => array( 'fkey'  => 'txncreatedby',  'invfkey' => 'record#', 'join' => 'outer', 'table' => 'userinfomst', ),
        'userinfo2' => array( 'fkey'  => 'txnmodifiedby', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'userinfomst', ),
    ),

    'nexus' => array(
        'item' => array(
            'object' => 'item',
            'relation' => 'ONE2ONE',
            'field' => 'ITEMID',
        ),
        'warehouse' => array(
            'object' => 'warehouse',
            'relation' => 'ONE2ONE',
            'field' => 'WAREHOUSEID',
        ),
        'userinfo'  => array(
            'object' => 'userinfo',
            'relation' => MANY2ONE,
            'field' => 'userid',
            'printas' => 'IA.CREATED_BY_USER',
        ),
        'userinfo2'  => array(
           'object' => 'userinfo',
           'relation' => MANY2ONE,
           'field' => 'userid',
           'printas' => 'IA.LAST_UPDATED_USER',
        ),
    ),

    'fieldinfo' => array(

        array (
            'path' => 'RECORDNO',
            'fullname' => 'IA.RECORD',
            'type' => array (
                'ptype' => 'sequence',
                'type' => 'integer',
            ),
        ),
        array(
            'path' => 'COSTINGUPDATEDON',
            'fullname' => 'IA.COSTING_UPDATED_ON',
            'desc' => 'IA.WHEN_THE_MIV_TOOL_RAN_ON_THIS_TRANSACTION',
            'type' => $gTimestampType,
        ),
        array(
            'path' => 'PRIORCOSTINGUPDATEDON',
            'fullname' => 'IA.PRIOR_COSTING_UPDATED_ON',
            'desc' => 'IA.PRIOR_TIME_THE_MIV_TOOL_RAN_ON_THIS_TRANSACTION',
            'type' => $gTimestampType,
        ),
        array(
            'fullname' => 'IA.IS_A_SUMMARY',
            'type' => $gBooleanType,
            'default' => 'false',
            'path' => 'ISASUMMARY',
            'readonly' => true,
        ),
        array(
            'fullname' => 'IA.IS_A_DETAIL',
            'type' => $gBooleanType,
            'default' => 'false',
            'path' => 'ISADETAIL',
            'readonly' => true,
        ),
        array(
            'path'      => 'TRANSACTIONEFFECTIVEDATE',
            'fullname'  => 'IA.TRANSACTION_DATE',
            'type'      => array(
                'type' => 'date',
                'ptype' => 'date',
                'format' => $gDateFormat,
                'maxlength' => 12,
            ),
        ),
        array (
            'path' => 'TRANSACTIONNAME',
            'fullname' => 'IA.TRANSACTION_NAME',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'size' => 40,
            ),
        ),
        array (
            'path' => 'TRANSACTIONID',
            'fullname' => 'IA.TRANSACTION_ID',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'size' => 40,
            ),
        ),
        array (
            'path' => 'ITEMID',
            'fullname' => 'IA.ITEM',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'size' => 40,
            ),
        ),
        array (
            'path' => 'WAREHOUSEID',
            'fullname' => 'IA.WAREHOUSE',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'size' => 40,
            ),
        ),
        array(
            'fullname' => 'IA.QUANTITY_BEFORE',
            'path' => 'QUANTITYBEFORE',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 20,
                'size' => 12,
                'format' => $gDecimalFormat,
            ),
        ),
        array(
            'fullname' => 'IA.QUANTITY_AFTER',
            'path' => 'QUANTITYAFTER',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 20,
                'size' => 12,
                'format' => $gDecimalFormat,
            ),
        ),
        array(
            'fullname' => 'IA.QUANTITY_QOH',
            'path' => 'QUANTITYQOH',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 20,
                'size' => 12,
                'format' => $gDecimalFormat,
            ),
        ),
        array(
            'fullname' => 'IA.VALUE_QOH',
            'path' => 'VALUEQOH',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 20,
                'size' => 12,
                'format' => $gDecimalFormat,
            ),
        ),
        array(
            'fullname' => 'IA.UNIT_COST_BEFORE',
            'path' => 'UNITCOSTBEFORE',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 20,
                'size' => 12,
                'format' => $gDecimalFormat,
            ),
        ),
        array(
            'fullname' => 'IA.UNIT_COST_AFTER',
            'path' => 'UNITCOSTAFTER',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 20,
                'size' => 12,
                'format' => $gDecimalFormat,
            ),
        ),
        array(
            'fullname' => 'IA.VALUE_BEFORE',
            'path' => 'VALUEBEFORE',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 20,
                'size' => 12,
                'format' => $gDecimalFormat,
            ),
        ),
        array(
            'fullname' => 'IA.VALUE_AFTER',
            'path' => 'VALUEAFTER',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 20,
                'size' => 12,
                'format' => $gDecimalFormat,
            ),
        ),
        array(
            'fullname' => 'IA.COGS_POSTED_BEFORE',
            'path' => 'COGSPOSTEDBEFORE',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 20,
                'size' => 12,
                'format' => $gDecimalFormat,
            ),
        ),
        array(
            'fullname' => 'IA.COGS_POSTED_AFTER',
            'path' => 'COGSPOSTEDAFTER',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 20,
                'size' => 12,
                'format' => $gDecimalFormat,
            ),
        ),
        array(
            'fullname' => 'IA.COGS_ADJUSTMENTS_POSTED',
            'path' => 'COGSADJUSTMENTSPOSTED',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 20,
                'size' => 12,
                'format' => $gDecimalFormat,
            ),
        ),
        array(
            'fullname' => 'IA.COGS_ADJUSTMENTS_NEEDED',
            'path' => 'COGSADJUSTMENTNEEDED',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 20,
                'size' => 12,
                'format' => $gDecimalFormat,
            ),
        ),
        array(
            'fullname' => 'IA.COGS_CLOSED',
            'type' => $gBooleanType,
            'default' => 'false',
            'path' => 'COGSCLOSED',
            'readonly' => true,
        ),
        array (
            'fullname' => 'IA.SALE_PURCHASE_OR_INTERNAL',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 10,
                'size' => 3,
            ),
            'path' => 'SALEPURCHASEORINTERNAL',
            'readonly' => true,
        ),
        array (
            'fullname' => 'IA.INCREMENT_OR_DECREMENT',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 10,
                'size' => 3,
            ),
            'path' => 'INCREMENTORDECREMENT',
            'readonly' => true,
        ),
        array (
            'fullname' => 'IA.QUANTITY_OR_VALUE',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 10,
                'size' => 3,
            ),
            'path' => 'QUANTITYORVALUE',
            'readonly' => true,
        ),
        array (
            'fullname' => 'IA.COST_METHOD',
            'type' => array (
                'ptype' => 'enum',
                'type' => 'text',
                'validvalues' => $gCostMethodValues,
                '_validivalues' => $gCostMethodInternalValues,
                'validlabels' => $gCostMethodLabels,
            ),
            'path' => 'COSTMETHOD',
            'readonly' => true,
        ),
        array (
            'path' => 'SOURCETRANSACTIONBEFORE',
            'fullname' => 'IA.SOURCE_TRANSACTION_BEFORE',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'size' => 40,
            ),
        ),
        array (
            'path' => 'SOURCETRANSACTIONAFTER',
            'fullname' => 'IA.SOURCE_TRANSACTION_AFTER',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'size' => 40,
            ),
        ),
        array (
            'path' => 'CONVERTEDFROMTRANSACTION',
            'fullname' => 'IA.CONVERTED_FROM_TRANSACTION',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'size' => 40,
            ),
        ),
        array (
            'path' => 'STATE',
            'fullname' => 'IA.STATE',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'size' => 40,
            ),
        ),
        array(
            'path'      => 'TXNCREATEDDATE',
            'fullname'  => 'IA.TXN_CREATED_DATE',
            'desc'      => 'IA.TIMESTAMP_TRANSACTION_WAS_CREATED',
            'readonly'  =>  true,
            'noapiadd'  =>  true,
            'noapiset'  =>  true,
            'type'      =>  $gDateType,
        ),
        array(
            'path'      => 'TXNCREATEDBY',
            'fullname'  => 'IA.TXN_CREATED_BY',
            'desc'      => 'IA.USER_WHO_CREATED_THE_TXN',
            'readonly'  =>  true,
            'noapiadd'  =>  true,
            'noapiset'  =>  true,
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
            ),
        ),
        array(
            'path'      => 'TXNCREATEDBYID',
            'fullname'  => 'IA.TXN_CREATED_BY',
            'desc'      => 'IA.USER_WHO_CREATED_THE_TXN',
            'readonly'  =>  true,
            'noapiadd'  =>  true,
            'noapiset'  =>  true,
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
            ),
        ),
        array(
           'path'      => 'TXNMODIFIEDDATE',
           'fullname'  => 'IA.TXN_MODIFIED_DATE',
           'desc'      => 'IA.TIMESTAMP_TRANSACTION_WAS_MODIFIED',
           'readonly'  =>  true,
           'noapiadd'  =>  true,
           'noapiset'  =>  true,
           'type'      =>  $gDateType,
        ),
        array(
            'path'      => 'TXNMODIFIEDBY',
            'fullname'  => 'IA.TXN_MODIFIED_BY',
            'desc'      => 'IA.USER_WHO_MODIFIED_TXN',
            'readonly'  =>  true,
            'noapiadd'  =>  true,
            'noapiset'  =>  true,
            'type'      => array(
                'type' => 'text',
                'ptype' => 'text',
            ),
        ),
        array(
            'path'      => 'TXNMODIFIEDBYID',
            'fullname'  => 'IA.TXN_MODIFIED_BY',
            'desc'      => 'IA.USER_WHO_MODIFIED_TXN',
            'readonly'  =>  true,
            'noapiadd'  =>  true,
            'noapiset'  =>  true,
            'type'      => array(
                'type' => 'text',
                'ptype' => 'text',
            ),
        ),

        array (
            'path' => 'NOTES',
            'fullname' => 'IA.NOTES',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'size' => 50,
            ),
        ),

    ),

    'api' => array(
        'PERMISSION_READ'   => 'lists/invdocument',
        'PERMISSION_CREATE' => 'NONE',
        'PERMISSION_UPDATE' => 'NONE',
        'PERMISSION_DELETE' => 'NONE',
        'PERMISSION_MODULES' => array('inv'),
    ),
    'printas' => 'IA.COST_CHANGE_HISTORY',
    'pluralprintas' => 'IA.COST_CHANGE_HISTORIES',
    'table' => 'costchangehistory',
    'module' => 'inv',
    'autoincrement' => 'RECORDNO',
    'vid' => 'RECORDNO',
    'auditcolumns' => false,
    'parententity' => 'invhlthrun',
    'renameable' => true,
    'nochatter' => true,
    'url' => array(
        'no_short_url' => true,    // Don't allow short url in custom reports
    ),
    'description' => 'IA.LIST_OF_CHANGES_TO_COGS_DUE_TO_THE_INVENTORY_REVAL',

);
