#Makefile.in
#

ENTITY_XMLS=                           \
	$(EMPTY)

ENTITY_ENTS=                           \
	dochistory.ent\
	invdochistory.ent\
	docmessage.ent                     \
	document.ent                       \
	documententry.ent                  \
	documentsubtotals.ent                  \
	invdocumentsubtotals.ent                  \
	invdocumentparentityprop.ent		\
	documententrytrack.ent                  \
	documententrytrackdetail.ent                  \
	invdocumententrytrackdetail.ent                  \
	documententrytrackkit.ent                  \
	documentparams.ent                 \
	documentparinvgl.ent                  \
	invdocumentparinvgl.ent                  \
	documentparprgl.ent                  \
	documentparsubtotal.ent            \
	invdocumentparsubtotal.ent            \
	documentpartotals.ent              \
	invdocumentpartotals.ent              \
	docrecalls.ent	              \
	invdocrecalls.ent	              \
	invbatch.ent                       \
	invdocument.ent                    \
	invdocumententry.ent                    \
	invdocumentparams.ent              \
	inventorycycle.ent                 \
	invpricelist.ent                   \
	invpricelistentry.ent              \
	invtotal.ent                       \
	item.ent                           \
	itemcomponent.ent                           \
	itemglgroup.ent                    \
	itemvendor.ent                     \
	itemlandedcost.ent                 \
	itemwarehouseinfo.ent              \
	itemwarehousestdcost.ent           \
	itemcrossref.ent                   \
	inventorytotaldetail.ent          \
	productline.ent                    \
	recurdocument.ent		   \
	recurdocumententry.ent		   \
	recursubtotals.ent		   \
	uom.ent                            \
	uomdetail.ent                      \
	serialmask.ent                     \
	landedcostcategory.ent			   \
	lotcategory.ent			   \
	warehouse.ent                      \
    warehousepick.ent                  \
    warehousegroup.ent               \
    warehousegrpmember.ent           \
	warehousesubsection.ent            \
	warehouseaisle.ent		   \
	warehouserow.ent		   \
	warehousebin.ent		   \
	itempick.ent            \
	serialpick.ent            \
	lotpick.ent            \
	aisle.ent            \
	icrow.ent            \
	bin.ent            \
	upcpick.ent		\
	invrecurdocument.ent			   \
	invrecursubtotals.ent		\
	stkitdocument.ent		   \
	stkitdocumententry.ent		   \
	stkitdocumententrycost.ent                  \
	detotals.ent						\
    itemgroup.ent \
    itemgrpmember.ent \
    itemngrouppick.ent \
    kitcosting.ent \
    documententrysubtotals.ent \
    costingchanges.ent \
	invhlthrun.ent \
	invhlthrundetail.ent \
	scmmacro.ent    \
    scmmacroentry.ent  \
   	scmmacroresolve.ent  \
	aislepick.ent       \
	documententrycomponent.ent  \
	invsetup.ent                \
	itemsonlypick.ent           \
	kitpick.ent                 \
	lotattributes.ent           \
	stkitpick.ent               \
	documententrycost.ent       \
	invdocumententrycost.ent \
	ictransfer.ent \
	ictransferitem.ent \
	kitsallpick.ent \
	itemwarehousevendor.ent \
	replenishment.ent \
	replenishdetail.ent \
	replenishmentrun.ent \
	replenishrundetail.ent \
	cogsclosedje.ent \
	replenishforecast.ent \
	replenishforecastdetail.ent \
    replenishforecastitemwarehpick.ent \
    replenishforecastwarehousepick.ent \
    replenishforecastwarepick2.ent \
    replenishforecastitempick.ent \
    costchangehistory.ent \
    zone.ent            \
    binsize.ent            \
    binface.ent            \
    iccyclecount.ent \
    iccyclecountentry.ent \
    availableinventory.ent \
    deglposting.ent \
    ietcogsposting.ent \
    deglsubtotalposting.ent \
    priorperiodcogsposting.ent \
    inventoryworkqueuedetails.ent \
    inventoryworkqueue.ent \
    inventorywqorder.ent \
    inventorywqdetailtracking.ent \
    inventorywqdetail.ent \
    invsuppliesdocument.ent \
    invsupplies.ent \
    opentransitdocument.ent \
    documententryglresolve.ent \
	$(EMPTY)

#
# no generated queries, but custom queries:
#   (list the custom query files for which no .qry file is generated from a .ent file)
#
CUSTOM_ONLY_QUERIES=    \
    invjournal.cqry     \
    invregister.cqry    \
    invstatus.cqry      \
    invtracking.cqry    \
    invvaluation.cqry   \
    itemlist.cqry       \
    lotcat.cqry         \
    pricelistrep.cqry   \
    docparusers.cqry    \
    $(EMPTY)

LAYOUT_XMLS=                           \
	document_layout_edit.xml           \
	documentparams_layout_edit.xml     \
	documentparams_advanced_layout_edit.xml     \
	docparwiz_layout_edit.xml			\
	invdocumentparams_form.xml  \
	invdocument_layout_edit.xml        \
	invpricelistentry_form.xml  \
	invsetup_layout_edit.xml           \
	invtotal_form.xml			\
	item_layout_edit.xml               \
	item_advanced_layout_edit.xml               \
	itemtotals_layout_edit.xml         \
	posetup_layout_edit.xml            \
	predoc_layout_edit.xml        \
	recurdocument_layout_edit.xml        \
	productline_form.xml			\
	invrecurdocument_layout_edit.xml	\
	stkitdocument_layout_edit.xml        \
	inventorycycle_form.xml			\
	lotcategory_form.xml			\
	aisle_form.xml				\
	icrow_form.xml				\
	bin_form.xml				\
	itemglgroup_form.xml			\
	invpricelist_form.xml			\
	serialmask_form.xml			\
	priceschedule_form.xml			\
	warehouse_form.xml			\
	cost_form.xml			\
	uom_form.xml			\
	zone_form.xml				\
	binsize_form.xml				\
	binface_form.xml				\
	iccyclecount_form.xml			\
	iccyclecount_reconcile_form.xml	\
	itemcrossref_form.xml \
	inventoryworkqueue_form.xml \
	inventorywqorder_form.xml \
	invsupplies_form.xml \
	$(EMPTY)

QUERY_OBJECTS=$(ENTITY_ENTS:.ent=.qry)
LAYOUT_PHPS=$(LAYOUT_XMLS:.xml=.php)
LAYOUT_XSLS=$(LAYOUT_XMLS:.xml=.xsl)

include ../Makefile.in
