<?php
/**
 * File replenishforecast.ent contains forecast information which hangs off
 * item and itemwarehouseinfo records.  When you select 'forecast table' as the
 * Replenishment Method for an item or for an item/warehouse, then there can be
 * zero or more records in this table for that item or item/warehouse, telling us
 * when customers expect a demand on those items.
 *
 * For exanmple, if you have a Ganesh Holiday Sale coming up, you might set the
 * need for an item to be 30 starting on Sept 2.
 *
 * <AUTHOR> @copyright 2000-2018 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 *
 * The SQL looks like this:
 *    CREATE TABLE REPLENISHFORECAST
 *               CNY#         NUMBER(15)
 *               RECORD#      NUMBER(8)
 *               ITEMID       VARCHAR2(50 CHAR)
 *               WAREHOUSEID  VARCHAR2(40 CHAR)   -- this CAN be null for 'no specific warehouse'
 *               EFFECTIVEDATE DATE
 *               QUANTITY     NUMBER
 *
 *               WHENCREATED  DATE DEFAULT CURRENT_TIMESTAMP AT TIME ZONE 'GMT',
 *               WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP AT TIME ZONE 'GMT',
 *               CREATEDBY    NUMBER(8, 0) DEFAULT SYS_CONTEXT('TMCtx', 'USERKEY'),
 *               MODIFIEDBY   NUMBER(8, 0) DEFAULT SYS_CONTEXT('TMCtx', 'USERKEY'),
 *
 */

global $gWhenCreatedFieldInfo, $gWhenModifiedFieldInfo, $gCreatedByFieldInfo, $gModifiedByFieldInfo, $gRecordNoFieldInfo;


$kSchemas['replenishforecastdetail'] = [

    'object' => [
        'RECORDNO',
        'ITEMKEY',
        'ITEMID',
        'WAREHOUSEID',
        'EFFECTIVEDATE',
        'QUANTITY',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
        'ITEMNAME',
        'WAREHOUSEKEY',
        'WAREHOUSENAME',
        'FORECASTKEY',
        'FORECASTNAME'
    ],

    'publish' => [
        'RECORDNO',
        'ITEMID',
        'WAREHOUSEID',
        'EFFECTIVEDATE',
        'QUANTITY',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ],

    'schema' => [
        'RECORDNO'      => 'record#',
        'ITEMKEY'       => 'icitem.record#',
        'ITEMID'        => 'itemid',
        'WAREHOUSEID'   => 'warehouseid',
        'EFFECTIVEDATE' => 'effectivedate',
        'QUANTITY'      => 'quantity',
        'WHENCREATED'   => 'whencreated',
        'WHENMODIFIED'  => 'whenmodified',
        'CREATEDBY'     => 'createdby',
        'MODIFIEDBY'    => 'modifiedby',
        'ITEMNAME'      => 'icitem.name',
        'WAREHOUSEKEY'  => 'warehouse.record#',
        'WAREHOUSENAME' => 'warehouse.name',
        'FORECASTKEY'   => 'parent.record#',
        'FORECASTNAME' => 'parent.name',
    ],

    'children' => [
        'icitem'    => [
            'fkey'    => 'itemid',
            'invfkey' => 'itemid',
            'table'   => 'icitem',
        ],
        'warehouse' => [
            'fkey'    => 'warehouseid',
            'invfkey' => 'location_no',
            'table'   => 'icwarehouse',
            'join'    => 'outer',
        ],
        'parent' => [
            'fkey'    => 'ITEMID',
            'invfkey' => 'itemid',
            'table'   => 'replenishforecasthdr',
            'join'    => 'inner'
        ]
    ],

    'fieldinfo' => [
        $gRecordNoFieldInfo,
        [
            'path'     => 'ITEMID',
            'fullname' => 'IA.ITEM',
            'type'     => [
                'ptype'      => 'ptr',
                'type'       => 'ptr',
                'entity'     => 'itemngrouppick',
                'pickentity' => 'itemngrouppick',
                'restrict'   => [
                    [
                        'pickField' => 'ITEMTYPE',
                        'value'     => ['I', 'SK'],
                    ],
                ],
            ],
            'required' => true,
        ],
        [
            'fullname' => 'IA.WAREHOUSE',
            'type'     => [
                'ptype'      => 'ptr',
                'type'       => 'ptr',
                'entity'     => "replenishforecastwarepick2",
                'pickentity' => "replenishforecastwarepick2",
            ],
            'path'     => 'WAREHOUSEID',
        ],
        [
            'path'     => 'EFFECTIVEDATE',
            'fullname' => 'IA.EFFECTIVE_DATE',
            'readonly' => false,
            'required' => true,
            'type'     => [
                'type'      => 'date',
                'ptype'     => 'date',
                'format'    => $gDateFormat,
                'maxlength' => 12,
                'size'      => 12,
            ],
        ],
        [
            'fullname' => 'IA.FORECAST_QUANTITY',
            'path'     => 'QUANTITY',
            'type'     => [
                'ptype'     => 'decimal',
                'type'      => 'decimal',
                'maxlength' => 12,
                'size'      => 12,
                'format'    => $gDecimalFormat,
            ],
            'precision' => 4,
            'required' => true,
        ],

        [
            'fullname' => 'IA.SELECT',
            'type'     => $gBooleanType,
            'default'  => 'false',
            'path'     => 'INCLUDED',
            'id'       => 202,
        ],


        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
    ],

    'sqldomarkup'     => true,
    'sqlmarkupfields' => [
        'WHENCREATED',
        'WHENMODIFIED',
    ],
    'auditcolumns'    => true,
    'table'           => 'replenishforecast',
    'module'          => 'inv',
    'autoincrement'   => 'RECORDNO',
    'vid'             => 'RECORDNO',
    'printas'         => 'IA.REPLENISHMENT_FORECAST_DETAIL_TABLE',
    'pluralprintas'   => 'IA.REPLENISHMENT_FORECAST_DETAIL_TABLES',
    'postprocess'     => 'entitymanipulaterawdata',         // for custom reports; call postProcessCustomReportResults in this class when all the data has been read....
    'parententity' => 'replenishforecast',
    'url'             => [
        'no_short_url' => true,    // Don't allow short url in custom reports and elsewhere
    ],
    'audittrail_disabled' => true,
    'api'             => [
        'PERMISSION_MODULES' => ['inv', 'so', 'po'],   // this is what item uses, so....
        'GET_BY_GET'         => true,    // use my 'get' method
        'PERMISSION_READ'    => 'lists/replenishforecast', //read and readby query
        'PERMISSION_CREATE'  => 'lists/replenishforecast/create',//mark and add
        'PERMISSION_DELETE'  => 'lists/replenishforecast/delete',//unmark and delete
        'PERMISSION_UPDATE'  => 'lists/replenishforecast/create', //cedit creates new record, so create is ok to use
    ],
];
