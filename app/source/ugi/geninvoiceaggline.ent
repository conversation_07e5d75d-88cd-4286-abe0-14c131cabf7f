<?php
/**
 * File geninvoiceaggline.ent contains entity definition for geninvoiceaggline
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

$kSchemas['geninvoiceaggline'] = [

    'object' => [
        'RECORDNO',
        'GENINVOICELINEKEY',
        'TSENTRYKEY',
        'BILLINGSCHEDULEENTRYKEY',
        'QUANTITY',
        'PRICE',
        'TRX_AMOUNT',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
        'EMPLOYEEDIMKEY',
        'LINENO',
        'ENTRYDATE',
        'EMPLOYEE_NAME',
        'EMPLOYEE_LOCATIONKEY',
        'TSENTRY_TASK',
        'DESCRIPTION',
        'CONTRACTUSAGEBILLINGID',
        'CONTRACTDETAILKEY',
        'TSENTRY_LOCATIONKEY',
        'TSENTRY_LOCATIONID',
        'TSENTRY_LOCATIONNAME',
        'TSENTRY_DEPARTMENTKEY',
        'TSENTRY_DEPARTMENTID',
        'TSENTRY_DEPARTMENTNAME',
        'TSENTRY_CLASSID'
    ],

    'schema' => [
        'RECORDNO'          => 'record#',
        'GENINVOICELINEKEY' => 'geninvoicelinekey',
        'TSENTRYKEY'        => 'tsentrykey',
        'BILLINGSCHEDULEENTRYKEY'=> 'billingscheduleentrykey',
        'QUANTITY'         => 'quantity',
        'PRICE'            => 'price',
        'TRX_AMOUNT'       => 'trx_amount',
        'WHENMODIFIED'     => 'whenmodified',
        'WHENCREATED'      => 'whencreated',
        'CREATEDBY'        => 'createdby',
        'MODIFIEDBY'       => 'modifiedby',
        'EMPLOYEEDIMKEY'   =>  'timesheetentry.employeedimkey',
        'LINENO'   =>  'timesheetentry.lineno',
        'ENTRYDATE' => 'timesheetentry.entrydate',
        'EMPLOYEE_NAME' => 'ts_employee_contact.name',
        'EMPLOYEE_LOCATIONKEY'   =>  'employee.locationkey' ,
        'TSENTRY_TASK'   =>   'task.name',
        'DESCRIPTION' => 'description',
        'CONTRACTUSAGEBILLINGID' => 'contractusagebillingid',
        'CONTRACTDETAILKEY' => 'contractdetailkey',
        'TSENTRY_LOCATIONKEY' => 'timesheetentry.locationkey',
        'TSENTRY_LOCATIONID' => 'location.location_no',
        'TSENTRY_LOCATIONNAME' => 'location.name',
        'TSENTRY_DEPARTMENTKEY' => 'timesheetentry.deptkey',
        'TSENTRY_DEPARTMENTID' => 'department.dept_no',
        'TSENTRY_DEPARTMENTNAME' => 'department.title',
        'TSENTRY_CLASSID'           =>  'class.classid'
    ],
    'children' => array(
        'timesheetentry' => [
            'fkey' => 'tsentrykey', 'invfkey' => 'record#', 'table' => 'timesheetentry', 'join' => 'outer',
            'children' => array(
                'location' => array(
                    'fkey' => 'locationkey', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'location'
                ),
                'department' => array(
                    'fkey' => 'deptkey', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'department'
                ),
                'employee' => array('fkey' => 'employeedimkey', 'invfkey' => 'record#', 'table' => 'employee', 'join' => 'outer',
                    'children' => array(
                        'ts_employee_contact' => array(
                            'fkey' => 'contactkey', 'table' => 'contact', 'invfkey' => 'record#', 'join' => 'outer'
                        ),
                    ),
                ),
                'class' => array(
                    'fkey' => 'classdimkey', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'class'
                ),
                'task' => array('fkey' => 'taskkey', 'invfkey' => 'record#', 'table' => 'task', 'join' => 'outer',),
            ),

        ],
    ),
    'nexus' => array(
        'timesheetentry' => [
            'object' => 'timesheetentry', 'relation' => MANY2ONE, 'field' => 'TSENTRYKEY',
        ],
    ),
    'fieldinfo' => [
        $gRecordNoFieldInfo,
        [
            'path'      => 'TRX_AMOUNT',
            'fullname'  => 'IA.TRANSACTION_AMOUNT',
            'desc'      => 'IA.TRANSACTION_AMOUNT',
            'type'      => [
                'ptype'     => 'currency',
                'type'      => 'decimal',
                'maxlength' => 15,
                'size' => 8,
            ],
            'readonly'  => true,
        ],
        [
            'path'      => 'PRICE',
            'fullname'  => 'IA.PRICE',
            'type'      => [
                'ptype'     => 'currency',
                'type'      => 'decimal',
                'maxlength' => 15,
                'size' => 8,
            ],
            'readonly'  => false,
        ],
        [
            'path'      => 'AMOUNT',
            'fullname'  => 'IA.BASE_AMOUNT',
            'desc'      => 'IA.BASE_AMOUNT',
            'type'      => [
                'ptype'     => 'currency',
                'type'      => 'decimal',
                'size'      => 20,
            ],
            'readonly'  => true,
        ],
        [
            'path'      => 'QUANTITY',
            'fullname'  => 'IA.QUANTITY',
            'type'      => [
                'ptype'     => 'decimal',
                'type'      => 'decimal',
                'size'      => 20,
                'maxlength' => 40,
                'format' => $gQuantityFormatFourDec,
            ],
            'precision' => 4,
            'readonly'  => true,
        ],
        [
            'path'      => 'LINENO',
            'fullname'  => 'IA.LINE_NO',
            'type'      => [
                'ptype'     => 'text',
                'type'      => 'text',
            ],
            'readonly'  => true,
        ],
        [
            'path'      => 'TASK',
            'fullname'  => 'IA.TASK',
            'type'      => [
                'ptype'     => 'text',
                'type'      => 'text',
            ],
            'readonly'  => true,
        ],
        [
            'path'      => 'ENTRYDATE',
            'fullname'  => 'IA.DATE',
            'type'      => $gDateType,
            'readonly'  => true,
        ],
        [
            'path'      => 'EMPLOYEE_NAME',
            'fullname'  => 'IA.EMPLOYEE',
            'type'      => [
                'ptype'     => 'text',
                'type'      => 'text',
            ],
            'readonly'  => true,
        ],

        [
            'path'      => 'TSENTRY_LOCATIONID',
            'fullname'  => 'IA.LOCATION',
            'type'      => [
                'ptype'     => 'text',
                'type'      => 'text',
            ],
            'readonly'  => true,
        ],
        [
            'path'      => 'TSENTRY_TASK',
            'fullname'  => 'IA.TASK',
            'type'      => [
                'ptype'     => 'text',
                'type'      => 'text',
            ],
            'readonly'  => true,
        ],
        [
            'path'      => 'TSENTRY_DEPARTMENTID',
            'fullname'  => 'IA.DEPARTMENT',
            'type'      => [
                'ptype'     => 'text',
                'type'      => 'text',
            ],
            'readonly'  => true,
        ],
        [
            'path'      => _('DESCRIPTION'),
            'fullname'  => 'IA.DESCRIPTION',
            'type'      => [
                'ptype'     => 'text',
                'type'      => 'text',
                'size'      => 40,
                'maxlength' => 2000,
            ],
            'readonly'  => false,
        ],
        [
            'path'      => 'TSENTRY_CLASSID',
            'fullname'  => 'IA.CLASS',
            'type'      => [
                'ptype'     => 'text',
                'type'      => 'text',
            ],
            'readonly'  => true,
        ],
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
    ],
    'parententity'     => 'geninvoiceaggheader',
    'module'        => 'cn',
    'table'         => 'geninvoicelineandtsentry',
    'auditcolumns'  => true,
    'autoincrement' => 'RECORDNO',
    'vid'           => 'RECORDNO',
    'nosysview'     => true,

    'printas'       => 'IA.AGGREGATE_TIMESHEET_ENTRIES_PREVIEW_HEADER',
    'pluralprintas' => 'IA.AGGREGATE_TIMESHEET_ENTRIES_PREVIEW_HEADERS',
    'audittrail_disabled' => true,
];
