<?php
/**
 * File ContractBillableLine.cls contains the class ContractBillableLine
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * Contract billable line DTO
 *
 * Class ContractBillableLine
 */
class ContractBillableLine extends ContractCommonBillableLine
{
    protected const localTokens = [
        'IA.PRICE_CALC_MEMO'
    ];

    /** @var array $textMap */
    private $textMap = [];

    /* @var  int   $billingSchEntryKey */
    private $billingSchEntryKey;
    /* @var string $priceCalcMemo */
    private $priceCalcMemo;

    /**
     * @param int   $itemKey            item record number
     * @param int   $customerKey        customer record number
     * @param float $trxAmount          transaction amount
     * @param int   $quantity           quantity being billed
     * @param int   $billingSchEntryKey billing schedule entry key
     */
    public function __construct($itemKey, $customerKey, $trxAmount, $quantity, $billingSchEntryKey)
    {
        $this->textMap = getLocalizedTextWithThrow(I18N::tokenArrayToObjectArray(self::localTokens));
        parent::__construct($itemKey, $customerKey, $trxAmount, $quantity);

        $this->billingSchEntryKey = $billingSchEntryKey;
    }

    /**
     * Returns billing schedule entry key
     *
     * @return int
     */
    public function getBillingSchEntryKey()
    {
        return $this->billingSchEntryKey;
    }

    /**
     * Contract billing schedule entry specific gen invoice line entity values
     *
     * @param array $lineEntity data required to create a geninvoiceline
     */
    public function innerToGenInvoiceLineEntity(&$lineEntity)
    {
        parent::innerToGenInvoiceLineEntity($lineEntity);
        $lineEntity['TYPE']                  = GenInvoiceUtil::SOURCE_PREFIX_CONTRACT_SCHEDULE;
        $lineEntity['CNBILLINGSCHENTRYKEY']  = $this->billingSchEntryKey;
        $lineEntity['PRICECALCMEMO'] = $this->priceCalcMemo;
    }


    /**
     * Sets price calc memo
     *
     * @param array $entry
     */
    public function setPriceCalcMemo($entry)
    {
        $qty = $entry['QUANTITY'] != '' ? $entry['QUANTITY'] : 1;
        $price = $entry['PRICE'] != '' ? $entry['PRICE'] : 1;
        $multi = $entry['MULTIPLIER'] != '' ? $entry['MULTIPLIER']
            : 1;
        $disc = $entry['DISCOUNTPERCENT'] != ''
            ? $entry['DISCOUNTPERCENT'] : 0;
        $priceCalcMemo = $entry['PRICECALCMEMO'];

        $percentBilled = ibcdiv(
            ibcmul($entry['AMOUNT'], '100'), $entry['FLATAMOUNT']
            , ContractUtil::INTERMEDIATE_PRECISION, true
        );

        if ($priceCalcMemo != '') {
            $priceCalcMemo .= '; ';
        }
        $priceCalcMemo .= ContractUtil::GTP($this->textMap, 'IA.PRICE_CALC_MEMO', [
            'PERCENT_BILLED' => $percentBilled,
            'FLATAMOUNT' => $entry['FLATAMOUNT'],
            'QTY' => $qty,
            'PRICE' => $price,
            'MULTI' => $multi,
            'DISC' => $disc
        ]);

        $this->priceCalcMemo = $priceCalcMemo;
    }

    /**
     * Gets the entries to be locked for the billable line
     *
     * @param int $contractRunEntryKey
     * @param int $genInvoiceHeaderKey
     * @param int $locationKey
     *
     * @return array
     */
    public function getLockingEntries($contractRunEntryKey, $genInvoiceHeaderKey, $locationKey)
    {
        $lockingEntry = $this->createLockingEntry($contractRunEntryKey, $genInvoiceHeaderKey, $locationKey);
        $lockingEntry['CNBILLINGSCHENTRYKEY']  = $this->billingSchEntryKey;
        $lockingEntries[] = $lockingEntry;
        return $lockingEntries;
    }
}
