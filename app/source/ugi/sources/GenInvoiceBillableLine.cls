<?php
/**
 * File GenInvoiceBillableLine.cls contains the class GenInvoiceBillableLine
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * Parent of all billable line DTOs
 *
 * Class GenInvoiceBillableLine
 */
abstract class GenInvoiceBillableLine implements JsonSerializable
{
    /* @var  int   $itemKey */
    private $itemKey;

    /* @var  int   $customerKey */
    private $customerKey;

    /* @var  float $trxAmount */
    private $trxAmount;

    /* @var  float $amount */
    private $amount;

    /* @var  int   $quantity */
    private $quantity;

    /* @var  string $currency */
    private $currency;

    /* @var  float $exchangeRate */
    private $exchangeRate;

    /* @var  int   $contractKey */
    private $contractKey;

    /* @var  int   $projectKey */
    private $projectKey;

    /* @var  int   $locationKey */
    private $locationKey;

    /* @var  int   $deptKey */
    private $deptKey;

    /* @var  int   $vendorKey */
    private $vendorKey;

    /* @var  int   $employeeKey */
    private $employeeKey;

    /* @var  int   $assetKey */
    private $assetKey;

    /* @var  int   $classKey */
    private $classKey;

    /* @var  int   $taskKey */
    private $taskKey;

    /* @var  int   $warehouseKey */
    private $warehouseKey;

    /* @var  array   $customFields */
    private $customFields;

    /* @var  array   $relationships */
    private $relationships;

    /* @var  int $shipToKey */
    private $shipToKey;

    /* @var  int $billToKey */
    private $billToKey;

    /* @var  int   $committedQuantity */
    private $committedQuantity;
    /**
     * @return int
     */
    public function getShipToKey()
    {
        return $this->shipToKey;
    }

    /**
     * @param int $shipToKey
     */
    public function setShipToKey($shipToKey)
    {
        $this->shipToKey = $shipToKey;
    }

    /**
     * @param int   $itemKey     item record#
     * @param int   $customerKey customer record#
     * @param float $trxAmount   transaction amount
     * @param int   $quantity    transaction quantity
     */
    public function __construct($itemKey, $customerKey, $trxAmount, $quantity)
    {
        $this->itemKey      = $itemKey;
        $this->customerKey  = $customerKey;
        $this->trxAmount    = $trxAmount;
        $this->quantity     = $quantity;
    }

    /**
     * @return array
     */
    public function jsonSerialize() {
        // return [$this->property1, $this->array1];
        $retVal = get_object_vars($this);
        $retVal['__class_name__'] = get_class($this);
        return $retVal;
    }

    /**
     * @param int $itemKey
     */
    public function setItemKey($itemKey)
    {
        $this->itemKey = $itemKey;
    }

    /**
     * @param int $customerKey
     */
    public function setCustomerKey($customerKey)
    {
        $this->customerKey = $customerKey;
    }

    /**
     * @param float $trxAmount
     */
    public function setTrxAmount($trxAmount)
    {
        $this->trxAmount = $trxAmount;
    }

    /**
     * @param int $quantity
     */
    public function setQuantity($quantity)
    {
        $this->quantity = $quantity;
    }

    /**
     * @param array $customFields
     */
    public function setCustomFields($customFields)
    {
        $this->customFields = $customFields;
    }

    /**
     * @param array $relationships
     */
    public function setRelationships($relationships)
    {
        $this->relationships = $relationships;
    }


    /**
     * Returns item key
     *
     * @return int
     */
    public function getItemKey()
    {
        return $this->itemKey;
    }

    /**
     * Returns CustomerKey
     *
     * @return int
     */
    public function getCustomerKey()
    {
        return $this->customerKey;
    }

    /**
     * Returns Amount
     *
     * @return float
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * Returns TrxAmount
     *
     * @return float
     */
    public function getTrxAmount()
    {
        return $this->trxAmount;
    }

    /**
     * Sets Amount
     *
     * @param float $amount amount
     */
    public function setAmount($amount)
    {
        $this->amount = $amount;
    }

    /**
     * Returns ExchangeRate
     *
     * @return float
     */
    public function getExchangeRate()
    {
        return $this->exchangeRate;
    }

    /**
     * Sets ExchangeRate
     *
     * @param float $exchangeRate exchange rate
     */
    public function setExchangeRate($exchangeRate)
    {
        $this->exchangeRate = $exchangeRate;
    }

    /**
     * Returns Currency
     *
     * @return string
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * Sets Currency
     *
     * @param string $currency currency
     */
    public function setCurrency($currency)
    {
        $this->currency = $currency;
    }

    /**
     * Sets ContractKey
     *
     * @param int $contractKey contract record#
     */
    public function setContractKey($contractKey)
    {
        $this->contractKey = $contractKey;
    }

    /**
     * Returns ContractKey
     *
     * @return int
     */
    public function getContractKey()
    {
        return $this->contractKey;
    }

    /**
     * Returns ProjectKey
     *
     * @return int
     */
    public function getProjectKey()
    {
        return $this->projectKey;
    }

    /**
     * Sets ProjectKey
     *
     * @param int $projectKey project record#
     */
    public function setProjectKey($projectKey)
    {
        $this->projectKey = $projectKey;
    }

    /**
     * Returns Quantity
     *
     * @return int
     */
    public function getQuantity()
    {
        return $this->quantity;
    }

    /**
     * Returns LocationKey
     *
     * @return int
     */
    public function getLocationKey()
    {
        return $this->locationKey;
    }

    /**
     * Sets LocationKey
     *
     * @param int $locationKey location record#
     */
    public function setLocationKey($locationKey)
    {
        $this->locationKey = $locationKey;
    }

    /**
     * Returns DeptKey
     *
     * @return int
     */
    public function getDeptKey()
    {
        return $this->deptKey;
    }

    /**
     * Sets DeptKey
     *
     * @param int $deptKey department record#
     */
    public function setDeptKey($deptKey)
    {
        $this->deptKey = $deptKey;
    }

    /**
     * @return int
     */
    public function getVendorKey()
    {
        return $this->vendorKey;
    }

    /**
     * @param int $vendorKey
     */
    public function setVendorKey($vendorKey)
    {
        $this->vendorKey = $vendorKey;
    }

    /**
     * @return int
     */
    public function getEmployeeKey()
    {
        return $this->employeeKey;
    }

    /**
     * @param int $employeeKey
     */
    public function setEmployeeKey($employeeKey)
    {
        $this->employeeKey = $employeeKey;
    }

    /**
     * @return int
     */
    public function getAssetKey()
    {
        return $this->assetKey;
    }

    /**
     * @param int $assetKey
     */
    public function setAssetKey($assetKey)
    {
        $this->assetKey = $assetKey;
    }

    /**
     * @return int
     */
    public function getClassKey()
    {
        return $this->classKey;
    }

    /**
     * @param int $classKey
     */
    public function setClassKey($classKey)
    {
        $this->classKey = $classKey;
    }

    /**
     * @return int
     */
    public function getTaskKey()
    {
        return $this->taskKey;
    }

    /**
     * @param int $taskKey
     */
    public function setTaskKey($taskKey)
    {
        $this->taskKey = $taskKey;
    }

    /**
     * @return int
     */
    public function getWarehouseKey()
    {
        return $this->warehouseKey;
    }

    /**
     * @param int $warehouseKey
     */
    public function setWarehouseKey($warehouseKey)
    {
        $this->warehouseKey = $warehouseKey;
    }

    /**
     * @return int
     */
    public function getBillToKey()
    {
        return $this->billToKey;
    }

    /**
     * @param int $billToKey
     */
    public function setBillToKey($billToKey)
    {
        $this->billToKey = $billToKey;
    }

    /**
     * Sets CustomFieldValues
     *
     * @param array $customFieldsMap custom field name/value pairs
     */
    public function setCustomFieldValues($customFieldsMap)
    {
        $this->customFields = $customFieldsMap;
    }

    /**
     * Sets CustomFieldValue
     *
     * @param string $fieldId field name
     * @param mixed  $value   field value
     */
    public function setCustomFieldValue($fieldId, $value)
    {
        $this->customFields[$fieldId] = $value;
    }

    /**
     * Returns CustomFieldValues
     *
     * @return mixed
     */
    public function getCustomFieldValues()
    {
        return $this->customFields;
    }

    /**
     * Sets RelationshipValues
     *
     * @param array $relationshipsMap relationship name/value pairs
     */
    public function setRelationshipValues($relationshipsMap)
    {
        $this->relationships = $relationshipsMap;
    }

    /**
     * Sets RelationshipValue
     *
     * @param string $lookupId relationship id
     * @param mixed  $value    relationship value
     */
    public function setRelationshipValue($lookupId, $value)
    {
        $this->relationships[$lookupId] = $value;
    }

    /**
     * Returns RelationshipValues
     *
     * @return mixed
     */
    public function getRelationshipValues()
    {
        return $this->relationships;
    }

    /**
     * Converts to gen invoice line entity values
     *
     * @param int    $headerKey Header record#
     * @param string $docType   Document type
     * @param array  $header    Header attributes
     *
     * @return array
     */
    public function toGenInvoiceLineEntity($headerKey, $docType, $header)
    {

        $lineEntity = [
            'ITEMKEY'      => $this->getItemKey(),
            'CUSTOMERKEY'  => $this->getCustomerKey(),
            'HEADERKEY'    => $headerKey,
            'TRX_AMOUNT'   => $this->trxAmount,
            'QUANTITY'     => $this->quantity

        ];

        $lineEntity['SHIPTOKEY'] = $this->getShipToKey();

        $lineEntity['LOCATIONKEY'] = $this->getLocationKey();
        $lineEntity['DEPTKEY'] = $this->getDeptKey();

        $lineEntity['CONTRACTKEY'] = $this->getContractKey();
        $lineEntity['PROJECTKEY'] = $this->getProjectKey();

        if (ContractUtil::isMCPEnabled()) {
            $exchRate = $header['EXCHRATE'];
            $this->amount = ibcmul($this->trxAmount, $exchRate, ContractUtil::AMOUNT_PRECISION, true);
            $lineEntity['AMOUNT'] = $this->amount;
            $lineEntity['EXCHRATE'] = $exchRate;
        }

        // Copy rest of the dimensions
        $this->copyDimensions($lineEntity);

        // Custom fields
        // TODO: Check type
        $cfMap = $this->getCustomFieldValues();
        if ($cfMap) {
            $cfIds = GenInvoiceUtil::getCustomFieldIds('sodocumententry', $docType);
            foreach ($cfIds as $id) {
                if (isset($cfMap[$id]) && $cfMap[$id] !== '') {
                    $lineEntity[$id] = $cfMap[$id];
                }
            }
        }

        // UDDs
        $uddMap = $this->getRelationshipValues();
        if ($uddMap) {
            $lineEntity = $lineEntity + $uddMap;
        }

        $this->innerToGenInvoiceLineEntity($lineEntity);

        return $lineEntity;
    }

    /**
     * Copy over dimensions from billable line to gen invoice line
     *
     * @param array $lineEntity Gen invoice line attributes
     */
    protected function copyDimensions(&$lineEntity)
    {
        $lineEntity['VENDORDIMKEY'] = $this->getVendorKey();
        $lineEntity['EMPLOYEEDIMKEY'] = $this->getEmployeeKey();
        $lineEntity['ASSETDIMKEY'] = $this->getAssetKey();
        $lineEntity['CLASSDIMKEY'] = $this->getClassKey();
        $lineEntity['TASKDIMKEY'] = $this->getTaskKey();
        $lineEntity['WAREHOUSEDIMKEY'] = $this->getWarehouseKey();
    }

    /**
     * Set standard dimension data members from dimensions array
     *
     * @param array $dims dimensions array
     */
    public function setStdDimensions($dims)
    {
        $this->setVendorKey($dims['VENDORDIMKEY']);
        $this->setEmployeeKey($dims['EMPLOYEEDIMKEY']);
        $this->setAssetKey($dims['ASSETDIMKEY']);
        $this->setClassKey($dims['CLASSDIMKEY']);
        $this->setTaskKey($dims['TASKDIMKEY']);
        $this->setWarehouseKey($dims['WAREHOUSEDIMKEY']);
    }

    /**
     * @param int $quantity
     */
    public function setCommittedUsageQuantity($quantity)
    {
        $this->committedQuantity = $quantity;
    }
    /**
     * Source specific pre-bill line entity values
     *
     * @param array $prebillLineEntity geninvoiceline field name/value pairs
     *
     */
    abstract public function innerToGenInvoiceLineEntity(&$prebillLineEntity);

    /**
     * Gets the locking entries for the billable line
     *
     * @param int $contractRunEntryKey
     * @param int $genInvoiceHeaderKey
     * @param int $locationKey
     *
     * @return array
     */
    abstract public function getLockingEntries($contractRunEntryKey, $genInvoiceHeaderKey, $locationKey);

    /**
     * Creates the locking entry for the billable line with the base locking data (i.e. CONTRACTRUNENTRYKEY,
     * GENINVOICEHEADERKEY, and LOCATIONKEY).
     *
     * @param int $contractRunEntryKey
     * @param int $genInvoiceHeaderKey
     * @param int $locationKey
     *
     * @return array the locking entry
     */
    function createLockingEntry($contractRunEntryKey, $genInvoiceHeaderKey, $locationKey)
    {
        $lockingEntry['CONTRACTRUNENTRYKEY'] = $contractRunEntryKey;
        $lockingEntry['GENINVOICEHEADERKEY'] = $genInvoiceHeaderKey;
        // set location key if applicable; For root, location key is null so no need to set
        if (isset($locationKey)) {
            $lockingEntry['LOCATIONKEY'] = $locationKey;
        }
        return $lockingEntry;
    }
}
