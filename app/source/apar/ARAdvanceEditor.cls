<?php
/**
 * @file      ARAdvanceEditor.cls
 * <AUTHOR> <raj<PERSON>.<EMAIL>>
 * @copyright 2021 Intacct Corporation All, Rights Reserved
 */

class ARAdvanceEditor extends SubLedgerAdvanceEditor
{

    /**
     * @return ARAdvanceManager|EntityManager|SubLedgerAdvanceManager
     */
    public function getEntityMgr()
    {
        assert($this->entityMgr instanceof ARAdvanceManager);
        return $this->entityMgr;
    }

    /**
     * Figure out if we have multi-visibility subscribed
     *
     * @return bool true if we have multi-visibility subscribed else false
     */
    protected function hasMultiVisibilitySubscribed()
    {
        return IsMultiVisibilitySubscribed('customer');
    }

    /**
     * @param array $obj
     *
     * @return bool
     */
    protected function prepareObjectBeforeProcess(&$obj)
    {
        // unset the extra fields
        if ($this->isAutoBatchEnabled) {
            if ($obj['ACCOUNTTYPE'] == 'Bank') {
                unset($obj['UNDEPOSITEDACCOUNTNO']);
            } else if ($obj['ACCOUNTTYPE'] == 'Undeposited Funds Account') {
                unset($obj['FINANCIALENTITY']);
            }
        } else {
            unset($obj['UNDEPOSITEDACCOUNTNO']);
            unset($obj['FINANCIALENTITY']);
            // we dont need prbatch if in case of UI, it will have the value in PRBATCH
            unset($obj['PRBATCHKEY']);
        }
        return true;
    }

    /**
     * Set View page title according to action create / view
     *
     * @param array $params
     */
    protected function setPageTitleByAction(&$params)
    {
        if ($this->verb == $this->kCreateAction) {
            $params['view']['title'] = 'IA.RECEIVE_ADVANCE';
        }
    }

    /**
     * @return array
     */
    protected function getMCPDynamicMetadataFields()
    {
        $mcFields = [
            'AMOUNT',
            'CURRENCY',
            'EXCH_RATE_DATE',
            'EXCH_RATE_TYPE_ID',
            'EXCHANGE_RATE'
        ];
        return $mcFields;
    }

    /**
     * @param array $obj
     */
    protected function mediateDefaultModulePrefrerences(&$obj)
    {
        $this->setAccountType($obj);
        parent::mediateDefaultModulePrefrerences($obj);
        $modulePreferences = $this->getModulePreferences();

        if ($this->state != $this->kShowViewState) {
            if ($this->isAutoBatchEnabled) {
                if (empty($obj['ACCOUNTTYPE']) && !empty($modulePreferences['DEFAULT_ACCOUNTTYPE'])) {
                    $obj['ACCOUNTTYPE'] = $modulePreferences['DEFAULT_ACCOUNTTYPE'];
                }
                if (empty($obj['FINANCIALENTITY']) && !empty($modulePreferences['DEFAULT_FINACCTID'])
                    && $obj['ACCOUNTTYPE'] == 'Bank') {
                    $obj['FINANCIALENTITY'] = $modulePreferences['DEFAULT_FINACCTID'];
                } else if (empty($obj['UNDEPOSITEDACCOUNTNO']) && !empty($modulePreferences['DEFAULT_UNDEPGLACCOUNT'])
                    && $obj['ACCOUNTTYPE'] == 'Undeposited Funds Account') {
                    $obj['UNDEPOSITEDACCOUNTNO'] = $modulePreferences['DEFAULT_UNDEPGLACCOUNT'];
                }
            }
        }
    }

    /**
     * Use for setting default bank account values
     *
     * @param array $obj
     */
    protected function mediateDefaultBankAccounts(&$obj)
    {
        if ($this->isAutoBatchEnabled || !empty($obj['FINANCIALENTITY'])) {
            parent::mediateDefaultBankAccounts($obj);
        }

        if (!empty($obj['UNDEPOSITEDACCOUNTNO'])) {
            $undepoTitle = $obj['OFFSETS'][0]['ACCOUNTTITLE'];
            if (!empty($undepoTitle)) {
                $obj['UNDEPOSITEDACCOUNTNO'] = $obj['UNDEPOSITEDACCOUNTNO'] . PICK_RECVAL_SEP . $undepoTitle;
            }
        }
    }

    /**
     * Method for setting the account type according to the value
     *
     * @param array $obj
     */
    protected function setAccountType(&$obj)
    {
        if (!empty($obj['FINANCIALENTITY'])) {
            $obj['ACCOUNTTYPE'] = 'Bank';
        } else if (!empty($obj['UNDEPOSITEDACCOUNTNO'])) {
            $obj['ACCOUNTTYPE'] = 'Undeposited Funds Account';
        }
    }

    /**
     * @param array $obj
     */
    protected function mediateFinancialCurrency(&$obj)
    {
        // check if auto batch is enabled, so account type will be bank or undeposit
        if ($this->isAutoBatchEnabled) {
            if ($obj['ACCOUNTTYPE'] == 'Bank') {
                parent::mediateFinancialCurrency($obj);
            } else if ($obj['ACCOUNTTYPE'] == 'Undeposited Funds Account' && empty($obj['FINANCIALCURRENCY'])) {
                $obj['FINANCIALCURRENCY'] =  !empty($obj['BASECURR']) ? $obj['BASECURR'] : GetBaseCurrency();
            }
        } else if (!empty($obj['PRBATCH']) && empty($obj['FINANCIALCURRENCY'])) {
            // batch summary is enabled
            $financialCurrency = $this->getBatchFinancialCurrency($obj['PRBATCH']);
            $obj['ACCOUNTTYPE'] = 'Bank';
            if (empty($financialCurrency)) {
                $financialCurrency = !empty($obj['BASECURR']) ? $obj['BASECURR'] : GetBaseCurrency();
                $obj['ACCOUNTTYPE'] = 'Undeposited Funds Account';
            }
            $obj['FINANCIALCURRENCY'] = $financialCurrency;
        }
    }

    /**
     * @param array $obj
     */
    protected function mediateShowHideFields(&$obj)
    {
        $view = $this->getView();
        // check if the manual batch is enable or its comming from Summary Page
        if (!$this->isAutoBatchEnabled) {
            $view->findAndSetProperty(['path' => 'PRBATCH'], ['hidden' => false, 'required' => true]);
            $setProp = ['required' => false, 'hidden' => true];
            $view->findAndSetProperty(['path' => 'UNDEPOSITEDACCOUNTNO'], $setProp);
            $view->findAndSetProperty(['path' => 'FINANCIALENTITY'], $setProp);
            $view->findAndSetProperty(['path' => 'ACCOUNTTYPE'], $setProp);
        } else if ($obj['ACCOUNTTYPE'] == 'Bank') {
            $view->findAndSetProperty(['path' => 'FINANCIALENTITY'], ['required' => true]);
            $view->findAndSetProperty(['path' => 'UNDEPOSITEDACCOUNTNO'], ['hidden' => true]);
        } else if ($obj['ACCOUNTTYPE'] == 'Undeposited Funds Account') {
            $view->findAndSetProperty(['path' => 'UNDEPOSITEDACCOUNTNO'], ['required' => true]);
            $view->findAndSetProperty(['path' => 'FINANCIALENTITY'], ['hidden' => true, 'required' => false]);
        }

        if (!in_array($this->state, [$this->kInitState, $this->kShowNewState, $this->kShowEditState])) {
            $viewFields = [
                'CLRDATE',
                'CLEARED',
                'STATE',
                'TRX_TOTALENTERED_CURRENCY'
            ];
            $this->setTrxTotalEnteredWithCurrency($obj);
            $fieldFName = 'IA.PAYMENT_AMOUNT';
            if ($this->isMCPEnabled) {
                $fieldFName = 'IA.TXN_PAYMENT_AMOUNT';
            }
            $view->findAndSetProperty(
                ['path' => 'TRX_TOTALENTERED_CURRENCY'],
                ['hidden' => false, 'fullname' => $fieldFName],
                EditorComponentFactory::TYPE_FIELD
            );

            foreach ($viewFields as $fieldPath) {
                $view->findAndSetProperty(
                    ['path' => $fieldPath],
                    ['hidden' => false],
                    EditorComponentFactory::TYPE_FIELD
                );
            }
        }

        parent::mediateShowHideFields($obj);
    }

    /**
     * @return array
     */
    protected function postAndNewResetFields()
    {
        $pFields = parent::postAndNewResetFields();
        $fields = [
            'ACCOUNTTYPE',
            'FINANCIALENTITY',
            'UNDEPOSITEDACCOUNTNO',
            'RECEIPTDATE',
            'CREDITCARDTYPE'
        ];
        $fields = INTACCTarray_merge($pFields, $fields);
        return $fields;
    }

    /**
     * @param array $obj
     */
    protected function handleRequestFromOtherSources(array &$obj): bool
    {
        if (!isNullOrBlank(Request::$r->_cid)) {
            $obj['CUSTOMERID'] = Request::$r->_cid;
        }
        if (!empty(Request::$r->_prbatch)) {
            $obj['PRBATCH'] = Request::$r->_prbatch;
        }
        if (!empty(Request::$r->_atype)) {
            $obj['ACCOUNTTYPE'] = Request::$r->_atype;
        }
        if (!empty(Request::$r->_bank)) {
            $obj['FINANCIALENTITY'] = Request::$r->_bank;
        }
        if (!empty(Request::$r->_undep)) {
            $obj['UNDEPOSITEDACCOUNTNO'] = Request::$r->_undep;
        }
        if (!empty(Request::$r->_pmethod)) {
            $obj['PAYMENTMETHOD'] = Request::$r->_pmethod;
        }
        if (!isNullOrBlank(Request::$r->_desc)) {
            $obj['DESCRIPTION'] = Request::$r->_desc;
        }
        if (!isNullOrBlank(Request::$r->_docno)) {
            $obj['DOCNUMBER'] = Request::$r->_docno;
        }
        return true;
    }

    /**
     * @param string $key
     * @param array  $obj
     *
     * @return string
     */
    protected function getPopupCloseCallbackFunction(string $key, array $obj ): string
    {
        $jsCode = '';
        if (Request::$r->_uisource == 'arpymt') {
            $jsCode = 'reloadAllCredits("'.$key.'", "'.$this->getEntityMgr()->_entity.'");';
        }
        $jsCode .= parent::getPopupCloseCallbackFunction($key, $obj);

        return $jsCode;
    }

    /**
     * to credit card map
     *
     * @return array
     *
     */
    protected function getCreditCardMap()
    {
        return ARPymtEditor::receivableOfflineCreditCards($this->modulePreferences);
    }

    /**
     * Return an array of javascript files to include into the page
     *
     * @return array the list of javascript files to include
     */
    protected function getJavaScriptFileNames()
    {
        $jsFiles = parent::getJavaScriptFileNames();
        $jsFiles[] = '../resources/js/aradvancehelper.js';
        $jsFiles[] = '../resources/js/subledgertax.js';
        return $jsFiles;
    }

    /**
     * Cleanup the grid values before it reaches the manager
     *
     * @param EditorGrid    $grid      the grid
     * @param EntityManager $entityMgr the grid's object manager
     * @param array         $_obj      the transaction data
     *
     * @return bool
     */
    protected function innerCleanupLineItems($grid, $entityMgr, &$_obj)
    {
        $path = $grid->getProperty('path');
        if ( $path == 'SUBTOTALS' ) {
            if(isset($_obj[$path]) && is_array($_obj[$path])) {
                foreach ($_obj[$path] as $key => $item) {
                    if (!isset($item['TRX_AMOUNT']) || $item['TRX_AMOUNT'] === '') {
                        unset($_obj[$path][$key]);
                    }
                }
                $_obj[$path] = array_values($_obj[$path]);
            }
        } else {
            parent::innerCleanupLineItems($grid, $entityMgr, $_obj);
        }

        return true;
    }
/**
* This is a hook function for subclasses to adjust the metadata according to the current data & state
     *
     * @param array $obj the data
     *
     * @return bool
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        parent::mediateDataAndMetadata($obj);
        //upate all values of recordid in  object array in advanceapplied array
        if (!empty($obj['ADVANCEAPPLIED']) && is_array($obj['ADVANCEAPPLIED'])) {
            foreach ($obj['ADVANCEAPPLIED'] as &$advance) {
                if (isset($advance['RECORDID'])) {
                    $advance['RECORDID'] = XMLUtils::xmlSpecialChars($advance['RECORDID']);
                }
            }
        }
        return true;
    }

}
