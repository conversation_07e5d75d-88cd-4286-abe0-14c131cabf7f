<?php

/**
 * Manager class for the AR Posted Advance object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2015 Intacct Corporation All, Rights Reserved
 */

/**
 * Manager class for the AR Posted Advance object
 */
class ARPostedAdvanceManager extends SubLedgerPymtManager
{
    // Class variables
    /* @var array $inlineCreditRecordTypes */
    public static $inlineCreditRecordTypes = array();

    /* @var array $externalCreditRecordTypes */
    protected static $externalCreditRecordTypes = array();

    /* @var string $_validItemState */
    private $_validItemState;

    /**
     * @param array $params the parameters of the class
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
        $this->_validItemState = PRRECORD_STATE_PCONFIRMED;
    }

    /**
     * Return the offset account key for the transaction
     * Each subclass needs to define from where we will pick this offset from
     *
     * @param array $values the transaction data
     *
     * @return string|null the offset account key
     */
    public function getOffsetAccountKey($values)
    {
        return APARSetupManager::getOffsetAccountKey(SubLedgerTxnManager::ARADVANCE_RECTYPE, $this->getModuleKey());
    }

    /**
     * Get the payment key field path in the payment details data structure
     *
     * @return string the payment key field path in the payment details data structure
     */
    protected function getPaymentKeyPath()
    {
        return 'POSTEDADVANCEKEY';
    }

    /**
     * Get the payment entry key field path in the payment details data structure
     *
     * @return string the payment entry key field path in the payment details data structure
     */
    protected function getPaymentEntryKeyPath()
    {
        return 'POSTEDADVANCEENTRYKEY';
    }

    /**
     * Get the payment date field path in the payment details data structure
     *
     * @return string the paymentdate field path in the payment details data structure
     */
    protected function getPaymentDatePath()
    {
        return 'PAYMENTDATE';
    }

    /**
     * Get the payment amount field path in the payment details data structure
     *
     * @return string the the payment amount field path in the payment details data structure
     */
    protected function getPaymentAmountPath()
    {
        return 'TRX_POSTEDADVANCEAMOUNT';
    }

    /**
     * Get the payment base amount field path in the payment details data structure
     *
     * @return string the the payment amount field path in the payment details data structure
     */
    protected function getPaymentBaseAmountPath()
    {
        return 'POSTEDADVANCEAMOUNT';
    }

    /**
     * Get the record type of the transaction we are paying
     *
     * @return array the record type of the transaction we are paying
     */
    protected function getPaidTransactionRecordTypes()
    {
        $recordTypes = array(
            SubLedgerTxnManager::INVOICE_RECTYPE
        );
        return $recordTypes;
    }

    /**
     * Get the record type of the transaction we are fetching for payment
     *
     * @return array the record type of the transaction we are fetching for payment
     */
    protected function getParentTransactionRecordTypes()
    {
        $recordTypes = array(
            SubLedgerTxnManager::ARADVANCE_RECTYPE
        );
        return $recordTypes;
    }

    /**
     * Get the key field path of the transaction we need to fetch for the payment in the payment details data structure
     * @param string $recordType the transaction record type
     *
     * @return string the key field path of the transaction we need to fetch for the payment in the payment details data structure
     */
    protected function getParentTransactionKeyPath($recordType='')
    {
        return 'ADVANCEKEY';
    }

    /**
     * Get the entry key field path of the transaction we need to fetch for the payment in the payment details data structure
     * @param string $recordType the transaction record type
     *
     * @return string the key field path of the transaction we need to fetch for the payment in the payment details data structure
     */
    protected function getParentTransactionEntryKeyPath($recordType='')
    {
        return 'ADVANCEENTRYKEY';
    }

    /**
     * Get all the recordNo of the transactions to gather
     *
     * @param string $keyPath the transaction key path
     * @param array  $values the payment request data
     * @param array  $keys the transaction keys
     *
     * @return bool
     */
    protected function getTransactionKeys($keyPath, $values, &$keys)
    {
        // No specific transaction keys we can filter here.
        // All the open advances for that vendor are valid
        $keys = array();
        return true;
    }

    /**
     * Get the list of column to query for each transaction entry
     *
     * @return array the list of column to query for each transaction entry
     */
    protected function getTransactionEntryColumnList()
    {
        $selects = parent::getTransactionEntryColumnList();
        $key = array_search('PRENTRYOFFSETACCOUNTNO', $selects);
        unset($selects[$key]);
        $selects = array_values($selects);

        return $selects;
    }

    /**
     * Get the query filters for the transactions gathering
     *
     * @param array $values the payment request data
     * @param array $txnKeys the transactions keys
     *
     * @return array
     */
    protected function getTransactionQueryFilters($values, $txnKeys)
    {
        $primaryDimension = $this->getPrimaryDimension();
        $filters = array(
            array(
                array($primaryDimension, '=', $values[$primaryDimension]),
                array('STATE', '=', BasePRRecordManager::CONFIRMED_RAWSTATE)
            )
        );

        return $filters;
    }

    /**
     * @return string
     */
    protected function getDefaultStateForSubmitAction()
    {
        return BasePRRecordManager::DRAFT_RAWSTATE;
    }

    /**
     * Process the payment details data
     *
     * @param array $values the payment request data
     *
     * @return bool false if error else true
     */
    protected function processPymtDetails(&$values){
        $ok = true;

        // Call the parent for common processing
        $ok = $ok && parent::processPymtDetails($values);

        // Order the payment details by split details.
        // We should process the most specific payment first to be sure we do not
        // use those credits to pay transactions with no specific splits
        $pymtDetails = $this->getPymtDetails();
        $ok = $ok
              && usort(
                  $pymtDetails, function ($a, $b) {
                  $advanceKeyA = $this->getParentTransactionKey(null, $a);
                  $advanceKeyB = $this->getParentTransactionKey(null, $b);
                  $advanceEntryKeyA = $this->getParentTransactionEntryKey(null, $a);
                  $advanceEntryKeyB = $this->getParentTransactionEntryKey(null, $b);
                  if ( !empty($advanceKeyA) && !empty($advanceEntryKeyA) ) {
                      return -1;
                  } else if ( !empty($advanceKeyB) && !empty($advanceEntryKeyB) ) {
                      return 1;
                  } else if ( !empty($advanceKeyA) ) {
                      return -1;
                  } else if ( !empty($advanceKeyB) ) {
                      return 1;
                  }

                  return 0;
              }
              );
        $this->setPymtDetails($pymtDetails);

        return $ok;
    }


    /**
     * Validate the due of the transaction
     *
     * @param array $transaction the transaction data
     * @param array $pymtDetail the payment detail data
     *
     * @return bool false if error else true
     */
    protected function validateDue($transaction, $pymtDetail)
    {
        $ok = true;

        // Validate the payment on the advance transaction
        $ok = $ok && $this->validateAdvanceDue($pymtDetail);

        // Call the parent for common validations
        $ok = $ok && parent::validateDue($transaction, $pymtDetail);

        return $ok;
    }

    /**
     * Validate the advance due
     *
     * @param array $pymtDetail the payment details data
     *
     * @return bool false if error else true
     */
    protected function validateAdvanceDue($pymtDetail)
    {
        $ok = true;
        $gErr = Globals::$g->gErr;

        // Get the advances key. If no key then no need to continue.
        $advanceKey = $this->getParentTransactionKey(null, $pymtDetail);
        if ( empty($advanceKey) ) {
            return $ok;
        }

        // Get the advance
        $advance = $this->getParentTransactions($advanceKey);

        // Get the payment amount
        $pymtAmount = $this->getPaymentAmount($pymtDetail);

        // If the payment amount if bigger than the advances total due, throw an error
        if ( ibccomp($pymtAmount, $advance['TRX_TOTALDUE']) > 0 ) {
            $ok = false;
            $msg = "The payment amount is bigger than the advance total amount available.";
            $gErr->addError('AR-0190', __FILE__ . ':' . __LINE__, $msg);
        }

        return $ok;
    }

    /**
     * Validate the distribution at the line level
     *
     * @param array $transaction the transaction data
     * @param array $pymtDetail the payment detail data
     *
     * @return bool false if error else true
     */
    protected function validateEntryDue($transaction, $pymtDetail)
    {
        $ok = true;

        // Validate the payment against the advance entry
        $ok = $ok && $this->validateAdvanceEntryDue($pymtDetail);

        // Call the parent for common validation
        $ok = $ok && parent::validateEntryDue($transaction, $pymtDetail);

        return $ok;
    }

    /**
     * Validate the distribution at the advance line level
     *
     * @param array $pymtDetail the payment details data
     *
     * @return bool false if error else true
     */
    protected function validateAdvanceEntryDue($pymtDetail)
    {
        $ok = true;
        $gErr = Globals::$g->gErr;

        // Do we have the entry key of the advance we will use to pay ?
        // If we do not, no need to continue
        $advanceEntryKey = $this->getParentTransactionEntryKey(null, $pymtDetail);
        if ( empty($advanceEntryKey) ) {
            return $ok;
        }

        // Get the advances key. If no key then no need to continue.
        $advanceKey = $this->getParentTransactionKey(null, $pymtDetail);
        if ( empty($advanceKey) ) {
            return $ok;
        }

        // Get the advance transactions
        $advance = $this->getParentTransactions($advanceKey);

        // Find the avance line and validate
        foreach ( $advance['ITEMS'] as $item ) {

            // Continue until we find the right entry
            if ( $item['RECORDNO'] != $advanceEntryKey ) {
                continue;
            }

            // If the amount to pay on the line is more than the available amount on the advance then throw an error
            $advanceDue = $this->calculateEntryTotalDue($item);
            $lineAmountToPay = $this->getPaymentAmount($pymtDetail);
            if ( ibccomp($lineAmountToPay, $advanceDue) > 0 ) {
                $ok = false;
                $msg = "The payment amount is bigger than the advance entry amount available.";
                $gErr->addError('AR-0191', __FILE__ . ':' . __LINE__, $msg);
            }
            break;
        }

        return $ok;
    }

    /**
     * Apply a payment
     *
     * @param array $transaction the transaction data
     * @param array $pymtDetail the payment detail data
     *
     * @return bool false if error else true
     */
    public function applyPayment(&$transaction, $pymtDetail)
    {
        $ok = true;

        // Apply the payment
        $ok = $ok && parent::applyPayment($transaction, $pymtDetail);

        // Match the payment amount with the credits
        $ok = $ok && $this->distributeCredits($transaction, $pymtDetail);

        return $ok;
    }

    /**
     * Match credits to a transaction
     *
     * @param array $transaction the transaction data
     * @param array $pymtDetail the payment detail data
     *
     * @return bool false if error else true
     */
    protected function distributeCredits($transaction, $pymtDetail)
    {
        $ok = true;

        // Do we apply a credit on a specific transaction line only  ?
        $trxEntryKey = $this->getPaidTransactionEntryKey($pymtDetail);

        // Match each payment with the credits
        foreach ( $transaction['ITEMS'] as $entry ) {

            // No the right transaction entry ? No need to continue
            if ( !empty($trxEntryKey) && $entry['RECORDNO'] != $trxEntryKey ) {
                continue;
            }

            // Get the credit amount to match. If not amount no need to continue
            $amountPath = $this->getDistributionAmountPath();
            $creditToApply = $entry['__distribute'][$amountPath];
            if ( empty($creditToApply) || !ibccomp($creditToApply, '0') ) {
                continue;
            }

            // Match credits for that line
            $ok = $ok && $this->distributeEntryCredits($entry, $creditToApply, $pymtDetail);
            if ( !$ok ) {
                break;
            }
        }

        return $ok;
    }

    /**
     * Match each transaction entry with credits
     *
     * @param array $entry the transaction entry data
     * @param float $creditToApply
     * @param array $pymtDetail
     *
     * @return bool false if error else true
     */
    protected function distributeEntryCredits($entry, $creditToApply, $pymtDetail)
    {
        $ok = true;

        // Do we already have a credit specified ?
        $parentRecordTypes = $this->getParentTransactionRecordTypes();
        $parentKey = $this->getParentTransactionKey(null, $pymtDetail);
        $parentEntryKey = $this->getParentTransactionEntryKey(null, $pymtDetail);

        // Get the credits available
        $credits = $this->getParentTransactions();

        if ( !empty($parentKey) && !empty($parentEntryKey) ) {

            // We already validate the amount at this point so we know this assignment is valid
            $credit =& $credits[$parentKey];
            $creditEntry =& $this->getTransactionEntryByRecordNo($credit, $parentEntryKey);
            $ok = $ok && $this->assignCreditToTransactionLine($entry, $creditEntry, $creditToApply);

        } else {

            // Get the credits or credit if key is specified
            if ( !empty($parentKey) ) {
                $availableCredits =& $credits[$parentKey]['ITEMS'];
            } else {
                $availableCredits =& $this->gatherCreditEntries($credits);
            }

            // Find all the credits that matches that line to pay it off
            $ok = $ok && $this->matchCreditsForTransactionLine($entry, $creditToApply, $availableCredits);
        }

        // Update the credits data
        self::setTransactions($parentRecordTypes[0], $credits); // There is only 1 recordtype for now !

        return $ok;
    }

    /**
     * Get the list of available credit entries
     *
     * @param array $transactions the list of transaction from which to gather the entries
     *
     * @return array the list of available credit entries
     */
    protected function &gatherCreditEntries(&$transactions)
    {
        $credits = array();
        foreach ( $transactions as &$transaction ) {
            foreach ( $transaction['ITEMS'] as &$entry ) {
                $credits[] =& $entry;
            }
            unset($entry);
        }
        unset($transaction);

        return $credits;
    }

    /**
     * Distribute the rounding on the entry
     *
     * @param array   $entry the transaction data
     * @param float $roundingDiff the rounding difference to distribute
     *
     * @return bool false if error else true
     */
    public function distributeRoundingOnEntry(&$entry, &$roundingDiff)
    {
        $ok = true;

        // Distribute the pennie on the credit map for that specific transaction
        $creditMap = $this->getCreditMap();
        foreach ( $creditMap as &$credit ) {
            foreach ( $credit as &$creditEntry ) {
                $entryMapAmount = &$creditEntry[$entry['RECORDKEY']][$entry['RECORDNO']];
                if ( empty($entryMapAmount) ) {
                    continue;
                }
                if ( ibccomp($roundingDiff, '0') > 0 ) {
                    $entryMapAmount = ibcadd($entryMapAmount, '0.01');
                } else {
                    $entryMapAmount = ibcsub($entryMapAmount, '0.01');
                }
                unset($entryMapAmount);
                break 2;
            }
            unset($creditEntry);
        }
        unset($credit);

        // Set the new credit map
        $this->setCreditMap($creditMap);

        // Take off the pennies from the entries
        $ok = $ok && parent::distributeRoundingOnEntry($entry, $roundingDiff);

        return $ok;
    }

    /**
     * Build the payment records
     *
     * @param array $values the payment request data
     * @param array $payments the payments records data
     *
     * @return bool false if error else true
     */
    protected function buildPaymentRecords($values, &$payments)
    {

        $ok = true;

        // Build a payment per advance
        $creditsMap = $this->getCreditMap();
        $creditKeys = array_keys($creditsMap);
        foreach ( $creditKeys as $creditKey ) {
            $payment = $this->buildPaymentRecord($values, $creditKey);
            $payments[] = $payment;
        }

        return $ok;
    }

    /**
     * Build a payment record
     *
     * @param array $values the payment request data
     * @param int|null $creditKey the credit key
     *
     * @return array the payment record
     */
    protected function buildPaymentRecord($values, $creditKey=null)
    {
        // Build the header
        $payment = $this->buildPaymentHeader($values, $creditKey);
        // Populate the base currency
        $transactions = self::getTransactions(SubLedgerTxnManager::ARADVANCE_RECTYPE);
        if(!empty($transactions)) {
            foreach($transactions as $transaction) {
                if($transaction['RECORDNO'] == $creditKey) {
                    $payment['BASECURR'] = $transaction['BASECURR'];
                    $payment['CURRENCY'] = $transaction['CURRENCY'];
                    break;
                }
            }
        }
        // Build the entries
        $payment['ITEMS'] = $this->buildPaymentEntries($creditKey, $payment);

        return $payment;
    }

    /**
     * Build a payment record header
     *
     * @param array $values the payment request data
     * @param int|null   $creditKey the credit key
     *
     * @return array the payment record header
     */
    protected function buildPaymentHeader($values, $creditKey=null)
    {
        $header = parent::buildPaymentHeader($values);
        $header['PARENTPAYMENTKEY'] = $creditKey;

        return $header;
    }

    /**
     * Build the payment record entries
     *
     * @param int $creditKey the credit key
     * @param array $payment the payment record
     *
     * @return array the payment record entries
     */
    protected function buildPaymentEntries($creditKey, $payment)
    {
        $transactionEntries = $this->collectTrxEntriesByCreditKey($creditKey);

        return parent::buildPaymentEntries($transactionEntries, $payment);
    }

    /**
     * Collect all the transaction entry paid by a credit
     *
     * @param int $creditKey the credit key
     *
     * @return array the list of transaction entries
     */
    protected function collectTrxEntriesByCreditKey($creditKey)
    {
        $entries = array();
        $creditsMap = $this->getCreditMap();
        $creditMap = $creditsMap[$creditKey];
        $amountPath = $this->getDistributionAmountPath();

        foreach ( $creditMap as $creditEntryKey => $trxMap ) {
            foreach ( $trxMap as $trxKey => $trxEntryMap ) {
                $transaction = $this->getPaidTransactions($trxKey);
                $trxEntryKeys = array_keys($trxEntryMap);
                foreach ( $transaction['ITEMS'] as $entry ) {
                    if ( in_array($entry['RECORDNO'], $trxEntryKeys) ) {
                        $entry['__distribute'][$amountPath] = $trxEntryMap[$entry['RECORDNO']];
                        $entry['__distribute']['PARENTENTRYKEY'] = $creditEntryKey;
                        $entries[] = $entry;
                    }
                }
            }
        }

        return $entries;
    }

    /**
     * Build the payment record entry
     *
     * @param array $entry the transaction entry
     * @param int   $nextRecordNo
     *
     * @return array array the payment record entry
     */
    protected function buildPaymentEntry($entry, $nextRecordNo)
    {
        $pymtEntry = parent::buildPaymentEntry($entry, $nextRecordNo);
        $pymtEntry['PARENTENTRYKEY'] = $entry['__distribute']['PARENTENTRYKEY'];

        return $pymtEntry;
    }

    /**
     * Build the payment detail data
     *
     * @param array $payment the payment data
     * @param array $entry the payment entry data
     *
     * @return array the payment detail data
     */
    protected function buildPaymentDetail($payment, $entry)
    {
        $pymtDetail = parent::buildPaymentDetail($payment, $entry);

        // Add the parent advance key to the payment detail data structure
        $parentKeyPath = $this->getParentTransactionKeyPath();
        $parentEntryKeyPath = $this->getParentTransactionEntryKeyPath();
        $pymtDetail[$parentKeyPath] = $payment['PARENTPAYMENTKEY'];
        $pymtDetail[$parentEntryKeyPath] = $entry['PARENTENTRYKEY'];

        return $pymtDetail;
    }

    //TODO taken from PaymentManager. Check the alternative later
    /**
     *     Uses the workflow definition file for the type of each payment received to translate the state
     *    information into a presenation layer description of the payment's current status.
     *
     *    IN/OUT
     *        $paymentList - an array of payments whose 'STATE' value is to be translated, having the
     *            following minimal structure:
     *            array(
     *                'PAYMENTTYPE'    => <prrecord.recordtype of the payment>
     *                'STATE'            => <prrecord.state of the payment>
     *            )
     *
     *    Returns
     *        The input array with it's 'STATE' value translated as described.
     *
     * @param array $paymentList
     *
     * @return array
     */
    function SetStatusFromWorkflow(&$paymentList)
    {
        $statecache = array();
        foreach($paymentList as $key => $row) {
            $paytype = $row['PAYMENTTYPE'];
            $state = $row['STATE'];
            if (empty($statecache[$paytype][$state])) {
                unset($objhist);
                $objhist = Globals::$g->gManagerFactory->getManager('prrecordhistory');
                $objhist->SetWorkflow($this->GetWorkflowName($paytype));
                $statecache[$paytype][$state] = $objhist->GetNomenclatureState($state);
            }
            $paymentList[$key]['STATE'] = $statecache[$paytype][$state];
        }

        return $paymentList;

    }

    /**
     * Returns the workflow name based on the payment type.
     *
     * @param string $paymenttype
     *
     * @return string|null
     */
    function GetWorkflowName($paymenttype)
    {
        $valid_workflows = $this->_schemas[$this->_entity]['workflows'];
        // validate only if the workflows are set in the entity
        if(isset($valid_workflows)) {
            assert(isset($valid_workflows[$paymenttype]));
            if (!isset($valid_workflows[$paymenttype])) {
                epp("invalid paymenttype: $paymenttype");
            } else {
                return $valid_workflows[$paymenttype];
            }
        }
        return null;
    }

    /**
     * Return the result for the given ID.
     *a
     * @param int  $ID
     * @param array|null  $fields
     *
     * @return array
     */
    function Get($ID, $fields=null)
    {
        $obj = parent::get ($ID, $fields);
        //TODO this call is temporary for making the current code work, this need to be refactored later on.
        $obj = $this->populatePaymentDetails($ID, $obj);
        // ADD HISTORY INFORMATION
        $obj['HISTORY'] = $this->GetHistory($ID, $obj);

        // we need to unset offset line items from prentry since the base query returns all rows
        // we want to show only the lines which lineitems not offsets
        $nlines = [];
        $lines = $obj['ITEMS'];

        // filter lineitems
        foreach( $lines as $line) {
            if( $line['LINEITEM'] == 'T' ) {
                $nlines[] = $line;
            }
        }

        $obj['PRENTRY'] = $nlines;

        $invQuery =
            "SELECT 
			pr.record#, pr.parentpayment, pr.recordtype, pr.recordid
			, pr.docnumber, pr.whendue, pr.whenpaid, pr.whencreated
			, nvl(pr.trx_totalentered, pr.totalentered) totalentered
			, nvl(pr.trx_totalpaid, pr.totalpaid) totalpaid
			, nvl(pr.trx_totaldue, pr.totaldue) totaldue
			, sum(nvl(prp.trx_amount, prp.amount)) appliedamount
		FROM prrecord pr, prentrypymtrecs prp
		WHERE
			prp.cny# =:1 
			and prp.paymentkey in (select record# from prrecordmst porec where porec.cny# = :1 and porec.parentpayment = :2)
			and pr.cny# = prp.cny# and pr.record# = prp.recordkey
			and pr.recordtype in ('ri', 'ra')
		GROUP BY
			pr.record#, pr.parentpayment, pr.recordtype, pr.recordid
			, pr.docnumber, pr.whendue, pr.whenpaid, pr.whencreated
			, nvl(pr.trx_totalentered, pr.totalentered)
			, nvl(pr.trx_totalpaid, pr.totalpaid)
			, nvl(pr.trx_totaldue, pr.totaldue)
		ORDER BY pr.whencreated, pr.record#
		";

        // do the inv query
        $allPaidInvoices = QueryResult(array($invQuery, GetMyCompany(), $ID));

        // construct the paid invoices information
        foreach($allPaidInvoices as $inv) {

            // make the HTML drilldown a separate field so as not to interfere with other clients (XML)
            $inv['DRILLDOWN'] = $this->_BuildDrillDown($inv);

            $obj['INVOICES'][] = $inv;

        }
        //eppp_p($obj);
        $exchTypeMgr = Globals::$g->gManagerFactory->getManager('exchangeratetypes');
        $item = $nlines[0];
        $obj['EXCH_RATE_DATE'] = ($item['EXCH_RATE_DATE'] ?: '');
        $obj['EXCHANGE_RATE'] = ($item['EXCHANGE_RATE'] ? number_format($item['EXCHANGE_RATE'], 8) : '');
        $obj['EXCH_RATE_TYPE_ID'] = (isset($item['EXCH_RATE_TYPE_ID']) &&  $item['EXCH_RATE_TYPE_ID']!='' ? $exchTypeMgr->GetExchangeRateTypeName($item['EXCH_RATE_TYPE_ID']) : '');

        return $obj;
    }

    /**
     * Get the object to delete
     *
     * @param int $ID the ID of the object to delete
     *
     * @return array the object to delete
     */
    protected function getForDelete($ID)
    {
        // Get the raw object
        $objs = $this->GetRaw($ID);
        // set the key field name
        if(!empty($objs)) {
            $obj = $objs[0];
            if(!empty($obj)) {
                $obj[$this->GetKeyFieldName()] = $obj['RECORD#'];
            }
        }

        /** @noinspection PhpUndefinedVariableInspection */
        return $obj;
    }

    /**
     * This method populates the
     *
     * @param int $ID
     * @param array $obj
     *
     * @return array
     */
    private function populatePaymentDetails($ID, &$obj) {

        //TODO : This is to fix the existing broken posted payment lister, need to look for better alternative
        global $kAPid, $kEEid;
        $creditRTypes = array(PRRECORD_TYPE_ARADJUSTMENT, PRRECORD_TYPE_AROVERPAYAPPL,
                              PRRECORD_TYPE_EEOVERPAYAPPL, PRRECORD_TYPE_INVOICE, PRRECORD_TYPE_EXPENSEADJUSTMENTREPORT);

        $query = "SELECT paidrec.record#, paidrec.recordid, paidrec.docnumber,".
                 "nvl(paidrec.trx_totalentered, paidrec.totalentered) totalentered, ".
                 "nvl(paidrec.trx_totaldue, paidrec.totaldue) totaldue, ".
                 "paidrec.whendue, paidrec.recpaymentdate, paidrec.whencreated, paidrec.recordtype, " .
                 "decode(paidrec.currency, prpaymentrecords.currency, nvl(prpaymentrecords.trx_amount, prpaymentrecords.amount), prpaymentrecords.amount) amount, ".
                 "prpaymentrecords.trx_amount, ".
                 "case when payment.recordtype in ('" . join("', '", $creditRTypes) .
                 "') then 1 else 0 end CREDITSAPPLIED,  " .
                 "decode(payment.recordtype, '" . PRRECORD_TYPE_ARDISCOUNT . "', 1, 0) DISCOUNT,  " .
                 "prpaymentrecords.parentpayment, payment.whencreated APPLYDATE, ".
                 "prpaymentrecords.paymentkey PAYMENTDETAIL,  payment.state PAYMENTSTATE, ".
                 "decode(payment.parentpayment, ?, nvl(prpaymentrecords.trx_amount, ".
                 "prpaymentrecords.amount), 0) MOREAPPLIED  " .
                 "FROM prrecord paidrec, prpaymentrecords, prrecordmst payment  " .
                 "WHERE (prpaymentrecords.parentpayment = ? or payment.parentpayment = ?) ".
                 "and payment.record# = prpaymentrecords.paymentkey and " .
                 "prpaymentrecords.recordkey = paidrec.record# and " .
                 "paidrec.cny# = ? AND prpaymentrecords.cny# = ? and payment.cny# = ? ".
                 "and payment.recordtype not in ('pm', 'rm', 'em') ";

        // If our instantiated class wants detail on only those payments in a certain state, add the appropriate filter.
        if ($this->_validItemState) {
            $query .= "and prpaymentrecords.state = '" . $this->_validItemState . "'";
        }

        // Do the query
        $query .= ' order by payment.whencreated, payment.record#, prpaymentrecords.amount';
        $queryItems = array(
            'QUERY' => $query,
            'ARGTYPES' => array('integer', 'integer', 'integer', 'integer', 'integer', 'integer')
        );
        $allPaymentsToItems = $this->DoCustomQuery($queryItems, array($ID, $ID, $ID));

        // CONSTRUCT THE PAID ITEM INFORMATION
        $uniquePayItems = $creditMap = $discountMap = $paySumMap = $payDetailMap = array();
        $obj['PAYMENTS'] = $parentPymtMap = array();

        // array for supdoc ids
        $ids_supdocs = array();

        foreach($allPaymentsToItems as $paidItem) {
            $paidAmount = $paidItem['AMOUNT'];
            $paidItemKey = $paidItem['RECORD#'];

            // Collect the record ids for querying the Supdocs
            if($paidItem['RECORDTYPE'] == 'pi' || $paidItem['RECORDTYPE'] == 'ei') {
                $ids_supdocs[] = $paidItemKey;
            }

            $parentPayKey = $paidItem['PARENTPAYMENT'];

            // GATHER A UNIQUE SET OF THE PAID RECORDS
            if (!is_array($uniquePayItems[$parentPayKey]) || (!in_array($paidItemKey, $uniquePayItems[$parentPayKey]))) {
                $uniquePayItems[$parentPayKey][] = $paidItemKey;
                $parentPymtMap[$parentPayKey][] = $paidItem;
            }

            // SUM THE PAYMENT AMOUNTS BY TYPE
            if ($paidItem['CREDITSAPPLIED']) {
                $creditMap[$parentPayKey][$paidItemKey] = bcadd($creditMap[$parentPayKey][$paidItemKey], $paidAmount);
            } else if ($paidItem['DISCOUNT']) {
                $discountMap[$parentPayKey][$paidItemKey] = bcadd($discountMap[$parentPayKey][$paidItemKey], $paidAmount);
            } else {
                // sum the 'AMOUNT' paid directly by this record ($ID)
                $paySumMap[$parentPayKey][$paidItemKey] = bcadd($paySumMap[$parentPayKey][$paidItemKey], $paidAmount);
            }

            // CONSTRUCT THE PAYMENT DETAIL INFO (which record paid how much to this item)
            $payDetailMap[$parentPayKey][$paidItemKey][$paidItem['PAYMENTDETAIL']] = $paidAmount;
        }

        // set the transaction type for supdocs
        if ($this->_moduleKey == $kAPid) {
            $trtype = "ARINVOICE";
        } else if ($this->_moduleKey == $kEEid) {
            $trtype = "EMPEXPENSE";
        }

        if(isset($trtype)) {
            // Query supdocs
            // Got the recordids array. Form the query to get the supdocs
            $stmt= array();
            $stmt[0] = "select supdocmaps.recordid, supdoc.documentid as supdocid from supdocmaps, supdoc where supdocmaps.cny# = :1 and supdoc.cny# = :1  and supdocmaps.transactiontype = :2 and supdocmaps.documentid = supdoc.record#";
            $stmt[1] = GetMyCompany();
            $stmt[2] = $trtype;
            $stmt = PrepINClauseStmt($stmt, $ids_supdocs, " and supdocmaps.recordid ");
            $rs = QueryResult($stmt);
            $doc_count = count($rs);
            if ($doc_count > 0) {
                $supArr = array();
                foreach ($rs as $supdata) {
                    $recid = $supdata['RECORDID'];
                    $doc = $supdata['SUPDOCID'];
                    $supArr["$recid"]["SUPDOCID"] = $doc;
                }
            }
        }

        // INCLUDE THE ITEMS AND SUMMARY DATA
        foreach($parentPymtMap as $parentPayKey => $itemList) {
            foreach($itemList as $item) {
                $index = count($obj['PAYMENTS']);
                $obj['PAYMENTS'][] = $item;
                $obj['PAYMENTS'][$index]['AMOUNT'] = $paySumMap[$parentPayKey][$item['RECORD#']];
                $obj['PAYMENTS'][$index]['CREDITSAPPLIED'] = $creditMap[$parentPayKey][$item['RECORD#']];
                $obj['PAYMENTS'][$index]['DISCOUNT'] = $discountMap[$parentPayKey][$item['RECORD#']];
                $obj['PAYMENTS'][$index]['PAYMENTDETAIL'] = $payDetailMap[$parentPayKey][$item['RECORD#']];

                // Add the supdoc details into the item details
                if($item['RECORDTYPE'] == 'pi' || $item['RECORDTYPE'] == 'ei') {
                    if (isset($supArr[$item['RECORD#']])) {
                        /** @noinspection PhpUndefinedVariableInspection */
                        $obj['PAYMENTS'][$index]['SUPDOCID'] = $supArr[$item['RECORD#']]['SUPDOCID'];
                    }
                }

            }
        }

        //iterate and add the invoce details
        // FORMAT AR-SPECIFIC FIELDS
        foreach($obj['PAYMENTS'] as $itemkey => $item) {
            // make the HTML drilldown a separate field so as not to interfere with other clients (XML)
            $obj['PAYMENTS'][$itemkey]['DRILLDOWN'] = $this->_BuildDrillDown($item);
            $obj['PAYMENTS'][$itemkey]['SUPDOC'] = $this->_BuildSupDoc($item);
        }
        return $obj;
    }

    /**
     * Returns an object containing the full workflow history.  Also sets the state of the object.
     *
     * @param int $ID Unique Id
     * @param array $obj in/out The prrecord object.  This function will set the state
     *
     * @return bool|string[][] An array containing all the workflow history
     */
    function GetHistory($ID, &$obj)
    {

        // GET THE HISTORY
        $queryHistory = array(
            'QUERY' => 'SELECT prrecordhistory.action, prrecordhistory.eventdate, contact.name, prrecordhistory.comments ' .
                       'FROM prrecordhistory, contact ' .
                       'WHERE prrecordhistory.recordkey = ? and prrecordhistory.contactkey = contact.record# ' .
                       'AND prrecordhistory.cny# = ? and contact.cny# = ? ' .
                       'ORDER BY prrecordhistory.record# asc',
            'ARGTYPES' => array('integer', 'integer', 'integer')
        );

        $history = $this->DoCustomQuery($queryHistory, array($ID));

        if (isl_stristr($obj['DOCUMENTNUMBER'], 'Voided') ) {
            $workflow = 'voidpayment';
        } elseif ($obj['PAYMENTTYPE'] == 'Printed Check') {
            $workflow = 'printedcheck';
        } elseif ($obj['PAYMENTTYPE'] == 'Online') {
            $workflow = 'onlinepayment';
        } else {
            $workflow = 'arpayment';
        }

        // GROOM THE HISTORY
        $histobj = Globals::$g->gManagerFactory->getManager('prrecordhistory');
        $histobj->SetWorkflow($workflow);

        foreach($history as $eventkey => $event) {
            $nomenclature = $histobj->GetNomenclatureAction($event['ACTION'], $this->fromGateway);
            $history[$eventkey]['ACTION'] = $nomenclature[0];
            $history[$eventkey]['EVENTDATE'] = $event['EVENTDATE'];
        }

        // now that we have the workflow, set the state from the nomenclature
        $obj['STATE'] = $histobj->GetNomenclatureState($obj['STATE']);

        return $history;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function onAfterAdd(&$values)
    {
        $ok = true;
        //there is no after add actions needed for posted advances hence not calling the parent
        //method to create any history.
        return $ok;
    }

    /**
     * @param array $item
     *
     * @return string
     */
    function _BuildDrillDown($item)
    {
        $drilldown = $item['RECORDID'];

        $recordhash = urlencode($item['RECORD#']);
        $recordtype = urlencode($item['RECORDTYPE']);

        $drillmeta = array(
            'pi' => array('edit' => 'ap/lists/apbill/edit',
                          'view' => 'ap/lists/apbill/view'),
            'pa' => array('edit' => 'ap/lists/apadjustment/edit',
                          'view' => 'ap/lists/apadjustment/view'),
            'ri' => array('edit' => 'ar/lists/arinvoice/edit',
                          'view' => 'ar/lists/arinvoice/view'),
            'ra' => array('edit' => 'ar/lists/aradjustment/edit',
                          'view' => 'ar/lists/aradjustment/view')
        );

        if (!in_array($recordtype, array_keys($drillmeta))) {
            return $drilldown;
        }

        $viewop = GetOperationId($drillmeta[$recordtype]['view']);

        if ( ! IsOperationAllowed($viewop) ) {
            return $drilldown;
        }

        $opid = $viewop;

        $page  ="'editor.phtml?.do=view&.recordtype=$recordtype&.r=$recordhash&.op=$opid&.popup=1'";
        $recordid = ($item['RECORDID'] == '') ? '<i><b>view details</b></i>' : "<b>" . $item['RECORDID'] . "</b>";
        $drilldown = '<a href="javascript:Launch('."$page,'Details',640,480)" . '">' .$recordid . "</a>";

        return $drilldown;
    }

    /**
     * @param array $item
     *
     * @return string
     */
    function _BuildSupDoc($item)
    {
        $recordtype = urlencode($item['RECORDTYPE']);
        $supdoc_link = '';

        // Creating the view_attachment link for pi and ei records
        if($recordtype == 'pi' || $recordtype == 'ei') {
            $supdocid = urlencode(URLCleanParams::insert('.val', $item['SUPDOCID']));
            if (isset($supdocid) && $supdocid != '') {
                $_sess = Session::getKey();
                $opid = GetOperationId('co/lists/supportingdocumentdata/view');
                $scripturl = "lister.phtml?.do=view&.it=supportingdocumentdata&.val=$supdocid&.op=$opid&.popup=1&.viewsonly=1&.sess=$_sess";

                $supdoc_link = '<a class="Pick"  id = "fld_obj__SUPDOCID"  title="view_attachment" href="#" onClick="Launch(\''.$scripturl.'\',\'supdoc\',600,400); return	false;"  onmouseover="window.status=\'View the attachments\';return true;"   onmouseout="window.status=\'\'; return true;"><img src="' . IALayoutManager::getCSSButtonPath("paperclip.gif") . '" border="0" alt="view_attachment" ></a>';
            }
        }

        return $supdoc_link;
    }

    /**
     * Payment with discount do not need to create the payment detail entry as it will be
     * created with actual payment, so return false.
     *
     * @return bool
     */
    protected function isPaymentDetailCreationAllowed()
    {
        return false;
    }

    /**
     * Validate the payment method
     *
     * @param array $values the transaction data
     *
     * @return bool false if error else true
     */
    protected function validatePaymentMethod($values)
    {
        // No need to validate the payment methods for advance payment
        return true;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function validateBankAccount($values) {
        return true;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function translatePaymentMethod(&$values) {
        $ok = true;
        if(isset($values['PAYMENTMETHOD'])) {
            $ok = parent::translatePaymentMethod($values);
        }
        return $ok;
    }

    /**
     * Build the offset entry for a line item
     *
     * @param array  $values                the transaction data
     * @param array  $item                  the line item data
     * @param array  $offsetGroupKeyMap     the offset map of recordno by summarization fields
     * @param int    $accountKey            the offset account key
     * @param string $baselocationKey       the baselocation key
     *
     * @return array the offset entry
     */
    protected function buildOffsetEntry($values, $item, &$offsetGroupKeyMap, $accountKey, $baselocationKey = '')
    {
        $offset = parent::buildOffsetEntry($values, $item, $offsetGroupKeyMap, $accountKey, $baselocationKey);
        if(isset($item['PARENTENTRYBASELOCATION'])){
            $offset['BASELOCATION'] = $item['PARENTENTRYBASELOCATION'];
        }
        if(isset($item['PARENTENTRYDEPT'])){
            $offset['DEPT#'] = $item['PARENTENTRYDEPT'];
        }
        if(isset($item['PARENTENTRYACCT'])){
            $offset['ACCOUNTKEY'] = $offset['GLOFFSETKEY'] = $item['PARENTENTRYACCT'];
        }
        return $offset;
    }

    /**
     * get the Inline application credit preference path
     *
     * @return string the preference path
     */
    public function getInlineCreditPrefPath()
    {
        return 'AR_LIMITCREDIT';
    }


    /**
     * Overridden the method to set the GL posting false before add API call and set it back to original value. This
     * is needed to avoid the GL batch premature update in case of multi book compnay using advance credits.
     *
     * @param array $values the transaction data
     *
     * @return bool false if error else true
     */
    protected function regularAdd(&$values)
    {

        $tempGLPost = $this->getPostToGL();
        // Mark the post to GL false to avoid the premature update onthe GL
        $this->setPostToGL(false);
        $ok = parent::regularAdd($values);
        // Set the original value back after add API call
        $this->setPostToGL($tempGLPost);
        return $ok;
    }

    /**
     * Figure out the journal to use for the transaction batch
     *
     * The batching logic is not centralized or organized in any approriate way so we need ot delegate the job
     * to the transaction classes to figure out where to put the transaction.
     *
     * @param array      $values       the transaction data
     * @param string|int $origBatchKey the original batch key
     *
     * @return string the journal of the batch where to post the transaction
     */
    protected function getBatchJournal($values, $origBatchKey)
    {
        GetFinAcctJrnlSymbols($values['FINANCIALENTITY'], $entSymbolMap, $this->getModuleKey(), self::CREDIT, true);
        $journalSymbol = $entSymbolMap[$values['FINANCIALENTITY']];
        return $journalSymbol;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function unsetFinancialEntity ( &$values )
    {
        unset($values['FINANCIALENTITY']);
        return true;
    }

    /**
     * @param array $values
     * @param string $journalSymbol
     *
     * @return bool
     */
    protected function updateBatch($values, $journalSymbol)
    {
        return true;
    }

    /**
     * @return APPaymentDetailProcessor|ARPaymentDetailProcessor
     */
    public function getPymtDetailProcessor()
    {
        return new ARPaymentDetailProcessor();
    }

    /**
     * @return string
     */
    public function getPaymentDetailModulePath()
    {
        return 'arpymtdetail';
    }

}