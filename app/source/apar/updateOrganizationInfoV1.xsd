<?xml version="1.0" encoding="utf-8"?>
<!-- edited with XMLSpy v2014 sp1 (http://www.altova.com) by CCD (Infosys Limited) -->
<!--Version History-->
<!-- DP Version
            1.0 - Baseline version (PRJ0005N55- GCPT2013 ERP Partners Integration (GM&P-TIRB 5))
-->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://www.americanexpress.com/PAYVE/OrganizationManagementService/V1/updateOrganizationInfo" targetNamespace="http://www.americanexpress.com/PAYVE/OrganizationManagementService/V1/updateOrganizationInfo" elementFormDefault="qualified">
	<xs:element name="updateOrganizationInfo" type="tns:UpdateOrganizationInfoType"/>
	<xs:element name="updateOrganizationInfoResponse" type="tns:UpdateOrganizationInfoResponseType"/>
	<xs:element name="updateOrganizationInfoFault" type="tns:UpdateOrganizationInfoFaultType"/>
	<xs:complexType name="UpdateOrganizationInfoType">
		<xs:sequence>
			<xs:element name="Request" type="tns:RequestType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UpdateOrganizationInfoResponseType">
		<xs:sequence>
			<xs:element ref="tns:updateOrganizationInfo" minOccurs="0"/>
			<xs:element name="Response" type="tns:ResponseType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UpdateOrganizationInfoFaultType">
		<xs:sequence>
			<xs:element ref="tns:TrackingHdr" minOccurs="0"/>
			<xs:element ref="tns:updateOrganizationInfo" minOccurs="0"/>
			<xs:element ref="tns:Fault"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RequestType">
		<xs:sequence>
			<xs:element ref="tns:ServiceAttributesGrp"/>
			<xs:element name="UpdateOrganizationInfoReqGrp" type="tns:UpdateOrganizationInfoReqGrpType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ResponseType">
		<xs:sequence>
			<xs:element name="Status" type="tns:StatusType"/>
			<xs:element name="UpdateOrganizationInfoRespGrp" type="tns:UpdateOrganizationInfoRespGrpType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!-- Fault Block Definition -->
	<xs:element name="Fault" type="tns:FaultType"/>
	<xs:complexType name="FaultType">
		<xs:sequence>
			<xs:element name="FaultCode" type="xs:string"/>
			<xs:element name="FaultString" type="xs:string"/>
			<xs:element name="FaultActor" type="xs:string"/>
			<xs:element name="FaultDetail" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>
	<!-- Status Block Definition -->
	<xs:element name="Status" type="tns:StatusType"/>
	<xs:complexType name="StatusType">
		<xs:sequence>
			<xs:element name="RespCd" type="xs:string"/>
			<xs:element name="RespDesc" type="xs:string" minOccurs="0"/>
			<xs:element name="ExplCd" type="xs:string" minOccurs="0"/>
			<xs:element name="ExplDesc" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="TrackingHdr" type="tns:TrackingHdrType"/>
	<xs:complexType name="TrackingHdrType">
		<xs:sequence>
			<xs:element name="RequestorInfo" type="tns:RequestorInfoType"/>
			<xs:element name="ProviderInfo" type="tns:ProviderInfoType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RequestorInfoType">
		<xs:sequence>
			<xs:element name="MsgPostTime" type="xs:string"/>
			<!-- Time stamp when the message sent by the requestor-->
			<xs:element name="MessageID" type="xs:string"/>
			<!-- Unique message id from requestor-->
			<xs:element name="CorrelationMessageID" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Unique transaction identifier to be provided by consumer per service request and used for end to end monitoring</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="32"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ProviderInfoType">
		<xs:sequence>
			<xs:element name="ProviderName" type="xs:string"/>
			<xs:element name="ESBTranId" type="xs:string" minOccurs="0"/>
			<xs:element name="ESBServerName" type="xs:string"/>
			<xs:element name="ESBTimeStamp" type="xs:string"/>
			<xs:element name="ESBElapsedTime" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ServiceAttributesGrpType">
		<xs:sequence>
			<xs:element name="MinorVer" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--Operation Specific Types-->
	<!--Response Block Definition-->
	<xs:element name="ServiceAttributesGrp" type="tns:ServiceAttributesGrpType"/>
	<xs:complexType name="UpdateOrganizationInfoReqGrpType">
		<xs:sequence>
			<xs:element name="TxnIdentifier">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="30"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="OrganizationInfo" type="tns:OrganizationInfoType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrganizationInfoType">
		<xs:sequence>
			<xs:element name="PaymentEntityId">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="10"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="PartnerEntityId">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="10"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="RefEntityId" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="10"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="OrgNm" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="100"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="OrgShortNm" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="25"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="OrgId">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="100"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="OrgStatus" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="DisablePaymentMethods" type="tns:DisablePaymentMethodsType" minOccurs="0" maxOccurs="5"/>
			<xs:element name="PaymentMethods" type="tns:PaymentMethodsType" minOccurs="0"/>
			<xs:element name="ContactDetail" type="tns:ContactDetailType" minOccurs="0"/>
			<xs:element name="OrganizationAddr" type="tns:OrganizationAddrType" minOccurs="0"/>
			<xs:element name="CheckDetails" type="tns:CheckDetailsType" minOccurs="0"/>
			<xs:element name="ReserveField1" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="50"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="ReserveField2" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="50"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="ReserveField3" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="50"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="ReserveField4" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="50"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PaymentMethodsType">
		<xs:sequence>
			<xs:element name="PaymentMethod" minOccurs="0" maxOccurs="5">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="5"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ContactDetailType">
		<xs:sequence>
			<xs:element name="JobTitle" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="30"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Salutation" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="4"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="FirstNm" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="25"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="MiddleNm" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="LastNm" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="25"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="PrimaryEmailID" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="50"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="PrimaryPhone" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="15"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="MobilePhone" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="10"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Extension" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="10"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Fax" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="10"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrganizationAddrType">
		<xs:sequence>
			<xs:element name="Address1" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="50"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Address2" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="50"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Address3" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="50"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Address4" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="50"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="City" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="30"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="State" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="35"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Country" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="30"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="ZipCd" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="15"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="TaxId" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="20"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CheckDetailsType">
		<xs:sequence>
			<xs:element name="CheckSettings" type="tns:CheckSettingsType" minOccurs="0"/>
			<xs:element name="CheckDeliveryInd" type="tns:CheckDeliveryIndType" minOccurs="0"/>
			<xs:element name="PayorAttachment" type="tns:PayorAttachmentType" minOccurs="0" maxOccurs="5"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CheckSettingsType">
		<xs:sequence>
			<xs:element name="PrintServiceInd" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="FastForwardServiceInd" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="3"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="SupplierNotificationInd" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CheckDeliveryIndType">
		<xs:sequence>
			<xs:element name="MailVendorMtdCd" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="20"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="DeliveryInd" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="30"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="MailVendorAcctNbr" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="30"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PayorAttachmentType">
		<xs:sequence>
			<xs:element name="AttachmentId" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="40"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="AttachmentFile" type="xs:base64Binary" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UpdateOrganizationInfoRespGrpType">
		<xs:sequence>
			<xs:element name="PaymentEntityId">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="10"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="OrgId">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="100"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="StatusCd" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="20"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="ReserveField1" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="50"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="ReserveField2" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="50"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="ReserveField3" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="50"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="ReserveField4" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="50"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DisablePaymentMethodsType">
		<xs:sequence>
			<xs:element name="DisablePaymentMethod" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="5"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
