<?php
 /**
 *    FILE:            vendaging.ent
 *    AUTHOR:            <PERSON> <<EMAIL>>
 *    DESCRIPTION:    Aging bucket header ent
 *
 *    (C) 2014, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
*/

$kSchemas['vendaging'] = array(

    'children' => array(
        'vendagingdetail' => array( 'fkey' => 'prentrykey', 'invfkey' => 'record#', 'table' => 'prentrymst', 'join' => 'outer'),
    ),

    'object' => array(
        'RECORDNO',
        'PRENTRYKEY',
    ),
    
    'schema' => array(
        'RECORDNO'     => 'prrecordkey',
        'PRENTRYKEY'     => 'prentrykey',
    ), 
    

        'nexus' => array('vendagingdetail'  => array( 'object' => 'vendagingdetail', 'relation' => MANY2ONE, 'field' => 'prentrykey')
            ),

        'fieldinfo' => array(
        array (
        'path'        => 'RECORDNO',
                'fullname'    => 'IA.RECORD_NUMBER',
        'readonly'    => true, 
        'hidden'    => true,
        'desc'        => 'IA.RECORD_NUMBER',
        'type' => array ( 
        'ptype' => 'sequence', 
        'type' => 'text', 
        'maxlength' => 8
        )
        ),
        array (
        'path'        => 'PRENTRYKEY',
                        'fullname'    => 'IA.PRENTRY_KEY',
        'readonly'    => true, 
        'hidden'    => true,
        'desc'        => 'IA.RECORD_NUMBER',
        'type' => array ( 
        'ptype' => 'sequence', 
        'type' => 'text', 
        'maxlength' => 8
        )
        ),
       
        ),

        'table'     => 'VENDAGING',
        'vid'         => 'RECORDNO',
        'url' => array(
            'no_short_url' => true,    // Don't allow short url
        ),
        'printas'    => 'IA.VENDOR_AGING_REPORT',
        'pluralprintas'    => 'IA.VENDOR_AGING_REPORTS',
        'module'     => 'ap',
        'customerp' => array( 
        'SLTypes' => array(CUSTOMERP_SMARTLINKCLICK,
        CUSTOMERP_SMARTLINKFETCH,
        CUSTOMERP_SMARTLINKVALIDATE,
        CUSTOMERP_SMARTLINKWORKFLOW),
        'SLEvents' => array(CUSTOMERP_EVENT_ADD, 
        CUSTOMERP_EVENT_SET,
        CUSTOMERP_EVENT_ADDSET,
        CUSTOMERP_EVENT_DELETE,
        CUSTOMERP_EVENT_CLICK),
        'AllowCF' => true),
    'description' => 'IA.DETAIL_AND_HEADER_INFORMATION_FOR_VENDOR_BILL_AGIN',
);
