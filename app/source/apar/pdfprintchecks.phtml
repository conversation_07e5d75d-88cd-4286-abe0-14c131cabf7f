<?  
//=============================================================================
//
//	FILE:			pdfprintchecks.phtml
//	AUTHOR:			
//	DESCRIPTION:	
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================



require_once 'util.inc';
require_once 'backend_payment.inc';
require_once 'backend_npayment.inc';
require_once 'backend_pdf.inc';

Init();
$_r                    = Request::$r->_r;
$_mod                  = Request::$r->_mod;
$_quick                = Request::$r->_quick;
$_ordersortby          = &Request::$r->_ordersortby;
$_showEntityChecks     = Request::$r->_showEntityChecks;
$_pcopy                = Request::$r->_pcopy;
$_inclwatermark        = Request::$r->_inclwatermark;
$mod                   = Request::$r->mod;
$_showdetails          = Request::$r->_showdetails;
$_bankacct             = Request::$r->_bankacct;
$_showdetailedvendstub = Request::$r->_showdetailedvendstub;
$_samplecheck          = &Request::$r->_samplecheck;
$_checksperpage        = &Request::$r->_checksperpage;
$_showdetailbankdef    = Request::$r->_showdetailbankdef;

$_samplecheck     = ( $_samplecheck == '1' ? '1' : '' );
/** @noinspection PhpUndefinedVariableInspection */
$sdbdefault       = ( $_showdetailbankdef == '1' );
$pcopy            = ( $_pcopy == '1' );
$showEntityChecks = ($_showEntityChecks == '1');
$inclwatermark    = ($_inclwatermark == '1');
$checksperpage    = ($_checksperpage == THREE_CHECKS ) ? THREE_CHECKS : ONE_CHECK;

if (!isset($_ordersortby) || $_ordersortby == 'undefined') {
    $_ordersortby = '';
}
if ( $_samplecheck == '1' ) {
    // Print dummy check
    GetSampleChecksForPDF($mod, $_bankacct, $_showdetails, $checksperpage);
} 
else { 
           $sortByOrder = explode('#~#', $_ordersortby);
           $sortByClause= '';
    foreach ($sortByOrder as $key => $sortByGiven){
        switch ($sortByGiven){
        case 'Entity' : 
            $sortByClause .= 'location.location_no ASC, ' ;
            break;
        case 'Check' :
            // Adding LDAP to fix the check number sorting for AP. We will fix for T&E with other ticket and then
            // remove the if else block
            if($_mod == '6.EE' ) {
                $sortByClause .= 'prrecord.docnumber ASC, ' ;
            }else {
                $sortByClause .= 'lpad(prrecord.docnumber,45,0) ASC, ' ;
            }
            break;
        case 'ENTITYID' :
            if($_mod == '6.EE' ) {
                $sortByClause .= 'employee.employeeid ASC, ' ;
            }else {
                $sortByClause .= 'vendor.VENDORID ASC, ' ;
            }
            break;
        case 'Employee' :
            $sortByClause .= 'contact.name ASC, ' ;
            break;
        case 'Vendor' :
            $sortByClause .= 'vendor.name ASC, ' ;
            break;
        case 'ENTITYNAME' :
            if($_mod == '6.EE' ) {
                $sortByClause .= 'contact.name ASC, ' ;
            }else {
                $sortByClause .= 'vendor.name ASC, ' ;
            }
            break;
        case 'Amount' :
            $sortByClause .= 'prrecord.totalentered ASC, ' ;
            break;
        case 'Date' :
            $sortByClause .= 'prrecord.whencreated ASC, ' ;
            break;
        case 'Bank' :
            $sortByClause .= 'prrecord.financialentity ASC, ' ;
            case 'PAYMENTREQUEST' :
                $sortByClause .= 'prrecord.auwhencreated ASC, ' ;
            break;
        }

    }
    $sortByClause = rtrim($sortByClause, ', ');
    // Print check using regular APIs
    if (!GetChecksForPDF($_mod, $_r, false, $_quick, $_showdetails, $_showdetailedvendstub, $sdbdefault, $pcopy, $showEntityChecks, $sortByClause, $inclwatermark, $checksperpage)) {
        include 'popuperror.phtml';
        exit();
    }
}
    
    PrintChecksToPDF();

