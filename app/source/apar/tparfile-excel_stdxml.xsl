<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <xsl:import href="../../private/xslinc/report_helpers.xsl"/>

    <xsl:include href="../../private/xslinc/stylegroup_inc.xsl"/>
    <xsl:include href="../../private/xslinc/inventoryjs_inc.xsl"/>

    <xsl:template match="/">
        <xsl:apply-templates/>
    </xsl:template>

    <xsl:template match="reportdata">
        <xsl:apply-templates/>
    </xsl:template>

    <xsl:template match="report">
        <report
                department="{@department}"
                location="{@location}"
                report_date="{@reportdate}"
                report_time="{@reporttime}"
        >

            <xsl:if test="(@orientation = 'Portrait')">
                <xsl:attribute name="maxfit">Y</xsl:attribute>
            </xsl:if>
            <company s="2">
                <xsl:value-of select="@co"/>
            </company>
            <title s="3" titleNum="1">
                <xsl:value-of select="@title"/>
            </title>
            <title s="3" titleNum="2">
                <xsl:value-of select="@title2"/>
            </title>
            <title s="4" otherTitle="As of Date">
                <xsl:value-of select="@asofdate"/>
            </title>
            <footer s="5" lines="1" footerNum="1">
                <xsl:value-of select="@titlecomment"/>
            </footer>
            <xsl:for-each select="rtdim">
                <rtdim s="3" name="@name">
                    <name>
                        <xsl:value-of select="@name"/>
                    </name>
                    <value>
                        <xsl:value-of select="@value"/>
                    </value>
                </rtdim>
            </xsl:for-each>

            <header>
                <xsl:if test="number($narrow_format) = 1">
                    <hrow s="header">
                        <hcol width="10" s="txt"/> <!--Payer Australian business number-->
                        <hcol width="10" s="txt"/> <!--Branch number-->
                        <hcol width="10" s="txt"/> <!--Financial year (CCYY)-->
                        <hcol width="10" s="txt"/> <!--Payer name-->
                        <hcol width="10" s="txt"/> <!--Payer trading name-->
                        <hcol width="10" s="txt"/> <!--Payer address line 1-->
                        <hcol width="10" s="txt"/> <!--Payer address line 2-->
                        <hcol width="10" s="txt"/> <!--Payer suburb, town or locality-->
                        <hcol width="10" s="txt"/> <!--Payer state or territory-->
                        <hcol width="10" s="txt"/> <!--Payer postcode-->
                        <hcol width="10" s="txt"/> <!--Payer country-->
                        <hcol width="10" s="txt"/> <!--Payer contact name-->
                        <hcol width="10" s="txt"/> <!--Payer contact telephone number-->
                        <hcol width="10" s="txt"/> <!--Payer contact facsimile number-->
                        <hcol width="10" s="txt"/> <!--Payer contact email address-->
                        <hcol width="10" s="txt"/> <!--Payee Australian business number-->
                        <hcol width="10" s="txt"/> <!--Payee surname or family name-->
                        <hcol width="10" s="txt"/> <!--Payee first given name-->
                        <hcol width="10" s="txt"/> <!--Payee second given name-->
                        <hcol width="10" s="txt"/> <!--Payee business name-->
                        <hcol width="10" s="txt"/> <!--Payee address line 1-->
                        <hcol width="10" s="txt"/> <!--Payee address line 2-->
                        <hcol width="10" s="txt"/> <!--Payee suburb, town or locality-->
                        <hcol width="10" s="txt"/> <!--Payee state or territory-->
                        <hcol width="10" s="txt"/> <!--Payee postcode-->
                        <hcol width="10" s="txt"/> <!--Payee country-->
                        <hcol width="10" s="txt"/> <!--Payee contact telephone number-->
                        <hcol width="10" s="txt"/> <!--Payee financial institution BSB-->
                        <hcol width="10" s="txt"/> <!--Payee financial institution account number-->
                        <hcol width="10" s="txt"/> <!--Gross amount paid-->
                        <hcol width="10" s="txt"/> <!--Total tax withheld-->
                        <hcol width="10" s="txt"/> <!--Total GST-->
                        <hcol width="10" s="txt"/> <!--Payment Type (=G,P)-->
                        <hcol width="10" s="txt"/> <!--Date of grant payment (DDMMCCYY)-->
                        <hcol width="10" s="txt"/> <!--Name of grant or grant program-->
                        <hcol width="10" s="txt"/> <!--Email address-->
                        <hcol width="10" s="txt"/> <!--Statement by a Supplier (=Y,N)-->
                    </hrow>
                </xsl:if>
                <hrow s="51">
                    <hcol id="0" s="17">Payer Australian business number</hcol>
                    <hcol id="0" s="17">Branch number</hcol>
                    <hcol id="0" s="17">Financial year (CCYY)</hcol>
                    <hcol id="0" s="17">Payer name</hcol>
                    <hcol id="0" s="17">Payer trading name</hcol>
                    <hcol id="0" s="17">Payer address line 1</hcol>
                    <hcol id="0" s="17">Payer address line 2</hcol>
                    <hcol id="0" s="17">Payer suburb, town or locality</hcol>
                    <hcol id="0" s="17">Payer state or territory</hcol>
                    <hcol id="0" s="17">Payer postcode</hcol>
                    <hcol id="0" s="17">Payer country</hcol>
                    <hcol id="0" s="17">Payer contact name</hcol>
                    <hcol id="0" s="17">Payer contact telephone number</hcol>
                    <hcol id="0" s="17">Payer contact facsimile number</hcol>
                    <hcol id="0" s="17">Payer contact email address</hcol>
                    <hcol id="0" s="17">Payee Australian business number</hcol>
                    <hcol id="0" s="17">Payee surname or family name</hcol>
                    <hcol id="0" s="17">Payee first given name</hcol>
                    <hcol id="0" s="17">Payee second given name</hcol>
                    <hcol id="0" s="17">Payee business name</hcol>
                    <hcol id="0" s="17">Payee address line 1</hcol>
                    <hcol id="0" s="17">Payee address line 2</hcol>
                    <hcol id="0" s="17">Payee suburb, town or locality</hcol>
                    <hcol id="0" s="17">Payee state or territory</hcol>
                    <hcol id="0" s="17">Payee postcode</hcol>
                    <hcol id="0" s="17">Payee country</hcol>
                    <hcol id="0" s="17">Payee contact telephone number</hcol>
                    <hcol id="0" s="17">Payee financial institution BSB</hcol>
                    <hcol id="0" s="17">Payee financial institution account number</hcol>
                    <hcol id="0" s="17">Gross amount paid</hcol>
                    <hcol id="0" s="17">Total tax withheld</hcol>
                    <hcol id="0" s="17">Total GST</hcol>
                    <hcol id="0" s="17">Payment Type (=G,P)</hcol>
                    <hcol id="0" s="17">Date of grant payment (DDMMCCYY)</hcol>
                    <hcol id="0" s="17">Name of grant or grant program</hcol>
                    <hcol id="0" s="17">Email address</hcol>
                    <hcol id="0" s="17">Statement by a Supplier (=Y,N)</hcol>
                </hrow>
                <xsl:if test="number($narrow_format) = 1">
                    <hrow s="14">
                        <hcol id="0" s="19" colspan="18"></hcol>
                    </hrow>
                </xsl:if>
            </header>

            <body s="body">
                <xsl:if test="number($narrow_format) = 1">
                    <row s="12">
                        <col s="19" colspan="{/reportdata/report/@noofcolumns}"></col>
                    </row>
                </xsl:if>
                <xsl:apply-templates/>
            </body>
            <xsl:call-template name="stylegroups"/>
            <script language="javascript">
                <xsl:apply-templates select="@javascript"/>
                <xsl:call-template name="script"/>
            </script>
        </report>
    </xsl:template>

    <xsl:template match="NODATA">
        <xsl:if test="string(@NODATA)=1">
            <row s="14">
                <col id="0" s="21" colspan="20">IA.NO_DATA_FOUND</col>
            </row>
        </xsl:if>
    </xsl:template>

    <xsl:template match="ITEMS">
        <row s="12">
            <col id="0" s="24">
                <xsl:value-of select="@ENTITY_TAXID"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@LEGALBRANCHNUMBER"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@YEAR"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@ENTITY_NAME"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@LEGALNAME"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@LEGALADDRESS1"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@LEGALADDRESS2"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@LEGALCITY"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@LEGALSTATE"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@LEGALZIPCODE"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@LEGALCOUNTRY"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@LEGALCONTACTNAME"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@LEGALCONTACTPHONE"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@LEGALCONTACTFAX"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@LEGALCONTACTEMAIL"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@TAXID"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@VENDOR_LASTNAME"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@VENDOR_FIRSTNAME"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@VENDOR_MIDDLENAME"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@VENDOR_NAMETPAR"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@PAYEE_ADDRESS1"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@PAYEE_ADDRESS2"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@PAYEE_CITY"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@PAYEE_STATE"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@PAYEE_POSTCODE"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@PAYEE_COUNTRY"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@PAYEE_PHONE1"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@BSBNUMBER"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@BANKACCOUNTNUMBER"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@GROSS_PAID"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@TOTALTAXWITHHELD"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@TOTAL_GST"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@PAYMENTTYPE"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@DATEOFGRANTPAYMENT"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@NAMEOFGRANT"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@PAYEE_EMAIL1"/>
            </col>
            <col id="0" s="24">
                <xsl:value-of select="@STATEMENTBYSUPPLIER"/>
            </col>
        </row>
    </xsl:template>

</xsl:stylesheet>
