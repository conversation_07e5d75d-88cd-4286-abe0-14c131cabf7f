<?php
 /**
 * File ARDetailGetAllReportTypeBatch.cls contains the class ARDetailGetAllReportTypeBatch
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2013 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
 

class DetailGetAllReportTypeBatch extends GetAllBatch
{
    /**
     * @param string $additionalWhere
     * @return array
     */
    public function getMinMax( $additionalWhere = "" )
    {
        $recordTable = Util::getMstForTable('prrecord');
        $detailTable = Util::getMstForTable('prentry');
        $params = $this->getParams();

        if ( $params == null || !isset($params['recordtypes']) || count($params['recordtypes']) == 0 ) {
            if ( DDS_DEBUG == 1 ) {
                impp(get_class($this). " needs to know recordtypes specified in the batch.", pp($params));
            }

            throw new Exception(get_class($this). " needs to know about recordtypes specified in the batch.");
        }

        $additionalWhere = " AND ".$recordTable.".recordtype IN ('". implode("','", $params['recordtypes']). "')";
        $prrecordJoin = " ".$detailTable.".cny# = :1 AND ".$recordTable.".cny# = :1 AND ".$detailTable.".recordkey = ".$recordTable.".record# ";
        $asofQry = " AS OF TIMESTAMP TO_TIMESTAMP('". $this->getReadTimeLocalTZ() ."', 'MM/DD/YYYY HH24:MI:SS')";
        $fromQry = " FROM ".$detailTable." $asofQry, ".$recordTable." $asofQry";

        // Record min record#
        $minQry = "SELECT MIN(".$detailTable.".record#) minrec $fromQry WHERE ". $prrecordJoin . $additionalWhere;
        $minRes = QueryResult(array($minQry, GetMyCompany()));

        if ( !isset($minRes[0]['MINREC']) ) {
            return null;
        }

        $arr = array();
        $arr['MINREC'] = intval($minRes[0]['MINREC']);

        // Record max record#
        $maxQry = "SELECT MAX(".$detailTable.".record#) maxrec $fromQry WHERE ". $prrecordJoin . $additionalWhere;
        $maxRes = QueryResult(array($maxQry, GetMyCompany()));

        if ( !isset($maxRes[0]['MAXREC']) ) {
            return null;
        }

        $arr['MAXREC'] = intval($maxRes[0]['MAXREC']);

        return $arr;
    }
}