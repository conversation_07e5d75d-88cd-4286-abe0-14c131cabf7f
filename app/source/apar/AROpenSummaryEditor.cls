<?php
/**
 * AROpenSummaryEditor.cls
 * <AUTHOR>
 * @copyright 2013 Intacct Corporation All, Rights Reserved
 */


class AROpenSummaryEditor extends SubledgerSummaryEditor
{
    /**
     * AROpenSummaryEditor constructor.
     *
     * @param array $_params
     */
    public function __construct($_params)
    {
        parent::__construct($_params);
    }

    /**
     * get the button name
     *
     * @return string
     */
    protected function getButtonName()
    {
        return 'IA.OPEN';
    }

    /**
     * @return string
     */
    protected function getConfirmationMsgForSummary()
    {
        return GT($this->textMap,'IA.AR_SUMMARIES_OPENED_MESSAGE');
    }

    /**
     * Implementing method to populate the entity manager for AR Open Summary detail processor.
     */
    protected function populateEntityManager()
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $this->entityManager = $gManagerFactory->getManager('aropensummary');
    }
}
