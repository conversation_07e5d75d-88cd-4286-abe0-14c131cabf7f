<?
class VendorPicker extends NPicker
{
    function __construct()
    {
        parent::__construct(
            array(
            'entity'        =>  'vendor',
            'pickfield'        =>  'VENDORID',
            'fields'        =>  array('VENDORID','CONTACTINFO.CONTACTNAME','NAME'),
            'nonencodedfields'  => [],
                'pickcount'     => 50
            )
        );
    }

    /**
     * Add Javascript and CSS to the page.  Subclasses really should call back to their parent.
     *
     * @param bool $addYuiCss include the YUI css files
     */
    function showScripts($addYuiCss = true)
    {
        $_refresh = Request::$r->_refresh;
        parent::showScripts($addYuiCss);
        ?>
        <? UIUtils::PrintLayerSetupForBrowser(); ?>
        <? UIUtils::PrintSetField($_refresh); ?>
        <?
    }
}

