<?
$obj = array(
    'filter' => "prrecord.recordtype = 'ri' and prrecord.systemgenerated = 'T'",
    'addlabel' => 'Accounts Receivable Quick Deposit',
    'fieldlabels' => array(
        'Customer', 'Document#', 'Date', 'Amount', 'Summary Title'
    ),
    'qryfields' => array(
        'prrecord.record#',
        'customer.name',
        'prrecord.recordid',
        'prrecord.whencreated',
        'prrecord.currency',
        'prrecord.trx_totalentered',
        'prrecord.basecurr',
        'prrecord.totalentered',
        'prbatch.title',
        'prrecord.prbatchkey',
        'prrecord.trx_totalpaid',
        'prrecord.trx_totalselected',
        'prrecord.trx_totaldue'
    ),
    'tblfields' => array(
        'NAME', 'RECORDID', 'WHENCREATED', 'TOTALENTERED', 'TITLE'
    ),
    'fullnames' => array(
        'CUSTOMER.NAME', 'RECORDID', 'PRRECORD.WHENCREATED', 'TOTALENTERED', 'PRBATCH.TITLE'
    ),
    'groomtype' => array(
        'CHAR', 'CHAR', 'DATE', 'MONEY', 'CHAR'
    ),
    'disabledelete' => true,
    'format' => array(
        'TOTALENTERED' => array(
            'calign' =>'right',
            'cwidth' => '1%'
        ),
    ),
    'hlpfile' => 'Viewing_and_Managing_Quick_Deposits',
    'sortcol' => 'WHENCREATED:d',
    'detail' => "prrecord.phtml?.recordtype=ri&.quick=1&.op=" . GetOperationId('ar/lists/arquickdeposit'),
    'editurl' => FwdUrl(
        "edit_invoice.phtml?.recordtype=ri&.quick=1&.op=" .
        GetOperationId('ar/lists/arquickdeposit/view'), $embeddedDone
    ),
    'addurl' => FwdUrl("editor.phtml?&.op=" . GetOperationId('ar/lists/arquickdeposit/create'), $embeddedDone)
);

if (IsMCPSubscribed()) {
    $obj['fieldlabels'] = array('Customer', 'Document#', 'Date', 'Currency', 'Amount', 'Summary Title');
    $obj['tblfields'] = array('NAME', 'RECORDID', 'WHENCREATED', 'CURRENCY', 'TRX_TOTALENTERED', 'TITLE');
    $obj['fullnames'] = array('CUSTOMER.NAME', 'RECORDID', 'PRRECORD.WHENCREATED', 'PRRECORD.CURRENCY', 'TRX_TOTALENTERED', 'PRBATCH.TITLE');
    $obj['groomtype'] = array('CHAR', 'CHAR', 'DATE', 'CHAR', 'MONEY', 'CHAR');
}


