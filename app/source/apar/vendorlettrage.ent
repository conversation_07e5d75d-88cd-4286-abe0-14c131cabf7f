<?

/**
 *    FILE: vendorlettrage.ent
 *
 * <AUTHOR> <ravi.shankar<PERSON><PERSON><PERSON><PERSON>@sage.com>
 * @copyright 2023 Intacct Corporation, All Rights Reserved
 *
 */

global $gWhenCreatedFieldInfo, $gWhenModifiedFieldInfo, $gCreatedByFieldInfo;
global $gModifiedByFieldInfo, $gRecordNoFieldInfo, $gRecordNoFormat;

$kSchemas['vendorlettrage'] = array(
    'object' => array(
        'RECORDNO','PRRECORDKEY', 'LETTRAGECODE', 'DISPLAYLETTRAGECODE'),
    'schema' => array(
        'RECORDNO'      => 'record#',
        'PRRECORDKEY'   => 'prrecordkey',
        'LETTRAGECODE'  => 'lettragecode',
        'DISPLAYLETTRAGECODE'  => 'displaylettragecode',
    ),

    'fieldinfo' => array(
        $gRecordNoFieldInfo,
        array (
            'path'         => 'PRRECORDKEY',
            'fullname'    => 'IA.PRRECORDNO',
            'desc'         => 'IA.IA.PRRECORDNO',
            'required'    => true,
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'id'        => 1,
        ),
        array (
            'path'         => 'LETTRAGECODE',
            'fullname'    => 'IA.LETTRAGECODE',
            'desc'         => 'IA.LETTRAGECODE',
            'type' => array(
                'type' => 'text',
                'ptype' => 'textarea',
                'maxlength' => 20
            ),
            'id'        => 2,
        ),
        array (
            'path'         => 'DISPLAYLETTRAGECODE',
            'fullname'    => 'IA.LETTRAGECODE',
            'desc'         => 'IA.LETTRAGECODE',
            'type' => array(
                'type' => 'text',
                'ptype' => 'textarea',
                'maxlength' => 20
            ),
            'id'        => 3,
        ),
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
    ),

    'table'     => 'lettragemap',
    'vid'         => 'RECORDNO',
    'printas'    =>    'IA.VENDOR_LETTRAGE_REPORT',
    'pluralprintas' => 'IA.VENDOR_LETTRAGE_REPORTS',
    'module'     => 'ar',
    'nochatter' => true,
    'bulkoperation' => true,
    'auditcolumns' => true,
    'platformProperties' => array(
        // PRR needs this setting. If you intend to change it, please confirm it first with someone from the Platform
        // team
        SOBJECT_CAN_HAVE_RELATIONSHIPS => false,
    ),

    // TODO: custom fields ,smart event /rule should t be there
    'customerp'  => array(
        'SLTypes' => array(CUSTOMERP_SMARTLINKFETCH,
                           CUSTOMERP_SMARTLINKCLICK,
                           CUSTOMERP_SMARTLINKVALIDATE,
                           CUSTOMERP_SMARTLINKCALCULATE,
                           CUSTOMERP_SMARTLINKWORKFLOW),
        'SLEvents' => array(CUSTOMERP_EVENT_ADD,
                            CUSTOMERP_EVENT_SET,
                            CUSTOMERP_EVENT_ADDSET,
                            CUSTOMERP_EVENT_DELETE,
                            CUSTOMERP_EVENT_CLICK),
        'AllowCF' => false),
    'description' => 'IA.VENDOR_LETTRAGE_DESC',
);



