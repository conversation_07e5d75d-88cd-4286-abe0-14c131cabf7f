<?php
/**
 *    FILE:
 *    AUTHOR: <PERSON><PERSON>
 *    DESCRIPTION:
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
*/
require 'appayment.ent'; 

$kSchemas['appostedpayment'] = $kSchemas['appayment']; 

$kSchemas['appostedpayment']['dbfilters'] = array (
    array ('appostedpayment.recordtype', 'IN', array(PRRECORD_TYPE_PAYMENT, PRRECORD_TYPE_APADVANCE)), 
    array('appostedpayment.state', 'IN', array(PRRECORD_STATE_PCONFIRMED, PRRECORD_STATE_DVOIDED))
);

//overriding to use vendormst table and removing outer joins from vendormst and iapaymethod
$kSchemas['appostedpayment']['children']['vendor'] = array ('table' => 'vendormst', 'fkey' => 'entity', 'invfkey' => 'entity','join' => 'inner');
$kSchemas['appostedpayment']['children']['iapaymethod'] = array ('table' => 'iapaymethod', 'fkey' => 'paymethodkey', 'invkey' => 'record#', 'join' => 'inner');
$kSchemas['appostedpayment']['fieldinfo'] = INTACCTarray_merge(
    array(
        array(
            'fullname' => 'IA.PAYMENT_TXN_AMOUNT',
            'type' => array(
                'ptype' => 'currency',
                'type' => 'currency',
                'format' => $gCurrencyFormat,
            ),
            'desc' => 'IA.PAYMENT_AMOUNT',
            'path' => 'PAYMENTTRXAMOUNT',
            'renameable' => true,
        ),
    ),
    array(
        array(
            'fullname' =>  'IA.PAYMENT_STATUS',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'text',
                'validvalues' => array('IA.COMPLETE', 'IA.VOIDED'),
                '_validivalues' => array ('C', 'V'),
                'validlabels' => array('IA.COMPLETE', 'IA.VOIDED'),
                'format' => '/.{0,100}/'
            ),
            'desc' => "IA.STATE_OF_PAYMENT",
            'path' => 'STATE',
            'renameable' => true,
        ),
    ),
    $kSchemas['appostedpayment']['fieldinfo']
);

// ARE WE FILTERING ON A PARTICULAR BATCH?
$batchkey = Request::$r->_batch;
if ($batchkey) {
    $kSchemas['appostedpayment']['dbfilters'][] = array('appostedpayment.prbatchkey', '=', $batchkey);
}

$kSchemas['payment'] = EntityManager::inheritEnts($kSchemas['prrecord'], $kSchemas['payment']);


//the appayment and prrecord we inherited doesn't define the metadata for this field.
$kSchemas['appostedpayment']['fieldinfo'][] =
    array (
            'fullname' => 'IA.RECOMMENDED_PAYMENT_DATE',
            'desc' => 'IA.RECOMMENDED_PAYMENT_DATE',
            'type' => $gDateType,
            'required' => false,
            'path' => 'RECPAYMENTDATE',
            'partialedit' => true
           );
$kSchemas['appostedpayment']['fieldinfo'][] = $gSiUuidFieldInfo;
unset($kSchemas['appostedpayment']['ownedobjects']);

// This ownedobject is used only by Global Search. In other cases, it will be unset.
$kSchemas['appostedpayment']['ownedobjects'][] = [
    'fkey'             => 'RECORDKEY',        // the field that the owned object point to
    'invfkey'          => 'RECORDNO',
    'minLinesRequired' => 1,
    'entity'           => 'appostedpaymententry',    // to the parent vid
    'path'             => 'PRENTRY'
];

$kSchemas['appostedpayment']['children']['jointpayee'] = array ('table' => 'apjointpayeemst', 'fkey' => 'record#', 'invfkey' => 'prrecordkey','join' => 'outer');

$kSchemas['appostedpayment']['object'][] = 'JOINTPAYEENAME';
$kSchemas['appostedpayment']['object'][] = 'JOINTPAYEEPRINTAS';

$kSchemas['appostedpayment']['schema']['JOINTPAYEENAME'] = 'jointpayee.jointpayeename';
$kSchemas['appostedpayment']['schema']['JOINTPAYEEPRINTAS'] = 'jointpayee.jointpayeeprintas';
$kSchemas['appostedpayment']['schema']['SI_UUID'] = 'si_uuid';

$kSchemas['appostedpayment']['fieldinfo'][] =
    [
        'path'        => 'JOINTPAYEENAME',
        'desc'        => 'IA.JOINT_PAYEE',
        'fullname'    => 'IA.JOINT_PAYEE',
        'required'    => false,
        'type'        => [
            'ptype'     => 'text',
            'type'      => 'text',
            'maxlength' => 200,
            'format'    => $gJointPayeeNameFormat,
        ],
        'readonly' => true,
    ];
$kSchemas['appostedpayment']['fieldinfo'][] =
    [
        'path'        => 'JOINTPAYEEPRINTAS',
        'desc'        => 'IA.JOINT_PAYEE_PRINT_AS',
        'fullname'    => 'IA.JOINT_PAYEE_PRINT_AS',
        'required'    => false,
        'type'        => [
            'ptype'     => 'text',
            'type'      => 'text',
            'maxlength' => 148,
            'format'    => $gJointPayeePrintAsFormat,
        ],
        'readonly' => true,
    ];


$kSchemas['appostedpayment']['api'] = [
    'PERMISSION_MODULES' => array('ap'),
    'READ_AUDITTRAIL' => 'lists/appostedpayment/view'
];


$kSchemas['appostedpayment'][SearchTable::GSCOLUMNS] = [  'VENDORNAME', 'DOCUMENTNUMBER', 'PAYMENTAMOUNT',
                                               'PAYMENTDATE', 'MEMO', 'STATUS', ];
$kSchemas['appostedpayment'][SearchTable::GSALLOWED] = true;
$kSchemas['appostedpayment'][SearchTable::GSDEFAULTFILTERFIELD] = 'DOCUMENTNUMBER';
$kSchemas['appostedpayment'][SearchTable::GSANYFIELD] = 'RECORDID';
$kSchemas['appostedpayment'][SearchTable::GSMULTIENTITYCLAUSE] = PRRECORD_ME_CLAUSE;
$kSchemas['appostedpayment']['sicollaboration'] = true;
