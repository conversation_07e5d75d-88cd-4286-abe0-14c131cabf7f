<?
/**
 *    FILE:                revrectemplate.ent
 *    AUTHOR:        <PERSON><PERSON><PERSON>
 *    DESCRIPTION:
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */

$kSchemas['revrectemplate'] = array(
    'object' => array(
        'RECORDNO',
        'TEMPLATEID',
        'DESCRIPTION',
        'USESTANDARD',
        'SCHEDULEPERIOD',
        'POSTINGDAY',
        'TOTALPERIODS',
        'RECMETHOD',
        'RECSTARTDATE',
        'POSTINGMETHOD',
        'STATUS',
        'LATESTVERSIONKEY',
        'RECOGNITIONTERM',
        'RESUMEOPTION',
        'MILESTONESOURCE',
        'PACALCSOURCE',
        'PACALCHOURS',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ),
    'schema' => array(
        'RECORDNO' => 'record#',
        'TEMPLATEID' => 'templateid',
        'DESCRIPTION' => 'description',
        'USESTANDARD' => 'usestandard',
        'SCHEDULEPERIOD' => 'scheduleperiod',
        'POSTINGDAY' => 'postingday',
        'TOTALPERIODS' => 'totalperiods',
        'RECMETHOD' => 'recmethod',
        'RECSTARTDATE' => 'recstartdate',
        'POSTINGMETHOD' => 'postingmethod',
        'STATUS' => 'status',
        'LATESTVERSIONKEY' => 'latestversionkey',
        'RECOGNITIONTERM' => 'recognitionterm',
        'RESUMEOPTION' => 'resumeoption',
        'MILESTONESOURCE' => 'milestonesource',
        'PACALCSOURCE' => 'pacalcsource',
        'PACALCHOURS' => 'pacalchours',
        'WHENCREATED' => 'whencreated',
        'WHENMODIFIED' => 'whenmodified',
        'CREATEDBY' => 'createdby',
        'MODIFIEDBY' => 'modifiedby',
    ),
    'ownedobjects' => array(
        array(
            'fkey' => 'TEMPLATEKEY',
            // the field that the owned object point to
            'invfkey' => 'RECORDNO',
            'entity' => 'revrectemplentry',
            // to the parent vid
            'path' => 'REVRECTEMPLENTRY'
        ),
        array(
            'fkey' => 'TEMPLATEKEY',
            // the field that the owned object point to
            'invfkey' => 'RECORDNO',
            'entity' => 'revrectemplmilestone',
            // to the parent vid
            'path' => 'REVRECTEMPLMILESTONE'
        ),
    ),
    'publish' => array(
        'TEMPLATEID',
        'DESCRIPTION',
        'USESTANDARD',
        'SCHEDULEPERIOD',
        'POSTINGDAY',
        'TOTALPERIODS',
        'RECMETHOD',
        'RECSTARTDATE',
        'POSTINGMETHOD',
        'STATUS',
        'MILESTONESOURCE',
        'PACALCSOURCE',
        'PACALCHOURS',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENCREATED',
        'WHENMODIFIED',
    ),
    'fieldinfo' => array(
        array(
            'path' => 'RECORDNO',
            'desc' => 'IA.RECORD_NUMBER',
            'fullname' => 'IA.RECORD_NUMBER',
            'hidden' => true,
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'size' => 8,
                'format' => $gRecordNoFormat,
            ),
            'id' => 1
        ),
        array(
            'path' => 'TEMPLATEID',
            'fullname' => 'IA.TEMPLATE_ID',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 30,
                'size' => 45,
                'format' => $gTemplateIDFormat
            ),
            'required' => true,
            'desc' => 'IA.TEMPLATE_ID',
            'id' => 2
        ),
        array(
            'path' => 'DESCRIPTION',
            'fullname' => 'IA.TEMPLATE_DESCRIPTION',
            'type' => array(
                'ptype' => 'textarea',
                'type' => 'text',
                'maxlength' => 100,
            ),
            'numofrows' => 3,
            'numofcols' => 40,
            'required' => true,
            'desc' => 'IA.NAME',
            'id' => 3
        ),
        array(
            'path' => 'USESTANDARD',
            'fullname' => 'IA.USE_STANDARD_CALENDAR_AMORTIZATION',
            'type' => $gBooleanType,
            'default' => 'false',
            'desc' => 'IA.OVERWRITE_USING_STANDARD_CALENDAR_FOR',
            'hidden' => true,
            'id' => 4
        ),
        array(
            'path' => 'SCHEDULEPERIOD',
            'fullname' => 'IA.RECOGNITION_SCHEDULE_PERIOD',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'text',
                'validlabels' => array( 'IA.DAILY', 'IA.MONTHLY', 'IA.QUARTERLY', 'IA.SEMI_ANNUALLY', 'IA.ANNUALLY' ),
                'validvalues' => array(
                    'Daily',
                    'Monthly',
                    'Quarterly',
                    'Semi-annually',
                    'Annually'
                ),
                '_validivalues' => array(
                    'D',
                    'M',
                    'Q',
                    'S',
                    'A'
                ),
            ),
            'desc' => 'IA.SCHEDULE_PERIOD',
            'onchange' => 'handleSchedulePeriod(this.value);',
            'id' => 5
        ),
        array(
            'path' => 'POSTINGDAY',
            'fullname' => 'IA.POSTING_DAY',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'text',
                'validlabels' => array( '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', 'IA.END_OF_PERIOD', 'IA.DAILY' ),
                'validvalues' => array(
                    '1',
                    '2',
                    '3',
                    '4',
                    '5',
                    '6',
                    '7',
                    '8',
                    '9',
                    '10',
                    '11',
                    '12',
                    '13',
                    '14',
                    '15',
                    '16',
                    '17',
                    '18',
                    '19',
                    '20',
                    '21',
                    '22',
                    '23',
                    '24',
                    '25',
                    '26',
                    '27',
                    '28',
                    '29',
                    '30',
                    '31',
                    'End Of Period',
                    'Daily'
                ),
                '_validivalues' => array(
                    '1',
                    '2',
                    '3',
                    '4',
                    '5',
                    '6',
                    '7',
                    '8',
                    '9',
                    '10',
                    '11',
                    '12',
                    '13',
                    '14',
                    '15',
                    '16',
                    '17',
                    '18',
                    '19',
                    '20',
                    '21',
                    '22',
                    '23',
                    '24',
                    '25',
                    '26',
                    '27',
                    '28',
                    '29',
                    '30',
                    '31',
                    '32',
                    '33'
                ),
                'maxlength' => 2,
            ),
            'desc' => 'IA.POSTING_DAY',
            'id' => 6
        ),
        array(
            'path' => 'RECOGNITIONTERM',
            'fullname' => 'IA.RECOGNITION_TERM',
            'type' => array(
                'ptype' => 'radio',
                'type' => 'text',
                'validlabels' => array( 'IA.FIXED_PERIOD', 'IA.CONTRACT_TERM', 'IA.PROJECT' ),
                'validvalues' => array(
                    'Fixed Period',
                    'Contract Term',
                    'Project'
                ),
                '_validivalues' => array(
                    'F',
                    'C',
                    'P'
                ),
            ),
            'layout' => 'landscape',
            'required' => false,
            'default' => 'Fixed Period',
            'desc' => 'IA.RECOGNITION_TERM',
            'onchange_js' => "javascript:toggleNoOfPeriod(this.value);",
            'id' => 7
        ),
        array(
            'path' => 'RESUMEOPTION',
            'fullname' => 'IA.SYSTEM_RESUME_OPTION',
            'type' => array(
                'ptype' => 'radio',
                'type' => 'text',
                'validlabels' => array( 'IA.CATCH_UP', 'IA.WALKFORWARD' ),
                'validvalues' => array(
                    'Catch Up',
                    'Walkforward'
                ),
                '_validivalues' => array(
                    'C',
                    'F'
                ),
            ),
            'layout' => 'landscape',
            'default' => 'Walkforward',
            'desc' => 'IA.RESUME_OPTION_FOR_VSOE',
            'hidden' => true,
            'id' => 8
        ),
        array(
            'path' => 'TOTALPERIODS',
            'fullname' => 'IA.NUMBER_OF_PERIODS',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
                'maxlength' => 4,
                'size' => 8,
                'format' => $gRecordNoFormat,
            ),
            //'required' => true,
            'desc' => 'IA.TOTAL_PERIODS',
            'id' => 9
        ),
        array(
            'path' => 'RECMETHOD',
            'fullname' => 'IA.RECOGNITION_METHOD',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'text',
                'validlabels' => array( 'IA.STRAIGHT_LINE_1', 'IA.STRAIGHT_LINE_PRORATE_EXACT_DAYS', 'IA.STRAIGHT_LINE_PERCENT_ALLOCATION', 'IA.STRAIGHT_LINE_PERCENT_ALLOCATION_END_OF_PERIOD', 'IA.EXACT_DAYS_PER_PERIOD_PRORATE_DAYS', 'IA.EXACT_DAYS_PER_PERIOD_PRORATE_DAYS_END_OF', 'IA.PERCENT_COMPLETED', 'IA.MILESTONE', 'IA.CUSTOM' ),
                'validvalues' => array(
                    'Straight-line',
                    'Straight-line, prorate exact days',
                    'Straight-line, percent allocation',
                    'Straight-line, percent allocation, end of period',
                    'Exact days per period, prorate days',
                    'Exact days per period, prorate days, end of period',
                    'Percent Completed',
                    'Milestone',
                    'Custom'
                ),
                '_validivalues' => array(
                    'E',
                    'S',
                    'P',
                    'F',
                    'X',
                    'D',
                    'T',
                    'M',
                    'C'
                ),
            ),
            'desc' => 'IA.REC_METHOD',
            'onchange' => 'handleRecMethod(this.value);',
            'id' => 10
        ),
        array(
            'path' => 'RECSTARTDATE',
            'fullname' => 'IA.RECOGNITION_START_DATE',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'text',
                'validlabels' => array( 'IA.TRANSACTION_DATE', 'IA.USER_SPECIFIED' ),
                'validvalues' => array(
                    'Transaction Date',
                    'User Specified'
                ),
                '_validivalues' => array(
                    'I',
                    'C'
                ),
            ),
            'desc' => 'IA.REC_START_DATE',
            'id' => 11
        ),
        array(
            'path' => 'POSTINGMETHOD',
            'fullname' => 'IA.POSTING_METHOD',
            'type' => array(
                'ptype' => 'radio',
                'type' => 'text',
                'validlabels' => array( 'IA.AUTOMATIC', 'IA.MANUAL' ),
                'validvalues' => array(
                    'Automatic',
                    'Manual'
                ),
                '_validivalues' => array(
                    'A',
                    'M'
                ),
            ),
            'default' => 'Manual',
            'layout' => 'landscape',
            'required' => false,
            'desc' => 'IA.POSTING_METHOD',
            'id' => 12
        ),
        $gStatusFieldInfo,
        array(
            'path' => 'LATESTVERSIONKEY',
            'desc' => 'IA.LATEST_VERSION_KEY',
            'fullname' => 'IA.LATEST_VERSION_KEY',
            'hidden' => true,
            'type' => array(
                'type' => 'decimal',
                'maxlength' => 10,
            ),
            'id' => 13
        ),
        array(
            'path' => 'MILESTONESOURCE',
            'fullname' => 'IA.PERCENT_OR_MILESTONE_SOURCE',
            'type' => array(
                'ptype' => 'radio',
                'type' => 'text',
                'validlabels' => array( 'IA.USER_SPECIFIED', 'IA.PROJECT_ACCOUNTING' ),
                'validvalues' => array(
                    'Manual',
                    'Project'
                ),
                '_validivalues' => array(
                    'manual',
                    'project'
                ),
            ),
            'hidden' => true,
            'layout' => 'landscape',
            'required' => false,
            'default' => 'Manual',
            'desc' => 'IA.MILESTONE_SOURCE',
            'onchange_js' => 'handleMilestoneSourceChanged();',
            'id' => 14
        ),
        array(
            'path' => 'PACALCSOURCE',
            'fullname' => 'IA.CALCULATE_ON',
            'desc' => 'IA.CALCULATE_ON',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'text',
                'validlabels' => array( 'IA.PROJECT', 'IA.TASK' ),
                'validvalues' => array(
                    'Project',
                    'Task'
                ),
                '_validivalues' => array(
                    'project',
                    'task'
                ),
            ),
            'hidden' => true,
            'required' => false,
            'default' => 'Project',
            'onchange' => 'handlePASourceChanged();',
            'id' => 15
        ),
        array(
            'path' => 'PACALCHOURS',
            'fullname' => 'IA.BASED_ON',
            'desc' => 'IA.BASED_ON',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'text',
                'validlabels' => array( 'IA.ESTIMATED_HOURS', 'IA.PLANNED_HOURS', 'IA.BUDGETED_HOURS', 'IA.BUDGETED_COST_FROM_GL', 'IA.BUDGETED_COST_FROM_SUMMARY', 'IA.OBSERVED_PERCENT_COMPLETED' ),
                'validvalues' => array(
                    'Estimated Hours',
                    'Planned Hours',
                    'Budgeted Hours',
                    'Budgeted Cost from GL',
                    'Budgeted Cost from Summary',
                    'Observed % Completed'
                ),
                '_validivalues' => array(
                    'estimated',
                    'planned',
                    'budgeted',
                    'glcost',
                    'cost',
                    'observed'
                ),
            ),
            'hidden' => true,
            'required' => false,
            'default' => 'Estimated Hours',
            'onchange' => 'handlePACalcHoursChanged();',
            'id' => 16
        ),
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo
    ),
    'table' => 'revrectemplate',
    'module' => 'ar',
    'dbfilters' => array(
        array(
            'revrectemplate.latestversionkey',
            'ISNULL'
        )
    ),
    // IF SOMEONE FIXES THIS VID, IT NEEDS TO FIX getEntityData IN THE RevRecTemplateEditor NOT TO DO THE TRANSLATION FROM TEMPLATEID TO RECORDNO!
    'vid' => 'RECORDNO',
    'autoincrement' => 'RECORDNO',
    'renameable' => true,
    'nochatter' => true,
    'printas' => 'IA.REVENUE_RECOGNITION_TEMPLATE',
    'pluralprintas' => 'IA.REVENUE_RECOGNITION_TEMPLATES',
    'customerp' => array(
        'SLTypes' => array(
            CUSTOMERP_SMARTLINKVALIDATE,
            CUSTOMERP_SMARTLINKWORKFLOW
        ),
    ),
    'allowDDS' => true,
    'description' => 'IA.INFORMATION_FOR_ORDER_ENTRY_REVENUE_RECOGNITION_TE',
);