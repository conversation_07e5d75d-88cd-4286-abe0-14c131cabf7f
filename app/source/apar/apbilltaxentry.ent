<?php

/**
 * Entity for apbill tax entry.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2024 Sage Intacct Corporation All, Rights Reserved
 */
$kSchemas['apbilltaxentry'] = array(
    'children' => array(
        'prrecord' => array(
            'fkey' => 'recordkey', 'invfkey' => 'record#', 'table' => 'prrecordmst'
        ),
        'prentry' => array(
            'fkey' => 'parententry', 'invfkey' => 'record#', 'table' => 'prentrymst'
        ),
        'detail' => array(
            'fkey' => 'taxdetail#', 'invfkey' => 'record#', 'table' => 'taxdetailmst', 'join' => 'outer',
        ),
    ),
    'object' => array(
        'RECORDNO',
        'PARENTENTRY',
        'TAX',
        'TRX_TAX',
        'TAXRATE',
        'DETAILID',
        'DETAILKEY',
        'TOTALPAID',
        'TRX_TOTALPAID',
        'TO<PERSON>LSELECTED',
        'TRX_TOTALSELECTED',
    ),
    'schema' => array(
        'RECORDNO' => 'record#',
        'PARENTENTRY' => 'parententry',
        'TAX' => 'amount',
        'TRX_TAX' => 'trx_amount',
        'TAXRATE' => 'detail.value',
        'DETAILID' => 'detail.detailid',
        'DETAILKEY' => 'taxdetail#',
        'TOTALPAID' => 'totalpaid',
        'TRX_TOTALPAID' => 'trx_totalpaid',
        'TOTALSELECTED' => 'totalselected',
        'TRX_TOTALSELECTED' => 'trx_totalselected',
    ),
    'fieldinfo' => array(
        $gRecordNoFieldInfo,
        array(
            'path' => 'TAX',
            'fullname' => 'IA.BASE_TAX',
            'desc' => 'IA.BASE_TAX_AMOUNT',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'format' => $gDecimalFormat,
                'size' => 22,
                'maxlength' => 15,
            ),
            'hasTotal' => true,
            'readonly' => true,
            'id' => 71
        ),
        array(
            'path' => 'TRX_TAX',
            'fullname' => 'IA.TRANSACTION_TAX',
            'desc' => 'IA.TRANSACTION_TAX_AMOUNT',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'format' => $gDecimalFormat,
                'size' => 22,
                'maxlength' => 15,
            ),
            'hasTotal' => true,
            'id' => 72
        ),
        array(
            'fullname' => 'IA.TAX_DETAIL',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'taxdetail',
                'pickfield' => array(
                    'DETAILID', 'DESCRIPTION', 'VALUE', 'STATUS'
                ),
            ),
            'noedit' => true,
            'nonew' => true,
            'desc' => 'IA.DETAIL_ID',
            'path' => 'DETAILID',
            'id' => 73
        ),
        array(
            'path' => 'TAXRATE',
            'fullname' => 'IA.RATE',
            'desc' => 'IA.RATE',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'decimal',
                'size' => 22,
                'maxlength' => 15,
            ),
            'precision' => 3,
            'readonly' => true,
            'id' => 75
        ),
    ),
    'parententity' => 'apbillitem',
    'dbfilters' => array(
        array('apbilltaxentry.lineitem', '=', 'T'),
        array('apbilltaxentry.istax', '=', 'T'),
        array(
            'prrecord.recordtype',
            '=',
            SubLedgerTxnManager::BILL_RECTYPE
        )
    ),
    'printas' => 'IA.TAX_ENTRY',
    'pluralprintas' => 'IA.TAX_ENTRIES',
    'primaryfield' => 'TRX_TAX',
    'table' => 'prentry',
    'vid' 	=> 'RECORDNO',
    //'dontGenerateQueries' => true,
    'api' => array(
        'GET_BY_GET' => true,
        'PERMISSION_CREATE' => 'NONE',
        'PERMISSION_UPDATE' => 'NONE',
        'PERMISSION_DELETE' => 'NONE',
    ),
);
