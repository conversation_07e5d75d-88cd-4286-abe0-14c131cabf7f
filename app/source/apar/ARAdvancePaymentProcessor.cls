<?php

/**
 * Account payable discount payment processing implementation.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Intacct Corporation All, Rights Reserved
 */
class ARAdvancePaymentProcessor extends AdvancePaymentProcessor
{

    /**
     * @var string $exchaneRateDate
     */
    private $exchaneRateDate;

    /**
     * @var string $exchaneRateID
     */
    private $exchaneRateID;

    /**
     * @var string $exchaneRate
     */
    private $exchaneRate;

    /**
     * @var array $postedAdvancePaymentDetail
     */
    private $postedAdvancePaymentDetail;

    /** @var string $entityEntryPath */
    public string $entityEntryPath = 'aradvanceitem';

    /**
     * @return array
     */
    public function getPostedAdvancePaymentDetail()
    {
        return $this->postedAdvancePaymentDetail;
    }

    /**
     * @param array $postedAdvancePaymentDetail
     */
    public function setPostedAdvancePaymentDetail($postedAdvancePaymentDetail)
    {
        $this->postedAdvancePaymentDetail = $postedAdvancePaymentDetail;
    }

    /**
     * Get the record types of the transaction we are paying
     *
     * @return string[] the record type of the transaction we are paying
     */
    protected function getPaidTransactionRecordTypes()
    {
        return array(SubLedgerTxnManager::INVOICE_RECTYPE, SubLedgerTxnManager::ARADJUSTMENT_RECTYPE);
    }

    /**
     * Get the record types of the parent transactions we need to fetch for the payment
     *
     * @return string[] the record types of the parent transactions we need to fetch for the payment
     */
    protected function getParentTransactionRecordTypes()
    {
        // No parent transactions
        return array(SubLedgerTxnManager::ARADVANCE_RECTYPE);
    }

    /**
     * Get the payment key field path in the payment details data structure
     *
     * @return string the payment key field path in the payment details data structure
     */
    protected function getPaymentKeyPath()
    {
        return 'ADVANCEKEY';
    }

    /**
     * Get the payment entry key field path in the payment details data structure
     *
     * @return string the payment entry key field path in the payment details data structure
     */
    protected function getPaymentEntryKeyPath()
    {
        return 'ADVANCEENTRYKEY';
    }

    /**
     * Get the payment date field path in the payment details data structure
     *
     * @return string the paymentdate field path in the payment details data structure
     */
    protected function getPaymentDatePath()
    {
        return 'PAYMENTDATE';
    }

    /**
     * Get the payment amount field path in the payment details data structure
     *
     * @return string the the payment amount field path in the payment details data structure
     */
    protected function getPaymentAmountPath()
    {
        return 'TRX_POSTEDADVANCEAMOUNT';
    }

    /**
     * Get the key field path of the transaction we need to fetch for the payment in the payment details data structure
     *
     * @return string the key field path of the transaction we need to fetch for the payment in the payment details
     * data structure
     */
    protected function getParentTransactionKeyPath()
    {
        return 'POSTEDADVANCEKEY';
    }
    
    /**
     * Get the entry key field path of the transaction we need to fetch for the payment in the payment details
     * data structure
     *
     * @return string the key field path of the transaction we need to fetch for the payment in the payment details
     * data structure
     *
     */
    protected function getParentTransactionEntryKeyPath()
    {
        return 'POSTEDADVANCEENTRYKEY';
    }

    /**
     * Get the payment base amount field path in the payment details data structure
     *
     * @return string the payment amount field path in the payment details data structure
     */
    protected function getPaymentBaseAmountPath()
    {
        return 'POSTEDADVANCEAMOUNT';
    }

    /**
     * Get the payment amount
     *
     * @param PaymentDetailDTO $pymtDetail the payment details data
     *
     * @return string the payment amount
     */
    protected function getPaymentAmount($pymtDetail)
    {
        return $pymtDetail->getTrxPostedAdvanceAmount();
    }

    /**
     * Abstract method to implement the record type for the processor.
     *
     * @return string
     */
    protected function getRecordType()
    {
        return SubLedgerPymtManager::ARPOSTEDADVANCE_RECTYPE;
    }

    /**
     * Method to process transactions.
     *
     * @param PaymentDTO $paymentInput
     * @param array      $transactionMap
     * @return bool
     */
    public function processTransactions(/** @noinspection PhpUnusedParameterInspection */ PaymentDTO $paymentInput,
        /** @noinspection PhpUnusedParameterInspection */ $transactionMap)
    {
        // do nothing as there is no discount prrecord exist as such.
        return true;
    }

    /**
     * @param PaymentDetailDTO $paymentDetail
     * @return string|null
     */
    protected function getCreditPaymentAmount(PaymentDetailDTO $paymentDetail)
    {
        if (isset($paymentDetail)) {
            return $paymentDetail->getTrxPostedAdvanceAmount();
        }
        return null;
    }

    /**
     * @param PaymentDetailDTO $paymentDetail
     * @return string|null
     */
    protected function getCreditBasePaymentAmount(PaymentDetailDTO $paymentDetail)
    {
        if (isset($paymentDetail)) {
            return $paymentDetail->getPostedAdvanceAmount();
        }
        return null;
    }

    /**
     * @param PaymentDetailDTO $paymentDetail
     * @return string|null
     */
    protected function getParentTransactionKey(PaymentDetailDTO $paymentDetail)
    {
        if (isset($paymentDetail)) {
            return $paymentDetail->getAdvanceKey();
        }
        return null;
    }

    /**
     * @param PaymentDetailDTO $paymentDetail
     * @return string|null
     */
    protected function getParentTransactionEntryKey(PaymentDetailDTO $paymentDetail)
    {
        if (isset($paymentDetail)) {
            return $paymentDetail->getAdvanceEntryKey();
        }
        return null;
    }
    /**
     * Process the payments for the given request.
     *
     * @param PaymentRequest  $request
     * @param PaymentResponse $response
     *
     * @return bool false if error else true
     */
    public function processPayments(PaymentRequest $request, PaymentResponse $response)
    {
        $ok = true;
        $tranactionKeyMap = $request->getTransactionKeysByPath(PaymentUtils::ADVANCEKEY_PATH);
        if (empty($tranactionKeyMap)) {
            return $ok;
        }
        // go further only if the advanced is applied for any transactions
        if ($this->isAdvancePaymentRequested($request)) {
            // call the parent to apply the payments.
            $ok = parent::processPayments($request, $response);
        }
        return $ok;
    }

    /**
     * Apply a payment
     *
     * @param array|PRRecordDTO             $transaction the transaction data
     * @param array|PaymentDetailDTO        $pymtDetail  the payment detail data
     * @param array|PaymentResponse         $response
     * @param array|PaymentRequest          $request
     * @param PaymentDistributionHelper     $pymtDistroHelper
     *
     * @return bool false if error else true
     */
    public function applyPayment(PRRecordDTO $transaction, $pymtDetail, PaymentResponse $response, PaymentRequest
                                    $request, $pymtDistroHelper = null)
    {
        $ok = true;

        // before aply the payment and distribute credit, go for distribution logic
        $parentKey = $this->getParentTransactionKey($pymtDetail);
        $parentEntryKey = $this->getParentTransactionEntryKey($pymtDetail);
        $credits = $request->getTransactions();
        $creditTransaction = $credits[$parentKey];
        $trxEntryKey = $this->getPaidTransactionEntryKey($pymtDetail);
        $creditToApply = $this->getCreditPaymentAmount($pymtDetail);

        // get the payment distribution object
        $pymtDistroHelper = new CreditPaymentDistributionHelper();
        $pymtDistroHelper->setDistributionMethod(PaymentUtils::getPaymentDistributionPreference());
        $pymtDistroHelper->setDistributionPath($this->getPaymentKeyPath());
        $pymtDistroHelper->setPaymentInfoObj($response->getPaymentInfoObj());
        $pymtDistroHelper->setCreditToApplyAmount($creditToApply);

        $this->setPymtDistroHelper($pymtDistroHelper);

        if(!empty($parentKey)){
            $ok = $this->validateCreditAvailable($credits[$parentKey], $this->getCreditPaymentAmount($pymtDetail));
        }

        if ( $ok && PaymentUtils::isPartialPymtTaxCaptureSupported($transaction->getValues()) ) {
            $pymtDistroHelper->setDistributionMethod(PaymentUtils::WEIGHTED_AVERAGE_DISTRIBUTION);
            $transaction->setTxnEntryKey($parentEntryKey);
        }
        // if the payment distribution method is "priority" then distribute based on location
        if( $pymtDistroHelper->getDistributionMethod() == PaymentUtils::PRIORITY_DISTRIBUTION ) {
            // set the credit type as advance
            $pymtDistroHelper->setPaymentType(PaymentDistributionHelper::ADVANCE_CREDIT);

            if(!empty($parentEntryKey)) {
                $pymtDistroHelper->setSplitedCreditAmt($creditToApply);
                $pymtDistroHelper->setSplitCredit(true);
                $pymtDistroHelper->setCreditEntryKey($parentEntryKey);
            }
            if(!empty($trxEntryKey)) {
                $pymtDistroHelper->setTxnEntryKey($trxEntryKey);
            }
            // for for the distribution logic
            $ok = $ok && $this->renderCreditDistrubutionMap($transaction, $creditTransaction);
            $distributedCreditToApply = $pymtDistroHelper->getDistributedCreditAmount();
            // update the paymentdistrohelper
            $this->setPymtDistroHelper($pymtDistroHelper);
            // in case of credit mismatch, update with total distributed amount
            if( $creditToApply > $distributedCreditToApply ) {
                $pymtDetail->setTrxPostedAdvanceAmount($distributedCreditToApply);
            }
        }

        if ( $ok && PaymentUtils::isPartialPymtTaxCaptureSupported($transaction->getValues()) ) {
            $ok = $ok && $this->validatePayment($transaction, $pymtDetail);
            $pymtAmount = $this->getPaymentAmount($pymtDetail);
            $trxEntryKey = $this->getPaidTransactionEntryKey($pymtDetail);
            $transaction->setTxnEntryKey($trxEntryKey);
            $ok = $ok && $pymtDistroHelper->distributePayment($pymtAmount, $transaction);
        } else {
            // Apply the payment
            $ok = $ok && parent::applyPayment($transaction, $pymtDetail, $response, $request, $pymtDistroHelper);
        }

        // Match the payment amount with the credits
        $ok = $ok && $this->distributeCredits($transaction, $pymtDetail, $response, $request);

        return $ok;
    }

    /**
     * Match credits to a transaction
     *
     * @param array|PRRecordDTO      $transaction the transaction data
     * @param array|PaymentDetailDTO $pymtDetail the payment detail data
     * @param array|PaymentResponse  $response
     * @param array|PaymentRequest   $request
     *
     * @return bool false if error else true
     */
    protected function distributeCredits(PRRecordDTO $transaction, $pymtDetail, PaymentResponse $response, PaymentRequest $request)
    {
        $ok = true;


        $trxEntryKey = $this->getPaidTransactionEntryKey($pymtDetail);
        $creditToApply = $this->getCreditPaymentAmount($pymtDetail);
        $isPartialPymtTaxCapture = PaymentUtils::isPartialPymtTaxCaptureSupported($transaction->getValues());

        $txnEntries = $transaction->getEntryList();
        foreach ($txnEntries as &$txnEntry) {
            if ($txnEntry instanceof PREntryDTO) {
                $isValidEntry = $isPartialPymtTaxCapture ? ! in_array($trxEntryKey, [ $txnEntry->getRecordNo(),
                                                                                      $txnEntry->getParentEntry() ])
                    : ( $txnEntry->getRecordNo() != $trxEntryKey );
                // No the right transaction entry ? No need to continue
                if ( ! empty($trxEntryKey) && $isValidEntry ) {
                    continue;
                }
                $amountPath = $this->getPaymentKeyPath();
                $existingCreditApplied = $response->getPaymentInfoObj()->getBillLinePaymentDistribution($txnEntry->getRecordKey(), $txnEntry->getRecordNo(),
                    $amountPath);

                // Get the exact credit apply amount for this credit
                $appliedDistributionAmount = $response->getPaymentInfoObj()->getBillLineAppliedPaymentDistribution($txnEntry->getRecordKey(),
                    $txnEntry->getRecordNo(),$amountPath);
                $remainingAmount = ibcsub($existingCreditApplied, $appliedDistributionAmount);
                $applyAmount = min($remainingAmount, $creditToApply);

                if ( empty($applyAmount) || !ibccomp($applyAmount, '0') ) {
                    continue;
                }
                // Match credits for that line
                $ok = $ok && $this->distributeEntryCredits($txnEntry, $applyAmount, $pymtDetail, $response, $request);
                if ( !$ok ) {
                    break;
                }
            }
        }
        return $ok;
    }

    /**
     * Match each transaction entry with credits
     *
     * @param array|PREntryDTO       $entry
     * @param float                  $creditToApply
     * @param array|PaymentDetailDTO $pymtDetail
     * @param array|PaymentResponse  $response
     * @param array|PaymentRequest   $request
     * @return bool
     */
    protected function distributeEntryCredits( PREntryDTO $entry, $creditToApply, PaymentDetailDTO $pymtDetail,
                                               PaymentResponse $response, PaymentRequest $request)
    {
        // Do we already have a credit specified ?
        $ok = true;
        $parentKey = $this->getParentTransactionKey($pymtDetail);
        $parentEntryKey = $this->getParentTransactionEntryKey($pymtDetail);

        $credits = $request->getTransactions();

        // get the payment distribution helper
        $pymtDistroHelper = $this->getPymtDistroHelper();

        // If not set, then instance the object
        if( empty($pymtDistroHelper) ) {
            $pymtDistroHelper = new CreditPaymentDistributionHelper();
            $pymtDistroHelper->setDistributionMethod(PaymentUtils::getPaymentDistributionPreference());
            $pymtDistroHelper->setDistributionPath($this->getPaymentKeyPath());
            $pymtDistroHelper->setPaymentInfoObj($response->getPaymentInfoObj());
            $this->setPymtDistroHelper($pymtDistroHelper);
        }

        if ( !empty($parentKey) && !empty($parentEntryKey) ) {

            // We already validate the amount at this point so we know this assignment is valid
            $credit =& $credits[$parentKey];
            $creditEntry =& $this->getTransactionEntryByRecordNo($credit, $parentEntryKey);
            $ok = $ok && $this->assignCreditToTransactionLine($entry, $creditEntry, $request, $response, $creditToApply);

        } else {
            $creditTransaction = $credits[$parentKey];
            // Get the credits or credit if key is specified
            // Find all the credits that matches that line to pay it off
            // Match credits on the primary values first.
            // If those values are a match then match the credits else do no match it.
            // This will ensure we do not match a credit line to an entry that is a lesser match that another one.
            // if the payment distribution method is "priority" then distribute based on location
            if( $pymtDistroHelper->getDistributionMethod() == PaymentUtils::PRIORITY_DISTRIBUTION ) {
                $matchingValues = $this->getPrimaryCreditsValuesMatch();

                $ok = $ok && $this->findCreditsForTransactionLine(
                        $entry, $creditTransaction, $request, $response, $matchingValues, true, $creditToApply);
            } else {
                $ok = $ok && $this->matchCreditsForTransactionLine(
                        $entry, $creditTransaction, $creditToApply, $request, $response);
            }
        }

        if($ok) {
            // Update the applied payment distribution
            $response->getPaymentInfoObj()->setBillLineAppliedPaymentDistribution($entry->getRecordKey(),
                $entry->getRecordNo(), $this->getPaymentKeyPath(), $creditToApply);
        }

        return $ok;
    }

    /**
     * Build the payment detail data structure
     *
     * @param array                 $payment the payment data
     * @param array|PaymentResponse $response
     *
     * @return bool false if error else true
     */
    protected function buildPaymentDetails(&$payment, PaymentResponse $response)
    {
        $ok = true;
        // For each payment line build the payment detail data
        foreach ($payment['ITEMS'] as $entry) {
            $pymtDetail = $this->buildPaymentDetail($payment, $entry);
            // populate the payment entry details
            $response->getPaymentInfoObj()->updateEntryPaymentDetails($pymtDetail, $entry['__trx_recordno'],
                                                                      $entry['__trxline_recordno'],
                                                                      $this->getParentTransactionKeyPath());
            // In case of cash/check payment only add the payment keys. For discount NO
            if (in_array($payment['RECORDTYPE'], array(BasePymtManager::ARPOSTEDADVANCE_RECTYPE))) {
                $response->setPaymentKeys($payment['RECORDNO'], $entry['__trx_recordno'], $entry['__trxline_recordno']);
            }
        }
        return $ok;
    }

    /**
     * Build a payment record header
     *
     * @param array|PaymentDTO   $paymentDTO    the payment request data
     * @param int                $creditKey the credit key
     *
     * @return array the payment record header
     */
    protected function buildPaymentHeader(PaymentDTO $paymentDTO, $creditKey)
    {
        $header = parent::buildPaymentHeader($paymentDTO, $creditKey);
        $header['WHENDUE'] = null;
        $header['EXCH_RATE_DATE'] = $this->exchaneRateDate;
        $header['EXCH_RATE_TYPE_ID'] = $this->exchaneRateID;
        $header['EXCHANGE_RATE'] = $this->exchaneRate;
        // The value of $header['CLEARED'] is changed to 'In Transit' to match the validvalues in arpostedadvance.ent
        $header['CLEARED'] = 'In Transit';
        $header['PARENTPAYMENTKEY'] = $creditKey;
        $header['WHENCREATED'] = $paymentDTO->getReceiptDate();
        // Include PRBATCHKEY to handle manual batching case
        // In that case, PRBATCHKEY is provided by the user mandatorily
        $header['PRBATCHKEY'] = $paymentDTO->getPrBatchKey();
        return $header;
    }

    /**
     * Build a payment record
     *
     * @param array|PaymentRequest $request
     * @param array|PaymentResponse $response
     * @param int $creditKey the credit key
     *
     * @return array the payment record
     */
    protected function buildPaymentRecord(PaymentRequest $request, PaymentResponse $response, $creditKey)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        //Get the Transactions from the request
        $transactions = $request->getTransactions();
        $creditTransactions = $transactions[$creditKey];
        $creditTransactionsEntries = $creditTransactions->getEntryList();

        $paymentDTO = $request->getPaymentDTO();
        // Build the header
        /** @var PREntryDTO $creditTransactionsEntry */
        //Here we get the Advance trasaction exchange rate to add to the Posted Advance record whihc is getting created
        foreach ($creditTransactionsEntries as $creditTransactionsEntry){
            $this->exchaneRateDate = $creditTransactionsEntry->getExchangeRateDate();
            $exchRateMgr = $gManagerFactory->getManager('exchangeratetypes');
            $excahangeRateType = $exchRateMgr->GetExchangeRateTypeName($creditTransactionsEntry->getExchangeRateType());
            $this->exchaneRateID = $excahangeRateType;
            $this->exchaneRate = $creditTransactionsEntry->getExchangeRate();
        }
        $payment = $this->buildPaymentHeader($paymentDTO, $creditKey);

        if($paymentDTO->isCreditOnlyPayment($payment[$this->entityManager->getPrimaryDimension()])) {
            // Financial entity is included in the batch-title while creating a new payment batch
            // So we should not unset it
            // unset($payment['FINANCIALENTITY']);
            unset($payment['PAYMENTMETHOD']);
        }
        // Populate the base currency
        //$transactions = self::getTransactions(SubLedgerTxnManager::APADVANCE_RECTYPE);
        //$transactions = $request->getTransactions();
        foreach($transactions as $transaction) {
            if($transaction instanceof PRRecordDTO) {
                if ($transaction->getRecordNo() == $creditKey) {
                    $payment['BASECURR'] = $transaction->getBaseCurrency();
                    $payment['CURRENCY'] = $transaction->getCurrency();
                    break;
                }
            }
            // set the base currency and payment currency
            $this->populateCurrency($payment, $request, $transaction);
        }

        $transactionEntries = $this->collectTrxEntriesByCreditKey($creditKey, $request);
        // Build the entries
        $payment['ITEMS'] = $this->buildPaymentEntries($transactionEntries, $payment, $response);

        return $payment;
    }

    /**
     * Build the payment record entries
     *
     * @param array             $transactionEntries
     * @param array             $payment the payment record
     * @param PaymentResponse   $response
     * @param int $nextRecordNo value of the next record#
     *
     * @return array the payment record entries
     */
    protected function buildPaymentEntries($transactionEntries, $payment, PaymentResponse $response, $nextRecordNo = 0)
    {
        $pymtEntries = parent::buildPaymentEntries($transactionEntries, $payment, $response, $nextRecordNo);
        if ( TaxSetupManager::isVATEnabled() ) {
            foreach ( $pymtEntries as &$pymtentry ) {
                unset($pymtentry['ISTAX']);
                unset($pymtentry['PAYMENTTAXCAPTURE']);
            }
        }
        return $pymtEntries;
    }
    
    /**
     * Collect all the transaction entry paid by a credit
     *
     * @param int|string        $creditKey the credit key
     * @param PaymentRequest    $request
     *
     * @return array the list of transaction entries
     */
    protected function collectTrxEntriesByCreditKey($creditKey, PaymentRequest $request)
    {
        $entries = array();
        $creditsMap = $this->getCreditMap();
        $creditMap = $creditsMap[$creditKey];
        $transactions = $request->getTransactions();
        $amountPath = $this->getPaymentAmountPath();
        // build the $parentDetailMap based on Applying advance
        if(!empty($creditMap)) {
            /** @var PRRecordDTO $creditTransaction */
            $creditTransaction = $transactions[$creditKey];
            $creditTxnEntries = $creditTransaction->getEntryList();
            foreach ( $creditMap as $creditEntryKey => $trxMap ) {
                foreach($creditTxnEntries as $creditEntry) {
                    if( $creditEntry instanceof PREntryDTO && $creditEntry->getRecordNo() == $creditEntryKey ) {
                        $parentDetailMap = array(
                            'PARENTENTRYACCT' =>  $creditEntry->getAccountKey(),
                            'PARENTENTRYLOC' => $creditEntry->getLocationKey(),
                            'PARENTENTRYBASELOC' => $creditEntry->getBaseLocation(),
                            'PARENTENTRYDEPT' => $creditEntry->getDepartmentKey(),
                        );
                        $parentEntryValues[$creditEntryKey] = $parentDetailMap;
                    }
                }
            }
        }
        // Build the applied overpayment line detail
        foreach ( $creditMap as $creditEntryKey => $trxMap ) {
            foreach ( $trxMap as $trxKey => $trxEntryMap ) {
                /** @var PRRecordDTO $transaction */
                $transaction = $transactions[$trxKey];

                $trxEntryKeys = array_keys($trxEntryMap);
                $txnEntries = $transaction->getEntryList();
                foreach ( $txnEntries as $entry ) {
                    if ( $entry instanceof PREntryDTO && in_array($entry->getRecordNo(), $trxEntryKeys) ) {
                        $entryValue = $entry->getValues();
                        $entryValue['PARENTENTRYKEY'] = $creditEntryKey;
                        /** @noinspection PhpUndefinedVariableInspection */
                        $entryValue['PARENTENTRYACCT'] = $parentEntryValues[$creditEntryKey]['PARENTENTRYACCT'];
                        $entryValue['PARENTENTRYLOC'] = $parentEntryValues[$creditEntryKey]['PARENTENTRYLOC'];
                        $entryValue['PARENTENTRYBASELOC'] = $parentEntryValues[$creditEntryKey]['PARENTENTRYBASELOC'];
                        $entryValue['PARENTENTRYDEPT'] = $parentEntryValues[$creditEntryKey]['PARENTENTRYDEPT'];
                        $entryValue[$amountPath] = $trxEntryMap[$entryValue['RECORDNO']];
                        $entryValue['EXCH_RATE_TYPE_ID'] = $this->exchaneRateID;
                        $entryValue['EXCH_RATE_DATE'] = $this->exchaneRateDate;
                        $entryValue['EXCHANGE_RATE'] = $this->exchaneRate;
                        $entryDto = new PREntryDTO($entryValue);
                        $entries[] = $entryDto;
                    }
                }
            }
        }
        return $entries;
    }

    /**
     * @param array|PREntryDTO $entry
     * @param PaymentResponse $response
     *
     * @return string
     */
    protected function getPymtAmount($entry, PaymentResponse $response)
    {
        $pymtAmount = $entry->getPosteAdvanceAmount();
        if(empty($pymtAmount)) {
            $pymtAmount = parent::getPymtAmount($entry, $response);
        }
        return $pymtAmount;
    }

    /**
     * Build the payment records
     *
     * @param PaymentRequest  $request
     * @param PaymentResponse $response
     * @param array           $payments the payment data
     *
     * @return bool false if error else true
     */
    protected function buildPaymentRecords(PaymentRequest $request, PaymentResponse $response, &$payments)
    {
        $ok = true;
        // Build a payment per advance
        $creditsMap = $this->getCreditMap();
        $creditsMap = is_array($creditsMap) ? $creditsMap : [];
        $creditKeys = array_keys($creditsMap);
        $transactions = $request->getTransactions();
        foreach ( $creditKeys as $creditKey ) {
            /** @var PRRecordDTO $creditTransaction */
            $creditTransaction = $transactions[$creditKey];
            $payment = $this->buildPaymentRecord($request, $response, $creditKey);

            //here we do the rounding for the posted advance before creating the 'RO' record type. as we have to
            // compare with the advance and posted advance amount
            $ok  = $ok && $this->roundingPostedAdvanceOverpymtAmount($creditTransaction,$payment);

            if($payment['EXCH_RATE_TYPE_ID'] == CUSTOM_RATE) {
                // Get the exchange rate from previously set
                $customExchangeRate = $response->getPaymentInfoObj()->getCustomExchangeRate();
                // If its not present, then calculate
                if(empty($customExchangeRate)) {
                    $items = $payment['ITEMS'];
                    $baseAmount = 0;
                    $trxAmount = 0;
                    foreach ($items as $item) {
                        $baseAmount = ibcadd($baseAmount, $item['AMOUNT']);
                        $trxAmount = ibcadd($trxAmount, $item['TRX_AMOUNT']);
                    }
                    $customExchangeRate = ibcdiv($baseAmount, $trxAmount, 14, true);
                }
                // Set the exchange rate
                if($customExchangeRate != 0){
                    $payment['EXCHANGE_RATE'] = $customExchangeRate;
                }
            }
            $payment['PARENTRECORDTYPE'] = $creditTransaction->getRecordType();
            $payments[] = $payment;
        }
        return $ok;
    }
    
    /**
     * Build the payment detail data
     *
     * @param array|PREntryDTO $txnEntry
     * @param array|PREntryDTO $creditTxnEntry
     * @param string           $paymentDate
     * @param float            $creditToApply
     * @param string           $recordType
     *
     * @return array|PaymentDetailDTO the payment detail data
     */
    protected function buildInlinePaymentDetail(PREntryDTO $txnEntry, PREntryDTO $creditTxnEntry, $paymentDate,
                                                &$creditToApply, $recordType)
    {
        $trxKeyPath = $this->getPaidTransactionKeyPath($recordType);
        $trxEntryKeyPath = $this->getPaidTransactionEntryKeyPath($recordType);
        
        $inlineAmountPath = $this->getPaymentAmountPath();
        $inlineBaseAmountPath = $this->getPaymentBaseAmountPath();
        $paymentKeyPath = $this->getPaymentKeyPath();
        $paymentEntryKeyPath = $this->getPaymentEntryKeyPath();
        $paymentDatePath = $this->getPaymentDatePath();

        $creditCurrency = $creditTxnEntry->getCurrency();
        $creditBaseCurrency = $creditTxnEntry->getBaseCurrency();
        $inlineBaseAmount = $creditToApply;
        if(IsMCPSubscribed() && $creditCurrency != $creditBaseCurrency) {
            $creditExchangeRate = $creditTxnEntry->getExchangeRate();
            $inlineBaseAmount = ibcmul($creditToApply, $creditExchangeRate, 2, true);
        }

        $pymtDetail = array(
            $paymentKeyPath => $creditTxnEntry->getRecordKey(),
            $paymentEntryKeyPath => $creditTxnEntry->getRecordNo(),
            $trxKeyPath => $txnEntry->getRecordKey(),
            $trxEntryKeyPath => $txnEntry->getRecordNo(),
            $inlineAmountPath => $creditToApply,
            $inlineBaseAmountPath => $inlineBaseAmount,
            $paymentDatePath => $paymentDate,
            'CURRENCY' => $creditTxnEntry->getCurrency(),
            'MODULEKEY' => $this->entityManager->getModuleKey()
        );

        $pymtDetailObj = new PaymentDetailDTO($pymtDetail);
        return $pymtDetailObj;
    }

    /**
     * Build the payment detail data
     *
     * @param array $payment the payment data
     * @param array $entry   the payment entry data
     *
     * @return PaymentDetailDTO the payment detail data
     */
    protected function buildPaymentDetail($payment, $entry)
    {
        $entryRecordType = $entry['__recordType'];
        $trxKeyPath = $this->getPaidTransactionKeyPath($entryRecordType);
        $trxEntryKeyPath = $this->getPaidTransactionEntryKeyPath($entryRecordType);
        $paymentAmountPath = $this->getPaymentAmountPath();
        $paymentBaseAmountPath = $this->getPaymentBaseAmountPath();
        $paymentKeyPath = $this->getParentTransactionKeyPath();
        $paymentEntryKeyPath = $this->getParentTransactionEntryKeyPath();
        $paymentDatePath = $this->getPaymentDatePath();
        $parentPaymentKeyPath = $this->getPaymentKeyPath();
        $parentPaymentEntryKeyPath = $this->getPaymentEntryKeyPath();
        $pymtDetail = array(
            $paymentKeyPath => $payment['RECORDNO'],
            $paymentEntryKeyPath => $entry['RECORDNO'],
            $trxKeyPath => $entry['__trx_recordno'],
            $trxEntryKeyPath => $entry['__trxline_recordno'],
            $paymentAmountPath => $entry['TRX_AMOUNT'],
            $paymentBaseAmountPath => $entry['AMOUNT'],
            $paymentDatePath => $payment['WHENCREATED'],
            $parentPaymentKeyPath => $payment['PARENTPAYMENTKEY'],
            $parentPaymentEntryKeyPath => $entry['PARENTENTRYKEY'],
            'CURRENCY' => $payment['CURRENCY'],
            'MODULEKEY' => $this->entityManager->getModuleKey(),
            //'STATE'=>BasePRRecordManager::CONFIRMED_RAWSTATE
        );
        $pymtDetailObj = new PaymentDetailDTO($pymtDetail);
        return $pymtDetailObj;
    }

    /**
     * Returns if there is any advance payment requested in this request
     *
     * @param PaymentRequest $request
     *
     * @return bool
     */
    private function isAdvancePaymentRequested(PaymentRequest $request)
    {
        $applyAdvance = false;
        $paymentDetails = $request->getPaymentDTO()->getPaymentDetailList();
        if(!empty($paymentDetails)) {
            foreach ($paymentDetails as $paymentDetail) {
                if ($paymentDetail instanceof PaymentDetailDTO) {
                    $advanceKey = $paymentDetail->getAdvanceKey();
                    if (isset($advanceKey) && $advanceKey != '') {
                        $applyAdvance = true;
                        break;
                    }
                }
            }
        }
        return $applyAdvance;
    }

    /**
     * Method to create payments with response data provided.
     *
     * @param PaymentResponse $response
     *
     * @return bool
     */
    public function createPayments(PaymentResponse $response)
    {
        $ok =  parent::createPayments($response);
        return $ok;
    }

    /**
     * Build the payment record entry
     *
     * @param array|PREntryDTO      $entry
     * @param string                $nextRecordNo
     * @param PaymentResponse $response
     *
     * @return array the payment record entry
     */
    protected function buildPaymentEntry($entry, $nextRecordNo, PaymentResponse $response)
    {
        $pymtEntry = parent::buildPaymentEntry($entry, $nextRecordNo, $response);
        $pymtEntry['__trx_exchange_rate'] = $this->exchaneRate;
        $pymtEntry['EXCH_RATE_DATE'] = $this->exchaneRateDate;
        $pymtEntry['EXCH_RATE_TYPE_ID'] = $this->exchaneRateID;
        $pymtEntry['EXCHANGE_RATE'] = $this->exchaneRate;
        $pymtEntry['PARENTENTRYKEY'] = $entry->getParentEntryKey();
        $parentBaseLoc = $entry->getCustomFieldValue('PARENTENTRYBASELOC') ;
        $pymtEntry['PARENTENTRYBASELOCATION'] = $parentBaseLoc ?: $entry->getBaseLocation();
        $pymtEntry['PARENTENTRYDEPT'] = $entry->getCustomFieldValue('PARENTENTRYDEPT');
        $pymtEntry['PARENTENTRYACCT'] = $entry->getCustomFieldValue('PARENTENTRYACCT');
        return $pymtEntry;
    }


    /**
     * @param PRRecordDTO $transaction
     * @param PRRecordDTO $creditTransaction
     * @return mixed
     */
    protected function renderCreditDistrubutionMap(PRRecordDTO $transaction, PRRecordDTO $creditTransaction )
    {
        // Read the MultiEntity reference for "Limit  to same entity"
        /** @var ARPostedAdvanceManager $entMgr */
        $entMgr = $this->getEntityManager();
        $inlineCreditPrefPath = $entMgr->getInlineCreditPrefPath();

        // check if the ME preference set to distribute to other entity or not
        $distributToOtherEntity = PaymentUtils::canDistributeToOtherEntity($inlineCreditPrefPath);

        $creditDistributionHelper = $this->getPymtDistroHelper();
        // Update the distribute to other entity into PymtDistribution Object
        $creditDistributionHelper->setDistributeToOtherEntity($distributToOtherEntity);

        $creditDistributionHelper->setEntityType('customer');
        // Call the parent renderCreditDistrubutionMap
        return $creditDistributionHelper->renderCreditDistrubutionMap($transaction, $creditTransaction);
    }

    /**
     * @param PaymentDTO $paymentDTO
     *
     * @return string
     */
    protected function getExistingRecordNo(PaymentDTO $paymentDTO)
    {
        return '';
    }

    /**
     * @param array $payment
     * @return bool
     */
    protected function isRecordTypeMatchForPayment($payment)
    {
        $ok = false;
        if((isset($payment['PARENTRECORDTYPE']) && $payment['PARENTRECORDTYPE'] == SubLedgerTxnManager::ARADVANCE_RECTYPE) &&
            $payment['RECORDTYPE'] == $this->getRecordType()) {
            $ok = true;
        }
        return $ok;
    }

    /**
     * @param array|PaymentDetailDTO $paymentDetail
     *
     * @return string|null
     */
    protected function getPaymentKeyToConfirm($paymentDetail)
    {
        if (isset($paymentDetail)) {
            if(is_array($paymentDetail)){
                return $paymentDetail['POSTEDADVANCEKEY'];
            }else{
                return $paymentDetail->getPostedAdvanceKey();
            }
        }
        return null;
    }

    /**
     * Method to create an payment.
     *
     * @param array     $payment
     *
     * @return bool
     */
    protected function createPayment(&$payment)
    {
        $ok = parent::createPayment($payment);
        $ok = $ok && $this->updateMaxPayActDate($payment);

        return $ok;
    }

}