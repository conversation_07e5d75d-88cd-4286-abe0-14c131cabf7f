<?php
require_once('util.inc');
import('editor');
import('PrRecordEditor');
include_once('backend_dates.inc');

class InvoiceEditor extends PrRecordEditor
{
    /** @var string $kShowReclassState */
	var $kShowReclassState = Editor_ShowReclassState;

	/** @var string $kReclassAction */
	var $kReclassAction = 'reclass';

	/** @var string $kReclassButton */
	var $kReclassButton = 'Reclassify';

	/** @var bool $showPaymentHistory */
	var $showPaymentHistory = false;

	/** @var string|int $paymentHistoryOP */
	var $paymentHistoryOP = -1;

	/** @var bool|string $useAllocation */
	var $useAllocation = false;

	/** @var array $printTemplateCache */
	var $printTemplateCache = array();

	/** @var string $_rectype */
	var $_rectype;

	/** @var string[] $kDefaultVerbActions */
	var $kDefaultVerbActions;

	/** @var int $numofrows */
	var $numofrows;

	/** @var bool $postAndNew */
	var $postAndNew;

	/** @var string[] $postAndNewPopData */
	var $postAndNewPopData;

	/** @var string $_docLinkType */
	var $_docLinkType;

	/** @var string $autoBatchFrequency */
	var $autoBatchFrequency;

	/** @var string[] $templateInfo */
	var $templateInfo;

	/** @var string[] $xsltemplateInfo */
	var $xsltemplateInfo;

    /**
     * @param array $_params
     */
	function __construct($_params) {
		$this->mcpEnabled = IsMCPEnabled();
		parent::__construct($_params);
	}

   /**
    * @param array $_params
    *
    * @return array
    */
	function Buttons_Instantiate($_params) {
		$_params = Editor::Buttons_Instantiate($_params);

		unset($_params['deliverbutton']);
		unset($_params['deliveraction']);

		if (isset($_params['saveandnewbutton']) &&
				$_params['saveandnewbutton'] != '' && $_params['entity'] != 'apbill') {
			$_params['saveandnewbutton'] = 'Save';
			unset(	$_params['dobutton']);
			unset(	$_params['doaction']);
		}

		if ($_params['state'] == Editor_ShowReclassState) {
			$_params['dobutton'] = 'Save';
			$_params['doaction'] = 'save';
			$_params['cancelbutton'] = 'Cancel';
			$_params['cancelaction'] = 'cancel';
			if($_params['entity'] == 'apbill') {
				$_params['saveandprintbutton'] = 'Save & Print';
				$_params['saveandprintaction'] = 'save';
			}
		}
		if ($this->state == Editor_ShowReclassState) {
			unset($_params['copybutton']);
			unset($_params['copyaction']);
		}

		// check reclass is allowed in the application
		if ($_params['state'] == Editor_ShowEditState && Globals::$g->gSecurity->IsOperationAllowed($_params['entity'],	$this->kReclassAction)) {
			$_params['reclassbutton'] = $this->kReclassButton;
			$_params['reclassaction'] = $this->kReclassAction;
		}

		return $_params;
	}

	/**
    * @param array  $_params
    * @param string $disable
    */
	function ShowDoButtons($_params, $disable = "")
	 {
		$dobutton = $_params['dobutton'];
		$doaction = $_params['doaction'];
        if($_REQUEST['proceed']==1) {
            $proceed=1;
        }

        if ($dobutton) { ?>
			<INPUT type="hidden" name="proceed" value='<? /** @noinspection PhpUndefinedVariableInspection */echo $proceed; ?>'>
			<INPUT id="savebuttid" type="button" name="savebutton" class="nosavehistory" value="<? echo $dobutton;  ?>"
				onclick="if (typeof(document.forms[0].after) != 'undefined') { document.forms[0].after.value = '' };document.forms[0].proceed.value = 0;document.forms[0].<? echo Editor_Action ; ?>.value = '<?  echo $doaction; ?>';document.forms[0]._kNoWarn.value = false ;if ( beforeSubmitJS() && BeforeSubmit() ) { document.forms[0].submit(); };" disableonsubmit="true">
		<? }
	}

	/**
     * @return array
     */
	function getHotKeyButtonIDs() {
		$cmdButtonIDMap = parent::getHotKeyButtonIDs();

		if (count($cmdButtonIDMap)>0) {
			if ( $this->_params['buttons']['saveandnewbutton'] ) {
				// Using 'Save And New' button ID for 'Save'
				if ( $cmdButtonIDMap['SAVE'] ) {
					$cmdButtonIDMap['SAVE'] = Editor_SaveAndNewBtnID;
				}
			}
			$cmdButtonIDMap['PPLLAST'] = '_obj__LASTPOPULATEbuttid';

		}

		return $cmdButtonIDMap;
	}

	/**
     * @param array $_params
     */
	function ShowSaveAndNewButtons($_params) {

		$saveandnewbutton 	= $_params['saveandnewbutton'];
		$saveandnewaction 	= $_params['saveandnewaction'];

        $cssClass = $this->isQuixote ? 'btn btn-primary' : 'nosavehistory';
/*
//  FOLLOWING JAVASCRIPT CODE is taken out from onlick below to disable distribution templates.
		$isMECo = IsMultiEntityCompany();
		$myctx = GetContextLocation();

// Verify distributions for an ME company and prevent validations during copy, hence the check for Layer1 tab
	if ($isMECo && !$myctx) {
	if (document.all.Layer1) {
		if (!VerifyDistribution()) {
			return;
		}
	}
}
*/

        if (Request::$r->after == 1 && Request::$r->proceed == 0) {
            $after = 1;
        } else {
            $after = 0;
        }

        if (Request::$r->proceed == 1) {
            $proceed = 1;
        }

		if ($saveandnewbutton) { ?>
                        <script>
                                if(!document.forms[0].proceed){
                                        document.write("<INPUT type='hidden' name='proceed' value='<? /** @noinspection PhpUndefinedVariableInspection */echo $proceed; ?>'>");
                                }
                        </script>
			<INPUT type="hidden" name="after" value='<? echo $after; ?>'>
			<INPUT id="saveandnewbuttid" class="<?= $cssClass; ?>" type="button" name="saveandnewbutton" value="<?= $saveandnewbutton ?>"
				onclick="
					document.forms[0].<? echo Editor_Action ; ?>.value = '<?= $saveandnewaction ?>';
					document.forms[0].after.value = 1;
					document.forms[0]._kNoWarn.value = false;
					document.forms[0].proceed.value = 0;
					if( beforeSubmitJS() && BeforeSubmit() ) { document.forms[0].submit(); };"
			disableonsubmit="true">
		<? }
	}

	/**
    * @param array $_params
    */
	function ShowSaveAndPrintButton($_params) {
		$saveandprintbutton = $_params['saveandprintbutton'];
		$saveandprintaction = $_params['saveandprintaction'];

        $cssClass = $this->isQuixote ? 'btn btn-primary' : 'nosavehistory';

		if ( $this->state == $this->kShowViewState ) {
			$pdfurl = "invoice.phtml?.op=".Request::$r->_op."&.r=".Request::$r->_r."&.recordtype=pi&.ent=".
				Request::$r->entity;
			$print = " Launch('$pdfurl','Invoice',450,300); ";
			$appendtoonclick = $print;
		}
		else {
			$print = "document.forms[0].submit()";
			$appendtoonclick = "if ( beforeSubmitJS() && BeforeSubmit()) { " . $print . " }";
		}
                /** @noinspection PhpUnusedLocalVariableInspection */
                $printnew = '';
                if($_REQUEST['printnew']==1 && $_REQUEST['proceed']==0) {
                    $printnew=1;
                } else {
                    $printnew = 0;
                }
                if($printnew){
                        ?>
                        <script>
                                document.forms[0].printnew.value = 1;
                                document.forms[0].next.value = 1;
                        </script>
                        <?
                }

		if ($saveandprintbutton) { ?>
		<INPUT id="saveandprintbuttid" type="button" class="<?= $cssClass; ?>" name="saveandprintbutton" value="<?= $saveandprintbutton ?>"
				onclick=" if(document.forms[0].proceed) {document.forms[0].proceed.value = 0;}document.forms[0].<? echo Editor_Action ; ?>.value='<?= $saveandprintaction ?>';document.forms[0]._kNoWarn.value = false; document.forms[0].next.value = 1;document.forms[0].printnew.value = 1;<?echo $appendtoonclick;?>" disableonsubmit="true">
	<? }
	}

	/**
    * @param array $_params
    * @param int   $op
    */
	function ShowReclassButtons($_params,$op) {
		$reclassbutton = $_params['reclassbutton'];
		/** @noinspection PhpUnusedLocalVariableInspection */
		$reclassaction = $_params['reclassaction'];
		$cssClass = $this->isQuixote ? 'btn btn-primary' : 'nosavehistory';
		$_sess = Session::getKey();
		$it = Request::$r->_it;
		$done = Request::$r->_done;
		$rec = Request::$r->_r;
		if ($reclassbutton) { ?>
			<INPUT type="button" name="savebutton" class="<?= $cssClass; ?>" value="<? echo $reclassbutton; ?>"
				onclick="document.forms[0]._action.value = 'reclass';if(BeforeSubmit()){doReclass('<?=urlencode($rec)?>','<?=$it?>',<?=$op?>,'<?=$_sess?>','<?=insertDone($done)?>');document.forms[0].submit();}" >
		<? }
	}

	/**
     * @return bool
     */
	function CanEdit() {
		$ok = parent::CanEdit();

		$mod = Request::$r->_mod;
		$viewonly = Request::$r->_viewsonly;

		if(isset($viewonly) && $viewonly == 1){
			return !($this->kShowEditState);
		}

		$parentop = Globals::$g->gSecurity->GetListerOperationID(
			$this->_params['entity'], $mod);

		$children = GetPagePermissions($parentop);
		$childrenverbs = array_keys($children);

		if ($ok && $this->state == $this->kShowEditState &&
				in_array($this->kReclassAction, $childrenverbs)
				&& $this->CanReclass($mod)) {
			$this->ShowReclassButtons($this->_params['buttons'],
				$children[$this->kReclassAction]);
		}


		$objId = Request::$r->{Globals::$g->kId};

		if ( $ok && $objId !== '' ) {
			$entity = $this->_params['entity'];
			/** @noinspection PhpUnusedLocalVariableInspection */
			$entityMgr = Globals::$g->gManagerFactory->getManager($entity);
			$Obj = $this->getEntityData($entity, $objId);
			$invbasecurr = ($Obj['BASECURR'] ?: GetBaseCurrency());

			if (!IsMCPEnabled() && $Obj['CURRENCY'] != $invbasecurr) {
				return false;
			}
		}

		return $ok;
	}

	/**
    * @param string $module
    *
    * @return bool
    */
	function CanReclass($module) {
		// check is application is allowed to reclass
		global $kAPid, $kARid, $kSOid, $kPOid;
		$mcenabled = IsMCPEnabled();

		$recordKey = Request::$r->GetCurrentObjectValueByPath('RECORDNO');

		if ( !isset($recordKey) || $recordKey == '' ) { return false; }

		if (!in_array($this->_rectype, array('pi','pa','ri','ra'))) {
		    return false;
		}

		// check if currently it is edit state
		if ($this->_params['state'] != Editor_ShowEditState) {
			return false;
		}

		$modulekey = Request::$r->GetCurrentObjectValueByPath('MODULEKEY');

		// check if the doc is created by either AP, AR, SO or PO modules
		$reclassAllowedMods = array($kAPid, $kARid, $kSOid, $kPOid);
		if (!in_array($modulekey, $reclassAllowedMods)) {
			return false;
		}

		// Donot allow reclass if user preference is not set and if doc posted from SCM and account labels are on
		if ($module == 'ap') {
			if (!(GetPreferenceForProperty($kAPid, 'RECLASS_ALLOWED') == 'Y') ||
				($modulekey == $kPOid && (GetPreferenceForProperty($kAPid, 'LABEL')) == 'true' )) {
				return false;
			}
		} elseif ($module == 'ar') {
			if (!(GetPreferenceForProperty($kARid, 'RECLASS_ALLOWED') == 'true') ||
				($modulekey == $kSOid && (GetPreferenceForProperty($kARid, 'LABEL')) == 'true' )) {
				return false;
			}
		}

		// check if the doc is paid
		if (!self::IsInvoicePaid($recordKey)) {
			$isSCMObj = ($modulekey == $kSOid || $modulekey == $kPOid) ? true : false;
			if($isSCMObj) {
				// For SCM objects not need to check for payment if they are either converted or partially converted
				// This condifition should be overridden for CASH basis company, b'coz bill will hit GL only after actual payment
				if(!IsCompanyAccrual() || !isSCMObjConvOrPartialConv($recordKey)) {
					return false;
				}
			}
			else {
				return false;
			}
		}
        //If all the lines of a transaction is billed then we are not showing the reclassify button.
        if ( $this->IsAllLinesBilled($recordKey) ) {
            return false;
        }
		// check if the doc is reversed
		if (Request::$r->GetCurrentObjectValueByPath('STATE') == 'V') {
			return false;
		}

		// check if the doc has deferred revenue
		if ($this->IsInvoiceDeferred(
			Request::$r->GetCurrentObjectValueByPath('RECORDNO'))) {
			return false;
		}

		// check if the bill is foreign currency bill
		if($mcenabled && Request::$r->GetCurrentObjectValueByPath('CURRENCY') != GetBaseCurrency()) {
			return false;
		}

		// check if the book open for document date
		if (DateCompare(Request::$r->GetCurrentObjectValueByPath('WHENPOSTED'), GetCompanyOpenDate()) < 0) {
			return false;
		}

		// check if the document is in an open PR batch
		$prbatchkey =
			Request::$r->GetCurrentObjectValueByPath('PRBATCHKEY');
		if ($prbatchkey == '') {
			// see if we are returning from an error page
			$prbatchkey = Request::$r->_inbatch;
		}
		if ($prbatchkey == '') {
			return false;
		}

		//Disabling reclassification for quick checks in multi books company
		//This is a temporary fix, till we come up with the correct fix for letting users reclassify quick checks
		if( IsQuickCheckBatch($prbatchkey, $this->_rectype) && IsOperatingMultipleBooks() ){
			return false;
		}

		if (!IsBatchOpen($prbatchkey, $isopen)) {
			return false;
		}
		if ($isopen == 'F') {
			return false;
		}

		if ($module == 'ar') {
			// check if it is using advanced tax schedules
			$taxEngine = GetTaxEngine('ar');
			if ($taxEngine == 'AdvancedTax' || $taxEngine == 'AvaTax') {
				return false;
			} elseif ($taxEngine == 'SimpleTax' &&
					$this->IsInvoiceWithSubtotal(
					Request::$r->GetCurrentObjectValueByPath(
					'RECORDNO'))) {
				// if using tax and subtotals, need to check
				// if tax entry exists for this record
				return false;
			}
		}
		return true;
	}

	/**
    * Shows or hides transaction state. If the transaction is reversed or reversal, its shown.
    *
    * @param array $stateparam
    * @param int   $totalEntered
    */
	function ShowReversalState(&$stateparam,/** @noinspection PhpUnusedParameterInspection */ $totalEntered) {
		if ($stateparam['value'] != 'Reversed' && $stateparam['value'] != 'Reversal') {
			$stateparam['hidden'] = true;
		}
	}

	function InitConstants()
	{
		parent::InitConstants();
        self::invoiceInitConstants($this);
    }

    /**
    * @param InvoiceEditor $clz
    */
    public static function invoiceInitConstants($clz)
    {
		$clz->kActionHandlers['populate'] = array (
			'handler' => 'ProcessPopulateFromLast',
			'states' => array(
				$clz->kShowNewState,
				$clz->kShowEditState
			)
		);

		$clz->kActionHandlers['getdefaults'] = array (
			'handler' => 'ProcessGetDefaults',
			'states' => array(
				$clz->kShowNewState,
				$clz->kShowEditState
			)
		);

		$clz->kActionHandlers['save'] = array (
			'handler' => 'ProcessSaveAction',
			'states' => array(
				$clz->kShowEditState,
				$clz->kEditWarningState,
				$clz->kShowReclassState
			),
            'csrf' => true,
		);

		$clz->kActionHandlers['cancel'] = array (
			'handler' => 'ProcessCancelAction',
			'states' => array(
				$clz->kShowNewState,
				$clz->kShowEditState,
				$clz->kCreateWarningState,
				$clz->kEditWarningState,
				$clz->kShowViewState,
				$clz->kShowReclassState
			)
		);

		$clz->kActionHandlers['reclass'] = array (
			'handler' => 'ProcessReclassAction',
			'states' => array(
				$clz->kShowReclassState,
				$clz->kShowEditState,
				$clz->kInitState
			),
            'csrf' => true,
		);

		$clz->kDefaultVerbActions['reclass'] = $clz->kReclassAction;
		$clz->kRequireVerbForAction['reclass'] = $clz->kReclassAction;
	}

	/**
    * Please override this function in the child class for Populating the Term related values from Last Invoice/Bill...
    *
    * @param array $_params
    */
	function ProcessPopulateFromLast(&$_params) {
	}


	/**
    * @param array $_params
    */
	function ProcessGetDefaults(&$_params) 
	{
		$ok = true;
		$obj =& Request::$r->GetCurrentObject();
		$gManagerFactory = Globals::$g->gManagerFactory;

		$Mgr = $gManagerFactory->getManager($_params['entity']);

		$ok && $entitytype = $Mgr->_schemas[$Mgr->_entity]['entitytype'];

        /** @noinspection PhpUndefinedVariableInspection */
        $entityMgr =  $gManagerFactory->getManager($entitytype);

		$entityinfo = $entityMgr->get($obj[isl_strtoupper($entitytype)]);

		$obj['TERMNAME'] = $entityinfo['TERMNAME'];
		$obj['CREDITLIMIT'] = $entityinfo['CREDITLIMIT'];
		if (GetLabelStatus($Mgr->_schemas[$Mgr->_entity]['recordtype'][0]) && $entityinfo['ACCOUNTLABEL'] != '') {
			$obj['PRENTRY'][0]['GLACCOUNT'] = $entityinfo['ACCOUNTLABEL'];
		} else {
			$obj['PRENTRY'][0]['GLACCOUNT'] = $entityinfo['ARACCOUNT'];
		}

		if (!$ok) {
			/** @noinspection PhpUnusedLocalVariableInspection */
			$entityDesc = $_params['entityDesc'];
			Globals::$g->gErr->addError('AR-0500', __FILE__.":".__LINE__ , "Unable to get Customer Defaults");

			$this->state = $this->kErrorState;
		} else {
			Request::$r->SetCurrentObject($obj);
//			$this->state = $this->kGoBackState;
		}
	}

	/**
     * @param array $_params
     *
     * @return bool
     */
	function ProcessEditAction(&$_params) {
		$entityMgr = Globals::$g->gManagerFactory->getManager($_params['entity']);

		if ($this->ProcessErrorRetrivalAction($entityMgr)) {
			$edittype = Request::$r->_edittype;
			if ($edittype == 'duplicate') {
				$this->state = $this->kShowNewState;
			}
			return true;
		}

		parent::ProcessEditAction($_params);

		return true;
	}

	/**
    * @param array $_params
    *
    * @return bool
    */
	function ProcessReclassAction(&$_params) {

		$entityMgr = Globals::$g->gManagerFactory->getManager($_params['entity']);

		if ($this->ProcessErrorRetrivalAction($entityMgr)) {
			return true;
		}

		Request::$r->_changed = false;
		$objId = Request::$r->{Globals::$g->kId};
		$obj = $this->getEntityData($_params['entity'], $objId);
		if (!$obj) {
			$entityDesc = $_params['entityDesc'];
			Globals::$g->gErr->addIAError(
			    "AR-0501", __FILE__.":".__LINE__ ,
				"Fetching $entityDesc '$objId' failed",
				['ENTITY' => $entityDesc, 'OBJECTID' => $objId]
		    );
			$this->state = $this->kErrorState;
		} else {
			Request::$r->SetCurrentObject($obj);
			$this->state = Editor_ShowReclassState;
		}

		return true;
	}

	/**
    * @param array $_params
    *
    * @return array
    */
	function MergeLayout($_params) {

		$this->AddDyanamicFields($_params);

		return parent::MergeLayout($_params);
	}

	/**
    * @param array $_params
    *
    * @return array
    */
	function Editor_Expand($_params) {

		$gManagerFactory = Globals::$g->gManagerFactory;
        $_do = Request::$r->_do;
		/** @noinspection PhpUnusedLocalVariableInspection */
		$deptMgr = $gManagerFactory->getManager('department');
		/** @noinspection PhpUnusedLocalVariableInspection */
		$locMgr = $gManagerFactory->getManager('location');
		/** @noinspection PhpUnusedLocalVariableInspection */
		$params = array( 'filters' => array (array (array( 'STATUS', 'in', array('active')))));

		$_params = PrRecordEditor::Editor_Expand($_params);

		// for screen field size purpose
		$deptExist = departmentsExist();
		$locExist = locationsExist();

		$mcenabled = IsMCPEnabled();
		$atlas = IsMCMESubscribed();

		// get screensize
		$screensize = GetUserPreferences($prefs, 'SCREENSIZE', '0');

		/** @noinspection PhpUnusedLocalVariableInspection */
		$asize = $msize = $dsize = $lsize = 0;
		if ($_params['entity'] != 'apbill' && $_params['entity'] != 'arinvoice') {
			$asize = 33;
			$msize = 33;
			$dsize = 0;
			$lsize = 0;
		} elseif ($_params['entity'] == 'arinvoice') {
			global $kARid;
			$enabledrevrec = GetPreferenceForProperty($kARid, 'ENABLEREVREC');
			$asize = 19;
			$msize = 23;
			$dsize = 0;
			$lsize = 0;
			if ($enabledrevrec != 'true') {
				$asize += 2;
				$msize += 2;
			}
		} else {
			// $asize = ($screensize == '0' ? ($mcenabled ? 13 : 17) : ($mcenabled ? 29 : 41));
			// $msize = ($screensize == '0' ? ($mcenabled ? 16 : 23) : ($mcenabled ? 29 : 33));
			$asize = ($mcenabled ? 22 : 25);
			$msize = ($mcenabled ? 22 : 28);
			$dsize = 8;
			$lsize = 8;
		}
		if (!$deptExist && !$locExist) {
			$asize += 26;
			$msize += 26;
		} elseif (!$deptExist) {
			$asize += 10;
			$msize += 10;
			$lsize += 6;
		} elseif (!$locExist) {
			$asize += 10;
			$msize += 10;
			$dsize += 6;
		}
		if ($screensize == '1') {
			$asize += ($deptExist ? 7 : 11);
			$msize += ($deptExist ? 7 : 11);
			$asize += ($locExist ? 7 : 11);
			$msize += ($locExist ? 7 : 11);
			$dsize += 6;
			$lsize += 6;
		}
		$findfields = array(
			array('path' => 'GLACCOUNT'),
			array('path' => 'ENTRYDESCRIPTION'),
			array('path' => 'DEPARTMENT'),
			array('path' => 'LOCATION'),
		);
		foreach ($findfields as $ff) {
			$_found = array();
			$this->MatchTemplates($_params, $ff, $_found);
			if ($_found) {
				if ($ff['path'] == 'GLACCOUNT') {
					$_found[0]['type']['size'] = $asize;
				} elseif ($ff['path'] == 'ENTRYDESCRIPTION') {
					$_found[0]['type']['size'] = $msize;
					$_found[0]['numofcols'] = $msize;
				} elseif ($ff['path'] == 'DEPARTMENT') {
					$_found[0]['type']['size'] += $dsize;
				} elseif ($ff['path'] == 'LOCATION') {
					$_found[0]['type']['size'] += $lsize;
					if ($this->atlas && !in_array($_params['entity'], array('apbill','arinvoice'))) {
						foreach ($_found as $key => $val) {
							$_found[$key]['required'] = true;
						}
					}
				}
			}
		}

		if ($atlas && $_do == 'view') {
			$findfield = array('path' => 'BASECURR');
			$_found = array();
			$this->MatchTemplates($_params, $findfield, $_found);
			if ($_found) {
				$_found[0]['hidden'] = false;
			}
		}

		return $_params;
	}

	/**
    * @param array $_params
    */
	function AddDyanamicFields(&$_params) {

		$deptlocfound = false;

		$findfields = array(
			array('path' => 'DEPARTMENT'),
			array('path' => 'LOCATION'),
		);
		foreach ($findfields as $ff) {
			$_found = array();
			$this->MatchTemplates($_params, $ff, $_found);
			if ($_found) {
				$deptlocfound = true;
			}
		}

		if (!IsMCPEnabled() && $_params['entity'] == 'arinvoice') {
			$deptlocfound = false;
		}

		$deptExist = departmentsExist();
		$locExist = locationsExist();

		if (!$deptlocfound && ($deptExist || $locExist)) {
			foreach ($_params['pages'] as $pgindx => $page) {
				if (!strcmp($page['title'],"Header")) {
					foreach ($_params['pages'][$pgindx]['fields'] as $fldindx => $field) {
						if (!strcmp($field['path'],"PRENTRY")) {
							if ($deptExist) {
								$_params['pages'][$pgindx]['fields'][$fldindx]['columns'][] = array('path' => 'DEPARTMENT');
							}
							if ($locExist) {
								$_params['pages'][$pgindx]['fields'][$fldindx]['columns'][] = array('path' => 'LOCATION');
							}
						}
					}
				}
			}
		}


//		if ($_do != 'view' && $_params['entity'] == 'apbill' && $this->mcpEnabled) {
//			foreach ($_params['pages'] as $pgindx => $page) {
//				if (!strcmp($page['title'],"Header")) {
//					foreach ($_params['pages'][$pgindx]['fields'] as $fldindx => $field) {
//						if (!strcmp($field['path'],"PRENTRY")) {
//							$_params['pages'][$pgindx]['fields'][$fldindx]['columns'][] = array('path' => 'ENTRYCURRENCY', 'hidden' => true );
//							$_params['pages'][$pgindx]['fields'][$fldindx]['columns'][] = array('path' => 'ENTRYEXCHRATEDATE', 'hidden' => true );
//							$_params['pages'][$pgindx]['fields'][$fldindx]['columns'][] = array('path' => 'ENTRYEXCHRATE', 'hidden' => true );
//							$_params['pages'][$pgindx]['fields'][$fldindx]['columns'][] = array('path' => 'ENTRYEXCHRATETYPE', 'hidden' => true );
//						}
//					}
//				}
//			}
//		}

	}

	/**
    * @param array $_layout
    *
    * @return int|mixed|null|string
    */
	function FigureOutNumOfRows($_layout) {
		//kludge to make sure that the numofrows is set to actual for edit and view
		if ($this->state == $this->kShowViewState) {
			$_layout['readonly'] = true;
		} else {
			/** @var PREntryEditor $prentryEditor */
			$prentryEditor = GetEntityEditor('prentry');
			$default = $_layout['numofrows'];
			$_layout['numofrows'] = $prentryEditor->GetNumOfLineitemRows($default);

		}

		if (Request::$r->_edittype == $this->kReclassAction) {
			$obj =& Request::$r->GetCurrentObject();
			$this->numofrows = count(
				EntityManager::AccessByPath($obj,
				$_layout['path']));
		} else {
			$this->numofrows = Editor::FigureOutNumOfRows($_layout);
		}

		return $this->numofrows;
	}

	/**
     * @return array
     */
	function GetDistTemplates() {

		$sel['selects'][] = 'TEMPLNAME';
		$sel['selects'][] = 'LOCATION';
		$sel['selects'][] = 'PCTVAL';

		$distMgr = Globals::$g->gManagerFactory->getManager('disttemplentry');

		$templraw = $distMgr->GetList($sel);

		$templs = Array();

		foreach ($templraw as $traw) {
			$newtemp = array();
			$newtemp['ENTLOC'] = $traw['LOCATION'];
			$newtemp['PCTVAL'] = $traw['PCTVAL'];

			$templs[$traw['TEMPLNAME']][] = $newtemp;
		}
		return $templs;
	}

    /**
     * Add Javascript and CSS to the page.  Subclasses really should call back to their parent.
     *
     * @param bool $addYuiCss include the YUI css files
     */
	function showScripts($addYuiCss = true)
	{

		parent::showScripts($addYuiCss);

		$_mod = Request::$r->_mod;

		$_sess = Session::getKey();

		$_r = Request::$r->_r;
        $_op = Request::$r->_op;

		$js_abs_alloc_items = "";

		if ( $this->useAllocation == 'true' ) {
			$allocMgr = Globals::$g->gManagerFactory->getManager('allocation');
			$absAllocs = $allocMgr->getAbsoluteAllocationsTotals();

			// Absolute allocations
			$abs_alloc_items=array();
			for($k=0; $k < count($absAllocs) ; $k++) {
				$abs_alloc_items[] = "['".str_replace("'","\'",$absAllocs[$k]['ALLOCATIONID'])."','".$absAllocs[$k]['TOTAL']."']";
			}
			$abs_alloc_items = "[" . join(',',$abs_alloc_items) . "]";

			$js_abs_alloc_items = "\n var absalloc_arr = ".$abs_alloc_items.";\n";

			}
		?>
		<script>
			var tocheck = '<? echo $this->_params['entity']; ?>';
			var creditlimitcheck = '<? echo GetCreditLimitCheck(); ?>';
			var onholdcheck = '<? echo GetOnHoldCheck(); ?>';
			var warnonduplicate = '<? echo GetWarnOnDuplicate(); ?>';
			var ipdatetype = '<? echo GetUserDateFormat(); ?>';
			var crlimit = '';
			var entitydue = '';
			var needMCP = <? echo ($_mod=='ap')?1:0;?>;
			var flgReleaseToPayPerm = '<? echo GetReleaseToPayFlag('Bill'); ?>';
			var op = '<?=$_op;?>';
			var rec = '<?=$_r;?>';
			var sess = '<?=$_sess;?>';

			// Common fields for all apar editors
			var datefmt = '<? echo GetUserDateFormat(); ?>';

			//

		</script>
		<script src="../resources/js/apar.js"></script>
		<script src="../resources/js/invoice.js"></script>
		<script>
		<? BuildNonStandardPeriodJSArray(); ?>
		<?= $js_abs_alloc_items; ?>
		<? if (($this->state == Editor_ShowEditState) && isset($this->_params['buttons']['reclassbutton'])) { ?>
				function doReclass(rec,it,op,sess,done) {
					document.forms[0].elements['_do'].value = 'reclass';
					document.forms[0].action = "editor.phtml?.r="+rec+"&.it="+it+"&.do=reclass&.op="+op+"&.sess="+sess+"&.done="+done;
				}
		<? } ?>

		<?
			$splitFlds = array('amount','departmentid','locationid','spreleasetopay');
			$splitFlds = array_merge($splitFlds,$this->ownerObjectDimensions);
			$edittype = Request::$r->_edittype;
			for ($i=0;$i<$this->numofrows;$i++) {
				$fld_splitcnt = "_obj__PRENTRY($i)__SPLITCOUNT";
				$splitcnt = Request::$r->$fld_splitcnt;
				if (isset($splitcnt) && $splitcnt) {

					/** @noinspection PhpUndefinedVariableInspection */
					$jsscript .= "if (rownum == $i) {\n";
					$jsscript .= "CreateHidden('$fld_splitcnt','$splitcnt');\n";
					$jsscript .= "}\n";

					for ($split=0;$split<$splitcnt;$split++) {
						foreach ($splitFlds as $splitFld) {
							$fld_name = "_obj__PRENTRY($i)__SPLIT($split)(".isl_strtoupper($splitFld).")";
							/** @noinspection PhpUnusedLocalVariableInspection */
							$value = '';

							$value = Request::$r->$fld_name;
							if($splitFld == 'releasetopay') {
								if ($edittype == 'duplicate' ) {
									$value = 'false';
								}
							}
							$jsscript .= "if (rownum == $i) {\n";
							$jsscript .= "CreateHidden('{$fld_name}','{$value}');\n";
							$jsscript .= "}\n";
						}
					}
				}
			}
			/** @noinspection PhpUndefinedVariableInspection */
			$jsscript .= "return;\n";
		?>
		function refreshHiddens(rownum) {
			<?=$jsscript;?>
		}

		</script>
		<?
	}

	//function ShowGeneralHiddenFields()
	function ShowGeneralHiddenFields() {
		parent::ShowGeneralHiddenFields();?>
		<INPUT type="hidden" name="_kNoWarn" value="<? echo Request::$r->_kNoWarn; ?>">
		<INPUT type="hidden" name="_kNoDupl" value="<? echo Request::$r->_kNoDupl; ?>">
		<INPUT type="hidden" name="_batch" value="<? echo Request::$r->_batch; ?>">
		<INPUT type="hidden" name="_r" value="<? echo Request::$r->_r; ?>">
		<INPUT type="hidden" name='_edittype' value="<? echo Request::$r->_edittype; ?>">
	<?}

	/**
     * Override the ProcessCreateAction to unset the value from $obj, so that in Save & New it will not load with pre data
     *
     * @param array $_params  the current param value
     *
     * @return bool|array $_params
     */
	function ProcessCreateAction(&$_params) 
	{
		$gManagerFactory = Globals::$g->gManagerFactory;
		$entityMgr = $gManagerFactory->getManager($_params['entity']);

		if ($this->ProcessErrorRetrivalAction($entityMgr)) {
			return true;
		}

		global $gURLs;
		if ($_params['entity'] == 'arinvoice') {
			$obj =& Request::$r->GetCurrentObject(true);
		} else {
			$obj =& Request::$r->GetCurrentObject();
		}

		$obj = $this->DoRefreshAction($_params,$obj);

        	$entityMgr->setWarningValidation();

		$ok = $this->PrepareInputValues($_params, $obj);
        $ok = $ok && $this->validatePlatformData($obj);

		$ok = $ok && $entityMgr->add($obj);
		$entityDesc = $_params['entityDesc'];

		if (!$ok) {
			$gErr = Globals::$g->gErr;
			if ($gErr->hasWarnings()){
				$warnings = array();
				$gErr->getWarningsList($warnings);
				$this->warningmessage = implode("<br>", $warnings);
				$this->state = $this->kCreateWarningState;
			}

			if ($gErr->hasErrors()){
				$gErr->addIAError("AR-0502", __FILE__.":".__LINE__ ,
								"Creating $entityDesc failed", ['ENTITY_DESC' => $entityDesc]);
				$this->state = $this->kErrorState;
			}
		}
		else {
            /** @var URLS $gURLs */
			//Give the message to show the invoice number just created
			$confirmation = I18N::getSingleToken("IA.THE_ENTITY_RECORDID_WAS_CREATED_SUCCESSFULLY",[["name" => "ENTITY_DESC", "value" => $entityDesc],["name" => "RECORDID", "value" => $obj['RECORDID']]]);
			$this->SetMessage($confirmation);

			$after = Request::$r->after;

			$obj =& Request::$r->GetCurrentObject();
			//$this->LogAuditRecord($_params, 'M', true, 'create');

            if ($after == 1) {
                Request::$r->_currentlayer = '';
				Request::$r->_changed = 'false';

				$mod = Request::$r->_mod;
				$nobj['WHENCREATED'] = $obj['WHENCREATED'];
				$nobj['WHENPOSTED'] = $obj['WHENPOSTED'];
				if ($obj['WHENMODIFIED'] != '') {
					$nobj['WHENMODIFIED'] = $obj['WHENMODIFIED'];
				}

				$entity = $_params['entity'];
				$id = 'k'.strtoupper($mod).'id';

				GetModulePreferences($$id, $prefs);

				$autopopulateentity = 'AUTOPOPULATE_'.isl_strtoupper($entity);

				if( $prefs[$autopopulateentity] == 'Y' ){

					//logic is built into retain the prentry line items value also
					$retainval = array(
					        'apbill'=> array(
											'VENDOR',
										//	'PAYMENTPRIORITY',
										//	'TERMNAME',
										//	'PRENTRY.GLACCOUNT'
										),
										'arinvoice'=> array(
											'CUSTOMER',
										)
							);

					$prcnt = 0;
					foreach($retainval[$entity] as $field){
						list($prentry, $line) = explode('.', $field);
						if(!$line){
							$nobj[$field] = $obj[$field];
						}else{
							//to retain prentry line items
							foreach($obj[$prentry] as $efield){
								foreach($efield as $prkey => $prval){
									if($prkey == $line){
										$nobj[$prentry][$prcnt][$line] = $prval;
										$prcnt++;
									}
								}
							}
						}
					}
				}

                if ($this->state != $this->kErrorState) {
                    Request::$r->SetCurrentObject($nobj);
                    $this->state = $this->kShowNewState;
                }
			} elseif ($this->GetPreference($_params, "Create.RequireConfirmation")) {
				$this->state = $this->kConfirmState;
			} elseif ($gURLs->HasReturnPath())  {
				$next = Request::$r->next;
				if($next == 1) {
					$this->SetPrintRecVar($obj['RECORD#']);
				}
				$this->state = $this->kGoBackState;
			} elseif ($_params['popup']) {
				$this->state = $this->kCloseState;
			}
			else {
				$this->state = $this->kGoHomeState;
			}
		}

		//To populate default values for new bill
		if ($this->state == $this->kShowNewState && $this->mcpEnabled) {
			$obj =& Request::$r->GetCurrentObject();
			$nobj['WHENCREATED'] = $obj['WHENCREATED'];
			$nobj['WHENPOSTED'] = $obj['WHENPOSTED'];
			$exchMgr = $gManagerFactory->getManager('exchangeratetypes');
			$rtype = $exchMgr->GetDefaultExchangeRateType();
			$nobj['EXCHRATEDATE'] = GetCurrentDate();
			$nobj['EXCHRATETYPE'] = $rtype[0]['NAME'];
			Request::$r->SetCurrentObject($nobj);
		}

        // We must change the state here (instead of above) because there are conditional modifications to $nobj
        if ($ok && $this->state === $this->kShowNewState && Request::$r->after == 1) {
            if (!empty(Request::$r->_postandnew)) {
                // Since we now redirect for post and new, we must put these re-populate fields into the redirect url
                $this->postAndNew = true;
            	/** @noinspection PhpUndefinedVariableInspection */
            	$this->postAndNewPopData = $nobj;
                $this->state = $this->kGoBackState;
            } else {
                logToFileWarning("Post & New request variable '_postandnew' not found in class '"
                    . get_class($this) . "' @"
                    . __FILE__ . ':' . __LINE__ . "\n");
            }
        }

        return true;
	}

	/**
     * @param array $_params
     *
     * @return bool
     */
	function ProcessEditNewAction(&$_params) {
		parent::ProcessEditNewAction($_params);

		//To store default value only at new mode not at the time of error retrival
		$errorRecoveryTime = Request::$r->_errorTimeStamp;
		if (!isset($errorRecoveryTime) || $errorRecoveryTime=='') {
			$obj =& Request::$r->GetCurrentObject();
			$obj['WHENCREATED'] = GetCurrentDate();
			if ($this->mcpEnabled) {
				$exchMgr = Globals::$g->gManagerFactory->getManager('exchangeratetypes');
				$rtype = $exchMgr->GetDefaultExchangeRateType();
				$obj['EXCHRATEDATE'] = GetCurrentDate();
				$obj['EXCHRATETYPE'] = $rtype[0]['NAME'];
			}
			if ($obj['WHENDISCOUNT'] != '') {
				$obj['WHENDISCOUNT'] = FormatDateForDisplay($obj['WHENDISCOUNT']);
			}
			Request::$r->SetCurrentObject($obj);
		}

		return true;
	}

	/**
    * @param array $_params
    */
	function ProcessSaveAction(&$_params) {
		/** @var InvoiceManager $entityMgr */
		$entityMgr = Globals::$g->gManagerFactory->getManager($_params['entity']);

		$objId = Request::$r->{Globals::$g->kId};
		if ($_params['entity'] == 'arinvoice') {
			$obj =& Request::$r->GetCurrentObject(true);
		} else {
			$obj =& Request::$r->GetCurrentObject();
		}
		$obj = $this->DoRefreshAction($_params,$obj);
		$obj['RECORDNO'] = Request::$r->_r;
		$obj['ONHOLD'] = ($obj['ONHOLD'] == 'true' || $obj['ONHOLD'] == 'T') ? 'T' : 'F';

		$entityMgr->setWarningValidation();

		$ok = $this->PrepareInputValues($_params, $obj);
        $ok = $ok && $this->validatePlatformData($obj);

		//get the edittype
		$edittype = Request::$r->_edittype;

		if ($edittype ==''){
			$ok = $ok && $entityMgr->set($obj);
		} else if ($edittype == $this->kPartialEditAction) {
			// if _edittype is found from the request and the value is 'partialedit', call SetPartial function from entitymanager
			$ok = $ok && $entityMgr->SetPartial($obj);
		} else if ($edittype == $this->kReclassAction) {
			$ok = $ok && $entityMgr->SetReclass($obj);
		}

		$entityDesc = $_params['entityDesc'];
		if (!$ok) {
			global $gErr;
			if ($gErr->hasWarnings()){
				$warnings = array();
				$gErr->getWarningsList($warnings);
				$this->warningmessage = implode("<br>", $warnings);
				$this->state = $this->kEditWarningState;
			}

			if ($gErr->hasErrors()){
				$gErr->addIAError("AR-0503", __FILE__.":".__LINE__ ,
					"Updating $entityDesc '$objId' failed", ['DESC' => $entityDesc, 'ID' => $objId]);
				$this->state = $this->kErrorState;
			} else {
				$next = Request::$r->next;
				if($next == 1) {
					$this->SetPrintRecVar($obj['RECORDNO']);
				}
			}
		} else if ($this->GetPreference($_params, "Update.RequireConfirmation")) {
			$confirmation = I18N::getSingleToken("IA.THE_ENTITY_WAS_SAVED_SUCCESSFULLY",[["name" => "ENTITY_DESC", "value" => $entityDesc]]);
			$this->SetMessage($confirmation);
			$this->state = $this->kConfirmState;
		} else if (Globals::$g->gURLs->HasReturnPath())  {
			$next = Request::$r->next;
			if($next == 1) {
				$this->SetPrintRecVar($obj['RECORDNO']);
			}
			$this->state = $this->kGoBackState;
		} else if ($_params['popup']) {
			$this->state = $this->kCloseState;
		} else {
			$this->state = $this->kGoHomeState;
		}
	}

	/**
    * @param array $_params
    */
	function ProcessShowEditState(&$_params) {

		global $kINVid, $kSOid, $kPOid, $kAPid, $kARid;

		$gManagerFactory = Globals::$g->gManagerFactory;

		parent::ProcessShowEditState($_params);

		if (Globals::$g->gErr->hasWarnings()) {
			return;
		}

		/** @noinspection PhpUnusedLocalVariableInspection */
		$ok = true;
		$viewmode = false;

		Request::$r->_changed = false;
		$objId = Request::$r->{Globals::$g->kId};
		$inbatch = Request::$r->_batch;

		$entity = $_params['entity'];


		//get the specific batch manager
		if ($entity=='apbill'){
			$batchentity  =  'APBillBatch';
		} else if($entity=='apadjustment'){
			$batchentity  =  'APAdjustmentBatch';
		} else if($entity=='arinvoice'){
			$batchentity  =  'ARInvoiceBatch';
		} else if($entity=='aradjustment'){
			$batchentity  =  'ARAdjustmentBatch';
		}

		$entityMgr = $gManagerFactory->getManager($entity);
		/** @noinspection PhpUndefinedVariableInspection */
		$batchMgr = $gManagerFactory->getManager($batchentity);

		$rawObj = $entityMgr->GetRaw($objId);
        /** @noinspection PhpUnusedLocalVariableInspection */
        $action = Request::$r->_action;
		$rawObj = $rawObj[0];
		$this->_rectype = $rawObj['RECORDTYPE'];

		// make editor view only if the object is not owned
		if (IsMultiEntityCompany() && !$entityMgr->IsOwnedEntity()) {
			$viewmode = true;
		}

		if (!isset($inbatch) || $inbatch == '') {
			$inbatch = $rawObj['PRBATCHKEY'];
		}
		Request::$r->_inbatch = $inbatch;

		$batch = $batchMgr->GetRaw($inbatch);
		$batch = $batch[0];

		//set the Document_ID
		if (in_array($rawObj['MODULEKEY'], array($kINVid, $kSOid, $kPOid))) {
			$this->_docLinkType = $rawObj['MODULEKEY'];
		}

		// SET THE VIEWMODE BASED ON following CRITERIAS.
		$viewmode = ( in_array($rawObj['MODULEKEY'],
				array($kINVid, $kSOid, $kPOid))
				|| $rawObj['STATE'] == 'V'
				|| $rawObj['TOTALPAID'] != 0
				|| $rawObj['TOTALSELECTED'] != 0
				|| $batch['OPEN'] == 'F'
				|| $batch['NOGL'] == 'T'
				|| $viewmode == true ) ;

		// SET THE VIEWMODE BASED ON THE PAYMENT RELATIONSHIP TOO.
		if ( !$viewmode ) {
			if (self::IsInvoicePaid($rawObj['RECORD#'])) {
				$viewmode = true;
			}
		}

		// WAS THIS BILL GENERATED FROM A PAYOFF OF CREDIT CARD
		// CHARGES?
		$isCCLiabilityXfer = false;
		if ($rawObj['RECORDTYPE']=='pi') {
			$ccMgr = $gManagerFactory->getManager('creditcard');
			$isCCLiabilityXfer = $ccMgr->IsCCLiabilityXfer(
				$rawObj['RECORD#']);
			if ($isCCLiabilityXfer) {
				$viewmode = true;
			}
		}

		if ($viewmode) {
			$allfields = array();
			$this->MatchTemplates($_params,array('_func' => 'Field'), $allfields);

			// partial edit is only for module keys 4.AR($kARid), 3.AP($kAPid), $kSOid, $kPOid
			if (!$isCCLiabilityXfer && in_array($rawObj['MODULEKEY'], array($kAPid, $kARid, $kSOid, $kPOid))) {
				// this must be partial edittype, so set it
				// in request for the 'save' action to know
				/** @noinspection PhpUnusedLocalVariableInspection */
				$edittype = '';
				$_edittype = Request::$r->_edittype;

				// need to also check we are coming from error in the reclass screen
				if ($_edittype == 'reclass' || $this->state == Editor_ShowReclassState) {
					if ($this->state != Editor_ShowReclassState) {
						$this->state = Editor_ShowReclassState;
					}
					$edittype = $this->kReclassAction;
				} else {
					$edittype = $this->kPartialEditAction;
				}
				Request::$r->_edittype = $edittype;

				// set fields to readonly only
				$this->SetFieldsToReadOnly($allfields,
					$edittype);

				// to set the help file
				/** @noinspection PhpUnusedLocalVariableInspection */
				$ok = $this->SetHelpID($_params);
			} else {
				$this->state = $this->kShowViewState;

				// set the help file
				/** @noinspection PhpUnusedLocalVariableInspection */
				$ok = $this->SetHelpID($_params);
			}
		} else {
			$this->state = Editor_ShowEditState;
			// set the help file
			/** @noinspection PhpUnusedLocalVariableInspection */
			$ok = $this->SetHelpID($_params);
		}

	}

	/**
     * @param array $_params
     *
     * @return int
     */
	function _calctotalcolumns(&$_params){
		$cnt = parent::_calctotalcolumns($_params);
		return ++$cnt;

	}

	/**
    * @param array  $_fields
    * @param string $_pos
    */
	function ShowMultilineRefresh(&$_fields, $_pos) {
		if ($this->state == $this->kShowViewState ||
				$this->state == $this->kShowReclassState) {
			$_fields['norefreshlink'] = true;
		}
		parent::ShowMultilineRefresh($_fields, $_pos);
	}

	/**
    * @param array $_field
    */
	function ShowSimpleFieldValue(&$_field) {

		if ($_field['path'] == 'ALLOCATION' && !$_field['noredist']) {
            $_r = Request::$r->_r;
			$mod = Request::$r->_mod;
			$edittype = Request::$r->_edittype;
			if ($this->state != 'showview' && (!in_array($edittype, array($this->kPartialEditAction, $this->kReclassAction)))) {
				$do = ($_r && !isset($_field['value']))?'create':Request::$r->_do;
                $action = Request::$r->_action;
				$_field['assists'][] = array(
					'customlink'	=> "RedistributeAllocation('".$_field['layer']."','".$_field['form']."','".$_field['rownum']."', '$do', '');",
					'text'				=> 'redist',
					'statusmsg'			=> "Redistribute Amount",
				);

				$flagReleaseToPay = GetReleaseToPayFlag();
				if ( ( $_r || $action == 'populate') &&
				     ( ($flagReleaseToPay && $mod == 'ap' && $_field['value'] != '') || ($_field['value'] == 'Custom') )
				   ) {
					$recKey = ($action == 'populate') ? Request::$r->_lastRecKey : $_r;

					$exchrate = Request::$r->GetCurrentObjectValueByPath('EXCHRATE');
					$needMCP = (isset($exchrate) && $exchrate!='');

					/** @var PREntryManager $prEntryMgr */
					$prEntryMgr = Globals::$g->gManagerFactory->getManager($this->getEntityMgr()->_entity.'item');
					$lineno = $_field['rownum'];

					if ($flagReleaseToPay) {
						$allocKey = 0;
						$prentries = $prEntryMgr->GetEntriesForLineno($recKey, $lineno+1);
						if (count($prentries)) {
							$allocKey = $prentries[0]['ALLOCATIONKEY'];
						}

						$customEntries = $prEntryMgr->GetEntriesForAlloc($allocKey, $recKey, $lineno+1, $needMCP);
					} else {
						$customEntries = $prEntryMgr->GetEntriesForCustomAlloc($recKey,$lineno+1, $needMCP);
					}

					if ($splitcnt = count($customEntries)) {
						$jsscript = "<SCRIPT language='JavaScript'>\n";
						$starttag = "<input type='hidden' name='";
						$midtag = "' value='";
						$endtag = "' >";
						$fld_splitcnt = "_obj__PRENTRY($lineno)__SPLITCOUNT";
						$tag_splitcnt = $starttag.$fld_splitcnt.$midtag.$splitcnt.$endtag;
						echo $tag_splitcnt;
						$jsscript .= "CreateHiddenEx('$fld_splitcnt','$splitcnt');\n";

						/** @noinspection PhpUndefinedVariableInspection */
						$splitchanged = ($allocKey == 0) ? '' : 'N';
						$fld_splitchanged = "_obj__PRENTRY($lineno)__SPLITCHANGED";
						$tag_splitchanged = $starttag.$fld_splitchanged.$midtag.$splitchanged.$endtag;
						echo $tag_splitchanged;
						$jsscript .= "CreateHiddenEx('$fld_splitchanged','$splitchanged');\n";

						$splitFlds = array('amount','departmentid','locationid','spreleasetopay');
						if ( !empty($this->ownerObjectDimensions) ) {
							$splitFlds = array_merge($splitFlds,$this->ownerObjectDimensions);
						}
						$fld2ResRel = array(
						    'departmentid'	=>	'department',
						    'locationid'	=>	'location',
						    'projectid'		=>	'project',
						    'employeeid'	=>	'employee',
						    'customerid'	=>	'customer',
						    'classid'		=>	'class',
						    'itemid'		=>	'item',
						    'vendorid'		=>	'vendor',
						);
						for ($split=0;$split<$splitcnt;$split++) {
							foreach ($splitFlds as $splitFld) {
								$fld_name = "_obj__PRENTRY($lineno)__SPLIT($split)(".isl_strtoupper($splitFld).")";
								$value = '';

								if ($object = $fld2ResRel[isl_strtolower($splitFld)]) {
									$keyVal = $customEntries[$split][isl_strtoupper($object).'ID'];
									if(isset($keyVal) && $keyVal != ''){
										$nameVal = $customEntries[$split][isl_strtoupper($object).'NAME'];
										$value = $keyVal.'--'.$nameVal;
									}
								} else {
									$value = $customEntries[$split][isl_strtoupper($splitFld)];
									if($splitFld == 'releasetopay') {
										if ($edittype == 'duplicate' ) {
											$value = 'false';
										} else {
											$value = ($value == 'T') ? 'true' : 'false';
										}
									}

								}
								$tag = $starttag.$fld_name.$midtag.$value.$endtag;
								echo $tag;
								$jsscript .= "CreateHiddenEx('{$fld_name}','{$value}');\n";
							}
						}
						$jsscript .= "</SCRIPT>\n";
					}
					/** @noinspection PhpUndefinedVariableInspection */
					echo $jsscript;
				}
			}
			else if (isset($_field['value']) && $_field['value'] != '') {
				$_field['assists'][] = array(
					'customlink'	=> "RedistributeAllocation('".$_field['layer']."','".$_field['form']."','".$_field['rownum']."', 'view', '');",
					'text'				=> 'redist',
					'statusmsg'			=> "View Amount Distribution",
				);
			}
		}

		// do we need to add view payment details link for totalpaid?
		if( ($_field['path'] =='TOTALPAID' || $_field['path'] =='TRX_TOTALPAID') && $_field['value'] != 0 && $this->showPaymentHistory === true ) {
			$url = $this->getPaymentDetailsUrl($_field);
			if ( $url != '' ) {
				$_field['assists'][] = array(
					'customlink' => "Launch('$url', 'View_Details', 1000, 300)",
					'text'	=> 'View Details',
					'statusmsg' => "View Details",
				);
			}
		}

		parent::ShowSimpleFieldValue($_field);


	}

	/**
    * @param string $msg
    */
	function PrintOnLoad($msg = "") {
		parent::PrintOnLoad();
			$entity = Request::$r->entity;
			$mod = Request::$r->_mod;
            $action = Request::$r->_action;;
			// Call AutoFill to default vendor/customer data
			// only if save click in add mode and if no duplicate warning
			// is set for bill/invoice number and non-empty vendor/customer
			if ($this->state == $this->kShowNewState && $action == 'create' && $entity == 'apbill' && $mod == 'ap') {
				echo "if(document.forms[0]._kNoWarn.value != 'true' && document.forms[0]._kNoDupl.value != 'true' &&    document.forms[1].elements['_obj__VENDOR'].value != '') {";
				echo "AutoFill('Layer0','Layer0_form','VENDOR','','');";
				echo "}";
			}else if ($this->state == $this->kShowNewState && $action == 'create' && $entity == 'arinvoice' && $mod == 'ar') {
				echo "if(document.forms[0]._kNoWarn.value != 'true' && document.forms[1].elements['_obj__CUSTOMER'].value != '') {";
				echo "AutoFill('Layer0','Layer0_form','CUSTOMER','','');";
				echo "}";
			}

			if ($msg != "") {

			//If preference is set to no duplicates then don't let the user proceed forward
			echo "if(document.forms[0]._kNoDupl.value == 'true') {";
				echo " alert('A Bill with the same number already exists. Please use a different number');";
				echo " document.forms[0]._action.value='create';";
				echo " document.forms[1].elements['_obj__RECORDID'].focus();";
			echo "}";

			//If there is a warning then show the warning and decide to continue or not based on the response
			echo "if(document.forms[0]._kNoWarn.value == 'true') {";
			echo " if(confirm('$msg')) { ";
			echo " SetNoCheckRequired(); if(BeforeSubmit()){ document.forms[0]._action.value='create';document.forms[0]._kNoWarn.value = 'true'; document.forms[0].submit();}";
			echo " }else { ";

			echo " document.forms[0]._kNoWarn.value = ''; ";
			echo " } ";
			echo "}";
		}
	}

	/**
     * @return bool
    */
	function supportsTabOnEnterKey() {
		return true;
	}

	/**
    * @param array  $_params
    * @param bool   $autobatch
    * @param bool   $fromManageBatches
    * @param int    $batch
    * @param string $module
    */
	function ShowGLPostingDateField(&$_params, $autobatch, $fromManageBatches, $batch, $module) {

		//Flag to decide whether GL posting date to be displayed
		$flag = ( GetShowGLPostingDate($module) && $autobatch && (IsCompanyAccrual() || IsOperatingMultipleBooks()) ? true : false );

		if ($flag) {
			$_params['hidden'] = false;
			if ($fromManageBatches) {
				$_params['readonly'] = true;
			}
            $action = Request::$r->_action;
			$errorRecoveryTime = Request::$r->_errorTimeStamp;
			if ($this->state == $this->kShowNewState && (!isset($errorRecoveryTime) || $errorRecoveryTime=='') && $action !='populate') {
				if (!$fromManageBatches) {
					if ($this->autoBatchFrequency == 'M') {
						$lastDate = GetCompanyLastDayOfCurrentPeriod();
						if (!isset($_params['value']) || $_params['value'] == '') {
							$_params['value'] = $lastDate;
						}
					} elseif (!isset($_params['value']) || $_params['value'] == '') {
						$_params['value'] = GetCurrentDate();
					} else {
						/** @noinspection PhpSillyAssignmentInspection */
						$_params['value'] = $_params['value'];
					}
				} else {
					$qry = "select created from prbatch where cny# = :1 and record# = :2";
					$param[0] = $qry;
					$param[1] = GetMyCompany();
					$param[2] = $batch;
					$res = QueryResult($param);
					$_params['value'] = $res[0]['CREATED'];
				}
			}
		}
	}

	/**
    * @param array $_params
    */
	function SelectProcess(&$_params) {
		switch ($_params['state']) {
			case Template_EditWarningState:
			case Editor_ShowReclassState:
			case Editor_ShowEditState:
				$_params['_func'] = 'Editor';
				$this->ProcessShowEditState($_params);
				break;
			case Editor_ShowViewState:
				$_params['_func'] = 'Viewer';
				$_params['layout']['readonly'] = true;
				break;
			case Editor_ShowNewState:
				$_params['_func'] = 'Creator';
				break;
			case Editor_ConfirmState:
				$_params['_func'] = 'Confirmer';
				break;
			case Editor_DeliverState:
				parent::SelectProcess($_params);
				break;
		}
	}

	/**
    * @param array $_params
    */
	function Editor_Show(&$_params)
	{
		if (!is_object($this->entityMgr)) {
			$this->entityMgr = Globals::$g->gManagerFactory->getManager(
				$_params['entity']);
		}
		$this->_params =& $_params;

		if ($this->state == Editor_ShowReclassState) {
			$this->ShowEdit();
		} else {
			parent::Editor_Show($_params);
		}
        }

	/**
    * @param array  $allfields
    * @param string $searchType
    */
	function SetFieldsToReadOnly(&$allfields, $searchType) {
		global $kSOid, $kPOid;
		$num_fields = count($allfields);
		$reclassAllowedMods = array($kSOid, $kPOid);

		$mod = ($this->state == Editor_ShowReclassState) ? '' : $this->_docLinkType;
		for ($num=0; $num < $num_fields; $num++) {
			$field =& $allfields[$num];

			if (!$field['readonly'] && in_array($searchType,
					array($this->kPartialEditAction,
					$this->kReclassAction))) {
				$field['readonly'] = !$this->getEntityMgr()->IsPartialEditField($field['path']);
				// Except in Reclass state partial edit fields should be readonly for SCM transactions
				if(!empty($mod) && in_array($mod, $reclassAllowedMods)) {
					$field['readonly'] =  true;
				}
			}
			if ($field['readonly'] &&
					$searchType == $this->kReclassAction) {
				$field['readonly'] = !$this->getEntityMgr()->IsReclassField($field['path']);
				//Change the Location Field to ReadOnly if bank Restriction there
				if($field['path'] == 'LOCATION'){
					$this->changeLocationFieldState($field);
				}
			}
		}
	}

	/**
    * @param array $field
    */
	function changeLocationFieldState(&$field){
		$recordKey = Request::$r->GetCurrentObjectValueByPath('RECORDNO');
		$paymentInfo = GetAllAssociatedPaymentLines($recordKey);
		$br = ValidateBRReclass($paymentInfo);
		if(!$br){
			$field['readonly'] = true;
		}
	}

	/**
    * @param int $reckey
    *
    * @return bool
    */
	public static function IsInvoicePaid($reckey) {
		$qry = "select count(1) count from prentrypymtrecs where ";
		// commenting state = 'C' clause - as if we've a payment request with one of the bills,
		// that got paid by its own inline credit, it'll still not have its prrecord.TOTALSELECTED,
		// updated with inline credit application and hence,
		// line::812 checks will still have $viewmode = false, also for this partial,
		// payment case of inline credit, pymtrecs.STATE IS NULL and not 'C'
		// So, No need to check state = 'C'
		// this will ensure we get such bill lines in view mode when we edit it
		//$qry .= "state = 'C' and ";
		$qry .= "cny# = :1 and (recordkey = :2 or paymentkey = :2 )";
		$res = QueryResult(array($qry, GetMyCompany(), $reckey));
		if ($res[0]['COUNT'] > 0) {
			return true;
		}
		return false;
	}

	/**
    * @param int $reckey
    *
    * @return bool
    */
	function IsInvoiceDeferred($reckey) {
		$qry = "select count(1) count from prentry where ";
		$qry .= "deferrevenue = 'T' and ";
		$qry .= "cny# = :1 and recordkey = :2 ";
		$res = QueryResult(array($qry, GetMyCompany(), $reckey));
		//eppp_p($res);

		if ($res[0]['COUNT'] > 0) {
			//eppp_p('returning true');
			return true;
		}
		//eppp_p('returning false');
		return false;
	}

	/**
     * @param array $_params
     */
	function ShowCopyButtons($_params) {
		$state = Request::$r->GetCurrentObjectValueByPath('STATE');
		if (isset($_params['state']) &&	($state == 'Reversed' || $state == 'Reversal') ) {
			unset($_params['copybutton']);
		}
		parent::ShowCopyButtons($_params);
	}

	/**
    * @param int $reckey
    *
    * @return bool
    */
	function IsInvoiceWithSubtotal($reckey) {
		$qry = "select count(1) count from prtaxentry ".
			"where cny# = :1 and prrecordkey = :2";
		$res = QueryResult(array($qry, GetMyCompany(), $reckey));
		if ($res[0]['COUNT'] > 0) {
			return true;
		}
		return false;
	}

	/**
    * @param array $fields
    *
    * @return bool
    */
	function ShowVerticalFieldLayout(&$fields) {
		if ($fields['title'] != "Header") {
			parent::ShowVerticalFieldLayout($fields);
            return true;
		}

		if (count($this->_params['layout']['pages']) > 1) {
			$dataTableClass = "field_list_multitab_data";
		} else {
			$dataTableClass = "field_list_data";
		}
		if ($fields['title']=="Bill Details") {
			/** @noinspection PhpUnusedLocalVariableInspection */
			$t="Header";
		} else {
			/** @noinspection PhpUnusedLocalVariableInspection */
			$t=$fields['title'];
		}

		if(count($fields) && $fields != '') {
			$cnt = 0;
			foreach($fields as $field) {
				if (!$field['hidden'] && isset($field['fullname']) && $field['_func'] == 'Field') {
					$cnt++;
				} elseif ($field['hidden']) {
					$this->ShowFieldsRow($field);
				}
			}
			$fieldbreak = ceil(ibcdiv($cnt, '2'));

		?>

		<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" class="<?=$dataTableClass;?>">
			<tr valign="top">
			<td width="50%">
				<table border="0" cellpadding="0" cellspacing="1" width="100%">
				<?
				$col = 0;
				foreach ($fields as $field) {
					if ($field['hidden']) {
						continue;
					}
					if ($col > $fieldbreak || $field['breakcol'] == 'true') {
						$col = 0;
					?>
				</table>
			</td>
			<td width="50%">
				<table border="0" cellpadding="0" cellspacing="1" width="100%">
					<?
					}
					$this->ShowFieldsRow($field);
					if ($this->FieldNeedsStar($field)) { $tableHasStar = true; }
					if ($this->FieldNeedsHash($field)) { $tableHasHash = true; }
					if ($this->FieldNeedsBug($field)) { $tableHasBug = true; }
					$col++;
			}
			?>

			<table bordercolor="red" border="0" width=100% cellpadding="0" cellspacing="0" >
				<TR>
					<TD width=80% valign="top">
					<?if (isset($tableHasStar) && $tableHasStar) { ?>
					<table border=0 cellpadding="0" cellspacing="0" align=left width=100% class="bottom_section_title"><tr><td align=left valign="top">
						  <FONT class="form_required">* required <?=/** @noinspection PhpUndefinedVariableInspection */$custBotMsg?></FONT>
					</td></tr></table>
					<? }
					?>
					<? if (isset($tableHasHash) && $tableHasHash) { ?>
					 <table border=0 align=left cellpadding="0" cellspacing="0" width=100% class="bottom_section_title"><tr><td align=left valign="top">
						  <FONT class="form_required"># Please select one OR both</FONT>
					</td></tr></table>
					<? } ?>
					<? if (isset($tableHasBug) && $tableHasBug) { ?>
					  <table border=0 align=left cellpadding="0" cellspacing="0" width=100% class="bottom_section_title"><tr><td align=left valign="top">
						  <img src="../resources/images/ia-app/icons/5x5.gif"/><FONT size="0">&nbsp;custom field </FONT>
						</td></tr></table>
					<? } /** aaron **/?>
		<?
		}

	    return true;
	}

	/**
     * @param array  $_field
     * @param string $layout used only in some descendants of Editor class
     * @param bool   $first  used only in some descendants of Editor class
     */
    function ShowFieldsRow(&$_field, $layout = "", $first = false)
    {
		if (!$_field['hidden']) {
			if (is_array($_field) && $_field['_func'] == 'MultilineLayout') {
			?>
			</tr></table></td></tr></table>
			<? $this->ShowMultiLineFieldLayout($_field);
			} else if (is_array($_field) && $_field['_func'] == 'FieldSetLayout') {
			?>
			<TD valign="middle" class1="label_cell_flexi" width="35%">&nbsp;</TD>
			<TD valign="middle" class="value_cell" width="65%">
				<? if(count($_field['rows'][0]['cells'][0]['_args'])) {
					$this->ShowFieldSetLayout($_field);
				} ?>
			</TD>
			<? } else { ?>
			<tr>
				<? if (!$this->IsNoLabelField($_field)) { ?>
					<td align="right" <? if($_field['dependant'] == 'true') { echo "ID='TD_".$_field['path']."'  "."style='display:none'"; } ?> valign="middle" class="label_cell_flexi" width="35%">
						<? $this->ShowSimpleFieldLabel($_field); ?>
					</td>
				<? } ?>
				<td valign="middle" <? if($_field['dependant'] == 'true') { echo "ID='TDVAL_".$_field['path']."'  "."style='display:none'"; } ?> class="value_cell">
					<? if ($_field['_func'] == 'SinglelineLayout') {
						$this->ShowSinglelineFieldLayout($_field);
					} elseif ($_field['_func'] == 'popup' ){
                       $this->ShowPopupField($_field);
					}
					else {
						$this->ShowSimpleFieldValue($_field);
					} ?>
				</td>
			</tr>
			<? } ?>
		<? } else {
            parent::ShowFieldsRow($_field);
		}
	}

	/**
     * @param array $_field
     *
     * @return string
     */
	function getPaymentDetailsUrl(/** @noinspection PhpUnusedParameterInspection */ &$_field)
	{
		$_sess = Session::getKey();

		$prrecordkey = Request::$r->_r;
		$_locationListFlag = Request::$r->_locationListFlag;

		$op = $this->paymentHistoryOP;

		if ( $prrecordkey == '' || $op == '' || $op == -1) {
		    return '';
		}
		$url = "reporteditor.phtml?.locationListFlag=$_locationListFlag&.sess=$_sess&.op=$op&DRILLINVOICE=$prrecordkey&SHOWPAYDETAILS=true&SHOWDETAILS=true&SHOWCREDITDETAILS=true&.p=1&.type=html&.drillfilter=1&TESTVARIABLE=true";

 		if($this->atlas){
			$url .= '&REPORT_LOC='.Request::$r->{'_obj__PRENTRY(0)__LOCATIONID'};
		}

		if(Request::$r->_obj__REPORTINGACCOUNTSET){
			$url .= '&_obj__REPORTINGACCOUNTS=' . URLCleanParams::insert('_obj__REPORTINGACCOUNTS', 'true')
			. '&_obj__REPORTINGACCOUNTSET=' . URLCleanParams::insert('_obj__REPORTINGACCOUNTSET', Request::$r->_obj__REPORTINGACCOUNTSET);
		}


		//&_tr=$prrecordkey
		return $url;
		//
	}

	/**
    * @param array $_params
    */
	function ProcessViewAction(&$_params) {
		if (!$this->mcpEnabled) {
			$findfields = array(
				array('path' => 'TRX_AMOUNT'),
			);
			foreach ($findfields as $ff) {
				$_found=array();
				$this->MatchTemplates($_params, $ff, $_found);
				if ($_found) {
					for ($i = 0; $i < count($_found); $i++) {
						$_found[$i] = '';
					}
				}
			}
		}

		parent::ProcessViewAction($_params);
	}

	/**
    * @param array  $_params
    * @param string $entobjname
    */
	function SetMCPUpdates(&$_params, $entobjname) {

		if ($this->mcpEnabled) {
			$_sess = Session::getKey();

			if (!$this->atlas || ($this->atlas && GetContextLocation())) {
				$basecurr = GetBaseCurrency();
			}

			// add QRequest for defaulting vendor currency
			$this->MatchTemplates($_params, array('path' => $entobjname), $_found0);
			if ($_found0) {
				$qop = GetOperationId('co/lists/trxcurrencies/view');
				foreach ($_found0 as $v => $acct_fld) {
					if ($this->atlas && !GetContextLocation()) {
						$_found0[$v]['onchange'] .= "UpdateDefaultCurrency('$_sess', $qop, document.getElementsByName('_obj__BASECURR')[0].value);";
					} else {
						/** @noinspection PhpUndefinedVariableInspection */
						$_found0[$v]['onchange'] .= "UpdateDefaultCurrency('$_sess', $qop, '$basecurr');";
					}
				}
			}

			$qop = GetOperationId('co/lists/trxcurrencies/view');

			// add QRequest for getting exchange rate
			$find = array (
				array('path' => 'CURRENCY'),
				array('path' => 'EXCHRATEDATE'),
				array('path' => 'EXCHRATETYPE'),
			);

			if ($this->atlas && !GetContextLocation()) {
				$find[] = array('path' => 'BASECURR');
			}

			foreach ($find as $f) {
				$_found = array();
				$this->MatchTemplates($_params, $f, $_found);
				if ($this->atlas  && !GetContextLocation()) {
					$_found[0]['onchange'] .= "UpdateExchangeRate('$_sess', $qop, document.getElementsByName('_obj__BASECURR')[0].value);";
				} else {
					/** @noinspection PhpUndefinedVariableInspection */
					$_found[0]['onchange'] .= "UpdateExchangeRate('$_sess', $qop, '$basecurr', '');";
				}
			}
		}
	}

    /**
    * @param string $key
    * @param string $xslrecord
    *
    * @return string|array
    */
    function GetTemplateType($key, $xslrecord)
	{
		$indexKey = $key . '#~#' . $xslrecord;

        //for a invoice there is only one allowed template at a time, would make sense to cache the value
        if ( !isset($this->printTemplateCache[$indexKey]) ) {

			// custom doc is only for AR for now
			if ( in_array(get_class($this), array('ARInvoiceEditor','ARAdjustmentEditor')) ) {

				// if we dont already have a passed in invoice format
				// lets see if the customer (of the invoice) has any default arinvoice print format configured
				if ( $key != '' && (!isset($xslrecord) || $xslrecord == '') ) {
					$stmt = array(
						"select entity from prrecordmst where cny# = :1 and record# = :2",
						GetMyCompany(), $key,
					);
					$resultSet = QueryResult($stmt);

					$customerid = substr($resultSet[0]['ENTITY'], 1);

					if ( $customerid != '' ) {
					    /** @var CustomerManager $customerMgr */
						$customerMgr = $this->GetManager('customer');
						$xslrecord = $customerMgr->getPrintFormatTemplate($customerid, 'ar');
					}
				}

				// if none found so far lets get the ar module level default format
				if ( !isset($xslrecord) || $xslrecord == '') {
					$xslrecord = GetMyInvoiceFormat();
				}
			}

            $formatdata = array('type' => null, 'template' => null);

			// if we have a key to xslformat lets query all other info
            if ( $xslrecord != '' ) {
                $XslformatMgr = Globals::$g->gManagerFactory->getManager('xslformat');
                $formatdata = $XslformatMgr->DoQuery('QRY_XSLFORMAT_SELECT_RAW_FROM_RECORDNO', array($xslrecord), GetMyCompany());
                if (isset($formatdata[0]['XSL'])) {
                    $formatdata[0]['XSL'] = databaseStringUncompress($formatdata[0]['XSL']);
                }
				$formatdata = $formatdata[0];

				$formatdata['type'] = $formatdata['TEMPLATETYPE'];
				$formatdata['template'] = $formatdata['XSL'];
            }

			$this->printTemplateCache[$indexKey] = $formatdata;
        }

        return $this->printTemplateCache[$indexKey];
    }

    /**
    * @param int|null|string $renameTerms
    * @param int|null|string $docid
    * @param string          $entity
    * @param bool            $iscustomdoc
    *
    * @return array
    */
    function GetDataForPrinting($renameTerms = 0, $docid = 0, $entity = '', $iscustomdoc = false)
    {
        $entityMgr = $this->GetManager($entity);
        if (!$docid) {
            $vid = $entityMgr->GetKeyFieldName();
            $docid = Request::$r->{Request::$r->PathToFieldName($vid)};
        }

        $document = $entityMgr->get($docid);

        if($iscustomdoc) {

            // fetch custom relationship attributes...
            $this->fetchRelationshipValues($entityMgr, $entity, $document);

            foreach ($entityMgr->customFields as $cField) {
                $key = $cField->customFieldID;
                $type = $cField->type;
                // remove field which type is "PASSWORD" in $document.
                if (!is_null($key) && $type=="PASSWORD") {
                    unset($document[$key]);
                }
                // reformat the field which type is "DATE" for display
                if(!is_null($key) && $type=="DATE") {
                    $document[$key]  = FormatDateForDisplay($document[$key]);
                }
            }

            // remove field which type is "PASSWORD" in $document['PRENTRY'].
            $ent = $entity == 'invoice' ? 'arinvoice' : $entity;
            $prentry = $ent ? $ent.'Item' : 'prentry';
            $documentprentryMgr =  $this->GetManager($prentry);
            for ($i=0;$i<count($document['PRENTRY']);$i++) {
                foreach ($documentprentryMgr->customFields as $cField) {
                    $key = $cField->customFieldID;
                    $type = $cField->type;
                    if (!is_null($key) && $type=="PASSWORD") {
                        unset($document['PRENTRY'][$i][$key]);
                    }
	                // reformat the field which type is "DATE" for display
	                if(!is_null($key) && $type=="DATE") {
	                    $document['PRENTRY'][$i][$key]  = FormatDateForDisplay($document['PRENTRY'][$i][$key]);
	                }
                }
            }

            // reformat the dates for display
            $document['WHENCREATED'] = FormatDateForDisplay($document['WHENCREATED']);
            $document['WHENPOSTED'] = FormatDateForDisplay($document['WHENPOSTED']);
            $document['WHENDUE'] = FormatDateForDisplay($document['WHENDUE']);
            $document['WHENPAID'] = FormatDateForDisplay($document['WHENPAID']);
            $document['WHENMODIFIED'] = FormatDateForDisplay($document['WHENMODIFIED']);
            $document['WHENDISCOUNT'] = FormatDateForDisplay($document['WHENDISCOUNT']);
            $document['EXCHRATEDATE'] = FormatDateForDisplay($document['EXCHRATEDATE']);
            $document['DATE'] = FormatDateForDisplay($document['DATE']);

            // reformat these fields, like from 55.5 to 55.50
            $document['TOTALDUE'] = ibcmul($document['TOTALDUE'], '1', 2, false);
            $document['TOTALPAID'] = ibcmul($document['TOTALPAID'], '1', 2, false);
            $document['TOTALENTERED'] = ibcmul($document['TOTALENTERED'], '1', 2, false);

            // reformat these fields, like from 5.00000000 to 5.00, 0.73200000 to 0.732
            $document['EXCHRATE'] = ibcmul($document['EXCHRATE'], '1', 8, true) . '';
            $document['EXCHRATE'] = is_int($document['EXCHRATE'] + 0) ? ibcmul($document['EXCHRATE'], '1', 2, false) : $document['EXCHRATE'];

            // unset SUBTOTALS fields
            unset($document['SUBTOTALS']);
            $count = 0;
			$subtotals = array();

			$_mod = Request::$r->_mod;
			$collapseTaxLines = false;
			if ($_mod == 'ar'){
				if(GetTaxEngine($_mod) == 'AvaTax'){
					global $kAVAid;
					$ava_disp_detail_taxlines = GetPreferenceForProperty($kAVAid, 'AVA_DISP_DETAIL_TAXLINES');
					if($ava_disp_detail_taxlines != 'T'){
						$collapseTaxLines = true;
						$subtaxkey = '';
						$cny = GetMyCompany();
						global $kARid, $kSOid;
					}
				}
			}

			$_ismcpEnabled = IsMCPEnabled($_mod);
			$_isMCPSubscribed = IsMCPSubscribed();
			$basecurr = GetBaseCurrency();

			$trxCurrencyMgr = Globals::$g->gManagerFactory->getManager('trxcurrencies');

			$document['TRX_CURRENCY_SYMBOL'] = getInvoiceTRXCurrencySymbol($basecurr, $_ismcpEnabled, $_isMCPSubscribed, $trxCurrencyMgr, $document);

            foreach($document['PRENTRY'] as $key => &$prentry){
                /* @var array $prentry */
                $prentry['ENTRYEXCHRATE'] = ibcmul($prentry['ENTRYEXCHRATE'], '1', 8, true) . '';
                $prentry['ENTRYEXCHRATE'] = is_int($prentry['ENTRYEXCHRATE'] + 0) ? ibcmul($prentry['ENTRYEXCHRATE'], '1', 2, false) : $prentry['ENTRYEXCHRATE'];
                $prentry['AMOUNT'] = ibcmul($prentry['AMOUNT'], '1', 2, false);
                $prentry['REVRECSTARTDATE'] = FormatDateForDisplay($prentry['REVRECSTARTDATE']);
                $prentry['REVRECENDDATE'] = FormatDateForDisplay($prentry['REVRECENDDATE']);
				$prentry['TRX_CURRENCY_SYMBOL'] = $document['TRX_CURRENCY_SYMBOL'];
                // set SUBTOTALS fields
				if($prentry['SUBTOTAL']) {
					$count ++;
					$prentry['LINE_NO'] = $count;
					unset($document['PRENTRY'][$key]);
					//if avalar is enabled, we have to sum up all the ava tax and change the desc as account label
					if($collapseTaxLines && $prentry['SUBTOTAL'] == 'T') {
						/** @noinspection PhpUndefinedVariableInspection */
						if($oldRecordkey == $prentry['RECORDKEY']){

							/** @noinspection PhpUndefinedVariableInspection */
							$subtotals[$prentry['RECORDKEY']][$subtaxkey]['TRX_AMOUNT'] = ibcadd($subtotals[$prentry['RECORDKEY']][$subtaxkey]['TRX_AMOUNT'], $prentry['TRX_AMOUNT'],2);
							$subtotals[$prentry['RECORDKEY']][$subtaxkey]['RAWAMOUNT'] = ibcadd($subtotals[$prentry['RECORDKEY']][$subtaxkey]['RAWAMOUNT'], $prentry['RAWAMOUNT'],2);
							$subtotals[$prentry['RECORDKEY']][$subtaxkey]['AMOUNT'] = ibcadd($subtotals[$prentry['RECORDKEY']][$subtaxkey]['AMOUNT'], $prentry['AMOUNT'],2);

							continue;
						}else{
							$subtaxkey = count($subtotals[$prentry['RECORDKEY']]);
							$oldRecordkey = $prentry['RECORDKEY'];
							/** @noinspection PhpUndefinedVariableInspection */
							if($document['MODULEKEY'] == $kARid){
								$prentry['ENTRYDESCRIPTION'] = $prentry['GLACCOUNTTITLE'];
							}/** @noinspection PhpUndefinedVariableInspection */
							elseif($document['MODULEKEY'] == $kSOid){
								//get the desc for the subtotal (with Is tax) from TD
								$query = "SELECT docparsubtotal.description FROM dochdr, docparsubtotal WHERE dochdr.cny# = :1 AND dochdr.prrecordkey = :2 and dochdr.cny# = docparsubtotal.cny# and dochdr.docparkey = docparsubtotal.docparkey and docparsubtotal.AMOUNT_PERC = 'P' and docparsubtotal.ISTAX = 'T'";
								/** @noinspection PhpUndefinedVariableInspection */
								$taxdesc = QueryResult(array($query, $cny, $prentry['RECORDKEY']));
								if($taxdesc[0]['DESCRIPTION']){
									$prentry['ENTRYDESCRIPTION'] = $taxdesc[0]['DESCRIPTION'];
								}
							}
						}


					}
					$subtotals[$prentry['RECORDKEY']][] = $prentry;
				}
            }
            if(!empty($subtotals)) {
                $subtotals = array_values($subtotals);
                $document['SUBTOTALS'] = $subtotals[0];
            }else {
                //TC# 11452 :: This is to pass the SUBTOTALS structure in XML
				//Empty array will not set the SUBTOTALS structure for printing
                $document['SUBTOTALS'] = array(array('DUMMY' => ''));
            }

            // set REMITTOADDR fields
            $company = GetCompanyDataForXML();
            $document['REMITTOADDR'] = $company['RemitToAddr'][0];
        }
        $printData =  array(
            'COMPANY' => $this->GetCompanyData(true,$iscustomdoc),
            'REC' => $document
        );

        return $printData;
    }

    /**
     * This function fetches all the values for platform relationship fields
     * ideally emgr->getList() should provide these but in absense this functions tries to retrieve
     * this function is heavily inspired from FormEditor->fetchPlatformRels()
	 * if we atleast have one custom dimension created...
     *
     * @param EntityManager $entityMgr entity manager instance
     * @param string        $entity
     * @param array         $document  array of current document values
     */
    protected function fetchRelationshipValues(/** @noinspection PhpUnusedParameterInspection */ $entityMgr, $entity, &$document)
    {
        // fetch custom relationship attributes...
        if( ! util_isPlatformDisabled() ) {
            $line_entity = ( $entity == 'invoice' ? 'arinvoice' : $entity );
            $prentry = ( $line_entity ? $line_entity.'Item' : 'prentry' );

            $mgr =  $this->GetManager($prentry);
            $customEntity = $mgr->GetCustomComponentsEntity();
            $platformDef = Pt_StandardUtil::getObjDef($customEntity);

            // skip if no relationship found...
            if ( !$platformDef ) { return; }

            $platformRels = Pt_StandardUtil::getRelationshipFields($platformDef);

            foreach( $document['PRENTRY'] as $idx => $row ) {
                foreach( $platformRels as $field ) {
                    $fieldName = isl_strtoupper( $field->getFieldName() );
                    /** @var Pt_FieldRelationship $uiField */
                    $uiField = $field->getUIField();

                    $value = Pt_RelationshipManagerChoose::getObjectIds(
                        $field->getRelationshipDef(),
                        $field->getObjectDefId(),
                        $row['RECORDNO']
                    );

                    $ids = null;
                    if ($value instanceof Pt_ArrayIds) {
                        $ids = $value->getIds();
                    }

                    if ( isset($ids[0]) ) {
						$getObjectDef2 = $uiField->getObjectDef2();

						$lookup2FieldName = '';
						$lookupTemplateFields = $getObjectDef2->getLookupTemplateFields();
						if ( $lookupTemplateFields[1] ) {
							$lookup2FieldName = $lookupTemplateFields[1]->getFieldName();
						}

						$data = Pt_DataObjectManager::getById($getObjectDef2, $ids[0]);
						$allData = $data->getFullFieldMap();

						$document['PRENTRY'][$idx][$fieldName] = $allData['name'];
						if ( $allData[$lookup2FieldName] != '' ) {
							$document['PRENTRY'][$idx][$fieldName] .= '--' . $allData[$lookup2FieldName];
						}
                    }
                }
            }
        }

    }

	/**
	 * Use custom XSL to print current record
	 *
	 * @param array       $_params
	 * @param array       $values
	 * @param string      $_method
	 * @param bool        $offline
	 * @param bool|string $out
	 *
	 * @access
	 * @return bool
	 */
	function Editor_Deliver(&$_params, &$values, $_method, $offline, &$out)
	{
		//impp('InvoiceEditor::Editor_Deliver');
		//impp(pp($values));

		$entity = $_params['ENTITY'];
		$xsltemplateid = $_params['xsltemplateid'];
		$recordno = $values['REC']['RECORDNO'];
		//impp($recordno);

		require_once('backend_pdf.inc');
		require_once('backend_invoice.inc');
		require_once('backend_delivery.inc');

		if (!isset($xsltemplateid) || $xsltemplateid == '') {
			$xsltemplateid = Request::$r->_invformat;
		}

		$this->templateInfo = $this->GetTemplateType($recordno, $xsltemplateid);
		$isCustomDocTemplate = ( in_array($this->templateInfo['type'], array('DOC', 'DOX')) ? true : false );

        switch ($_method) {
        case 'pdf':
            if ( $isCustomDocTemplate ) {

                $template = $this->templateInfo['template'];
                $printdata = $this->GetDataForPrinting(0, $recordno, $entity, true);
                $xml = XMLUtils::BuildWSXML($printdata);

				list($langCode, $countryCode) = explode('-', $this->xsltemplateInfo['LOCALE']);
				$langCode = ( $langCode != '' ? $langCode : 'en' );
				$countryCode = ( $countryCode != '' ? $countryCode : 'US' );

				$params = array(
					'LANGCODE' => $langCode,
					'COUNTRYCODE' => $countryCode,
					'WHENMODIFIED' => $this->templateInfo['WHENMODIFIED'],
				);

                $out = genPDFFromWORD($xml, $template, !$offline, $params);

            } else {
                $arr = GetInvoiceArr(array($recordno));
                //impp(pp($arr));

				// followin glines where copied from ARInvoiceEditor
				if ( $this instanceof \ARInvoiceEditor ) {
					$billaddr1 = $arr[0]['Invoice'][0]['BILLINGADDR1'];
					$billaddr2 = $arr[0]['Invoice'][0]['BILLINGADDR2'];
					$shipaddr1 = $arr[0]['Invoice'][0]['SHIPPINGADDR1'];
					$shipaddr2 = $arr[0]['Invoice'][0]['SHIPPINGADDR2'];

					$arr[0]['Invoice'][0]['BILLINGADDR1'] = MailaddressManager::GetTrimAddress($billaddr1);
					$arr[0]['Invoice'][0]['BILLINGADDR2'] = MailaddressManager::GetTrimAddress($billaddr2);
					$arr[0]['Invoice'][0]['SHIPPINGADDR1'] = MailaddressManager::GetTrimAddress($shipaddr1);
					$arr[0]['Invoice'][0]['SHIPPINGADDR2'] = MailaddressManager::GetTrimAddress($shipaddr2);
				}

                $xml = GetInvoiceXML($arr, $values['MESSAGE'], $values['MARKETTEXT']);
                //impp($xml);
				if($xsltemplateid == '' && $this->templateInfo['RECORD#']){
                    $xsltemplateid = $this->templateInfo['RECORD#'];
                }
                $fo = GetInvoiceFO($xml, $xsltemplateid);
                //impp($fo);

                $out = genPDF($fo, !$offline);
                //impp($out);
            }
            return true;
        case 'xmldataraw':
            if ( $isCustomDocTemplate ) {
                $printdata = $this->GetDataForPrinting(0, $recordno, $_params['ENTITY'], true);
                $xml = XMLUtils::BuildWSXML($printdata);
            } else {
                $arr = GetInvoiceArr(array($recordno));
                $xml = GetInvoiceXML($arr, $values['MESSAGE'], $values['MARKETTEXT']);
            }
            $out = $xml;
            return $out !== '';
        }

        return true;
	}

	/**
    * Changing and adding vars record and ent to the $_done global variable for the Save & Print button option.
    *
    * @param int $rec
    */
	function SetPrintRecVar($rec = 0) {
		INTACCTsetcookie(".printrec", $rec);
	}

	/**
    * @param array $_params
    */
	function MergeOwnerDimensionFields(&$_params) {
		$this->calcOwnerDimensionFields($_params);
	}

	/**
    * @param EntityManager $entityMgr
    *
    * @return bool
    */
	function IsHeaderDimensionsEditable($entityMgr) {
		$ok = parent::IsHeaderDimensionsEditable($entityMgr);
		$action = Request::$r->_do;

		// stop showing header fields for reclassification
		if ($action == 'reclass') {
			$ok = false;
		}

		return $ok;
	}

    /**
    * Checks if all the line items for this transaction is billed.
    *
    * @param int $reckey
    *
    * @return bool
    */
    function IsAllLinesBilled($reckey)
    {
        $qry = "select count(1) count from prentry where cny# = :1 and recordkey = :2 and LINEITEM = 'T' and nvl(billed, 'F') = 'F'";
        $res = QueryResult(array($qry, GetMyCompany(), $reckey));
        if ( $res[0]['COUNT'] > 0 ) {
            return false;
        }
        return true;
    }
}
