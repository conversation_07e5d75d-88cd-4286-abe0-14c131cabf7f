<?php

/**
 * Pymt Detail Processor Interface
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2024 Intacct Corporation All, Rights Reserved
 */

interface PymtDetailProcessorInterface
{

    public function setDefaultState(?string $state) : void;

    public function process(PaymentResponseInterface $response) : bool;

    public function processExchangeGL(PaymentResponseInterface $response) : bool;

    public function getUniqueTxnMap() : array;

}