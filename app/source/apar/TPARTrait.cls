<?php

/**
 * Trait TPAR
 *
 *
 */
trait TPARTrait
{

    /**
     * is feature enabled
     *
     * @return bool
     *
     */
    public static function isTPARAPConfigEnabled()
    {
        GetModulePreferences(Globals::$g->kAPid, $modPref);
        $ok = ($modPref['ENABLE_TPAR'] ?? 'false' ) === 'true';
        return $ok;
    }

    /**
     * set locationfilter
     *
     * @param array $filters
     */
    public static function setLocationFilter(array &$filters)
    {
        $filters['LOCATIONLIST'] = [];
        if (!GetContextLocation()) { // TOP LEVEL
            if(is_array($filters["LOCATION"]) && countArray($filters["LOCATION"]) > 1) {
                foreach($filters["LOCATION"] as $loc) {
                    list($entId) = explode('--', $loc);
                    $filters['ISGROUP'] = true;
                    $entityID = GetEntityId($entId);
                    $filters['LOCATIONLIST'][] = $entityID;
                }
            } else {
                $location = is_array($filters["LOCATION"]) ? $filters["LOCATION"][0] : $filters["LOCATION"];
                list($entId) = explode('--', $location);
                if (IsGroup('location', $entId, $rec)) {
                    $filters['ISGROUP'] = true;
                    $LocMgr = Globals::$g->gManagerFactory->getManager('locationgroup');
                    $locgrp = $LocMgr->ValidateEntityGroup($entId);
                    if (!isset($locgrp['MEMBERS']) || $locgrp['MEMBERS'] == '') {
                        Globals::$g->gErr->addError('BL03002028', __FILE__ . ':' . __LINE__, "All members are not entities for location group '" . $entId . "' ");
                        return false;
                    }
                    $filters['LOCATIONLIST'] = $locgrp['MEMBERSRECNO'];

                } else {
                    // Not group
                    $filters['ISGROUP'] = false;
                    $entityID = GetEntityId($entId);
                    $filters['LOCATIONLIST'][] = $entityID;
                }
            }
        } else {
            // ENTITY LEVEL
            $filters['ISGROUP'] = false;
            $ctxlocation = GetContextLocation();
            $filters['LOCATIONLIST'][] = $ctxlocation;
        }
    }

    /**
     * set $filters
     *
     * @param array $filters
     *
     */
    public static function setObjectFilter(array &$filters)
    {
        $from = explode("--", $filters['FROM']);
        if($from && $from[0]) {
            $filters['FROM'] = $from[0];
        }
        $to = explode("--", $filters['TO']);
        if($to && $to[0]) {
            $filters['TO'] = $to[0];
        }
    }


}