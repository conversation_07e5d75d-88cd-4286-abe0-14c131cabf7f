<grid id="smartTransactionsGrid" title="" noDragDrop="true" noPagination="true" hasFixedNumOfRows="true" >
    <path>CONNECTTOSERVICES</path>
    <column fullname="IA.SERVICE_NAME">
        <field noLabel="true" fullname="IA.SERVICE_NAME" >
            <path>SMARTTRXSERVICE</path>
            <readonly>true</readonly>
        </field>
    </column>
    <column noLabel="true">
        <field noLabel="true" fullname="IA.SERVICE_STATUS">
            <path>SMARTTRXSERVICESTATUS</path>
            <readonly>true</readonly>
        </field>
    </column>
    <column>
<!--        <field readonly='true' href="javascript:void(0);">-->
<!--            <type type="href" ptype="href"/>-->
<!--            <path>SMARTTRXSERVICECHECK</path>-->
<!--            <events>-->
<!--                <click>enableDisableSmartTransactions(this, 'CONNECTTOSERVICES')</click>-->
<!--            </events>-->
<!--        </field>-->
        <field readonly='true' href="javascript:void(0);">
            <type type="href" ptype="href"/>
            <path>SMARTTRXSERVICECONFIG</path>
            <events>
                <click>openConfigurationFloatingPage(this)</click>
            </events>
        </field>
    </column>
</grid>