<?php
 /**
 * File ARDetailGetAllBatch.cls contains the class ARDetailGetAllBatch
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2013 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
 

class ARDetailGetAllBatch extends UnionBaseBatch
{

    /**
     * @param string $entity
     * @param int    $readTimestamp
     * @param int    $pageSize
     * @param array  $params
     */
    function __construct( $entity, $readTimestamp, $pageSize, $params = array() )
    {
        $this->configurationArray = UnionIteratorConfigurator::getBatchConfigurationArray()['ardetail'];
        parent::__construct($entity, $readTimestamp, $pageSize, $params);
    }

    /**
     * @param array $params
     *
     * @return DetailGetAllReportTypeBatch
     */
    protected function getReportTypeBatch($params)
    {
        if ( DDS_DEBUG == 1 ) {
            impp('ARDetailGetAllReportTypeBatch', '');
        }
        return new DetailGetAllReportTypeBatch('ardetail', $this->getReadTimestamp(), $this->getPageSize(), $params);
    }
}