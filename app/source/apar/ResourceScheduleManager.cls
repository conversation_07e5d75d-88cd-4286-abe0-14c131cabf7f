<?php
/**
 * Resource Schedule View entity Manager
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 Intacct Corporation, All Rights Reserved
 */
 
/**
 * Manager class for the Resource Schedule View
 */
class ResourceScheduleManager extends EntityManager
{
    /**
     * @param array $params the initialization parameters of the class
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
    }
    
    /**
     * Not supported. Always returns false.
     *
     * @param string        $ID        unused
     * @param string[]|null $fields
     * @param bool          $systemCall unused
     *
     * @return null
     */
    public function get($ID, $fields = null, $systemCall = false)
    {
        return null;
    }

    /**
     * Not supported. Always returns false.
     *
     * @param string $ID unused
     *
     * @return bool false
     */
    public function delete($ID)
    {
        return false;
    }

    /**
     * Not supported. Always returns false.
     *
     * @param array &$values unused
     *
     * @return bool false
     */
    protected function regularAdd(&$values)
    {
        return false;
    }

    /**
     * Not supported. Always returns false.
     *
     * @param array &$values unused
     *
     * @return bool false
     */
    protected function regularSet(&$values)
    {
        return false;
    }
}
