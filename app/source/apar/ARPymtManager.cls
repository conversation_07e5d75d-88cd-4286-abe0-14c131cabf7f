<?php
/**
 * Manager class for the AP Payments
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Intacct Corporation All, Rights Reserved
 */

/**
 * Manager class for the AP Payments
 */
class ARPymtManager extends SubLedgerPymtManager
{
    // Class variables
    public static array $uiSourceMapForMetric = [
        'new'       => 'new',
        'old'       => 'oldscreen',
        'arrecur'   => 'arrecurinvoice',
        'sorecur'   => 'sorecurpayment',
        'sopayment' => 'sopayment',
    ];

    /**
     * @var string[] $inlineCreditRecordTypes Internal credit record types.
     */
    public static $inlineCreditRecordTypes = array(
        SubLedgerTxnManager::INVOICE_RECTYPE,
        SubLedgerTxnManager::ARADJUSTMENT_RECTYPE
    );
    /**
     * @var string[] $externalCreditRecordTypes External credit record types.
     */
    protected static $externalCreditRecordTypes = array(
        BasePymtManager::ARDISCOUNT_RECTYPE,
        BasePymtManager::ARPOSTEDADVANCE_RECTYPE
    );

    /** @var bool $_parentRecordTypesByRecordType Use for pymtdetail owned object to change the parent query */
    public static bool $_isParentRecordMultiEntity = false;
    /* @var bool $deferPermissionCheck */
    private static $deferPermissionCheck;
    /** @var bool $isPartOfMultiEntityPayment */
    private static bool $isPartOfMultiEntityPayment = false;
    /** @var bool $isMultiEntityPymtUpdate */
    private bool $isMultiEntityPymtUpdate = false;

    /** Use Traits for arpymt methods*/
    use ARPymtTrait;

    /**
     * @param array $params the parameters of the class
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
        $this->setHasUserSpecifiedBatch(!self::isARPaymentAutoBatchingEnabled());
    }

    /**
     * Check if Draft transaction allowed or not
     *
     * @return bool from the subclass
     */
    public function isDraftAllowed()
    {
        return true;
    }

    /**
     * unset defer permission validation at Init
     */
    public static function unsetDeferPermValidation()
    {
        self::$deferPermissionCheck = false;
    }

    /**
     * Setter to defer the permission validation at Init
     *
     * @param string $noun noun of the permission
     */
    public static function setDeferPermValidation($noun='')
    {
        $addOp = GetOperationId('ar/lists/arpymt/create');
        self::$deferPermissionCheck = CheckAuthorization($addOp, 1);
    }

    /**
     * Getter to defer the permission validation at Init
     *
     * @return bool
     */
    public static function getDeferPermValidation()
    {
        return self::$deferPermissionCheck;
    }

    /**
     * Check if the user has the permission to post a payment
     * @return bool
     */
    private function hasPostPermission(): bool
    {
        // validate user have POST permission to create a payment
        return CheckAuthorization($this->getSubmitPermissionId(), 1);
    }

    /**
     * Get the payment key field path in the payment details data structure
     *
     * @return string the payment key field path in the payment details data structure
     */
    protected function getPaymentKeyPath()
    {
        return 'PAYMENTKEY';
    }

    /**
     * Get the payment entry key field path in the payment details data structure
     *
     * @return string the payment entry key field path in the payment details data structure
     */
    protected function getPaymentEntryKeyPath()
    {
        return 'PAYMENTENTRYKEY';
    }

    /**
     * Get the payment date field path in the payment details data structure
     *
     * @return string the paymentdate field path in the payment details data structure
     */
    protected function getPaymentDatePath()
    {
        return 'PAYMENTDATE';
    }

    /**
     * Get the payment amount field path in the payment details data structure
     *
     * @return string the the payment amount field path in the payment details data structure
     */
    protected function getPaymentAmountPath()
    {
        return 'TRX_PAYMENTAMOUNT';
    }

    /**
     * Get the payment base amount field path in the payment details data structure
     *
     * @return string the payment amount field path in the payment details data structure
     */
    protected function getPaymentBaseAmountPath()
    {
        return 'PAYMENTAMOUNT';
    }

    /**
     * Get the record type of the transaction we are paying
     *
     * @return string[] the record types of the transaction we are paying
     */
    protected function getPaidTransactionRecordTypes()
    {
        $recordTypes = array(
            SubLedgerTxnManager::INVOICE_RECTYPE,
            SubLedgerTxnManager::ARADJUSTMENT_RECTYPE
        );
        return $recordTypes;
    }

    /**
     * Get the record types of the parent transactions we need to fetch for the payment
     *
     * @return string[] the record types of the parent transactions we need to fetch for the payment
     */
    protected function getParentTransactionRecordTypes()
    {
        $recordTypes = array(
            SubLedgerTxnManager::INVOICE_RECTYPE,
            SubLedgerTxnManager::ARADJUSTMENT_RECTYPE
        );
        return $recordTypes;
    }

    /**
     * Get the list of valid payment methods
     *
     * @param string|null $paymentProvider
     * @param string|null $providerPayMethod
     *
     * @return array a list of valid payment method
     */
    public function getValidPaymentMethod(string $paymentProvider = null, string $providerPayMethod = null)
    {
        $payMethod = parent::getValidPaymentMethod($paymentProvider, $providerPayMethod);
        $arPaymethod = array(
            BasePymtManager::CC_PAYMENTMETHOD,
            BasePymtManager::ACH_PAYMENTMETHOD,
            BasePymtManager::ONLINE_ACH_DEBIT_PAYMENTMETHOD,
            BasePymtManager::ONLINE_CHARGECARD_PAYMENTMETHOD
        );

        return array_merge($payMethod, $arPaymethod);
    }


    /**
     * @return string
     */
    protected function getDefaultStateForSubmitAction()
    {
        return BasePRRecordManager::DRAFT_RAWSTATE;
    }

    /**
     * Initialize the class vaiables and data for update
     *
     * @param array &$values the transaction data
     *
     * @return bool false if error else true
     */
    protected function initialize(&$values)
    {
        $ok = true;
        // Does the transaction exists already ?
        $result = $this->getHeaderInfo($values['RECORDNO']);

        // Keep the header information for futher processing
        $values['EXISTING_HEADER'] = $result;
        return $ok;
    }

    /**
     * Some of the payment related activities specific to various payment method eg: Amex, WF payments
     *
     * @param array $values the payment details data
     *
     * @return bool is this action is success or failure
     */
    protected function onBeforeAdd(&$values)
    {
        $ok = true;

        //Set parent entry for VAT tax entries
        $this->setParentEntryForTaxEntries($values);

        return $ok;
    }

    /**
     * Add a new payment record
     *
     * @param array &$values the payment request data
     *
     * @return bool false if error else true
     */
    protected function regularAdd(&$values)
    {
        $start = microtime(true);
        BasePymtManager::resetClassValues();
        // invoke the payment
        $ok = $this->invokePayment($values);
        // add into the matrix
        $metricData = [
            'RECORDTYPE' => 'arpymt',
            'RECORDNO' => $ok ? $values['RECORDNO'] : null,
        ];

        $timeTaken = (float)(microtime(true) - $start);
        $this->addMetricsParams('PROCESSING_TIME', $timeTaken);
        LogToFile("TIME TAKEN for ARPymtManager::Add API:::: $timeTaken\n");

        $this->addToMetric($metricData, 'add');

        return $ok;
    }

    /**
     * update the draft payment records.
     *
     * @param array $values
     *
     * @return bool
     */
    protected function regularSet(&$values)
    {
        $start = microtime(true);
        self::unsetDeferPermValidation();
        BasePymtManager::resetClassValues();
        $paymentKey = $values['RECORDNO'];
        logToFileInfo("ARPymt:: regularSet called for the payment $paymentKey");
        if (!$this->validatePaymentUpdate($values)) {
            return false;
        }
        // set seaction as draft if not set
        if (empty($values['ACTION'])) {
            $values['ACTION'] = self::DRAFT_ACTION;
        }
        // get the transaction details that are selected for the payment
        if (!$this->getPaymentTransactionDetails($values)) {
            Globals::$g->gErr->addError(
                'AR-0552', __FILE__.'.'.__LINE__,
                "No transactions found for the payment."
            );
            return false;
        }
        // make sure build payment request called before this, so will have SELECTEDTXN values
        if (!$this->buildMultiEntityPymtKeyMapForUpdate($values)) {
            return false;
        }
        // delete the existing payment and create a new one
        $ok = $this->updatePayment($values);

        $metricData = [
            'RECORDTYPE' => 'arpymt',
            'RECORDNO' => $values['RECORDNO'],
        ];
        $timeTaken = (float)(microtime(true) - $start);
        $this->addMetricsParams('PROCESSING_TIME', $timeTaken);
        LogToFile("TIME TAKEN for ARPymtManager::regularSet :::: $timeTaken\n");

        $this->addToMetric($metricData, 'set');
        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     * @throws IAException
     */
    private function invokePayment(array &$values): bool
    {
        //translate batch title to batch key
        $this->translatePRBatch($values);
        // Get the payment controller to process the payment
        $paymentController = $this->getPaymentController(false, $this->fromGateway);
        //In AR we do not need action tag value if Draft is not supported
        if (!$this->isDraftAllowed()) {
            unset($values['ACTION']);
        }
        /**
         * PAYMENTDATE optional / Default today's date
         * As per API documentation
         * This is only for arpymt
         */
        if (!isset($values['PAYMENTDATE']) || $values['PAYMENTDATE'] === '') {
            $values['PAYMENTDATE'] = GetCurrentDate();
        }
        $paymentInput = new PaymentDTO($values);
        $ok = $paymentController->createPayment($paymentInput);
        // convert the object back to array for response
        $values = $paymentInput->getValues();
        if ($ok) {
            if ($paymentInput->isMultiEntityPayment()) {
                $values['RECORDNO'] = $paymentInput->getMultiEntityPymtKey();
            }
            $values['EXTERNALPAYMENTRETURNVALUE'] = $paymentInput->getExternalPymtReturnValue();
        }

        $this->prepareMetricDataForPayment('arpymt', $paymentInput, $values, 0);
        return $ok;
    }

    /**
     * @param array $values
     * @return bool
     */
    protected function translatePRBatch(array &$values): bool
    {
        if (empty($values['PRBATCH']) || !empty($values['PRBATCHKEY'])) {
            return true;
        }
        $batchTitle = $values['PRBATCH'];
        $result = $this->getBatchDetails(
            ['RECORDNO'], [[['TITLE', '=', $batchTitle]]]
        );
        if (!empty($result[0]['RECORDNO'])) {
            $values['PRBATCHKEY'] = $result[0]['RECORDNO'];
            if (count($result) > 1) {
                logToFileError('Multiple batches found for same title - '.$batchTitle);
            }
        }
        return true;
    }

    /**
     * Method to call the base payment creation.
     *
     * @param array     $payment
     *
     * @return bool
     */
    public function createPayment(array &$payment): bool
    {
        return parent::regularAdd($payment);
    }

    /**
     * Method to call the base payment set.
     *
     * @param array     $payment
     *
     * @return bool
     */
    public function editPayment(array &$payment): bool
    {
        return parent::regularSet($payment);
    }

    /**
     * @param array $payment
     * @param bool  $fastUpdate
     *
     * @return bool
     * @throws IAException
     */
    private function validatePaymentUpdate(array $payment, bool $fastUpdate = false): bool
    {
        return ARCommonPaymentValidator::validateUpdate($payment, $fastUpdate, self::$isPartOfMultiEntityPayment);
    }

    /**
     * Invoked right before entering \EntityManager::doSetFast
     * @param  array $values
     *
     * @return bool true on success, false if error
     */
    protected function preSetFast(&$values)
    {
        // check if request is coming from system update, no need to perform below operations
        if (self::getIsSystemUpdate()) {
            return true;
        }

        $ok = parent::preSetFast($values);
        $paymentData = $this->getListPaymentData($values['RECORDNO']);
        if ($ok && !$this->validatePaymentUpdate($paymentData, true)) {
            return false;
        }

        // get the update values that needs to be updated
        $updateValues = $this->getUpdatePaymentValues($values, $paymentData);
        if (empty($updateValues)) {
            // if there is no update values and action is draft, no need to perform below operations
            return true;
        }

        $ok = $ok && $this->UpdatePayments($updateValues, $paymentData);
        return $ok;
    }

    /**
     * For applications to determine the post fast update condition
     * Fast Update will be aborted and rolled back if return false
     *
     * @param array $values the input values to update opeartion
     *
     * @return bool true if the current record supports fast updates given the input $values
     * @see EntityManager::entityManagerSetFull
     */
    protected function postSetFast($values)
    {
        // check if request is coming from system update, no need to perform below operations
        if (self::getIsSystemUpdate()) {
            return true;
        }
        $ok = parent::postSetFast($values);
        // only update the supporting document for rp payment and rl multi entity payment
        if (isset($values['SUPDOCID'])) {
            $supDocValue = [
                'RECORDNO' => $values['RECORDNO'],
                'SUPDOCID' => $values['SUPDOCID'],
                'EXTERNALURL' => null, // no need to update the external url
            ];

            $ok = $ok && $this->setSupportingDocument($supDocValue);
        }
        if ($this->isMultiEntityPymtUpdate) {
            $this->isMultiEntityPymtUpdate = false;
        }
        return $ok;
    }

    /**
     * @param int $paymentKey
     *
     * @return bool
     * @throws Exception
     */
    final public function submitPayment(int $paymentKey): bool
    {
        $start = microtime(true);
        $values = $this->get($paymentKey);
        if (empty($values)) {
            Globals::$g->gErr->addError(
                'AR-0548',
                __FILE__ . ':' . __LINE__,
                "Payment record not found for the key - $paymentKey"
            );
            return false;
        }
        // begin the transaction
        $source = 'ARPymtManager::SubmitPayment';
        $ok = $this->_QM->beginTrx($source);
        $isMultiEntityPymt = $values['RECORDTYPE'] === self::ARPYMT_MULTI_ENTITY_RECTYPE;
        // set the action to submit
        $values['ACTION'] = self::SUBMIT_ACTION;
        // validate payment is allowed to update
        $ok = $ok && $this->validatePaymentUpdate($values);
        // process Action only if the payment is allowed to update
        $ok = $ok && $this->processAction($values);
        // validate the Drafted payment before confirm
        $ok = $ok && $this->validateDraftPaymentBeforeSubmit($values);
        if ($isMultiEntityPymt) {
            $multEntityPymtMgr = Globals::$g->gManagerFactory->getManager('armulticustomerpymt');
            // call check And Confirm Payment
            $ok = $ok && $multEntityPymtMgr->checkAndConfirmPayment($values);
        } else {
            // call check And Confirm Payment
            $ok = $ok && $this->checkAndConfirmPayment($values);
        }

        if (!$ok || !$this->_QM->commitTrx($source)) {
            $ok = false;
            $msg = "Oops, we've encountered a glitch, review your payment, then try again.";
            Globals::$g->gErr->addError('AR-0196', __FILE__ . ':' . __LINE__, $msg);
            $this->_QM->rollbackTrx($source);
        }

        $timeTaken = microtime(true) - $start;
        LogToFile("TIME TAKEN for ARPymtManager::submitPayment:::: $timeTaken\n");

        return $ok;
    }

    /**
     * API to process multiple payments.
     *
     * @param array $payments array of payments belonging to different vendors.
     * @param bool  $isMerge
     * @param bool  $isAdd
     * @param array $response output info
     *
     * @return bool returns true if the operation is success otherwise false.
     */
    public function processPayments(&$payments, $isMerge = false, $isAdd = true, &$response = null)
    {
        $start = microtime(true);
        $ok = true;
        if (!empty($payments)) {
            $ok = true;
            // iterate each payments and save
            foreach ($payments as &$payment) {
                // Process each payment request
                $ok = $ok && $this->processPayment($payment, $isMerge, $isAdd);
            }
        }
        $timeTaken = microtime(true) - $start;
        LogToFile("TIME TAKEN for ARPymtManager::processPayments API:::: $timeTaken\n");
        return $ok;
    }

    /**
     * API to create single payment for the given inout
     *
     * @param array $payment array of payment request.
     * @param bool  $isMerge
     * @param bool  $isAdd
     *
     * @return bool returns true if the operation is success otherwise false.
     */
    public function processPayment(&$payment, /** @noinspection PhpUnusedParameterInspection */ $isMerge = false, $isAdd = true)
    {
        $gErr = Globals::$g->gErr;
        $source = 'ARPymtManager::processPayment';
        // initiate the transaction
        $ok = $this->_QM->beginTrx($source);

        if(!empty($payment)) {
            // for bills, create a payment
            if($isAdd) {
                $ok = $ok && $this->add($payment);
            } else {
                $ok = $ok && $this->set($payment);
            }
        }
        // if there are no payment input passed throw error back
        else {
            $ok = false;
            $gErr->addError(
                'AR-0098',
                __FILE__.":".__LINE__,
                'There is no payment provided to process',
                'Please provide payment details to process.'
            );
        }

        // commit/rollback based on success
        if ( !$ok ) {
            // do *not* add error, if it has only warnings
            $this->_QM->rollbackTrx($source);
        } else {

            if ( !$this->_QM->commitTrx($source) ) {
                $ok = false;
                $msg = "Oops, we've encountered a glitch, review your payment, then try again.";
                $gErr->addError('AR-0196', __FILE__ . ':' . __LINE__, $msg);
                $this->_QM->rollbackTrx($source);
            }
        }
        return $ok;
    }

    /**
     * API to remove the payment records.
     *
     * @param string|int    $ID Payment request id that need to be deleted.
     * @param string        $pymtRecordType
     *
     * @return bool returns true for success else false.
     */
    public function Delete($ID, $pymtRecordType = '')
    {
        $ok = true;
        $parentPaymentData = [];
        if (!in_array($pymtRecordType, static::$inlineCreditRecordTypes)) {
            // Get payment record
            $parentPaymentData = $this->getListPaymentData($ID);
            if (empty($parentPaymentData)) {
                Globals::$g->gErr->addError(
                    'AR-0546',
                    __FILE__.":".__LINE__,
                    'Payment record not found',
                    'The payment record you are trying to delete is not found.'
                );
                return false;
            }
            // set the pymtRecordType to payment recordtype
            $pymtRecordType = $parentPaymentData['RECORDTYPE'];
            // if the payment is multi entity payment, then delete the payment from multi entity payment
            if ($pymtRecordType === self::ARPYMT_MULTI_ENTITY_RECTYPE) {
                $multEntityPymtMgr = Globals::$g->gManagerFactory->getManager('armulticustomerpymt');
                return $ok && $multEntityPymtMgr->Delete($ID);
            }
        }
        // Delete the payment data from prentrypymtrecs, pymtdetail, and prrecord/prentry
        return $ok && $this->_Delete($ID, $pymtRecordType, $parentPaymentData);
    }

    /**
     * @param string $paymentKey
     *
     * @return array
     */
    private function getListPaymentData(string $paymentKey): array
    {
        if (empty($paymentKey)) {
            return [];
        }
        // Get payment record
        $filter = [
            'selects' => [
                'RECORDNO', 'STATE', 'RAWSTATE', 'RECORDTYPE','CUSTOMERKEY','CUSTOMERID',
                'MULTIENTITYPYMTKEY', 'PAYMENTMETHODKEY', 'PAYMENTMETHOD',
            ],
            'filters' => [[
                ['RECORDNO', '=', $paymentKey],
                ['RECORDTYPE', 'IN', [self::ARPYMT_MULTI_ENTITY_RECTYPE, self::ARPYMT_RECTYPE]],
            ]],
            'nodbfilters' => true,
        ];
        $result = $this->GetList($filter) ?? [];
        return $result[0] ?? [];
    }

    /**
     * Delete the payment details for the given parent payment.
     *
     * @param string|int $parentPaymentId
     * @param string     $pymtRecordType
     *
     * @return bool false if error else true
     *
     */
    private function deletePymtDetails($parentPaymentId, $pymtRecordType = '')
    {
        // 1.1 Delete all the PRENTRYPYMTRECS entries
        $ok = $this->deletePaymentLines($parentPaymentId);
        // 1.2 Get the payment detail manager to delete the PYMTDETAIL entries
        if ($ok) {
            $entity = $this->getPymtDetailEntity();
            $gManagerFactory = Globals::$g->gManagerFactory;
            /* @var APPymtDetailManager $pymtDetailMgr */
            $pymtDetailMgr = $gManagerFactory->getManager($entity);
            $ok = $pymtDetailMgr->deletePaymentDetails($parentPaymentId, $pymtRecordType);
        }
        return $ok;
    }

    /**
     * Method to delete all the related payments from payment recs table
     * for the given parent payment.
     *
     * @param string|int    $parentPaymentId  parent payment id
     *
     * @return bool         returns true for success other returns false
     */
    private function deletePaymentLines($parentPaymentId)
    {
        $source = "ARPymtManager::deletePaymentLines";
        XACT_BEGIN($source);
        // Delete all the PRENTRYPYMTRECS entries
        $query = "DELETE from (select * from prentrypymtrecs " .
            "WHERE cny# = :1 and parentpymt = :2 " .
            "order by paymentkey desc)";

        $ok = ExecStmt(array($query, GetMyCompany(), $parentPaymentId));

        if ($ok) {
            XACT_COMMIT($source);
        } else {
            XACT_ABORT($source);
        }
        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    private function canDeletePaymentRecord(array $values): bool
    {
        // if the delete is not happening from edit, return true
        // so it will delete the payment record
        if (!SubLedgerPymtManager::isUpdatePayment()) {
            return true;
        }
        // if request is coming from edit payment, then check if the payment is part of multi entity payment
        // if its empty, then its single customer payment, so return false, so it will not delete the payment record
        $multEntityPymtMap = $this->getMultiEntityPymtKeyMapForUpdate();
        // if mult entity pymt map is not empty and the payment entity is not in the selected entity list,
        // delete the payment record as its not part of the edited payment
        if (!empty($multEntityPymtMap) && !in_array($values['CUSTOMERID'], $multEntityPymtMap['SELECTEDTXNS'])) {
            return true;
        }

        return false;
    }

    /**
     * Method to Delete the payment record and its related records.
     *
     * @param string $ID
     * @param string $pymtRecordType
     * @param array  $paymentData
     *
     * @return bool
     * @throws IAException
     */
    private function _Delete(string $ID, string $pymtRecordType, array $paymentData): bool
    {
        // validate the action before proceeding
        $validator = ARPaymentController::getPaymentValidator($paymentData['PAYMENTMETHOD']?? null);
        if (!$validator->validateDelete($paymentData, $pymtRecordType)) {
            return false;
        }
        $source = 'ARPymtManager::Delete';
        $ok = $this->_QM->beginTrx($source);
        // Get the related payment records first to delete them
        $prRecords = $this->getRelatedPaymentRecords($ID);
        // 1. Delete the pymtdetail entries first
        $ok = $ok && $this->deletePymtDetails($ID, $pymtRecordType);
        // 2. Delete all the payments corresponding to this parent payment
        $ok = $ok && $this->deleteRelatedPayments($prRecords);
        // Delete the bank transaction map if there is one
        if (!self::isUpdatePayment()) {
            $ok = $ok && $this->handleBankTransaction([$ID]);
        }
        // 3. Delete the checkrun if it is printed check payment method if the payment is not inline credit
        if (!empty($paymentData) && !in_array($pymtRecordType, static::$inlineCreditRecordTypes)) {
            // 3. Delete the checkrun if it is printed check payment method
            if (isset($paymentData['PAYMENTMETHODKEY']) && $paymentData['PAYMENTMETHODKEY'] == APPymtUtils::PRINTED_CHECK_PAYMETHOD) {
                $chkRunDetMgr = Globals::$g->gManagerFactory->getManager('checkrundetail');
                $ok = $ok && $chkRunDetMgr->DoQuery(
                        'QRY_DELETE_CHECKRUNDETAIL_BY_PAYMENTKEY_FOR_CONFIRMVOID',
                        [$ID, GetMyCompany()]
                    );
            }

            if ($this->canDeletePaymentRecord($paymentData)) {
                // 4. Call the parent to delete the payment PR record
                $ok = $ok && parent::delete($ID);
            } else {
                // 4. Call the parent to delete IET records
                $ok = $ok && parent::deleteIETs($ID, DELETE_FOR_DELETE);
            }
        } else {
            // 4. Call the parent to delete IET records
            $ok = $ok && parent::deleteIETs($ID, DELETE_FOR_DELETE);
        }

        if (!$ok || !$this->_QM->commitTrx($source)) {
            $ok = false;
            $msg = "Oops, we've encountered a glitch, review your payment, then try again.";
            Globals::$g->gErr->addError('AR-0197', __FILE__ . ':' . __LINE__, $msg);
            $this->_QM->rollbackTrx($source);
        }
        return $ok;
    }

    /**
     * Method to delete all the related payments from payment recs table
     * for the given parent payment.
     *
     * @param array $prRecords
     *
     * @return bool
     */
    private function deleteRelatedPayments(array $prRecords): bool
    {
        $ok = true;
        foreach ($prRecords as $row) {
            if (!in_array($row['RECORDTYPE'], static::$inlineCreditRecordTypes)) {
                $ok = $ok && $this->deleteByRecordType($row['RECORDTYPE'], $row['RECORD#']);
                if (!$ok) {
                    break;
                }
            }
        }

        return $ok;
    }

    /**
     * Returns the payment object.
     *
     * @param string|int    $ID
     * @param array         $fields
     *
     * @return array|false
     *
     * @throws Exception
     */
    public function get($ID, $fields = null)
    {
        $values = parent::get($ID, $fields);
        // If there is no pymtdetail object in the result, that means that payment is made from old object
        // Lets build the pymtdetail object and return
        if(!empty($values) && empty($values['PYMTDETAILS'])) {
            // Populate all the payment detail information
            $gManagerFactory = Globals::$g->gManagerFactory;
            $pymtDetailMgr = $gManagerFactory->getManager('arpymtdetail');
            $pymtDetails = $pymtDetailMgr->getPaymentDetailsFromPaymentRecords($ID);
            $values['PYMTDETAILS'] = $pymtDetails;
        }

        // In case of undeposited funds account, $values['UNDEPOSITEDACCOUNTNO'] contains the record# of the glaccount entry for undeposited funds
        // But it should have the account_no of the above mentioned entry.
        // So, we query the glaccount table and fetch the account_no for undeposited funds account entry
        // And replace the $values['UNDEPOSITEDACCOUNTNO'] with the new account number fetched
        if ( ! empty($values['UNDEPOSITEDACCOUNTNO']) ) {
            $glAcctMgr = Globals::$g->gManagerFactory->getManager('glaccount');
            $acctNoTitle = $glAcctMgr->GetAcctNoTitle($values['UNDEPOSITEDACCOUNTNO']);
            $values['UNDEPOSITEDACCOUNTNO'] = $acctNoTitle[0]['ACCT_NO'];
        }

        return $values;
    }

    /**
     * @param array $obj
     * @param array $ownedObjects
     *
     * @return bool success or failure
     */
    function GetOwnedObjectData(&$obj, $ownedObjects)
    {
        // If recordtype is multi entity payment, then no need to query on ARPYMTENTRY
        // as no direct lineitems are associated with this
        if (!empty($obj) && isset($obj['RECORDTYPE']) && $obj['RECORDTYPE'] === PRRECORD_TYPE_RECEIPT_MULTI_ENTITY) {
            self::$_isParentRecordMultiEntity = true;
            $ownedObjects = array_filter($ownedObjects, function($objRec) {
                return $objRec['entity'] !== 'ARPYMTENTRY';
            });
        }
        $ok = parent::GetOwnedObjectData($obj, $ownedObjects);
        //reset again for the next record
        self::$_isParentRecordMultiEntity = false;
        return $ok;
    }

    /**
     * Some of the payment related activities specific to various payment method eg: Amex, WF payments
     *
     * @param array $values the payment details data
     *
     * @return bool is this action is success or failure
     */
    protected function onAfterAdd(&$values)
    {
        // Don't do anything yet, lets do the post create activities in postAdd
        return true;
    }

    /**
     * @param array           $payment
     * @param PaymentResponse|null $response
     *
     * @return bool
     */
    public function postAdd(array &$payment, ?PaymentResponse $response = null): bool
    {
        $ok = parent::postAdd($payment, $response);
        if ( $ok ) {
            // update postedpayment for Global Search;
            $this->registerPostedPaymentInGlobalSearch($payment, 'arpostedpayment');
            $paymentEntry = Globals::$g->gManagerFactory->getManager('arpymtentry');
            if (isset($payment['ITEMS']) ) {
                foreach ( $payment['ITEMS'] as $paymentItem ) {
                    $paymentEntry->registerPostedPaymentEntryInGlobalSearch($paymentItem['ITEMS'][0] ?? [],
                                                                            'arpostedpaymentitem');
                }
            }
        }
        return $ok;
    }

    /**
     * @param array                $payment
     * @param bool                 $fromService
     * @param PaymentResponse|null $paymentResponse
     *
     * @return bool
     */
    public function checkAndConfirmPayment(
        array $payment, bool $fromService = true, ?PaymentResponse $paymentResponse = null
    ): bool
    {
        return $this->confirmPayment($payment, $fromService, $paymentResponse);
    }

    /**
     * After the payment we need to create payment detail entry, so lets return true.
     *
     * @return bool
     */
    protected function isPaymentDetailCreationAllowed()
    {
        return true;
    }

    /**
     * get the Inline application credit preference path
     *
     * @return string the preference path
     */
    public function getInlineCreditPrefPath()
    {
        return 'AR_LIMITCREDIT';
    }

    /**
     * @return array|null|string
     */
    public function getPaymentRecordType()
    {
        return PaymentUtils::ARPYMT_RECTYPE;
    }

    /**
     * @return APPaymentRequestResponseBuilder|PaymentRequestResponseBuilder
     */
    public function getPaymentRequestResponseBuilder()
    {
        return new ARPaymentRequestResponseBuilder();
    }

    /**
     * @return APDynamicCreditPaymentProcessor|ARDynamicCreditPaymentProcessor|DynamicCreditPaymentProcessor
     */
    public function getDynamicCreditPaymentProcessor()
    {
        return new ARDynamicCreditPaymentProcessor();
    }

    /**
     * @return APPaymentDataHelper|PaymentDataHelper
     */
    public function getPaymentDataHelper()
    {
        return new ARPaymentDataHelper();
    }

    /**
     * @param PaymentDTO $paymentDTO
     * @return string
     */
    public function getEntityID(PaymentDTO $paymentDTO)
    {
        return $paymentDTO->getCustomerId();
    }

    /**
     * @return string
     */
    public function getEntityType()
    {
        return 'customer';
    }

    /**
     * @param array $entitydata
     * @param PaymentRequest $paymentRequest
     */
    public function setEntityData($entitydata, PaymentRequest $paymentRequest)
    {
        $paymentRequest->setCustomer($entitydata);
    }

    /**
     * @return string
     */
    public function getPaymentDetailModulePath()
    {
        return 'arpymtdetail';
    }

    /**
     * @return string
     */
    protected function getFeatureKey()
    {
        return 'AR_ENABLE_WEIGHTED';
    }

    /**
     * @return APPaymentDetailProcessor|ARPaymentDetailProcessor
     */
    public function getPymtDetailProcessor()
    {
        return new ARPaymentDetailProcessor();
    }

    /**
     * @return string
     */
    public function getObjectType()
    {
        return 'RECEIVABLES';
    }

    /**
     * @param string $object
     * @param string $paymentRequestMethod
     * @return string
     */
    public function getPaymentMergeMethod(/** @noinspection PhpUnusedParameterInspection */ $object, /** @noinspection PhpUnusedParameterInspection */ $paymentRequestMethod)
    {
        return '';
    }

    /**
     * @param string $paymentMethod
     * @return bool
     */
    public function getEntityMoreInfo(/** @noinspection PhpUnusedParameterInspection */ $paymentMethod)
    {
        return false;
    }

    /**
     * Method is to check PaymentID sequence is enable for payment
     * @return bool
     */
    public function isPaymentIDSequenceEnable(): bool
    {
        return true;
    }

    /**
     * @return bool
     * @return bool
     */
    protected static function isARPaymentAutoBatchingEnabled()
    {

        global $kARid;
        /** @noinspection PhpUnusedLocalVariableInspection - TODO: Error handling? */
        $err = GetModulePreferences($kARid, $arPref);

        // is autobatching enabled?
        $isAutoBatching = ( in_array($arPref['RP_BATCHES'], array('D', 'M', 'E')) ? true : false );

        return $isAutoBatching;
    }

    /**
     * @return ARCreditPaymentDistributionHelper
     */
    public function getCreditPaymentDistributionHelper()
    {
        return new ARCreditPaymentDistributionHelper();
    }

    /**
     * @return ARPymtHelper
     */
    public function getPaymentHelper()
    {
        return new ARPymtHelper();
    }


    /**
     * @return ARPymtDetailManager|EntityManager
     */
    public function getPymtDetailMgr()
    {
        return Globals::$g->gManagerFactory->getManager('arpymtdetail');
    }

    /**
     * Validate the bank account
     *
     * @param array $values the transaction data
     *
     * @return bool false if error else true
     */
    protected function validateBankAccount($values)
    {
        return $this->validateFinancialEntity($values);
    }

    /**
     * Method to extract record keys for SFDC update
     * This is done for every sales invoice created in a Salesforce company
     *
     * @param PaymentResponse $response
     *
     * @return array
     */
    public function getInvoiceKeysForSfdcUpdate(PaymentResponse $response)
    {
        $postArr = [];
        $paymentDetailMap = $response->getPaymentInfoObj()->getPaymentDetailMap();
        // Loop through payment details map to get positive and negative invoice keys
        // Positive invoice keys are extracted from recordKey and associated with sales invoice payment
        // Negative invoice keys are extracted from negativeBillInvKey and associated with sales return
        // A PaymentDetailDTO will contain recordKey always
        // However, it may/may not contain negativeBillInvKey
        // A negativeBillInvKey will be populated only if credit is used while making accounts receivable payment
        foreach ( $paymentDetailMap as $item ) {
            foreach ( $item as $value ) {
                foreach ( $value as $paymentDetail ) {
                    if(is_array($paymentDetail)){
                        $postArr[] = $paymentDetail['RECORDKEY'];
                        $postArr[] = $paymentDetail['NEGATIVEINVOICEKEY'];
                    } else {
                        /** @var PaymentDetailDTO $paymentDetail */
                        $postArr[] = $paymentDetail->getRecordKey();
                        $postArr[] = $paymentDetail->getNegativeBillInvKey();
                    }
                }
            }
        }
        // remove the null value from the array
        $postArr = array_filter($postArr);

        //This scenario comes when we have inline credit more than postive line item
        //API throws and error as we dont find any prrecord in the query
        //So we have to skip this when empty response
        if ( ! empty($postArr) ) {
            // We need to query prrecord table to filter invoices coming from OE against above procured invoice ids
            $prRecMgr = Globals::$g->gManagerFactory->getManager('prrecord');
            $qry = [
                'selects' => ['RECORDNO'],
                'filters' => [[
                    ['RECORDNO', 'IN', array_unique($postArr)],
                    ['MODULEKEY', '=', Globals::$g->kSOid],
                ]],
            ];

            $prRecs = $prRecMgr->GetList($qry);
            // Extract the RECORDNO from all the records fetched.
            $postArr = array_column($prRecs, 'RECORDNO');
        }

        return $postArr;
    }

    /**
     * @param array $values
     * @param string $journalSymbol
     *
     * @return bool
     */
    protected function updateBatch($values, $journalSymbol)
    {
        return true;
    }

    /**
     * Change the transaction PR Batch
     *
     * @param array     $values
     * @param int       $origBatchKey  the original PR Batch Key
     * @param int       $newBatchKey   the new PR Batch Key
     * @param string    $journalSymbol the journal symbol
     *
     * @return bool false if error else true
     */
    protected function changeTransactionBatch($values, $origBatchKey, $newBatchKey, $journalSymbol)
    {
        // in case of payment, we dont need to post the batch from here, confirmPayment will be taking care
        $ok = true;
        // we need to check if new batchkey is not there as origBatchKey, validate and delete the prbatch
        if ( $newBatchKey != $origBatchKey && !empty($origBatchKey)) {
            $ok = $ok && $this->validateDeleteOrigPRBatch($origBatchKey);
        }
        return $ok;
    }

    /**
     * This function is the opposite of API_FormatObject, and allows the subclass to 'untransform' the outbound values
     * in a suitable PHP structure.  The API integration point is responsible
     * for formatting values -- Objects are responsible for formatting structures
     *
     * @param array $values Outbound object structure
     *
     * @return array unformatted structure
     */
    public function API_UnformatObject($values)
    {
        $values = parent::API_UnformatObject($values);

        foreach ( $values as &$nextObject ) {
            // Unset the offsets from the Read methods
            if ( ! empty($nextObject['OFFSETS']) ) {
                unset($nextObject['OFFSETS']);
            }
            foreach ($nextObject['ARPYMTENTRIES']['ARPYMTENTRY'] as &$arpymtentry)
            {
                unset($arpymtentry['LINEITEM']);
            }
        }

        unset($nextObject);

        return $values;
    }

    /**
     * @param bool $simulate
     * @param bool $fromGateway
     *
     * @return ARPaymentController|null
     */
    public function getPaymentController($simulate = false, $fromGateway = false)
    {
        return new ARPaymentController($simulate, $fromGateway);
    }

    /**
     * @param string $operation
     * @param array  &$values
     *
     * @return bool
     */
    function API_Validate($operation, &$values = null)
    {
        $ok = parent::API_Validate($operation, $values);
        $isFeatureEnabled = !FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('AR_ENABLE_WEIGHTED');
        if($ok && $isFeatureEnabled == false){
            Globals::$g->gErr->addError('AR-0099', __FILE__ . ":" . __LINE__, 'This object is not available to 
            this company, please contact customer support to enable this feature');
            $ok = false;
        }
        if(!empty($values['UNDEPOSITEDACCOUNTNO']) && !empty($values['FINANCIALENTITY'])){
            Globals::$g->gErr->addError(
                'AR-0100',
                GetFL(),
                'Accounts receivable payment request did not post.','',
                'Provide a valid undeposited funds account number or bank account or the batch with this payment. '
            );
            $ok = false;
        }
        return $ok;
    }

    /**
     * if multiple payment is enable
     * @return bool
     */
    public function isMultiEntityPaymentEnable(): bool
    {
        // check feature flag is enable or not
        return self::multiEntityPymtFeatureEnable()
            && self::multiEnitityPymtEnableForAR();
    }

    /**
     * @return bool
     * @throws Exception
     */
    public static function multiEntityPymtFeatureEnable(): bool
    {
        return true;
    }

    /**
     * @return bool
     */
    public static function multiEnitityPymtEnableForAR(): bool
    {
        return GetPreferenceForProperty(Globals::$g->kARid, 'MULTIENTITYPYMT') === 'true';
    }

    /**
     * @return bool
     * @throws Exception
     */
    public function isRoutingEnable()
    {
        return FeatureConfigManagerFactory::getInstance()->isFeatureEnabled($this->getFeatureKey());
    }

    /**
     * Figure out the batch date
     *
     * @param array $values the transaction data
     *
     * @return string the batch date
     */
    protected function getBatchDate($values)
    {
        if (!empty($values['RECEIPTDATE'])) {
            $batchDate = $values['RECEIPTDATE'];
        } else {
            $batchDate = parent::getBatchDate($values);
        }
        return $batchDate;
    }

    /**
     * Figure out if we allow negative total due on the transaction
     *
     * @return bool true if negative total due due is allowed else false
     */
    protected function allowNegativeTotal()
    {
        return true;
    }

    /**
     * Validate the payment method
     *
     * @param array $values the transaction data
     *
     * @return bool false if error else true
     */
    protected function validatePaymentMethod($values)
    {
        if ($this->isZeroPayment($values)) {
            return true;
        }
        return parent::validatePaymentMethod($values);
    }

    /**
     *  API_pruneFields
     *   This override of the Entity Manager version ensures that the values returned for the owned objects are also pruned.
     *
     * @param array           $values
     * @param string|string[] $fields
     *
     * @return array
     */
    function API_pruneFields(&$values, $fields)
    {
        return parent::API_pruneFieldsOwnedToo($values, $fields);
    }

    /**
     *
     * @param array $values object data
     *
     * @return bool true on success, false on error
     */
    public function isApprovalsAllowed($values)
    {
        return false;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    private function updatePayment(array &$values): bool
    {
        $source = 'ARPymtManager::deleteAndCreatePayment';
        $ok = $this->_QM->beginTrx($source);
        // set the update payment flag to true
        $this->setIsUpdatePayment(true);
        // First delete the payment record
        $ok = $ok && $this->Delete($values['RECORDNO']);
        $ok = $ok && $this->onUpdateHandleBankTransaction($values);
        if ($ok) {
            $payment = $this->translatePaymentData($values);
        }
        // Recreate the payment records again
        $ok = $ok && $this->invokePayment($payment);
        // reset the update payment flag to true
        $this->setIsUpdatePayment(false);

        if (!$ok || !$this->_QM->commitTrx($source)) {
            $ok = false;
            $msg = "There is a problem updating the payment record, please check the input.";
            Globals::$g->gErr->addError('AR-0547', __FILE__ . ':' . __LINE__, $msg);
            $this->_QM->rollbackTrx($source);
        }
        return $ok;
    }

    /**
     * @param array $values
     * @param array $paymentData
     *
     * @return array
     */
    private function getUpdatePaymentValues(array &$values, array $paymentData): array
    {
        $fastUpdateFields = [
            'DOCNUMBER',
            'DESCRIPTION',
            'SUPDOCID',
        ];
        // add this into preset $values, so in smartlink validate we should not call smart events for multi entity payment
        $values['RECORDTYPE'] = $paymentData['RECORDTYPE'];

        $isMultiEntityPayment = $paymentData['RECORDTYPE'] === self::ARPYMT_MULTI_ENTITY_RECTYPE;
        // Payername can be updated only in case of multi entity payment
        // and for the child payment that were part of multi entity payment
        if ($isMultiEntityPayment || self::$isPartOfMultiEntityPayment) {
            $fastUpdateFields[] = 'PAYERNAME';
        } else {
            // unset the payername as its not allowed to update for single entity payment
            unset($values['PAYERNAME']);
        }
        $updateValues = [];
        foreach ($fastUpdateFields as $key) {
            if (isset($values[$key])) {
                $updateValues[$key] = $values[$key];
            }
        }
        return $updateValues;
    }

    /**
     * @param array  $updateValues
     * @param array  $paymentData
     *
     * @return bool
     * @throws IAException
     */
    private function UpdatePayments(array &$updateValues, array $paymentData): bool
    {
        $ok = true;
        $paymentKey = $paymentData['RECORDNO'];
        $isMultiEntityPayment = $paymentData['RECORDTYPE'] === BasePymtManager::ARPYMT_MULTI_ENTITY_RECTYPE;

        if ($isMultiEntityPayment) {
            $paymentKeys = ARMultiCustomerPymtManager::getChildPaymentRecordsByParent($paymentKey, true);
            // we need to set this flags, so in case of child rp record modify, validation will not fail for fast Update,
            self::$isPartOfMultiEntityPayment = true;
            foreach ($paymentKeys as $paymentKey => $recordType) {
                // update the rp records too in case of multi entity payment
                if ($recordType !== $this->getRecordType()) {
                    continue;
                }
                $values = array_merge($updateValues, ['RECORDNO' => $paymentKey]);
                // update the child rp records
                $ok = $ok && $this->set($values);
                if (!$ok) {
                    break;
                }
            }
            self::$isPartOfMultiEntityPayment = false;
            $this->isMultiEntityPymtUpdate = true;
        } else {
            // unset the supdocid as we dont need to update the attachment to child payments
            unset($updateValues['SUPDOCID']);
            if (empty($updateValues)) {
                logToFileInfo("ARPymtManager::UpdatePayments: No update values found for payment record $paymentKey.");
                return $ok;
            }
            // update the rp related(child) payments
            $ok = $ok && $this->updateRelatedPayments($paymentKey, $updateValues);
        }

        return $ok;
    }

    /**
     * @param string $paymentKey
     * @param array  $values
     *
     * @return bool
     */
    private function updateRelatedPayments(string $paymentKey, array $values): bool
    {
        $ok = true;
        $allChildPayments = $this->getRelatedPaymentRecords($paymentKey);
        foreach ($allChildPayments as $payment) {
            // for each prrecord type, call the respective manager to update the records created as part of this payment
            $recordType = $payment['RECORDTYPE'];
            // Do not update inline credit records (negative invoice and adjustment)
            if (!in_array($recordType, static::$inlineCreditRecordTypes)) {
                // call to set for fast update
                $ok = $ok && $this->updateRelatedPayment($recordType, $payment['RECORD#'], $values);
            }
            if (!$ok) {
                break;
            }
        }

        return $ok;
    }

    /**
     * @param string $paymentKey
     *
     * @return array
     */
    private function getRelatedPaymentRecords(string $paymentKey): array
    {
        // lets get all the payments corresponding to the input payment request except the inline credits and adjustments
        $qry = "SELECT distinct pr.record#, pr.recordtype " .
            "FROM prrecordmst pr, prentrypymtrecs prp " .
            "WHERE prp.cny# = :1 AND prp.parentpymt = :2 " .
            "AND pr.cny# = prp.cny# AND pr.record# = prp.paymentkey " .
            "AND pr.record# != prp.parentpymt " .
            "AND prp.recordkey != prp.paymentkey " .
            "order by pr.record# desc";

        $result = QueryResult([$qry, GetMyCompany(), $paymentKey]);
        return is_array($result) ? $result : [];
    }

    /**
     * @param string $recordType
     * @param string $recordNo
     * @param array  $values
     *
     * @return bool
     */
    private function updateRelatedPayment(string $recordType, string $recordNo, array $values): bool
    {
        // if the description is not empty and the record type is discount or exchange rate change, then unset the description
        // as its default added and not allowed to update as well
        $descNotSupportedRecordTypes = [self::ARDISCOUNT_RECTYPE, self::AREXCHGLPYMT_RECTYPE];
        if (isset($values['DESCRIPTION']) && in_array($recordType, $descNotSupportedRecordTypes)) {
            unset($values['DESCRIPTION']);
        }
        // if no values to update, return true
        if (empty($values)) {
            return true;
        }
        // it will update rm, rd, ro records of a rp record
        $entity = BasePymtManager::$recordTypeToEntity[$recordType];
        $entityMgr = Globals::$g->gManagerFactory->getManager($entity, true);
        logToFileInfo("ARPymtManager::fastUpdatePayment: Updating $recordType the payment record $recordNo");
        $values['RECORDNO'] = $recordNo;
        $ok = $entityMgr->set($values);
        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     * @throws IAException
     */
    private function buildMultiEntityPymtKeyMapForUpdate(array $values): bool
    {
        if ($values['RECORDTYPE'] !== self::ARPYMT_MULTI_ENTITY_RECTYPE) {
            return true;
        }

        $selectedEntityMap = array_column($values['SELECTEDTXN'], 'CUSTOMERID', 'CUSTOMERID');
        if (count($selectedEntityMap) < 2) {
            // need to throw an error by saying, multi customer can not change to single customer
            Globals::$g->gErr->addError(
                'AR-0550', __FILE__.'.'.__LINE__,
                "You cannot change a payment from being associated with multiple customers to being associated with a single customer during an update operation."
            );
            return false;
        }

        $childPymtData = $this->getChildPaymentDetailsByMultPymtKey(
            $values['RECORDNO'], ['RECORDNO','CUSTOMERID']
        );
        if (empty($childPymtData)) {
            Globals::$g->gErr->addError(
                'AR-0551', __FILE__.'.'.__LINE__,
                "No child payments found for the multi customer payment."
            );
            return false;
        }
        $childPymtData = array_column($childPymtData, 'RECORDNO', 'CUSTOMERID');
        // this can be used to validate which payment needs to be deleted and for upsert record# for the payment
        $this->setMultiEntityPymtKeyMapForUpdate([
            'PAYMENTS' => $childPymtData,
            'SELECTEDTXNS' => $selectedEntityMap,
        ]);

        return true;
    }

    /**
     * API entry point that merges given update values with existing values.
     *  For term objects, the VALUE field is read-only for Set, so make sure the VALUE
     *  is not passed in the inputs.
     *
     * @param array $object Existing object values
     * @param array $values Newly passed in values
     *
     * @return array merged values
     */
    function API_UpdateMerge(&$object, &$values)
    {
        // if the values contains pymtdetails, then unset the existing pymtdetails
        if (!empty($values['ARPYMTDETAILS'])) {
            unset($object['ARPYMTDETAILS']);
        }
        // translate the dependent fields before calling the parent API_UpdateMerge
        $this->translateDependentFieldsBeforeAPI_UpdateMerge($object, $values);

        return parent::API_UpdateMerge($object, $values);
    }

    /**
     * @param array $object
     * @param array $values
     *
     * @return void
     */
    private function translateDependentFieldsBeforeAPI_UpdateMerge(array &$object, array &$values): void
    {
        // if auto summary is enabled, unset the prbatch and prbatchkey from object
        // so that it will use the batch and post the payment in it
        if (self::isARPaymentAutoBatchingEnabled()) {
            unset($object['PRBATCH'], $object['PRBATCHKEY']);
        }
        $dependentFields = ['PRBATCH', 'PRBATCHKEY', 'FINANCIALENTITY', 'UNDEPOSITEDACCOUNTNO'];
        foreach ($dependentFields as $field) {
            if (!empty($values[$field])) {
                // unset all other dependent fields except the current field
                foreach ($dependentFields as $unsetField) {
                    if ($unsetField !== $field) {
                        unset($object[$unsetField]);
                    }
                }
                // break the loop after unsetting other dependent fields
                break;
            }
        }
    }

    /**
     * @param string $multiEntityPymtKey
     * @param array  $selects
     *
     * @return array
     */
    public function getChildPaymentDetailsByMultPymtKey(string $multiEntityPymtKey, array $selects): array
    {
        if (empty($multiEntityPymtKey) || empty($selects)) {
            return [];
        }

        $qry = [
            'selects' => $selects,
            'filters' => [ [
                [ 'MULTIENTITYPYMTKEY', '=', $multiEntityPymtKey ],
                [ 'RECORDTYPE', '=', self::ARPYMT_RECTYPE ],
            ] ],
        ];

        return $this->GetList($qry);
    }

    /**
     * @param string $multiEntityPymtKey
     *
     * @return array
     */
    public static function getChildPaymentItemsByMultiEntityPymt(string $multiEntityPymtKey): array
    {
        if (empty($multiEntityPymtKey)) {
            return [];
        }
        $filter = [
            'filters' => [[
                ['RECORDTYPE', '=', self::ARPYMT_RECTYPE],
                ['MULTIENTITYPYMTKEY', '=', $multiEntityPymtKey]
            ]]
        ];
        $arpymtentryMgr = Globals::$g->gManagerFactory->getManager('arpymtentry');
        $result = $arpymtentryMgr->GetList($filter);
        return is_array($result) ? $result : [];
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    private function getPaymentTransactionDetails(array &$values): bool
    {
        if (empty($values['PYMTDETAILS'])) {
            return false;
        }
        $selectedTxn = [];
        $txnDiscountMap = [];
        $isCustomDiscountEnable = GetPreferenceForProperty(Globals::$g->kARid, 'USERENTEREDDISCOUNT') === 'true';
        foreach ($values['PYMTDETAILS'] as $pymtDetail) {
            $recordKey = $pymtDetail['RECORDKEY'] ?? $pymtDetail['POSADJKEY'];
            if (empty($recordKey)) {
                continue;
            }
            $selectedTxn[$recordKey] = true;

            if ($isCustomDiscountEnable && !empty($pymtDetail['DISCOUNTKEY'])) {
                $txnDiscountMap[$recordKey] = ibcadd(($txnDiscountMap[$recordKey] ?? 0), abs($pymtDetail['TRX_DISCOUNTAMOUNT']));
            }
        }
        // set it true only if the custom discount is enabled and the txnDiscountMap is not empty
        $isAddCustomDiscount = $isCustomDiscountEnable && !empty($txnDiscountMap);

        if (empty($selectedTxn)) {
            return false;
        }

        $selects = ['RECORDNO', 'CUSTOMERID', 'CURRENCY'];
        if ($isAddCustomDiscount) {
            $selects = array_merge($selects, [
                'TRX_TOTALENTERED', 'TRX_TOTALDUE', 'TRX_TOTALSELECTED', 'TERMKEY',
                'WHENCREATED', 'WHENDUE', 'TERMVALUE', 'TAXSOLUTIONID',
                ]
            );
        }
        $filters = [
            'selects' => $selects,
            'filters' => [[
                ['RECORDNO', 'IN', array_keys($selectedTxn)],
                ['RECORDTYPE', 'IN', $this->getPaidTransactionRecordTypes()],
            ]],
            'usesubquery' => false,
        ];
        $receivableManager = Globals::$g->gManagerFactory->getManager('receivable');

        // get the selected transactions data
        $prRecs = $receivableManager->GetList($filters);

        if (empty($prRecs)) {
            return false;
        }
        // populate discount amount only if we need to add custom discount
        if ($isAddCustomDiscount) {
            foreach ($prRecs as $row) {
                // if discount map is empty for the record, then continue
                if (empty($txnDiscountMap[$row['RECORDNO']])) {
                    continue;
                }
                $receivableManager->getDiscountDetailForInvoice($row);
                $isTermDiscountEligible = ($row['DISCOUNTTIMESTAMP'] >= strtotime($values['PAYMENTDATE'] ?? $values['WHENPAID']))
                                      && ibccomp($row['TRX_TOTALSELECTED'], $row['TRX_TOTALDUE']) === 0;
                $discountApplied = $txnDiscountMap[$row['RECORDNO']] ?? 0;
                if ($isTermDiscountEligible && ibccomp($discountApplied, abs($row['DISCOUNTAMOUNT'])) === 0) {
                    // unset it from the txnDiscountMap, as its term discount
                    unset($txnDiscountMap[$row['RECORDNO']]);
                }
            }
            // set the custom discount map
            $values['CUSTOMSELECTEDDISCOUNTMAP'] = $txnDiscountMap;
        }
        $values['SELECTEDTXN'] = $prRecs;
        return true;
    }

    /**
     * @param array $values
     *
     * @return array
     */
    private function translatePaymentData(array &$values): array
    {
        // translate the values according to create
        $values['PAYMENTDATE'] ??= $values['WHENPAID'];
        $values['AMOUNTTOPAY'] ??= $values['TOTALENTERED'];
        $values['TRX_AMOUNTTOPAY'] ??= $values['TRX_TOTALENTERED'];

        $paymentBaseCurr = $values['BASECURR'];
        if (!empty($values['FINANCIALENTITY'])) {
            $bankObj = $this->getBankDetails($values['FINANCIALENTITY']);
            $bankCurrency = $bankObj[0]['CURRENCY'] ?? null;
        }
        if (empty($bankCurrency)) {
            $bankCurrency = $paymentBaseCurr;
        }
        $txnCurrency = $values['SELECTEDTXN'][0]['CURRENCY'] ?? null;

        //B2F Boolean
        $foreignBankToBaseInv = ( isset($txnCurrency) && $txnCurrency == $paymentBaseCurr
            && $bankCurrency != $paymentBaseCurr );

        $this->translateOverPaymentFromLineItem($values, $foreignBankToBaseInv);
        $pymtDetails = $this->translatePymtDetails($values, $foreignBankToBaseInv);

        /** @var ARPymtHelper $arpymtHelper */
        $arpymtHelper = $this->getPaymentHelper();
        return $arpymtHelper->buildPaymentRequestForEdit($values, $pymtDetails);
    }

    /**
     * @param array $values
     * @param bool  $foreignBankToBaseInv
     *
     * @return array
     */
    private function translatePymtDetails(array $values, bool $foreignBankToBaseInv): array
    {
        // No need to translate the pymtdetails, if its empty
        if (empty($values['PYMTDETAILS'])) {
            return $values['PYMTDETAILS'] ?? [];
        }
        $pymtDetails = [];
        $excludeColumns = [
            'RECORDNO', 'PAYMENTDATE', 'CURRENCY', 'WHENCREATED', 'WHENMODIFIED',
            'CREATEDBY', 'MODIFIEDBY', 'STATE', 'MODULEKEY',
            'EXCHGAINLOSSKEY', 'EXCHGAINLOSSENTRYKEY', 'EXCHGAINLOSSPARENTPYMTKEY',
            'PARENTPAYMENTKEY', 'PAYMENTKEY', 'PAYMENTENTRYKEY', 'PAYMENTAMOUNT',
            'POSTEDOVERPAYMENTKEY', 'POSTEDOVERPAYMENTENTRYKEY',
            'POSTEDADVANCEKEY', 'POSTEDADVANCEENTRYKEY',
            // no need to pick the amount fields, need only trx amount fields
            'EXCHGAINLOSSAMOUNT', 'PAYMENTAMOUNT', 'INLINEAMOUNT', 'ADJUSTMENTAMOUNT',
            'POSTEDADVANCEAMOUNT', 'POSTEDOVERPAYMENTAMOUNT', 'NEGATIVEINVOICEAMOUNT',
            // no need to pick the discount fields, need only discount date
            'DISCOUNTKEY', 'DISCOUNTENTRYKEY', 'DISCOUNTAMOUNT', 'TRX_DISCOUNTAMOUNT',
        ];
        foreach ($values['PYMTDETAILS'] as $entry) {
            $recordKey = $entry['RECORDKEY'] ?? $entry['POSADJKEY'];
            if (empty($recordKey)) {
                continue;
            }
            $filteredEntry = [];
            $addFieldCount = 0;
            foreach ($entry as $key => $val) {
                if ($val !== null && $val !== '' && !in_array($key, $excludeColumns)) {
                    // in case of foreign bank to base invoice, need to pick the amount fields
                    if ($foreignBankToBaseInv && !empty($entry['PAYMENTAMOUNT']) && $key === 'TRX_PAYMENTAMOUNT') {
                        // if foreign bank to base invoice, then set the TRX_PAYMENTAMOUNT to PAYMENTAMOUNT
                        $filteredEntry[$key] = $entry['PAYMENTAMOUNT'];
                    } else if ($key === 'DISCOUNTDATE') {
                        if (!empty($values['CUSTOMSELECTEDDISCOUNTMAP'][$recordKey])) {
                            $filteredEntry['TRX_TOTALDISCOUNTAPPLIED'] = $values['CUSTOMSELECTEDDISCOUNTMAP'][$recordKey];
                        } else {
                            // if DISCOUNTDATE is set, then set the PAYMENTDATE to DISCOUNTDATE
                            $filteredEntry[$key] = $values['PAYMENTDATE'] ?? $val;
                        }
                    } else {
                        $filteredEntry[$key] = $val;
                    }
                    if (!in_array($key, ['ENTRYKEY', 'POSADJKEYENTRYKEY'])) {
                        $addFieldCount++;
                    }
                }
            }
            // check if the filteredEntry has more than one field other than the lineitems
            // This is to make sure, while building the request, its should take only transaction fields not rm
            if ($addFieldCount > 1){
                $pymtDetails[] = $filteredEntry;
            }
        }
        return $pymtDetails;
    }

    /**
     * @param array $values
     * @param bool  $foreignBankToBaseInv
     *
     * @return void
     */
    public function translateOverPaymentFromLineItem(array &$values, bool $foreignBankToBaseInv): void
    {
        // if overpaymentamount is already part of $values, no need to calculate
        if (isset($values['OVERPAYMENTAMOUNT'])) {
            return;
        }
        $isOverPayment = ((float) ibcsub($values['TRX_TOTALENTERED'], $values['TRX_TOTALSELECTED'])) > 0;
        if (!$isOverPayment) {
            return;
        }
        if ($values['RECORDTYPE'] === self::ARPYMT_MULTI_ENTITY_RECTYPE && empty($values['ITEMS'])) {
            // fetch the all line items for the multi entity payment
            $values['ITEMS'] = self::getChildPaymentItemsByMultiEntityPymt($values['RECORDNO']);
        }
        // if payment is not from foreign bank to base invoice, then no need to consider AMOUNT fields,
        // it can be TRX_AMOUNT only
        $alias = !$foreignBankToBaseInv ? 'TRX_' : '';
        foreach ($values['ITEMS'] ?? [] as $item) {
            $totalPaid = (float)ibcadd(
                $item[$alias.'TOTALPAID'], ibcadd($item[$alias.'TOTALSELECTED'], $item[$alias.'AMOUNTRETAINED'])
            );
            $trxOverPaymentAmt = (float)ibcsub($item[$alias.'AMOUNT'], $totalPaid);
            if ($trxOverPaymentAmt > 0) {
                $values['OVERPAYMENTAMOUNT'] = $trxOverPaymentAmt;
                $values['OVERPAYMENTDEPARTMENTID'] = $item['DEPARTMENTID'];
                $values['OVERPAYMENTCUSTOMERID'] = $item['CUSTOMERID'];
                $values['OVERPAYMENTLOCATIONID'] = $item['LOCATIONID'];
                $values['OVERPAYMENTCLASSID'] = $item['CLASSID'];
                $values['OVERPAYMENTPROJECTID'] = $item['PROJECTID'];
                break;
            }
        }
    }

    /**
     * @param array $values
     *
     * @return bool
     * @throws IAException
     */
    private function validateDraftPaymentBeforeSubmit(array &$values): bool
    {
        // get the transaction details that are selected for the payment
        if (!$this->getPaymentTransactionDetails($values)) {
            Globals::$g->gErr->addError(
                'AR-0555', __FILE__.'.'.__LINE__,
                "No transactions found for the payment."
            );
            return false;
        }
        $payment = $this->translatePaymentData($values);
        $paymentDTO = new PaymentDTO($payment);
        $paymentDTO->setIsUpdatePayment(true);
        $paymentDTO->setPaymentNo($payment['RECORDNO']);
        $requestBuilder = $this->getPaymentRequestResponseBuilder();
        $paymentRequest = new PaymentRequest();
        $requestBuilder->buildPaymentRequest($paymentDTO, $paymentRequest);
        $paymentController = $this->getPaymentController();

        return $paymentController->validateRequest($paymentRequest);
    }

    /**
     * @param string $_verb
     * @param array  $_object
     *
     * @return bool success or failure
     */
    protected function handleVerb($_verb, $_object)
    {
        $ok = true;
        switch ($_verb) {
            case 'post':
                $ID = $this->GetKeyValue($_object);
                $ok = $ok && $this->submitPayment($ID);
                break;
            default:
                $ok = $ok && parent::handleVerb($_verb, $_object);
                break;
        }
        return $ok;
    }

    /**
     * @param int $count
     *
     * @return bool
     */
    protected function canAddSubmitSummary(int $count): bool
    {
        return $count > 0;
    }

    /**
     * This function validates the values against all the smartlinks
     *
     * @param array $actions        array of actions
     * @param array $values         in the object.
     * @param bool  $fromFastUpdate true if is invoked in Fast Update path
     *
     * @return bool
     */
    function smartlinkValidate($actions, $values, $fromFastUpdate = false)
    {
        // if the payment is multi entity payment, then no need to validate the smartlinks
        if (($values['RECORDTYPE'] ?? null) === self::ARPYMT_MULTI_ENTITY_RECTYPE) {
            return true;
        }
        return parent::smartlinkValidate($actions, $values, $fromFastUpdate);
    }

    /**
     * @return string
     */
    public function GetCustomComponentsEntity()
    {
        if ($this->isMultiEntityPymtUpdate) {
            return 'armulticustomerpymt';
        }
        return parent::GetCustomComponentsEntity();
    }

    /**
     * API_Validate
     *   This function performs a validation of the intended API operation.  If the operation
     *  cannot be performed, this function return false, WITHOUT an appropriate error added to the
     *  global error stack.
     *
     * @param string        $operation  api operation to be validated
     * @param array|string  $values     object data
     * @param int           $checkOpId  the successful checked op id
     *
     * @return bool
     */
    function API_ValidateWithoutError($operation, &$values = null, &$checkOpId = null)
    {
        $ok = parent::API_ValidateWithoutError($operation, $values, $checkOpId);
        if ($ok && !empty($values) && in_array($operation, [API_CREATE, API_UPDATE])) {
            $action = $values['ACTION'] ?? '';
            if (empty($action)) {
                $action = $operation === API_CREATE ? self::SUBMIT_ACTION : self::DRAFT_ACTION;
            }
            if ($action === self::SUBMIT_ACTION) {
                $ok = $ok && $this->hasPostPermission();
            }
        }
        return $ok;
    }

    /**
     * @param array $reconToHandle
     *
     * @return bool
     *
     * @throws Exception
     */
    protected function handleBankTransaction($reconToHandle)
    {
        return ReconciliationUtils::handleBankTransaction($reconToHandle, $this->getRecordType());
    }

    /**
     * Handle the associated reconciliation entries if there is any
     *
     * @param array $values
     *
     * @return bool
     * @throws Exception
     */
    private function onUpdateHandleBankTransaction(&$values) : bool
    {
        $ok = true;
        if (!empty($values)) {
            $origData = $this->get($values['RECORDNO']);
            if (!empty($origData)) {
                $values['DOCSOURCE'] = $origData['DOCSOURCE'];
                if ($origData['FINANCIALENTITY'] !== $values['FINANCIALENTITY'] ||
                    ibccomp($origData['TRX_TOTALENTERED'], $values['AMOUNTSELECTED']) != 0) {
                    $ok = $this->handleBankTransaction([$values['RECORDNO']]);
                }
            }
        }
        return $ok;
    }

    /**
     * @return string
     */
    protected function getSubmitActionField() : string
    {
        return 'ACTION';
    }

    /**
     * @return string
     */
    protected function getSubmitActionValue() : string
    {
        return self::SUBMIT_ACTION;
    }
}
