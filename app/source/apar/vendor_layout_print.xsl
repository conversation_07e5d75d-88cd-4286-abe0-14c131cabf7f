<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format">
	<xsl:template match="/">
		<xsl:apply-templates select="ROOT"/>
	</xsl:template>
	<xsl:template match="ROOT">
		<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:svg="http://www.w3.org/2000/svg">
			<!--  master template -->
			<fo:layout-master-set>
				<!-- <fo:simple-page-master margin-right="0.60in" margin-left="0.60in" margin-bottom="0in" margin-top="0.5in" page-width="8.5in" page-height="11.0in" master-name="first"> -->
				<fo:simple-page-master 
						margin-right="0.60in" 
						margin-left="0.00in" 
						margin-bottom="0in" 
						margin-top="0.5in" 
						page-width="8.5in" 
						page-height="11.0in" 
						master-name="first">
					<!-- <fo:region-body overflow="auto" margin-top="0.20in" margin-bottom="0.60in"/> -->
					<fo:region-body overflow="visible" margin-top="0.20in" margin-bottom="0.60in"/> 
					<fo:region-before extent="1.00in"/>
					<fo:region-after extent="0.60in"/>
				</fo:simple-page-master>
			</fo:layout-master-set>
			<!--  page printing -->
			<fo:page-sequence master-reference="first">
				<fo:static-content flow-name="xsl-region-before">
					<!-- static page content -->
					<!-- Letterhead -->
					<!-- company logo -->
                    <xsl:if test="COMPANY/LOGO and COMPANY/LOGO != ''">
                        <fo:block-container height="0.6in" width="1.0in" top="0in" left="0.80in" position="absolute">
                            <fo:block>
                                <fo:external-graphic max-height="0.6in" max-width="1.0in" src="{COMPANY/LOGO}"/>
                            </fo:block>
                        </fo:block-container>
                    </xsl:if>
					<!-- company fields -->
					<fo:block-container height="0.7in" width="2.5in" top="0.55in" left="0.72in" position="absolute">
						<fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="10pt">
							<xsl:value-of select="COMPANY/TITLE"/>
						</fo:block>
						<fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="10pt">
							<xsl:value-of select="COMPANY/ADDRESS1"/>
						</fo:block>
						<fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="10pt">
							<xsl:value-of select="COMPANY/ADDRESS2"/>
						</fo:block>
						<fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="10pt">
							<xsl:value-of select="COMPANY/CITY"/>
							<xsl:text>, </xsl:text>
							<xsl:value-of select="COMPANY/STATE"/>
							<xsl:text> </xsl:text>
							<xsl:value-of select="COMPANY/ZIPCODE"/>
						</fo:block>
						<fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="10pt">
							<xsl:value-of select="COMPANY/CONTACTPHONE"/>
						</fo:block>
					</fo:block-container>
					<!-- Title -->
					<!-- Title background -->
					<fo:block-container height="0.61in" width="2.6in" top="0in" left="4.60in" position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="2.6in" height="0.61in">
									<svg:rect x="0.15in" y="0in" width="2.3in" height="0.3in" style="fill: #BBBBBB"/>
									<svg:circle cx="0.15in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
									<svg:circle cx="2.45in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- Title Text -->
					<fo:block-container height="0.61in" width="2.3in" top=".08in" left="5.0in" position="absolute">
						<fo:block text-align="start" color="#ffffff" line-height="15pt" font-family="Times Roman" font-size="13pt" font-weight="bold">
						<xsl:value-of select="RENAMETERM/Term_Vendor"/><xsl:text> IA.INFORMATION</xsl:text>						</fo:block>
					</fo:block-container>
					<!-- Up-right static -->
					<!-- Vertical Line under title -->
					<fo:block-container height="0.6in" width="0.001in" top="0.3in" left="5.95in" position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="0.001in" height="0.6in">
									<svg:rect x="0in" y="0in" width="0.0001in" height="0.6in" style="fill: #FFFFFF; stroke:black"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- Text at the right of the vertical line -->
					<fo:block-container height="0.6in" width="0.5in" top="0.60in" left="6.05in" position="absolute">
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="12pt">
							<xsl:value-of select="REC/VENDORID"/>
						</fo:block>
					</fo:block-container>
					<!-- Text at the left of the vertical line -->
					<fo:block-container height="0.6in" width="0.5in" top="0.6in" left="5.35in" position="absolute">
						<fo:block text-align="end" line-height="11pt" font-family="Helvetica" font-size="8pt">
						<xsl:value-of select="RENAMETERM/Term_Vendor"/>						</fo:block>
						<fo:block text-align="center" line-height="11pt" font-family="Helvetica" font-size="7pt">
							<xsl:if test="REC/STATUS and (REC/STATUS != 'Active')">
								(<xsl:value-of select="REC/STATUS"/>)
							</xsl:if> 
						</fo:block>
					</fo:block-container>
				</fo:static-content>
				<fo:static-content flow-name="xsl-region-after">
					<!-- footer - page number -->
					<fo:block-container height="0.2in" width="7.7in" top="0.01in" left=".4in" position="absolute">
						<fo:block>
							<fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
						</fo:block>
					</fo:block-container>
					<fo:block-container height="0.5in" width="7.7in" top="0.20in" left=".4in" position="absolute">
						<fo:table height="0.25in" width="7.7in">
							<fo:table-column column-width="3.85in"/>
							<fo:table-column column-width="3.85in"/>
							<fo:table-body font-family="Helvetica" font-weight="normal" font-size="8pt">
								<fo:table-row line-height="11pt">
									<fo:table-cell>
										<fo:block border-width="1.0pt" border-color="white" text-align="start" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">
										<xsl:value-of select="RENAMETERM/Term_Vendor"/>										</fo:block>
									</fo:table-cell>
									<fo:table-cell>
										<fo:block border-width="1.0pt" border-color="white" text-align="end" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">IA.PAGE <fo:page-number/>
										</fo:block>
									</fo:table-cell>
								</fo:table-row>
							</fo:table-body>
						</fo:table>
					</fo:block-container>
				</fo:static-content>
				<fo:flow flow-name="xsl-region-body">
			    <!-- dynamic page content -->
					<!-- Fields 1 -->
					<fo:block-container width="6.0in" height="6in" top="1.82in" left="0.72in" position="absolute">
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.RECORD_NUMBER</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/RECORDNO"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:value-of select="RENAMETERM/Term_Vendor"/><xsl:text>IA.ID</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/VENDORID"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:value-of select="RENAMETERM/Term_Vendor"/><xsl:text> IA.NAME</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/NAME"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.LAST_NAME</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/LASTNAME"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.FIRST_NAME</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/FIRSTNAME"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.MIDDLE_NAME</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/INITIAL"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.PRINT_AS</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/PRINTAS"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.DONT_INCLUDE_IN_COMPANY_S_CONTACT_LIST</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/HIDEDISPLAYCONTACT"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.ADDRESS_LINE_1</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/MAILADDRESS/ADDRESS1"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.ADDRESS_LINE_2</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/MAILADDRESS/ADDRESS2"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.CITY</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/MAILADDRESS/CITY"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.STATE_PROVINCE_1</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/MAILADDRESS/STATE"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.ZIP_CODE</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/MAILADDRESS/ZIP"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.COUNTRY_CODE</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/MAILADDRESS/COUNTRYCODE"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.PHONE_NUMBER</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/PHONE1"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.CELLULAR_PHONE_NUMBER</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/CELLPHONE"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.PAGER_NUMBER</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/PAGER"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.FAX_NUMBER</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/FAX"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.EMAIL_ADDRESS</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/EMAIL1"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.SECONDARY_EMAIL_ADDRESSES</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/EMAIL2"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.URL</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/URL1"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.ONE_TIME</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/ONETIME"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.THIS_VENDOR_IS_AN_OWNER</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/ISOWNER"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>A.STATUS</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/STATUS"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.CONTACT_NAME</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/CONTACTNAME"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.COMPANY_NAME</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/COMPANYNAME"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.MR_MS_MRS</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/PREFIX"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.SECONDARY_PHONE_NUMBER</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/PHONE2"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.SECONDARY_URL</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/URL2"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.STATUS</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/STATUS"/>
                                                        </fo:block>
					</fo:block-container>
				<!-- Multiline 1 -->
				</fo:flow>
			</fo:page-sequence>
			<!--  page printing -->
			<fo:page-sequence master-reference="first">
				<fo:static-content flow-name="xsl-region-before">
					<!-- static page content -->
					<!-- Letterhead -->
					<!-- company logo -->
                    <xsl:if test="COMPANY/LOGO and COMPANY/LOGO != ''">
                        <fo:block-container height="0.6in" width="1.0in" top="0in" left="0.80in" position="absolute">
                            <fo:block>
                                <fo:external-graphic max-height="0.6in" max-width="1.0in" src="{COMPANY/LOGO}"/>
                            </fo:block>
                        </fo:block-container>
                    </xsl:if>
					<!-- company fields -->
					<!-- Title -->
					<!-- Title background -->
					<fo:block-container height="0.61in" width="2.6in" top="0in" left="4.60in" position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="2.6in" height="0.61in">
									<svg:rect x="0.15in" y="0in" width="2.3in" height="0.3in" style="fill: #BBBBBB"/>
									<svg:circle cx="0.15in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
									<svg:circle cx="2.45in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- Title Text -->
					<fo:block-container height="0.61in" width="2.3in" top=".08in" left="5.0in" position="absolute">
						<fo:block text-align="start" color="#ffffff" line-height="15pt" font-family="Times Roman" font-size="13pt" font-weight="bold">
<xsl:value-of select="RENAMETERM/Term_Vendor"/><xsl:text> IA.INFORMATION</xsl:text>						</fo:block>
					</fo:block-container>
					<!-- Up-right static -->
					<!-- Vertical Line under title -->
					<fo:block-container height="0.6in" width="0.001in" top="0.3in" left="5.95in" position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="0.001in" height="0.6in">
									<svg:rect x="0in" y="0in" width="0.0001in" height="0.6in" style="fill: #FFFFFF; stroke:black"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- Text at the right of the vertical line -->
					<fo:block-container height="0.6in" width="0.5in" top="0.60in" left="6.05in" position="absolute">
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="12pt">
							<xsl:value-of select="REC/VENDORID"/>
						</fo:block>
					</fo:block-container>
					<!-- Text at the left of the vertical line -->
					<fo:block-container height="0.6in" width="0.5in" top="0.6in" left="5.35in" position="absolute">
						<fo:block text-align="end" line-height="11pt" font-family="Helvetica" font-size="8pt">
<xsl:value-of select="RENAMETERM/Term_Vendor"/>						</fo:block>
						<fo:block text-align="center" line-height="11pt" font-family="Helvetica" font-size="7pt">
							<xsl:if test="REC/STATUS and (REC/STATUS != 'Active')">
								(<xsl:value-of select="REC/STATUS"/>)
							</xsl:if> 
						</fo:block>
					</fo:block-container>
				</fo:static-content>
				<fo:static-content flow-name="xsl-region-after">
					<!-- footer - page number -->
					<fo:block-container height="0.2in" width="7.7in" top="0.01in" left=".4in" position="absolute">
						<fo:block>
							<fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
						</fo:block>
					</fo:block-container>
					<fo:block-container height="0.5in" width="7.7in" top="0.20in" left=".4in" position="absolute">
						<fo:table height="0.25in" width="7.7in">
							<fo:table-column column-width="3.85in"/>
							<fo:table-column column-width="3.85in"/>
							<fo:table-body font-family="Helvetica" font-weight="normal" font-size="8pt">
								<fo:table-row line-height="11pt">
									<fo:table-cell>
										<fo:block border-width="1.0pt" border-color="white" text-align="start" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">
<xsl:text>IA.ADDITIONAL_INFORMATION</xsl:text>										</fo:block>
									</fo:table-cell>
									<fo:table-cell>
										<fo:block border-width="1.0pt" border-color="white" text-align="end" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">IA.PAGE <fo:page-number/>
										</fo:block>
									</fo:table-cell>
								</fo:table-row>
							</fo:table-body>
						</fo:table>
					</fo:block-container>
				</fo:static-content>
				<fo:flow flow-name="xsl-region-body">
			    <!-- dynamic page content -->
					<!-- Fields 1 -->
					<fo:block-container width="6.0in" height="3.6in" top="1.82in" left="0.72in" position="absolute">
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.PARENT </xsl:text><xsl:value-of select="RENAMETERM/Term_Vendor"/><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/PARENTID"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.GL_GROUP</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/GLGROUP"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.PRICE_SCHEDULE</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/PRICESCHEDULE"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.DISCOUNT_PERCENTAGE_SIGN</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISCOUNT"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:value-of select="RENAMETERM/Term_Vendor"/><xsl:text> IA.PRICE_LIST</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/PRICELIST"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:value-of select="RENAMETERM/Term_Vendor"/><xsl:text> IA.TYPE_ID</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/VENDTYPE"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.ACCOUNT_LABEL</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/ACCOUNTLABEL"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.GL_ACCOUNT</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/APACCOUNT"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.TAXABLE</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/TAXABLE"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.CONTACT_TAX_GROUP</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYCONTACT/TAXGROUP"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.TAX_ID</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/TAXID"/>
                                                        </fo:block>
													<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
														<xsl:text>IA.COMPANY_NAME_AS_REGISTERED</xsl:text><xsl:text>: </xsl:text>
														<xsl:value-of select="REC/DISPLAYCONTACT/TAXCOMPANYNAME"/>
													</fo:block>
													<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
														<xsl:text>IA.DATE_TAX_NUMBER_LAST_VALIDATED</xsl:text><xsl:text>: </xsl:text>
														<xsl:value-of select="REC/DISPLAYCONTACT/TAXIDVALIDATIONDATE"/>
													</fo:block>
													<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
														<xsl:text>IA.GST_REGISTERED</xsl:text><xsl:text>: </xsl:text>
														<xsl:value-of select="REC/DISPLAYCONTACT/GSTREGISTERED"/>
													</fo:block>
													<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
														<xsl:text>IA.ADDRESS</xsl:text><xsl:text>: </xsl:text>
														<xsl:value-of select="REC/DISPLAYCONTACT/TAXADDRESS"/>
													</fo:block>
													<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
														<xsl:text>IA.TAX_SOLUTION</xsl:text><xsl:text>: </xsl:text>
														<xsl:value-of select="REC/DISPLAYCONTACT/TAXSOLUTIONID"/>
													</fo:block>
													<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
														<xsl:text>IA.DEFAULT_TAX_SCHEDULE</xsl:text><xsl:text>: </xsl:text>
														<xsl:value-of select="REC/DISPLAYCONTACT/TAXSCHEDULE"/>
													</fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.DISCOUNT_PERCENTAGE_SIGN</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/CREDITLIMIT"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.ON_HOLD</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/ONHOLD"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.DONOT_PAY</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DONOTCUTCHECK"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.COMMENTS</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/COMMENTS"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.1099_ELIGIBLE</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/ELIGIBLE1099"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.1099_NAME</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/NAME1099"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.FORM_1099</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/FORM1099"/>
                                                        </fo:block>
													<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.ATTACHMENTS</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/SUPDOCID"/>
                                                        </fo:block>
													 <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.DEFAULT_CURRENCY</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/CURRENCY"/>
                                                        </fo:block>
					</fo:block-container>
				<!-- Multiline 1 -->
				</fo:flow>
			</fo:page-sequence>
			<!--  page printing -->
			<fo:page-sequence master-reference="first">
				<fo:static-content flow-name="xsl-region-before">
					<!-- static page content -->
					<!-- Letterhead -->
					<!-- company logo -->
                    <xsl:if test="COMPANY/LOGO and COMPANY/LOGO != ''">
                        <fo:block-container height="0.6in" width="1.0in" top="0in" left="0.80in" position="absolute">
                            <fo:block>
                                <fo:external-graphic max-height="0.6in" max-width="1.0in" src="{COMPANY/LOGO}"/>
                            </fo:block>
                        </fo:block-container>
                    </xsl:if>
					<!-- company fields -->
					<!-- Title -->
					<!-- Title background -->
					<fo:block-container height="0.61in" width="2.6in" top="0in" left="4.60in" position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="2.6in" height="0.61in">
									<svg:rect x="0.15in" y="0in" width="2.3in" height="0.3in" style="fill: #BBBBBB"/>
									<svg:circle cx="0.15in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
									<svg:circle cx="2.45in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- Title Text -->
					<fo:block-container height="0.61in" width="2.3in" top=".08in" left="5.0in" position="absolute">
						<fo:block text-align="start" color="#ffffff" line-height="15pt" font-family="Times Roman" font-size="13pt" font-weight="bold">
<xsl:value-of select="RENAMETERM/Term_Vendor"/><xsl:text> IA.INFORMATION</xsl:text>						</fo:block>
					</fo:block-container>
					<!-- Up-right static -->
					<!-- Vertical Line under title -->
					<fo:block-container height="0.6in" width="0.001in" top="0.3in" left="5.95in" position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="0.001in" height="0.6in">
									<svg:rect x="0in" y="0in" width="0.0001in" height="0.6in" style="fill: #FFFFFF; stroke:black"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- Text at the right of the vertical line -->
					<fo:block-container height="0.6in" width="0.5in" top="0.60in" left="6.05in" position="absolute">
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="12pt">
							<xsl:value-of select="REC/VENDORID"/>
						</fo:block>
					</fo:block-container>
					<!-- Text at the left of the vertical line -->
					<fo:block-container height="0.6in" width="0.5in" top="0.6in" left="5.35in" position="absolute">
						<fo:block text-align="end" line-height="11pt" font-family="Helvetica" font-size="8pt">
<xsl:value-of select="RENAMETERM/Term_Vendor"/>						</fo:block>
						<fo:block text-align="center" line-height="11pt" font-family="Helvetica" font-size="7pt">
							<xsl:if test="REC/STATUS and (REC/STATUS != 'Active')">
								(<xsl:value-of select="REC/STATUS"/>)
							</xsl:if> 
						</fo:block>
					</fo:block-container>
				</fo:static-content>
				<fo:static-content flow-name="xsl-region-after">
					<!-- footer - page number -->
					<fo:block-container height="0.2in" width="7.7in" top="0.01in" left=".4in" position="absolute">
						<fo:block>
							<fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
						</fo:block>
					</fo:block-container>
					<fo:block-container height="0.5in" width="7.7in" top="0.20in" left=".4in" position="absolute">
						<fo:table height="0.25in" width="7.7in">
							<fo:table-column column-width="3.85in"/>
							<fo:table-column column-width="3.85in"/>
							<fo:table-body font-family="Helvetica" font-weight="normal" font-size="8pt">
								<fo:table-row line-height="11pt">
									<fo:table-cell>
										<fo:block border-width="1.0pt" border-color="white" text-align="start" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">
<xsl:text>Contact List</xsl:text>										</fo:block>
									</fo:table-cell>
									<fo:table-cell>
										<fo:block border-width="1.0pt" border-color="white" text-align="end" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">IA.PAGE <fo:page-number/>
										</fo:block>
									</fo:table-cell>
								</fo:table-row>
							</fo:table-body>
						</fo:table>
					</fo:block-container>
				</fo:static-content>
				<fo:flow flow-name="xsl-region-body">
			    <!-- dynamic page content -->
					<!-- Fields 1 -->
					<fo:block-container width="6.0in" height="2in" top="1.82in" left="0.72in" position="absolute">
					</fo:block-container>
				<!-- Multiline 1 -->
				<!-- column headers -->
					<!-- column header 2 -->
					<!-- column header round box 2 -->
					<fo:block-container
							height="0.2in" 
							width="2.049in"
							top="1.81in"
							left="0.46838407494145in"
							position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="2.049in" height="0.2in">
									<svg:circle cx="0.08in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
									<svg:rect x="0.08in" y="0in" width="1.889in" height="0.16in" style="fill: #BBBBBB"/>
									<svg:circle cx="1.969in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- column header label 2 -->
					<fo:block-container 
							height="0.2in"
							width="2.389in"
							top="1.84in"
							left="0.54838407494145in"
							position="absolute">
						<fo:block text-align="start" color="#afffff" line-height="11pt" font-family="Times Roman" font-size="8pt" font-weight="bold">
							IA.CATEGORY_EG_HOME_OFFICE_ETC
						</fo:block>
					</fo:block-container>
					<!-- column header horizontal line 2 -->
					<fo:block-container 
							height="0.2in" 
							width="1.0411639344262in" 
							top="1.77in" 
							left="2.5173840749415in" 
							position="absolute">
						<fo:block>
							<fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
						</fo:block>
					</fo:block-container>
					<!-- column header 3 -->
					<!-- column header round box 3 -->
					<fo:block-container
							height="0.2in" 
							width="0.549in"
							top="1.81in"
							left="3.5585480093677in"
							position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="0.549in" height="0.2in">
									<svg:circle cx="0.08in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
									<svg:rect x="0.08in" y="0in" width="0.389in" height="0.16in" style="fill: #BBBBBB"/>
									<svg:circle cx="0.469in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- column header label 3 -->
					<fo:block-container 
							height="0.2in"
							width="0.889in"
							top="1.84in"
							left="3.6385480093677in"
							position="absolute">
						<fo:block text-align="start" color="#afffff" line-height="11pt" font-family="Times Roman" font-size="8pt" font-weight="bold">
							Contact
						</fo:block>
					</fo:block-container>
					<!-- code to drive printing of rows -->
					<fo:block space-before.optimum="2.09in">
						<fo:table>
							<fo:table-column column-width="0.46838407494145in"/>
							<fo:table-column column-width="3.0901639344262in"/>
							<fo:table-column column-width="3.0901639344262in"/>
							<fo:table-body font-family="Helvetica" font-weight="normal" font-size="8pt">
								<xsl:apply-templates select="REC/CONTACT_LIST_INFO"/>
							</fo:table-body>
						</fo:table>
					</fo:block>
				</fo:flow>
			</fo:page-sequence>
			<!--  page printing -->
			<fo:page-sequence master-reference="first">
				<fo:static-content flow-name="xsl-region-before">
					<!-- static page content -->
					<!-- Letterhead -->
					<!-- company logo -->
                    <xsl:if test="COMPANY/LOGO and COMPANY/LOGO != ''">
                        <fo:block-container height="0.6in" width="1.0in" top="0in" left="0.80in" position="absolute">
                            <fo:block>
                                <fo:external-graphic max-height="0.6in" max-width="1.0in" src="{COMPANY/LOGO}"/>
                            </fo:block>
                        </fo:block-container>
                    </xsl:if>
					<!-- company fields -->
					<!-- Title -->
					<!-- Title background -->
					<fo:block-container height="0.61in" width="2.6in" top="0in" left="4.60in" position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="2.6in" height="0.61in">
									<svg:rect x="0.15in" y="0in" width="2.3in" height="0.3in" style="fill: #BBBBBB"/>
									<svg:circle cx="0.15in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
									<svg:circle cx="2.45in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- Title Text -->
					<fo:block-container height="0.61in" width="2.3in" top=".08in" left="5.0in" position="absolute">
						<fo:block text-align="start" color="#ffffff" line-height="15pt" font-family="Times Roman" font-size="13pt" font-weight="bold">
<xsl:value-of select="RENAMETERM/Term_Vendor"/><xsl:text> IA.INFORMATION</xsl:text>						</fo:block>
					</fo:block-container>
					<!-- Up-right static -->
					<!-- Vertical Line under title -->
					<fo:block-container height="0.6in" width="0.001in" top="0.3in" left="5.95in" position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="0.001in" height="0.6in">
									<svg:rect x="0in" y="0in" width="0.0001in" height="0.6in" style="fill: #FFFFFF; stroke:black"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- Text at the right of the vertical line -->
					<fo:block-container height="0.6in" width="0.5in" top="0.60in" left="6.05in" position="absolute">
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="12pt">
							<xsl:value-of select="REC/VENDORID"/>
						</fo:block>
					</fo:block-container>
					<!-- Text at the left of the vertical line -->
					<fo:block-container height="0.6in" width="0.5in" top="0.6in" left="5.35in" position="absolute">
						<fo:block text-align="end" line-height="11pt" font-family="Helvetica" font-size="8pt">
<xsl:value-of select="RENAMETERM/Term_Vendor"/>						</fo:block>
						<fo:block text-align="center" line-height="11pt" font-family="Helvetica" font-size="7pt">
							<xsl:if test="REC/STATUS and (REC/STATUS != 'Active')">
								(<xsl:value-of select="REC/STATUS"/>)
							</xsl:if> 
						</fo:block>
					</fo:block-container>
				</fo:static-content>
				<fo:static-content flow-name="xsl-region-after">
					<!-- footer - page number -->
					<fo:block-container height="0.2in" width="7.7in" top="0.01in" left=".4in" position="absolute">
						<fo:block>
							<fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
						</fo:block>
					</fo:block-container>
					<fo:block-container height="0.5in" width="7.7in" top="0.20in" left=".4in" position="absolute">
						<fo:table height="0.25in" width="7.7in">
							<fo:table-column column-width="3.85in"/>
							<fo:table-column column-width="3.85in"/>
							<fo:table-body font-family="Helvetica" font-weight="normal" font-size="8pt">
								<fo:table-row line-height="11pt">
									<fo:table-cell>
										<fo:block border-width="1.0pt" border-color="white" text-align="start" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">
<xsl:text>Payto/Returnto</xsl:text>										</fo:block>
									</fo:table-cell>
									<fo:table-cell>
										<fo:block border-width="1.0pt" border-color="white" text-align="end" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">IA.PAGE <fo:page-number/>
										</fo:block>
									</fo:table-cell>
								</fo:table-row>
							</fo:table-body>
						</fo:table>
					</fo:block-container>
				</fo:static-content>
				<fo:flow flow-name="xsl-region-body">
			    <!-- dynamic page content -->
					<!-- Fields 1 -->
					<fo:block-container width="6.0in" height="2in" top="1.82in" left="0.72in" position="absolute">
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.PRIMARY_CONTACT</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/CONTACTINFO/CONTACTNAME"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.PAY_TO_CONTACT</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/PAYTO/CONTACTNAME"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.RETURN_TO_CONTACT</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/RETURNTO/CONTACTNAME"/>
                                                        </fo:block>
					</fo:block-container>
				<!-- Multiline 1 -->
				</fo:flow>
			</fo:page-sequence>
			<!--  page printing -->
			<fo:page-sequence master-reference="first">
				<fo:static-content flow-name="xsl-region-before">
					<!-- static page content -->
					<!-- Letterhead -->
					<!-- company logo -->
                    <xsl:if test="COMPANY/LOGO and COMPANY/LOGO != ''">
                        <fo:block-container height="0.6in" width="1.0in" top="0in" left="0.80in" position="absolute">
                            <fo:block>
                                <fo:external-graphic max-height="0.6in" max-width="1.0in" src="{COMPANY/LOGO}"/>
                            </fo:block>
                        </fo:block-container>
                    </xsl:if>
					<!-- company fields -->
					<!-- Title -->
					<!-- Title background -->
					<fo:block-container height="0.61in" width="2.6in" top="0in" left="4.60in" position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="2.6in" height="0.61in">
									<svg:rect x="0.15in" y="0in" width="2.3in" height="0.3in" style="fill: #BBBBBB"/>
									<svg:circle cx="0.15in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
									<svg:circle cx="2.45in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- Title Text -->
					<fo:block-container height="0.61in" width="2.3in" top=".08in" left="5.0in" position="absolute">
						<fo:block text-align="start" color="#ffffff" line-height="15pt" font-family="Times Roman" font-size="13pt" font-weight="bold">
<xsl:value-of select="RENAMETERM/Term_Vendor"/><xsl:text> IA.INFORMATION</xsl:text>						</fo:block>
					</fo:block-container>
					<!-- Up-right static -->
					<!-- Vertical Line under title -->
					<fo:block-container height="0.6in" width="0.001in" top="0.3in" left="5.95in" position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="0.001in" height="0.6in">
									<svg:rect x="0in" y="0in" width="0.0001in" height="0.6in" style="fill: #FFFFFF; stroke:black"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- Text at the right of the vertical line -->
					<fo:block-container height="0.6in" width="0.5in" top="0.60in" left="6.05in" position="absolute">
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="12pt">
							<xsl:value-of select="REC/VENDORID"/>
						</fo:block>
					</fo:block-container>
					<!-- Text at the left of the vertical line -->
					<fo:block-container height="0.6in" width="0.5in" top="0.6in" left="5.35in" position="absolute">
						<fo:block text-align="end" line-height="11pt" font-family="Helvetica" font-size="8pt">
<xsl:value-of select="RENAMETERM/Term_Vendor"/>						</fo:block>
						<fo:block text-align="center" line-height="11pt" font-family="Helvetica" font-size="7pt">
							<xsl:if test="REC/STATUS and (REC/STATUS != 'Active')">
								(<xsl:value-of select="REC/STATUS"/>)
							</xsl:if> 
						</fo:block>
					</fo:block-container>
				</fo:static-content>
				<fo:static-content flow-name="xsl-region-after">
					<!-- footer - page number -->
					<fo:block-container height="0.2in" width="7.7in" top="0.01in" left=".4in" position="absolute">
						<fo:block>
							<fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
						</fo:block>
					</fo:block-container>
					<fo:block-container height="0.5in" width="7.7in" top="0.20in" left=".4in" position="absolute">
						<fo:table height="0.25in" width="7.7in">
							<fo:table-column column-width="3.85in"/>
							<fo:table-column column-width="3.85in"/>
							<fo:table-body font-family="Helvetica" font-weight="normal" font-size="8pt">
								<fo:table-row line-height="11pt">
									<fo:table-cell>
										<fo:block border-width="1.0pt" border-color="white" text-align="start" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">
<xsl:text>IA.PAYMENT_INFORMATION</xsl:text>										</fo:block>
									</fo:table-cell>
									<fo:table-cell>
										<fo:block border-width="1.0pt" border-color="white" text-align="end" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">IA.PAGE <fo:page-number/>
										</fo:block>
									</fo:table-cell>
								</fo:table-row>
							</fo:table-body>
						</fo:table>
					</fo:block-container>
				</fo:static-content>
				<fo:flow flow-name="xsl-region-body">
			    <!-- dynamic page content -->
					<!-- Fields 1 -->
					<fo:block-container width="6.0in" height="3.6in" top="1.82in" left="0.72in" position="absolute">
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.PREFERRED_PAYMENT_METHOD</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/PAYMETHODKEY"/>
                                                        </fo:block>
						    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.MERGE_PAYMENT_REQUESTS</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/MERGEPAYMENTREQ"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.SEND_AUTOMATIC_PAYMENT_NOTIFICATION</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/PAYMENTNOTIFY"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:value-of select="RENAMETERM/Term_Vendor"/><xsl:text> IA.ACCOUNT_NO</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/VENDORACCOUNTNO"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:value-of select="RENAMETERM/Term_Vendor"/><xsl:text> IA.BILLING_TYPE</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/BILLINGTYPE"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.PAYMENT_PRIORITY</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/PAYMENTPRIORITY"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.TERM</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/TERMNAME"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text></xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DUEDATEDIFF"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text></xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/INVOICEDATEDIFF"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text></xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/PLUSMINUS"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.DEFAULT_BILL_PAYMENT_DATE</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/PAYDATEVALUE"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.ENABLE_ACH</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/ACHENABLED"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.ACH_BANK_ROUTING_NUMBER</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/ACHBANKROUTINGNUMBER"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.ACCOUNT_NUMBER</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/ACHACCOUNTNUMBER"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.ACCOUNT_TYPE</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/ACHACCOUNTTYPE"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.ACCOUNT_CLASSIFICATION</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/ACHREMITTANCETYPE"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.DISPLAY_TERM_DISCOUNT_ON_CHECK_STUB</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLAYTERMDISCOUNT"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.VENDOR_SCHECK_STUB</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/DISPLOCACCTNOCHECK"/>
                                                        </fo:block>
					</fo:block-container>
				<!-- Multiline 1 -->
				<!-- column headers -->
					<!-- column header 2 -->
					<!-- column header round box 2 -->
					<fo:block-container
							height="0.2in" 
							width="0.604in"
							top="5.1155555555556in"
							left="1.3333333333333in"
							position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="0.604in" height="0.2in">
									<svg:circle cx="0.08in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
									<svg:rect x="0.08in" y="0in" width="0.444in" height="0.16in" style="fill: #BBBBBB"/>
									<svg:circle cx="0.524in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- column header label 2 -->
					<fo:block-container 
							height="0.2in"
							width="0.944in"
							top="5.1455555555556in"
							left="1.4133333333333in"
							position="absolute">
						<fo:block text-align="start" color="#afffff" line-height="11pt" font-family="Times Roman" font-size="8pt" font-weight="bold">
							IA.LOCATION
						</fo:block>
					</fo:block-container>
					<!-- column header horizontal line 2 -->
					<fo:block-container 
							height="0.2in" 
							width="1.7093333333333in" 
							top="5.0755555555556in" 
							left="1.9373333333333in" 
							position="absolute">
						<fo:block>
							<fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
						</fo:block>
					</fo:block-container>
					<!-- column header 3 -->
					<!-- column header round box 3 -->
					<fo:block-container
							height="0.2in" 
							width="0.771in"
							top="5.1155555555556in"
							left="3.6466666666667in"
							position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="0.771in" height="0.2in">
									<svg:circle cx="0.08in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
									<svg:rect x="0.08in" y="0in" width="0.611in" height="0.16in" style="fill: #BBBBBB"/>
									<svg:circle cx="0.691in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- column header label 3 -->
					<fo:block-container 
							height="0.2in"
							width="1.111in"
							top="5.1455555555556in"
							left="3.7266666666667in"
							position="absolute">
						<fo:block text-align="start" color="#afffff" line-height="11pt" font-family="Times Roman" font-size="8pt" font-weight="bold">
							IA.ACCOUNT_NO
						</fo:block>
					</fo:block-container>
					<!-- code to drive printing of rows -->
					<fo:block space-before.optimum="5.3955555555556in">
						<fo:table>
							<fo:table-column column-width="1.3333333333333in"/>
							<fo:table-column column-width="2.3133333333333in"/>
							<fo:table-column column-width="2.87in"/>
							<fo:table-body font-family="Helvetica" font-weight="normal" font-size="8pt">
								<xsl:apply-templates select="REC/VENDOR_ACCTNO_LOC_HEAD"/>
							</fo:table-body>
						</fo:table>
					</fo:block>
				</fo:flow>
			</fo:page-sequence>
			<!--  page printing -->
			<fo:page-sequence master-reference="first">
				<fo:static-content flow-name="xsl-region-before">
					<!-- static page content -->
					<!-- Letterhead  -->
					<!-- company logo -->
                    <xsl:if test="COMPANY/LOGO and COMPANY/LOGO != ''">
                        <fo:block-container height="0.6in" width="1.0in" top="0in" left="0.80in" position="absolute">
                            <fo:block>
                                <fo:external-graphic max-height="0.6in" max-width="1.0in" src="{COMPANY/LOGO}"/>
                            </fo:block>
                        </fo:block-container>
                    </xsl:if>
					<!-- company fields -->
					<!-- Title -->
					<!-- Title background -->
					<fo:block-container height="0.61in" width="2.6in" top="0in" left="4.60in" position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="2.6in" height="0.61in">
									<svg:rect x="0.15in" y="0in" width="2.3in" height="0.3in" style="fill: #BBBBBB"/>
									<svg:circle cx="0.15in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
									<svg:circle cx="2.45in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- Title Text -->
					<fo:block-container height="0.61in" width="2.3in" top=".08in" left="5.0in" position="absolute">
						<fo:block text-align="start" color="#ffffff" line-height="15pt" font-family="Times Roman" font-size="13pt" font-weight="bold">
<xsl:value-of select="RENAMETERM/Term_Vendor"/><xsl:text> IA.INFORMATION</xsl:text>						</fo:block>
					</fo:block-container>
					<!-- Up-right static -->
					<!-- Vertical Line under title -->
					<fo:block-container height="0.6in" width="0.001in" top="0.3in" left="5.95in" position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="0.001in" height="0.6in">
									<svg:rect x="0in" y="0in" width="0.0001in" height="0.6in" style="fill: #FFFFFF; stroke:black"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- Text at the right of the vertical line  -->
					<fo:block-container height="0.6in" width="0.5in" top="0.60in" left="6.05in" position="absolute">
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="12pt">
							<xsl:value-of select="REC/VENDORID"/>
						</fo:block>
					</fo:block-container>
					<!-- Text at the left of the vertical line -->
					<fo:block-container height="0.6in" width="0.5in" top="0.6in" left="5.35in" position="absolute">
						<fo:block text-align="end" line-height="11pt" font-family="Helvetica" font-size="8pt">
<xsl:value-of select="RENAMETERM/Term_Vendor"/>						</fo:block>
						<fo:block text-align="center" line-height="11pt" font-family="Helvetica" font-size="7pt">
							<xsl:if test="REC/STATUS and (REC/STATUS != 'Active')">
								(<xsl:value-of select="REC/STATUS"/>)
							</xsl:if> 
						</fo:block>
					</fo:block-container>
				</fo:static-content>
				<fo:static-content flow-name="xsl-region-after">
					<!-- footer - page number -->
					<fo:block-container height="0.2in" width="7.7in" top="0.01in" left=".4in" position="absolute">
						<fo:block>
							<fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
						</fo:block>
					</fo:block-container>
					<fo:block-container height="0.5in" width="7.7in" top="0.20in" left=".4in" position="absolute">
						<fo:table height="0.25in" width="7.7in">
							<fo:table-column column-width="3.85in"/>
							<fo:table-column column-width="3.85in"/>
							<fo:table-body font-family="Helvetica" font-weight="normal" font-size="8pt">
								<fo:table-row line-height="11pt">
									<fo:table-cell>
										<fo:block border-width="1.0pt" border-color="white" text-align="start" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">
<xsl:text>Payment Manager</xsl:text>										</fo:block>
									</fo:table-cell>
									<fo:table-cell>
										<fo:block border-width="1.0pt" border-color="white" text-align="end" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">IA.PAGE <fo:page-number/>
										</fo:block>
									</fo:table-cell>
								</fo:table-row>
							</fo:table-body>
						</fo:table>
					</fo:block-container>
				</fo:static-content>
				<fo:flow flow-name="xsl-region-body">
			    <!-- dynamic page content -->
					<!-- Fields 1 -->
					<fo:block-container width="6.0in" height="2in" top="1.82in" left="0.72in" position="absolute">
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.ENABLE_CHECK_OUTSOURCING</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/WIREENABLED"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>Enable Check Outsourcing</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/CHECKENABLED"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.NAME_OF_BANK</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/WIREBANKNAME"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.BANK_ROUTING_NUMBER</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/WIREBANKROUTINGNUMBER"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.ACCOUNT_NUMBER</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/WIREACCOUNTNUMBER"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.ACCOUNT_TYPE</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/WIREACCOUNTTYPE"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.REMITTANCE_DELIVERY_TYPE</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/PMPLUSREMITTANCETYPE"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.REMITTANCE_EMAIL_ADDRESS</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/PMPLUSEMAIL"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.REMITTANCE_FAX_NUMBER</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/PMPLUSFAX"/>
                                                        </fo:block>
					</fo:block-container>
				<!-- Multiline 1 -->
				</fo:flow>
			</fo:page-sequence>
			<xsl:if test="REC/OWNER/ACCOUNTLABEL != ''">
			<!--  page printing -->
			<fo:page-sequence master-reference="first">
				<fo:static-content flow-name="xsl-region-before">
					<!-- static page content -->
					<!-- Letterhead -->
					<!-- company logo -->
                    <xsl:if test="COMPANY/LOGO and COMPANY/LOGO != ''">
                        <fo:block-container height="0.6in" width="1.0in" top="0in" left="0.80in" position="absolute">
                            <fo:block>
                                <fo:external-graphic max-height="0.6in" max-width="1.0in" src="{COMPANY/LOGO}"/>
                            </fo:block>
                        </fo:block-container>
                    </xsl:if>
					<!-- company fields -->
					<!-- Title -->
					<!-- Title background -->
					<fo:block-container height="0.61in" width="2.6in" top="0in" left="4.60in" position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="2.6in" height="0.61in">
									<svg:rect x="0.15in" y="0in" width="2.3in" height="0.3in" style="fill: #BBBBBB"/>
									<svg:circle cx="0.15in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
									<svg:circle cx="2.45in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- Title Text -->
					<fo:block-container height="0.61in" width="2.3in" top=".08in" left="5.0in" position="absolute">
						<fo:block text-align="start" color="#ffffff" line-height="15pt" font-family="Times Roman" font-size="13pt" font-weight="bold">
						<xsl:value-of select="RENAMETERM/Term_Vendor"/><xsl:text> IA.INFORMATION</xsl:text>						</fo:block>
					</fo:block-container>
					<!-- Up-right static -->
					<!-- Vertical Line under title -->
					<fo:block-container height="0.6in" width="0.001in" top="0.3in" left="5.95in" position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="0.001in" height="0.6in">
									<svg:rect x="0in" y="0in" width="0.0001in" height="0.6in" style="fill: #FFFFFF; stroke:black"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- Text at the right of the vertical line -->
					<fo:block-container height="0.6in" width="0.5in" top="0.60in" left="6.05in" position="absolute">
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="12pt">
							<xsl:value-of select="REC/VENDORID"/>
						</fo:block>
					</fo:block-container>
					<!-- Text at the left of the vertical line -->
					<fo:block-container height="0.6in" width="0.5in" top="0.6in" left="5.35in" position="absolute">
						<fo:block text-align="end" line-height="11pt" font-family="Helvetica" font-size="8pt">
						<xsl:value-of select="RENAMETERM/Term_Vendor"/>						</fo:block>
						<fo:block text-align="center" line-height="11pt" font-family="Helvetica" font-size="7pt">
							<xsl:if test="REC/STATUS and (REC/STATUS != 'Active')">
								(<xsl:value-of select="REC/STATUS"/>)
							</xsl:if> 
						</fo:block>
					</fo:block-container>
				</fo:static-content>
				<fo:static-content flow-name="xsl-region-after">
					<!-- footer - page number -->
					<fo:block-container height="0.2in" width="7.7in" top="0.01in" left=".4in" position="absolute">
						<fo:block>
							<fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
						</fo:block>
					</fo:block-container>
					<fo:block-container height="0.5in" width="7.7in" top="0.20in" left=".4in" position="absolute">
						<fo:table height="0.25in" width="7.7in">
							<fo:table-column column-width="3.85in"/>
							<fo:table-column column-width="3.85in"/>
							<fo:table-body font-family="Helvetica" font-weight="normal" font-size="8pt">
								<fo:table-row line-height="11pt">
									<fo:table-cell>
										<fo:block border-width="1.0pt" border-color="white" text-align="start" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">
										<xsl:text>IA.OWNER_INFORMATION</xsl:text>										</fo:block>
									</fo:table-cell>
									<fo:table-cell>
										<fo:block border-width="1.0pt" border-color="white" text-align="end" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">IA.PAGE <fo:page-number/>
										</fo:block>
									</fo:table-cell>
								</fo:table-row>
							</fo:table-body>
						</fo:table>
					</fo:block-container>
				</fo:static-content>
				<fo:flow flow-name="xsl-region-body">
			    <!-- dynamic page content -->
					<!-- Fields 1 -->
					<fo:block-container width="6.0in" height="2in" top="1.82in" left="0.72in" position="absolute">
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.DEFAULT_EQUITY_ACCOUNT</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/OWNER/EQGLACCOUNT"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.DEFAULT_EQUITY_ACCOUNT_LABEL</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/OWNER/EQGLACCOUNTLABEL"/>
                                                        </fo:block>
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.HOLD_DISTRIBUTION</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/OWNER/HOLDDIST"/>
                                                        </fo:block>
					</fo:block-container>
				<!-- Multiline 1 -->
				</fo:flow>
			</fo:page-sequence>
			</xsl:if>
			<xsl:if test="REC/INSURANCEREQUIRED != ''">
			<!--  page printing -->
			<fo:page-sequence master-reference="first">
				<fo:static-content flow-name="xsl-region-before">
					<!-- static page content -->
					<!-- Letterhead -->
					<!-- company logo -->
                    <xsl:if test="COMPANY/LOGO and COMPANY/LOGO != ''">
                        <fo:block-container height="0.6in" width="1.0in" top="0in" left="0.80in" position="absolute">
                            <fo:block>
                                <fo:external-graphic max-height="0.6in" max-width="1.0in" src="{COMPANY/LOGO}"/>
                            </fo:block>
                        </fo:block-container>
                    </xsl:if>
					<!-- company fields -->
					<!-- Title -->
					<!-- Title background -->
					<fo:block-container height="0.61in" width="2.6in" top="0in" left="4.60in" position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="2.6in" height="0.61in">
									<svg:rect x="0.15in" y="0in" width="2.3in" height="0.3in" style="fill: #BBBBBB"/>
									<svg:circle cx="0.15in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
									<svg:circle cx="2.45in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- Title Text -->
					<fo:block-container height="0.61in" width="2.3in" top=".08in" left="5.0in" position="absolute">
						<fo:block text-align="start" color="#ffffff" line-height="15pt" font-family="Times Roman" font-size="13pt" font-weight="bold">
<xsl:value-of select="RENAMETERM/Term_Vendor"/><xsl:text> IA.INFORMATION</xsl:text>						</fo:block>
					</fo:block-container>
					<!-- Up-right static -->
					<!-- Vertical Line under title -->
					<fo:block-container height="0.6in" width="0.001in" top="0.3in" left="5.95in" position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="0.001in" height="0.6in">
									<svg:rect x="0in" y="0in" width="0.0001in" height="0.6in" style="fill: #FFFFFF; stroke:black"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- Text at the right of the vertical line -->
					<fo:block-container height="0.6in" width="0.5in" top="0.60in" left="6.05in" position="absolute">
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="12pt">
							<xsl:value-of select="REC/VENDORID"/>
						</fo:block>
					</fo:block-container>
					<!-- Text at the left of the vertical line -->
					<fo:block-container height="0.6in" width="0.5in" top="0.6in" left="5.35in" position="absolute">
						<fo:block text-align="end" line-height="11pt" font-family="Helvetica" font-size="8pt">
<xsl:value-of select="RENAMETERM/Term_Vendor"/>						</fo:block>
						<fo:block text-align="center" line-height="11pt" font-family="Helvetica" font-size="7pt">
							<xsl:if test="REC/STATUS and (REC/STATUS != 'Active')">
								(<xsl:value-of select="REC/STATUS"/>)
							</xsl:if> 
						</fo:block>
					</fo:block-container>
				</fo:static-content>
				<fo:static-content flow-name="xsl-region-after">
					<!-- footer - page number -->
					<fo:block-container height="0.2in" width="7.7in" top="0.01in" left=".4in" position="absolute">
						<fo:block>
							<fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
						</fo:block>
					</fo:block-container>
					<fo:block-container height="0.5in" width="7.7in" top="0.20in" left=".4in" position="absolute">
						<fo:table height="0.25in" width="7.7in">
							<fo:table-column column-width="3.85in"/>
							<fo:table-column column-width="3.85in"/>
							<fo:table-body font-family="Helvetica" font-weight="normal" font-size="8pt">
								<fo:table-row line-height="11pt">
									<fo:table-cell>
										<fo:block border-width="1.0pt" border-color="white" text-align="start" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">
<xsl:text>Insurance/License</xsl:text>										</fo:block>
									</fo:table-cell>
									<fo:table-cell>
										<fo:block border-width="1.0pt" border-color="white" text-align="end" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">IA.PAGE <fo:page-number/>
										</fo:block>
									</fo:table-cell>
								</fo:table-row>
							</fo:table-body>
						</fo:table>
					</fo:block-container>
				</fo:static-content>
				<fo:flow flow-name="xsl-region-body">
			    <!-- dynamic page content -->
					<!-- Fields 1 -->
					<fo:block-container width="6.0in" height="2in" top="1.82in" left="0.72in" position="absolute">
                                                    <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                                                                <xsl:text>IA.INSURANCE_REQUIRED</xsl:text><xsl:text>: </xsl:text>
                                                            <xsl:value-of select="REC/INSURANCEREQUIRED"/>
                                                        </fo:block>
					</fo:block-container>
				<!-- Multiline 1 -->
				</fo:flow>
			</fo:page-sequence>
			</xsl:if>
			<!--  page printing -->
			<xsl:if test="REC/OBJECTRESTRICTION != ''">
			<fo:page-sequence master-reference="first">
				<fo:static-content flow-name="xsl-region-before">
					<!-- static page content -->
					<!-- Letterhead -->
					<!-- company logo -->
                    <xsl:if test="COMPANY/LOGO and COMPANY/LOGO != ''">
                        <fo:block-container height="0.6in" width="1.0in" top="0in" left="0.80in" position="absolute">
                            <fo:block>
                                <fo:external-graphic max-height="0.6in" max-width="1.0in" src="{COMPANY/LOGO}"/>
                            </fo:block>
                        </fo:block-container>
                    </xsl:if>
					<!-- company fields -->
					<!-- Title -->
					<!-- Title background -->
					<fo:block-container height="0.61in" width="2.6in" top="0in" left="4.60in" position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="2.6in" height="0.61in">
									<svg:rect x="0.15in" y="0in" width="2.3in" height="0.3in" style="fill: #BBBBBB"/>
									<svg:circle cx="0.15in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
									<svg:circle cx="2.45in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- Title Text -->
					<fo:block-container height="0.61in" width="2.3in" top=".08in" left="5.0in" position="absolute">
						<fo:block text-align="start" color="#ffffff" line-height="15pt" font-family="Times Roman" font-size="13pt" font-weight="bold">
						<xsl:value-of select="RENAMETERM/Term_Vendor"/><xsl:text> IA.INFORMATION</xsl:text>						</fo:block>
					</fo:block-container>
					<!-- Up-right static -->
					<!-- Vertical Line under title -->
					<fo:block-container height="0.6in" width="0.001in" top="0.3in" left="5.95in" position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="0.001in" height="0.6in">
									<svg:rect x="0in" y="0in" width="0.0001in" height="0.6in" style="fill: #FFFFFF; stroke:black"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- Text at the right of the vertical line -->
					<fo:block-container height="0.6in" width="0.5in" top="0.60in" left="6.05in" position="absolute">
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="12pt">
							<xsl:value-of select="REC/VENDORID"/>
						</fo:block>
					</fo:block-container>
					<!-- Text at the left of the vertical line -->
					<fo:block-container height="0.6in" width="0.5in" top="0.6in" left="5.35in" position="absolute">
						<fo:block text-align="end" line-height="11pt" font-family="Helvetica" font-size="8pt">
							<xsl:value-of select="RENAMETERM/Term_Vendor"/>						</fo:block>
						<fo:block text-align="center" line-height="11pt" font-family="Helvetica" font-size="7pt">
							<xsl:if test="REC/STATUS and (REC/STATUS != 'Active')">
								(<xsl:value-of select="REC/STATUS"/>)
							</xsl:if> 
						</fo:block>
					</fo:block-container>
				</fo:static-content>
				<fo:static-content flow-name="xsl-region-after">
					<!-- footer - page number -->
					<fo:block-container height="0.2in" width="7.7in" top="0.01in" left=".4in" position="absolute">
						<fo:block>
							<fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
						</fo:block>
					</fo:block-container>
					
					<fo:block-container height="0.5in" width="7.7in" top="0.20in" left=".4in" position="absolute">
						<fo:table height="0.25in" width="7.7in">
							<fo:table-column column-width="3.85in"/>
							<fo:table-column column-width="3.85in"/>
							<fo:table-body font-family="Helvetica" font-weight="normal" font-size="8pt">
								<fo:table-row line-height="11pt">
									<fo:table-cell>
										<fo:block border-width="1.0pt" border-color="white" text-align="start" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">
											<xsl:text>IA.RESTRICTIONS</xsl:text>										</fo:block>
									</fo:table-cell>
									<fo:table-cell>
										<fo:block border-width="1.0pt" border-color="white" text-align="end" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">IA.PAGE_UPPER_CASE <fo:page-number/>
										</fo:block>
									</fo:table-cell>
								</fo:table-row>
							</fo:table-body>
						</fo:table>
					</fo:block-container>

				</fo:static-content>
				<fo:flow flow-name="xsl-region-body">
			    <!-- dynamic page content -->
					<!-- Fields 1 -->
					<fo:block-container width="6.0in" height="2in" top="1.82in" left="0.72in" position="absolute">
							<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
										<xsl:text>IA.VISIBILITY </xsl:text><xsl:text>: </xsl:text>
									<xsl:value-of select="REC/OBJECTRESTRICTION"/>
								</fo:block>
							<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
										<xsl:text>IA.LOACATIONS_LOCATION_GROUPS_DESC</xsl:text><xsl:text>: </xsl:text>
									<xsl:value-of select="REC/RESTRICTEDLOCATIONS"/>
								</fo:block>
							<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
										<xsl:text>IA.DEPARTMENTS_DEPARTMENT_GROUPS_DESC</xsl:text><xsl:text>: </xsl:text>
									<xsl:value-of select="REC/RESTRICTEDDEPARTMENTS"/>
								</fo:block>
					</fo:block-container>
				<!-- Multiline 1 -->
				</fo:flow>
			</fo:page-sequence>
		</xsl:if>
		</fo:root>
	</xsl:template>
	<!-- code for printing one row -->
	<xsl:template match="REC/CONTACT_LIST_INFO">
		<fo:table-row line-height="10pt">
			<fo:table-cell padding="2pt">
				<fo:block text-align="end">
					<xsl:value-of select="number(position())"/>.
				</fo:block>
			</fo:table-cell>
			<fo:table-cell padding="2pt">
				<fo:block text-align="start">
					<xsl:value-of select="CATEGORYNAME"/>
				</fo:block>
			</fo:table-cell>
			<fo:table-cell padding="2pt">
				<fo:block text-align="start">
					<xsl:value-of select="CONTACT.NAME"/>
				</fo:block>
			</fo:table-cell>
		</fo:table-row>
	</xsl:template>
	<!-- code for printing one row -->
	<xsl:template match="REC/VENDOR_ACCTNO_LOC_HEAD">
		<fo:table-row line-height="10pt">
			<fo:table-cell padding="2pt">
				<fo:block text-align="end">
					<xsl:value-of select="number(position())"/>.
				</fo:block>
			</fo:table-cell>
			<fo:table-cell padding="2pt">
				<fo:block text-align="start">
					<xsl:value-of select="LOCATION"/>
				</fo:block>
			</fo:table-cell>
			<fo:table-cell padding="2pt">
				<fo:block text-align="start">
					<xsl:value-of select="ACCOUNTNO"/>
				</fo:block>
			</fo:table-cell>
		</fo:table-row>
	</xsl:template>
</xsl:stylesheet>