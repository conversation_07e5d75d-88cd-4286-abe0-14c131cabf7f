<?php
/**
 * Digital Network Sync Dispatcher Class
 *
 * <AUTHOR> <pavan.sank<PERSON><PERSON><PERSON>@sage.com>
 * @copyright 2024 Intacct Corporation, All Rights Reserved
 */


class CashFlowDigitalNetworkSyncDispatcher extends DigitalNetworkSyncDispatcher
{

    const CONFIG_SECTION_NAME = 'CASH_FLOW_PIPELINE_DISPATCHER';
    /**
     * @inheritDoc
     */
    public function getLogPrefix(): string
    {
        return 'CashFlow DigitalNetworkSync Processor';
    }

    /**
     * @inheritDoc
     */
    public function getType(): string
    {
        return FIFODispatcher::DISPATCHER_TYPES['cashflowdigitalnetworksyncqueue'];
    }

    /**
     * @inheritDoc
     */
    public function getControlLockPrefix(): string
    {
        return 'CashFlowDigitalNetworkSync';
    }

    /**
     * @return string
     */
    public static function getCashFlowDataPipelineBucket()
    {
        $dispatcherConfig = GetValueForIACFGProperty(self::CONFIG_SECTION_NAME);
        return $dispatcherConfig['CASH_FLOW_S3_BUCKET'];
    }
}