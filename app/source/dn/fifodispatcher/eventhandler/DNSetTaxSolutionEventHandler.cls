<?php

/**
 * <PERSON> Handler for Set Tax Solution
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Intacct Corporation, All Rights Reserved
 */

class DNSetTaxSolutionEventHandler extends DNDimCommonEventHandler
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @return string
     */
    protected function getEventSubjectStr() : string
    {
        return '_TAXSOLUTION_';
    }

    /**
     * @return string
     */
    protected function getDimType() : string
    {
        return DNEventHandlerConstaint::TAXSOLUTION_TYPE;
    }

    /**
     * @return string
     */
    protected function getObjectSyncEventName() : string
    {
        return DNEventHandlerConstaint::TAXSOLUTIONUPDATE_EVENT;
    }

    /**
     * @return string
     */
    protected function getObjectIDField() : string
    {
        return 'solutionid';
    }
}