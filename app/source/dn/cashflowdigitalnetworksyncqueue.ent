<?php
//=============================================================================
//
//	FILE:			 cashflowdigitalnetworksyncqueue.ent
//	<AUTHOR> <<EMAIL>>
//	DESCRIPTION:	 cashflowdigitalnetworksyncqueue entity file
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information herein
//	may not be used, copied or disclosed in whole or part without prior
//	written consent from Intacct Corporation.
//
//=============================================================================
global $kSchemas;

require 'fifodispatcherqueue.ent';
$kSchemas['cashflowdigitalnetworksyncqueue'] = $kSchemas['fifodispatcherqueue'];
$kSchemas['cashflowdigitalnetworksyncqueue']['ownedobjects'] =  [
    [
        'fkey' => CashFlowDigitalNetworkSyncQueueHistoryManager::DISPATCHERQUEUEKEY,
        'invfkey' => 'RECORDNO',
        'entity' => 'cashflowdigitalnetworksyncqueuehistory',
        'skipOwnedObjectWithNoValues' => true, // Skip owned object if it has no values
        'path' => 'CASHFLOWDIGITALNETWORKSYC'
    ]
];
$kSchemas['digitalnetworksyncqueue']['upsertEntriesPaths'] = ['CASHFLOWDIGITALNETWORKSYC'];