<?php

/**
 * Class ContractComplianceCategoryEditor
 * Editor class for the Contract Compliance note object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
class ContractComplianceNoteEditor extends FormEditor
{
    
    /**
     * @param array $_params default editor parameters
     */
    public function __construct($_params = array())
    {
        parent::__construct($_params);
    }

    
     /**
     * getJavaScriptFileNames
     * 
     * @return array $jsfiles
     */
    protected function getJavaScriptFileNames()
    {
        $jsfiles[] = "../resources/js/contract.js";
        return $jsfiles;
    }

    /**
     * @param array $obj
     *
     * @return bool
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        parent::mediateDataAndMetadata($obj);

        $contractid = Request::$r->_contractid;
        if ($contractid != '') {
            $obj['CONTRACTID'] = $contractid;
        }

        $contractkey = Request::$r->_contractkey;
        if ($contractid != '') {
            $obj['CONTRACTKEY'] = $contractkey;
        }

        if ('shownew' === $this->state) {
            $this->hideParentObjectInfo();
        }

        return true;
    }

    private function hideParentObjectInfo()
    {
        $view = $this->getView();
        $fields = ['COMPLETION_DATE', 'COMPLETEDBY'];
        foreach ($fields as $field) {
            $view->findAndSetProperty(
                array('path' => $field),
                array('hidden' => true),
                EditorComponentFactory::TYPE_FIELD
            );
        }
    }

}
