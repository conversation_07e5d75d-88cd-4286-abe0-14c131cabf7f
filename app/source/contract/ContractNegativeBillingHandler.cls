<?php
/**
 * File ContractNegativeBillingHandler.cls contains the class ContractNegativeBillingHandler
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2015 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * Contract Percentage Billing form handler
 *
 * Class ContractBundleEditor
 */
class ContractNegativeBillingHandler
{
    /* @var ContractDetailInfo $parentDetail */
    protected $parentDetail;
    /* @var bool $rebuiltResolvesForBundle */
    private $rebuiltResolvesForBundle;

    public function __construct()
    {
        $this->rebuiltResolvesForBundle = false;
    }

    /**
     * @param int                               $contractDetailKey
     * @param ContractNegativeBillingInfo|null  $negBillingInfo
     *
     * @return bool
     */
    public static function queryNegativeBillingInfos ($contractDetailKey, &$negBillingInfo)
    {
        $ok = true;
        $negBillingInfo = null;

        $entryMgr = Globals::$g->gManagerFactory->getManager('contractnegativebillingentry');
        $entries = $entryMgr->GetList([
            'selects' => [ContractNegativeBillingManager::PARENTCNDETKEY],
            'filters' => [[[ContractNegativeBillingManager::CHILDCNDETKEY, '=', $contractDetailKey]]],
        ]);
        if (isNonEmptyArray($entries)) {
            $negBillingInfo = new ContractNegativeBillingInfo(ContractNegativeBillingInfo::FROM_POSITIVE_LINE, $contractDetailKey);
            foreach ($entries as $entry) {
                $negDetailKey = $entry[ContractNegativeBillingManager::PARENTCNDETKEY];
                $ok = $ok && self::queryNegativeBillingInfosInternal($negDetailKey, $negBillingInfo);
            }
        } else {
            $negBillingInfo = new ContractNegativeBillingInfo(ContractNegativeBillingInfo::FROM_NEGATIVE_LINE, $contractDetailKey);
            $ok = $ok && self::queryNegativeBillingInfosInternal($contractDetailKey, $negBillingInfo);
            if ($negBillingInfo->count() == 0) {
                $negBillingInfo = null;
            }
        }

        return $ok;
    }
    
    /**
     * @param ContractNegativeBillingInfo|null $negBillingInfo
     *
     * @return bool
     */
    public static function clearPositiveBillingEntryKeys ($negBillingInfo)
    {
        $ok = true;
        if ($negBillingInfo !== null && $negBillingInfo->isFromPositiveLine()) {
            $posContractDetailKey = $negBillingInfo->getFromDetailKey();

            $negBilingObjs = $negBillingInfo->getNegativeBillingObjs();
            $negContractDetailKeys = INTACCTarray_project($negBilingObjs, ContractNegativeBillingManager::PARENTCNDETKEY);

            $mgr = Globals::$g->gManagerFactory->getManager('contractbillingscheduleentry');
            $schEntries = $mgr->GetList([
                'selects' => ['RECORDNO'],
                'filters' => [[
                    ['CONTRACTDETAILKEY', 'in', $negContractDetailKeys],
                    ['LINKEDCONTRACTDETAILKEY', '=', $posContractDetailKey],
                ]]
            ]);

            $schEntriesRecNos = INTACCTarray_project($schEntries, 'RECORDNO');
            $stmt = ['update contractscheduleentry set billschentrykey=null where cny#=:1 and ', GetMyCompany()];
            $stmt = PrepINClauseStmt($stmt, $schEntriesRecNos, 'record#');
            $ok = ExecStmtEx($stmt, $numRows, true);
            assert($numRows == count($schEntries));
        }

        return $ok;
    }
    /**
     * @param int                         $negDetailKey
     * @param ContractNegativeBillingInfo $negBillingInfo
     * @return bool
     */
    private static function queryNegativeBillingInfosInternal($negDetailKey, $negBillingInfo)
    {
        $ok = true;
        /* @var ContractNegativeBillingManager $mgr */
        $mgr = Globals::$g->gManagerFactory->getManager('contractnegativebilling');
        $negBillingObj = $mgr->getByParentDetailKey($negDetailKey);
        if (isset($negBillingObj['CHILDREN']) && arrayCount($negBillingObj['CHILDREN']) > 0) {
            $ok = $negBillingInfo->addNegativeBillingObj($negBillingObj);
        }
        return $ok;
    }
    
    /**
     * @param ContractNegativeBillingInfo|null $negBillingInfo
     * @param bool                             $resolveHasBeenFixed
     *
     * @return bool
     */
    public static function updateNegativeBillingEntries ($negBillingInfo, &$resolveHasBeenFixed)
    {
        $ok = true;
        $resolveHasBeenFixed = false;
        if ($negBillingInfo !== null) {
            $negBillingMgr = Globals::$g->gManagerFactory->getManager('contractnegativebilling');
            if ($negBillingInfo->isFromNegativeLine()) {
                if ($negBillingInfo->count() > 0) {
                    assert($negBillingInfo->count() == 1);
                    $negBillingObj = $negBillingInfo->getNegativeBillingObj(0);
                    $ok = $ok && $negBillingMgr->set($negBillingObj);
                    // Unleess there is a link to positive line, the caller has to fix schedules resolve themselves
                    $resolveHasBeenFixed = true;
                }
            } elseif ($negBillingInfo->isFromPositiveLine()) {
                foreach ($negBillingInfo->getNegativeBillingObjs() as $negBillingObj) {
                    $ok = $ok && $negBillingMgr->set($negBillingObj);
                    $handler = ContractNegativeBillingManager::getHandler($negBillingObj, false);
                    if ($ok && $handler !== null && $handler->rebuiltResolvesForBundle) {
                        // Schedules resolve is not fixed unleess we do MEA -- which updates the schedules resolve for negative and positive lines too
                        $resolveHasBeenFixed = true;
                    }
                }
            }
        }
        return $ok;
    }

    /**
     * @param int   $contractKey
     * @param array $summaryByChildren
     * @param array $summaryByParents
     *
     * @return bool
     */
    public static function querySummarizedEntries ($contractKey, &$summaryByChildren, &$summaryByParents)
    {
        $ok = true;

        $summaryParentField = ContractNegativeBillingManager::PARENTCNDETKEY;
        $summaryChildField = ContractNegativeBillingManager::CHILDCNDETKEY;
        $summaryParentLineNoField = 'PARENTLINENO';
        $summaryChildLineNoField = 'CHILDLINENO';

        assert($contractKey);

        $mgr = Globals::$g->gManagerFactory->getManager('contractnegativebillingentry');
        $negBillingEntries = $mgr->GetList([
            'selects' => [
                $summaryParentField, $summaryChildField, $summaryParentLineNoField, $summaryChildLineNoField,
                'LINKEDTOTALFLATAMOUNT', 'CHILDTOTFLATAMOUNT',
                'LINKEDTOTALBASEFLATAMOUNT', 'CHILDTOTBASEFLATAMOUNT',
            ],
            'filters' => [[
                ['CONTRACTKEY', '=', $contractKey],
            ]]
        ]);

        $summaryByChildren = [];
        $summaryByParents = [];
        foreach ($negBillingEntries as $negBillingEntry) {
            $childDetailKey = $negBillingEntry[$summaryChildField];
            if (!isset($summaryByChildren[$childDetailKey])) {
                $childEntry = $negBillingEntry;
                $childEntry[$summaryParentField] = [$negBillingEntry[$summaryParentField]];
                $childEntry[$summaryParentLineNoField] = [$negBillingEntry[$summaryParentLineNoField]];
                $summaryByChildren[$childDetailKey] = $childEntry;
            } else {
                $existingChildEntry = &$summaryByChildren[$childDetailKey];
                assert(!in_array($negBillingEntry[$summaryParentField], $existingChildEntry[$summaryParentField]));
                assert(!in_array($negBillingEntry[$summaryParentLineNoField], $existingChildEntry[$summaryParentLineNoField]));
                assert($existingChildEntry[$summaryChildLineNoField] == $negBillingEntry[$summaryChildLineNoField]);
                assert($existingChildEntry['CHILDTOTFLATAMOUNT'] == $negBillingEntry['CHILDTOTFLATAMOUNT']);
                assert($existingChildEntry['CHILDTOTBASEFLATAMOUNT'] == $negBillingEntry['CHILDTOTBASEFLATAMOUNT']);
                $existingChildEntry[$summaryParentField][] = $negBillingEntry[$summaryParentField];
                $existingChildEntry[$summaryParentLineNoField][] = $negBillingEntry[$summaryParentLineNoField];
                $existingChildEntry['LINKEDTOTALFLATAMOUNT'] = ibcadd($existingChildEntry['LINKEDTOTALFLATAMOUNT'], $negBillingEntry['LINKEDTOTALFLATAMOUNT']);
                $existingChildEntry['LINKEDTOTALBASEFLATAMOUNT'] = ibcadd($existingChildEntry['LINKEDTOTALBASEFLATAMOUNT'], $negBillingEntry['LINKEDTOTALBASEFLATAMOUNT']);
            }

            $parentDetailKey = $negBillingEntry[$summaryParentField];
            if (!isset($summaryByParents[$parentDetailKey])) {
                $parentEntry = $negBillingEntry;
                $parentEntry[$summaryChildField] = [$negBillingEntry[$summaryChildField]];
                $parentEntry[$summaryChildLineNoField] = [$negBillingEntry[$summaryChildLineNoField]];
                $summaryByParents[$parentDetailKey] = $parentEntry;
            } else {
                $existingParentEntry = &$summaryByParents[$parentDetailKey];
                assert(!in_array($negBillingEntry[$summaryChildField], $existingParentEntry[$summaryChildField]));
                assert(!in_array($negBillingEntry[$summaryChildLineNoField], $existingParentEntry[$summaryChildLineNoField]));
                assert($existingParentEntry[$summaryParentLineNoField] == $negBillingEntry[$summaryParentLineNoField]);
                $existingParentEntry[$summaryChildField][] = $negBillingEntry[$summaryChildField];
                $existingParentEntry[$summaryChildLineNoField][] = $negBillingEntry[$summaryChildLineNoField];
                $existingParentEntry['LINKEDTOTALFLATAMOUNT'] = ibcadd($existingParentEntry['LINKEDTOTALFLATAMOUNT'], $negBillingEntry['LINKEDTOTALFLATAMOUNT']);
                $existingParentEntry['LINKEDTOTALBASEFLATAMOUNT'] = ibcadd($existingParentEntry['LINKEDTOTALBASEFLATAMOUNT'], $negBillingEntry['LINKEDTOTALBASEFLATAMOUNT']);
            }
        }
        return $ok;
    }

    /**
     * @param int $contractKey
     *
     * @return bool
     */
    public static function validatePositiveLinksAcrossContract($contractKey)
    {
        $ok = self::querySummarizedEntries($contractKey, $summaryByChildren, $summaryByParents);
        if ($ok) {
            foreach ( $summaryByChildren as $summaryByChild) {
                if (!self::validateSummaryByChild($summaryByChild, 'LINKEDTOTALFLATAMOUNT', 'CHILDTOTFLATAMOUNT', 'total flat amount')) {
                    $ok = false;
                }
                if (!self::validateSummaryByChild($summaryByChild, 'LINKEDTOTALBASEFLATAMOUNT', 'CHILDTOTBASEFLATAMOUNT', 'total base flat amount')) {
                    $ok = false;
                }
            }
        }

        return $ok;
    }

    /**
     * @param array     $summaryByChild
     * @param string    $linkedField
     * @param string    $childField
     * @param string    $what
     *
     * @return bool
     */
    private static function validateSummaryByChild ($summaryByChild, $linkedField, $childField, $what)
    {
        $ok = true;
        if (ibcadd($summaryByChild[$linkedField], $summaryByChild[$childField]) < 0) {
            Globals::$g->gErr->addIAError('CN-0265', __FILE__ . ':' . __LINE__, 'Line no ' . $summaryByChild['CHILDLINENO'] .
                                                                       ' with ' . $what . ' ' . $summaryByChild[$childField] .
                                                                       ' is used by too many discount lines: ' . implode(',', $summaryByChild['PARENTLINENO']) .
                                                                       ' with a total discount of ' . $summaryByChild[$linkedField],
                                          ['LINENO' => $summaryByChild['CHILDLINENO'],
                                           'AMOUNT_TYPE' => $what,
                                           'AMOUNT' => $summaryByChild[$childField],
                                           'LINES' => implode(',', $summaryByChild['PARENTLINENO']),
                                           'LINKED_AMOUNT' => $summaryByChild[$linkedField]]);
            $ok = false;
        }
        return $ok;
    }

    /**
     * Primarily recreate the negative (parent) billing schedule entries to follow its linked positive (children)  schedule
     * entries.  Also populate LINKEDTOTALFLATAMOUNTs in the obj array based on the sum of the linked entries of each
     * positive (child) contract detail key.
     *
     * @param array $obj This is the $values array of the contractnegativebilling entity and its children
     * @return bool
     */
    public function prepareSaveAction(&$obj)
    {
        $ok = true;

        $cnDetailKeys = INTACCTarray_project($obj['CHILDREN'], ContractNegativeBillingManager::CHILDCNDETKEY, false, false);
        $parentCnDetailKey = $obj[ContractNegativeBillingManager::PARENTCNDETKEY];
        $cnDetailKeys[] = $parentCnDetailKey;

        if (count($cnDetailKeys) > 1) {
            try {
                $cnHandler = new ContractObjHandler();
                $cnDetails = $cnHandler->getBillingSchedulesForContractDetails($cnDetailKeys);

                $this->extractParentDetail($cnDetails, $parentCnDetailKey);

                $this->computeAmountsAndSchedules($cnDetails, $newParentEntries, $entriesByCnDetailKey);
                $this->generateNewBillingSchedule($newParentEntries);

                // Populate the $obj's CHILDREN linked amount fields
                foreach ($obj['CHILDREN'] as $key => $row) {
                    $entries = $entriesByCnDetailKey[$row[ContractNegativeBillingManager::CHILDCNDETKEY]];
                    $sumAmount = ContractUtil::sumAmountObjectRoundingErrors($entries);
                    $sumBaseAmount = ContractUtil::sumBaseAmountObjectRoundingErrors($entries);
                    $obj['CHILDREN'][$key]['LINKEDTOTALFLATAMOUNT'] = $sumAmount;
                    $obj['CHILDREN'][$key]['LINKEDTOTALBASEFLATAMOUNT'] = $sumBaseAmount;
                }
            } catch (IAException $e) {
                Globals::$g->gErr->addIAError('CN-0250', __FILE__ . ':' . __LINE__,
                                              'Failed saving discount distribution. ' . $e->getMessage(),
                                              ['EX_MESSAGE' => ContractUtil::getThrowableMessage($e)]);
                $ok = false;
            }
        } else {
            // there is no child to link to this parent, which means we need to clear the link records (later in the Upsert logic)
            // and reset the billing and payment schedules
            $cnHandler = new ContractObjHandler();
            $ok = $cnHandler->resetBillingAndPaymentSchedules($parentCnDetailKey, true);
        }

        return $ok;
    }

    /**
     * @param ContractDetailInfo[] $cnDetails
     * @param int              $parentCnDetailKey
     */
    private function extractParentDetail(&$cnDetails, $parentCnDetailKey)
    {
        foreach ($cnDetails as $key => $cnDetail) {
            if ($cnDetail->getRecordNo() == $parentCnDetailKey) {
                $this->parentDetail = $cnDetail;
                unset ($cnDetails[$key]);
                break;
            }
        }

        assert ($this->parentDetail != null);

        $parentAmt = $this->parentDetail->getTotalFlatAmount();
        $parentBaseAmt = $this->parentDetail->getTotalFlatBaseAmount();
        if ($parentAmt >= 0 || $parentBaseAmt > 0) {
            // If a detail line is linked as parent, by definition it has to be negative.
            // Either programmatic error, or the sign is flipped without clearing the linking.
            $msg = 'Contract detail line ' . $this->parentDetail->getLineNo() .
                ' cannot distribute discount to other contract lines.  It has to be negative or clear the links first to make it positive';
            throw IAException::newIAException('CN-0921', $msg, ['LINENO' => $this->parentDetail->getLineNo()]);
        }
    }

    /**
     * @param ContractDetailInfo[]      $cnDetails
     * @param ContractScheduleEntry[]   $newParentEntries
     * @param ContractScheduleEntry[][] $entriesByCnDetailKey
     */
    private function computeAmountsAndSchedules($cnDetails, &$newParentEntries, &$entriesByCnDetailKey)
    {
        $totalChildrenAmt = 0;
        $totalChildrenBaseAmt = 0;
        $newParentEntries = [];
        $entriesByCnDetailKey = [];

        foreach ($cnDetails as $cnDetail) {
            if ($cnDetail->getTotalFlatAmount() < 0) {
                $msg = 'In order to distribute discount to line ' . $cnDetail->getLineNo() .
                    ' (' . $cnDetail->getRecordNo() . '), it has to be non-negative.  The amount is ' .
                    $cnDetail->getTotalFlatAmount();
                throw IAException::newIAException('CN-0922', $msg,
                                                  ['LINENO' => $cnDetail->getLineNo(),
                                                   'RECORDNO' => $cnDetail->getRecordNo(),
                                                   'TOTALFLATAMOUNT' => $cnDetail->getTotalFlatAmount()]);
            }
            assert ($cnDetail->getTotalFlatBaseAmount() >= 0);
            $totalChildrenAmt = ibcadd($totalChildrenAmt, $cnDetail->getTotalFlatAmount());
            $totalChildrenBaseAmt = ibcadd($totalChildrenBaseAmt, $cnDetail->getTotalFlatBaseAmount());
            $this->populateNewParentEntries($newParentEntries, $entriesByCnDetailKey, $cnDetail);
        }

        $this->distributeParentsAmounts($newParentEntries, $totalChildrenAmt, $totalChildrenBaseAmt);
    }

    /**
     * @param ContractScheduleEntry[] $entries
     */
    private function generateNewBillingSchedule($entries)
    {
        $mgr = Globals::$g->gManagerFactory->getManager('contractbillingschedule');
        $billingScheduleInfo = $this->parentDetail->getContractBillingSchedule()->getContractScheduleInfo();
        $scheduleKey = $billingScheduleInfo->getRecordNo();
        $billingSchedule = $mgr->get($scheduleKey);
        $existingEntries = $billingSchedule['SCHEDULEENTRY'];
        $billingSchedule['SCHEDULEENTRY'] = [];
        foreach ($entries as $entry) {
            $entryArray = $entry->getValues();
            $idx = $this->findMatchingExistingScheduleEntry($existingEntries, $entryArray);
            if ($idx >= 0) {
                $billingSchedule['SCHEDULEENTRY'][] = $existingEntries[$idx];
            } else {
                $billingSchedule['SCHEDULEENTRY'][] = $entryArray;
            }
        }
        $billingSchedule[ContractBillingScheduleManager::NEGATIVE_SCHEDULE_CONTEXT] = true;

        $ok = $this->validateAndNullifyUGIRefs();
        $ok = $ok && $mgr->set($billingSchedule);
        if (!$ok) {
            throw new IAException('Failed to save Billing Schedule', 'CN-0923');
        }

        $contractManager = Globals::$g->gManagerFactory->getManager('contract');
        if ( $contractManager->hasMEAOrKitAllocation($this->parentDetail->getContractId()) === true ) {
            $this->rebuiltResolvesForBundle = true;
        }
    }

    /**
     * Validates and removes UGI references
     *
     * @return bool
     */
    private function validateAndNullifyUGIRefs()
    {
        $billingSchInfo = $this->parentDetail->getContractBillingSchedule()->getContractScheduleInfo();

        $billingSchKey = $billingSchInfo->getRecordNo();
        $ok = ContractGenInvoiceUtil::validateUGIRefs($billingSchKey);
        if (!$ok) {
            $contractLineNo = $this->parentDetail->getLineNo();
            $msg = sprintf(_('The discount associated with contract line %1$s can\'t be distributed as line %1$s has one or more invoices posted against it.'), $contractLineNo);
            Globals::$g->gErr->addIAError('CN-0251', __FILE__.':'.__LINE__, $msg, ['CONTRACTLINENO' => $contractLineNo]);
        }
        $ok = $ok && ContractGenInvoiceUtil::nullifyUGIRefs($billingSchKey);

        return $ok;
    }


    /**
     * @param ContractScheduleEntry[] $entries
     * @param float                   $totalChildrenAmt
     * @param float                   $totalChildrenBaseAmt
     */
    private function distributeParentsAmounts($entries, $totalChildrenAmt, $totalChildrenBaseAmt)
    {
        $parentAmt = $this->parentDetail->getTotalFlatAmount();
        $parentBaseAmt = $this->parentDetail->getTotalFlatBaseAmount();
        if (ibcadd($parentAmt, $totalChildrenAmt) < 0) {
            $msg = "Cannot distribute discount since discount amount is more negative than the total positive ($parentAmt vs $totalChildrenAmt)";
            throw IAException::newIAException('CN-0924', $msg, ['PARENT_AMT' => $parentAmt, 'TOTAL_CHILDREN_AMT' => $totalChildrenAmt]);
        }
        if (ibcadd($parentBaseAmt, $totalChildrenBaseAmt) < 0) {
            $msg = "Cannot distribute discount since discount base amount is more negative than the total positive ($parentBaseAmt vs $totalChildrenBaseAmt)";
            throw IAException::newIAException('CN-0925', $msg, ['PARENT_BASE_AMT' => $parentBaseAmt, 'TOTAL_CHILDREN_BASE_AMT' => $totalChildrenBaseAmt]);
        }

        foreach ($entries as $entry) {
            $originalEntryAmt = $entry->getAmount();
            $entry->setAmount(ContractUtil::computeAmountRatio($parentAmt, $originalEntryAmt, $totalChildrenAmt));
            $entry->setBaseAmount(ContractUtil::computeAmountRatio($parentBaseAmt, $originalEntryAmt, $totalChildrenAmt));
            assert ($entry->getAmount() <= 0);
            assert ($entry->getBaseAmount() <= 0);
            assert ($originalEntryAmt >= 0);
        }
        ContractUtil::correctObjectRoundingErrors($parentAmt, $entries);
        ContractUtil::correctObjectRoundingBaseAmountErrors($parentBaseAmt, $entries);
    }

    /**
     * @param ContractScheduleEntry[]   $entries
     * @param ContractScheduleEntry[][] $entriesByCnDetailKey
     * @param ContractDetailInfo        $cnDetail
     */
    private function populateNewParentEntries(&$entries, &$entriesByCnDetailKey, ContractDetailInfo $cnDetail)
    {
        $cnDetailKey = $cnDetail->getRecordNo();
        $parentBillingSchedule = $this->parentDetail->getContractBillingSchedule()->getContractScheduleInfo();
        $parentScheduleKey = $parentBillingSchedule->getRecordNo();
        $billingSchedule = $cnDetail->getContractBillingSchedule();

        if (!isset($entriesByCnDetailKey[$cnDetailKey])) {
            $entriesByCnDetailKey[$cnDetailKey] = [];
        }

        if ($billingSchedule) {
            $clonedEntries = $billingSchedule->getContractScheduleInfo()->getClonedEntries();

            foreach ($clonedEntries as $entry) {
                $entry->setStateChanged(ContractScheduleEntry::CONTRACT_SCHEDULE_ENTRY_NEW);
                $entry->setScheduleKey($parentScheduleKey);
                $entry->setScheduleEntryKey(null);
                $entry->setBillScheduleEntryKey($entry->getRecordNo());
                $entry->setRecordNo(null);

                $entry->setPosted(null);
                $entry->setState(ContractScheduleEntryState::STATE_OPEN);
                $entry->setActualPostingDate(null);
                $entry->setPostedBaseAmount(null);
                $entry->setPostedExchangeRate(null);

                $entries[] = $entry;
                $entriesByCnDetailKey[$cnDetailKey][] = $entry;
            }
        }
    }

    /* @var string[] $FIELDS_TO_COMPARE  */
    private static $FIELDS_TO_COMPARE = [
        'AMOUNT',
        'BASEAMOUNT',
        'POSTEDBASEAMOUNT',
        'EXCHANGE_RATE',
        'POSTEDEXCHANGE_RATE',
        'POSTINGDATE',
        'ACTUALPOSTINGDATE',
        'POSTED',
        'STATE',
        'COMPUTATIONMEMO',
        'BILLSCHEDULEENTRYKEY',
    ];

    /**
     * @param array $existingEntries
     * @param array $newEntry
     *
     * @return int|string
     */
    private function findMatchingExistingScheduleEntry($existingEntries, $newEntry)
    {
        $retVal = -1;
        foreach ($existingEntries as $key => $existingEntry) {
            $match = true;
            foreach (self::$FIELDS_TO_COMPARE as $field) {
                if (isArrayValueProvided($newEntry, $field) &&
                    (!isArrayValueProvided($existingEntry, $field) || $newEntry[$field] != $existingEntry[$field])
                ) {
                    $match = false;
                    break;
                }
            }
            if ($match) {
                $retVal = $key;
                break;
            }
        }
        return $retVal;
    }
}

class ContractNegativeBillingInfo
{
    const FROM_POSITIVE_LINE = 1;
    const FROM_NEGATIVE_LINE = -1;
    /* @var int $fromLineSign */
    private $fromLineSign;
    /* @var int $fromDetailKey */
    private $fromDetailKey;
    /* @var array[] $negBillingObjs */
    private $negBillingObjs;    // the array of values array

    /**
     * @param int $fromLineSign
     * @param int $fromLineDetailKey
     */
    public function __construct($fromLineSign, $fromLineDetailKey)
    {
        $this->fromLineSign = $fromLineSign;
        $this->fromDetailKey = $fromLineDetailKey;
        $this->negBillingObjs = [];
    }

    /**
     * @param array $negBillingObj
     *
     * @return bool
     */
    public function addNegativeBillingObj($negBillingObj)
    {
        $ok = true;
        if ($this->isFromNegativeLine() && $this->count() > 0) {
            $ok = false;
        } else {
            $this->negBillingObjs[] = $negBillingObj;
        }
        return $ok;
    }

    /**
     * @return int
     */
    public function getFromLineSign()
    {
        return $this->fromLineSign;
    }

    /**
     * @return bool
     */
    public function isFromPositiveLine()
    {
        return $this->fromLineSign === self::FROM_POSITIVE_LINE;
    }

    /**
     * @return bool
     */
    public function isFromNegativeLine()
    {
        return $this->fromLineSign === self::FROM_NEGATIVE_LINE;
    }

    /**
     * @return int
     */
    public function getFromDetailKey()
    {
        return $this->fromDetailKey;
    }

    /**
     * @return array[]
     */
    public function getNegativeBillingObjs()
    {
        return $this->negBillingObjs;
    }

    /**
     * @param int $idx
     * @return array
     */
    public function getNegativeBillingObj($idx)
    {
        return $this->negBillingObjs[$idx];
    }

    /**
     * @return int
     */
    public function count()
    {
        return count($this->negBillingObjs);
    }
}
