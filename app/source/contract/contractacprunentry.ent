<?php
/**
 * Entity for Contract ACP Run Entry.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Sage Intacct Inc., All Rights Reserved
 */

$kSchemas['contractacprunentry'] = [
    'object' => [
        'RECORDNO',
        'CONTRACTRUNKEY',
        'CONTRACTKEY',
        'CONTRACTID',
        'CONTRACTBEGINDATE',
        'ERRORDATA',
        'NUMPROCESSED',
        'STATE',
        'CREATEDBY',
        'MODIFIEDBY',
        'WHENCREATED',
        'WHENMODIFIED'
    ],
    'schema' => [
        'RECORDNO'          => 'record#',
        'CONTRACTRUNKEY'    => 'contractrunkey',
        'CONTRACTKEY'       => 'contractkey',
        'CONTRACTID'        => 'contract.contractid',
        'CONTRACTBEGINDATE' => 'contract.begindate',
        'ERRORDATA'         => 'errordata',
        'NUMPROCESSED'      => 'numprocessed',
        'STATE'             => 'state',
        'CREATEDBY'         => 'createdby',
        'MODIFIEDBY'        => 'modifiedby',
        'CREATEDBYUSER'     => 'userinfo.description',
        'WHENCREATED'       => 'whencreated',
        'WHENMODIFIED'      => 'whenmodified'
    ],
    'children' => [
        'contract' => [
            'fkey' => 'contractkey', 'invfkey' => 'record#',
            'join' => 'inner', 'table' => 'contract',
        ],
        'userinfo' => [
            'fkey' => 'createdby', 'invfkey' => 'record#',
            'join' => 'outer', 'table' => 'userinfo',
        ],
    ],
    'sqldomarkup' => true,
    'sqlmarkupfields' => [
        'WHENCREATED',
        'WHENMODIFIED'
    ],
    'fieldinfo' => [
        [
            'path' => 'RECORDNO',
            'desc' => 'IA.RECORD_NUMBER',
            'fullname' => 'IA.RECORD_NUMBER',
            'hidden' => true,
            'type' => [
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'size' => 8,
                'format' => $gRecordNoFormat,
            ],
            'id' => 1
        ],
        [
            'path' => 'CONTRACTRUNKEY',
            'fullname' => 'IA.PROCESS_CONTRACT_SCHEDULES_RUN_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'readonly' => true,
            'derived' => true,
            'renameable' => true,
            'id' => 2
        ],
        [
            'path' => 'CONTRACTKEY',
            'fullname' => 'IA.CONTRACT_KEY',
            'desc' => 'IA.CONTRACT_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'readonly' => true,
            'derived' => true,
            'renameable' => true,
            'id' => 3
        ],
        [
            'path' => 'CONTRACTID',
            'fullname' => 'IA.CONTRACT',
            'desc' => 'IA.CONTRACT',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'text',
                'entity' => 'contract',
                'pickentity' => 'contractpick',
                'maxlength' => 20
            ),
            'nonew' => true,
            'noedit' => true,
            'required' => true,
            'renameable' => true,
            'id' => 4,
        ],
        [
            'path' => 'CONTRACTBEGINDATE',
            'desc' => 'IA.CONTRACT_START_DATE',
            'fullname' => 'IA.CONTRACT_START_DATE',
            'type' => $gDateType,
            'readonly' => true,
            'id' => 5,
        ],
        [
            'path' => 'ERRORDATA',
            'fullname' => 'IA.NOTES',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'size' => 4000,
                'maxlength' => 4000,
            ],
            'id' => 6,
        ],
        [
            'path' => 'ERRORDATA_HTML',
            'fullname' => 'IA.NOTES',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'size' => 4000,
                'maxlength' => 4000,
            ],
            'isHTML'   => true,
        ],
        [
            'path' => 'STATE',
            'fullname' => 'IA.RUN_STATUS',
            'desc' => 'IA.RUN_STATUS',
            'type' => [
                'ptype' => 'enum',
                'type' => 'text',
                'validlabels' => ['IA.SUCCESS', 'IA.FAILED', 'IA.IN_PROGRESS', 'IA.QUEUED'],
                'validvalues' => ['Success', 'Failed', 'In Progress', 'Queued'],
                '_validivalues' => ['S', 'F', 'I', 'Q'],
            ],
            'default' => 'Queued',
            'readonly' => true,
            'id' => 7,
        ],
        [
            'path' => 'NUMPROCESSED',
            'fullname' => 'IA.NUMBER_PROCESSED',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
            ),
            'readonly' => true,
            'id' => 8
        ],
        [
            'path'      => 'CREATEDBYUSER',
            'fullname'  => 'IA.CREATED_BY',
            'type'      => [
                'ptype'     => 'text',
                'type'      => 'text',
            ],
            'readonly'  => true,
        ],
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,

        //Ui_fields
        [

        ]
    ],
    'table' => 'contractrunentry',
    'vid' => 'RECORDNO',
    'autoincrement' => 'RECORDNO',
    'printas' => 'IA.CONTRACT_SCHEDULES_PROCESSING_RESULTS_ENTRY',
    'pluralprintas' => 'IA.CONTRACT_SCHEDULES_PROCESSING_RESULTS_ENTRIES',
    'module' => 'cn',
    'renameable' => true,

    'allowDDS' => true,
];
