<?php

/**
 * Lister class for Contract Group
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

/**
 * Lister class for Class Group
 */
class ContractPriceListLister extends ContractModuleNLister
{

    /**
     * Contract Price List specific tokens
     * @var string[]
     */
    protected $additionalTokens = [
        'IA.DETAILS'
    ];

    public function __construct()
    {
        $params = [
            'entity'           => 'contractpricelist',
            'title'            => 'IA.BILLING_PRICE_LISTS',
            'fields'           => [ 'NAME', 'DESCRIPTION', 'STATUS', "'DETAILS'" ],
            'nonencodedfields' => [ "'DETAILS'", 'RECORD_URL'],
            'importtype'       => 'contractpricelist',
        ];

        parent::__construct($params);
    }


    function BuildTable()
    {
        parent::BuildTable();
        $opID = GetOperationId('cn/lists/contractitempricelist');
    
        foreach($this->table as $key => $row)
        {
            $plistKey  = urlencode($row['RECORDNO'] ?? '');
            $plistName = urlencode($row['NAME'] ?? '');

            $url = 'lister.phtml?.op='.$opID.'&.plkey='.$plistKey.'&.plname='.$plistName.'&'.OptDone(ScriptRequest().'&.plkey='.$plistKey);
            $url = URLEncryption::transformUrl($url);


            $text = GT($this-> textMap, 'IA.DETAILS');

            $this->table[$key]["'DETAILS'"] =  '<a tabIndex="-1" href="' . $url . '">' . $text . "</a>";
        }
    }


}
