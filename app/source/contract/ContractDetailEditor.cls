<?php

/**
 * Editor class for Contract
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

/**
 * Editor class for Contract object
 */

import('ContractUtil');
import('ChangeControlTypeManager');

/**
 * @property ContractDetailManager $entityMgr
 */
class ContractDetailEditor extends FormEditor
{
    //use ContractDetailEditorBalancesTrait;
    
    /** @var bool $isMCPEnabled */
    private $isMCPEnabled;
    /** @var bool $isStandardRevRecEnabled */
    private $isStandardRevRecEnabled;
    /** @var bool $isAdvancedRevRecEnabled */
    private $isAdvancedRevRecEnabled;
    /** @var bool $isExpenseEnabled */
    private $isExpenseEnabled;
    /** @var bool $isCdList */
    private $isCdList;
    /** @var bool $isUsageBillingEnabled */
    private $isUsageBillingEnabled;
    /** @var string[] $contractValues */
    private $contractValues;
    /** @var int $previewRecordNo */
    private $previewRecordNo;

    /**
     * @var array $contractDetailData
     */
    private $contractDetailData;

    protected const localTokens = [
        'IA.VIEW_SCHEDULE',
        'IA.VIEW_SCHEDULE_1',
        'IA.VIEW_SCHEDULE_2',
        'IA.CONTRACT_LINE_NUM_OF_CONTRACT_ID_NAME',
        'IA.CONTRACT_LINE_NUM_PERIOD_NUM_OF_CONTRACT_ID_NAME',
        'IA.THE_GL_POSTING_DATE_MUST_BE_WITHIN_AN_OPEN_PERIOD',
        'IA.DRAFT_CONTRACT_LINES_ARE_INELIGIBLE_FOR_RENEWAL',
        'IA.UNCANCEL_CONTRACT_LINE_NUM_OF_CONTRACT_ID',
        'IA.CONTRACT_LINE_INELIGIBLE_TO_BE_RENEWED_TO_EVERGREEN',
        'IA.KIT_REVENUE_NOTICE',
        'IA.KIT_COMPONENTS',
        'IA.PREVIEW_BILLING_SCHEDULE',
    ];

    public const jsTokens = [
        'IA.TOTAL_FLAT_FIXED_AMOUNT',
        'IA.TOTAL_BASE_FLAT_FIXED_AMOUNT',
        'IA.CALCULATE',
        'IA.MONTHLY',
        'IA.QUARTERLY',
        'IA.ANNUALLY',
        'IA.THIS_CONTRACT_IS_NOT_SET_UP_TO_RENEW',
        'IA.CONTRACT_LINE_IN_ACTIVE_MEA_REVALUE_WARNING',
        'IA.CONTRACT_LINE_FIELDS_CHANGED_WARNING',
        'IA.PROJECT_TIME',
        'IA.PROJECT_TIME_AND_MATERIALS',
        'IA.PROJECT_MATERIALS',
    ];

    private const acctAndJournalsTokens = [
        'IA.AR_UNBILLED', 'IA.AR_BILLED', 'IA.DR_UNBILLED', 'IA.DR_BILLED',
        'IA.DR_PAID', 'IA.SALES_UNBILLED', 'IA.SALES_BILLED', 'IA.SALES_PAID',
        'IA.REVENUE_JOURNAL_1', 'IA.REVENUE_JOURNAL_2', 'IA.EXPENSE_JOURNAL_1',
        'IA.EXPENSE_JOURNAL_2', 'IA.MONTHLY_RECURRING_REVENUE_MRR', 'IA.CUSTOMER_COUNT',
        'IA.NEW_MRR', 'IA.ADD_ON_MRR', 'IA.RENEWAL_UPGRADE', 'IA.RENEWAL_DOWNGRADE',
        'IA.CHURN_MRR', 'IA.DOWNGRADE', 'IA.MRR_OFFSET', 'IA.CUSTOMER_COUNT'
    ];

    /**
     * @param array $_params default editor parameters
     */
    public function __construct($_params = [])
    {
        $this->additionalTokens = array_merge($this->additionalTokens, self::localTokens);
        $this->textTokens = array_merge($this->textTokens, ContractEditor::jsTokens, self::jsTokens);

        parent::__construct($_params);

        $this->isMCPEnabled = ContractUtil::isMCPEnabled();

        $cnsetupMgr = Globals::$g->gManagerFactory->getManager('cnsetup');
        $this->isStandardRevRecEnabled = $cnsetupMgr->isStandardRevRecEnabled();
        $this->isAdvancedRevRecEnabled = $cnsetupMgr->isAdvancedRevRecEnabled();
        $this->isExpenseEnabled = $cnsetupMgr->isExpenseEnabled();
        $this->isUsageBillingEnabled = $cnsetupMgr->isUsageBillingEnabled();
    }

    /**
     * getJavaScriptFileNames
     *
     * @return array $jsfiles
     */
    protected function getJavaScriptFileNames()
    {
        $jsfiles[] = "../resources/js/contract.js";

        return $jsfiles;
    }

    /**
     * Get the editor button list
     * ** commenting out percentage billing setup for now
     * ** this functionality will be implemented in contracts version 2.0
     *
     * @param string $state the editor state
     *
     * @return array the buttons list
     */
    //    public function getStandardButtons($state)
    //    {
    //        $buttons = parent::getStandardButtons($state);
    //
    //        if ( $state === Editor_ShowEditState || $state === Editor_ShowViewState ) {
    //            $action = $this->createAction(
    //                'percent_b',
    //                Editor_CancelBtnID . '_percent_b',
    //                'Percentage Billing',
    //                'byline',
    //                false,
    //                "renderPercentageView('$state', 'byline');",
    //                false
    //            );
    //            $action['inMoreActions'] = true;
    //
    //            $buttons[] = $action;
    //
    //        }
    //
    //        return $buttons;
    //    }

    /**
     * @param array $params
     */
    protected function buildDynamicMetadata(&$params)
    {
        $verb = $this->getVerb();
    
        $contractDetail = null;
        $contractId = null;
        $contractDetailKey = Request::$r->{Globals::$g->kId};
        if ($contractDetailKey) {
            $contractDetail = $this->getCnDetailData();
            $contractId = $contractDetail[0]['CONTRACTID'] ?? null;
        } elseif ($verb == 'create') {
            $contractId = Request::$r->_contractid;
        }

        if ($contractId) {
            $this->setContractValues($contractId);
        }

        if ( ! util_isPlatformDisabled() ) {
            $matches = [];
            self::findElements($params, [ 'path' => 'ITEMID' ], EditorComponentFactory::TYPE_FIELD, $matches);
            $matches[0]['standard'] = true;
            $matches[0]['isDimension'] = true;
            $matches[0]['autofillrelated'] = Pt_StandardUtil::autoFillRelated('item');
        }

        $dimensionSection = [];
        $dimensionList = [];
        self::findElements($params, [ 'id' => 'dimensions' ], EditorComponentFactory::TYPE_SECTION, $dimensionSection);
        self::findElements($dimensionSection, [], EditorComponentFactory::TYPE_FIELD, $dimensionList);

        $taskIndex = null;
        $projectIndex = null;
        foreach ( $dimensionList[0] as $id => &$oneDim ) {
            if ( $oneDim['path'] == 'TASKID' ) {
                $taskIndex = $id;
            }
            if ( $oneDim['path'] == 'PROJECTID' ) {
                $oneDim['events'] = [ 'change' => 'onProjectChange(this);' ];
                $projectIndex = $id;
            }
        }
        if ( $taskIndex !== null && $projectIndex !== null ) {
            $temp = $dimensionList[0][$taskIndex];
            $dimensionList[0][$taskIndex] = $dimensionList[0][$projectIndex];
            $dimensionList[0][$projectIndex] = $temp;
        }

        if ( $verb != "create" ) {
            self::findAndSetMetadata($params, [ 'path' => 'STATE' ], [ 'hidden' => false ]);
        }
        $dimensionSection = [];
        $dimensionList = [];
        self::findElements($params, [ 'id' => 'dimensions' ], EditorComponentFactory::TYPE_SECTION, $dimensionSection);
        self::findElements($dimensionSection, [], EditorComponentFactory::TYPE_FIELD, $dimensionList);

        foreach ( $dimensionList[0] as &$oneDim ) {
            if ( $oneDim['path'] == 'TASKID' ) {
                $oneDim['events'] = [ 'change' => 'onTaskSelectCheck(this);' ];
            }
            if ( $oneDim['path'] == 'PROJECTID' ) {
                $oneDim['events'] = [ 'change' => 'onProjectChange(this);' ];
            }
        }

        // Show required only on the UI
        self::findAndSetMetadata($params, [ 'path' => 'GLPOSTINGDATE' ], [ 'required' => true ]);

        if ( $verb == "view" || $verb == "edit" ) {
            self::findAndSetMetadata($params, [ 'path' => 'REVENUESCHEDULELINK' ], [ 'hidden' => false ]);
            self::findAndSetMetadata($params, [ 'path' => 'REVENUE2SCHEDULELINK' ], [ 'hidden' => false ]);
            self::findAndSetMetadata($params, [ 'path' => 'BILLINGSCHEDULELINK' ], [ 'hidden' => false ]);
            self::findAndSetMetadata($params, [ 'path' => 'EXPENSESCHEDULELINK' ], [ 'hidden' => false ]);
            self::findAndSetMetadata($params, [ 'path' => 'EXPENSE2SCHEDULELINK' ], [ 'hidden' => false ]);
            self::findAndSetMetadata($params, [ 'path' => 'BUNDLENO' ], [ 'hidden' => false ]);

            // Enable balances and accounts
            $hiddenPages = [];
            self::findElements($params, [ 'id' => 'lineBalancesPage' ], EditorComponentFactory::TYPE_PAGE,
                               $hiddenPages);
            self::findElements($params, [ 'id' => 'accountsPage' ], EditorComponentFactory::TYPE_PAGE, $hiddenPages);
            self::findElements($params, [ 'id' => 'historyPage' ], EditorComponentFactory::TYPE_PAGE, $hiddenPages);

            GetModulePreferences(Globals::$g->kCNid, $prefs);
            $hiddenPages[0]['child'][0]['section'][1]['title'] = $prefs['REVENUEJOURNAL1'];
            $hiddenPages[0]['child'][0]['section'][2]['title'] = $prefs['REVENUEJOURNAL2'];
            $hiddenPages[2]['child'][0]['section'][1]['title'] = $prefs['REVENUEJOURNAL1'];
            $hiddenPages[2]['child'][0]['section'][2]['title'] = $prefs['REVENUEJOURNAL2'];

            if ( ContractBillingMethodTypes::isProjectTime($contractDetail[0]['BILLINGMETHOD']) ) {
                self::findElements($params, [ 'id' => 'projecttimeaggtrx' ], EditorComponentFactory::TYPE_PAGE,
                                   $hiddenPages);
            }

            // Show required only on the UI for non-Draft lines
            if ( $contractDetail[0]['STATE'] != ContractScheduleState::STATE_DRAFT ) {
                self::findAndSetMetadata($params, [ 'path' => 'GLPOSTINGDATE' ], [ 'required' => true ]);
            }

            if ( IsInstalled(Globals::$g->kDBBid) ) {
                self::findElements($params, [ 'id' => 'mrrHistoryPage' ], EditorComponentFactory::TYPE_PAGE,
                                   $hiddenPages);
            }

            if ( $this->isExpenseEnabled ) {
                self::findElements($params, [ 'id' => 'contractexpensepage' ], EditorComponentFactory::TYPE_PAGE,
                                   $hiddenPages);
            }
            foreach ( $hiddenPages as $idx => $page ) {
                $hiddenPages[$idx]['hidden'] = false;
            }

            if ( IsInstalled(Globals::$g->kDBBid) ) {
                $found = [];
                self::findElements($params, [ 'path' => 'DBBACCOUNTS' ], EditorComponentFactory::TYPE_SECTION,
                                   $found);
                if ( $found[0] ) {
                    $found[0]['hidden'] = false;
                }
            }
        }

        self::findElements($params, [ 'path' => 'CONTRACTDETAIL_EXPENSEDETAIL' ], EditorComponentFactory::TYPE_GRID,
                           $expGrid);
        self::findElements($expGrid, [ 'path' => 'CUSTOMERID' ], EditorComponentFactory::TYPE_FIELD, $custMatch);
        if ( $custMatch[0] ) {
            $custMatch[0]['hidden'] = true;
        }

        if ( $this->isMCPEnabled ) {
            $mcpFields = [ 'EXCH_RATE_DATE', 'EXCHANGE_RATE', 'BASEAMOUNT' ];
            foreach ( $mcpFields as $fld ) {
                $found = [];
                self::findElements($expGrid, [ 'path' => $fld ], EditorComponentFactory::TYPE_FIELD, $found);
                if ( $found[0] ) {
                    $found[0]['hidden'] = false;
                }
            }
        }

        if ( $this->contractValues['RECORDNO'] ) {
            $advBillOptions = ContractUtil::getAdvBillOptions($this->contractValues['RECORDNO']);
            if ( $advBillOptions ) {
                self::findElements($params, [ 'path' => 'CNADVBILL' ], EditorComponentFactory::TYPE_SUBSECTION,
                                   $advBill);
                if ( $advBill[0] ) {
                    $advBill[0]['hidden'] = false;
                }

                // Take out 'Include with every invoice' option
                if ( $advBillOptions['ADVBILLBYTYPE'] == ContractUtil::ADV_BILLING_TYPE_DAYS ) {
                    $billingOptionsField = [];

                    self::findElements($params, [ 'path' => 'BILLINGOPTIONS' ], EditorComponentFactory::TYPE_FIELD,
                                       $billingOptionsField);

                    if ( $billingOptionsField[0] ) {
                        $billingOptionsField[0]['type'] = [
                            'ptype'         => 'enum',
                            'type'          => 'text',
                            'validlabels'   => [ 'IA.ONE_TIME', 'IA.USE_BILLING_TEMPLATE' ],
                            'validvalues'   => [ 'One-time', 'Use billing template' ],
                            '_validivalues' => [ 'O', 'U' ],
                        ];
                    }
                }
            }
        }
        ContractUtil::findAndSetJournalsMetadata($params, EditorComponentFactory::TYPE_SUBSECTION,
                                                 EditorComponentFactory::TYPE_FIELD);

        if ( $verb != 'create' ) {
            self::findAndSetMetadata($params, [ 'path' => 'DELIVERYSTATUS' ], [ 'readonly' => true ]);
            self::findAndSetMetadata($params, [ 'path' => 'DEFERRALSTATUS' ], [ 'readonly' => true ]);
        }
    }

    /**
     * @return string
     */
    protected function getLabelForSaveButton()
    {
        $label = parent::getLabelForSaveButton();
        $obj = Request::$r->GetCurrentObject();

        $verb = $this->getVerb();
        $isDraft = false;

        if ( ( $verb == "edit" || $verb == "showedit" ) && isset($obj['RECORDNO']) ) {
            $states = $this->getCurrentStates($obj['RECORDNO']);
            $isDraft = $states['STATE'] == ContractDetailState::STATE_DRAFT;
        } else if ( $obj['STATE'] == ContractDetailState::STATE_DRAFT || $verb == 'create'
                    || Request::$r->_action == 'copy' ) {
            $isDraft = true;
        }

        if ( $isDraft ) {
            $label = 'IA.DRAFT';
        }

        return $label;
    }

    /**
     * @param array $obj
     *
     * @return bool
     */
    protected function prepareObjectForCopyNew(&$obj)
    {
        if ( $obj['CONTRACT']['RECORDNO'] && ContractUtil::getAdvBillOptions($obj['CONTRACT']['RECORDNO']) ) {
            $view = $this->getView();
            $view->findAndSetProperty([ 'path' => 'CNADVBILL' ], [ 'hidden' => false ],
                                      EditorComponentFactory::TYPE_SUBSECTION);
        }

        ContractUtil::unsetContractLineObjectForCopyNew($obj);

        return parent::prepareObjectForCopyNew($obj);
    }

    /**
     * @param string $entity
     * @param array  $values
     *
     * @return string[]
     */
    protected function getNonEditableFields($entity, $values)
    {
        $this->maybeAppendContractValues($values);

        return parent::getNonEditableFields($entity, $values);
    }

    /**
     * @param array $obj the data
     *
     * @return bool  true on success and false on failure - make sure an error is raised in case of failure
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        $state = $this->getState();
        $view = $this->getView();
        $verb = $this->getVerb();
        $action = $this->getEditorAction();
    
        $contractId = $obj['CONTRACTID'] ?? null;
        if ($verb == "view" || $verb == "edit") {
            if ($contractId && !isset($this->contractValues)) {
                // Handle the case when go-back is clicked from error popup, contractValues is not set in
                // buildDynamicMetadata because _contractid does not exist in request.
                $this->setContractValues($contractId);
            }

            // Need to call this in case derived fields were removed
            $this->addDerivedFields($obj);
        }
        $isEvergreen = $this->isEvergreenContract();
        $isKit = $this->isKit($obj);
        $isKitComponent = $this->isKitComponent($obj);

        $entryPath = Request::$r->_entrypath;
        $this->isCdList = ($entryPath === 'cdlist');

        GetModulePreferences(Globals::$g->kCNid, $modulePrefs);

        $this->maybeAppendContractValues($obj);
        
        if (empty($obj['BILLINGOPTIONS'])) {
            if ($isKit) {
                $obj['BILLINGOPTIONS'] = ContractBillingOptions::INCLUDE_WITH_EVERY_INVOICE;
            } elseif ($isKitComponent) {
                $obj['BILLINGOPTIONS'] = '';
            } else {
                $obj['BILLINGOPTIONS'] = ContractBillingOptions::USE_BILLING_TEMPLATE;
            }
        }
        
        if (empty($obj['BILLINGOPTIONS'])) {
            $obj['BILLINGOPTIONS'] = ContractBillingOptions::USE_BILLING_TEMPLATE;
        }
        
        // show hide field for saas metrics integration
        $obj['ENABLE_CONTRACTS'] = false;
        if (!$isEvergreen && SaaSMetricsUtil::isSaaSMetricsEnabled() && SaaSMetricsUtil::isContractIntegrationEnabled()) {
            $view->findAndSetProperty([ 'path' => 'SAASCHANGETYPEKEY' ], ['required' => false, 'hidden' => false ]);
            $view->findAndSetProperty([ 'path' => 'SAASCHANGETYPEID' ], [ 'hidden' => false ]);
            $obj['ENABLE_CONTRACTS'] = true;
        }

        $verb = $this->getVerb();
        if ( $this->kShowNewState == $state ) {
            $view->findAndSetProperty([ 'path' => 'CONTRACTID' ], [ 'required' => true, 'hidden' => false ]);

            $contractHeader = [];
            $view->findComponents([ 'id' => 'header' ], EditorComponentFactory::TYPE_SECTION, $contractHeader);
            if ( $contractHeader[0] ) {
                $contractHeader[0]->setProperty('hidden', true);
            }

            $view->findAndSetProperty([ 'path' => 'STATE' ], [ 'hidden' => true ], EditorComponentFactory::TYPE_FIELD);

            $expDetail = [];
            $view->findComponents([ 'id' => 'expSch' ], EditorComponentFactory::TYPE_SECTION, $expDetail);
            if ( $expDetail[0] ) {
                $expDetail[0]->setProperty('hidden', true);
            }

            $this->defaultDimensionValues($obj);
        } else {
            $view->findAndSetProperty([ 'path' => 'CONTRACTID' ], [ 'readonly' => true, 'hidden' => false ]);
            $this->setAllTotalAmount($obj);

            if($this->contractValues['TERMTYPE'] == 'Evergreen') {
                $view->findAndSetProperty(['path' => 'RENEWAL_TRIGGER_DATE'], ['readonly' => true, 'hidden' => false]);
                $view->findAndSetProperty(['path' => 'CN_LINE_RENEWAL_DATE'], ['readonly' => true, 'hidden' => false]);
            }
        }

        $contractKey = $this->contractValues['RECORDNO'];

        $obj['CONTRACTKEY'] = $contractKey;

        $advBillOptions = ContractUtil::getAdvBillOptions($contractKey, $this->contractValues);

        if ( $verb == "create" || $verb == "edit" ) {
            if ( $contractKey !== null ) {
                $glPostingDate = ContractUtil::getCompanyOpenDate($contractKey);
                if ( ! BaseContractGLReclassEngine::isACPFlow($glPostingDate) ) {
                    // Show this text only for non ACP contracts.
                    $glPostingDateHelp = ContractUtil::GTP(
                        $this->textMap,'IA.THE_GL_POSTING_DATE_MUST_BE_WITHIN_AN_OPEN_PERIOD',
                        [ 'GL_POSTING_DATE' => $glPostingDate ]
                    );
                    $view->findAndSetProperty(['path' => 'GLPOSTINGDATEHELP'],
                                              ['hidden' => false, 'default' => $glPostingDateHelp],
                                              EditorComponentFactory::TYPE_FIELD);
                }
            }
        }
        $isRevRecOnInvoice = ContractDetailManager::isRevRecOnInvoice($obj);
        if ( $verb == "view" || $verb == "edit" ) {
            if ( $obj['CONTRACTID'] != '' ) {
                $contractname = $this->contractValues['NAME'] ?? '';
                $placeHolders = [
                    'LINENO' => $obj['LINENO'],
                    'CONTRACT_ID' => $obj['CONTRACTID'],
                    'CONTRACT_NAME' => $contractname
                ];
                if (!empty($obj['PERIOD'])) {
                    $tokenId = 'IA.CONTRACT_LINE_NUM_PERIOD_NUM_OF_CONTRACT_ID_NAME';
                    $placeHolders['PERIOD'] = $obj['PERIOD'];
                } else {
                    $tokenId = 'IA.CONTRACT_LINE_NUM_OF_CONTRACT_ID_NAME';
                }
                $this->setTitleEncoded(ContractUtil::GTP($this->textMap, $tokenId, $placeHolders));
            }

            if ( $obj['ITEMID'] && $obj['ITEMNAME'] ) {
                $obj['ITEMID'] = $obj['ITEMID'] . '--' . $obj['ITEMNAME'];
            }

            $checkForJ1 = false;
            $checkForJ2 = false;

            // Hide schedules for kits
            if ($isKit) {
                unset($obj['REVENUESCHEDULEKEY']);
                unset($obj['REVENUE2SCHEDULEKEY']);
            } elseif ($isKitComponent) {
                unset($obj['BILLINGSCHEDULEKEY']);
            }

            // Revenue schedule link
            if (!empty($obj['REVENUESCHEDULEKEY'])) {
                $obj['REVENUESCHEDULELINK'] = !$isRevRecOnInvoice ? $this->constructEntityLink(
                    'cn/lists/contractrevenueschedule/view',
                    $obj['REVENUESCHEDULEKEY'],
                    'contractrevenueschedule',
                    'IA.VIEW_SCHEDULE_1'
                ) : '--';
            } else {
                $checkForJ1 = true;
            }

            // Revenue 2 schedule link
            if (!empty($obj['REVENUE2SCHEDULEKEY'])) {
                $obj['REVENUE2SCHEDULELINK'] = !$isRevRecOnInvoice ? $this->constructEntityLink(
                    'cn/lists/contractrevenue2schedule/view',
                    $obj['REVENUE2SCHEDULEKEY'],
                    'contractrevenue2schedule',
                    'IA.VIEW_SCHEDULE_2'
                ) : '--';
            } else {
                $checkForJ2 = true;
            }

            // Billing schedule link
            if (!empty($obj['BILLINGSCHEDULEKEY'])) {
                $obj['BILLINGSCHEDULELINK'] = $this->constructEntityLink(
                    'cn/lists/contractbillingschedule/view',
                    $obj['BILLINGSCHEDULEKEY'],
                    'contractbillingschedule',
                    'IA.VIEW_SCHEDULE'
                );

                if (ContractUtil::isPreviewBillingScheduleEnabled()) {
                    // save the original link so can use to toggle between preview link and saved billing schedule link
                    $obj['ORIG_BILLINGSCHEDULELINK'] = $obj['BILLINGSCHEDULELINK'];
                }
            }

            // Expense schedule links
            foreach ($obj['CONTRACTDETAIL_EXPENSEDETAIL'] as &$objExpenseDetail) {
                if (!empty($objExpenseDetail['LINENO'])) {
                    // Expense schedule line link
                    $lineNoLink = ContractEditor::constructLineNoLinkForContractExpense(
                        $objExpenseDetail['LINENO'],
                        $objExpenseDetail['RECORDNO'],
                        null,
                        $obj['RECORDNO'],
                        $obj['CONTRACTID']
                    );
                    $objExpenseDetail['LINENOLINK'] = $lineNoLink;
                }

                // Expense schedule link
                if (!empty($objExpenseDetail['SCHEDULEKEY'])) {
                    $objExpenseDetail['EXPENSESCHEDULELINK'] = $this->constructEntityLink(
                        'cn/lists/contractexpenseschedule/view',
                        $objExpenseDetail['SCHEDULEKEY'],
                        'contractdetailexpenseschedule',
                        'IA.VIEW_SCHEDULE_1'
                    );
                }

                // Expense 2 schedule link
                if (!empty($objExpenseDetail['SCHEDULE2KEY'])) {
                    $objExpenseDetail['EXPENSE2SCHEDULELINK'] = $this->constructEntityLink(
                        'cn/lists/contractexpense2schedule/view',
                        $objExpenseDetail['SCHEDULE2KEY'],
                        'contractdetailexpense2schedule',
                        'IA.VIEW_SCHEDULE_2'
                    );
                }
            }

            if ( $isEvergreen && ContractUtil::isEvergreenQuantityBasedBillingEnabled() ) {
                $view->findAndSetProperty(['path' => 'RECURUSAGEDATE'], ['hidden' => true],
                                          EditorComponentFactory::TYPE_GRID_COLUMN);
            }
    
            // Contract usage document links
            foreach ($obj['CONTRACTDETAIL_CONTRACTUSAGE'] as &$objContractUsage) {
                if (!empty($objContractUsage['DOCID'])) {
                    $encodedDocId = urlencode(str_replace('#', '%23', $objContractUsage['DOCID']));
                    $objContractUsage['DOCIDLINK'] = $this->constructEntityLink(
                        'so/lists/sodocument/view',
                        $encodedDocId,
                        'sodocument',
                        $objContractUsage['DOCID']
                    );
                }
            }

            // Contract recurrence links
            foreach ($obj['RECURRENCE'] as &$recurrence) {
                if (!empty($recurrence['RECORDNO'])) {
                    $recurrence['PERIODLINK'] = ContractEditor::constructLineNoLinkForContractDetail(
                        $recurrence['PERIOD'],
                        $recurrence['RECORDNO'],
                        $contractKey,
                        $this->contractValues['CONTRACTID']
                    );
                }
            }

            if ($isKit) {
                $view->findComponents(['path' => 'KITCOMPONENTLINES'], EditorComponentFactory::TYPE_GRID, $grids);
                $grid = $grids[0] ?? null;

                if ($verb == "edit") {
                    // For edit mode, enable the kit component tab grid revenue template and delivery status columns
                    $grid->findAndSetProperty(
                        ['path' => 'DELIVERYSTATUS'],
                        ['readonly' => true],
                        EditorComponentFactory::TYPE_GRID_COLUMN
                    );
    
                    // If either revenue templates are non-editable then make allocation percent read-only also
                    $nonEditableFields = $this->getEntityMgr()->getNonEditableFields($obj);
                    if (in_array('REVENUETEMPLATENAME', $nonEditableFields) || in_array('REVENUE2TEMPLATENAME', $nonEditableFields)) {
                        $grid->findAndSetProperty(
                            ['path' => 'REVPERCENT'],
                            ['readonly' => true],
                            EditorComponentFactory::TYPE_GRID_COLUMN
                        );
                    }
                }

                // Show the kit component grid revenue link columns
                $grid->findAndSetProperty(
                    ['path' => 'REVENUESCHEDULELINK'],
                    ['hidden' => !ContractUtil::isShowJournal1()],
                    EditorComponentFactory::TYPE_GRID_COLUMN
                );

                $grid->findAndSetProperty(
                    ['path' => 'REVENUE2SCHEDULELINK'],
                    ['hidden' => !ContractUtil::isShowJournal2()],
                    EditorComponentFactory::TYPE_GRID_COLUMN
                );

                // Populate the kit component tab grid links
                foreach ($obj['KITCOMPONENTLINES'] as &$componentDetail) {
                    // Kit component line link
                    if (!empty($componentDetail['LINENO'])) {
                        $lineNoLink = ContractEditor::constructLineNoLinkForContractDetail(
                            $componentDetail['LINENO'],
                            $componentDetail['RECORDNO'],
                            $obj['CONTRACTKEY'],
                            $obj['CONTRACTID']
                        );
                        $componentDetail['LINENOLINK'] = $lineNoLink;
                    }

                    // Kit component revenue link
                    if (!empty($componentDetail['REVENUESCHEDULEKEY'])) {
                        $componentDetail['REVENUESCHEDULELINK'] = $this->constructEntityLink(
                            'cn/lists/contractrevenueschedule/view',
                            $componentDetail['REVENUESCHEDULEKEY'],
                            'contractrevenueschedule',
                            'IA.VIEW_SCHEDULE_1'
                        );
                    }

                    // Kit component revenue 2 link
                    if (!empty($componentDetail['REVENUE2SCHEDULEKEY'])) {
                        $componentDetail['REVENUE2SCHEDULELINK'] = $this->constructEntityLink(
                            'cn/lists/contractrevenue2schedule/view',
                            $componentDetail['REVENUE2SCHEDULEKEY'],
                            'contractrevenue2schedule',
                            'IA.VIEW_SCHEDULE_2'
                        );
                    }
                }
            }

            $view->findAndSetProperty(['path' => 'BALANCES_ASOFDATE'], ['readonly' => false], EditorComponentFactory::TYPE_FIELD);
            $view->findAndSetProperty(['path' => 'BALANCES_SCOPE'], ['readonly' => false,
                'default' => ($isEvergreen ? 'In progress period' : 'All periods')], EditorComponentFactory::TYPE_FIELD);

            //support for saas change type
            if (isset($obj['SAASCHANGETYPEID']) && isset($obj['SAASCHANGETYPENAME'])) {
                $obj['SAASCHANGETYPEID'] = $obj['SAASCHANGETYPEID'] . '--' . $obj['SAASCHANGETYPENAME'];
            }
        } else {
            // when contract header has contract id make the contarct id field read only
            if (!empty($obj['CONTRACTID']) && !$this->isCdList && $action != 'copy') {
                $view->findAndSetProperty([ 'path' => 'CONTRACTID' ], [ 'readonly' => true, 'hidden' => false ]);
            }

            if ( isset($this->contractValues['BEGINDATE'])
                 && ( isset($this->contractValues['ENDDATE']) || $isEvergreen )
            ) {
                if (Request::$r->_action != $this->kCopyForNewAction) {
                    if ( $isEvergreen ) {
                        $beginDate = GetCurrentDate();
                        $endDate = ContractUtil::getEvergreenEndDate(
                            $this->contractValues['BEGINDATE'],
                            $beginDate,
                            ContractUtil::BILLING_FREQUENCY_MONTHLY
                        );
                    } else {
                        $beginDate = $this->contractValues['BEGINDATE'];
                        $endDate = $this->contractValues['ENDDATE'];
                    }

                    $startDateFields =
                        [ 'BEGINDATE', 'REVENUESTARTDATE', 'REVENUE2STARTDATE', 'STARTDATE', 'START2DATE',
                          'EXCH_RATE_DATE' ];
                    $endDateFields = [ 'ENDDATE', 'REVENUEENDDATE', 'REVENUE2ENDDATE', 'END2DATE' ];

                    if ( $advBillOptions && $beginDate && $endDate ) {
                        [ $obj['BILLINGSTARTDATE'], $obj['BILLINGENDDATE'] ] =
                            ContractUtil::getAdvanceBillDates($this->contractValues['RECORDNO'], $beginDate, $endDate,
                                                              $this->contractValues);
                    } else {
                        $startDateFields[] = 'BILLINGSTARTDATE';
                        $endDateFields[] = 'BILLINGENDDATE';
                    }

                    foreach ( $startDateFields as $f ) {
                        $obj[$f] = $beginDate;
                    }
                    foreach ( $endDateFields as $f ) {
                        $obj[$f] = $endDate;
                    }
                }

                if ( isset($obj['BEGINDATE']) ) {
                    $glPostingDate = ContractUtil::getGLPostingDate($obj['BEGINDATE'],
                                                                    ContractUtil::getCompanyOpenDate($contractKey),
                                                                    $contractKey);

                    $obj['GLPOSTINGDATE'] = $glPostingDate;
                    if ( $this->isMCPEnabled ) {
                        $exchRateTypeId = $this->contractValues['EXCH_RATE_TYPE_ID'];
                        ContractDetailManager::setExchRateDate($obj, $glPostingDate, $exchRateTypeId);
                    }
                }
            }
            $checkForJ1 = true;
            $checkForJ2 = true;
        }

        if (isset($this->contractValues) && RenewalContractHandler::isTermedToEvergreenRenewal($this->contractValues)) {
            $obj['EVERGREENMACROREADONLY'] = $this->contractValues['RENEWALMACRO'];
            $view->findAndSetProperty([ 'id' => 'renewalTermSection' ], [ 'hidden' => true ],
                                      EditorComponentFactory::TYPE_SECTION);
            $view->findAndSetProperty([ 'id' => 'renewToEvergreenRenewalSection' ], [ 'hidden' => false ],
                                      EditorComponentFactory::TYPE_SECTION);
            if ($obj['RECORDNO'] !== '') {
                if ($obj['STATE'] === ContractDetailState::STATE_DRAFT) {
                    $obj['RENEWTOEVERGREENMSGREADONLY'] = GT(
                        $this->textMap,'IA.DRAFT_CONTRACT_LINES_ARE_INELIGIBLE_FOR_RENEWAL');
                    $view->findAndSetProperty([ 'path' => 'RENEWTOEVERGREENMSGREADONLY' ], [ 'hidden' => false ]);
                } else if ( ! ContractDetailManager::isEligibleToRenewToEvergreen($obj) ) {
                    $obj['RENEWTOEVERGREENMSGREADONLY'] = GT(
                        $this->textMap,'IA.CONTRACT_LINE_INELIGIBLE_TO_BE_RENEWED_TO_EVERGREEN');
                    $view->findAndSetProperty([ 'path' => 'RENEWTOEVERGREENMSGREADONLY' ], [ 'hidden' => false ]);
                }
            }
        }

        if ( ( $checkForJ1 && ContractUtil::isDisabledJournal1($modulePrefs) )
             || ContractUtil::isHideJournal1($modulePrefs) ) {
            $view->findAndSetProperty([ 'id' => 'revSchJ1' ], [ 'hidden' => true ],
                                      EditorComponentFactory::TYPE_SUBSECTION);
        }
        if ( ( $checkForJ2 && ContractUtil::isDisabledJournal2($modulePrefs) )
             || ContractUtil::isHideJournal2($modulePrefs) ) {
            $view->findAndSetProperty([ 'id' => 'revSchJ2' ], [ 'hidden' => true ],
                                      EditorComponentFactory::TYPE_SUBSECTION);
        }

        if ( $advBillOptions ) {
            $obj['CNADVBILL']['ADVBILLBY'] = $advBillOptions['ADVBILLBY'];
            $obj['CNADVBILL']['ADVBILLBYTYPE'] = $advBillOptions['ADVBILLBYTYPE'];
        }

        $state = $this->getState();

        if ( $state == Editor_ShowNewState ) {
            // if Bill to is not already populated, check if there is an associated contract to get the Bill to from
            if (!$obj['BILLTOCONTACTNAME']) {
                if ( isset($obj['CONTRACT']['BILLTOCONTACTNAME']) ) {
                    $obj['BILLTOCONTACTNAME'] = $obj['CONTRACT']['BILLTOCONTACTNAME'];
                }

                // set the bill to source to "Contract value" regardless if there is an associated contract or not
                $obj['BILLTOSOURCE'] = ContractDetailManager::CONTACT_SOURCE_CONTRACT_VALUE;
            }

            // if Ship to is not already populated, check if there is an associated contract to get the Ship to from
            if ( ! $obj['SHIPTOCONTACTNAME'] ) {
                if ( isset($obj['CONTRACT']['SHIPTOCONTACTNAME']) ) {
                    $obj['SHIPTOCONTACTNAME'] = $obj['CONTRACT']['SHIPTOCONTACTNAME'];
                }

                // set the ship to source to "Contract value" regardless if there is an associated contract or not
                $obj['SHIPTOSOURCE'] = ContractDetailManager::CONTACT_SOURCE_CONTRACT_VALUE;
            }

            if ($action === $this->kFetchForNewAction) {
                if ($isEvergreen) {
                    $obj['RECURRING'] = $obj['RENEWAL'] = 'true';
                } else if (isset($this->contractValues['RENEWAL'])) {
                    $obj['RENEWAL'] = $this->contractValues['RENEWAL'];     // Set default to the same as contract's RENEWAL flag
                }
            }
        }

        if ($obj['BILLTOSOURCE'] == ContractDetailManager::CONTACT_SOURCE_CONTRACT_VALUE) {
            $view->findAndSetProperty([ 'path' => 'BILLTOCONTACTNAME' ], [ 'readonly' => true ]);
        }

        if ( $obj['SHIPTOSOURCE'] == ContractDetailManager::CONTACT_SOURCE_CONTRACT_VALUE ) {
            $view->findAndSetProperty([ 'path' => 'SHIPTOCONTACTNAME' ], [ 'readonly' => true ]);
        }

        if (ContractUtil::isShipToLineItemEnabled() && ContractUtil::isBillToLineItemEnabled()) {
            $contactsColumnCount = 2;
        } else {
            $contactsColumnCount = 1;
        }

        // enforce addig department, if feature is enabled and the restriction checkbox is on
        if ( hasHiddenDepartment() ) {
            $view->findAndSetProperty(
                [ 'path' => 'DEPARTMENTID' ],
                [ 'required' => true ]
            );
        }

        $view->findAndSetProperty(
            [ 'id' => 'contactsSubsection' ], [ 'columnCount' => $contactsColumnCount ],
            EditorComponentFactory::TYPE_SUBSECTION
        );
        $view->findAndSetProperty([ 'path' => 'BILLTOSOURCE' ],
                                  [ 'hidden' => !ContractUtil::isBillToLineItemEnabled() ]);
        $view->findAndSetProperty([ 'path' => 'BILLTOCONTACTNAME' ],
                                  [ 'hidden' => !ContractUtil::isBillToLineItemEnabled() ]);
        $view->findAndSetProperty([ 'path' => 'SHIPTOSOURCE' ],
                                  [ 'hidden' => ! ContractUtil::isShipToLineItemEnabled() ]);
        $view->findAndSetProperty([ 'path' => 'SHIPTOCONTACTNAME' ],
                                  [ 'hidden' => ! ContractUtil::isShipToLineItemEnabled() ]);

        if ( $this->isMCPEnabled ) {
            $view->findAndSetProperty([ 'path' => 'EXCHRATETYPE' ], [ 'hidden' => false ]);
            $view->findAndSetProperty([ 'path' => 'EXCH_RATE_DATE' ], [ 'hidden' => false ]);
            $view->findAndSetProperty([ 'path' => 'EXCHANGE_RATE' ], [ 'hidden' => false ]);
            $view->findAndSetProperty([ 'path' => 'BASEFLATAMOUNT' ], [ 'hidden' => false ]);
            $this->findAndSetExchRate($obj);
        }

        if ( IsMultiEntityCompany() ) {
            $view->findAndSetProperty([ 'path' => 'LOCATIONID' ], [ 'required' => true ]);
        }

        if ( $verb == "view" || $verb == "edit" ) {
            $balances = new ContractDetailEditorBalances(
                $this->isMCPEnabled,
                $this->isStandardRevRecEnabled,
                $this->isExpenseEnabled
            );
            $balances->setBalances($this->getView(), $obj);
            //            $this->setHistory($obj);
            $this->setMRRHistory($obj);

            // contact configuration for refresh
            $contactTypes = [];
            // BILLTO context
            if (isset($obj['BILLTOSOURCE']) &&
                $obj['BILLTOSOURCE'] == ContractDetailManager::CONTACT_SOURCE_USER_SPECIFIED ) {
                $contactTypes[] = 'BILLTO';
            }
            // SHIPTO context
            if ( isset($obj['SHIPTOSOURCE'])
                 && $obj['SHIPTOSOURCE'] == ContractDetailManager::CONTACT_SOURCE_USER_SPECIFIED ) {
                $contactTypes[] = 'SHIPTO';
            }

            if ( ! empty($contactTypes) ) {
                $this->configureContactsForRefresh($obj, $contactTypes);
            }

            if ($verb == 'edit') {

                $isBillMethodTMOrProjMaterials =
                    ContractBillingMethodTypes::isProjectTMOrProjectMaterials($obj['BILLINGMETHOD']);

                if ($obj['BILLTOSOURCE'] == ContractDetailManager::CONTACT_SOURCE_CONTRACT_VALUE
                    && $isBillMethodTMOrProjMaterials) {
                    $view->findAndSetProperty([ 'path' => 'BILLTOSOURCE' ], [ 'disabled' => true ]);
                }

                if ($obj['SHIPTOSOURCE'] == ContractDetailManager::CONTACT_SOURCE_CONTRACT_VALUE
                    && $isBillMethodTMOrProjMaterials) {
                    $view->findAndSetProperty([ 'path' => 'SHIPTOSOURCE' ], [ 'disabled' => true ]);
                }
            }
        }

        // hide revenue recognition related fields if contract revrec is not subscribed...
        if ( ! $this->isStandardRevRecEnabled && ! $this->isAdvancedRevRecEnabled ) {
            // hide revenue section/grid inside window shade for contractdetail
            $revSch = [];
            $view->findComponents([ 'id' => 'revSch' ], EditorComponentFactory::TYPE_SECTION, $revSch);
            if ( $revSch[0] ) {
                $revSch[0]->setProperty('hidden', true);
            }
        }

        // hide delivery status if fulfillment not enabled
        if ( ContractUtil::isFulfillmentEnabled() ) {
            $view->findAndSetProperty([ 'path' => 'DELIVERYSTATUS' ], [ 'hidden' => false ]);
            $view->findAndSetProperty([ 'path' => 'DEFERRALSTATUS' ], [ 'hidden' => false ]);
            $view->findAndSetProperty([ 'path' => 'DELIVERYDATE' ], [ 'hidden' => false ]);
        }

        if ( (! $this->isExpenseEnabled) || $isEvergreen ) {
            // hide expense section/grid inside window shade for contractdetail
            $expSch = [];
            $view->findComponents([ 'id' => 'expSch' ], EditorComponentFactory::TYPE_SECTION, $expSch);
            if ( $expSch[0] ) {
                $expSch[0]->setProperty('hidden', true);
            }

            $expAccts = [];
            $view->findComponents([ 'id' => 'expenseAccountsSection' ], EditorComponentFactory::TYPE_SECTION,
                                  $expAccts);
            if ( $expAccts[0] ) {
                $expAccts[0]->setProperty('hidden', true);
            }

            if ($this->isExpenseEnabled && $isEvergreen) {
                $view->findAndSetProperty([ 'id' => 'contractexpensepage' ], [ 'hidden' => true ], EditorComponentFactory::TYPE_TAB);
            }
        }

        if ($isEvergreen) {
            self::findAndSetMetadata($params, [ 'path' => 'DBBACCOUNTS' ], [ 'hidden' => true ]);
        }

        if ( IsMultiEntityCompany() ) {
            $cnMgr = Globals::$g->gManagerFactory->getManager('contract');
            $restrictedLocs = $cnMgr->getContractRestrictedLocationRecs($this->contractValues['LOCATIONKEY']);
            $locField = [];
            $view->findComponents(
                [
                    'path' => 'LOCATIONID',
                ], EditorComponentFactory::TYPE_FIELD, $locField
            );

            if ( $locField ) {
                $locField[0]->setFilterRestrict(
                    [
                        [ 'value'     => $restrictedLocs,
                          'pickField' => 'RECORDNO',
                          'operand'   => 'IN' ],
                    ]
                );
            }
        }

        // For making only custom fields editable for cancelled contract lines
        if ($obj['STATE'] == ContractDetailState::STATE_CANCELLED && $verb == $this->kFetchForEditAction) {
            $this->setEditableCustomFields($obj, $view);
        }


        if ( $obj['STATE'] == 'Cancelled' || ( $isEvergreen && !empty($obj['CANCELDATE']) ) ) {
            $view->findAndSetProperty([ 'path' => 'CANCELDATE' ], [ 'hidden' => false ]);

            // Uncancel confirmation pop-up window text values.

            $uncancelCnIdTxt = ContractUtil::GTP(
                $this->textMap, 'IA.UNCANCEL_CONTRACT_LINE_NUM_OF_CONTRACT_ID',
                [ 'LINENO' => $obj['LINENO'], 'CONTRACT_ID' => $obj['CONTRACTID'] ]
            );

            $view->findAndSetProperty(
                [ 'path' => 'UNCANCEL_CONTRACT_LINENO_TXT' ],
                [ 'default' => $uncancelCnIdTxt, 'hidden' => false ],
                EditorComponentFactory::TYPE_FIELD
            );
        }

        if ($isEvergreen) {
            $view->findAndSetProperty([ 'path' => 'INCLUDEUSAGEOPTION' ], [ 'hidden' => true ]);
            $view->findAndSetProperty([ 'path' => 'EXPENSEOPTION' ], [ 'hidden' => true ]);
            $view->findAndSetProperty([ 'path' => 'AUTOGENPOCENTRIES' ], [ 'hidden' => true ]);
        }

        if ( ContractProjectLinkHandler::isContractProjectLinkFeatureEnabled() === true ) {
            $taskIsEnabledAsDimensionInGL = IADimensions::IsDimensionEnabled('gl', 'task');
            if ( ! $taskIsEnabledAsDimensionInGL ) {
                $view->findAndSetProperty([ 'path' => 'TASKID' ], [ 'hidden' => false ]);
            }
        }
        // else if the isContractProjectLinkFeatureEnabled is false, then we don't need to unhide the TASKID field on the form.
        // The iadimensions class will take care of showing/hiding the TASKID field along with all other dimension fields.
        // If Task is enabled as a dimension in GL, then the TASKID field will be shown.

        $this->initializeBillingMethod();

        $this->configureUsageTabVisibility($obj);

        $this->configureRevenueTemplateDependants($obj);

        // Populate DBBCATEGORY dropdown
        if ( IsInstalled(Globals::$g->kDBBid) && !$isEvergreen ) {
            $hidden = false;
            $disabled = false;
            if (!isArrayValueTrue($obj, 'MRR')) {
                $hidden = true;
            }
            if ( ! $hidden && isArrayValueProvided($obj, 'RENEWEDCONTRACTDETAILKEY') ) {
                $validLabels = [
                    ' ',     // set to a single space to avoid becoming INVALID_TOKEN in i18n.js
                    BaseContractMRRLifeCycleEngine::DBB_CATEGORY_RENEWALUPGRADE_LABEL,
                    BaseContractMRRLifeCycleEngine::DBB_CATEGORY_RENEWALDOWNGRADE_LABEL,
                ];
                $validValues = [
                    '',
                    BaseContractMRRLifeCycleEngine::DBB_CATEGORY_RENEWALUPGRADE,
                    BaseContractMRRLifeCycleEngine::DBB_CATEGORY_RENEWALDOWNGRADE,
                ];
                $validIValues = [
                    '',
                    BaseContractMRRLifeCycleEngine::DBB_CATEGORY_RENEWALUPGRADE_CODE,
                    BaseContractMRRLifeCycleEngine::DBB_CATEGORY_RENEWALDOWNGRADE_CODE,
                ];

                $disabled = true;
            } else {
                $validLabels = [
                    ' ',    // set to a single space to avoid becoming INVALID_TOKEN in i18n.js
                    BaseContractMRRLifeCycleEngine::DBB_CATEGORY_NEWMRR_LABEL,
                    BaseContractMRRLifeCycleEngine::DBB_CATEGORY_ADDONMRR_LABEL,
                    BaseContractMRRLifeCycleEngine::DBB_CATEGORY_RENEWALUPGRADE_LABEL,
                ];
                $validValues = [
                    '',
                    BaseContractMRRLifeCycleEngine::DBB_CATEGORY_NEWMRR,
                    BaseContractMRRLifeCycleEngine::DBB_CATEGORY_ADDONMRR,
                    BaseContractMRRLifeCycleEngine::DBB_CATEGORY_RENEWALUPGRADE,
                ];
                $validIValues = [
                    '',
                    BaseContractMRRLifeCycleEngine::DBB_CATEGORY_NEWMRR_CODE,
                    BaseContractMRRLifeCycleEngine::DBB_CATEGORY_ADDONMRR_CODE,
                    BaseContractMRRLifeCycleEngine::DBB_CATEGORY_RENEWALUPGRADE_CODE,
                ];
            }

            if ( isArrayValueProvided($obj, 'DBBCATEGORY') && ! in_array($obj['DBBCATEGORY'], $validValues) ) {
                $validValues[] = $obj['DBBCATEGORY'];
                $validIValues[] = BaseContractMRRLifeCycleEngine::getCategoryCode($obj['DBBCATEGORY']);
                $validLabels[] = BaseContractMRRLifeCycleEngine::getCategoryLabel($obj['DBBCATEGORY']);
            }

            $type = [
                'ptype'         => 'enum',
                'type'          => 'text',
                'validlabels'   => $validLabels,
                'validvalues'   => $validValues,
                '_validivalues' => $validIValues,
            ];
            $view->findAndSetProperty([ 'path' => 'DBBCATEGORY' ],
                                      [ 'type' => $type, 'hidden' => $hidden, 'disabled' => $disabled ]);
        }

        $revenueJournalsTitles = ContractUtil::getRevenueJournals($modulePrefs);
        $expenseJournalsTitles = ContractUtil::getExpenseJournals($modulePrefs);

        if ($isRevRecOnInvoice) {
            $view->findAndSetProperty([ 'path' => 'REVENUEJOURNALS' ], [ 'hidden' => true ]);
        } else {
            ContractUtil::overrideLabelsAndValues($view, 'REVENUEJOURNALS', $revenueJournalsTitles);
        }
        ContractUtil::overrideLabelsAndValues($view, 'EXPENSEJOURNALS', $expenseJournalsTitles);

        //        $this->initializeChangeControl($obj);

        $holdButtonState = [ 'enabled', ($isRevRecOnInvoice ? 'disabled' : 'enabled'), 'enabled' ];
        $resumeButtonState = [ 'disabled', 'disabled', 'disabled' ];

        // if a particular schedule is put on hold do not allow it to be set on hold again by disabling that schedule option
        if ( $obj['BILLINGHOLDDATE'] ) {
            $holdButtonState[0] = 'disabled';
            $resumeButtonState[0] = 'enabled';
        }

        if ($obj['REVENUEHOLDDATE']) {
            $holdButtonState[1] = 'disabled';
            $resumeButtonState[1] = 'enabled';
        }

        if ( $obj['EXPENSEHOLDDATE'] ) {
            $holdButtonState[2] = 'disabled';
            $resumeButtonState[2] = 'enabled';
        }

        $view->findAndSetProperty([ 'path' => 'SCHEDULESTOHOLD' ], [ 'buttonstate' => $holdButtonState ]);
        $view->findAndSetProperty([ 'path' => 'SCHEDULESTORESUME' ], [ 'buttonstate' => $resumeButtonState ]);

        /* modifying billing method based on Track timesheet flag */
        if ( ContractUtil::isContractTimesheetTrackingEnable() ) {
            // billing method only have project time and Project Materials
            $billingMethodValidlabels = [ 'IA.PROJECT_TIME_AND_MATERIALS' ];
            $billingMethodValidvalues = [ 'Project T&M' ];
            $billingMethod_validivalues = [ 'P' ];
        } else {
            $billingMethodValidlabels = [
                ContractBillingMethodTypes::CONTRACT_BILLING_METHOD_PROJECT_TIME_L,
                ContractBillingMethodTypes::CONTRACT_BILLING_METHOD_PROJECT_MATERIALS_L,
            ];
            
            $billingMethodValidvalues = [
                ContractBillingMethodTypes::CONTRACT_BILLING_METHOD_PROJECT_TIME,
                ContractBillingMethodTypes::CONTRACT_BILLING_METHOD_PROJECT_MATERIALS,
            ];
            
            $billingMethod_validivalues = [
                ContractBillingMethodTypes::CONTRACT_BILLING_METHOD_PROJECT_TIME_V,
                ContractBillingMethodTypes::CONTRACT_BILLING_METHOD_PROJECT_MATERIALS_V,
            ];
        }
        $this->findAndRemoveEnumValue([ 'path' => 'BILLINGMETHOD' ], [ 'type' => [
            'validlabels'   => $billingMethodValidlabels,
            'validvalues'   => $billingMethodValidvalues,
            '_validivalues' => $billingMethod_validivalues,
        ] ]);

        if ( $obj['PRORATEBILLINGPERIOD'] === 'true' ) {
            $view->findAndSetProperty(
                [ 'path' => 'FLATAMOUNT_MULTIPLIER' ],
                [ 'default' => $obj['FLATAMOUNT_MULTIPLIER'], 'hidden' => false ],
                EditorComponentFactory::TYPE_FIELD
            );

            $view->findAndSetProperty(
                [ 'path' => 'PRO_BILL_TOTALFLATAMOUNT' ],
                [ 'default' => $obj['TOTALFLATAMOUNT'], 'hidden' => false ],
                EditorComponentFactory::TYPE_FIELD
            );

            $view->findAndSetProperty(
                [ 'path' => 'PRO_BILL_TOTALBASEFLATAMOUNT' ],
                [ 'default' => $obj['TOTALBASEFLATAMOUNT'], 'hidden' => false ],
                EditorComponentFactory::TYPE_FIELD
            );
        }

        if ( $state === Editor_ShowNewState || $state === Editor_ShowEditState ) {
            if ( ! isArrayValueProvided($obj, 'BILLINGFREQUENCY') ) {
                $obj['BILLINGFREQUENCY'] = $obj['CONTRACT']['BILLINGFREQUENCY'];
            }
        }

        if ( $obj['BILLINGMETHOD'] !== ContractBillingMethodTypes::CONTRACT_BILLING_METHOD_QTY_BASED ) {
            $view->findAndSetProperty([ 'path' => 'USAGEQTYRESETPERIOD' ], [ 'hidden' => true ]);
            $view->findAndSetProperty([ 'path' => 'USAGEQTYRECUR' ], [ 'hidden' => true ]);
        }

        if (ContractBillingMethodTypes::isCommittedQuantityType($obj['USAGELINETYPE'])) {
            // Disable this field for committed quantity usage billing.
            $view->findAndSetProperty(['path' => 'INCLUDEUSAGEOPTION'], ['disabled' => true]);
        }

        if (isArrayValueProvided($obj, 'USAGELINETYPE') && ContractBillingMethodTypes::isCommittedQuantityType($obj['USAGELINETYPE'])) {
            // Committed quantity billing.
            // Add committed quantity billing specific fields default values.

            $view->findAndSetProperty(
                [ 'path' => 'COMMITTEDUSAGEENDACTION' ],
                [ 'default' => ContractDetailHandler::END_ACTION_BILL_UNUSED ],
                EditorComponentFactory::TYPE_FIELD
            );

            $view->findAndSetProperty(
                [ 'path' => 'COMMITTEDUSAGEEXCESS' ],
                [ 'default' => ContractCommittedQuantityExcessPolicy::BILL_ON_EXCESS_USAGE ],
                EditorComponentFactory::TYPE_FIELD
            );
        }

        // preview billing schedule related
        if (ContractUtil::isPreviewBillingScheduleEnabled()) {

            $obj['PREVIEW_BILLINGSCHEDULELINK_DISABLED'] = '<a href=\'javascript:getPreviewBillingScheduleData()\' ' .
                                                           'style=\'pointer-events: none; opacity: 0.5;\'>' .
                                                           GT($this->textMap, 'IA.VIEW_SCHEDULE') . '</a>';

            $obj['PREVIEW_BILLINGSCHEDULELINK_ENABLED'] = '<a href=\'javascript:getPreviewBillingScheduleData()\' ' .
                                                          'style=\'pointer-events: auto; opacity: 1.0;\'>' .
                                                          GT($this->textMap, 'IA.VIEW_SCHEDULE') . '</a>';

            // if creating a new line or editing a line with no initial billing schedule (e.g. draft), initially
            // display the "View schedule" link in a "disabled" state
            if (($verb == "create" || ($verb == "edit" && !$obj['BILLINGSCHEDULEKEY'])) &&
                 ($obj['BILLINGMETHOD'] == ContractBillingMethodTypes::CONTRACT_BILLING_METHOD_FIXED_PRICE ||
                 ($obj['BILLINGMETHOD'] == ContractBillingMethodTypes::CONTRACT_BILLING_METHOD_QTY_BASED &&
                 $obj['USAGELINETYPE'] != ContractBillingMethodTypes::CONTRACT_QTY_BILLING_TYPE_COMMIITTED))) {
                $view->findAndSetProperty([ 'path' => 'BILLINGSCHEDULELINK' ], [ 'hidden' => false ]);
                $obj['BILLINGSCHEDULELINK'] = $obj['PREVIEW_BILLINGSCHEDULELINK_DISABLED'];
            }
        }

        $contractLockInfo = ContractProcessLockHandler::checkAllLocksForContractId($obj['CONTRACTID']);
        if ($contractLockInfo && $contractLockInfo->isLocked()) {
            $this->SetWarningMessage($contractLockInfo->getLockedMessage($obj['CONTRACTID']));
            $lockedMessage = $contractLockInfo->getLockedMessage($obj['CONTRACTID']);
            Globals::$g->gErr->AddIAWarning('CN-0018', __FILE__ . ':' . __LINE__, $lockedMessage,
                                                ['LOCKED_MESSAGE' => $lockedMessage]);
        }

        // For kits, under revenue we display a help message with a link to the Kit Components tab
        $link = '<a href="#" onclick="jq(\'a[href=&quot;#kitcomponents_form&quot;]\').tab(\'show\');">'
                . GT($this->textMap, 'IA.KIT_COMPONENTS') . '</a>';
        $obj['KITREVENUEHELP'] = ContractUtil::GTP($this->textMap, 'IA.KIT_REVENUE_NOTICE', ['KIT_COMPONENTS_LINK' => $link]);
    
        if ($verb == 'create' || $verb == 'edit') {
            // If this is an Evergreen contract and Kits on Evergreen is disabled, filter Kits from the ITEMID picker
            if (!ContractUtil::isKitsEvergreenEnabled() && $this->isEvergreenContract()) {
                $view->findComponents(['path' => 'ITEMID'], EditorComponentFactory::TYPE_FIELD, $itemField);
        
                if ($itemField) {
                    $itemField[0]->setFilterRestrict([['value' => 'K', 'pickField' => 'ITEMTYPE', 'operand' => '!=']]);
                }
            }
        }
    
        if ($verb == 'view' || $verb == 'edit') {
            if ($isKitComponent) {
                // Show fields
                $view->findAndSetProperty(['path' => 'LINETYPE'], ['hidden' => false], EditorComponentFactory::TYPE_FIELD);
                $view->findAndSetProperty(['path' => 'PRICECALCMEMO'], ['hidden' => false], EditorComponentFactory::TYPE_FIELD);

                // Hide fields
                $view->findAndSetProperty(['path' => 'TOTALFLATAMOUNT'], ['hidden' => true], EditorComponentFactory::TYPE_FIELD);
                $view->findAndSetProperty(['path' => 'EVGTOTALFLATAMOUNT'], ['hidden' => true], EditorComponentFactory::TYPE_FIELD);

                // Make all dimensions read-only for Kit Components
                $conObjHandler = new ContractObjHandler();
                $dimFields = $conObjHandler->getDimensionFields(false);
                $dimFields[] = 'TASKID';
                foreach ($dimFields as $dimField) {
                    $view->findAndSetProperty(['path' => $dimField], ['readonly' => true], EditorComponentFactory::TYPE_FIELD);
                }
    
                // Make all custom fields read-only for Kit Components
                $customFields = $this->entityMgr->GetCustomFields();
                foreach ($customFields as $customField) {
                    $view->findAndSetProperty(['path' => $customField->getFieldName()], ['readonly' => true], EditorComponentFactory::TYPE_FIELD);
                }
            }
            if($verb == "view"){
                $view->findAndSetProperty(['path' => 'REV_REC_ON_INVOICE'], ['readonly' => true], EditorComponentFactory::TYPE_FIELD);
            }
        }

        // hide checkbox when flag is enabled in Contract config
        if ( !ContractUtil::isRevRecOnInvoiceFlagEnabled()) {
            $view->findAndSetProperty([ 'path' => 'REV_REC_ON_INVOICE' ], [ 'hidden' => true ]);
        }

        return true;
    }

    /**
     * Sets custom fields editable for cancelled contract lines
     *
     * @param array $obj Contract Detail attributes
     * @param object $view Ui view manager to set properties
     */
    protected function setEditableCustomFields(&$obj, $view)
    {
        $customFields = $this->entityMgr->GetCustomFields();
        $customFieldNames = array_map(fn($field) => $field->getFieldName(), $customFields);
        $fieldsToDisable = [];

        foreach ($obj as $key => $value) {
            $isCustomField = in_array($key, $customFieldNames);
            if (!$isCustomField) {
                $fieldsToDisable[] = $key;
            }
        }

        $conObjHandler = new ContractObjHandler();
        $dimFields = $conObjHandler->getDimensionFields(false);

        foreach ($dimFields as $dimField) {
            $fieldsToDisable[] = $dimField;
        }

        if (!$this->getRenewalBillTmplNameEditAction()) {
            $fieldsToDisable[] = 'RENEWALBILLINGTEMPLATENAME';
        }

        // Disable all fields in the fieldsToDisable array
        foreach ($fieldsToDisable as $field) {
            $view->findAndSetProperty(['path' => $field], ['readonly' => true], EditorComponentFactory::TYPE_FIELD);
        }
    }

    /**
     * Checks if the Renewal Billing Template Name field is empty or not
     * for a renewal enabled cancelled contract line.
     *
     * @return bool True if all three conditions are met , false otherwise
     */
    public function getRenewalBillTmplNameEditAction()
    {
        $res = $this->getCnDetailData();
        return ($res[0]['STATE'] == ContractDetailState::STATE_CANCELLED && $res[0]['RENEWAL'] == 'true'
            && empty($res[0]['RENEWALBILLINGTEMPLATENAME']));
    }

    /**
     * @return bool
     * @throws IAException
     */
    protected function isEvergreenContract() : bool
    {
        return ContractManager::isEvergreenContract($this->contractValues);
    }

    protected function isKit(array $values) : bool
    {
        return ContractDetailManager::isKit($values);
    }

    protected function isKitComponent(array $values) : bool
    {
        return ContractDetailManager::isKitComponent($values);
    }

    /**
     * @return false
     */
    protected function getShowRepeatMsg()
    {
        return false;
    }

    /**
     * initialize BillingMethod field
     */
    function initializeBillingMethod()
    {
        $view = $this->getView();
        if ($this->isEvergreenContract())
        {
            $validlabels = [ 'IA.FIXED_PRICE' ];
            $validvalues = [ 'Fixed price' ];
            $validivalues = [ 'F' ];
            if ( ContractUtil::isEvergreenQuantityBasedBillingEnabled() ) {
                $validlabels[] = 'IA.QUANTITY_BASED';
                $validvalues[] = 'Quantity based';
                $validivalues[] = 'Q';
                $propVals = [];
            } else {
                $propVals = [ 'readonly' => true ];
            }
            $propVals['type'] = [
                'ptype'         => 'enum',
                'type'          => 'text',
                'validlabels'   => $validlabels,
                'validvalues'   => $validvalues,
                '_validivalues' => $validivalues,
            ];
            $view->findAndSetProperty([ 'path' => 'BILLINGMETHOD' ], $propVals);
        }
        else if ( ! $this->isUsageBillingEnabled )
        {
            $billingMethodFld = [];
            $view->findComponents([ 'path' => 'BILLINGMETHOD' ], EditorComponentFactory::TYPE_FIELD, $billingMethodFld);
            $billingType = $billingMethodFld[0]->getProperty('type');
            $key = array_search('Quantity based', $billingType['validvalues']);
            assert($key !== false);
            if ( $key !== false ) {
                unset($billingType['validlabels'][$key]);
                unset($billingType['validvalues'][$key]);
                unset($billingType['_validivalues'][$key]);

                $billingMethodFld[0]->setProperty([ 'type', 'validlabels' ], array_values($billingType['validlabels']));
                $billingMethodFld[0]->setProperty([ 'type', 'validvalues' ], array_values($billingType['validvalues']));
                $billingMethodFld[0]->setProperty([ 'type', '_validivalues' ],
                                                  array_values($billingType['_validivalues']));
            }
        }
    }

    /**
     * transformBizObjectToView
     *
     * @param array &$obj parameters
     *
     * @return bool  true on success and false on failure - make sure an error is raised in case of failure
     */
    protected function transformBizObjectToView(&$obj)
    {
        parent::transformBizObjectToView($obj);

        $action = $this->getEditorAction();

        if ( $action == 'copy' ) {//Unset values while duplicating
            $obj['STATE'] = ContractDetailState::STATE_INPROGRESS;
            unset($obj['CONTRACTDETAIL_CONTRACTUSAGE']);
            if ( isset($obj[ContractDetailManager::RENEWALPRICINGOVERRIDES]) ) {
                foreach ( $obj[ContractDetailManager::RENEWALPRICINGOVERRIDES] as &$overrides ) {
                    unset($overrides['RECORDNO']);
                }
            }
            unset($obj['CANCELDATE']);
            unset($obj['DELIVERYDATE']);
            unset($obj['RENEWEDORIGINALDETAILKEY']);
            $obj['EXCH_RATE_TYPE_ID'] = $obj['CONTRACT.EXCH_RATE_TYPE_ID'];
            /* @var ExchangeRateTypesManager $exchratetypeMgr */
            $exchratetypeMgr = Globals::$g->gManagerFactory->getManager('exchangeratetypes');
            $obj['EXCHRATETYPE'] = $exchratetypeMgr->GetExchangeRateTypeName($obj['EXCH_RATE_TYPE_ID']);
        }
        if ( isset($obj[ContractDetailManager::RENEWALPRICINGOVERRIDES]) ) {
            foreach ( $obj[ContractDetailManager::RENEWALPRICINGOVERRIDES] as &$overrides ) {
                unset($overrides[ContractDetailManager::RENEWALNUMBER]);
            }
        }

        return true;
    }

    /**
     * The visibility of the Usage TAB is specified as follows:
     * 1. Billing method = 'Quantity based'
     * 2. Billing method = 'Fixed price' AND
     *    any of two revenue template selections points to the one
     *    with Quantity based template method
     *
     * @param array $obj
     */
    private function configureUsageTabVisibility(&$obj)
    {
        $view = $this->getView();
        $hasQuantityBasedBilling = false;
        $cnDetailObj = new ContractDetailInfo();
        $cnDetailObj->setEmValues($obj);

        $usageTabVisible =
            isArrayValueProvided($obj, 'BILLINGMETHOD')
            && ContractBillingMethodTypes::CONTRACT_BILLING_METHOD_QTY_BASED === $obj['BILLINGMETHOD'];
        if ( $usageTabVisible === true ) {
            $hasQuantityBasedBilling = true;
        }

        if ( $hasQuantityBasedBilling === true ) {
            if ( $cnDetailObj->hasQuantityBasedRevRec() === true ) {
                //make total quantity readonly here
                $view->findAndSetProperty(
                    [ 'path' => 'REVENUETOTALQUANTITY' ], [ 'readonly' => true ], EditorComponentFactory::TYPE_FIELD);
            }
        } else {
            $nonEditableFields = $this->getEntityMgr()
                                      ->getNonEditableFields($obj);
            if ( ! in_array('REVENUETOTALQUANTITY', $nonEditableFields) ) {
                $view->findAndSetProperty(
                    [ 'path' => 'REVENUETOTALQUANTITY' ], [ 'readonly' => false ], EditorComponentFactory::TYPE_FIELD);
            }
        }
        if ( ! $usageTabVisible ) {
            $usageTabVisible =
                isArrayValueProvided($obj, 'BILLINGMETHOD')
                && ContractBillingMethodTypes::CONTRACT_BILLING_METHOD_FIXED_PRICE === $obj['BILLINGMETHOD']
                && ContractUtil::isQuantityBasedTemplateSelected($obj, 'REVENUETEMPLATENAME');
        }
        if ( ! $usageTabVisible ) {
            $usageTabVisible =
                isArrayValueProvided($obj, 'BILLINGMETHOD')
                && ContractBillingMethodTypes::CONTRACT_BILLING_METHOD_FIXED_PRICE === $obj['BILLINGMETHOD']
                && ContractUtil::isQuantityBasedTemplateSelected($obj, 'REVENUE2TEMPLATENAME');
        }

        if ( ContractBillingMethodTypes::isProjectTime($obj['BILLINGMETHOD']) && CONTRACT_DEBUG ) {
            $usageTabVisible = true;
        }
        $view->findAndSetProperty(
            [ 'id' => 'contractusage' ],
            [ 'hidden' => ! $usageTabVisible ],
            EditorComponentFactory::TYPE_TAB
        );
    }

    /**
     * Configures the visibility of both revenue template dependent fields.
     *
     * @param array $obj
     */
    private function configureRevenueTemplateDependants(&$obj)
    {
        $view = $this->getView();
        $templates = [
            'REVENUETEMPLATENAME' => ['pref' => 'ENABLEJOURNAL1', 'fields' => ['REVENUESTARTDATE', 'REVENUEENDDATE']],
            'REVENUE2TEMPLATENAME' => ['pref' => 'ENABLEJOURNAL2', 'fields' => ['REVENUE2STARTDATE', 'REVENUE2ENDDATE']],
        ];
        
        GetModulePreferences(Globals::$g->kCNid, $prefs);
        
        $showTotalQuantity = false;
        foreach ($templates as $templateFieldName => $values) {
            $pref = $values['pref'];
            $fields = $values['fields'];
            $hide = isset($prefs[$pref]) && isArrayValueFalse($prefs, $pref);
            $quantityBasedSelected = ContractUtil::isQuantityBasedTemplateSelected($obj, $templateFieldName);
            $revTemplate = ContractUtil::getContractRevenueTemplateForName($obj[$templateFieldName]);
            $projBasedSelected = ContractRevrecAndBillingMethodTypes::isTemplateMethodProjectBased($revTemplate['METHOD']);
            foreach ($fields as $field) {
                $view->findAndSetProperty(
                    ['path' => $field],
                    ['hidden' => ($hide || $quantityBasedSelected || $projBasedSelected)],
                    EditorComponentFactory::TYPE_FIELD
                );
            }
            
            $showTotalQuantity = $showTotalQuantity || $quantityBasedSelected;
            $view->findAndSetProperty(
                ['path' => 'REVENUETOTALQUANTITY'],
                ['hidden' => !$showTotalQuantity],
                EditorComponentFactory::TYPE_FIELD
            );
        }
    }

    /**
     * Retrieves resolves records for display
     *
     * @param array $obj
     */
    protected function setHistory(&$obj)
    {
        $filters = [ 'CONTRACTDETAILKEY' => $obj['RECORDNO'] ];

        $obj['JRNL1_HISTORY'] = ContractEditor::getHistory(ContractGLReclassEvent::JOURNALTYPE_J1, $filters);

        $obj['JRNL2_HISTORY'] = ContractEditor::getHistory(ContractGLReclassEvent::JOURNALTYPE_J2, $filters);
    }

    /**
     * Retrieves MRR history records for display
     *
     * @param array $obj
     */
    protected function setMRRHistory(&$obj)
    {
        $contractDetailKey = $obj['RECORDNO'];

        $obj['MRR_HISTORY'] = self::getMRRHistory($contractDetailKey);
    }

    /**
     * Retrieves accounts and journals
     *
     * @param array $obj
     */
    protected function setAccountsAndJournals(&$obj)
    {
        if (empty($this->textMap)) {    // l10n text for ajax
            // TODO: move to self::jsTokens and use client side GT instead for better performance
            $this->textMap = getLocalizedTextWithThrow(I18N::tokenArrayToObjectArray(self::acctAndJournalsTokens));
        }
        $this->setExpenseAccounts($obj, $expenses);

        $this->setJournals($obj, $expenses);

        $this->setRevenueAccounts($obj);

        if ( IsInstalled(Globals::$g->kDBBid) &
             !ContractManager::isEvergreenContract($obj, 'CONTRACT.TERMTYPE'))
        {
            $this->setDBBAccounts($obj);
        }
    }

    /**
     * Fetches and formats MRR history records
     *
     * @param int|null    $contractDetailKey
     * @param int|null    $contractKey
     * @param string|null $asOfDate As of date
     *
     * @return array
     */
    public static function getMRRHistory($contractDetailKey = null, $contractKey = null, $asOfDate = null)
    {
        $sess = Session::getKey();
        $glb_op = GetOperationId('gl/lists/glbatch/view');
        $sglb_op = GetOperationId('gl/lists/statglbatch/view');

        $selects = [
            'RECORDNO', 'CONTRACTKEY', 'CONTRACTDETAILKEY', 'LINENO', 'DBBCATEGORY', 'TRANSACTIONDATE', 'TR_TYPE',
            'AMOUNT', 'BASEAMOUNT', 'GLBATCHKEY', 'GLBATCHNO',
        ];

        $orders = [ [ 'TRANSACTIONDATE' ], [ 'RECORDNO' ] ];

        /* @var ManagerFactory $gManagerFactory */
        $gManagerFactory = Globals::$g->gManagerFactory;

        $mgr = $gManagerFactory->getManager('contractmrrresolve');
        $params = [];
        $params['selects'] = $selects;

        $params['filters'] = [ [
                                   // If we want to hide the reversal entries, uncomment the line below
                                   //            ['POSTINGTYPE', '!=', IContractMRRLifeCycleEngine::DBB_POSTINGTYPE_REVERSAL]
                               ] ];

        if ( $contractDetailKey ) {
            $params['filters'][0][] = [ 'CONTRACTDETAILKEY', '=', $contractDetailKey ];
        }

        if ( $contractKey ) {
            $params['filters'][0][] = [ 'CONTRACTKEY', '=', $contractKey ];
        }

        if ( $asOfDate ) {
            $params['filters'][0][] = [ 'TRANSACTIONDATE', '<=', $asOfDate ];
        }

        if ( $orders ) {
            $params['orders'] = $orders;
        }
        $mrr_history = $mgr->GetList($params);

        foreach ( $mrr_history as $idx => $history ) {
            $tr_type = $history['TR_TYPE'];

            $mrr_history[$idx]['MRRAMOUNT'] = $history['AMOUNT'] * $tr_type;
            $mrr_history[$idx]['MRRBASEAMOUNT'] = $history['BASEAMOUNT'] * $tr_type;
            $mrr_history[$idx]['MRRTRANSACTIONDATE'] = $history['TRANSACTIONDATE'];
            $mrr_history[$idx]['MRRDBBCATEGORY'] = $history['DBBCATEGORY'];
            $mrr_history[$idx]['CONTRACTDETAILLINENO'] = $history['LINENO'];

            $_op = $mrr_history[$idx]['DBBCATEGORY'] == 'Customer count' ? $sglb_op : $glb_op;

            $view_url = "editor.phtml?.op=$_op&.sess=$sess&.r=" . $history['GLBATCHKEY'] . '&.popup=1';

            $mrr_history[$idx]['MRRGLBATCHLINK'] = '<a href=\'javascript:Launch( "' . $view_url
                                                   . '" , "glbatch", 1200, 600);\' target1="_blank">'
                                                   . $history['GLBATCHNO'] . '</a>';
        }

        return $mrr_history;
    }

    /**
     * @param array  $obj
     * @param array &$expenses
     */
    protected function setExpenseAccounts(&$obj, &$expenses)
    {
        $selects = [ 'RECORDNO', 'DEFERREDEXPENSEACCTNO', 'DEFERREDEXPENSEACCTTITLE', 'RECOGNIZEDEXPENSEACCTNO',
                     'RECOGNIZEDEXPENSEACCTTITLE', 'EXPENSEACCRUALACCTNO', 'EXPENSEACCRUALACCTTITLE',
                     'EXPENSEJOURNAL', 'EXPENSEJOURNALTITLE', 'EXPENSE2JOURNAL', 'EXPENSE2JOURNALTITLE',
        ];
        $filters = [ 'CONTRACTDETAILKEY' => $obj['RECORDNO'] ];
        $orders = [ [ 'RECORDNO', 'ASC' ] ];

        $expenses = EntityManager::GetListQuick('contractexpense', $selects, $filters, $orders);

        if ( $expenses ) {
            $obj['EXPENSEACCOUNTS'] = [];

            foreach ( $expenses as $idx => $expense ) {
                $obj['EXPENSEACCOUNTS'][] = [
                    'EXPENSELINENO'         => 'Exp # ' . ++$idx,
                    'DEFERREDEXPENSEACCTNO' =>
                        $expense['DEFERREDEXPENSEACCTNO'] . '--' . $expense['DEFERREDEXPENSEACCTTITLE'],

                    'RECOGNIZEDEXPENSEACCTNO' =>
                        $expense['RECOGNIZEDEXPENSEACCTNO'] . '--' . $expense['DEFERREDEXPENSEACCTTITLE'],

                    'EXPENSEACCRUALACCTNO' =>
                        $expense['EXPENSEACCRUALACCTNO'] . '--' . $expense['DEFERREDEXPENSEACCTTITLE'],
                ];
            }
        }
    }

    /**
     * Retrieves revenue accounts
     *
     * @param array $obj
     */
    protected function setRevenueAccounts(&$obj)
    {
        $obj['REVENUEACCOUNTS'] = [
            [
                'ACCOUNTTYPE' => GT($this->textMap, 'IA.AR_UNBILLED'),
                'ACCOUNTNO'   => $obj['ARUNBILLEDACCTNO'] . '--' . $obj['ARUNBILLEDACCTTITLE'],
            ],
            [
                'ACCOUNTTYPE' => GT($this->textMap, 'IA.AR_BILLED'),
                'ACCOUNTNO'   => $obj['ARBILLEDACCTNO'] . '--' . $obj['ARBILLEDACCTTITLE'],
            ],
            [
                'ACCOUNTTYPE' => GT($this->textMap, 'IA.DR_UNBILLED'),
                'ACCOUNTNO'   => $obj['DRUNBILLEDACCTNO'] . '--' . $obj['DRUNBILLEDACCTTITLE'],
            ],
            [
                'ACCOUNTTYPE' => GT($this->textMap, 'IA.DR_BILLED'),
                'ACCOUNTNO'   => $obj['DRBILLEDACCTNO'] . '--' . $obj['DRBILLEDACCTTITLE'],
            ],
            [
                'ACCOUNTTYPE' => GT($this->textMap, 'IA.DR_PAID'),
                'ACCOUNTNO'   => $obj['DRPAIDACCTNO'] . '--' . $obj['DRPAIDACCTTITLE'],
            ],
            [
                'ACCOUNTTYPE' => GT($this->textMap, 'IA.SALES_UNBILLED'),
                'ACCOUNTNO'   => $obj['SALESUNBILLEDACCTNO'] . '--' . $obj['SALESUNBILLEDACCTTITLE'],
            ],
            [
                'ACCOUNTTYPE' => GT($this->textMap, 'IA.SALES_BILLED'),
                'ACCOUNTNO'   => $obj['SALESBILLEDACCTNO'] . '--' . $obj['SALESBILLEDACCTTITLE'],
            ],
            [
                'ACCOUNTTYPE' => GT($this->textMap, 'IA.SALES_PAID'),
                'ACCOUNTNO'   => $obj['SALESPAIDACCTNO'] . '--' . $obj['SALESPAIDACCTTITLE'],
            ],
        ];
    }

    /**
     * Retrieves DBB accounts
     *
     * @param array $obj Current object
     */
    protected function setDBBAccounts(&$obj)
    {
        $obj['DBBACCOUNTS'] = [
            [
                'ACCOUNTTYPE' => GT($this->textMap, 'IA.NEW_MRR'),
                'ACCOUNTNO'   => $obj['NEWMRRACCTNO'] . '--' . $obj['NEWMRRACCTTITLE'],
            ],
            [
                'ACCOUNTTYPE' => GT($this->textMap, 'IA.ADD_ON_MRR'),
                'ACCOUNTNO'   => $obj['ADDONMRRACCTNO'] . '--' . $obj['ADDONMRRACCTTITLE'],
            ],
            [
                'ACCOUNTTYPE' => GT($this->textMap, 'IA.RENEWAL_UPGRADE'),
                'ACCOUNTNO'   => $obj['RENEWALUPGRADEACCTNO'] . '--' . $obj['RENEWALUPGRADEACCTTITLE'],
            ],
            [
                'ACCOUNTTYPE' => GT($this->textMap, 'IA.RENEWAL_DOWNGRADE'),
                'ACCOUNTNO'   => $obj['RENEWALDOWNGRADEACCTNO'] . '--' . $obj['RENEWALDOWNGRADEACCTTITLE'],
            ],
            [
                'ACCOUNTTYPE' => GT($this->textMap, 'IA.CHURN_MRR'),
                'ACCOUNTNO'   => $obj['CHURNMRRACCTNO'] . '--' . $obj['CHURNMRRACCTTITLE'],
            ],
            [
                'ACCOUNTTYPE' => GT($this->textMap, 'IA.DOWNGRADE'),
                'ACCOUNTNO'   => $obj['PARTIALDOWNGRADEACCTNO'] . '--' . $obj['PARTIALDOWNGRADEACCTTITLE'],
            ],
            [
                'ACCOUNTTYPE' => GT($this->textMap, 'IA.MRR_OFFSET'),
                'ACCOUNTNO'   => $obj['MRROFFSETACCTNO'] . '--' . $obj['MRROFFSETACCTTITLE'],
            ],
            [
                'ACCOUNTTYPE' => GT($this->textMap, 'IA.CUSTOMER_COUNT'),
                'ACCOUNTNO'   => $obj['CUSTCNTACCTNO'] . '--' . $obj['CUSTCNTACCTTITLE'],
            ],
        ];
    }

    /**
     * Retrieves revenue journals
     *
     * @param array $obj      Contract detail attributes
     * @param array $expenses Contract expense detail attributes
     */
    protected function setJournals(&$obj, $expenses)
    {
        $obj['JOURNALS'] = [];

        if ( ContractUtil::isShowJournal1() ) {
            $obj['JOURNALS'][] = [
                'JOURNALTYPE' => GT($this->textMap, 'IA.REVENUE_JOURNAL_1'),
                'JOURNAL'     => $obj['REVENUEJOURNAL'] . '--' . $obj['REVENUEJOURNALTITLE'],
            ];
        }

        if ( ContractUtil::isShowJournal2() ) {
            $obj['JOURNALS'][] = [
                'JOURNALTYPE' => GT($this->textMap, 'IA.REVENUE_JOURNAL_2'),
                'JOURNAL'     => $obj['REVENUE2JOURNAL'] . '--' . $obj['REVENUE2JOURNALTITLE'],
            ];
        }

        if ( $expenses ) {
            if ( ContractUtil::isShowJournal1() ) {
                $obj['JOURNALS'][] = [
                    'JOURNALTYPE' => GT($this->textMap,'IA.EXPENSE_JOURNAL_1'),
                    'JOURNAL'     => $expenses[0]['EXPENSEJOURNAL'] . '--' . $expenses[0]['EXPENSEJOURNALTITLE'],
                ];
            }

            if ( ContractUtil::isShowJournal2() ) {
                $obj['JOURNALS'][] = [
                    'JOURNALTYPE' => GT($this->textMap,'IA.EXPENSE_JOURNAL_2'),
                    'JOURNAL'     => $expenses[0]['EXPENSE2JOURNAL'] . '--' . $expenses[0]['EXPENSE2JOURNALTITLE'],
                ];
            }
        }

        if ( IsInstalled(Globals::$g->kDBBid) &&
             !ContractManager::isEvergreenContract($obj, 'CONTRACT.TERMTYPE'))
        {
            $obj['JOURNALS'][] = [
                'JOURNALTYPE' => GT($this->textMap,'IA.MONTHLY_RECURRING_REVENUE_MRR'),
                'JOURNAL'     => $obj['MRRJOURNAL'] . '--' . $obj['MRRJOURNALTITLE'],
            ];

            $obj['JOURNALS'][] = [
                'JOURNALTYPE' => GT($this->textMap, 'IA.CUSTOMER_COUNT'),
                'JOURNAL'     => $obj['CUSTCNTJOURNAL'] . '--' . $obj['CUSTCNTJOURNALTITLE'],
            ];
        }
    }

    /**
     * @return array
     */
    protected function getEditorGlobals()
    {
        $vars = parent::getEditorGlobals();
        $contractid = Request::$r->_contractid;

        if ( $contractid != '' ) {
            $vars['cnstdate'] = $this->contractValues['BEGINDATE'];
            $vars['cnenddate'] = $this->contractValues['ENDDATE'];
            $vars['basecurr'] = $this->contractValues['BASECURR'];
            $vars['currency'] = $this->contractValues['CURRENCY'];
            $vars['exch_rate_type'] = $this->contractValues['EXCHRATETYPE'];
            $vars['exch_rate_type_id'] = $this->contractValues['EXCH_RATE_TYPE_ID'];
        } else {
            // This is the case of Duplicate contract detail
            $obj = Request::$r->GetCurrentObject();
            if ( isArrayValueProvided($obj, 'CONTRACTID') ) {
                $vars['cnstdate'] = $obj['BEGINDATE'];
                $vars['cnenddate'] = $obj['ENDDATE'];
                $vars['basecurr'] = $obj['BASECURR'];
                $vars['currency'] = $obj['CURRENCY'];
                $vars['exch_rate_type'] = $obj['EXCHRATETYPE'];
                $vars['exch_rate_type_id'] = $obj['EXCH_RATE_TYPE_ID'];
            }
        }
        $vars['billing_method_project_time'] = ContractBillingMethodTypes::CONTRACT_BILLING_METHOD_PROJECT_TIME;
        $vars['billing_method_project_materials'] =
            ContractBillingMethodTypes::CONTRACT_BILLING_METHOD_PROJECT_MATERIALS;
        $vars['UBBILLED_TO_ZERO'] = RevaluationType::UBBILLED_TO_ZERO;
        $vars['UBBILLED_TO_ZERO_RE_ESTIMATE'] = RevaluationType::UBBILLED_TO_ZERO_RE_ESTIMATE;
        $vars['prclistkey'] = '';
        if ( $this->contractValues['PRCLSTKEY'] != '' ) {
            $vars['prclistkey'] = $this->contractValues['PRCLSTKEY'];
        }

        $vars['prclstop'] = GetOperationId('cn/lists/contractitempricelist/view');
        $vars['cnpercent_create_op'] = GetOperationId('cn/lists/contractpercentbilling/create');
        $vars['cnpercent_view_op'] = GetOperationId('cn/lists/contractpercentbilling/view');
        $vars['cnnegative_create_op'] = GetOperationId('cn/lists/contractnegativebilling/create');
        $vars['cnnegative_view_op'] = GetOperationId('cn/lists/contractnegativebilling/view');

        if ( ! util_isPlatformDisabled() ) {
            $vars['platformPTRDimensionReverseFieldNames'] =
                [ 'LOCATION' => 'LOCATIONID', 'DEPARTMENT' => 'DEPARTMENTID' ];
            $vars['platformPTRDimensionFieldNames'] = [ 'LOCATIONID' => 'LOCATION', 'DEPARTMENTID' => 'DEPARTMENT' ];
        }

        $vars['dbbSubscribed'] = IsInstalled(Globals::$g->kDBBid);
        $vars['isMCPEnabled'] = $this->isMCPEnabled;

        if ( isset($this->contractValues['RECORDNO'])
             && ContractUtil::getAdvBillOptions($this->contractValues['RECORDNO']) ) {
            $vars['hasAdvBillOptions'] = true;
        }

        if ( isset($this->contractValues['RECORDNO']) ) {
            $vars['glOpenDate'] = ContractUtil::getCompanyOpenDate($this->contractValues['RECORDNO']);
        } else {
            $vars['glOpenDate'] = '';
        }

        $obj = Request::$r->GetCurrentObject();

        GetModulePreferences(Globals::$g->kCNid, $prefs);
        $vars['revenuejournal2name'] = $prefs['REVENUEJOURNAL2'];
        $vars['revenuejournal1name'] = $prefs['REVENUEJOURNAL1'];

        if ( isset($obj['REVENUETEMPLATEMETHOD'])
             && ( $obj['REVENUETEMPLATEMETHOD']
                  === ContractRevrecAndBillingMethodTypes::CONTRACT_REVREC_METHOD_STRAIGHT_LINE
                  || $obj['REVENUETEMPLATEMETHOD']
                     === ContractRevrecAndBillingMethodTypes::CONTRACT_REVREC_METHOD_DAILY_RATE ) ) {
            $vars['supportedRecMethodForRev1Resume'] = true;
        } else {
            $vars['supportedRecMethodForRev1Resume'] = false;
        }

        if ( isset($obj['REVENUE2TEMPLATEMETHOD'])
             && ( $obj['REVENUE2TEMPLATEMETHOD']
                  === ContractRevrecAndBillingMethodTypes::CONTRACT_REVREC_METHOD_STRAIGHT_LINE
                  || $obj['REVENUE2TEMPLATEMETHOD']
                     === ContractRevrecAndBillingMethodTypes::CONTRACT_REVREC_METHOD_DAILY_RATE ) ) {
            $vars['supportedRecMethodForRev2Resume'] = true;
        } else {
            $vars['supportedRecMethodForRev2Resume'] = false;
        }

        // make the preview billing schedule feature enablement state available in javascript
        $vars['PREVIEW_BILLING_SCHEDULE_ENABLED'] = ContractUtil::isPreviewBillingScheduleEnabled();

        $vars['isRelaxEvgGLPostEnabled'] = CNSetupManager::isRelaxEvergreenGLPostEnabled();

        return $vars;
    }

    /**
     * MergeOwnerDimensions
     *     override parent
     *
     * @param array &$_params params array
     */
    protected function MergeOwnerDimensions(&$_params)
    {
        parent::MergeOwnerDimensions($_params);

        // getDefaultsDimensionsFields() does not handle LOC AND DEPT. So set Auto-fill flag on LOC and DEPT dimension
        $deptLocDim = [ 'department' => 'DEPARTMENTID', 'location' => 'LOCATIONID' ];
        if ( ! util_isPlatformDisabled() ) {
            foreach ( $deptLocDim as $name => $id ) {
                $matches = [];
                self::findElements(
                    $_params, [ 'path' => $id ], EditorComponentFactory::TYPE_FIELD, $matches);
                $matches[0]['standard'] = true;
                $matches[0]['isDimension'] = true;
                $matches[0]['autofillrelated'] = Pt_StandardUtil::autoFillRelated($name);
            }
        }

        $nodes = $this->getDefaultsDimensionsFields($_params, $_params['entity']);
        //   eppp_p($nodes);diefl();
        $fieldCnt = count($nodes);

        // if the ownedobject has dimensions we add them to the default container
        if ( ! isset($nodes) || $fieldCnt <= 0 ) {
            return;
        }

        // let's see if we have a default container for the dimension header fields.
        $matches = [];
        $container = null;
        self::findElements($_params['view'], [ 'dimFields' => $_params['entity'] ], null, $matches);
        if ( is_array($matches) && count($matches) > 0 ) {
            $container = &$matches[0];
        }

        if ( isset($container) ) {
            foreach ( $nodes as $node ) {
                $this->addCustomFieldToLayout($node, $container);
            }
        }
    }

    /**
     * @param array $node
     * @param array $container
     */
    protected function addCustomFieldToLayout($node, &$container)
    {
        if ( ! empty($container['child'][0]['row']) ) {
            $node['rightSideLabel'] = '1';
            $row = [
                'label' => ' ',
                'child' => [
                    [
                        'field' => [
                            $node,
                        ],
                    ],
                ],
            ];
            $container['child'][0]['row'][] = $row;
        } else {
            if ( $node['dimensionid'] == 'customerdimension' ) {
                $node['hidden'] = '1';
            }
            parent::addCustomFieldToLayout($node, $container);
        }
    }

    /**
     * @param string        $entity
     * @param string        $objId
     * @param string        $doctype
     * @param string[]|null $fields
     *
     * @return array|bool
     */
    protected function getEntityData($entity, $objId, $doctype = '', $fields = null)
    {
        $entityData = parent::getEntityData($entity, $objId, $doctype, $fields);

        $fieldsToQuery = $this->findExpenseFieldsToQuery();

        $conObjHandler = new ContractObjHandler();
        $expenseDetailData = $conObjHandler->getExpenseDetailData(
            $entityData['RECORDNO'], 'CONTRACTDETAILKEY', $fieldsToQuery
        );
        $entityData['CONTRACTDETAIL_EXPENSEDETAIL'] = $expenseDetailData ?? [];

        if ( ! util_isPlatformDisabled() ) {
            $platformDef = Pt_StandardUtil::getObjDef($entity);
            $platformRels = Pt_StandardUtil::getRelationshipFields($platformDef);

            $this->setPlatformRelsAtrribute($platformRels, $entityData);
            $this->setGLDimRelsAttribute($entity, $platformRels, $entityData);
        }

        if ($this->isEvergreenContract()) {
            $recurrenceFields = $this->findContractDetailFieldsToQuery();

            $entityData['RECURRENCE'] = $this->getContractDetailDataFiltered(
                $entityData['CONTRACTKEY'],
                $entityData['RENEWEDORIGINALDETAILKEY'],
                $recurrenceFields
            );
        }

        return $entityData;
    }

    /**
     * @return bool
     */
    protected function CanPrint()
    {
        return false;
    }

    /**
     * @return bool
     */
    protected function CanEdit()
    {
        if ( ! parent::CanEdit() ) {
            return false;
        }

        $res = $this->getCnDetailData();
        $contractDetailKey = Request::$r->{Globals::$g->kId};

        if ( $res[0]['STATE'] === ContractDetailState::STATE_CLOSED || $res[0]['STATE'] === ContractDetailState::STATE_CANCELLED ) {
            return true;
        }
        if ( in_array($res[0]['STATE'], [ ContractDetailState::STATE_COMPLETED,
                                          ContractDetailState::STATE_REVALUED ])
             || ContractScheduleManager::hasPostedSchedules($contractDetailKey)
             || (ContractBillingMethodTypes::isProjectTime($res[0]['BILLINGMETHOD'])
                 && ContractUtil::isLineIsReEstimated($res[0]['RECORDNO']))
        ) {
            return false;
        }

        return true;
    }

    /**
     * @param array &$_params
     *
     * @return bool
     */
    protected function ProcessEditAction(&$_params)
    {
        $res = $this->getCnDetailData();
        $contractLockInfo = ContractProcessLockHandler::checkAllLocksForContractId($res[0]['CONTRACTID']);
        if ($contractLockInfo && $contractLockInfo->isLocked()) {
            $ok = parent::ProcessViewAction($_params);
        } else {
            if ($this->getRenewalBillTmplNameEditAction()) {
                $ok = parent::ProcessEditAction($_params);
            } else if ( $res[0]['STATE'] == ContractDetailState::STATE_COMPLETED
                || $res[0]['STATE'] == ContractDetailState::STATE_REVALUED ) {
                $ok = parent::ProcessViewAction($_params);
            } else {
                $ok = parent::ProcessEditAction($_params);
            }
        }

        return $ok;
    }

    /**
     * Map the ajax action to a PHP function
     *
     * @param string $cmd the Ajax action requested
     *
     * @return bool false if command not found
     */
    protected function runAjax($cmd)
    {
        $ok = true;

        switch ($cmd) {
            case 'getExchangeRate':
                $this->ajaxGetExchangeRate();
                break;
            case 'cancelContractDetail':
                $this->ajaxCancelContractDetail();
                break;
            case 'uncancelContractDetail':
                $this->ajaxUncancelContractDetail();
                break;
            case 'cancelExpenses':
                $this->ajaxCancelExpenses();
                break;
            case 'holdSchedules':
                $this->ajaxHoldSchedules();
                break;
            case 'resumeSchedules':
                $this->ajaxResumeContractDetail();
                break;
            case 'getBalances':
                $this->getBalances();
                break;
            case 'getHistoryData':
                $this->getHistoryData();
                break;
            case 'fetchDefaultChangeType':
                $this->getDefaultChangeType();
                break;
            case 'fetchDefaultItemInformation':
                $this->fetchDefaultItemInformation();
                break;
            case 'getActiveExpenses':
                $this->getActiveExpenses();
                break;
            case 'getAccounts':
                $this->ajaxGetAccountsAndJournals();
                break;
            case 'deliverContractLine':
                $this->deliverContractLine();
                break;
            case 'getBillInAdvanceDates':
                $this->ajaxGetBillInAdvanceDates();
                break;
            case 'getEvergreenEndDate':
                $this->ajaxGetEvergreenEndDate();
                break;
            case 'getGLPostingDate':
                $this->ajaxGetGLPostingDate();
                break;
            case 'convertSchedulesPostingType':
                $this->ajaxConvertContractDetailPostingType();
                break;
            case 'completecontractLine':
                $this->ajaxCompleteContractLine();
                break;
            case 'getProjectTimeAggHistory':
                $this->getProjectTimeAggHistory();
                break;
            case 'contractDetailHasMEA':
                $this->ajaxContractDetailHasMEA();
                break;
            case 'getProrateBillingData':
                $this->ajaxGetProrateBillingData();
                break;
            case 'fetchDefaultMRRChangeType':
                $this->ajaxGetDefaultMRRChangeType();
                break;
            case 'getPreviewBillingScheduleData':
                $this->ajaxGetPreviewBillingScheduleData();
                break;
            default:
                $ok = parent::runAjax($cmd);
                break;
        }

        return $ok;
    }

    /**
     * Calculate billing start date/end date based on Contract's Bill in Advance setting
     */
    protected function ajaxGetBillInAdvanceDates()
    {
        $contractKey = Request::$r->contractKey;
        $beginDate = Request::$r->beginDate;
        $endDate = Request::$r->endDate;

        $response = null;
        if ( $contractKey && $beginDate ) {
            $response = [];

            if ( $endDate ) {
                $advBillOptions = ContractUtil::getAdvBillOptions($contractKey);
                if ( $advBillOptions ) {
                    $response = $advBillOptions;
                    [ $response['BILLINGSTARTDATE'], $response['BILLINGENDDATE'] ] =
                        ContractUtil::getAdvanceBillDates($contractKey, $beginDate, $endDate);
                }
            }

            $glPostingDate = ContractUtil::getGLPostingDate($beginDate, ContractUtil::getCompanyOpenDate($contractKey),
                                                            $contractKey);
            $response['GLPOSTINGDATE'] = $glPostingDate;
        }

        echo $response ? json_encode($response) : json_encode(false);
    }

    /**
     * Calculate Evergreen end date
     */
    protected function ajaxGetEvergreenEndDate() : void
    {
        $contractBeginDate = Request::$r->contractBeginDate;
        $detailBeginDate = Request::$r->detailBeginDate;
        $billingFrequency = Request::$r->billingFrequency;

        $response = null;
        if ( $contractBeginDate && $detailBeginDate && $billingFrequency ) {
            $response = [];

            $endDate = ContractUtil::getEvergreenEndDate(
                $contractBeginDate,
                $detailBeginDate,
                $billingFrequency
            );
            $response['ENDDATE'] = $endDate;
        }

        echo $response && count($response) > 0 ? json_encode($response) : json_encode(false);
    }

    /**
     * Calculate gl posting date
     */
    protected function ajaxGetGLPostingDate()
    {
        $contractKey = Request::$r->contractKey;
        $beginDate = Request::$r->beginDate;

        $response = null;
        if ( $contractKey && $beginDate ) {
            $response = [];

            $glPostingDate = ContractUtil::getGLPostingDate($beginDate, ContractUtil::getCompanyOpenDate($contractKey),
                                                            $contractKey);
            $response['GLPOSTINGDATE'] = $glPostingDate;
        }

        echo $response && count($response) > 0 ? json_encode($response) : json_encode(false);
    }

    /**
     * Retrieves GL accounts and journals for display thru Ajax
     */
    private function ajaxGetAccountsAndJournals()
    {
        $obj = null;

        $contractDetailKey = Request::$r->contractDetailKey;
        if ( $contractDetailKey > 0 ) {
            $selects = ContractDetailManager::getAccountAndJournalFields();

            $obj = EntityManager::GetListQuick(
                'contractdetail',
                $selects,
                [ 'RECORDNO' => $contractDetailKey ]
            );

            if ( isset($obj[0]['RECORDNO']) ) {
                $obj = $obj[0];
                $this->setAccountsAndJournals($obj);
            } else {
                $obj = null;
            }
        }

        if ( $obj == null ) {
            // TODO how do we handle error reporting?
            $obj = [ 'error' ];
        }

        echo json_encode($obj);
    }
    
    /**
     * Retrieves balances for display thru Ajax
     */
    public function getBalances()
    {
        $selects = $this->getEntityMgr()->GetGetFields();
        $selects = array_merge($selects, ContractDetailManager::getAccountAndJournalFields());
        $selects = array_merge($selects, ['CONTRACT.BEGINDATE', 'CONTRACT.ENDDATE', 'CONTRACT.TERMTYPE', 'RENEWEDORIGINALDETAILKEY']);
        $obj = EntityManager::GetListQuick(
            'contractdetail',
            $selects,
            ['RECORDNO' => Request::$r->contractDetailKey]
        );
        
        $obj = $obj[0];
        $this->setAccountsAndJournals($obj);
        
        $balances = new ContractDetailEditorBalances(
            $this->isMCPEnabled,
            $this->isStandardRevRecEnabled,
            $this->isExpenseEnabled
        );
        
        $balances->getBalances($obj);
    }

    /**
     * Retrieves resolves records for display thru Ajax
     */
    protected function getHistoryData()
    {
        $obj = [];
        if ( ! empty(Request::$r->contractDetailKey) ) {
            $filters = [ 'CONTRACTDETAILKEY' => Request::$r->contractDetailKey ];

            $obj['JRNL1_HISTORY'] = ContractEditor::getHistory(
                ContractGLReclassEvent::JOURNALTYPE_J1,
                $filters,
                null,
                Request::$r->asOfDate
            );

            $obj['JRNL2_HISTORY'] = ContractEditor::getHistory(
                ContractGLReclassEvent::JOURNALTYPE_J2,
                $filters,
                null,
                Request::$r->asOfDate
            );
        }

        //logtofile(pp($obj) . "\n");
        echo json_encode($obj);
    }

    /**
     * Get the exchange rate from the posted parameters
     */
    protected function ajaxGetExchangeRate()
    {
        // Gather the parameters
        $fromcurrency = Request::$r->fromcurrency;
        $tocurrency = Request::$r->tocurrency;
        $exchratedate = Request::$r->exchratedate;
        $exchratetype = Request::$r->exchratetype;
        $exchratetypeid = Request::$r->exchratetypeid;

        $response = [];

        // support for intacct daily rate tokenization
        // in this case the intacct daily rate is directly fetched from UI using contract get and will always be in English
        $exchMgr = Globals::$g->gManagerFactory->getManager('exchangeratetypesall');
        $params = [
            'selects' => [ 'ID', 'NAME'],
            'filters' => [
                [
                    [ 'ID', '=', $exchratetypeid]
                ],
            ],
        ];

        $exchangeRateType = $exchMgr->GetList($params);
        $translatedExchangeRateType = '';
        if(!empty($exchangeRateType[0])) {
            $translatedExchangeRateType = $exchangeRateType[0]['NAME'];
        }

        $rate = $this->getEntityMgr()
                     ->calculateExchRate($exchratetype, $fromcurrency, $tocurrency, $exchratedate);

        $response['rate'] = $rate;
        $response['exchratetype'] = $translatedExchangeRateType;
        echo json_encode($response);
    }

    /**
     * Echos default change type of item is enabled for MRR
     */
    private function getDefaultChangeType()
    {
        $itemId = Request::$r->itemId;
        $itemIdArr = explode('--', $itemId);
        $itemId = $itemIdArr[0];

        $beginDate = Request::$r->beginDate;
        if ( $beginDate ) {
            $beginDate = FormatDateForStorage($beginDate);
        }

        $defaultChangeType = '';
        if ( IsInstalled(Globals::$g->kDBBid) && $itemId ) {
            $items = EntityManager::GetListQuick('item', [ 'MRR', 'ITEMTYPE' ], [ 'ITEMID' => $itemId ]);
            $item = $items[0] ?? null;
            
            if ($item && $item['MRR'] == 'true' && $item['ITEMTYPE'] !== ContractDetailManager::ITEM_TYPE_KIT) {
                $contractIdArr = explode('--', Request::$r->contractId);
                $contractId = $contractIdArr[0];
                $contract = EntityManager::GetListQuick('contract', [ 'BEGINDATE' ], [ 'CONTRACTID' => $contractId ]);

                if ( $contract && $contract[0] && $beginDate && $contract[0]['BEGINDATE'] ) {
                    if ( DateCompare($beginDate, $contract[0]['BEGINDATE']) > 0 ) {
                        $defaultChangeType = BaseContractMRRLifeCycleEngine::DBB_CATEGORY_ADDONMRR;
                    } else {
                        $defaultChangeType = BaseContractMRRLifeCycleEngine::DBB_CATEGORY_NEWMRR;
                    }
                } else {
                    $defaultChangeType = BaseContractMRRLifeCycleEngine::DBB_CATEGORY_NEWMRR;
                }
            }
        }

        echo '"' . $defaultChangeType . '"';
    }

    /**
     * Cancel a contract detail
     */
    private function ajaxCancelContractDetail()
    {
        $errMsg = null;
        $contractDetailKey = Request::$r->contractDetailKey;
        $cancelDate = Request::$r->cancelDate;
        $autoGenPOCOption = Request::$r->autoGenPOCOption == 'true';
        $cancelExpenseSchedule = Request::$r->expenseOption == 'true';
        $createAdj = Request::$r->createAdjustmentOption == 'true';
        $includeUsage = Request::$r->includeUsageOption == 'true';

        if ( ! ( $contractDetailKey && $cancelDate ) ) {
            Globals::$g->gErr->addError('CN-0032', __FILE__ . ':' . __LINE__,
                                        'Contract detail key and Cancel date are required.');

            echo json_encode(false);

            return;
        }

        $ok = true;
        $mesg = '';
        try {
            $cnDetailHandler = new ContractDetailHandler();
            $cnDetailHandler->cancel(
                $contractDetailKey,
                $cancelDate,
                $cancelExpenseSchedule,
                $createAdj,
                $includeUsage,
                $autoGenPOCOption,
                false,
                $mesg
            );
        } catch ( IAException $ex ) {
            $errMsg = $ex->getMessage();

            $errObj = Globals::$g->gErr;
            ContractUtil::addThrowableError($ex, __FILE__ . ':' . __LINE__);
            $ok = false;

            // Get all the global errors if exist, this will help client to
            // better understand the error messages and take appropriate action.

            if ( HasErrors() ) {
                $errMsg = nl2br($errObj->myToString(false));
            }
        }

        $response = [ 'status' => $ok, 'mesg' => $mesg ];
        if ( ! $ok ) {
            $response['error'] = $errMsg;
        }

        echo json_encode($response);
    }

    /**
     * Uncancel a contract detail line.
     *
     * @return bool
     */
    private function ajaxUncancelContractDetail()
    {
        $ok = true;
        $errMsg = "";
        $response = [];

        // Get request.

        $contractDetailKey = Request::$r->contractDetailKey;

        if ( empty($contractDetailKey) ) {
            $ok = false;
            $resMsg = "Contract detail key is required.";
            Globals::$g->gErr->addError('CN-0033', __FILE__ . ':' . __LINE__, $resMsg);

            $response["resMsg"] = $resMsg;
            $response["status"] = $ok;

            echo json_encode($response);

            return $ok;
        }

        try {
            $cnDetailHandler = new ContractDetailHandler();
            $cnDetailHandler->uncancel($contractDetailKey);
        } catch ( IAException $exp ) {
            $ok = false;
            $errMsg = "";

            $errObj = Globals::$g->gErr;
            ContractUtil::addThrowableError($exp, __FILE__ . ':' . __LINE__);

            // Get all the global errors if exist, this will help client to
            // better understand the error messages and take appropriate action.

            if ( HasErrors() ) {
                $errMsg = nl2br($errObj->myToString(false));
            }
        }

        if ( $ok ) {
            $resMsg = "Successfull: The Contract line has been uncancelled.";
        } else {
            $resMsg = "Failed to uncancel contract line. {$errMsg}";
        }

        $response["resMsg"] = $resMsg;
        $response["status"] = $ok;

        echo json_encode($response);

        return $ok;
    }

    /**
     * Cancel Expenses
     *
     * ajaxCancelExpenses
     */
    private function ajaxCancelExpenses()
    {
        $errMsg = null;
        $cnExpenses = [];
        $cancelArgs = Util_DataRecordFormatter::jsonToPhp(Request::$r->cancelArgs);

        $selectedExpenses = [];
        foreach ( $cancelArgs['cancelExpenseArgs'] as $cExpenses ) {
            $selectedExpenses[$cExpenses['recNo']] = $cExpenses;
        }

        $response = [ 'status' => false ];

        $expRecNos = array_keys($selectedExpenses);
        if ( count($expRecNos) > 0 ) {
            $cnExpMgr = Globals::$g->gManagerFactory->getManager('contractexpense');
            $querySpec = [
                'selects' => [ 'RECORDNO' ],
                'filters' => [
                    [
                        [ 'CONTRACTDETAILKEY', '=', Request::$r->contractDetailKey ],
                        [ 'RECORDNO', 'IN', $expRecNos ],
                    ],
                ],
            ];
            $cnExpenses = $cnExpMgr->GetList($querySpec);
            foreach ( $cnExpenses as &$cnExpense ) {
                $recNo = $cnExpense['RECORDNO'];
                $cnExpense['createdAdj'] = $selectedExpenses[$recNo]['createdAdj'] == 1 ? true : false;
            }
            unset($cnExpense);
        }

        try {
            $source = "ContractDetailEditor::ajaxCancelExpenses";
            $ok = Globals::$g->gQueryMgr->beginTrx($source);
            $cnExpenseHandler = new ContractExpenseHandler();
            foreach ( $cnExpenses as $cnExpense ) {
                $cnExpenseHandler->cancel($cnExpense['RECORDNO'], Request::$r->cancelDate, $cnExpense['createdAdj']);
            }
        } catch ( IAException $ex ) {
            $errMsg = $ex->getMessage();
            ContractUtil::addThrowableError($ex, __FILE__ . ':' . __LINE__);
            $ok = false;
        }

        $response['status'] = $ok;
        if ( ! $ok ) {
            $response['error'] = $errMsg;
        }

        $ok = $ok && Globals::$g->gQueryMgr->commitTrx($source);
        if ( ! $ok ) {
            Globals::$g->gQueryMgr->rollbackTrx($source);
        }

        echo json_encode($response);
    }

    /**
     * ajaxHoldSchedules
     */
    private function ajaxHoldSchedules()
    {
        $contractDetailKey = Request::$r->recordNo;
        $schedulesToHold = Request::$r->schedulesToHold;

        $holdBillingSchedule = strpos($schedulesToHold, 'B') !== false;
        $holdRevenueSchedule = strpos($schedulesToHold, 'R') !== false;
        $holdExpenseSchedule = strpos($schedulesToHold, 'E') !== false;
        $holdDate = Request::$r->holdDate;
        $holdMemo = Request::$r->holdMemo;

        $ok = true;
        $errMsg = '';

        if ( ! $holdDate ) {
            $errMsg = 'Hold date is required.';
            Globals::$g->gErr->addError('CN-0034', __FILE__ . ':' . __LINE__, $errMsg);
            $ok = false;
        }

        if ( empty($contractDetailKey) ) {
            $ok = false;
        }

        if ( $ok ) {
            $cnDetailHandler = new ContractDetailHandler();
            try {
                $ok = $cnDetailHandler->hold($contractDetailKey, $holdDate, $holdMemo, $holdBillingSchedule,
                                             $holdRevenueSchedule, $holdExpenseSchedule);
            } catch ( IAException $exp ) {
                $errMsg = $exp->getMessage();
                ContractUtil::addThrowableError($exp, __FILE__ . ':' . __LINE__);
                $ok = false;
            }
        }
        $response = [ 'status' => $ok ];
        if ( ! $ok ) {
            if ( HasErrors() ) {
                $errMsg = nl2br(Globals::$g->gErr->myToString(false));
            }

            $response['error'] = $errMsg;
        }

        echo json_encode($response);
    }

    /**
     * Resume a contract detail
     */
    private function ajaxResumeContractDetail()
    {
        $contractDetailKey = Request::$r->recordNo;
        $resumeDate = Request::$r->resumeDate;
        $schedulesToResume = Request::$r->schedulesToResume;
        $adjustmentType = Request::$r->revenueAdjustmentType;
        $resumeMemo = Request::$r->resumeMemo;

        $resumeBillingSchedules = strpos($schedulesToResume, 'B') !== false;
        $resumeRevenueSchedules = strpos($schedulesToResume, 'R') !== false;
        $resumeExpenseSchedules = strpos($schedulesToResume, 'E') !== false;

        $errMsg = '';
        $ok = true;

        if ( empty($contractDetailKey) ) {
            $ok = false;
            $errMsg = _("Missing contract detail key");
        }

        if ( $ok ) {
            $cnDetailHandler = new ContractDetailHandler();
            try {
                $ok = $cnDetailHandler->resume($contractDetailKey, $resumeDate, $resumeMemo, $resumeBillingSchedules,
                                               $resumeRevenueSchedules, $resumeExpenseSchedules, $adjustmentType);
            } catch ( IAException $exp ) {
                $errMsg = $exp->getMessage();
                ContractUtil::addThrowableError($exp, __FILE__ . ':' . __LINE__);
                $ok = false;
            }
        }
        $response = [ 'status' => $ok ];
        if ( ! $ok ) {
            if ( HasErrors() ) {
                $errMsg = nl2br(Globals::$g->gErr->myToString(false));
            }

            $response['error'] = $errMsg;
        }

        echo json_encode($response);
    }

    /**
     * Convert posting type for contract detail
     */
    private function ajaxConvertContractDetailPostingType()
    {
        $contractDetailKey = Request::$r->recordNo;
        $effectiveDate = Request::$r->effectiveDate;
        $revenueJournalsOption = Request::$r->revenueJournalsOption;
        $expenseJournalsOption = Request::$r->expenseJournalsOption;

        $revenue = $revenue2 = $expense = $expense2 = false;

        if ( strpos($revenueJournalsOption, 'R1') !== false ) {
            $revenue = true;
        }
        if ( strpos($revenueJournalsOption, 'R2') !== false ) {
            $revenue2 = true;
        }
        if ( strpos($expenseJournalsOption, 'E1') !== false ) {
            $expense = true;
        }
        if ( strpos($expenseJournalsOption, 'E2') !== false ) {
            $expense2 = true;
        }

        $errMsg = '';
        $ok = true;
        if ( empty($contractDetailKey) ) {
            $ok = false;
        }

        if ( $ok ) {
            $cnDetailHandler = new ContractDetailHandler();
            try {
                $cnDetailHandler->convertPostingType($contractDetailKey, null, $effectiveDate, $revenue, $revenue2,
                                                     $expense, $expense2);
            } catch ( IAException $exp ) {
                $errMsg = $exp->getMessage();
                ContractUtil::addThrowableError($exp, __FILE__ . ':' . __LINE__);
                $ok = false;
            }
        }
        $response = [ 'status' => $ok ];
        if ( ! $ok ) {
            $response['error'] = $errMsg;
        }

        echo json_encode($response);
    }

    /**
     * deliver contract line
     */
    protected function deliverContractLine()
    {
        $ok = true;

        $contractDetailKey = Request::$r->contractDetailKey;
        $deliveryDate = Request::$r->deliveryDate;

        if ( empty($contractDetailKey) ) {
            $errMsg = 'Contract detail key is required.';
            Globals::$g->gErr->addError('CN-0033', __FILE__ . ':' . __LINE__, $errMsg);
            $ok = false;
        }

        if ( ! $deliveryDate ) {
            $errMsg = 'Delivery date is required.';
            Globals::$g->gErr->addError('CN-0035', __FILE__ . ':' . __LINE__, $errMsg);
            $ok = false;
        }

        $errMsg = '';
        if ( $ok ) {
            $cnDetailHandler = new ContractDetailHandler();
            try {
                $ok = $cnDetailHandler->deliver($contractDetailKey, $deliveryDate);
            } catch ( IAException $exp ) {
                $errMsg = $exp->getMessage();
                ContractUtil::addThrowableError($exp, __FILE__ . ':' . __LINE__);
                $ok = false;
            }
        }

        $response = [ 'status' => $ok ];

        if ( ! $ok ) {
            if ( HasErrors() ) {
                $errMsg .= "<br>" . nl2br(Globals::$g->gErr->myToString(false));
            }

            $response['error'] = $errMsg;
        }

        echo json_encode($response);
    }

    /**
     * Returns dimension ids in GL order without module filtering
     *
     * @return array
     */
    protected function getDimensionIDsInOrder()
    {
        $dimOrder = IADimensions::getDimensionIDsInGLOrder('', ! util_isPlatformDisabled());

        return $dimOrder;
    }

    /**
     * Get the Add URL for the grid
     *
     * @param string $entity the entity
     * @param array  $object the data
     *
     * @return string the URL
     */
    public function GetGridAddUrl($entity, $object)
    {
        $url = parent::GetGridAddUrl($entity, $object);
        if ( $url != '' && $entity == 'contractexpense' ) {
            $url .= '&.contractdetailkey=' . $object['RECORDNO'];
            $url .= '&.contractid=' . $object['CONTRACTID'];
        }

        if ( $url != '' && $entity == 'contractusage' ) {
            $url .= '&.contractlineno=' . $object['LINENO'];
            $url .= '&.contractid=' . $object['CONTRACTID'];
            if ( ContractManager::isEvergreenContract($this->contractValues) &&
                 ContractUtil::isEvergreenQuantityBasedBillingEnabled() ) {
                $url .= '&.contractdetailkey=' . $object['RECORDNO'];
            };
        }

        return $url;
    }

    /**
     * @param string $entity
     * @param array  $object
     *
     * @return string
     */
    public function GetGridEditUrl($entity, $object)
    {
        $url = parent::GetGridEditUrl($entity, $object);
        if ( $url != '' && $entity == 'contractexpense' ) {
            $url .= '&.contractdetailkey=' . $object['RECORDNO'];
            $url .= '&.contractid=' . $object['CONTRACTID'];
        }

        return $url;
    }

    /**
     * Get the list of buttons for the screen
     *
     * @param string $state current state
     *
     * @return array buttons
     */
    public function getStandardButtons($state)
    {
        $obj = Request::$r->GetCurrentObject();
        $values = parent::getStandardButtons($state);
        $isKit = $this->isKit($obj);
        $isKitComponent = $this->isKitComponent($obj);

        $currStates = null;

        // Important to retrived from DB, since error go back has values from UI
        if ( isset($obj['RECORDNO']) && $obj['RECORDNO'] != '' ) {
            $currStates = $this->getCurrentStates($obj['RECORDNO']);
        } else if ( isset(Request::$r->_contractid) && Request::$r->_contractid != '' ) {
            $currStates['CONTRACTSTATE'] = $this->getContractState(Request::$r->_contractid);
        }

        foreach ( $values as $key => $val ) {
            if ( $val['id'] == 'savebuttid' && $val['label'] == 'IA.DRAFT' ) {
                // STATE needs to be explicitely set in JS for all error go back and duplicate scenarios
                $currLineState = $currStates['STATE'] ?? ContractDetailState::STATE_DRAFT;
                $values[$key]['jsCode'] =
                    $state == $this->kShowEditState ? "draftEditContractLine(this, '" . $currLineState . "')"
                        : 'draftNewContractLine(this)';

                break;
            }
        }

        $postOp = GetOperationId('cn/lists/contractdetail/post');
    
        if (
            $currStates['CONTRACTSTATE'] !== ContractState::STATE_DRAFT
            && CheckAuthorization($postOp, 1)
            && !ContractDetailManager::isKitComponent($obj)
        ) {
            if ( $state == $this->kShowNewState ) {
                $postJSCode = "postNewContractLine(this)";
                $postButton = [ [
                                    'id'           => 'postbuttid',
                                    'name'         => 'postbutton',
                                    'action'       => 'save',
                                    'label'        => 'IA.POST',
                                    'submitData'   => true,
                                    'serverAction' => true,
                                    'jsCode'       => $postJSCode,
                                ] ];

                $values = array_merge($postButton, $values);

                // Add 'Draft & new' link to 'More actions' menu.

                $reqParam = [ 'after' => 1, 'cnDraftAddAndNewAction' => 1 ];

                $this->createMoreActionEntry($values, 'draftaddandnewbuttid', 'draftaddandnewbutton',
                                             'IA.DRAFT_AND_NEW', 'create', true, null, true, true, $reqParam);

                foreach ( $values as &$val ) {
                    if ( $val['id'] == 'saveandnewbuttid' ) {
                        // Show 'Post & new' link if contract is not in Draft sate.
                        // Change label to 'Post & new'.
                        // Link still works as a 'Save & new' link.

                        $val['label'] = 'IA.POST_AND_NEW';
                        $val['args']['cnPostAddAndNewAction'] = 1;

                        break;
                    }
                }
            } else if ( $state == $this->kShowEditState ) {
                if ( $currStates['STATE'] == ContractDetailState::STATE_DRAFT ) {
                    $postJSCode = "window.editor.showPage('postCDPage', this)";
                    $postButton = [ [
                                        'id'     => 'postbuttid',
                                        'name'   => 'postbutton',
                                        'action' => 'post',
                                        'label'  => 'IA.POST',
                                        'jsCode' => $postJSCode,
                                    ] ];

                    $values = array_merge($postButton, $values);
                }
            }
        } else if ( $state == $this->kShowNewState && $currStates['CONTRACTSTATE'] == ContractState::STATE_DRAFT ) {
            // Add 'Draft & new' link to 'More actions' menu.

            $reqParam = [ 'after' => 1, 'cnDraftAddAndNewAction' => 1 ];

            $this->createMoreActionEntry($values, 'draftaddandnewbuttid', 'draftaddandnewbutton',
                                         'IA.DRAFT_AND_NEW', 'create', true, null, true, true, $reqParam);

            foreach ( $values as $key => $val ) {
                if ( $val['id'] == 'saveandnewbuttid' ) {
                    // Remove 'Save & new' link.

                    unset($values[$key]);

                    break;
                }
            }
        }

        if ( $state == $this->kShowNewState ) {
            $renewOnlyButton = [];
            $this->createMoreActionEntry(
                $renewOnlyButton, 'renewonlybuttid', 'renewonlybuttid', 'IA.RENEWAL_ONLY', 'create', true,
                "submitRenewalOnlyCNLine()");

            $values = array_merge($renewOnlyButton, $values);
        }
        // empty button array init
        $resumeCnDetailButton = [];
        $holdCnDetailButton = [];
        $addRevEstimateButton = [];
        $convertPostingTypeCnDetailButton = [];
        $cancelExpenseButton = [];
        $deliverCnDetailButton = [];
        $cancelCnDetailButton = [];

        $obj = Request::$r->GetCurrentObject();

        if ( $state == $this->kShowEditState && $obj['STATE'] != ContractDetailState::STATE_DRAFT && $obj['STATE'] != ContractDetailState::STATE_CANCELLED ) {
            $isRenewalOnlyContract = $this->isRenewalOnlyContract();

            if ( $isRenewalOnlyContract ) {
                $disRenewOnlyButton = [];
                $this->createMoreActionEntry(
                    $disRenewOnlyButton, 'renewonlybuttid', 'renewonlybuttid', 'IA.CONVERT_TO_STANDARD_POSTING', 'save',
                    true, "disableRenewalOnlyCNLine()");

                $values = array_merge($disRenewOnlyButton, $values);
            } else {
                $cancelCnDetailButton = [];
                if (
                    !ContractBillingMethodTypes::isProjectTime($obj['BILLINGMETHOD'])
                    && $obj['STATE'] !== ContractDetailState::STATE_CLOSED
                    && !$this->isKitComponent($obj)
                    && ContractUtil::hasCancelAccess()
                )
                {
                    $jsCode = "window.editor.showPage('cancelCDetailPage', this)";
                    $this->createMoreActionEntry(
                        $cancelCnDetailButton, 'ccndetailbuttid', 'ccndetailbutton', 'IA.CANCEL_CONTRACT_LINE',
                        'cancellation', false, $jsCode, false, true
                    );
                }

                if (!$this->isKitComponent($obj)) {
                    $this->addcancelExpenseButton($cancelExpenseButton);
                }

                $this->addHoldButton($holdCnDetailButton);
                $this->addResumeButton($resumeCnDetailButton);
                $this->addRevalueEstimateButton($obj, $addRevEstimateButton);
                
                if (!$isKit) {
                    $this->addConvertPostingTypeCnDetailButton($convertPostingTypeCnDetailButton);
                }

                if ( ContractUtil::isFulfillmentEnabled() ) {
                    if ( $obj['DELIVERYSTATUS'] != 'Delivered' ) {
                        $jsCode = "showDeliverCDLinePage();";
                        $this->createMoreActionEntry(
                            $deliverCnDetailButton, 'dcndetailbuttid', 'dcndetailbutton', 'IA.DELIVER_CONTRACT_LINE',
                            'deliveraction', false, $jsCode, false, true
                        );
                    }
                }
            }
        }
        if (
            $state === Editor_ShowViewState
            && ContractBillingMethodTypes::isProjectTime($obj['BILLINGMETHOD'])
            && !$this->isKitComponent($obj)
        ) {
            if ( ContractUtil::isLineIsReEstimated($obj['RECORDNO']) ) {
                $this->addcancelExpenseButton($cancelExpenseButton);
                $this->addHoldButton($holdCnDetailButton);
                $this->addResumeButton($resumeCnDetailButton);
                $this->addConvertPostingTypeCnDetailButton($convertPostingTypeCnDetailButton);
                $this->addRevalueEstimateButton($obj, $addRevEstimateButton);
            }
        }
        // sequence of array merge to display as in sequence in drop down
        $values = array_merge($addRevEstimateButton, $values);
        $values = array_merge($convertPostingTypeCnDetailButton, $values);
        if ( ! $obj['EXPENSEHOLDDATE'] ) {
            $values = array_merge($cancelExpenseButton, $values);
        }
        if ( ! ( $obj['REVENUEHOLDDATE'] || $obj['EXPENSEHOLDDATE'] || $obj['BILLINGHOLDDATE'] ||
                 ($this->isEvergreenContract() && $obj['CANCELDATE']) ) ) {
            $values = array_merge($cancelCnDetailButton, $values);
        }
        if ( $deliverCnDetailButton ) {
            $values = array_merge($deliverCnDetailButton, $values);
        }
        $values = array_merge($resumeCnDetailButton, $values);
        $values = array_merge($holdCnDetailButton, $values);

        if ( ( $state === Editor_ShowEditState || $state === Editor_ShowViewState )
             && $obj['STATE'] != ContractDetailState::STATE_DRAFT &&
             !ContractDetailManager::isRevRecOnInvoice($obj)
        ) {
            //            $action = $this->createAction(
            //                'percent_b',
            //                Editor_CancelBtnID . '_percent_b',
            //                'Percentage Billing',
            //                'byline',
            //                false,
            //                "renderPercentageView('$state', 'byline');",
            //                false
            //            );
            //            $action['inMoreActions'] = true;
            //
            //            $values[] = $action;

            // Uncomment when we support NEGATIVE_LINE_WITH_MEA

            $obj = Request::$r->GetCurrentObject();
            if ( isset($obj['TOTALFLATAMOUNT']) && $obj['TOTALFLATAMOUNT'] < 0 ) {
                $jsCode = null;
                $lineType = $obj['LINETYPE'];
                $contractLineNo = $obj['LINENO'];
                if ( $lineType === ContractDetailManager::LINETYPE_DEBOOK ) {
                    $msg = "Contract line $contractLineNo is a " . ContractDetailManager::LINETYPE_DEBOOK_LABEL .
                           " line. Only " . ContractDetailManager::LINETYPE_DISCOUNT_LABEL
                           . " line can be distributed.";
                    $jsCode = "showError('$msg');";
                } else if ( $lineType === ContractDetailManager::LINETYPE_DISCOUNT ) {
                    $contractDetailKey = $obj['RECORDNO'];
                    $hasPostedBillingSch = ContractScheduleManager::hasPostedBillingSchedules($contractDetailKey);
                    if ( $hasPostedBillingSch ) {
                        $msg =
                            sprintf(_('The discount associated with contract line %1$s cannot be distributed as line %1$s has one or more invoices posted against it.'),
                                    $contractLineNo);
                        $jsCode = "showError('$msg');";
                    }
                }
                if ( $jsCode === null ) {
                    $jsCode = "renderNegativeBillingView('$state', 'byline');";
                }
                $action = $this->createAction(
                    'negative_b',
                    Editor_CancelBtnID . '_negative_b',
                    'IA.DISTRIBUTE_DISCOUNT',
                    'byline',
                    false,
                    $jsCode,
                    false
                );
                $action['inMoreActions'] = true;

                $values[] = $action;
            }

            // if cancelled yet undelivered, we need to allow delivery
            if ( ContractUtil::isFulfillmentEnabled() ) {
                if ( $obj['STATE'] == 'Cancelled' && $obj['DELIVERYSTATUS'] != 'Delivered' ) {
                    $deliverCnDetailButton = [];
                    $jsCode = "showDeliverCDLinePage();";
                    $this->createMoreActionEntry(
                        $deliverCnDetailButton, 'dcndetailbuttid', 'dcndetailbutton', 'IA.DELIVER_CONTRACT_LINE',
                        'deliveraction', false, $jsCode, false, true
                    );
                    $values = array_merge($deliverCnDetailButton, $values);
                }
            }
        }

        if (!$isKitComponent) {
            // Add uncancel-link to More-Actions menu.
            $key = $this->getObjectRecordNo();
            if ($key) {
                $result = EntityManager::GetListQuick('contractdetail', ['STATE', 'CANCELDATE'], ['RECORDNO' => $key]);
            }
            $cnDetailRecord = $result[0] ?? null;
    
            if ( (isset($cnDetailRecord['STATE']) && $cnDetailRecord['STATE'] === ContractManager::STATE_IN_CANCELLED && ContractUtil::hasUncancelAccess()) ||
                 ($this->isEvergreenContract() && !empty($cnDetailRecord['CANCELDATE']) &&
                  $cnDetailRecord['STATE'] !== ContractDetailState::STATE_CLOSED)) {
                // Add uncancel-link to More-Actions menu,
                // only if contract-detail is in cancelled state.
        
                $jsCode = "window.editor.showPage('uncancelCDetailPage', this)";
                $uncancelCnDetailButton = [];
                $this->createMoreActionEntry(
                    $uncancelCnDetailButton, 'unccndetailbuttid', 'unccndetailbutton',
                    'IA.UNCANCEL_CONTRACT_LINE', 'uncancellation', false, $jsCode, false, true
                );
        
                $values = array_merge($uncancelCnDetailButton, $values);
            }
        }

        return $values;
    }

    /**
     * @param array &$resumeCnDetailButton
     */
    function addResumeButton(&$resumeCnDetailButton)
    {
        if ($this->isEvergreenContract()) {
            return;
        }
        $jsCode = "window.editor.showPage('resumeCDSchedulesPage', this)";
        $this->createMoreActionEntry(
            $resumeCnDetailButton, 'rcndetailbuttid', 'rcndetailbutton', 'IA.RESUME_CONTRACT_LINE', 'resumeaction', false,
            $jsCode, false, true
        );
    }

    /**
     * @param array &$holdCnDetailButton
     */
    function addHoldButton(&$holdCnDetailButton)
    {
        if ($this->isEvergreenContract()) {
            return;
        }
        $jsCode = "window.editor.showPage('holdCDSchedulesPage', this)";
        $this->createMoreActionEntry(
            $holdCnDetailButton, 'hcndetailbuttid', 'hcndetailbutton', 'IA.HOLD_CONTRACT_LINE', 'holdaction', false,
            $jsCode, false, true
        );
    }

    /**
     * @param array &$cancelExpenseButton
     */
    function addcancelExpenseButton(&$cancelExpenseButton)
    {
        if ($this->isEvergreenContract()) {
            return;
        }
        if (ContractUtil::hasCancelAccess()) {
            $jsCode = "renderCNDetailExpenseCancellationView('" . GetOperationId('cn/lists/contract/create') . "');";

            $this->createMoreActionEntry(
                $cancelExpenseButton, 'cexpensebuttid', 'cexpensebutton', 'IA.CANCEL_CONTRACT_LINE_EXPENSES', 'cancellation',
                false, $jsCode, false, true
            );
        }
    }

    /**
     * @param array &$convertPostingTypeCnDetailButton
     */
    function addConvertPostingTypeCnDetailButton(&$convertPostingTypeCnDetailButton)
    {
        if ( ! $this->isEvergreenContract() ) {
            $jsCode = "window.editor.showPage('convertPostingTypePage', this)";
            $this->createMoreActionEntry(
                $convertPostingTypeCnDetailButton, 'rcndetailbuttid', 'rcndetailbutton',
                'IA.CONVERT_CONTRACT_LINE_POSTING_TYPE_TO_AUTOMATIC', 'convertpostingtypeaction',
                false, $jsCode, false, true
            );
        }
    }

    /**
     * @param array $obj
     * @param array $addRevEstimateButton
     *
     * @throws IAException
     */
    function addRevalueEstimateButton($obj, &$addRevEstimateButton)
    {
        if ( ContractBillingMethodTypes::isProjectTime($obj['BILLINGMETHOD']) ) {
            $jsCode = "window.editor.showPage('completeCDetailPage', this)";
            $this->createMoreActionEntry(
                $addRevEstimateButton, 'comcndetailbuttid', 'comcndetailbutton', 'IA.REVALUE_ESTIMATION', 'complete',
                false, $jsCode, false, true
            );
        }
    }

    /**
     * getEditorAction
     *
     * @return string $action action
     */
    function getEditorAction()
    {
        $action = Request::$r->{$this->kAction};
        if ( ! $action ) {
            $kVerb = Globals::$g->kVerb;
            $action = $this->kDefaultVerbActions[Request::$r->$kVerb];
        }

        //new => (Add, Error go back), edit => (Edit, Error go back), view => (View), copy => (Duplicate), create => (Save and New)
        return $action;
    }

    /**
     * @return bool
     */
    private function isRenewalOnlyContract()
    {
        $ret = false;
        $obj = &Request::$r->GetCurrentObject();
        if ( $obj['STATE'] == ContractDetailState::STATE_RENEWALONLY ) {
            $ret = true;
        }

        return $ret;
    }

    /**
     * Editor use Contract information in multiple places. Store this for re-use.
     *
     * @param string $contractid
     */
    private function setContractValues($contractid)
    {
        if ( $contractid != '' ) {
            $conMgr = Globals::$g->gManagerFactory->getManager('contract');
            //Since we need to get the custom dimensions, 'selects' should be empty
            $res = $conMgr->GetList([
                                        'filters' => [ [ [ 'CONTRACTID', '=', $contractid ] ] ],
                                    ]);
            $this->contractValues = $res[0];
        }
    }

    /**
     * findAndSetExchRate
     *
     * @param array $obj
     */
    private function findAndSetExchRate(&$obj)
    {
        if ( $obj['EXCHANGE_RATE'] == '' && $obj['EXCH_RATE_DATE'] != ''
             && $this->contractValues['EXCHRATETYPE'] != '' ) {
            if ( $this->contractValues['CURRENCY'] === $this->contractValues['BASECURR'] ) {
                $obj['EXCHANGE_RATE'] = 1;
            } else {
                $exchRateMgr = Globals::$g->gManagerFactory->getManager('exchangerate');
                $exch_rate = $exchRateMgr->GetTrxExchangeRateByTypeID(
                    $this->contractValues['EXCH_RATE_TYPE_ID'], $this->contractValues['CURRENCY'],
                    $this->contractValues['BASECURR'], $obj['EXCH_RATE_DATE']
                );
                if ( $exch_rate != '' ) {
                    $obj['EXCHANGE_RATE'] = $exch_rate;
                }
            }
        }
        if ( ! isArrayValueProvided($obj, 'EXCHRATETYPE')
             && isArrayValueProvided($this->contractValues, 'EXCHRATETYPE') ) {
            $obj['EXCHRATETYPE'] = $this->contractValues['EXCHRATETYPE'];
        }
    }

    /**
     * PrepareInputValues
     *    override parent, mainly for unformatting display values
     *
     * @param array $_params input parameters
     * @param array $_obj    object data
     *
     * @return bool
     */
    protected function prepareInputValues(&$_params, &$_obj)
    {
        $ok = parent::PrepareInputValues($_params, $_obj);

        /**
         *  If the TEMPLATENAME field is not present in $_obj, ContractDetailManager is going to
         *   default the values from its 'ITEMID'. To skip this step for FormEditor, set the field with an empty value.
         */
        $templateNameFields = [ 'REVENUETEMPLATENAME', 'REVENUE2TEMPLATENAME', 'BILLINGTEMPLATENAME' ];
        foreach ( $templateNameFields as $flds ) {
            if ( ! array_key_exists($flds, $_obj) ) {
                $_obj[$flds] = '';
            }
        }

        return $ok;
    }

    /**
     * defaultDimensionValues
     *
     * @param array $obj
     */
    private function defaultDimensionValues(&$obj)
    {
        if ( empty($obj['LOCATIONID']) ) {
            $obj['LOCATIONID'] = $this->contractValues['LOCATIONID'];
        }
        if ( empty($obj['DEPARTMENTID']) ) {
            $obj['DEPARTMENTID'] = $this->contractValues['DEPARTMENTID'];
        }

        $dimFields = IADimensions::getAllDimensionObjectProperties(! util_isPlatformDisabled());
        $dimUsed = IADimensions::getDimensionsOrder();
        if ( ! util_isPlatformDisabled() ) {
            $platformDef = Pt_StandardUtil::getObjDef('contract');
            $platformRels = Pt_StandardUtil::getRelationshipFields($platformDef);
            $this->setPlatformRelsAtrribute($platformRels, $this->contractValues);
        }

        foreach ( $dimFields as $key => $field ) {
            if ( ! in_array($key, $dimUsed) ) {
                continue;
            }

            if ( $field['standard'] ) {
                $dimfkid = strtoupper($field['dimfkid']);
                if ( empty($obj[$dimfkid]) ) {
                    $obj[$dimfkid] = $this->contractValues[$dimfkid];
                }
            } else {
                $path = 'GLDIM' . str_replace(" ", "_", strtoupper($field['fullname']));
                $dispPath = $path . '_disp';
                if ( empty($obj[$path]) ) {
                    $obj[$path] = $this->contractValues[$path];
                    $obj[$dispPath] = $this->contractValues[$dispPath];
                }
            }
        }
    }

    private function fetchDefaultItemInformation()
    {
        $componentLines = [];
        $itemId = Request::$r->itemId;
        $itemId = explode('--', $itemId);

        if (!empty($itemId[0])) {
            $itemId = $itemId[0];
            $res = EntityManager::GetListQuick(
                'item',
                ['EXTENDED_DESCRIPTION', 'DEFCONTRACTDELIVERYSTATUS', 'DEFCONTRACTDEFERRALSTATUS', 'COMPUTEFORSHORTTERM', 'ITEMTYPE'],
                ['ITEMID' => $itemId]
            );

            if (!empty($res)) {
                $res = $res[0];
                $res['ITEMID'] = $itemId;
                $componentLines = $this->entityMgr->generateKitComponentLines($res);
            }
        } else {
            $res = [];
        }

        $defaultItemDescription = $res['EXTENDED_DESCRIPTION'] ?? '';

        $deliveryStatus =
            (!empty($res['DEFCONTRACTDELIVERYSTATUS']))
                ? $res['DEFCONTRACTDELIVERYSTATUS'] : ContractDetailDeliveryStatus::DELIVERED;

        $deferralStatus =
            (!empty($res['DEFCONTRACTDEFERRALSTATUS']))
                ? $res['DEFCONTRACTDEFERRALSTATUS'] : ContractDetailRevenueDeferralStatus::DEFER_UNTIL_ITEM;

        $prorateBillingEnabled = $res['COMPUTEFORSHORTTERM'] ?? 'false';

        $itemType = $res['ITEMTYPE'] ?? '';

        $results = [
            'ITEMDESC' => $defaultItemDescription,
            'DELIVERYSTATUS' => $deliveryStatus,
            'DEFERRALSTATUS' => $deferralStatus,
            'ITEMTYPE' => $itemType,
            'prorateBillingEnabled' => $prorateBillingEnabled,
            'COMPONENTLINES' => $componentLines,
        ];

        echo json_encode($results);
    }

    /**
     * Retrieves contract line's expenses for display thru Ajax
     */
    protected function getActiveExpenses()
    {
        $selects = [
            'RECORDNO', 'LINENO', 'ITEMID', 'ITEMNAME', 'STATE',
        ];

        $filters = [
            [ 'CONTRACTDETAILKEY', '=', Request::$r->contractDetailKey ],
            [ 'STATE', '!=', ContractExpenseState::STATE_CANCELLED ],
        ];

        $cnExpenseMgr = Globals::$g->gManagerFactory->getManager('contractexpense');

        $querySpec = [
            'selects' => $selects,
            'filters' => [
                $filters,
            ],
            'orders'  => [
                [ 'LINENO', 'asc' ],
            ],
        ];

        $obj['CANCELEXPENSEPAGE']['CANCEL_EXPENSES'] = $cnExpenseMgr->GetList($querySpec);
        echo json_encode($obj);
    }

    /**
     * @return true
     */
    protected function isTransactionObject()
    {
        return true;
    }

    /**
     * All dimensions are available
     *
     * @return array
     */
    protected function getPTNotSupportedDimensionFieldNames()
    {
        return [];
    }

    /**
     * Return map of standard dimension field name to field name used in current transaction page
     *
     * @param string $prefix
     *
     * @return array
     */
    protected function getPTDimensionFieldNameTranslationMap($prefix = '')
    {
        $map = parent::getPTDimensionFieldNameTranslationMap();
        if ( $map == null ) {
            $map = [];
        }

        $map['LOCATIONID'] = 'LOCATION';
        $map['DEPARTMENTID'] = 'DEPARTMENT';

        return $map;
    }

    /**
     * @return ContractDetailManager
     */
    public function getEntityMgr()
    {
        assert($this->entityMgr instanceof ContractDetailManager);

        return $this->entityMgr;
    }

    /**
     * @param array $values
     */
    private function maybeAppendContractValues(&$values)
    {
        if ( ! isSubArrayValueProvided($values, 'CONTRACT', 'CONTRACTID')
             && isArrayValueProvided($this->contractValues, 'CONTRACTID') ) {
            $values['CONTRACT'] = $this->contractValues;
        }
    }

    /**
     * @return string[]
     */
    private function findExpenseFieldsToQuery()
    {
        $fieldsToQuery = [
            'RECORDNO',
            'SCHEDULEKEY',
            'SCHEDULESTATUS',
            'SCHEDULE2KEY',
            'SCHEDULE2STATUS',
            'STATE',
        ];

        /** @var EditorGrid[] $expenseGrids */
        $this->getView()
             ->findComponents([ 'path' => 'CONTRACTDETAIL_EXPENSEDETAIL' ], EditorComponentFactory::TYPE_GRID,
                              $expenseGrids);
        assert(isset($expenseGrids[0]));
        /** @var EditorField[] $expenseFields */
        $expenseGrids[0]->findComponents(null, EditorComponentFactory::TYPE_FIELD, $expenseFields);
        foreach ( $expenseFields as $expenseField ) {
            $isHTML = $expenseField->getProperty('isHTML', false);
            if ( $isHTML !== true && $isHTML !== '1' && $isHTML !== 'true' ) {
                $fieldsToQuery[] = $expenseField->getProperty('path', false);
            }
        }

        return $fieldsToQuery;
    }

    /**
     * @param array  $searchParams
     * @param array  $propVals
     * @param string $type
     *
     * @desc this method used to remove some value from defined property, eg : from billing method list want to remove
     *       some billin method
     */
    public function findAndRemoveEnumValue($searchParams, $propVals, $type = EditorComponentFactory::TYPE_FIELD)
    {
        $matches = [];
        $this->getView()
             ->findComponents($searchParams, $type, $matches);
        foreach ( $matches as $match ) {
            // $propvals is 'type' data of property
            foreach ( $propVals as $property => $value ) {
                $propertyData = $match->getProperty($property);
                $finalProvertyValue = [];
                // checking $propertyData is typeof enum or not
                if ( $propertyData['ptype'] == 'enum' ) {
                    // $value is key in 'type' array , eg validlabels , validvalues , _validivalues
                    // $data is  list of validlables or validvalue or validvalues we want to remove
                    foreach ( $value as $key => $data ) {
                        if ( isset($propertyData[$key]) ) {
                            $finalProvertyValue[$key] = array_diff($propertyData[$key], $data); // filtering the values
                            array_splice($finalProvertyValue[$key], 0, 0);
                        }
                    }
                }
                if ( ! empty($finalProvertyValue) ) {
                    $match->setProperty($property, $finalProvertyValue);
                }
            }
        }
    }

    private function ajaxCompleteContractLine()
    {
        $contractDetailKey = Request::$r->contractDetailKey;
        $reEstimate = Request::$r->reEstimate;
        $price = Request::$r->rate;
        $multiplier = Request::$r->multiplier;
        $quantity = Request::$r->quantity;
        $discount = Request::$r->discountpercent;
        $flatamount = Request::$r->flatamount;
        $completedDate = Request::$r->completedDate;

        if ( ! ( $contractDetailKey && $completedDate ) ) {
            Globals::$g->gErr->addError('CN-0036', __FILE__ . ':' . __LINE__,
                                        'Contract detail key and completed date are required.');
            echo json_encode(false);

            return;
        }
        try {
            $reEstimate = ( $reEstimate == 'true' ) ? true : false;
            $cnEstimateRevaluationDetailHandler =
                new ContractDetailEstimateRevaluation($contractDetailKey, $completedDate, $reEstimate);
            $ok = $cnEstimateRevaluationDetailHandler->processRevaluationAndReEstimate(
                $contractDetail,
                $quantity,
                $price,
                $multiplier,
                $discount,
                $flatamount
            );
        } catch ( IAException $ex ) {
            ContractUtil::addThrowableError($ex, __FILE__ . ':' . __LINE__);
            $ok = false;
        }
        $response = [ 'status' => $ok ];
        if ( $ok ) {
            $response['revaluedContractDetailKey'] = $contractDetailKey;
            if ( $reEstimate == 'true' and ! empty($contractDetail) ) {
                $response ['reEstimatedContractDetailKey'] = $contractDetail['RECORDNO'];
                $response ['amount'] = $contractDetail['FLATAMOUNT'];
            }
        }
        if ( ! $ok ) {
            $response['error'] = Globals::$g->gErr->myToString(false);
        }
        echo json_encode($response);
    }

    /**
     * Retrieves aggregate trasaction records for display thru Ajax
     */
    protected function getProjectTimeAggHistory()
    {
        $obj = [];
        // idea behind this filter is to display ony one data per transaction
        // as in transaction tab its showing multiple row for transaction
        // we will use same get history method , but we will filter like that so it will give one entry per schedule billing entry of
        // or contractusagebillingid
        $filters = [
            'CONTRACTDETAILKEY' => Request::$r->contractDetailKey,
            'EVENTTYPE'         => ContractGLReclassEvent::EVENTTYPE_ONINVOICE,
            'TR_TYPE'           => 1,
            'CLASSIFICATION'    => 'Billed',
            'BALANCETYPE'       => 'Revenue',
            'TYPE'              => 'Adjust',

        ];
        $contractDetail = $this->getCnDetailData();
        // any one journal type is require to fetch data from contract resolve to  display projectime history
        if ( ! empty($contractDetail['0']['REVENUETEMPLATEKEY']) ) {
            $journalType = ContractGLReclassEvent::JOURNALTYPE_J1;
        } else {
            $journalType = ContractGLReclassEvent::JOURNALTYPE_J2;
        }
        $obj['PROJECT_TIME_AGG_HISTORY'] = self::getProjectTimeHistory(Request::$r->contractDetailKey);
        // fetching adjut seperatly and merging with above history
        $obj['ADJUST_PROJECT_TIME_AGG_HISTORY'] = ContractEditor::getHistory($journalType, $filters);

        if ( ! empty($obj['ADJUST_PROJECT_TIME_AGG_HISTORY']) ) {
            $obj['PROJECT_TIME_AGG_HISTORY'] =
                INTACCTarray_merge($obj['PROJECT_TIME_AGG_HISTORY'], $obj['ADJUST_PROJECT_TIME_AGG_HISTORY']);
        }
        ContractGenInvoiceUtil::getAggregateLineHtmlLink($obj);
        //logtofile(pp($obj) . "\n");
        echo json_encode($obj);
    }

    /**
     * it will store cn detail data
     *
     * @return array
     */
    private function getCnDetailData()
    {
        if ( empty($this->contractDetailData) ) {
            $contractDetailKey = Request::$r->{Globals::$g->kId};
            assert($contractDetailKey, "Contract Detail Key is required.");
            $contractDetail = EntityManager::GetListQuick(
                'contractdetail', [ 'RECORDNO', 'REVENUETEMPLATEKEY', 'REVENUE2TEMPLATEKEY', 'BILLINGMETHOD', 'STATE',
                                    'RENEWALBILLINGTEMPLATENAME', 'RENEWALBILLINGTEMPLATEKEY', 'RENEWAL', 'CONTRACTID' ],
                [ 'RECORDNO' => $contractDetailKey ]
            );

            $this->contractDetailData = $contractDetail;
        }

        return $this->contractDetailData;
    }

    /**
     * @param int $cnDetailKey
     *
     * @return array
     */
    private function getCurrentStates($cnDetailKey)
    {
        $cnDetail =
            EntityManager::GetListQuick('contractdetail', [ 'STATE', 'CONTRACTSTATE' ], [ 'RECORDNO' => $cnDetailKey ]);

        return [ 'STATE' => $cnDetail[0]['STATE'], 'CONTRACTSTATE' => $cnDetail[0]['CONTRACTSTATE'] ];
    }

    /**
     * @param string $contractId
     *
     * @return string|null
     */
    private function getContractState($contractId)
    {
        $cn = EntityManager::GetListQuick('contract', [ 'STATE' ], [ 'CONTRACTID' => $contractId ]);

        return $cn[0]['STATE'] ?? null;
    }

    /**
     * Get global hidden fields
     *
     * @return array
     */
    protected function getGlobalHiddenFields()
    {
        $fields = parent::getGlobalHiddenFields();
        $contractid = Request::$r->_contractid;
        if ($contractid) {
            $fields['.contractid'] = $contractid;
            $fields['urlParameters'][] = '.contractid';
            $entryPath = Request::$r->_entrypath;
            if ($entryPath === 'cdlist') {
                $fields['.entrypath'] = $entryPath;
                $fields['urlParameters'][] = '.entrypath';
            }
        }
        return $fields;
    }

    private function ajaxContractDetailHasMEA()
    {
        $contractDetailKey = Request::$r->contractDetailKey;
        $revaluationDate = Request::$r->revaluationDate;
        $meaName = ContractUtil::getActiveMEANameOfContractDetail($contractDetailKey, $revaluationDate);
        if ( ! empty($meaName) ) {
            $hasMEA = true;
            $result['meaName'] = $meaName;
        } else {
            $hasMEA = false;
        }
        $result['hasMEA'] = $hasMEA;
        echo json_encode($result);
    }

    /**
     * Get projected prorate billing data.
     * Calculate flat amount multiplier, prorated amount and base amount.
     */
    private function ajaxGetProrateBillingData()
    {
        // Contract detail API request params.

        $values = [];
        $values['CONTRACTKEY'] = Request::$r->contractKey;
        $values['CONTRACTID'] = Request::$r->contractId;
        $values['CONTRACTBEGINDATE'] = Request::$r->contractBeginDate;
        $values['CONTRACTENDDATE'] = Request::$r->contractEndDate;
        $values['BEGINDATE'] = Request::$r->beginDate;
        $values['ENDDATE'] = Request::$r->endDate;
        $values['EXCHANGE_RATE'] = Request::$r->exchangeRate;
        $values['FLATAMOUNT'] = Request::$r->flatAmount;
        $values['BASEFLATAMOUNT'] = Request::$r->baseFlatAmount; // Required for MCP company.
        $values['PRORATEBILLINGPERIOD'] = Request::$r->prorateBillingPeriod;
        $values['BILLINGFREQUENCY'] = Request::$r->billingFrequency;
        $values['ADVBILLBY'] = Request::$r->advBillBy;
        $values['ADVBILLBYTYPE'] = Request::$r->advBillByType;
        $values['CONTRACT']['TERMTYPE'] = Request::$r->termType;

        $cnDetailMgr = Globals::$g->gManagerFactory->getManager('contractdetail');
        $prorateBillData = $cnDetailMgr->getProrateBillingData($values);

        // Build JSON response.

        $jsonRes = [];
        $jsonRes['flatAmountMultiplier'] = $prorateBillData['flatAmountMultiplier'] ?? '--';
        $jsonRes['proratedTotalFlatAmount'] = $prorateBillData['proratedTotalFlatAmount'] ?? '--';
        $jsonRes['proratedTotalFlatBaseAmount'] = $prorateBillData['proratedTotalFlatBaseAmount'] ?? '--';

        echo json_encode($jsonRes);
    }

    /**
     * Echos default MRR change type for saas metrics - contract integration
     */
    private function ajaxGetDefaultMRRChangeType()
    {
        $itemId = Request::$r->itemId;
        $itemIdArr = explode('--', $itemId);
        $itemId = $itemIdArr[0];

        $beginDate = Request::$r->beginDate;
        if ( $beginDate ) {
            $beginDate = FormatDateForStorage($beginDate);
        }

        $lineTypeValue = Request::$r->lineType;

        $defaultMRRChangeType = '';
        if ($itemId) {
            $items = EntityManager::GetListQuick('item', [ 'MRR', 'ITEMTYPE' ], [ 'ITEMID' => $itemId ]);
            $item = $items[0] ?? null;
            if ($item) {
                $itemMRR = ($item['MRR'] == 'true' && $item['ITEMTYPE'] !== ContractDetailManager::ITEM_TYPE_KIT) ? true : false;
                $contractIdArr = explode('--', Request::$r->contractId);
                $contractId = $contractIdArr[0];
                $contract = EntityManager::GetListQuick('contract', [ 'BEGINDATE','PARENTKEY' ], [ 'CONTRACTID' => $contractId ]);
                if ($contract && $contract[0]) {
                    $defaultMRRChangeType = SaaSMetricsContractIntegration::processDefaultMRRChangeType(
                        $contract[0]['BEGINDATE'],
                        $beginDate,
                        $lineTypeValue,
                        $itemMRR,
                        $contract[0]['PARENTKEY']
                    );
                }

            }
        }

        echo '"' . $defaultMRRChangeType . '"';
    }


    /**
     * @param int $contractDetailkey
     *
     * @return false|string[][]
     */
    public static function getProjectTimeHistory($contractDetailkey)
    {
        $query = "select d.docid ,cse.actualpostingdate as RESOLVETRANSACTIONDATE , 'Time' as RESOLVETYPE , gl.record# GENINVOICELINEKEY, cse.record# as BILLINGSCHENTRYKEY , cse.amount as amount, cse.postedbaseamount as RESOLVEBASEAMOUNT from contractschedule cs inner join contractscheduleentry
                    cse on cs.cny#=cse.cny# and cs.record#=cse.schedulekey and cs.type = 'B'
                    inner join geninvoiceline gl on gl.cny#=cse.cny# and gl.cnbillingschentrykey = cse.record#
                    inner join docentry de on de.cny# = cse.cny# and de.billablecontractschentrykey = cse.record#
                    inner join dochdr d on d.cny# = de.cny# and de.dochdrkey = d.record#
                     where cs.cny# = :1 and cs.contractdetailkey = :2 and cse.posted='T' order by cse.actualpostingdate asc";
        $result = QueryResult([ $query, GetMyCompany(), $contractDetailkey ]);

        return $result;
    }

    /**
     * Configure the contacts for refresh
     *
     * @param array &$obj          data the transaction data
     * @param array  $contactTypes the contact types (i.e. SHIPTO, BILLTO)
     */
    protected function configureContactsForRefresh(&$obj, $contactTypes)
    {
        $contactMgr = Globals::$g->gManagerFactory->getManager('contact');

        foreach ( $contactTypes as $prefix ) {
            $namePath = $prefix . 'CONTACTNAME';
            // No contact ? No need to continue
            if ( empty($obj[$namePath]) ) {
                continue;
            }

            $keyPath = $prefix . 'KEY';
            if ( ! empty($obj[$keyPath]) ) {
                // Figure out if the contact has changed or not.
                $contactVersion = $contactMgr->GetCurrentVersionNo(
                    $obj[$namePath]
                );
                if ( isset($contactVersion) && $contactVersion != $obj[$keyPath] ) {
                    $latestFlagPath = 'HASLATEST' . $prefix;
                    $obj[$latestFlagPath] = true;
                }
            }
        }
    }

    /**
     * @param string $contractKey
     * @param string|null $originalKey
     * @param array $contractDetailFields
     * @return array
     */
    private function getContractDetailDataFiltered(
        string $contractKey,
        ?string $originalKey,
        array $contractDetailFields
    ) : array
    {
        $filters = [[
            'operator' => 'AND',
            'filters' => [
                ['CONTRACTKEY', '=', $contractKey],
                [
                    [
                        'fields' => ['RENEWEDORIGINALDETAILKEY', 'RECORDNO'],
                        'function' => 'nvl(${1},${2})'],
                    '=',
                    $originalKey
                ],
                ['STATE', '!=', ContractDetailState::STATE_RENEWAL_FORECAST],
            ],
        ]];

        $conObjHandler = new ContractObjHandler();

        return $conObjHandler->getContractDetailDataByFilterByConditions($filters, $contractDetailFields);
    }

    /**
     * @return string[]
     */
    private function findContractDetailFieldsToQuery() : array
    {
        $fieldsToQuery = [
            'RECORDNO',
            'CONTRACTKEY',
            'PERIOD',
            'RENEWEDORIGINALDETAILKEY',
        ];

        $fieldsInGrid = $this->findFieldsInGrid('RECURRENCE');

        $cnDetailMgr = Globals::$g->gManagerFactory->getManager('contractdetail');
        $allFieldPaths = $cnDetailMgr->GetGetFields();

        $fieldsInGrid = array_intersect($fieldsInGrid, $allFieldPaths);
        $fieldsToQuery = appendUnique($fieldsToQuery, $fieldsInGrid);

        return $fieldsToQuery;
    }

    /**
     * @param string $gridPath
     *
     * @return string[]
     */
    private function findFieldsInGrid(string $gridPath) : array
    {
        $fieldsInGrid = [];
        /** @var EditorGrid[] $editorGrids */
        $this->getView()->findComponents([ 'path' => $gridPath ], EditorComponentFactory::TYPE_GRID, $editorGrids);
        assert(isset($editorGrids[0]));
        /** @var EditorField[] $editorFields */
        $editorGrids[0]->findComponents(null, EditorComponentFactory::TYPE_FIELD, $editorFields);
        foreach ( $editorFields as $editorField ) {
            $isHTML = $editorField->getProperty('isHTML', false);
            if ( $isHTML !== true && $isHTML !== '1' && $isHTML !== 'true' ) {
                $fieldsInGrid[] = $editorField->getProperty('path', false);
            }
        }
        return $fieldsInGrid;
    }

    /**
     * @param array $obj
     */
    private function setAllTotalAmount(array &$obj) : void
    {
        if (empty($obj['RENEWEDORIGINALDETAILKEY'])) {
            $key = $obj['RECORDNO'];
        } else {
            $key = $obj['RENEWEDORIGINALDETAILKEY'];
        }
        if (empty($key)) {
            $obj['EVGTOTALFLATAMOUNT'] = glFormatCurrency('0') . ' / ' . glFormatCurrency('0');
        } else {
            $resTotal = $this->entityMgr->GetList(
                [
                    'selects' => [['fields' => ['TOTALFLATAMOUNT'], 'function' => 'sum(${1})']],
                    'columnaliases' => ['AMOUNT'],
                    'filters' => [[
                        [[ 'fields' => [ 'RENEWEDORIGINALDETAILKEY', 'RECORD#' ], 'function' => "nvl(\${1}, \${2})" ], '=', $key],
                        ['STATE', '!=', ContractDetailState::STATE_RENEWAL_FORECAST]
                    ]],
                ]
            );
            $obj['EVGTOTALFLATAMOUNT'] = glFormatCurrency($obj['TOTALFLATAMOUNT'] ?: '0') . ' / ' . glFormatCurrency($resTotal[0]['AMOUNT'] ?: '0');
        }
    }

    /**
     * @param array $obj
     *
     * @return void
     */
    private function addDerivedFields(&$obj): void
    {
        if (empty($obj['LINENO'])) {
            $res = EntityManager::GetListQuick(
                'contractdetail',
                ['LINENO', 'PARENTKEY', 'ITEMTYPE', 'PARENTITEMTYPE'],
                ['RECORDNO' => $obj['RECORDNO']]
            );

            $obj = array_merge($obj, $res[0] ?? []);
        }
    }

    private function constructEntityLink(string $opKey, string $key, string $entity, string $token): string
    {
        $sess = Session::getKey();
        $opId = GetOperationId($opKey);
        $label = GT($this->textMap, $token);

        $url = "editor.phtml?.op=$opId&.sess=$sess&.r=$key&.popup=1";
        return "<a href=\"javascript:Launch('$url','$entity', 1200, 600, true);\" target1=\"_blank\">$label</a>";
    }

    /**
     * This function is executed when a create action is performed
     *
     * @param array $_params    the metadata
     * @param array $obj        the data
     * @param bool  $ok         false if error else true
     *
     * @return bool             false if error else true
     */
    protected function innerProcessCreateAction(&$_params, &$obj, $ok)
    {
        $ok = $ok && parent::innerProcessCreateAction($_params, $obj, $ok);
        if ($ok && ContractUtil::isPreviewBillingScheduleFlow($obj)) {
            // capture the contractdetail record number so it can later be used to retrieve the associated billing
            // schedule
            $this->previewRecordNo = $obj['RECORDNO'];
        }
        
        return $ok;
    }

    /**
     * @param array  $_params
     * @param array  $obj
     * @param array  $origValues
     * @param string $entityDesc
     * @param bool   $ok
     *
     * @return bool
     */
    protected function innerFinishCreateAction(&$_params, &$obj, $origValues, $entityDesc, $ok)
    {
        // For the preview billing schedule context, need to override the entity description otherwise the
        // errors or messages could be misleading in specifying contract detail record was not created
        if (ContractUtil::isPreviewBillingScheduleFlow($obj)) {
            return parent::innerFinishCreateAction($_params, $obj, $origValues,
                                                   GT($this->textMap, 'IA.PREVIEW_BILLING_SCHEDULE'), $ok);
        } else {
            return parent::innerFinishCreateAction($_params, $obj, $origValues, $entityDesc, $ok);
        }
    }

    /**
     * @param array $_params
     * @param array $obj
     *
     * @return bool
     */
    protected function innerProcessEditAction(/** @noinspection PhpUnusedParameterInspection */ &$_params,
        /** @noinspection PhpUnusedParameterInspection */ &$obj)
    {
        $ok = parent::innerProcessEditAction($_params, $obj);
        if (ContractUtil::isPreviewBillingScheduleEnabled()) {
            $this->setOrigValues($obj);
            // check if the billing schedule is in a state that supports previewing; if so add flag to indicate this
            // instead of dynamically querying each time contract line details change on the fly on form
            if (ContractScheduleManager::isBillingSchedulePreviewable($obj['RECORDNO'])) {
                $obj[':enablePreviewBillingSchedule'] = true;
            }
        }
        return $ok;
    }

    /**
     * @param array $_params
     * @param array $obj
     * @param bool  $ok
     *
     * @return bool
     */
    protected function innerProcessSaveAction(&$_params, &$obj, $ok)
    {
        // For the preview billing schedule context, need to override the entity description otherwise the
        // errors or messages could be misleading in specifying contract detail record was not saved
        if ($ok && ContractUtil::isPreviewBillingScheduleFlow($obj)) {
            $_params['entityDesc'] = GT($this->textMap, 'IA.PREVIEW_BILLING_SCHEDULE');
        }

        $ok = $ok && parent::innerProcessSaveAction($_params, $obj, $ok);

        // capture the contractdetail record number so it can later be used to retrieve the associated billing schedule
        if ($ok && ContractUtil::isPreviewBillingScheduleFlow($obj)) {
            $this->previewRecordNo = $obj['RECORDNO'];
        }

        return $ok;
    }

    /**
     * Set the original values in values array for reference
     *
     * @param array $values
     */
    private function setOrigValues(&$values)
    {
        $values['ORIG_GLPOSTINGDATE'] = $values['GLPOSTINGDATE'];
        $values['ORIG_BILLINGMETHOD'] = $values['BILLINGMETHOD'];
        $values['ORIG_BILLINGOPTIONS'] = $values['BILLINGOPTIONS'];

        // one time and include with every invoice related
        $values['ORIG_BEGINDATE'] = $values['BEGINDATE'];
        $values['ORIG_ENDDATE'] = $values['ENDDATE'];

        // billing template related
        $values['ORIG_BILLINGTEMPLATENAME'] = $values['BILLINGTEMPLATENAME'];
        $values['ORIG_BILLINGSTARTDATE'] = $values['BILLINGSTARTDATE'];
        $values['ORIG_BILLINGENDDATE'] = $values['BILLINGENDDATE'];

        // include with every invoice related
        $values['ORIG_BILLINGFREQUENCY'] = $values['BILLINGFREQUENCY'];
        $values['ORIG_PRORATEBILLINGPERIOD'] = $values['PRORATEBILLINGPERIOD'];

        $values['ORIG_FLATAMOUNT'] = $values['FLATAMOUNT'];
    }

    /**
     * Gets the preview schedule data
     */
    private function ajaxGetPreviewBillingScheduleData()
    {
        $previewBillingData = [];
        $previewContext = Request::$r->previewContext;

        $source = 'ContractDetailEditor::ajaxGetPreviewBillingScheduleData';
        $entityMgr = $this->getEntityMgr();
        $ok = $entityMgr->beginTrx($source);

        // Create the contract line just for the purpose of creating the billing schedule
        $this->extractLayout();  // called just to trigger population of $this->_params metadata in the base class...
        $params = $this->GetTemplate();

        if ($previewContext == 'create') {
            $ok = $ok && $this->ProcessCreateAction($params);
        } else if ($previewContext == 'edit') {
            $ok = $ok && $this->ProcessSaveAction($params);
        } else {
            $ok = false;
        }

        // Get the billing schedule
        if ($ok && $this->previewRecordNo) {
            $result = EntityManager::GetListQuick($this->getEntity(), ['BILLINGSCHEDULEKEY'],
                                                  ['RECORDNO' => $this->previewRecordNo]);
            if ( $result[0]['BILLINGSCHEDULEKEY'] ) {
                $billingScheduleKey = $result[0]['BILLINGSCHEDULEKEY'];
                $scheduleMgr = Globals::$g->gManagerFactory->getManager('contractbillingschedule');
                $previewBillingData = $scheduleMgr->get($billingScheduleKey);
            }
        }
        // always rollback the txn back since we are just calling create to get a preview of the billing schedule
        $entityMgr->rollbackTrx($source);

        $response = $this->createPreviewResponse($previewBillingData);
        if (!$ok) {
            $cdErrors = Globals::$g->gErr->getCDescriptionErrors();
            foreach ($cdErrors as $error) {
                $response['ERRORS'][] = $error;
            }
        }

        echo json_encode($response);
    }

    /**
     * Creates the preview response for the AJAX preview request
     *
     * @param $previewBillingData
     *
     * @return array
     */
    private function createPreviewResponse($previewBillingData)
    {
        // calculate the entries total and base total for display
        $billSchedManager = Globals::$g->gManagerFactory->getManager('contractbillingschedule');
        $billSchedManager->getEntriesTotal($previewBillingData, $total, $baseTotal);

        $response['PREVIEW_BILLING_SCHEDULE_PAGE']['CONTRACTID'] = $previewBillingData['CONTRACTID'];
        $response['PREVIEW_BILLING_SCHEDULE_PAGE']['BILLINGTEMPLATENAME'] =
            $previewBillingData['CONTRACTDETAIL']['TEMPLATENAME'];
        $response['PREVIEW_BILLING_SCHEDULE_PAGE']['UIAMOUNT'] = $total;
        $response['PREVIEW_BILLING_SCHEDULE_PAGE']['UIBASEAMOUNT'] = $baseTotal;
        $response['PREVIEW_BILLING_SCHEDULE_PAGE']['BEGINDATE'] = $previewBillingData['BEGINDATE'];
        $response['PREVIEW_BILLING_SCHEDULE_PAGE']['ENDDATE'] = $previewBillingData['ENDDATE'];
        $response['PREVIEW_BILLING_SCHEDULE_PAGE']['CNDETAILGLPOSTINGDATE'] = $previewBillingData['CNDETAILGLPOSTINGDATE'];
        $response['PREVIEW_BILLING_SCHEDULE_PAGE']['SCHEDULEENTRY'] = $previewBillingData['SCHEDULEENTRY'];

        return $response;
    }

    /**
     * Enables usage add from contract line tab for the cancelled lines.
     * @param $entity
     * @param $object
     * @return int
     */
    public function CanAddOnGrid($entity, $object)
    {
        $result = parent::CanAddOnGrid($entity, $object);
        $verb = $this->getVerb();
        if ($verb === "edit" && $entity == 'contractusage' &&
            ($object['REVENUETEMPLATEMETHOD'] || $object['REVENUE2TEMPLATEMETHOD'] === ContractRevrecAndBillingMethodTypes::CONTRACT_REVREC_METHOD_QTY_BASED)) {
            $result = $this->canActionOnGrid($entity, $object, '/create');
        }
        return $result;
    }
}
