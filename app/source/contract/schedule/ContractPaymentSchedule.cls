<?php
/**
 * File ContractPaymentSchedule.cls contains the class ContractPaymentSchedule
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * Payment schedule handler
 *
 * Class ContractPaymentSchedule
 */
class ContractPaymentSchedule extends ContractEntriesBasedSchedule
{
    /**
     * @return EntityManager
     */
    private static function getPaymentScheduleEntryMgr()
    {
        return Globals::$g->gManagerFactory->getManager('contractpaymentscheduleentry');
    }

    /**
     * @param  ContractSchedulesResolveEntry[] $resolveEntries
     * @return array
     */
    private static function getPmtEntryKeys($resolveEntries)
    {
        $pmtEntryKeys = [];

        if (!empty($resolveEntries)) {

            foreach ($resolveEntries as $resolveEntry) {
                $pmtEntryKeys[] = $resolveEntry->getPmtScheduleEntryKey();
            }
            $pmtEntryKeys = INTACCTarray_unique($pmtEntryKeys);
        }
        return $pmtEntryKeys;
    }

    /**
     * @param array $pmtSchEntries
     * @param float $pmtAmt
     * @param int   $sourcePaymentPRRecordKey
     *
     * @return array
     *
     * @throws IAException
     */
    private static function findSchEntriesForPmtAmount($pmtSchEntries, $pmtAmt, $sourcePaymentPRRecordKey)
    {
        $retEntries = [];
        $totAmt = 0;
        foreach ($pmtSchEntries as $entry) {
                $retEntries[] = $entry;
                $totAmt = ibcadd($totAmt, $entry['AMOUNT']);
        }
        if (!empty($retEntries) && $totAmt != $pmtAmt) {
            //try one more time as the MEA could split the payment schedules.
            $retEntriesOld = self::findSchEntriesForPmtAmountOld($retEntries, $pmtAmt);
            if (empty($retEntriesOld)) {
                logToFileError('Internal Error: unable to find matching amount for pmtSchEntries');
                throw IAException::newIAException('CN-1403', "Internal Error: unable to find matching amount for pmtSchEntries linked to the source payment: $sourcePaymentPRRecordKey, $pmtAmt",
                                                  ['SOURCE_PAYMENT_RECORD_KEY' => $sourcePaymentPRRecordKey, 'PMT_AMT' => $pmtAmt]);
            } else {
                return $retEntriesOld;
            }
        }
        return $retEntries;
    }

    /**
     * @param array $pmtSchEntries
     * @param float $pmtAmt
     *
     * @return array
     */
    private static function findSchEntriesForPmtAmountOld($pmtSchEntries, $pmtAmt)
    {
        $retEntries = [];

        // First assume that we can find the $pmtAmt in ONE OF the $pmtSchEntries
        foreach ($pmtSchEntries as $entry) {
            if ($entry['AMOUNT'] == $pmtAmt) {
                $retEntries[] = $entry;
                // if we get more than one match it doesn't matter since the date and amount match anyway, just return the first one
                break;
            }
        }

        if (empty($retEntries)) {
            // Next try summing the $pmtSchEntries in order to come up with $pmtAmt
            self::findSchEntriesForPmtAmountInOrder($pmtSchEntries, $pmtAmt, $retEntries);

            if (empty($retEntries)) {
                // Next try summing the $pmtSchEntries in reverse order to come up with $pmtAmt
                self::findSchEntriesForPmtAmountInReverseOrder($pmtSchEntries, $pmtAmt, $retEntries);

                if (empty($retEntries)) {
                    // This is our last chance. We have to use the costly solution to solve the "subset sum problem"
                    self::findSchEntriesSubsetForAmount($pmtSchEntries, $pmtAmt, $retEntries);
                }
            }
        }

        return $retEntries;
    }

    /**
     * @param array $pmtSchEntries
     * @param float $pmtAmt
     * @param array $retEntries
     */
    private static function findSchEntriesForPmtAmountInOrder($pmtSchEntries, $pmtAmt, &$retEntries)
    {
        $totAmt = 0;
        foreach ($pmtSchEntries as $entry) {
            if ($totAmt < $pmtAmt) {
                $retEntries[] = $entry;
                $totAmt = ibcadd($totAmt, $entry['AMOUNT']);
            } else {
                break;
            }
        }
        if ($totAmt != $pmtAmt) {
            // Alas, the sum is not equal
            $retEntries = [];
        }
    }

    /**
     * @param array $pmtSchEntries
     * @param float $pmtAmt
     * @param array $retEntries
     */
    private static function findSchEntriesForPmtAmountInReverseOrder($pmtSchEntries, $pmtAmt, &$retEntries)
    {
        $totAmt = 0;
        for ($i=count($pmtSchEntries)-1; $i >= 0; $i--) {
            if ($totAmt < $pmtAmt) {
                $entry = $pmtSchEntries[$i];
                $retEntries[] = $entry;
                $totAmt = ibcadd($totAmt, $entry['AMOUNT']);
            } else {
                break;
            }
        }
        if ($totAmt != $pmtAmt) {
            // Alas, the sum is not equal
            $retEntries = [];
        }
    }

    /**
     * @param array  $pmtSchEntries
     * @param string $sum
     * @param array  $retEntries
     */
    private static function findSchEntriesSubsetForAmount($pmtSchEntries, $sum, &$retEntries)
    {
        $retEntries = [];
        if (!self::findSchEntriesSubsetForAmountRecursive($pmtSchEntries, count($pmtSchEntries), $sum, $retEntries)) {
            $retEntries = [];
        } else {
            $assertSum = 0;
            foreach ( $retEntries as $retEntry ) {
                $assertSum = ibcadd($assertSum, $retEntry['AMOUNT']);
            }
            ContractUtil::assert($assertSum == $sum);
        }
    }

    /**
     * A recursive solution for subset sum problem  https://www.geeksforgeeks.org/subset-sum-problem-dp-25/
     * Returns true if there is a subset of set with sum equal to given sum
     *
     * @param array  $pmtSchEntries
     * @param int    $n
     * @param string $sum
     * @param array  $retEntries
     *
     * @return bool
     */
    private static function findSchEntriesSubsetForAmountRecursive($pmtSchEntries, $n, $sum, &$retEntries)
    {
        // Base Cases
        if ($sum == 0) {
            return true;
        }
        if ($n == 0 && $sum != 0) {
            return false;
        }

        $amtOfLastElement = $pmtSchEntries[$n - 1]['AMOUNT'];
        if (!ContractUtil::isSameSign($sum, $amtOfLastElement) || abs($amtOfLastElement) > abs($sum)) {
            // If last element is not same sign or its absolute value is greater than sum, then ignore it
            $ret = self::findSchEntriesSubsetForAmountRecursive($pmtSchEntries, $n - 1, $sum, $retEntries);
        } else {
            // else, check if sum can be obtained by
            //     (a) including the last element
            $sumIfLastElementIsIncluded = ibcsub($sum, $amtOfLastElement);
            $ret1 = self::findSchEntriesSubsetForAmountRecursive($pmtSchEntries, $n - 1, $sumIfLastElementIsIncluded, $retEntries);

            if ($ret1) {
                $retEntries[] = $pmtSchEntries[$n-1];
                $ret2 = false;
            } else {
                // else, check if sum can be obtained by
                //     (b) excluding the last element
                $ret2 = self::findSchEntriesSubsetForAmountRecursive($pmtSchEntries, $n - 1, $sum, $retEntries);
            }

            $ret = $ret1 || $ret2;

        }
        return $ret;
    }

    /**
     * @param int $paymentKey
     *
     * @return int
     */
    public static function unpayPaymentScheduleEntries($paymentKey)
    {
        $updateQry = "UPDATE contractscheduleentry
                      SET actualpostingdate=NULL, posted=NULL, state='O', paymentprrecordkey=null, paymentprentrykey=null
                      WHERE cny#=:1 AND paymentprrecordkey=:2";

        $ok = ExecStmtEx([$updateQry, GetMyCompany(), $paymentKey], $numRowsUpdated, true);
        if ( ! $ok) {
            throw new IAException("Error updating payment schedule entries when deleting payment", 'CN-1404');
        }
        return $numRowsUpdated;
    }

    /**
     * Returns 'Payment'
     *
     * @return string
     */
    public function getType()
    {
        return ContractSchedule::TYPE_PAYMENT;
    }

    /**
     * Posts required transactions
     *
     * @param   int     $entryKey       Schedule entry key
     * @param   int     $postingDate    Posting date
     *
     * @throws IAException
     */
    protected function postTransactions($entryKey, $postingDate=null)
    {
        throw new IAException('Invalid method called', 'CN-1405');
    }

    /**
     * Returns template name from contract line
     *
     * @param int $entryKey Schedule entry key
     *
     * @throws IAException
     */
    protected function undoTransactions($entryKey)
    {
        throw new IAException('Invalid method called', 'CN-1405');

    }

    /**
     * Returns payment schedule entity
     *
     * @return string
     */
    public function getScheduleEntity()
    {
        return 'contractpaymentschedule';
    }

    /**
     * Returns payment schedule entry entity
     *
     * @return string
     */
    public function getScheduleEntryEntity()
    {
        return 'contractpaymentscheduleentry';
    }

    /**
     * Handles business logic on receiving payment (or payment reversal)
     *
     * @param   int     $billSchEntryKey        Schedule entiry key
     * @param   int     $paymentPREntrykey      Payment's prentry key
     * @param   float   $pmtAmt                 Amount
     * @param   string  $pmtDate                Transaction date
     * @param   int     $paymentPRRecordKey     Payment's PRRecord key
     *
     * @throws IAException
     */
    public static function handleReceivePayment($billSchEntryKey, $paymentPREntrykey, $pmtAmt, $pmtDate, $paymentPRRecordKey = null)
    {
        $ok = true;
        $isNegative = $pmtAmt < 0;
        $source = 'ContractPaymentSchedule::handleReceivePayment';

        $schResolveHandler = new ContractSchedulesResolveBaseHandler();
        $resolveEntries = $schResolveHandler->getEntriesByBillKey($billSchEntryKey);
        $pmtEntryKeys = self::getPmtEntryKeys($resolveEntries);

        if (!empty($pmtEntryKeys)) {
            /* @var ContractPaymentScheduleEntryManager $pmtSchEntryMgr */
            $pmtSchEntryMgr = self::getPaymentScheduleEntryMgr();
            $filters = [[
                        ['BILLSCHEDULEENTRYKEY', '=', $billSchEntryKey],
                        [
                            [
                                'fields'   => ['POSTED'],
                                'function' => "nvl(\${1}, 'F')"
                            ],
                            '=',
                            false
                        ]
                    ]];
            $pmtSchEntriesValues = $pmtSchEntryMgr->GetList(['filters' => $filters]);

            // First find the distribution amounts for each pmtSchEntry then distribute them
            if (arrayCount($pmtSchEntriesValues) > 0) {

                /* @var ContractScheduleEntry[] $pmtSchEntries */
                $pmtSchEntries = [];
                $totalSchAmt = 0;

                // First pass check that there is enough pmt schedule amount to be paid, also populate the $pmtSchEntries array
                foreach ($pmtSchEntriesValues as $pmtSchEntryValues) {
                    $pmtSchEntry = new ContractScheduleEntry();
                    $pmtSchEntry->setEmValues($pmtSchEntryValues);
                    $pmtSchEntries[] = $pmtSchEntry;
                    if ($isNegative) {
                        assert($pmtSchEntry->getAmount() <= 0);
                    } else {
                        assert($pmtSchEntry->getAmount() >= 0);
                    }
                    $totalSchAmt = ibcadd($totalSchAmt, $pmtSchEntry->getAmount());
                }

                if (ibcabs($pmtAmt) > ibcabs($totalSchAmt)) {
                    throw IAException::newIAException('CN-1406', "Payment amount ($pmtAmt) cannot exceed the scheduled amount ($totalSchAmt)",
                                                      ['PMT_AMT' => $pmtAmt, 'TOTAL_SCH_AMT' => $totalSchAmt]);
                }

                $pmtAmtToLink = $pmtAmt;
                $pmtSchEntryMgr->beginTrx($source);

                foreach ($pmtSchEntries as $pmtSchEntry) {
                    $schAmt = $pmtSchEntry->getAmount();

                    if (ibcabs($pmtAmtToLink) < ibcabs($schAmt)) {
                        // Partial payment -- we need to split the payment schedule amount
                        $schBaseAmt = $pmtSchEntry->getBaseAmount();
                        $pmtBaseAmtToLink = ContractUtil::computeAmountRatio($schBaseAmt, $pmtAmtToLink, $schAmt);

                        $pmtSchEntry->setAmount($pmtAmtToLink);
                        $pmtSchEntry->setBaseAmount($pmtBaseAmtToLink);

                        // Add a new schedule for the reminder amount
                        /* @var ContractScheduleEntry $newPmtSchEntry */
                        $newPmtSchEntry = clone $pmtSchEntry;
                        $newPmtSchEntry->setRecordNo(null);
                        $newPmtSchEntry->setAmount(ibcsub($schAmt, $pmtAmtToLink));
                        $newPmtSchEntry->setBaseAmount(ibcsub($schBaseAmt, $pmtBaseAmtToLink));

                        $emValues = $newPmtSchEntry->getValues();
                        $ok = $ok && $pmtSchEntryMgr->add($emValues);
                        $newPmtSchEntry->setRecordNo($emValues['RECORDNO']);

                        // Fix resolve entries
                        if ($ok) {
                            $schResolveHandler->handlePartialPayment($resolveEntries, $pmtSchEntry, $newPmtSchEntry);
                        }
                    }

                    $pmtSchEntry->setPosted(true);
                    $pmtSchEntry->setActualPostingDate($pmtDate);
                    $pmtSchEntry->setPaymentPREntryKey($paymentPREntrykey);
                    $pmtSchEntry->setPaymentPRRecordKey($paymentPRRecordKey);

                    $pmtSchEntryMgr->systemTriggeredUpdate = true;
                    $pmtSchEntryValues = $pmtSchEntry->getValues();
                    $ok = $ok && $pmtSchEntryMgr->set($pmtSchEntryValues);

                    $pmtAmtToLink = ibcsub($pmtAmtToLink, $pmtSchEntry->getAmount());
                    if (($isNegative && $pmtAmtToLink >= 0) || (!$isNegative && $pmtAmtToLink <= 0)) {
                        break;
                    }
                }
                ContractUtil::assert ($pmtAmtToLink == 0, "Unable to link all the payment. $pmtAmtToLink still needs to be linked");

                $ok = $ok && $pmtSchEntryMgr->commitTrx($source);
                if (!$ok) {
                    $pmtSchEntryMgr->rollbackTrx($source);
                    throw new IAException("Unable to process receive payment", 'CN-1407');
                }

            } else {
                throw IAException::newIAException('CN-1408', "Unable to find payment schedule entries linked to billing ".
                                                             "schedule entry key $billSchEntryKey that are not posted (payment amount: $pmtAmt)",
                                                  ['BILL_SCH_ENTRY_KEY' => $billSchEntryKey, 'PMT_AMT' => $pmtAmt]);
            }
        }
    }

    /**
     * Handles business logic on receiving payment (or payment reversal)
     *
     * @param int $billSchEntryKey Billing schedule entry key
     * @param array $sourcePaymentPREntryKeys
     * @param float $pmtAmt Reversal amount
     * @param string $pmtDate Reversal payment date
     * @param int $sourcePaymentPRRecordKey Reversal Payment's PRRecord key
     *
     * @throws IAException
     */
    public static function handlePaymentReversal($billSchEntryKey, $sourcePaymentPREntryKeys, $pmtAmt, $pmtDate, $sourcePaymentPRRecordKey)
    {
        $source = 'ContractPaymentSchedule::handleReceivePayment';

        $schResolveHandler = new ContractSchedulesResolveBaseHandler();
        $resolveEntries = $schResolveHandler->getEntriesByBillKey($billSchEntryKey);
        $pmtEntryKeys = self::getPmtEntryKeys($resolveEntries);

        if (!empty($pmtEntryKeys)) {

            $pmtSchEntryMgr = self::getPaymentScheduleEntryMgr();

            $params = [
                'filters' => [
                    [
                        ['RECORDNO','IN',$pmtEntryKeys],
                        ['REVERSALRECORDNO', 'ISNULL'],
                        ['PAYMENTPRRECORDKEY','=',$sourcePaymentPRRecordKey],
                        ['PAYMENTPRENTRYKEY' , 'IN', $sourcePaymentPREntryKeys]
                    ]
                ]
            ];

            $pmtSchEntries = $pmtSchEntryMgr->GetList($params);
            $numPmtSchEntries = count($pmtSchEntries);
            if (is_array($pmtSchEntries) && $numPmtSchEntries > 0) {
                $schEntriesToReverse = self::findSchEntriesForPmtAmount($pmtSchEntries, $pmtAmt, $sourcePaymentPRRecordKey);

                if (!empty($schEntriesToReverse)) {
                    $ok = $pmtSchEntryMgr->beginTrx($source);

                    foreach ($schEntriesToReverse as $schEntryToReverse) {
                        $pmtSchEntry = new ContractScheduleEntry();
                        $pmtSchEntry->setEmValues($schEntryToReverse);

                        // Add two new schedule entries:
                        // 1.  For the negative amount (reversed payment)
                        $newReversedPmtSchEntry = clone $pmtSchEntry;
                        $newReversedPmtSchEntry->setRecordNo(null);
                        $newReversedPmtSchEntry->setAmount(-$pmtSchEntry->getAmount());
                        $newReversedPmtSchEntry->setBaseAmount(-$pmtSchEntry->getBaseAmount());
                        if ($pmtSchEntry->getPostedBaseAmount() !== null) {
                            $newReversedPmtSchEntry->setPostedBaseAmount(-$pmtSchEntry->getPostedBaseAmount());
                        }
                        $newReversedPmtSchEntry->setActualPostingDate($pmtDate);
                        $newReversedPmtSchEntry->setOriPmtScheduleEntryKey($pmtSchEntry->getRecordNo());
                        $newReversedPmtSchEntry->setPaymentPREntryKey(null);
                        $newReversedPmtSchEntry->setPaymentPRRecordKey(null);
                        $emValues = $newReversedPmtSchEntry->getValues();
                        $ok = $pmtSchEntryMgr->add($emValues);
                        $newReversedPmtSchEntry->setRecordNo($emValues['RECORDNO']);
        
                        // 2.  For the now open (unpaid) positive amount
                        $newPmtSchEntry = clone $pmtSchEntry;
                        $newPmtSchEntry->setRecordNo(null);
                        $newPmtSchEntry->setPosted(null);
                        $newPmtSchEntry->setActualPostingDate(null);
                        $newPmtSchEntry->setOriPmtScheduleEntryKey($pmtSchEntry->getRecordNo());
                        $newPmtSchEntry->setPaymentPREntryKey(null);
                        $newPmtSchEntry->setPaymentPRRecordKey(null);
                        $emValues = $newPmtSchEntry->getValues();
                        $ok = $ok && $pmtSchEntryMgr->add($emValues);
                        $newPmtSchEntry->setRecordNo($emValues['RECORDNO']);

                        // Fix resolve entries
                        if ($ok) {
                            $schResolveHandler->handlePaymentReversal($resolveEntries, $pmtSchEntry, $newReversedPmtSchEntry, $newPmtSchEntry);
                        }

                    }

                    $ok = $ok && $pmtSchEntryMgr->commitTrx($source);
                    if (!$ok) {
                        $pmtSchEntryMgr->rollbackTrx($source);
                        throw new IAException("Unable to process payment reversal", 'CN-1409');
                    }
                } else {
                    logToFileError('Internal Error: unable to find matching amount for pmtSchEntries');
                    throw IAException::newIAException('CN-1410', "Internal Error: unable to find matching amount for pmtSchEntries linked to $billSchEntryKey, $pmtAmt, $sourcePaymentPRRecordKey",
                                                      ['BILL_SCH_ENTRY_KEY' => $billSchEntryKey, 'PMT_AMT' => $pmtAmt, 'SOURCE_PAYMENT_RECORD_KEY' => $sourcePaymentPRRecordKey]);
                }

            } else {
                logToFileError('Internal Error: unable to find matching entries for pmtSchEntries');


                $errorMgs = sprintf('We found an internal error with the payment dated %1$s for pmtSchEntries linked to %2$s. The payment data  is not synced with the latest payment schedule data. Contact your Sage Intacct Support resource,
                                        tell them you received this error, and they will resolve the issue.', $pmtDate, $billSchEntryKey);
                throw IAException::newIAException('CN-1411', $errorMgs,
                                                  ['PMT_DATE' => FormatDateForDisplay($pmtDate), 'BILL_SCH_ENTRY_KEY' => $billSchEntryKey]);
            }
        }
    }

    /**
     * @param array $params
     * @param bool  $isDraft
     * @param array $billingEntries
     * @param string $termValue
     * @param ContractPaymentSchedule &$paymentSchedule
     */
    public static function createPaymentScheduleFromBilling($params, $isDraft, $billingEntries, $termValue, &$paymentSchedule)
    {
        ContractPaymentScheduleCreateParams::createPaymentParamsEntriesFromBilling(
            $params, $billingEntries, $termValue, $pmtParams, $pmtEntries);
        $paySchParams = new ContractPaymentScheduleCreateParams($pmtParams, $pmtEntries, $isDraft);
        $paymentSchedule = new self();
        $paymentSchedule->create($paySchParams);
    }

    /**
     * @param int $billSchEntryKey
     *
     * @return string[][]
     */
    public function queryPmtSchEntryKeysAndAmountsFromBilling($billSchEntryKey)
    {
        $results = EntityManager::GetListQuick($this->getScheduleEntryEntity(), ['RECORDNO', 'AMOUNT', 'BASEAMOUNT'], ['BILLSCHEDULEENTRYKEY' => $billSchEntryKey]);
        return $results;
    }

    /**
     * getSchduelType
     * 
     * @return string
     */
    protected function getScheduleType()
    {
        return ContractSchedule::TYPE_PAYMENT;
    }

    /**
     * getScheduleEntryManager
     * 
     * @return ContractScheduleEntryManager
     */
    public function getScheduleEntryManager()
    {

        return Globals::$g->gManagerFactory->getManager('contractpaymentscheduleentry');
    }

    public function postPastDatedEntries()
    {
        //NO OP function
    }

}
