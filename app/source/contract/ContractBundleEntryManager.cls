<?php
/**
 * File ContractBundleEntryManager.cls contains the class ContractBundleEntryManager
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2015 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * CRUD handler for Contract Bundle Entries
 *
 * Class ContractBundleEntryManager
 */
class ContractBundleEntryManager extends OwnedObjectManager
{
    /**
     * add
     *
     * @param array $values
     *
     * @return bool
     *
     */
    protected function regularAdd(&$values)
    {
        $ok = $this->translate($values);

        $validator = new ContractBundleEntryValidator($this);
        $ok = $ok && $validator->validate($values);

        $ok = $ok && parent::regularAdd($values);
        return $ok;
    }

    /**
     * set
     *
     * @param array $values
     *
     * @return bool
     *
     */
    protected function regularSet(&$values)
    {
        $ok = $this->translate($values);

        $validator = new ContractBundleEntryValidator($this);
        $ok = $ok && $validator->validate($values);

        $ok = $ok && parent::regularSet($values);
        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    private function translate(& $values)
    {
        $translateUtil = new ContractTranslateUtil($this);
        // the request came for contract detail.
        $values['LINENO'] = $values['CONTRACTDETAILLINENO'];
        $values['PERIOD'] = $values['CONTRACTDETAILPERIOD'];
        $ok = $translateUtil->translateContractDetail($values);

        return $ok;
    }

}