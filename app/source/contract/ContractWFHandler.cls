<?php
/**
 *    FILE: ContractWFHandler.cls
 *    AUTHOR: <PERSON>
 *    DESCRIPTION:
 *
 *    (C) 2016, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */
class ContractWFHandler
{
    /** @var ContractObjHandler $contractObjectHandler */
    private $contractObjectHandler = null;

    public function __construct()
    {
        //this call will create the necessary entity manager objects
        $this->contractObjectHandler = new ContractObjHandler();
    }

    /**
     * @param bool                 $isPreview
     * @param string               $contractId
     * @param ContractBundleInfo[] $contractBundles
     *
     * @return ContractInfo
     */
    public function doMEAProcess($isPreview, $contractId, $contractBundles, $handleOpenEntries)
    {
        try {
            //for $previewOrSave : true means that save the allocation and adjust the schedule entires
            $contractObject = $this->contractObjectHandler->getContractObjectForMEA($contractId, $contractBundles);
            $meaHandler = new ContractMEAPricingHandler($contractObject);
            $bundles = $contractObject->getContractBundles();
            $bundleType = null;
            $isRenewalOnlyKitBundle = $isRenewalForecast = false;
            if (!empty($bundles[0])) {
                $bundleType = $bundles[0]->getType();
                if ($bundles[0]->isTypeKit() && !empty($bundles[0]->getContractDetails())) {
                    $kitLine = $bundles[0]->getContractDetails()[0];
                    // If it's Renewal only, skip schedule and resolve creation
                    $isRenewalOnlyKitBundle = $kitLine->getState() === ContractDetailState::STATE_RENEWALONLY;
                    $meaHandler->setIsRenewalOnlyKitBundle($isRenewalOnlyKitBundle);
                    // If it's Renewal forecast, skip reclass / resolve creation
                    $isRenewalForecast = $kitLine->isRenewalForecast();
                }
            }
            if (!$isPreview && !$isRenewalOnlyKitBundle) {
                $contractManager = Globals::$g->gManagerFactory->getManager('contract');
                foreach ($bundles as $oneBundle) {
                    $contractManager->clearCDSchedulesForBundleTillEffectiveDate($oneBundle, $this->contractObjectHandler);
                }
            }
            $meaHandler->processMEA($isPreview, $handleOpenEntries);
            $contractObject = $meaHandler->getCurrentContract();
            //return the values or save in the database as needed
            if ($isRenewalOnlyKitBundle) {
                if ($contractObject->getHasBundles()) {
                    $this->contractObjectHandler->saveContractAllocationDetails($contractObject);
                }
            } elseif (!$isPreview) {
                // retrieve current MEA schedule links before they get deleted
                $oldSchLinks = $this->getScheduleLinks($bundles);

                // retrieve current MEA disctribution for both journals before they get deleted
                $oldMEADist = $this->getMEADistribution($bundles);

                //save the adjusted schedule entries here
                $this->contractObjectHandler->updateScheduleEntriesForContract($contractObject, true);
                if (!$isRenewalForecast) {
                    //rebuild the resolves for this contract before the re-class engine is called
                    $this->rebuildOneContractSchedulesResolve($contractObject->getContractId(), null, true, $bundleType);
                    //adjust the base amount in revenue schedule entries by looking at the resolves
                    $this->contractObjectHandler->adjustBaseAmountsInScheduleEntries($contractObject);
                }
                // Adjust balances
                if ($contractObject->getHasBundles() ) {
                    //save the MEA Allocation here
                    $this->contractObjectHandler->saveContractAllocationDetails($contractObject);

                    if ( ContractUtil::enableMEAReclassV2() && !$isRenewalForecast ) {
                        // TODO: The validation below is expensive.  We need to consider removing this for "live"
                        $this->validateScheduleLinks($bundles);
                    }

                    // Remove any bundles containing draft contract lines (For Kits)
                    foreach ($bundles as $index => $bundle) {
                        foreach ($bundle->getContractDetails() as $contractDetail) {
                            if ($contractDetail->getState() === ContractDetailState::STATE_DRAFT) {
                                unset($bundles[$index]);
                                break;
                            }
                        }
                    }
                    
                    if (!empty($bundles) && !$isRenewalForecast) {
                        $this->adjustBalances($contractObject->getRecordNo(), $bundles, $oldSchLinks, $oldMEADist);
                    }

                    //perform more action after MEA process is done
                    $this->contractObjectHandler->moreActionsPostMEA($contractObject);
                }
            }

        } catch (IAException $excp) {
            //throw error here
            logFL($excp->getMessage());
            ContractUtil::rethrowException($excp);

        }
        return $contractObject;
    }

    /**
     *
     * @param array $bundles
     *
     * @throws IAException
     */
    protected function validateScheduleLinks($bundles)
    {
        $cnDetailKeys = $this->getCNDetailKeys($bundles);

        $j1Affected = $bundles[0]->getApplyToJournal1();
        $j2Affected = $bundles[0]->getApplyToJournal2();

        try {

            ContractSchedulesResolveBaseHandler::validateSavedResolveEntries($cnDetailKeys, $j1Affected, $j2Affected, $errs);

        } catch (IAException $iae) {
            logToFileError(ContractAssertionsUtil::LOG_PREFIX, "ContractSchedulesResolveBaseHandler::validateSavedResolveEntries: ".$iae->getMessage());

            throw new IAException("TODO: Schedule links are invalid.", 'CN-1060');
        }

        if ( !empty($errs) ) {
            logToFileError(ContractAssertionsUtil::LOG_PREFIX, "ContractSchedulesResolveBaseHandler::validateSavedResolveEntries: ".json_encode($errs));

            throw new IAException("TODO: Schedule links are invalid.", 'CN-1060');
        }
    }

    /**
     *
     *
     * @param array $bundles
     *
     * @return array
     */
    protected function getScheduleLinks($bundles)
    {
        $cnDetailKeys = $this->getCNDetailKeys($bundles);

        $j1Affected = $bundles[0]->getApplyToJournal1();
        $j2Affected = $bundles[0]->getApplyToJournal2();

        $schLinks = [];

        if ($j1Affected) {
            $j1SchLinks = ContractGLReclassOnMEA::getScheduleLinks($cnDetailKeys, ContractGLReclassEvent::JOURNALCODE_J1);
            if ($j1SchLinks) {
                $schLinks[ContractGLReclassEvent::JOURNALCODE_J1] = $j1SchLinks;
            }
        }

        if ($j2Affected) {
            $j2SchLinks = ContractGLReclassOnMEA::getScheduleLinks($cnDetailKeys, ContractGLReclassEvent::JOURNALCODE_J2);
            if ($j2SchLinks) {
                $schLinks[ContractGLReclassEvent::JOURNALCODE_J2] = $j2SchLinks;
            }
        }

        return $schLinks;
    }

    /**
     * @param ContractBundleInfo[] $bundles
     *
     * @return array
     */
    protected function getMEADistribution($bundles)
    {
        $j1Affected = $bundles[0]->getApplyToJournal1();
        $j2Affected = $bundles[0]->getApplyToJournal2();
        $contractKey = $bundles[0]->getContractKey();
        $meaDist = [];

        if ($j1Affected) {
            $j1MEADist = ContractGLReclassOnMEA::getMEADistribution(ContractGLReclassEvent::JOURNALCODE_J1, null, $contractKey);
            if ($j1MEADist) {
                $j1MEADistByBSE = $this->groupMEADistByBSE($j1MEADist);
                $meaDist[ContractGLReclassEvent::JOURNALCODE_J1] = $j1MEADistByBSE;
            }
        }

        if ($j2Affected) {
            $j2MEADist = ContractGLReclassOnMEA::getMEADistribution(ContractGLReclassEvent::JOURNALCODE_J2, null, $contractKey);
            if ($j2MEADist) {
                $j2MEADistByBSE = $this->groupMEADistByBSE($j2MEADist);
                $meaDist[ContractGLReclassEvent::JOURNALCODE_J2] = $j2MEADistByBSE;
            }
        }

        return $meaDist;
    }

    /**
     * @param array $meaDist
     *
     * @return array
     */
    protected function groupMEADistByBSE($meaDist)
    {
        $byBSE = [];
        foreach($meaDist as $dist) {
            $byBSE[$dist['BILLSCHENTRYKEY']][] = $dist;
        }
        return $byBSE;
    }

    /**
     * Adjust balances
     *
     * @param  int                  $contractKey
     * @param  ContractBundleInfo[] $bundles
     * @param  array                $oldSchLinks
     * @param  array                $oldMEADist
     */
    protected function adjustBalances($contractKey, $bundles, $oldSchLinks, $oldMEADist)
    {
        $effectiveDate = $bundles[0]->getEffectiveDate();
        $j1Affected = $bundles[0]->getApplyToJournal1();
        $j2Affected = $bundles[0]->getApplyToJournal2();
        $bundleType = $bundles[0]->getType();
        $cnDetailKeys = $this->getCNDetailKeys($bundles);
        $bundleKey = (int) $bundles[0]->getBundleKey();

        if ($j1Affected) {
            /** @var ContractRevenueGLReclassEngine $reclassEngine */
            $reclassEngine = BaseContractGLReclassEngine::getInstance(ContractSchedule::TYPE_REVENUE);

            $addlData['OLD_SCHEDULELINKS'] = $oldSchLinks[ContractGLReclassEvent::JOURNALCODE_J1];
            $addlData['OLD_MEADISTRIBUTION'] = $oldMEADist[ContractGLReclassEvent::JOURNALCODE_J1];
            $reclassEngine->setBundleType($bundleType);
            $reclassEngine->setBundleKey($bundleKey);
            ContractGLEventRegistry::forceRgisterLinkedReclass();
            $reclassEngine->onMEA(
                $contractKey,
                $effectiveDate,
                $addlData,
                false,
                ContractGLReclassEvent::JOURNALCODE_J1,
                $cnDetailKeys
            );
        }

        if ($j2Affected) {
            /** @var ContractRevenueGLReclassEngine $reclassEngine */
            $reclassEngine = BaseContractGLReclassEngine::getInstance(ContractSchedule::TYPE_REVENUE);

            $addlData['OLD_SCHEDULELINKS'] = $oldSchLinks[ContractGLReclassEvent::JOURNALCODE_J2];
            $addlData['OLD_MEADISTRIBUTION'] = $oldMEADist[ContractGLReclassEvent::JOURNALCODE_J2];
            $reclassEngine->setBundleType($bundleType);
            $reclassEngine->setBundleKey($bundleKey);
            ContractGLEventRegistry::forceRgisterLinkedReclass();
            $reclassEngine->onMEA(
                $contractKey,
                $effectiveDate,
                $addlData,
                false,
                ContractGLReclassEvent::JOURNALCODE_J2,
                $cnDetailKeys,
                $bundleKey
            );
        }
    }

    /**
     * Returns unique cn detail keys from bundles
     *
     * @param ContractBundleInfo[] $bundles
     *
     * @return int[]
     */
    protected function getCNDetailKeys($bundles)
    {
        $cnDetailKeys = [];

        $j1Affected = $bundles[0]->getApplyToJournal1();
        $j2Affected = $bundles[0]->getApplyToJournal2();

        $contractObjhandler = new ContractObjHandler();
        foreach ($bundles as $bundle) {
            $innerCNDKeys = $bundle->getContractDetailKeys();
            foreach ( $innerCNDKeys as $oneKey ) {
                if ( $bundle->getIsNoFairValue() === true ) {
                    if ( $j1Affected === true ) {
                        $innerJ1 = $contractObjhandler->checkContractAllocationDetailForContractDetail($oneKey,1);
                        if ( $innerJ1 === true ) {
                            if (in_array($oneKey, $cnDetailKeys) !== true) {
                                $cnDetailKeys[] = $oneKey;
                            }
                        }
                    }
                    if ( $j2Affected === true ) {
                        $innerJ2 = $contractObjhandler->checkContractAllocationDetailForContractDetail($oneKey,2);
                        if ( $innerJ2 === true ) {
                            if (in_array($oneKey, $cnDetailKeys) !== true) {
                                $cnDetailKeys[] = $oneKey;
                            }
                        }
                    }
                } else {
                    if ( in_array($oneKey, $cnDetailKeys) !== true ) {
                        $cnDetailKeys[] = $oneKey;
                    }
                }
            }
        }

        return $cnDetailKeys;
    }

    /**
     * @param array $values
     *
     * @return ContractBundleInfo[]
     */
    public function getBundleFromScreenData( $values )
    {
        return $this->contractObjectHandler->getContractBundleInfoFromEditorData($values);
    }

    /**
     * @param string    $entityLocationId
     * @param string[]  $customerIds
     * @param string[]  $itemIds
     * @param string    $oldDBBjournal
     * @param string    $emailId
     *
     * @throws IAException
     */
    public function reprocessDBBPostings($entityLocationId, $customerIds, $itemIds, $oldDBBjournal, $emailId = null)
    {
        //check for slide-in
        $errStatus = ContractUtil::isSlideInEnabled(GetMyCompany());
        if ($errStatus != 0) {
            if ($errStatus === 1) {
                $errMsg = 'Access to ' . GetMyCompanyName() . ' is disabled in the external authorization record for "intacct" user. Enable access and then try processing DBB journal entries again.';
                $errorInfo = new ContractI18nErrorInfo('CN-1069', ['COMPANY_NAME' => GetMyCompanyName()], $errMsg);
            } else if ($errStatus === 2) {
                $errMsg = 'intacct user is not an Admin.';
                $errorInfo = new ContractI18nErrorInfo('CN-1070', [], $errMsg);
            } else {
                $errMsg = 'Could not find slide-in information.';
                $errorInfo = new ContractI18nErrorInfo('CN-1072', [], $errMsg);
            }
            throw IAException::newIAException($errorInfo->getErrorId(), $errorInfo->getErrorMessage(),
                $errorInfo->getPlaceholders());
        }

        //Check whether subscribed to
        $dbbConfigured = GetPreferenceForProperty(Globals::$g->kDBBid, 'MODULE_CONFIGURED');
        if ($dbbConfigured != 'T') {
            throw new IAException("Company not subscribed to DBB", 'CN-1061');
        }

        // Check whether the DBB journal is a user-defined Management journal.
        $glBatchManager = Globals::$g->gManagerFactory->getManager('glbatch');
        $journalMap = $glBatchManager::getJournalsTypeMap();
        $dbbRevenueJrnlSymbol = GetPreferenceForProperty(Globals::$g->kDBBid, 'DBB_REVENUEJOURNAL');
        $journalInfo = $journalMap[$dbbRevenueJrnlSymbol];
        if (!(isset($journalInfo['OPERATIONAL']) && $journalInfo['OPERATIONAL'] === 'T')) {
            throw new IAException("Please configure Management journal for DBB.", 'CN-1062');
        }

        $glPrefs = array();
        GetModulePreferences(Globals::$g->kGLid, $glPrefs);
        if ($glPrefs['ENABLESTATPOSTTOCLOSED'] !== 'T') {
            throw new IAException("Please enable 'Ignore closing rules for statistical entries' in GL", 'CN-1063');
        }
        $updateAllPostings = true;
        $customerIdFlag = false;
        $source = 'ContractWFHandler::reprocessDBBPostings';
//        $mgr = Globals::$g->gManagerFactory->getManager('item');
        $filter[] = ['MRR', '=', 'true'];
        if (isset($itemIds) && isNonEmptyArray($itemIds)) {
            $filter[] = ['ITEMID', 'in', $itemIds];
            $updateAllPostings = false;
        }
//        $mrrItems = $mgr->GetList([
//            'selects' => ['RECORDNO', 'ITEMID'],
//            'filters' => [ $filter ],
//        ]);
        $custIdStr = "";
        if (isset($customerIds) && isNonEmptyArray($customerIds)) {
            $updateAllPostings = false;
            $customerIdFlag = true;
            foreach ($customerIds as $customerId) {
                if ($custIdStr != "") {
                    $custIdStr .= ",";
                }
                $custIdStr .= "'" . $customerId . "'";
            }
        }

        $locationMgr = Globals::$g->gManagerFactory->getManager('location');
        $locationObj = $locationMgr->get($entityLocationId, ['RECORD#']);
        if ($entityLocationId != null && ($locationObj == null || !isset($locationObj['RECORDNO']))) {
            throw IAException::newIAException('CN-1072', "Invalid entity location provided: " . $entityLocationId,
                ['ENTITY_LOCATION_ID' => $entityLocationId]);
        }
        $locationKey = $locationObj['RECORDNO'];
        if ($updateAllPostings) {
            $glJrnlMgr = Globals::$g->gManagerFactory->getManager('gljournal');
            $dbbRevenueJrnlSymbol = GetPreferenceForProperty(Globals::$g->kDBBid, 'DBB_REVENUEJOURNAL');
            $journalKey = $glJrnlMgr->GetRecordNoFromVid($dbbRevenueJrnlSymbol);
            $oldJournalKey = $glJrnlMgr->GetRecordNoFromVid($oldDBBjournal);
            $cny = GetMyCompany();
            // Update contract detail records.
            if ($oldJournalKey == null) {
                throw IAException::newIAException('CN-1073',
                    "Cannot find the journal: " . $oldDBBjournal . ". Please enter a valid one.",
                    ['OLD_DBBJOURNAL' => $oldDBBjournal]
                );
            }
            Globals::$g->gQueryMgr->beginTrx($source);
            $qry = "UPDATE contractdetail cd
                    SET
                        ( mrrjournalkey,
                        newmrracctkey,
                        addonmrracctkey,
                        churnmrracctkey,
                        mrroffsetacctkey,
                        custcntjournalkey,
                        renewalupgradeacctkey,
                        renewaldowngradeacctkey,
                        partialdowngradeacctkey,
                        custcntacctkey ) = (
                            SELECT
                                mrrjournal.record#,
                                newmrrjournal.record#,
                                addonmrrjournal.record#,
                                churnmrrjournal.record#,
                                mrroffsetjournal.record#,
                                custcntjournal.record#,
                                renewalupjournal.record#,
                                renewaldgjournal.record#,
                                partialdgjournal.record#,
                                custcntacct.record#
                            FROM
                                gljournal mrrjournal
                                INNER JOIN modulepref mp1 ON mp1.cny# = mrrjournal.cny#
                                                             AND mp1.modulekey = '57.DBB'
                                                             AND mp1.property = 'DBB_REVENUEJOURNAL'
                                                             AND mp1.value = mrrjournal.symbol,glaccount newmrrjournal
                                INNER JOIN modulepref mp1 ON mp1.cny# = newmrrjournal.cny#
                                                             AND mp1.modulekey = '57.DBB'
                                                             AND mp1.property = 'DBB_NEWMRR'
                                                             AND mp1.value = newmrrjournal.acct_no,glaccount addonmrrjournal
                                INNER JOIN modulepref mp1 ON mp1.cny# = addonmrrjournal.cny#
                                                             AND mp1.modulekey = '57.DBB'
                                                             AND mp1.property = 'DBB_ADDONMRR'
                                                             AND mp1.value = addonmrrjournal.acct_no,glaccount churnmrrjournal
                                INNER JOIN modulepref mp1 ON mp1.cny# = churnmrrjournal.cny#
                                                             AND mp1.modulekey = '57.DBB'
                                                             AND mp1.property = 'DBB_CHURNMRR'
                                                             AND mp1.value = churnmrrjournal.acct_no,glaccount mrroffsetjournal
                                INNER JOIN modulepref mp1 ON mp1.cny# = mrroffsetjournal.cny#
                                                             AND mp1.modulekey = '57.DBB'
                                                             AND mp1.property = 'DBB_MRROFFSET'
                                                             AND mp1.value = mrroffsetjournal.acct_no,glaccount renewalupjournal
                                INNER JOIN modulepref mp1 ON mp1.cny# = renewalupjournal.cny#
                                                             AND mp1.modulekey = '57.DBB'
                                                             AND mp1.property = 'DBB_RENEWALUP'
                                                             AND mp1.value = renewalupjournal.acct_no,glaccount renewaldgjournal
                                INNER JOIN modulepref mp1 ON mp1.cny# = renewaldgjournal.cny#
                                                             AND mp1.modulekey = '57.DBB'
                                                             AND mp1.property = 'DBB_RENEWALDOWN'
                                                             AND mp1.value = renewaldgjournal.acct_no,glaccount partialdgjournal
                                INNER JOIN modulepref mp1 ON mp1.cny# = partialdgjournal.cny#
                                                             AND mp1.modulekey = '57.DBB'
                                                             AND mp1.property = 'DBB_PARTIALDOWN'
                                                             AND mp1.value = partialdgjournal.acct_no,statjournal custcntjournal
                                INNER JOIN modulepref mp1 ON mp1.cny# = custcntjournal.cny#
                                                             AND mp1.modulekey = '57.DBB'
                                                             AND mp1.property = 'DBB_CUSTOMERCOUNTJOURNAL'
                                                             AND mp1.value = custcntjournal.symbol,stataccount custcntacct
                                INNER JOIN modulepref mp1 ON mp1.cny# = custcntacct.cny#
                                                             AND mp1.modulekey = '57.DBB'
                                                             AND mp1.property = 'DBB_CUSTOMER'
                                                             AND mp1.value = custcntacct.acct_no
                            WHERE
                                mrrjournal.cny# = cd.cny#
                                AND   newmrrjournal.cny# = cd.cny#
                                AND   addonmrrjournal.cny# = cd.cny#
                                AND   churnmrrjournal.cny# = cd.cny#
                                AND   mrroffsetjournal.cny# = cd.cny#
                                AND   renewalupjournal.cny# = cd.cny#
                                AND   renewaldgjournal.cny# = cd.cny#
                                AND   partialdgjournal.cny# = cd.cny#
                                AND   custcntjournal.cny# = cd.cny#
                                AND   custcntacct.cny# = cd.cny#
                        )
                WHERE cd.cny# = :1 ";
            if ($locationKey != null) {
                $qry .= " AND EXISTS (SELECT 1 from contract ch where ch.cny# = cd.cny# and ch.record# = cd.contractkey and ch.melocationkey = $locationKey)";
            }
            $ok = ExecStmt(array($qry, GetMyCompany()));

            $qry = "UPDATE glbatch glbatch
                        SET journal# = $journalKey,
                            batch_no = GET_NEXTRECORDID(:1, 'Journal__" . $journalKey . "', 1, 'select nvl(MAX(BATCH_NO),0)+ 1 from glbatchmst where JOURNAL# = $journalKey AND CNY# = $cny')
                    WHERE
                        cny# = :1
                        AND   journal# = $oldJournalKey
                        AND   modulekey = '55.CONTRACT'
                        ";
            
            $locationFilter = '';
            if ($locationKey != null) {
                $locationFilter = "AND ch.melocationkey = $locationKey";
            }
            
            $qry .= " AND EXISTS (SELECT 1 from CONTRACTMRRRESOLVE cmrr, CONTRACT ch
                          where ch.cny# = glbatch.cny# and ch.cny# = cmrr.cny# $locationFilter
                          and cmrr.contractkey = ch.record# and cmrr.glbatchkey = glbatch.record#)";

            $ok = $ok && ExecStmt(array($qry, GetMyCompany()));

            $updategltotalsQry = "begin acct_utils.buildgltotals(:1);end;";
            $ok = $ok && ExecStmt(array($updategltotalsQry, GetMyCompany()));
            $ok = $ok && Globals::$g->gQueryMgr->commitTrx($source);
            if (!$ok) {
                Globals::$g->gQueryMgr->rollbackTrx($source);
                throw new IAException("Unable to commit to database", 'CN-1064');
            }
        }
        $ok = true;
        $custRecs = [];
        if (!$customerIdFlag) {
            $itemIdStr = "";
            foreach ($itemIds as $itemId) {
                if ($itemIdStr != "") {
                    $itemIdStr .= ",";
                }
                $itemIdStr .= "'" . $itemId . "'";
            }
            $itemFilter = $updateAllPostings ? ' ' : " AND item.itemid in (" . $itemIdStr . " ) ";
            $tempTableQry = "SELECT DISTINCT
                                cd.record#,
                                ch.contractid,
                                cd.lineno,
                                CASE
                                    WHEN ch.begindate < cd.begindate THEN 'A'
                                    ELSE 'N'
                                END AS dbbcategory,
                                ch.begindate AS headerbegindate,
                                cd.begindate AS linebegindate,
                                cd.locationkey,
                                ch.melocationkey
                              FROM
                                contractdetail cd
                                INNER JOIN contract ch ON ch.cny# = cd.cny#
                                    AND ch.record# = cd.contractkey,
                                icitem item
                              WHERE
                                cd.cny# = :1
                                AND   item.cny# = cd.cny#
                                AND   item.record# = cd.itemkey
                                AND   item.mrr = 'T'"
                . $itemFilter .
                " AND   cd.dbbcategory IS NULL";
            if ($locationKey != null) {
                $tempTableQry .= " AND  ch.melocationkey = " . $locationKey;
            }
            $results = QueryResult(array($tempTableQry, GetMyCompany()));
            if ($results != null) {
                Globals::$g->gQueryMgr->beginTrx($source);
                foreach ($results as $result) {
                    $qry = "UPDATE contractdetail cd SET
                            mrr = 'T',
                            dbbcategory = '" . $result["DBBCATEGORY"] . "'
                    WHERE
                        cd.cny# = :1
                        AND cd.record# = " . $result["RECORD#"];
                    $ok = $ok && ExecStmt(array($qry, GetMyCompany()));
                }
                $ok = $ok && Globals::$g->gQueryMgr->commitTrx($source);
            }
            if ($ok) {
                $locationFilter = $locationKey == null ? ' ' : " AND ch.melocationkey = $locationKey ";
                $qry = "SELECT DISTINCT * FROM (SELECT
                            ch.customerkey,
                            nvl(ch.melocationkey, 0) MELOCATIONKEY
                        FROM
                            contract ch
                            INNER JOIN contractdetail cd ON cd.cny# = ch.cny#
                                AND cd.contractkey = ch.record#
                            INNER JOIN icitem item ON item.cny# = ch.cny#
                                AND item.record# = cd.itemkey
                            LEFT OUTER JOIN contractmrrresolve mrr ON mrr.cny# = ch.cny#
                                AND mrr.contractdetailkey = cd.record#
                        WHERE
                            ch.cny# = :1
                            AND (cd.mrr = 'T' OR mrr.record# IS NOT NULL)
                            " . $itemFilter . $locationFilter . "
                        GROUP BY
                            ch.customerkey,
                            ch.melocationkey
                        ORDER BY
                            ch.melocationkey,
                            ch.customerkey DESC
                        )";
                $results = QueryResult(array($qry, GetMyCompany()));
                
                if (empty($results)) {
                    throw new IAException('No lines detected for update.', 'CN-1066');
                }

                foreach ($results as $result) {
                    $custRecs[$result['MELOCATIONKEY']][] = $result['CUSTOMERKEY'];
                }

                //Need to disable the mrr on the contract lines when the item passed has mrr flag unchecked.
                if ($ok) {
                    $locationFilter = $locationKey == null ? ' ' :
                        ' AND EXISTS (SELECT 1 from contract ch where ch.cny# = cd.cny# AND ch.record# = cd.contractkey ' .
                        "AND ch.melocationkey = $locationKey)";
                    
                    Globals::$g->gQueryMgr->beginTrx($source);
                    $qry = "UPDATE CONTRACTDETAIL cd
                        SET mrr = 'F', dbbcategory = null
                        WHERE (cd.cny#, cd.itemkey) in (
                            SELECT item.cny#, item.record#
                            FROM icitem item
                            WHERE item.cny# = :1 AND item.mrr = 'F' " . $itemFilter . " )
                            AND (cd.mrr = 'T' OR cd.dbbcategory IS NOT NULL)" . $locationFilter;
                    $ok = $ok && ExecStmt(array($qry, GetMyCompany()));
                    $ok = $ok && Globals::$g->gQueryMgr->commitTrx($source);
                }
            }
        } else {
            //CustomerIds are passed. So we need to just reprocess for those customerIds. DO nothing else.
            $locationFilter = $locationKey == null ? " " : " AND ch.melocationkey = $locationKey ";
            $qry = "SELECT DISTINCT * FROM (SELECT
                        ch.customerkey,
                        nvl(ch.melocationkey, 0) MELOCATIONKEY
                    FROM
                        contract ch
                        INNER JOIN customer cust ON cust.cny# = ch.cny#
                            AND cust.record# = ch.customerkey
                        INNER JOIN contractdetail cd ON cd.cny# = ch.cny#
                            AND cd.contractkey = ch.record#
                        INNER JOIN icitem item ON item.cny# = ch.cny#
                            AND item.record# = cd.itemkey
                        LEFT OUTER JOIN contractmrrresolve mrr ON mrr.cny# = ch.cny#
                            AND mrr.contractdetailkey = cd.record#
                    WHERE
                        ch.cny# = :1
                        AND (cd.mrr = 'T' OR mrr.record# IS NOT NULL)
                        $locationFilter
                        AND cust.customerid IN ($custIdStr)
                    GROUP BY
                        ch.customerkey,
                        ch.melocationkey
                    ORDER BY
                        ch.melocationkey,
                        ch.customerkey DESC
                    )";
            $results = QueryResult(array($qry, GetMyCompany()));
            
            if (empty($results)) {
                throw new IAException('No lines detected for update.', 'CN-1066');
            }
            
            foreach ($results as $result) {
                $custRecs[$result['MELOCATIONKEY']][] = $result['CUSTOMERKEY'];
            }
        }
        $params["EMAILID"] = $emailId ?? "<EMAIL>";


        $publisher = new ims_publish_1(IMS_MODE_NONBLOCKING, IMS_PROCESS_REMOTE, IMS_MODE_QUEUED);
//            $publisher->SetUser("intacct_dev");
        global $_userid;
        $cny = GetMyCompany();
        $ret = QueryResult(array("SELECT RECORD# FROM userinfo WHERE cny# = :1 and loginid = :2", GetMyCompany(), 'intacct'));
        $userkey = $ret[0]['RECORD#'] ?? "1";
        $_userid = $userkey."@$cny";
        foreach ($custRecs as $key => $custRec) {
            $payload = [
                'CUSTOMERRECORDS' => $custRec,
                'RUN_PARAMS'    => $params,
            ];
            $credentials['INTACCT_CNYTITLE'] = GetMyCompanyTitle();
            $credentials['INTACCT_LOGIN'] = GetMyLogin();
            $credentials['INTACCT_PASSWORD'] = GetMyPassword();
            $credentials['INTACCT_USERID'] = $userkey . '@' . GetMyCompany() . '@' . GetMyApp();
            $credentials['INTACCT_LOCATIONID'] = $key == 0 ?  null : $key ;
            $ok = $publisher->PublishMsg('intacct_dev', 'CNDBB', 'DBB_REPOST', '', $payload, $credentials, $response);
        }

        if (!$ok) {
            throw new IAException('Unable to update lines with dbb category.', 'CN-1065');
        }
    }

    /**
     * @param array $args
     *
     * @return bool
     */
    public function reprocessDBBPostingsOffline($args) {
        $ok = true;
        $successCnt = 0;
        $failCnt = 0;
        $errMsg = "";
        if ($args["CUSTOMERRECORDS"] != null && $args["CUSTOMERRECORDS"] != " ") {
            foreach ($args["CUSTOMERRECORDS"] as $custRec) {
                $ok = $this->repostContractDBB($custRec);
                if ($ok) {
                    $successCnt++;
                } else if (HasErrors()) {
                    $errMsg .= nl2br(Globals::$g->gErr->myToString(false)) . "<br/><br/>";
                    Globals::$g->gErr->Clear();
                    $failCnt++;
                } else {
                    $custId = EntityManager::GetListQuick('customer', ['CUSTOMERID'], ['RECORDNO' => $custRec]);
                    $errMsg .= "Could not process DBB journal entries for customer  " . $custId[0]['CUSTOMERID'] . "<br/><br/>";
                    $failCnt++;
                }
            }
        }
//        $errMsg = "Couldn't process DBB for customer records: " . $errMsg;
        $this->sendCompletionEmail($successCnt, $failCnt, $errMsg, $args['RUN_PARAMS']['EMAILID']);
        return $ok;
    }

    /**
     * @param int $successCnt
     * @param int $failCnt
     * @param string $err
     * @param array|string $emailId
     *
     * @return int
     */
    public function sendCompletionEmail($successCnt, $failCnt, $err, $emailId)
    {
        $fromEmail = "<EMAIL>";
        $replyTo = "<EMAIL>";
        $companyName = GetMyCompanyName();
        $sub = 'DBB reprocess results for ' . $companyName;

        $msg = 'Dear ' . $emailId . ',';
//        $msg .= '<br/><br/><b>DBB reprocess results for ' .$companyName . '</b>';
        $msg .= '<br/><br/>Number of customers processed successfully: ' . $successCnt;
        $msg .= '<br/><br/>Number of errors:' . $failCnt;
        if ($failCnt > 0) {
            $msg .= '<br/><br/>Error details:<br/><br/>' . $err;
            $msg .= '<br/><br/><br/>No journal entries were posted for the customers with error conditions. Correct the errors for the applicable contract(s) and then try reprocessing the DBB data again.';
        }
        $msg .= "<br/><br/><p>Thank you,";
        $msg .= "<br>Intacct support team";

        $email = new IAEmail($emailId);
        $email->contenttype = 'text/html; charset="' . isl_get_charset() . '"';
        $email->mime_headers = "Importance: high\n";
        $email->_from = "< " . $fromEmail . " >";
        $email->_reply_to = "< " . $replyTo . " >";
        $email->subject = $sub;
        $email->body = $msg;
        $ok = $email->send();

        if ( ! $ok) {
            Globals::$g->gErr->addIAError('CN-0513', __FILE__ . ":" . __LINE__, _("Oops, '$email' email address is invalid. <br />Enter a valid address, then try again."), ['EMAILTO' => $email]);
        }

        return $ok;
    }

    /**
     * @param string[] $cnIds
     * @param bool     $nonMEA whether to only rebuild schedules resolves of non mea contract lines
     *
     * @throws IAException
     */
    public function rebuildContractSchedulesResolve($cnIds, $nonMEA)
    {
        $byCnIDs = false;
        $mgr = Globals::$g->gManagerFactory->getManager('contract');

        if (isset($cnIds) && isNonEmptyArray($cnIds)) {
            $filter = ['CONTRACTID', 'in', $cnIds];
            $byCnIDs = true;
        } else {
            $filter = ['STATE', '=', ContractManager::STATE_IN_PROGRESS];
        }
        $activeContracts = $mgr->GetList([
            'selects' => ['RECORDNO', 'CONTRACTID'],
            'filters' => [[ $filter] ],
        ]);
        $cnIdsToRebuild = INTACCTarray_project($activeContracts, 'CONTRACTID');
        if ($byCnIDs && count($cnIdsToRebuild) != count($cnIds)) {
            $missingCnIds = array_diff($cnIds, $cnIdsToRebuild);
            $missingCnIdsString = implode(',', $missingCnIds);
            throw IAException::newIAException('CN-1074',
                'Unable to retrieve the following contract IDs: ' . $missingCnIdsString,
                ['MISSING_CN_IDS_STRING' => $missingCnIdsString]
            );
        }
        $errs = [];
        foreach ($activeContracts as $activeContract) {
            try {
                $this->rebuildOneContractSchedulesResolve($activeContract['CONTRACTID'], null, false, '', $nonMEA);
            } catch (Exception $ex) {
                $errs[$activeContract['CONTRACTID']] = $activeContract['CONTRACTID'] . ' = ' . $ex->getMessage();
            }
        }
        if ($errs) {
            $implodedFailedIdString = implode(',', array_keys($errs));
            $implodedFailedErrMsg = implode(',', $errs);

            $msg = "Failed IDs: $implodedFailedIdString >>> $implodedFailedErrMsg";

            throw IAException::newIAException('CN-1075', $msg,
                [
                    'IMPLODED_FAILED_ID_STRING' => $implodedFailedIdString,
                    'IMPLODED_FSILED_ERR_MSG' => $implodedFailedErrMsg
                ]);
        }
    }

    /**
     * @param string                         $contractId
     * @param string|int|string[]|int[]|null $contractDetailKeys
     * @param bool                           $fromMEA
     * @param string                         $bundleType
     * @param bool                           $onlyNonMEALines whether to only rebuild schedules resolves of non mea
     *                                                        contract lines
     *
     * @throws Exception
     */
    public function rebuildOneContractSchedulesResolve($contractId, $contractDetailKeys=null, $fromMEA = false, $bundleType = '', $onlyNonMEALines = false)
    {
        $source='ContractWFHandler::rebuildOneContractSchedulesResolve';
        Globals::$g->gQueryMgr->beginTrx($source);

        try{
            if ($contractDetailKeys !== null && !is_array($contractDetailKeys)) {
                $contractDetailKeys = [$contractDetailKeys];
            }

            /** @var ContractBundleInfo[][] $bundleInfos */
            $bundleInfos = $this->contractObjectHandler->getContractBundleInfos($contractId, true, true, $fromMEA, $bundleType);

            if ($bundleInfos !== null && $bundleInfos['FULL_LIST'] != null) {
                $this->contractObjectHandler->createLogicalBundlesForRemainingContractLines($contractId, $bundleInfos, $fromMEA);
                if ( !isset($bundleInfos['JOURNAL1']) ) {
                    //this means that there is no MEA for JOURNAL1
                    //do the simple resolve for all lines with JOURNAL1 schedules
                    $cnDetailKeys = $this->contractObjectHandler->getMEAContractDetailKeysByContractId($contractId);
                    $this->innerRebuildOneContractSchedulesResolve($contractDetailKeys, $cnDetailKeys, true, false);
                } else if (!isset($bundleInfos['JOURNAL2'])) {
                    //this means that there is no MEA for JOURNAL2
                    //do the simple resolve for all lines with JOURNAL2 schedules
                    $cnDetailKeys = $this->contractObjectHandler->getMEAContractDetailKeysByContractId($contractId);
                    $this->innerRebuildOneContractSchedulesResolve($contractDetailKeys, $cnDetailKeys, false, true);
                }
                //this loop will create resolves for J1 first then J2
                for ($i = 0; $i < 2; $i++) {
                    $type = $i == 0 ? 'JOURNAL1' : 'JOURNAL2';
                    $applyToJournal1 = ($type === 'JOURNAL1');
                    $applyToJournal2 = ($type === 'JOURNAL2');
                    if ($i == 0 && $bundleInfos['NEW_J1'] != null && $bundleInfos['NEW_J1'] != 'T') {
                        continue;
                    }
                    if ($i == 1 && $bundleInfos['NEW_J2'] != null && $bundleInfos['NEW_J2'] != 'T') {
                        continue;
                    }

                    $bundlesByType = $bundleInfos[$type];
                    assert($applyToJournal1 || $applyToJournal2);
                    if ($bundlesByType != null) {
                        foreach ($bundlesByType as $oneBundleSet) {
                            if (!is_array($oneBundleSet)) {
                                $oneBundleSets[] = $oneBundleSet;
                            } else {
                                $oneBundleSets = $oneBundleSet;
                            }
                            foreach ($oneBundleSets as $oneBundle) {
                                if ($contractDetailKeys === null ||
                                    (is_array($contractDetailKeys) &&
                                     isNonEmptyArray(array_intersect($contractDetailKeys, $oneBundle->getContractDetailKeys())))) {
                                    if ($oneBundle->getIsNoFairValue()) {
                                        assert(arrayCount($oneBundle->getContractDetailKeys()) == 1);
                                        $cnDetailKey = $oneBundle->getContractDetailKeys()[0];
                                        $cnDetail = $this->contractObjectHandler->getContractDetailObject($cnDetailKey, true, true,
                                            true, false);
                                        $this->callSimpleResolveForOneContractDetailObject($cnDetail, $applyToJournal1, $applyToJournal2);
                                    } elseif (!$onlyNonMEALines) {
                                        $cnDetailArr = [];
                                        foreach ($oneBundle->getContractDetailKeys() as $oneCDKey) {
                                            $innerDetailObject = $this->contractObjectHandler->getContractDetailObject($oneCDKey, true, true,
                                                true, false);
                                            $cnDetailArr[] = $innerDetailObject;
                                        }
                                        $oneBundle->setApplyToJournal1($applyToJournal1);
                                        $oneBundle->setApplyToJournal2($applyToJournal2);
                                        $oneBundle->setContractDetails($cnDetailArr);

                                        $resolve = new ContractMEARevenueScheduleResolveHandler($oneBundle);
                                        $resolve->createResolveEntries(true);
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                $cnDetailKeys = $this->contractObjectHandler->getMEAContractDetailKeysByContractId($contractId);
                $this->innerRebuildOneContractSchedulesResolve($contractDetailKeys,$cnDetailKeys, true, true);
            }
            $ok = Globals::$g->gQueryMgr->commitTrx($source);
            if (!$ok) {
                throw new IAException('Unable to commit to database', 'CN-1064');
            }
        } catch (Exception $e) {
            Globals::$g->gQueryMgr->rollbackTrx($source);
            throw $e;
        }
    }

    /**
     * @param int[] $contractDetailKeys
     * @param int[] $allDetailKeys
     * @param bool  $applyToJ1
     * @param bool  $applyToJ2
     */
    private function innerRebuildOneContractSchedulesResolve( $contractDetailKeys, $allDetailKeys, $applyToJ1 , $applyToJ2)
    {
        foreach ( $allDetailKeys as $oneDetailKey ) {
            if ($contractDetailKeys === null || in_array($oneDetailKey, $contractDetailKeys)) {
                $contractDetail = $this->contractObjectHandler->getContractDetailObject($oneDetailKey, true, true,
                                                                                        true, false);
                if ($contractDetail->getTotalFlatAmount() != 0) {
                    $this->callSimpleResolveForOneContractDetailObject($contractDetail, $applyToJ1, $applyToJ2);
                }
            }
        }
    }

    /**
     * @param ContractDetailInfo $contractDetail
     * @param bool               $applyToJ1
     * @param bool               $applyToJ2
     */
    private function callSimpleResolveForOneContractDetailObject( $contractDetail, $applyToJ1 , $applyToJ2 )
    {
        $revSch1 = $applyToJ1 ? $contractDetail->getContractRevenueSchedule() : null;
        $revSch2 = $applyToJ2 ? $contractDetail->getContractRevenue2Schedule() : null;

        $skipResolve = true;

        if ( ($applyToJ1 === true && $applyToJ2 === false) && $revSch1 !== null ) {
            $skipResolve = false;
        } else if (($applyToJ2 === true && $applyToJ1 === false) && $revSch2 !== null ) {
            $skipResolve = false;
        } else if ( $applyToJ2 === true && $applyToJ1 === true ) {
            $skipResolve = false;
        }

        if ( $skipResolve === false ) {
            $resolve = new ContractSimpleRevenueScheduleResolveHandler(
                $revSch1,
                $revSch2,
                $contractDetail->getContractBillingSchedule(),
                $contractDetail->getContractPaymentSchedule()
            );
            $resolve->createResolveEntries(true);
        }
    }

    /**
     * @param int $custKey
     *
     * @return bool
     */
    public function repostContractDBB ($custKey)
    {
        $ok = true;
        $cnDetail = null;
        $source = 'ContractWFHandler::repostContractDBB';
        assert($custKey);

        $mrrEngine = BaseContractMRRLifeCycleEngine::getInstance();
        $mrrEngine->setIsRepostingByCustomer(true);

        $mgr = Globals::$g->gManagerFactory->getManager('contractdetail');
        $cnDetails = $mgr->GetList([
            'filters' => [[
                ['CUSTOMERKEY', '=', $custKey],
                ['DBBCATEGORY', 'ISNOTNULL']
            ]],
            'orders' => [
                ['BEGINDATE', 'asc'],
                ['RECORDNO', 'asc'],
            ]
        ]);

        Globals::$g->gQueryMgr->beginTrx($source);

        $ok = $ok && $this->deleteAllMRRPostings($custKey);

        foreach ($cnDetails as $cnDetail) {
            $ok = $ok && $mgr->repostContractDBB($cnDetail);
        }
        if ($cnDetail !== null) {
            $ok = $ok && $mgr->finishRepostContractDBB($cnDetail);
        }
        $ok = $ok && Globals::$g->gQueryMgr->commitTrx($source);
        if (!$ok) {
            $custId = EntityManager::GetListQuick('customer', ['CUSTOMERID'], ['RECORDNO' => $custKey]);
            Globals::$g->gErr->addIAError('CN-0514', __FILE__ . ':' . __LINE__, "Could not process DBB journal entries for customer $custId", ['CUSTID' => $custId]);
            Globals::$g->gQueryMgr->rollbackTrx($source);
        }

        return $ok;
    }

    /**
     * @param int $custKey
     *
     * @return bool
     */
    private function deleteAllMRRPostings($custKey)
    {
        $ok = true;
        $mrrEngine = BaseContractMRRLifeCycleEngine::getInstance();
        try {
            $mrrEngine->deleteAllMRRPostingsForCustomer($custKey);
        } catch(Exception $e) {
            ContractUtil::addThrowableError($e, __FILE__ . ':' . __LINE__);
            $ok = false;
        }
        return $ok;
    }

    /**
     * @param string      $contractId
     * @param string      $lineNo
     * @param array[]     $posBillingSchEntries
     * @param array[]     $negBillingSchEntries
     * @param string|null $contractDetailKey
     */
    private function queryBillingScheduleEntries($contractId, $lineNo, &$posBillingSchEntries, &$negBillingSchEntries, &$contractDetailKey)
    {
        $mgr = Globals::$g->gManagerFactory->getManager('contractbillingscheduleentry');
        $selects = ['RECORDNO', 'AMOUNT', 'BASEAMOUNT', 'STATE', 'CONTRACTID'];
        $filters = [['CONTRACTID', '=', $contractId]];
        if ($lineNo) {
            $selects[] = 'CONTRACTDETAILKEY';
            $filters[] = ['CONTRACTLINENO', '=', $lineNo];
        }
        $results = $mgr->GetList([
                                     'selects' => $selects,
                                     'filters' => [$filters]
                                 ]);

        $contractDetailKey = $results[0]['CONTRACTDETAILKEY'] ?? null;

        $posBillingSchEntries = [];
        $negBillingSchEntries = [];
        foreach ( $results as $result ) {
            $recordNo = $result['RECORDNO'];
            if ($result['AMOUNT'] < 0) {
                $negBillingSchEntries[$recordNo] = $result;
            } else {
                $posBillingSchEntries[$recordNo] = $result;
            }
        }
        if (empty($posBillingSchEntries)) {

            if(empty($negBillingSchEntries)){
                $msg = "Maybe invalid $contractId since it has no billing scbedule.";
                $errorIno = new ContractI18nErrorInfo('CN-1076', ['CONTRACT_ID' => $contractId], $msg);
            }else{
                $msg = "Maybe invalid $contractId since it has no positive billing schedule.";
                $errorIno = new ContractI18nErrorInfo('CN-1077', ['CONTRACT_ID' => $contractId], $msg);
            }
            throw IAException::newIAException($errorIno->getErrorId(), $errorIno->getErrorMessage(),
                $errorIno->getPlaceholders());
        }
    }

    /**
     * @param int      $sign
     * @param string[] $billingSchEntryKeys
     *
     * @return false|string[][]
     */
    private function queryPaymentInfos ($sign, $billingSchEntryKeys)
    {
        $qryStmt = "
            select paidpay.record#, paidpay.parentpymt parentrecordkey
              , paidpay.paymentkey payrecordkey, paidpay.payitemkey payentrykey
              , paidpay.recordkey paidrecordkey, paidpay.paiditemkey paidentrykey
              , pay.line_no paylineno, paid.line_no paidlineno 
              , paidpay.amount paidpayamount, paidpay.invtrxamt, paidpay.invbaseamt, paidpay.paymentdate
              , pay.billablecontractschentrykey paybillkey, paid.billablecontractschentrykey paidbillkey
              , parentpay.recordtype parentpayrecordtype, parentpay.state, parentpay.totalentered
              , parentpay.whencreated parentpaywhencreated, payrecord.whencreated payrecordwhencreated, paidrecord.whencreated paidrecordwhencreated
              , paybatch.created paybatchcreated, parentpay.whenpaid parentpaywhenpaid, payrecord.whenpaid paywhenpaid, paidrecord.whenpaid paidwhenpaid
              , parentpay.recordid, parentpay.docnumber
              , void.paymentkey voidedpaymentkey, void.voidpaymentkey voidpaymentkey
            from prentrypymtrecs paidpay 
              inner join prentry paid on paid.cny#=paidpay.cny# and paid.record#=paidpay.paiditemkey
              inner join prentry pay on pay.cny#=paidpay.cny# and pay.record#=paidpay.payitemkey
              inner join prrecord paidrecord on paidrecord.cny#=paidpay.cny# and paidrecord.record#=paidpay.recordkey
              inner join prrecord payrecord on payrecord.cny#=paidpay.cny# and payrecord.record#=paidpay.paymentkey
              inner join prrecord parentpay on parentpay.cny#=paidpay.cny# and parentpay.record#=paidpay.parentpymt
              inner join prbatch  paybatch on paybatch.cny#=payrecord.cny# and paybatch.record#=payrecord.prbatchkey
              left outer join voidlink void on void.cny#=pay.cny# and void.voidpaymentkey=paidpay.parentpymt
            where paid.cny# = :1 
              and 
        ";
        $qrySpec = [ $qryStmt, GetMyCompany() ];
        $cond = ($sign == -1) ? 'pay.billablecontractschentrykey' : 'paid.billablecontractschentrykey';
        $qrySpec = PrepINClauseStmt($qrySpec, $billingSchEntryKeys, $cond);
        $qrySpec[0] .= " order by paidpay.record#";

        $results = QueryResult($qrySpec);
        if ($results === false) {
            throw new IAException ("Unable to query payment infos", 'CN-1067');
        }

        return $results;
    }

    /**
     * @param string[][] $paymentInfos
     * @param string[]   $paymentInfo
     *
     * @return string[]|null
     */
    private function findSourcePaymentReversal($paymentInfos, $paymentInfo)
    {
        $foundSourcePaymentInfo = null;
        $voidedPaymentKey = null;
        $searchByPayRecordKey = $searchByParentAndEntryKeys = $searchByParentAndPayRecordKey = false;

        if (isset($paymentInfo['VOIDEDPAYMENTKEY'])) {
            $voidedPaymentKey = $paymentInfo['VOIDEDPAYMENTKEY'];
            if ($paymentInfo['PARENTRECORDKEY'] == $paymentInfo['PAYRECORDKEY']) {
                // When parent record key and pay record key are equal, that means we are reversing cash receipt payment
                $searchByPayRecordKey = true;
            } else {
                $searchByParentAndEntryKeys = true;
            }
        } elseif ($paymentInfo['PARENTPAYRECORDTYPE'] == 'ra') {
            $voidedPaymentKey = $paymentInfo['PAYRECORDKEY'];
            $searchByParentAndPayRecordKey = true;
        }

        if ($voidedPaymentKey) {
            foreach ( $paymentInfos as $srcPmtInfo ) {
                if ($searchByPayRecordKey && $srcPmtInfo['PAYRECORDKEY'] == $voidedPaymentKey) {
                    // source payment was a cash receipt
                    $foundSourcePaymentInfo = $srcPmtInfo;
                    break;
                } elseif ($searchByParentAndEntryKeys && $srcPmtInfo['PARENTRECORDKEY'] == $voidedPaymentKey
                          && $srcPmtInfo['PAYENTRYKEY'] == $paymentInfo['PAYENTRYKEY']
                          && $srcPmtInfo['PAIDENTRYKEY'] == $paymentInfo['PAIDENTRYKEY']) {
                    // source payment was a credit application
                    $foundSourcePaymentInfo = $srcPmtInfo;
                    break;
                } elseif ($searchByParentAndPayRecordKey && $srcPmtInfo['PARENTRECORDKEY'] == $voidedPaymentKey && $srcPmtInfo['PAYRECORDKEY'] == $voidedPaymentKey) {
                    // source payment was an AR adjustment
                    $foundSourcePaymentInfo = $srcPmtInfo;
                    break;
                }
            }
        }

        return $foundSourcePaymentInfo;
    }

    /**
     * @param int      $sign
     * @param string[] $billingSchEntryKeys
     */
    private function rebuildPaymentScheduleEntriesBySign($sign, $billingSchEntryKeys)
    {
        if ($billingSchEntryKeys) {
            $paymentInfos = $this->queryPaymentInfos($sign, $billingSchEntryKeys);
            foreach ( $paymentInfos as $paymentInfo ) {
                // $glPostingDate = $result['POSTINGDATE'];
                // $paymentDate = $result['PAYMENTDATE'];
                // TODO: I think we should be using paymentDate / glPostingDate combo like in ContractPaymentHandler::processContractBalances rather than receipt date
                $receiptDate = $paymentInfo['PAYRECORDWHENCREATED'];

                $paymentAmt = $paymentInfo['PAIDPAYAMOUNT'];
                $invAmt = $paymentInfo['INVTRXAMT'] ? : $paymentInfo['INVBASEAMT'];
                $payItemKey = $paymentInfo['PAYENTRYKEY'];
                $billKeyField = ($sign == -1) ? 'PAYBILLKEY' : 'PAIDBILLKEY';
                $billSchEntryKey = $paymentInfo[$billKeyField];

                if ( $paymentAmt > 0 ) {
                    ContractPaymentSchedule::handleReceivePayment(
                        $billSchEntryKey,
                        $payItemKey,
                        ibcmul($invAmt, $sign),
                        $receiptDate,
                        $paymentInfo['PAYMENTKEY']
                    );
                } else {
                    $sourcePaymentInfo = $this->findSourcePaymentReversal($paymentInfos, $paymentInfo);
                    ContractUtil::assert($sourcePaymentInfo !== null);
                    throw new IAException('Source item pr entry is not provided.', 'CN-1068');
                    //TODO::need to fix this call, need to provide soucePmtPrEntrykey array
//                    ContractPaymentSchedule::handlePaymentReversal(
//                        $billSchEntryKey, $payItemKey, ibcmul(ibcmul($invAmt, $sign), -1), $receiptDate, $sourcePaymentInfo['RECORD#']
//                    );
                }
            }
        }
    }

    /**
     * @param string[] $billingSchEntryKeys
     * @param array[]  $oriPaymentSchEntries    Keyed by billing sch entry key
     * @param array[]  $restOfPaymentSchEntries Keyed by payment sch entry key
     *
     * @return array[]
     */
    private function queryPaymentScheduleEntries($billingSchEntryKeys, &$oriPaymentSchEntries, &$restOfPaymentSchEntries)
    {
        $mgr = Globals::$g->gManagerFactory->getManager('contractpaymentscheduleentry');
        $qryParams = [
            'selects' => [ 'RECORDNO', 'ORIPMTSCHEDULEENTRYKEY', 'BILLSCHEDULEENTRYKEY', 'AMOUNT', 'BASEAMOUNT', 'POSTED', 'ACTUALPOSTINGDATE', 'CONTRACTID' ],
            'filters' => [ [
                               [ 'TYPE', '=', 'Payment' ],
                               [ 'BILLSCHEDULEENTRYKEY', 'IN', $billingSchEntryKeys ],
                           ] ],
            'orders'  => [ [ 'RECORDNO', 'asc' ] ],
            'options' => [ 'noDBSorts' => true ],  // this is paranoia.  I think sorting by posting date, recordno should be okay too
        ];
        $results = $mgr->GetList($qryParams);
        $oriPaymentSchEntries = [];
        $restOfPaymentSchEntries = [];

        foreach ( $results as $result ) {
            $billSchEntryKey = $result['BILLSCHEDULEENTRYKEY'];
            if (!isset($oriPaymentSchEntries[$billSchEntryKey])) {
                ContractUtil::assert(!isArrayValueProvided($result, 'ORIPMTSCHEDULEENTRYKEY'));
                $oriPaymentSchEntries[$billSchEntryKey] =  $result;
            } else {
                $restOfPaymentSchEntries[$result['RECORDNO']] = $result;
            }
        }
        $missingKeys = array_diff($billingSchEntryKeys, array_keys($oriPaymentSchEntries));
        if ($missingKeys) {

            $implodeStringMissingKeys = implode(', ', $missingKeys);
            throw IAException::newIAException('CN-1078',
                'Missing payment schedule for billing schedule entry keys: ' . $implodeStringMissingKeys,
                ['IMPLODE_STRING_MISSING_KEYS' => $implodeStringMissingKeys]
            );
        }

        return $results;
    }

    /**
     * @param array[] $oriPaymentSchEntries
     * @param array[] $posBillingSchEntries
     * @param array[] $negBillingSchEntries
     *
     * @throws IAException
     */
    private function resetOriginalPaymentScheduleEntries ($oriPaymentSchEntries, $posBillingSchEntries, $negBillingSchEntries)
    {
        foreach ( $oriPaymentSchEntries as $billSchEntryKey => $oriPaymentSchEntry ) {
            $billSchEntry = null;
            if (isset($posBillingSchEntries[$billSchEntryKey])) {
                $billSchEntry = $posBillingSchEntries[$billSchEntryKey];
            } elseif (isset($negBillingSchEntries[$billSchEntryKey])) {
                $billSchEntry = $negBillingSchEntries[$billSchEntryKey];
            }
            ContractUtil::assert($billSchEntry !== null);
            $pmtSchEntryKey = $oriPaymentSchEntry['RECORDNO'];

            $qry = "update contractscheduleentry set amount=:3, baseamount=:4, state=:5, posted=null, actualpostingdate=null where cny#=:1 and record#=:2";
            $state = ($billSchEntry['STATE'] == 'T') ? 'T' : 'O';  // if billing schedule is terminated, set state to terminated, otherwise set it to open
            $ok = ExecStmt(array($qry, GetMyCompany(), $pmtSchEntryKey, $billSchEntry['AMOUNT'], $billSchEntry['BASEAMOUNT'], $state));
            if (!$ok) {
                throw IAException::newIAException('CN-1079',
                    "Error updating original payment schedule entry: $pmtSchEntryKey (billing schedule entry: $billSchEntryKey)",
                    ['PMT_SCH_ENTRY_KEY' => $pmtSchEntryKey, 'BILL_SCH_ENTRY_KEY' => $billSchEntryKey]);
            }
        }

    }

    /**
     * @param array[] $restOfPaymentSchEntries
     *
     * @throws IAException
     */
    private function cleanupRestOfPaymentScheduleEntries ($restOfPaymentSchEntries)
    {
        if ($restOfPaymentSchEntries) {
            $qryStmt = "delete from contractscheduleentry where cny#=:1 and ";
            $qrySpec = [$qryStmt, GetMyCompany()];
            $qrySpec = PrepINClauseStmt($qrySpec, array_keys($restOfPaymentSchEntries), 'record#');
            $ok = ExecStmt($qrySpec);
            if (!$ok) {
                throw new IAException("Error deleting payment schedule entries", 'CN-1080');
            }
        }
    }


    /**
     * @param string $contractIdLineNo
     */
    public function rebuildPaymentScheduleEntriesForOneContract($contractIdLineNo)
    {
        $source = 'ContractWFHandler::rebuildPaymentScheduleEntriesForOneCnId';

        [$contractId, $lineNo] = explode(':', $contractIdLineNo);

        $this->queryBillingScheduleEntries($contractId, $lineNo, $posBillingSchEntries, $negBillingSchEntries, $contractDetailKey);
        $billingSchEntryKeys = array_union(array_keys($posBillingSchEntries), array_keys($negBillingSchEntries));
        $this->queryPaymentScheduleEntries($billingSchEntryKeys, $oriPaymentSchEntries, $restOfPaymentSchEntries);

        Globals::$g->gQueryMgr->beginTrx($source);

        try {
            $this->resetOriginalPaymentScheduleEntries($oriPaymentSchEntries, $posBillingSchEntries, $negBillingSchEntries);
            if ($restOfPaymentSchEntries) {
                $this->cleanupRestOfPaymentScheduleEntries($restOfPaymentSchEntries);
                $this->rebuildOneContractSchedulesResolve($contractId, $contractDetailKey);
            }
            $this->rebuildPaymentScheduleEntriesBySign(1, array_keys($posBillingSchEntries));
            $this->rebuildPaymentScheduleEntriesBySign(-1, array_keys($negBillingSchEntries));

            $ok = Globals::$g->gQueryMgr->commitTrx($source);
            if (!$ok) {
                throw new IAException('Unable to commit to database', 'CN-1064');
            }
        } catch (Exception $e) {
            Globals::$g->gQueryMgr->rollbackTrx($source);
            throw $e;
        }
    }

    /**
     * @param string[] $contractIds
     */
    public function rebuildPaymentScheduleEntries($contractIds)
    {
        $errs = [];
        foreach ($contractIds as $contractId) {
            try {
                $this->rebuildPaymentScheduleEntriesForOneContract($contractId);
            } catch (Exception $ex) {
                $errs[$contractId] = $contractId . ' = ' . $ex->getMessage();
            }
        }
        if ($errs) {
            $implodedFailedIdString = implode(',', array_keys($errs));
            $implodedFsiledErrMsg = implode(',', $errs);

            $msg = "Failed IDs: $implodedFailedIdString >>> $implodedFsiledErrMsg";

            throw IAException::newIAException('CN-1075', $msg,
                [
                    'IMPLODED_FAILED_ID_STRING' => $implodedFailedIdString,
                    'IMPLODED_FSILED_ERR_MSG' => $implodedFsiledErrMsg
                ]);
        }
    }
}