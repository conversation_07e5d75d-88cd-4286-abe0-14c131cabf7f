<?php
/**
 * File ContractManageScheduleFormEditor.cls contains the class ContractManageScheduleFormEditor
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2015 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * Manage schedule form hanlder
 *
 * Class ContractManageScheduleFormEditor
 */
class ContractManageScheduleFormEditor extends FormEditor
{
    use RevRecScheduleFiltersEditorTrait;
    
    protected const localTokens = [
        'IA.VIEW_RUN',
        'IA.UPDATE_PERCENTAGE_COMPLETE_SCHEDULES',
        'IA.BULK_ACTION_FOR_PERCENT_COMPLETE_SCHEDULES_INITIATED_NOTIFICATION_BE_SENT_ONCE_COMPLETES',
        'IA.NO_CONTRACT_SCHEDULES_MATCHED_FOR_RECOGNITION',
        'IA.PERCENT_COMPLETE_REVENUE_SCHEDULES_UPDATED_OFFLINE_NOTIFICATION_SENT_ONCE_COMPLETES',
        'IA.BULK_ACTION_FOR_REVENUE_RECOGNITION_INITIATED_NOTIFICATION_BE_SENT_ONCE_COMPLETES',
        'IA.DIDNOT_FIND_ANY_ENTRIES_FOR_RUN',
        'IA.BULK_ACTION_FOR_EXPENSE_RECOGNITION_INITIATED_NOTIFICATION_BE_SENT_ONCE_COMPLETES',
        'IA.ENTRIES_POSTED_OFFLINE_AS_MAXIMUM_LIMIT_EXCEEDS_NOTIFICATION_SENT_ONCE_COMPLETES',
        'IA.NUMBER_OF_REVENUE_ENTRIES_POSTED',
        'IA.NUMBER_OF_EXPENSE_ENTRIES_POSTED',
        'IA.NUMBER_OF_REVENUE_ERRORS',
        'IA.NUMBER_OF_EXPENSE_ERRORS',
        'IA.NUMBER_OF_REVENUE_ENTRIES_IGNORED',
        'IA.NUMBER_OF_EXPENSE_ENTRIES_IGNORED',
        'IA.NOTIFICATION_WITH_ERROR_SENT',
        'IA.REVENUE_EXPENSE_ENTRIES_POSTED_OFFLINE_NOTIFICATION_SENT_ONCE_COMPLETES',
        'IA.PREVIEW',
        'IA.POST',
        'IA.POST_OFFLINE',
        'IA.PLEASE_SELECT_SCHEDULES',
        'IA.DELIVERED',
        'IA.UNDELIVERED',
        'IA.EXPENSE_ITEM',
    ];
    public const jsTokens = [
        'IA.MAXIMUM_POSTING_LIMIT_EXCEEDS_FOR_OFFLINE_PROCESS',
        'IA.AT_LEAST_ONE_ITEM_MUST_BE_SELECTED',
    ];
    /** @var bool $isExpenseEnabled */
    private $isExpenseEnabled;
    /** @var bool $isProjectSubscribed */
    private $isProjectSubscribed = false;
    const DEFAULT_MAX_RECORDS = 200;
    const MAX_ONLINE_POSTINGS = 200;
    const MAX_OFFLINE_POSTINGS = 2000;
    
    const MAX_REC_COUNT_ALL = -1;

    /**
     * @param array $params the parameters of the class
     */
    public function __construct($params = array())
    {
        $this->additionalTokens = array_merge($this->additionalTokens, self::localTokens);
        $this->textTokens = array_merge($this->textTokens, self::jsTokens);
        parent::__construct($params);
        $this->addAction(ContractManageScheduleManager::ACTION_LIST, 'processViewListAction');
        $this->addAction(ContractManageScheduleManager::ACTION_POST, 'processPostAction');
        $this->addAction(ContractManageScheduleManager::ACTION_CLEAR, 'processClearAction');
        $this->addAction(ContractManageScheduleManager::ACTION_HOLD, 'processHoldAction');
        $this->addAction(ContractManageScheduleManager::ACTION_RESUME, 'processResumeAction');
        $this->addAction(ContractManageScheduleManager::ACTION_DELIVER, 'processDeliverLinesAction');
        $this->addAction(ContractManageScheduleManager::ACTION_PERCENT_COMPLETE, 'processGeneratePOCAction');
        $this->kDefaultVerbActions['submit'] = $this->kSubmitAction;
        $this->kRequireVerbForAction['submit'] = $this->kSubmitAction;

        /* @var CNSetupManager $cnsetupMgr */
        $cnsetupMgr = Globals::$g->gManagerFactory->getManager('cnsetup');
        $this->isExpenseEnabled = $cnsetupMgr->isExpenseEnabled();
        $modMgr = Globals::$g->gManagerFactory->getManager('modules');
        $this->isProjectSubscribed = $modMgr->isModuleSubscribed("48.PROJACCT");
        
    }

    /**
     * Customer JS files
     *
     * @return array
     */
    protected function getJavaScriptFileNames()
    {
        return array('../resources/js/contractmanageschedule.js');
    }

    /**
     * @return bool
     */
    private function validateBusinessUser()
    {
        if (GetMyUserType() != 'B') {
            Globals::$g->gErr->addIAError('CN-0264', __FILE__ . ":" . __LINE__,
                                          sprintf('Only Business users can access %s.', $this->getTitle()),
                                          ['TITLE' => $this->getTitle()]);
            $this->state = $this->kErrorState;
            return false;
        }
        return true;
    }

    /**
     * Sets derived field values
     *
     * @param array $obj Array of attributes
     *
     * @return bool
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        // TODO: Add fields to hide for each action
        $toggleFields = [
            ContractManageScheduleManager::ACTION_POST => [],
            ContractManageScheduleManager::ACTION_CLEAR => [],
            ContractManageScheduleManager::ACTION_HOLD => [],
            ContractManageScheduleManager::ACTION_RESUME => [],
            ContractManageScheduleManager::ACTION_DELIVER => [],
        ];

        if (!$this->validateBusinessUser()) {
            return false;
        }

        $action = Request::$r->{$this->kAction};

        if (!in_array($action, ContractManageScheduleManager::VALID_ACTIONS) && !Request::$r->_errorTimeStamp) {
            $obj['ASOFDATE'] = GetCurrentDate();
            $obj['TYPEFILTER'] = 'R';
        }
        if ($obj['WHENCREATED'] == '') {
            $obj['WHENCREATED'] = GetCurrentDate();
        }
        $view = $this->getView();

        if ( $this->isProjectSubscribed  === true  && ContractProjectLinkHandler::isContractProjectLinkFeatureEnabled() === true ) {
            $view->findAndSetProperty(array('path' => 'GENERATEPROJECTREVREC'), array('hidden' => false));
            $view->findAndSetProperty(array('path' => 'GENERATEPROJECTREVRECMESSAGE'), array('hidden' => false));
            $view->findAndSetProperty(array('path' => 'PROJECTNGROUPID'), array('hidden' => false));
        }

        $dimensions = IADimensions::getActiveGLDimensions();

        // Check if the class dimension is enabled for GL
        if ( !array_key_exists('class', $dimensions) ) {
            $view->findAndSetProperty(array('path' => 'CLASSNGROUPID'), array('hidden' => true));
        }

        // Check if the asset dimension is enabled for GL
        if ( !array_key_exists('fixedasset', $dimensions) ) {
            $view->findAndSetProperty(array('path' => 'ASSETNGROUPID'), array('hidden' => true));
        }

        // Check if the project dimension is enabled for GL
        if ( !array_key_exists('project', $dimensions) ) {
            $view->findAndSetProperty(array('path' => 'PROJECTNGROUPID'), array('hidden' => true));
        }
        
        $cn_viewop = GetOperationId('cn/lists/contract/view');
        $cnd_viewop = GetOperationId('cn/lists/contractdetail/view');
        $cne_viewop = GetOperationId('cn/lists/contractexpense/view');
        $revsch_viewop = GetOperationId('cn/lists/contractrevenueschedule/view');
        $rev2sch_viewop = GetOperationId('cn/lists/contractrevenue2schedule/view');
        $expsch_viewop = GetOperationId('cn/lists/contractexpenseschedule/view');
        $exp2sch_viewop = GetOperationId('cn/lists/contractexpense2schedule/view');
        $cust_viewop = GetOperationId('ar/lists/customer/view');
        $item_viewop = GetOperationId('inv/lists/item/view');
        $jrnl_viewop = GetOperationId('gl/lists/journal/view');
        $prj_viewop = GetOperationId('pa/lists/project/view');
        $task_viewop = GetOperationId('pa/lists/task/view');
        $revtemplate_viewop = GetOperationId('cn/lists/contractrevenuetemplate/view');
        $exptemplate_viewop = GetOperationId('cn/lists/contractexpensetemplate/view');
        
        $type = ($obj['TYPEFILTER'] ?? null) ?: 'R';
        $isRevenue = ($type == 'R');
        
        if ($isRevenue) {
            $view->findAndSetProperty(['path' => 'EXPENSETEMPLATE'], ['hidden' => true]);
            $cnsch_viewop = $obj['JOURNALFILTER'] == 'J1' ? $revsch_viewop : $rev2sch_viewop;
            $template_viewop = $revtemplate_viewop;
            $line_viewop = $cnd_viewop;
        } else {
            $view->findAndSetProperty(['path' => 'REVRECTEMPLATE'], ['hidden' => true]);
            $cnsch_viewop = $obj['JOURNALFILTER'] == 'J1' ? $expsch_viewop : $exp2sch_viewop;
            $template_viewop = $exptemplate_viewop;
            $line_viewop = $cne_viewop;
        }
        
        if (!ContractUtil::isContractPostSchedulesShowAllEnabled()) {
            $this->removeMaxRecCountAll();
        }

        $sess = Session::getKey();

        $entries = &$obj['SCHEDULEENTRIES'];
        if ($entries != null) {
            foreach ($entries as $key => $schEntry) {
                $cnd_url = "editor.phtml?.op=$cnd_viewop&.sess=$sess&.r=".$schEntry['LINERECORDNO'].'&.popup=1';
                $entries[$key]['CONTRACTLINENOHTML'] =
                    '<a href=\'javascript:Launch( "' . $cnd_url . '" , "contractdetail");\' target1="_blank">' . $schEntry['CONTRACTLINENO']
                    . '</a>';
                
                $cne_url = "editor.phtml?.op=$cne_viewop&.sess=$sess&.r=".$schEntry['EXPRECORDNO'].'&.popup=1';
                $entries[$key]['EXPENSELINENOHTML'] =
                    '<a href=\'javascript:Launch( "' . $cne_url . '" , "contractexpense");\' target1="_blank">' . $schEntry['EXPENSELINENO']
                    . '</a>';
                
                $cn_url = "editor.phtml?.op=$cn_viewop&.sess=$sess&.r=".$schEntry['CONTRACTNO'].'&.popup=1';
                $entries[$key]['CONTRACTNOHTML'] = '<a href=\'javascript:Launch( "' . $cn_url
                    . '" , "contract");\' target1="_blank">'.$schEntry['CONTRACTNO'].'--'.util_encode($schEntry['CONTRACTNAME']).'</a>';

                $cust_url = "editor.phtml?.op=$cust_viewop&.sess=$sess&.r=".$schEntry['CUSTOMERID'].'&.popup=1';
                $entries[$key]['CUSTOMERIDHTML'] = '<a href=\'javascript:Launch( "' . $cust_url
                    . '" , "customerid");\' target1="_blank">'.$schEntry['CUSTOMERID'].'--'.$schEntry['CUSTOMERNAME'].'</a>';

                $item_url = "editor.phtml?.op=$item_viewop&.sess=$sess&.r=".$schEntry['ITEMID'].'&.popup=1';
                $entries[$key]['ITEMIDHTML'] = '<a href=\'javascript:Launch( "' . $item_url
                    . '" , "itemid");\' target1="_blank">'.$schEntry['ITEMID'].'--'.util_encode($schEntry['ITEMNAME']).'</a>';

                $jrnl_url = "editor.phtml?.op=$jrnl_viewop&.sess=$sess&.r=".$schEntry['JOURNALSYMBOL'].'&.popup=1';
                $entries[$key]['JOURNALHTML'] = '<a href=\'javascript:Launch( "' . $jrnl_url
                    . '" , "journal");\' target1="_blank">'.$schEntry['JOURNALSYMBOL'].'--'.$schEntry['JOURNALTITLE'].'</a>';

                $amt_url = "editor.phtml?.op=$cnsch_viewop&.sess=$sess&.r=".$schEntry['SCHEDULEKEY'].'&.popup=1';
                $entries[$key]['AMOUNTHTML'] = '<a href=\'javascript:Launch( "' . $amt_url
                    . '" , "amount");\' target1="_blank">'.glFormatCurrency($schEntry['AMOUNT']).'</a>';
                
                $totalamt_url = "editor.phtml?.op=$line_viewop&.sess=$sess&.r=" . $schEntry['LINERECORDNO'] . '&.popup=1';
                $entries[$key]['TOTALAMOUNTHTML'] =
                    '<a href=\'javascript:Launch( "' . $totalamt_url . '" , "contractdetail");\' target1="_blank">'
                    . glFormatCurrency($schEntry['TOTALAMOUNT']) . '</a>';
                
                if (!empty($schEntry['PROJECTID'])) {
                    $prj_url = "editor.phtml?.op=$prj_viewop&.sess=$sess&.r=".$schEntry['PROJECTID'].'&.popup=1';
                    $entries[$key]['PROJECTHTML'] = '<a href=\'javascript:Launch( "' . $prj_url
                        . '" , "project");\' target1="_blank">'.$schEntry['PROJECTID'].'--'.$schEntry['PROJECTNAME'].'</a>';
                }
                
                if (!empty($schEntry['TASKID'])) {
                    $task_url = "editor.phtml?.op=$task_viewop&.sess=$sess&.r=" . $schEntry['TASKID'] . '&.popup=1';
                    $entries[$key]['TASKHTML'] = '<a href=\'javascript:Launch( "' . $task_url . '" , "task");\' target1="_blank">'
                                                    . $schEntry['TASKID'] . '--'. $schEntry['TASKNAME'] . '</a>';
                }

                if (!empty($schEntry['TEMPLATENAME'])) {
                    $templateUrl = "editor.phtml?.op=$template_viewop&.sess=$sess&.r=".$schEntry['TEMPLATENAME'].'&.popup=1';
                    $entries[$key]['TEMPLATEHTML'] = '<a href=\'javascript:Launch( "' . $templateUrl
                                                    . '" , "contract");\' target1="_blank">'.$schEntry['TEMPLATENAME'].'</a>';
                }
            }
        }
        
        $scheduleAction = empty($obj['SCHEDULESELIGIBLETO']) ? ContractManageScheduleManager::ACTION_POST : $obj['SCHEDULESELIGIBLETO'];
        $obj['MAXRECCNT'] = ($obj['MAXRECCNT']) ?: self::DEFAULT_MAX_RECORDS;
        $obj['SORTBY'] = ($obj['SORTBY'] != '') ? $obj['SORTBY'] : ManageScheduleFilter::DEFAULT_SORT_BY;
        $obj['SORTORDER'] = ($obj['SORTORDER'] != '') ? $obj['SORTORDER'] : ManageScheduleFilter::DEFAULT_SORT_ORDER;
        $obj['SCHEDULESELIGIBLETO'] = $scheduleAction;
        $obj['ASONBEFOREDATE'] = (empty($obj['ASONBEFOREDATE']) && $scheduleAction != ContractManageScheduleManager::ACTION_DELIVER)
            ? GetCurrentDate()
            : $obj['ASONBEFOREDATE'];
        $obj['CHECK_SELECTED'] = false;

        $this->initializeTypeFilterField();

        $this->setJournalLabels();
        
        // Hide actions based on feature flags
        if (!ContractUtil::isBulkHoldEnabled()) {
            $this->removeAction($view, ContractManageScheduleManager::ACTION_HOLD);
        }
        if (!ContractUtil::isBulkResumeEnabled()) {
            $this->removeAction($view, ContractManageScheduleManager::ACTION_RESUME);
        }
        if (!ContractUtil::isBulkClearEnabled()) {
            $this->removeAction($view, ContractManageScheduleManager::ACTION_CLEAR);
        }
        if (!ContractUtil::isBulkDeliverEnabled()) {
            $this->removeAction($view, ContractManageScheduleManager::ACTION_DELIVER);
        }
        
        if (!empty($scheduleAction) && !empty($entries)) {
            $this->populateEntryDisplays($entries);
            
            $grid = [];
            $view->findComponents(['path' => 'SCHEDULEENTRIES'], EditorComponentFactory::TYPE_GRID, $grid);
            /** @var EditorGrid $grid */
            $grid = $grid[0] ?? null;
            
            if ($grid) {
                if (
                    $scheduleAction == ContractManageScheduleManager::ACTION_POST
                    || $scheduleAction == ContractManageScheduleManager::ACTION_CLEAR
                ) {
                    $grid->setProperty('title', ($isRevenue) ? 'IA.REVENUE_SCHEDULES' : 'IA.EXPENSE_SCHEDULES');
                    
                    if (!$isRevenue) {
                        // For post/clear expenses, change the column title to Expense item
                        $grid->findAndSetProperty(
                            ['path' => 'ITEMIDHTML'],
                            ['fullname' => 'IA.EXPENSE_ITEM'],
                            EditorComponentFactory::TYPE_FIELD
                        );
                    }
                } else {
                    $grid->setProperty('title', 'IA.CONTRACT_LINES');
                    $grid->findAndSetProperty(
                        ['path' => 'TEMPLATEHTML'],
                        ['hidden' => true],
                        EditorComponentFactory::TYPE_GRID_COLUMN
                    );
                }
                
                if (!$isRevenue) {
                    $grid->findAndSetProperty(
                        ['path' => 'DELIVERYSTATUS_DISPLAY'],
                        ['hidden' => true],
                        EditorComponentFactory::TYPE_GRID_COLUMN
                    );

                    $grid->findAndSetProperty(
                        ['path' => 'DELIVERYDATE'],
                        ['hidden' => true],
                        EditorComponentFactory::TYPE_GRID_COLUMN
                    );
                    
                    $grid->findAndSetProperty(
                        ['path' => 'PERCENTRECOGNIZED'],
                        ['hidden' => true],
                        EditorComponentFactory::TYPE_GRID_COLUMN
                    );
                }
                
                $isPost = ($scheduleAction == ContractManageScheduleManager::ACTION_POST);
                $isClear = ($scheduleAction == ContractManageScheduleManager::ACTION_CLEAR);
                
                if ($isRevenue || (!$isPost && !$isClear)) {
                    $grid->findAndSetProperty(
                        ['path' => 'EXPENSELINENOHTML'],
                        ['hidden' => true],
                        EditorComponentFactory::TYPE_GRID_COLUMN
                    );
                }
                
                $showCols = [
                    'POSTINGDATE' => $isPost,
                    'ACTUALPOSTINGDATE' => $isClear,
                    'START_DATE' => ($isPost || $isClear),
                    'END_DATE' => ($isPost || $isClear),
                    'LINESTARTDATE' => (!$isPost && !$isClear),
                    'LINEENDDATE' => (!$isPost && !$isClear),
                    'AMOUNTHTML' => ($isPost || $isClear),
                    'SCHEDULESTATE_DISPLAY' => ($isPost || $isClear),
                    'COMPUTATIONMEMO' => ($isPost || $isClear),
                    'PERCENTRECOGNIZED' => ($isPost || $isClear),
                ];
                
                foreach ($showCols as $path => $showCol) {
                    $grid->findAndSetProperty(['path' => $path], ['hidden' => !$showCol]);
                }
            }
            
            foreach ((($toggleFields[$scheduleAction]) ?? []) as $path) {
                $fld = [];
                $view->findComponents(['path' => $path], EditorComponentFactory::TYPE_FIELD, $fld);
                if ($fld[0]) {
                    $fld[0]->setProperty('hidden', false);
                }
            }
            
            $section = [];
            if ($scheduleAction == ContractManageScheduleManager::ACTION_POST) {
                $view->findComponents(['id' => 'PostSection'], EditorComponentFactory::TYPE_SUBSECTION, $section);
                if ($section[0]) {
                    $section[0]->setProperty('hidden', false);
                }
            } elseif ($scheduleAction == ContractManageScheduleManager::ACTION_HOLD) {
                $view->findComponents(['id' => 'HoldSection'], EditorComponentFactory::TYPE_SUBSECTION, $section);
                if ($section[0]) {
                    $section[0]->setProperty('hidden', false);
                }
            } elseif ($scheduleAction == ContractManageScheduleManager::ACTION_RESUME) {
                $view->findComponents(['id' => 'ResumeSection'], EditorComponentFactory::TYPE_SUBSECTION, $section);
                if ($section[0]) {
                    $section[0]->setProperty('hidden', false);
                }
            } elseif ($scheduleAction == ContractManageScheduleManager::ACTION_DELIVER) {
                $view->findComponents(['id' => 'DeliverySection'], EditorComponentFactory::TYPE_SUBSECTION, $section);
                if ($section[0]) {
                    $section[0]->setProperty('hidden', false);
                }
                
                if ($grid) {
                    $select_fld = [];
                    $grid->findComponents(['path' => 'DLVR_DATE'], EditorComponentFactory::TYPE_FIELD, $select_fld);
                    if ($select_fld[0]) {
                        $select_fld[0]->setProperty('readonly', false);
                    }
                    $select_fld = [];
                    $grid->findComponents(['path' => 'COMPONENTID'], EditorComponentFactory::TYPE_FIELD, $select_fld);
                    if ($select_fld[0]) {
                        $select_fld[0]->setProperty('hidden', false);
                    }
                }
                
                $obj['DELIVERYDATE'] = (empty($obj['DELIVERYDATE'])) ? util_today() : $obj['DELIVERYDATE'];
            } else {
                if ($grid) {
                    $select_fld = [];
                    $grid->findComponents(['path' => 'SELECT'], EditorComponentFactory::TYPE_FIELD, $select_fld);
                    if ($select_fld[0]) {
                        $select_fld[0]->setProperty('hidden', true);
                    }
                }
            }
        }
        
        return true;
    }
    
    protected function removeMaxRecCountAll()
    {
        $view = $this->getView();
        $field = [];
        $view->findComponents(['path' => 'MAXRECCNT'], EditorComponentFactory::TYPE_FIELD, $field);
        $typeFilter = $field[0]->getProperty('type');
        $key = array_search(self::MAX_REC_COUNT_ALL, $typeFilter['validvalues']);
        if ($key !== false) {
            unset($typeFilter['validvalues'][$key]);
        }
        
        $field[0]->setProperty(['type', 'validvalues'], array_values($typeFilter['validvalues']));
    }
    
    /**
     * Get the editor button list
     *
     * @param string $state the editor state
     *
     * @return array the buttons list
     */
    public function getStandardButtons($state)
    {
        $buttons = [];
        $data = $this->getViewData();
        $revenueAction = $data['SCHEDULESELIGIBLETO'] ?? ContractManageScheduleManager::ACTION_POST;
        $validateFields = '[]';

        switch ($revenueAction) {
            case ContractManageScheduleManager::ACTION_CLEAR:
                $actionLabelToken = 'IA.CLEAR';
                $offlineActionLabelToken = 'IA.CLEAR_OFFLINE';
                break;
            case ContractManageScheduleManager::ACTION_HOLD:
                $actionLabelToken = 'IA.HOLD';
                $offlineActionLabelToken = 'IA.HOLD_OFFLINE';
                $validateFields = "['HOLDDATE']";
                break;
            case ContractManageScheduleManager::ACTION_RESUME:
                $actionLabelToken = 'IA.RESUME';
                $offlineActionLabelToken = 'IA.RESUME_OFFLINE';
                $validateFields = "['RESUMEDATE']";
                break;
            case ContractManageScheduleManager::ACTION_DELIVER:
                $actionLabelToken = 'IA.DELIVER';
                $offlineActionLabelToken = 'IA.DELIVER_OFFLINE';
                $validateFields = "['DELIVERYDATE']";
                break;
            case ContractManageScheduleManager::ACTION_POST:
            default:
                $actionLabelToken = 'IA.POST';
                $offlineActionLabelToken = 'IA.POST_OFFLINE';
                $validateFields = "['WHENCREATED']";
                break;
        }
        
        // Add action button for Update Percent Complete
        $action = ContractManageScheduleManager::ACTION_PERCENT_COMPLETE;
        $this->setButtonDetails(
            $buttons,
            'pctcompbuttonid',
            'pctcompbutton',
            'IA.UPDATE_PERCENTAGE_COMPLETE_SCHEDULES',
            $action,
            true,
            "doAction('$action', ['WHENCREATED'], false)",
            false
        );
        
        // Add action button for Preview entries
        $action = ContractManageScheduleManager::ACTION_LIST;
        $this->setButtonDetails(
            $buttons,
            'previewbuttonid',
            'previewbutton',
            'IA.PREVIEW',
            $action,
            true,
            "doAction('$action', ['ASOFDATE'], false)",
            false
        );
        
        // Add action button for the schedule action chosen for preview
        $action = $revenueAction;
        $jsCode = "doAction('$action', $validateFields, false)";
        $this->setButtonDetails(
            $buttons,
            'actionbuttonid',
            'actionbutton',
            $actionLabelToken,
            $action,
            true,
            $jsCode,
            false
        );
        
        if (!ContractUtil::isContractRunObjectFeatureEnabled() && $offlineActionLabelToken) {
            // Add action button for the schedule action chosen for preview (force offline)
            $jsCode = "doAction('$action', $validateFields, true)";
            $this->setButtonDetails(
                $buttons,
                'offlineactionbuttonid',
                'offlineactionbutton',
                $offlineActionLabelToken,
                strtolower($revenueAction) . 'offline',
                true,
                $jsCode,
                false,
                true
            );
        }
        
        return $buttons;
    }
    
    protected function addAction(string $name, string $handler): void
    {
        $this->kActionHandlers[$name] = [
            'handler' => $handler,
            'states' => [
                $this->kShowEditState,
                $this->kShowViewState,
                $this->kShowNewState,
            ],
            'csrf' => true,
        ];
    }

    /**
     * @param array $_params
     * @return bool|null
     */
    protected function processGeneratePOCAction(&$_params) {
        $this->retrieveObjectFromView($_params, $obj);

        $asOfDate = $obj['ASOFDATE'];
        $type = $obj['TYPEFILTER'];
        if (!$asOfDate) {
            Globals::$g->gErr->addError('CN-0235', __FILE__ . ':' . __LINE__, "As of Date is required.");
            $this->state = $this->kErrorState;
            return false;
        }

        if (!$type) {
            Globals::$g->gErr->addError('CN-0236', __FILE__ . ':' . __LINE__, "Must select one or both of Revenue and Expense.");
            $this->state = $this->kErrorState;
            return false;
        }

        try {
            $contractManageScheduleFilter = new ContractManageScheduleFilter($obj);
        } catch (IAException $exception) {
            ContractUtil::addThrowableError($exception, __FILE__ . ':' . __LINE__);
            $this->state = $this->kErrorState;
            return false;
        }

        if ( $this->isProjectSubscribed === true && ContractProjectLinkHandler::isContractProjectLinkFeatureEnabled() === true ) {
            $projectIds = [];
            $contractIds = [];
            if (isSpecified($obj['PROJECTGROUPID'])) {
                $projectIds = $contractManageScheduleFilter->getProjectIdsFromGroup();
            } else if (isSpecified($contractManageScheduleFilter->getProjectId())) {
                $projectIds[] = $contractManageScheduleFilter->getProjectId();
            }

            if (isSpecified($obj['CONTRACTGROUPID'])) {
                $contractIds = $contractManageScheduleFilter->getContractIdsFromGroup();
            } else if (isSpecified($contractManageScheduleFilter->getContractId())) {
                $contractIds[] = $contractManageScheduleFilter->getContractId();
            }

            $contractObjHandler = new ContractObjHandler();
            $contractLines = $contractObjHandler->getProjectIdsUsedInContracts($projectIds, $contractIds, null);
            if (ContractUtil::isContractRunObjectFeatureEnabled() && ContractUtil::isContractRunPOCFeatureEnabled()) {
                //add a contract run object for POC Run
                $contactInfo = Profile::getUserCacheProperty('contact');
                $emailId = $contactInfo['EMAIL1'];
                ContractHandler::createRunObjectForPOCRevRec($contractLines, $emailId, $asOfDate, $runObjectKey);
                if ($runObjectKey != null) {
                    $link = "<a href=" . RunObjectHandler::getDrillDownURLForRun($runObjectKey, 'cn') .">".GT($this->textMap, "IA.VIEW_RUN")."</a>";
                    $a = ContractUtil::GTP($this->textMap,'IA.BULK_ACTION_FOR_PERCENT_COMPLETE_SCHEDULES_INITIATED_NOTIFICATION_BE_SENT_ONCE_COMPLETES',['EMAIL_ID' => $emailId,'LINK' => $link]);
                    parent::SetMessage($a);
                } else {
                    $a = GT($this->textMap,'IA.NO_CONTRACT_SCHEDULES_MATCHED_FOR_RECOGNITION');
                    parent::SetMessage($a);
                }
            } else {
                if (count($contractLines) > 50) {
                    $this->createOfflinePOCRevRecScheduleJob($contractManageScheduleFilter->getAsOfDate(), $contractLines);
                    //set a message for UI
                    $contactInfo = Profile::getUserCacheProperty('contact');
                    $emailId = $contactInfo['EMAIL1'];
                    $a = ContractUtil::GTP($this->textMap,'IA.PERCENT_COMPLETE_REVENUE_SCHEDULES_UPDATED_OFFLINE_NOTIFICATION_SENT_ONCE_COMPLETES',['EMAIL_ID' => $emailId]);
                    parent::SetMessage($a);
                } else {
                    $contractProjectLink = new ContractProjectRevRecHandler();
                    $processSummary = array();
                    $ok = $contractProjectLink->doRevenueRecognitionForProject($contractManageScheduleFilter->getAsOfDate(),
                        $projectIds, $contractIds, $processSummary, null, $contractLines);

                    if ($ok) {
                        $obj['PROCESS_SUMMARY'] = $processSummary;
                    } else {
                        $this->state = $this->kErrorState;
                        $processErrors = $processSummary['ERROR'];
                        foreach ($processErrors as $oneError) {
                            // TODO: i18n - need to figure out how to handle
                            Globals::$g->gErr->addError($oneError);
                        }
                        //we need to return false here to make sure editor transaction is rolled back
                        return $ok;
                    }
                }
            }
        }
        Request::$r->SetCurrentObject($obj);

        if ( Globals::$g->gErr->hasErrors() ) {
            $this->state = $this->kErrorState;
        }

        return true;
    }
        /**
     * Process the preview button action
     *
     * @param array $_params the metedata
     *
     * @return bool     false if error else true
     */
    protected function processViewListAction(&$_params)
    {
        $this->retrieveObjectFromView($_params, $obj);

        $scheduleAction = $obj['SCHEDULESELIGIBLETO'];
        $asOfDate = $obj['ASOFDATE'];
        $type = $obj['TYPEFILTER'];

        if (!$asOfDate) {
            Globals::$g->gErr->addError('CN-0235', __FILE__ . ':' . __LINE__, "As of Date is required.");
            $this->state = $this->kErrorState;
            return false;
        }

        if (!$type) {
            Globals::$g->gErr->addError('CN-0236', __FILE__ . ':' . __LINE__, "Must select one or both of Revenue and Expense.");
            $this->state = $this->kErrorState;
            return false;
        }
        
        if (
            $scheduleAction != ContractManageScheduleManager::ACTION_POST
            && $scheduleAction != ContractManageScheduleManager::ACTION_CLEAR
        ) {
            // Only allow type Expense if action is Post or Clear
            $type = 'R';
        }
        $isRevenue = ($type == 'R');

        try {
            $contractManageScheduleFilter = new ContractManageScheduleFilter($obj, $type);
        } catch (IAException $exception) {
            ContractUtil::addThrowableError($exception, __FILE__ . ':' . __LINE__);
            $this->state = $this->kErrorState;
            return false;
        }

        unset($obj['PROCESS_SUMMARY']);
        
        $obj['SCHEDULEENTRIES'] = [];
        $hasRevenue = false;
        if ($isRevenue) {
            $obj['SCHEDULEENTRIES'] = ContractScheduleManager::getAllOpenSchedules($contractManageScheduleFilter);
            $hasRevenue = true;
        }

        $hasExpenses = false;
        if ($this->isExpenseEnabled && $type == 'E') {
            /* @var ContractExpenseScheduleManager $eschMgr  */
            $eschMgr = Globals::$g->gManagerFactory->getManager('contractexpenseschedule');
            $obj['SCHEDULEENTRIES'] = $eschMgr->getAllOpen($contractManageScheduleFilter);

            $hasExpenses = true;
        }
        $obj['DATAFORTYPE'] = $type;
        $obj['DATAFORJOURNAL'] = $contractManageScheduleFilter->getJournalType();
        $obj['HASENTRIES'] = ($hasRevenue || $hasExpenses);
        $obj['CANPOST'] = ($hasRevenue || $hasExpenses) ? true : false;

        Request::$r->SetCurrentObject($obj);
        if ( Globals::$g->gErr->hasErrors() ) {
            $this->state = $this->kErrorState;
        }


        return true;
    }
    
    /**
     * Process the post button action
     *
     * @param array $_params the metedata
     *
     * @return bool     false if error else true
     */
    protected function processPostAction(&$_params)
    {
        $this->retrieveObjectFromView($_params, $obj);
        
        if (!$this->validatePostParameters($obj)) {
            return false;
        }
        
        $scheduleEntriesMap = [];
        $totalNumSelected = 0;
        $useScheduleDate = false;
        
        $ok = true;
        $this->getPostingParameters($obj, $scheduleEntriesMap, $useScheduleDate, $hasEntries);
        $type = $obj['TYPEFILTER'] ?? ManageScheduleFilter::TYPE_REVENUE;
        $isRevenue = ($type == ManageScheduleFilter::TYPE_REVENUE);
        
        $contactInfo = Profile::getUserCacheProperty('contact');
        $emailId = $contactInfo['EMAIL1'];
        $contractSchedulePostResponse = new ContractSchedulePostResponse();
        
        $errors = null;
        $msg = [];
        if (ContractUtil::isContractRunObjectFeatureEnabled() && ContractUtil::isContractRunPostRevRecFeatureEnabled()) {
            $executionMode = $hasEntries ? 'Offline' : 'Online';
            if ($hasEntries && $isRevenue) {
                //add here to the dispatcher job list here
                $ok = ContractHandler::createRunObjectForPostRevRec(
                    $scheduleEntriesMap,
                    $emailId,
                    $executionMode,
                    $useScheduleDate,
                    $obj['WHENCREATED'],
                    $runObjectKey
                );
                if ($ok) {
                    $link =
                        "<a href=" . RunObjectHandler::getDrillDownURLForRun($runObjectKey, 'cn') . ">" .
                        GT($this->textMap, "IA.VIEW_RUN") . "</a>";
                    $msg[] = ContractUtil::GTP(
                        $this->textMap,
                        'IA.BULK_ACTION_FOR_REVENUE_RECOGNITION_INITIATED_NOTIFICATION_BE_SENT_ONCE_COMPLETES',
                        ['EMAIL_ID' => $emailId, 'LINK' => $link]
                    );
                } else {
                    $msg[] = GT($this->textMap, 'IA.DIDNOT_FIND_ANY_ENTRIES_FOR_RUN');
                }
            }
            
            if ($hasEntries && $type == ManageScheduleFilter::TYPE_EXPENSE) {
                //add here to the dispatcher job list here
                $ok = ContractHandler::createRunObjectForPostExpense(
                    $scheduleEntriesMap,
                    $emailId,
                    $executionMode,
                    $useScheduleDate,
                    $obj['WHENCREATED'],
                    $runObjectKey
                );
                
                $link = "<a href=" . RunObjectHandler::getDrillDownURLForRun($runObjectKey, 'cn') . ">" .
                        GT($this->textMap, "IA.VIEW_RUN") . "</a>";
                
                $msg[] = ContractUtil::GTP(
                    $this->textMap,
                    'IA.BULK_ACTION_FOR_EXPENSE_RECOGNITION_INITIATED_NOTIFICATION_BE_SENT_ONCE_COMPLETES',
                    ['EMAIL_ID' => $emailId, 'LINK' => $link]
                );
            }
            if ($hasEntries) {
                $obj['SCHEDULEENTRIES'] = [];
                $obj['HASENTRIES'] = false;
            } else {
                $obj['CANPOST'] = false;
            }
        } else {
            // Run Objects are disabled
            if (!$this->validateSelectedEntries($scheduleEntriesMap, $totalNumSelected)) {
                return false;
            }
            
            $tooManyItems = ($totalNumSelected > $this::MAX_ONLINE_POSTINGS);
            $forceOffline = $tooManyItems || isArrayValueTrue($obj, 'OFFLINE'); // User pressed offline action
            
            if ($forceOffline) {
                if ($tooManyItems) {
                    $msg = ContractUtil::GTP(
                        $this->textMap,
                        'IA.ENTRIES_POSTED_OFFLINE_AS_MAXIMUM_LIMIT_EXCEEDS_NOTIFICATION_SENT_ONCE_COMPLETES',
                        ['MAX_ONLINE_POSTINGS' => $this::MAX_ONLINE_POSTINGS, 'EMAIL_ID' => $emailId]
                    );
                    parent::SetMessage($msg);
                }
                
                $ok = $this->submitManageScheduleRunOfflineAction($obj, $scheduleEntriesMap, $useScheduleDate, $_params, true);
                
                return $ok;
            }
            $ok = ContractSchedule::postScheduleEntries(
                $scheduleEntriesMap,
                $obj['WHENCREATED'],
                $obj['JOURNALFILTER'],
                $contractSchedulePostResponse,
                $useScheduleDate
            );
            $revErrorCount = count($contractSchedulePostResponse->getContractRevSchedulePostErrorResponse());
            $expErrorCount = count($contractSchedulePostResponse->getContractExpSchedulePostErrorResponse());
            
            $msg = [];
            $msg[] = ContractUtil::GTP(
                $this->textMap,
                'IA.NUMBER_OF_REVENUE_ENTRIES_POSTED',
                ['POSTED_COUNT' => $contractSchedulePostResponse->getTotalNumberOfRevenueSchedulesPosted()]
            );
            $msg[] = ContractUtil::GTP(
                $this->textMap,
                'IA.NUMBER_OF_EXPENSE_ENTRIES_POSTED',
                ['POSTED_COUNT' => $contractSchedulePostResponse->getTotalNumberOfExpenseSchedulesPosted()]
            );
            
            if ($revErrorCount > 0) {
                $msg[] = ContractUtil::GTP($this->textMap, 'IA.NUMBER_OF_REVENUE_ERRORS', ['ERROR_COUNT' => $revErrorCount]);
            }
            
            if ($expErrorCount > 0) {
                $msg[] = ContractUtil::GTP($this->textMap, 'IA.NUMBER_OF_EXPENSE_ERRORS', ['ERROR_COUNT' => $expErrorCount]);
            }
            
            if ($contractSchedulePostResponse->getTotalNumberOfRevenueSchedulesSkipped() > 0) {
                $msg[] = ContractUtil::GTP(
                    $this->textMap,
                    'IA.NUMBER_OF_REVENUE_ENTRIES_IGNORED',
                    ['SKIPPED_COUNT' => $contractSchedulePostResponse->getTotalNumberOfRevenueSchedulesSkipped()]
                );
            }
            
            if ($contractSchedulePostResponse->getTotalNumberOfExpenseSchedulesSkipped() > 0) {
                $msg[] = ContractUtil::GTP(
                    $this->textMap,
                    'IA.NUMBER_OF_EXPENSE_ENTRIES_IGNORED',
                    ['SKIPPED_COUNT' => $contractSchedulePostResponse->getTotalNumberOfExpenseSchedulesSkipped()]
                );
            }
            
            if ($revErrorCount > 0 || $expErrorCount > 0) {
                if (ContractUtil::isContractRunObjectFeatureEnabled()) {
                    $contractManageScheduleManager = Globals::$g->gManagerFactory->getManager('contractmanageschedule');
                    $ok = $contractManageScheduleManager->sendCompletionEmail($contractSchedulePostResponse);
                    
                    $msg[] = ContractUtil::GTP($this->textMap, 'IA.NOTIFICATION_WITH_ERROR_SENT', ['EMAIL_ID' => $emailId]);
                } else {
                    $msg = [];
                    $ok = false;
                    foreach ($contractSchedulePostResponse->getAllErrorResponse() as $errorResponse) {
                        Globals::$g->gErr->addError(
                            '',
                            __FILE__ . ":" . __LINE__,
                            $errorResponse->getExceptionMessage()
                        );
                    }
                }
            }
        }
        if ($errors != null) {
            parent::SetWarningMessage($errors);
        }
        parent::SetMessage($msg);
        if (!ContractUtil::isContractRunObjectFeatureEnabled()) {
            // Fetch new data
            $this->processViewListAction($_params);
        }
        
        return $ok;
    }
    
    /**
     * @param array $obj
     * @param array $scheduleEntriesMap
     * @param bool $useScheduleDate
     * @param array $_params
     * @param bool $convertToOffline
     *
     * @return bool
     */
    protected function submitManageScheduleRunOfflineAction($obj, $scheduleEntriesMap, $useScheduleDate, $_params, $convertToOffline = false)
    {
        // Remove the UI only elements from the scheduleMap before posting the offline job
        foreach ( $scheduleEntriesMap as &$scheduleEntriesByType) {
            foreach ($scheduleEntriesByType as &$scheduleEntriesArray) {
                //Need the UnusedInspection as the performace is better. https://stackoverflow.com/questions/12346876/php-foreach-that-returns-keys-only
                /** @noinspection PhpUnusedLocalVariableInspection */
                foreach ( $scheduleEntriesArray as $key => &$entry) {
                    if (strcontains($key, "HTML")) {
                        unset($scheduleEntriesArray[$key]);
                    }
                }
            }
        }

        $ok = $this->createOfflineManageScheduleJob($obj, $scheduleEntriesMap, $useScheduleDate);

        if (!$convertToOffline && $ok) {

            $contactInfo = Profile::getUserCacheProperty('contact');
            $emailId = $contactInfo['EMAIL1'];
            $a = ContractUtil::GTP($this->textMap,'IA.REVENUE_EXPENSE_ENTRIES_POSTED_OFFLINE_NOTIFICATION_SENT_ONCE_COMPLETES',['EMAIL_ID' => $emailId]);
            parent::SetMessage($a);
        }

        if (!$ok) {
            $a = 'Error creating offline job. Try again later.';
            Globals::$g->gErr->addError('CN-0237', "", $a);
            $this->state = $this->kErrorState;
        }

        // Fetch new data
        $this->processViewListAction($_params);

        return $ok;
    }

    /**
     * @param string $asOfDate
     * @param array  $contractLines
     * @return bool
     */
    public function createOfflinePOCRevRecScheduleJob($asOfDate, $contractLines)
    {
        $payload = [
            'ASOFDATE' => $asOfDate,
            'CONTRACTLINES' => $contractLines,
        ];

        $publisher = new ims_publish_1(IMS_MODE_NONBLOCKING, IMS_PROCESS_REMOTE, IMS_MODE_QUEUED);
        $ok = $publisher->PublishMsg('MANAGESCHEDULERUN', 'CONTRACTMANAGESCHEDULE', 'POCREVRECSCHEDULE_RUN', '', $payload, [], $response);

        return $ok;
    }
    /**
     * Publishes the manage schedule offline job
     *
     * @param array $obj Input values
     * @param array $scheduleEntriesMap
     * @param bool $useScheduleDate
     * @return bool
     */

    public function createOfflineManageScheduleJob($obj, $scheduleEntriesMap, $useScheduleDate)
    {
        $payload = [
            'JOURNALFILTER'   => $obj['JOURNALFILTER'],
            'WHENCREATED'     => $obj['WHENCREATED'],
            'SCHEDULEMAP'     => $scheduleEntriesMap,
            'USESCHEDULEDATE' => $useScheduleDate
        ];

        $publisher = new ims_publish_1(IMS_MODE_NONBLOCKING, IMS_PROCESS_REMOTE, IMS_MODE_QUEUED);
        $ok = $publisher->PublishMsg('MANAGESCHEDULERUN', 'CONTRACTMANAGESCHEDULE', 'MANAGESCHEDULE_RUN', '', $payload, [], $response);

        return $ok;
    }

    /**
     * @param array[] $scheduleEntriesMap
     * @param int     &$totalNumSelected
     *
     * @return bool
     */
    private function validateSelectedEntries($scheduleEntriesMap, &$totalNumSelected=0)
    {
        $ok = true;

        $totalNumSelected = 0;
        foreach ($scheduleEntriesMap as $schType => $schEntriesSelected) {
            $numSelected = count($schEntriesSelected);
            $totalNumSelected += $numSelected;
            if ($numSelected > 0) {
                $mgr = null;
                if ($schType === ContractSchedule::TYPE_REVENUE || $schType === ContractSchedule::TYPE_REVENUE2) {
                    $mgr = Globals::$g->gManagerFactory->getManager('contractrevenuescheduleentry');
                } elseif ($schType === ContractSchedule::TYPE_EXPENSE || $schType === ContractSchedule::TYPE_EXPENSE2) {
                    $mgr = Globals::$g->gManagerFactory->getManager('contractexpensescheduleentry');
                } else {
                    ContractUtil::assert(false, 'Invalid contract schedule type');
                }
                $schEntryKeys = array_column($schEntriesSelected, 'SCHEDULEENTRYKEY');
                $results = $mgr->GetList(
                    [
                        'selects' => ['RECORDNO', 'SCHEDULEKEY'],
                        'filters' => [[
                            ['RECORDNO', 'in', $schEntryKeys],
                            ['TYPE', '=', $schType],
                                      ]],
                    ]
                );
                $numResults = count($results);
                ContractUtil::assert ($numResults == $numSelected, "Mismatch between selected schedule entries and the Journal/Type ($numSelected vs $numResults)");
                $resultsByRecordNo = resultSetToMapByColumn($results, 'RECORDNO', false);
                foreach ($schEntriesSelected as $selectedSchEntry) {
                    $schEntryKey = $selectedSchEntry['SCHEDULEENTRYKEY'];
                    $resultForRecordNo = $resultsByRecordNo[$schEntryKey];
                    if ($selectedSchEntry['SCHEDULEKEY'] != $resultForRecordNo['SCHEDULEKEY']) {
                        $msg = "Schedule key mismatch between DB and incoming data of selected schedule entry $schEntryKey (DB: {$resultForRecordNo['SCHEDULEKEY']} vs incoming: {$selectedSchEntry['SCHEDULEKEY']})";
                        throw IAException::newIAException('CN-0898', $msg,
                                                          ['SCH_ENTRY_KEY' => $schEntryKey,
                                                           'RESULT_FOR_RECORD_NO_SCHEDULEKEY' => $resultForRecordNo['SCHEDULEKEY'],
                                                           'SELECTED_SCH_ENTRY_SCHEDULEKEY' => $selectedSchEntry['SCHEDULEKEY']]);
                    }

                }
            }
        }
        if ($totalNumSelected == 0) {
            $msg = GT($this->textMap,'IA.PLEASE_SELECT_SCHEDULES');
            parent::SetMessage($msg);
            $ok = false;
        }

        return $ok;
    }

    /**
     * @param array $obj
     *
     * @return bool
     */
    protected function validatePostParameters($obj)
    {
        if (!$obj['WHENCREATED'] && $obj['POSTINGOPTIONS'] === 'P2' ) {
            Globals::$g->gErr->addError('CN-0238', __FILE__ . ':' . __LINE__, 'Posting date is required.');
            return false;
        }
        return true;
    }
    
    /**
     * @param array $obj
     *
     * @return bool
     * @noinspection PhpUnusedParameterInspection
     */
    protected function validateClearParameters($obj)
    {
        // As of right now, there is no validation required for Clear.
        // But if we need to add in the future this is where to put it.
        return true;
    }
    
    /**
     * @param array $obj
     *
     * @return bool
     */
    protected function validateHoldParameters($obj)
    {
        if (empty($obj['HOLDDATE'])) {
            Globals::$g->gErr->addError('CN-0034', __FILE__ . ':' . __LINE__, 'Hold date is required.');
            return false;
        }
        
        return true;
    }
    
    /**
     * @param array $obj
     *
     * @return bool
     */
    protected function validateDeliverLinesParameters($obj)
    {
        if (empty($obj['DELIVERYDATE'])) {
            Globals::$g->gErr->addError('CN-0034', __FILE__ . ':' . __LINE__, 'Delivery date is required.');
            return false;
        }
        
        return true;
    }
    
    /**
     * @param array $obj
     *
     * @return bool
     */
    protected function validateResumeParameters($obj)
    {
        if (empty($obj['RESUMEDATE'])) {
            Globals::$g->gErr->addError('CN-0696', __FILE__ . ':' . __LINE__, 'Resume date is required.');
            return false;
        }
        
        return true;
    }

    /**
     * @param array $obj
     * @param array $scheduleEntriesMap
     * @param bool  $useScheduleDate
     * @param bool  $hasEntries
     */
    protected function getPostingParameters($obj, &$scheduleEntriesMap, &$useScheduleDate, &$hasEntries)
    {
        if ($obj['JOURNALFILTER'] == 'J1') {
            $revSchType = ContractSchedule::TYPE_REVENUE;
            $expSchType = ContractSchedule::TYPE_EXPENSE;
        } else {
            $revSchType = ContractSchedule::TYPE_REVENUE2;
            $expSchType = ContractSchedule::TYPE_EXPENSE2;
        }
        
        $scheduleType = ($obj['TYPEFILTER'] == 'R') ? $revSchType : $expSchType;

        $this->populateSelectedScheduleEntries($obj['SCHEDULEENTRIES'], $scheduleEntries);

        if (!empty($scheduleEntries)) {
            $scheduleEntriesMap[$scheduleType] = $scheduleEntries;
            $count = count($scheduleEntries);
            $hasEntries = $count > 0;
        }

        $postingOption = $obj['POSTINGOPTIONS'];

        if ( $postingOption === 'P1' ) {
            $useScheduleDate = true;
        }
    }
    
    /**
     * @param array     $obj
     * @param array     $scheduleEntries
     * @param bool|null $hasEntries
     *
     * @return void
     */
    protected function getClearParameters(array $obj, array &$scheduleEntries, ?bool &$hasEntries): void
    {
        $this->populateSelectedScheduleEntries($obj['SCHEDULEENTRIES'], $scheduleEntries);
        $hasEntries = count($scheduleEntries ?? []) > 0;
    }
    
    /**
     * @param array       $obj
     * @param array|null  $contractLineGroups
     * @param string|null $schedulesToHold
     * @param string|null $holdDate
     * @param string|null $holdMemo
     *
     * @return void
     */
    protected function getHoldParameters(
        array $obj,
        ?array &$contractLineGroups,
        ?string &$schedulesToHold,
        ?string &$holdDate,
        ?string &$holdMemo
    ): void
    {
        $selected = array_filter($obj['SCHEDULEENTRIES'], function($entry) {
            return ($entry['SELECTED'] ?? null) === 'true';
        });
        
        $holdDate = $obj['HOLDDATE'];
        $schedulesToHold = $obj['SCHEDULESTOHOLD'];
        $holdMemo = $obj['HOLDMEMO'];
        
        $contractLineGroups = [];
        foreach ($selected as $line) {
            $contractKey = $line['CONTRACTKEY'];
            if (empty($contractLineGroups[$contractKey])) {
                $contractLineGroups[$contractKey] = [];
            }
            
            $line['RECORDNO'] = $line['LINERECORDNO'];
            $contractLineGroups[$contractKey][] = $line;
        }
    }
    
    /**
     * @param array       $obj
     * @param array|null  $contractLineGroups
     * @param string|null $schedulesToHold
     * @param string|null $resumeDate
     * @param string|null $resumeMemo
     *
     * @return void
     */
    protected function getResumeParameters(
        array $obj,
        ?array &$contractLineGroups,
        ?string &$schedulesToHold,
        ?string &$resumeDate,
        ?string &$resumeMemo
    ): void
    {
        $selected = array_filter($obj['SCHEDULEENTRIES'], function($entry) {
            return ($entry['SELECTED'] ?? null) === 'true';
        });
        
        $resumeDate = $obj['RESUMEDATE'];
        $schedulesToHold = $obj['SCHEDULESTORESUME'];
        $resumeMemo = $obj['RESUMEMEMO'];
        
        $contractLineGroups = [];
        foreach ($selected as $line) {
            $contractKey = $line['CONTRACTKEY'];
            if (empty($contractLineGroups[$contractKey])) {
                $contractLineGroups[$contractKey] = [];
            }
            
            $line['RECORDNO'] = $line['LINERECORDNO'];
            $contractLineGroups[$contractKey][] = $line;
        }
    }
    
    /**
     * @param array       $obj
     * @param array|null  $contractLineGroups
     * @param string|null $deliveryDate
     *
     * @return void
     */
    protected function getDeliverParameters(
        array $obj,
        ?array &$contractLineGroups,
        ?string &$deliveryDate,
    ): void
    {
        $selected = array_filter($obj['SCHEDULEENTRIES'], function($entry) {
            return ($entry['SELECTED'] ?? null) === 'true';
        });
        
        $deliveryDate = $obj['DELIVERYDATE'];
        
        $contractLineGroups = [];
        foreach ($selected as $line) {
            $contractKey = $line['CONTRACTKEY'];
            if (empty($contractLineGroups[$contractKey])) {
                $contractLineGroups[$contractKey] = [];
            }
            
            $line['RECORDNO'] = $line['LINERECORDNO'];
            $contractLineGroups[$contractKey][] = $line;
        }
    }
    
    /**
     * No edit
     *
     * @return bool
     */
    protected function CanEdit()
    {
        return false;
    }

    /**
     * No duplicate
     *
     * @return bool
     */
    protected function CanDuplicate()
    {
        return false;
    }
    
    /**
     * Initialize TYPEFILTER field
     */
    function initializeTypeFilterField()
    {
        if ( !$this->isExpenseEnabled ) {
            $view = $this->getView();
            $field = array();
            $view->findComponents(array('path' => 'TYPEFILTER'), EditorComponentFactory::TYPE_FIELD, $field);
            $typeFilter = $field[0]->getProperty('type');
            $key = array_search('E', $typeFilter['validvalues']);
            if ( $key !== false ) {
                unset($typeFilter['validvalues'][$key]);
            }
            $key = array_search('E', $typeFilter['_validivalues']);
            if ( $key !== false ) {
                unset($typeFilter['_validivalues'][$key]);
            }

            $field[0]->setProperty(array('type', 'validvalues'), array_values($typeFilter['validvalues']));
            $field[0]->setProperty(array('type', '_validivalues'), array_values($typeFilter['_validivalues']));
        }
    }

    /**
     * @param array $scheduleEntries
     * @param array &$selectedScheduleEntries
     */
    private function populateSelectedScheduleEntries($scheduleEntries, &$selectedScheduleEntries)
    {
        $selectedScheduleEntries = [];
        foreach ($scheduleEntries as $entry) {
            if ($entry['SELECTED'] == 'true' &&
                !in_array($entry['SCHEDULEENTRYKEY'], $selectedScheduleEntries)  // TODO: Not sure why form is submitting same key twice
            ) {
                $selectedScheduleEntries[] = $entry;
            }
        }
    }

    /**
     * @return mixed
     */
    protected function getEditorGlobals()
    {
        $vars['MAX_OFFLINE_POSTINGS'] = $this::MAX_OFFLINE_POSTINGS;
        $vars['valid_states'] = ContractManageScheduleManager::getValidActionEntryStates();
        $errors = $this->getEntityMgr()->errors ?? [];
        $vars['errors'] = [
            'COUNT' => count($errors),
            'ERRORS' => $errors
        ];

        return $vars;
    }

    /**
     * sets the journal labels.
     */
    private function setJournalLabels()
    {
        $field = [];
        $this->getView()->findComponents(array('path' => 'JOURNALFILTER'), EditorComponentFactory::TYPE_FIELD, $field);
        $typeFilter = $field[0]->getProperty('type');
        $validValues = $typeFilter['validvalues'];
        ContractUtil::assert($validValues[0] == 'J1' && $validValues[1] == ContractSchedule::JOURNAL2);

        $newValidValues = [];
        $newValidLabels = [];

        GetModulePreferences(Globals::$g->kCNid, $prefs);
        if (ContractUtil::isShowJournal1($prefs)) {
            $newValidValues[] = ContractSchedule::JOURNAL1;
            $newValidLabels[] = $prefs['REVENUEJOURNAL1'] . ' / ' . $prefs['EXPENSEJOURNAL1'];
        }
        if (ContractUtil::isShowJournal2($prefs)) {
            $newValidValues[] = ContractSchedule::JOURNAL2;
            $newValidLabels[] = $prefs['REVENUEJOURNAL2'] . ' / ' . $prefs['EXPENSEJOURNAL2'];
        }
        $field[0]->setProperty(array('type', 'validvalues'), $newValidValues);
        $field[0]->setProperty(array('type', 'validlabels'), $newValidLabels);
    }
    
    private function populateEntryDisplays(array &$scheduleEntries): void
    {
        $scheduleManager = Globals::$g->gManagerFactory->getManager('contractrevenueschedule');
        $fldInfo = $scheduleManager->getAllFieldsInfo();
        $validValues = $fldInfo['STATE']['type']['_validivalues'];
        $validLabels = $fldInfo['STATE']['type']['validvalues'];
        $stateMap = array_combine($validValues, $validLabels);
        $deliveredText = I18N::getSingleToken('IA.DELIVERED');
        $unDeliveredText = I18N::getSingleToken('IA.UNDELIVERED');
        
        foreach ($scheduleEntries as &$entry) {
            $entry['SCHEDULESTATE_DISPLAY'] = $stateMap[$entry['SCHEDULESTATE']] ?? null;
            $entry['DELIVERYSTATUS_DISPLAY'] = ($entry['DELIVERYSTATUS'] == 'D') ? $deliveredText : $unDeliveredText;
        }
    }
    
    protected function processClearAction(array &$_params): bool
    {
        if (!ContractUtil::isBulkClearEnabled()) {
            $this->notYetImplemented(ContractManageScheduleManager::ACTION_CLEAR, $_params);
        }

        $this->retrieveObjectFromView($_params, $obj);
        
        if (!$this->validateClearParameters($obj)) {
            return false;
        }

        $scheduleEntries = [];
        
        $ok = true;
        $this->getClearParameters($obj, $scheduleEntries, $hasEntries);
        $scheduleTypeCode = $obj['TYPEFILTER'] ?? ManageScheduleFilter::TYPE_REVENUE;
        $journalFilter = $obj['JOURNALFILTER'] ?? ContractSchedule::JOURNAL1;
        
        $class = ContractClearRevRecConsumerHelper::class;
        switch ($scheduleTypeCode . $journalFilter) {
            case ContractRevenueSchedule::TYPE_EXPENSE_CODE . ContractSchedule::JOURNAL1:
                $scheduleType = ContractRevenueSchedule::TYPE_EXPENSE;
                $class = ContractClearExpenseConsumerHelper::class;
                break;
            case ContractRevenueSchedule::TYPE_EXPENSE_CODE . ContractSchedule::JOURNAL2:
                $scheduleType = ContractRevenueSchedule::TYPE_EXPENSE2;
                $class = ContractClearExpenseConsumerHelper::class;
                break;
            case ContractRevenueSchedule::TYPE_REVENUE_CODE . ContractSchedule::JOURNAL2:
                $scheduleType = ContractRevenueSchedule::TYPE_REVENUE2;
                break;
            default:
                $scheduleType = ContractRevenueSchedule::TYPE_REVENUE;
                break;
        }
        
        if ($hasEntries) {
            
            // TODO: This is for consistency with the UI, which uses user prefs to determine J1 and J2 names.
            // However, we should be using the name for the actual journal the schedule is associated to
            //$journalSymbol = $scheduleEntries[0]['JOURNALSYMBOL'] ?? '';
            $journalSymbol = $class::getJournalName($scheduleType);
            $runParams = ['SCHEDULE_TYPE' => $scheduleType, 'JOURNAL_SYMBOL' => $journalSymbol];
            
            if (ContractUtil::isContractRunObjectFeatureEnabled()) {
                $contactInfo = Profile::getUserCacheProperty('contact');
                $email = $contactInfo['EMAIL1'];
                
                $ok = $class::createRunObject([$scheduleType => $scheduleEntries], $mesg, $email, $runParams, null, 'Offline');
            } else {
                $ok = $class::executeImmediate([$scheduleType => $scheduleEntries], $mesg, $runParams);
            }
            parent::SetMessage($mesg);
        }
        
        // Fetch new data
        $this->processViewListAction($_params);
        
        return $ok;
    }
    
    protected function processHoldAction(array &$_params): bool
    {
        $ok = true;
        
        if (!ContractUtil::isBulkHoldEnabled()) {
            $this->notYetImplemented(ContractManageScheduleManager::ACTION_HOLD, $_params);
        }
        
        $this->retrieveObjectFromView($_params, $obj);
        
        if (!$this->validateHoldParameters($obj)) {
            return false;
        }
        
        $this->getHoldParameters($obj, $contractLineGroups, $schedulesToHold, $holdDate, $holdMemo);
        
        $hasEntries = !empty($contractLineGroups);
        $obj['CANHOLD'] = $obj['HASENTRIES'] = $hasEntries;

        if ($hasEntries) {
            $runParams = [
                'SCHEDULES_TO_HOLD' => $schedulesToHold,
                'HOLD_DATE' => $holdDate,
                'HOLD_MEMO' => $holdMemo,
            ];

            if (ContractUtil::isContractRunObjectFeatureEnabled()) {
                $contactInfo = Profile::getUserCacheProperty('contact');
                $email = $contactInfo['EMAIL1'];
                $ok = ContractBulkHoldConsumerHelper::createRunObject($contractLineGroups, $mesg, $email, $runParams, null, 'Offline');
            } else {
                $ok = ContractBulkHoldConsumerHelper::executeImmediate($contractLineGroups, $mesg, $runParams);
            }
            parent::SetMessage($mesg);
        }
        
        // Fetch new data
        $this->processViewListAction($_params);
        
        return $ok;
    }
    
    protected function processResumeAction(array &$_params): bool
    {
        $ok = true;
        
        if (!ContractUtil::isBulkResumeEnabled()) {
            $this->notYetImplemented(ContractManageScheduleManager::ACTION_RESUME, $_params);
        }
        
        $this->retrieveObjectFromView($_params, $obj);
        
        if (!$this->validateResumeParameters($obj)) {
            return false;
        }
        
        $this->getResumeParameters($obj, $contractLineGroups, $schedulesToResume, $resumeDate, $resumeMemo);
        
        $hasEntries = !empty($contractLineGroups);
        $obj['CANRESUME'] = $obj['HASENTRIES'] = $hasEntries;
        
        if ($hasEntries) {
            $runParams = [
                'SCHEDULES_TO_RESUME' => $schedulesToResume,
                'RESUME_DATE' => $resumeDate,
                'RESUME_MEMO' => $resumeMemo,
            ];
            
            if (ContractUtil::isContractRunObjectFeatureEnabled()) {
                $contactInfo = Profile::getUserCacheProperty('contact');
                $email = $contactInfo['EMAIL1'];
                $ok = ContractBulkResumeConsumerHelper::createRunObject($contractLineGroups, $mesg, $email, $runParams, null, 'Offline');
            } else {
                $ok = ContractBulkResumeConsumerHelper::executeImmediate($contractLineGroups, $mesg, $runParams);
            }
            parent::SetMessage($mesg);
        }
        
        // Fetch new data
        $this->processViewListAction($_params);
        
        return $ok;
    }
    
    protected function processDeliverLinesAction(array &$_params): bool
    {
        $ok = true;
        
        if (!ContractUtil::isBulkDeliverEnabled()) {
            $this->notYetImplemented(ContractManageScheduleManager::ACTION_DELIVER, $_params);
        }
        
        $this->retrieveObjectFromView($_params, $obj);
        
        if (!$this->validateDeliverLinesParameters($obj)) {
            return false;
        }
        
        $this->getDeliverParameters($obj, $contractLineGroups, $deliveryDate);
        
        $hasEntries = !empty($contractLineGroups);
        $obj['CANDELIVER'] = $obj['HASENTRIES'] = $hasEntries;
        
        if ($hasEntries) {
            $runParams = [
                'DELIVERY_DATE' => $deliveryDate,
            ];
            
            if (ContractUtil::isContractRunObjectFeatureEnabled()) {
                $contactInfo = Profile::getUserCacheProperty('contact');
                $email = $contactInfo['EMAIL1'];
                $ok = ContractBulkDeliverConsumerHelper::createRunObject($contractLineGroups, $mesg, $email, $runParams, null, 'Offline');
            } else {
                $ok = ContractBulkDeliverConsumerHelper::executeImmediate($contractLineGroups, $mesg, $runParams);
            }
            parent::SetMessage($mesg);
        }
        
        // Fetch new data
        $this->processViewListAction($_params);
        
        return $ok;
    }
    
    /**
     * This is a temporary method to produce an error for unimplemented actions
     *
     * @noinspection PhpUnusedParameterInspection
     */
    private function notYetImplemented(string $action, array $_params): bool
    {
        $message = "The $action action is not yet implemented!";
        Globals::$g->gErr->addError('', __FILE__ . ":" . __LINE__, $message);
        Globals::$g->gErr->addError('CN-0237', '', $message);
        $this->state = $this->kErrorState;
        
        return false;
    }
    
    private function removeAction(EditorView $view, string $action)
    {
        $view->findComponents(['path' => 'SCHEDULESELIGIBLETO'], EditorComponentFactory::TYPE_FIELD, $fld);
        $actionFld = $fld[0] ?? null;
        
        if ($actionFld) {
            $typeData = $actionFld->getProperty('type');
            $validLabels = $typeData['validlabels'];
            $validValues = $typeData['validvalues'];
            
            $index = array_search($action, $validValues);
            if ($index !== false) {
                unset($validLabels[$index]);
                unset($validValues[$index]);
                
                $actionFld->setProperty(['type', 'validlabels'], array_values($validLabels));
                $actionFld->setProperty(['type', 'validvalues'], array_values($validValues));
            }
        }
    }
    
    /**
     * MergeOwnerDimensions
     *     override parent to call also call mergeDimensionforNonOwnedObject
     *
     * @param array &$_params params array
     */
    protected function MergeOwnerDimensions(&$_params)
    {
        parent::MergeOwnerDimensions($_params);
        
        $this->mergeDimensionforNonOwnedObject($_params, 'contractdetail');
    }
    
    /**
     * There are multiple similar versions of this method in other class hierarchies
     * This was copied from ContractEditor, but here we only return UDDs
     *
     * @param array  $_params
     * @param string $entity
     */
    private function mergeDimensionforNonOwnedObject(&$_params, $entity)
    {
        $allNodes = $this->getDefaultsDimensionsFields($_params, $entity);
        $uddFieldNames = Pt_StandardUtil::getGLDimensionFieldNames();
        $nodes = [];
        foreach ($allNodes as $node) {
            if (in_array($node['path'], $uddFieldNames)) {
                $nodes[] = $node;
            }
        }
        $fieldCnt = count($nodes);
        
        // if the ownedobject has dimensions we add them to the default container
        if (!isset($nodes) || $fieldCnt <= 0) {
            return;
        }
        
        // let's see if we have a default container for the dimension header fields.
        $matches = [];
        $container = null;
        
        self::findElements($_params['view'], ['dimFields' => $entity], null, $matches);
        if (is_array($matches) && count($matches) > 0) {
            $container = &$matches[0];
        }
        
        if (isset($container)) {
            foreach ($nodes as &$node) {
                $this->addCustomFieldToLayout($node, $container);
            }
        }
    }
}
