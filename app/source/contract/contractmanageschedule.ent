<?php
/**
 * File contractmanageschedule.ent contains the entity definition for contractmanageschedule
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2015 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

$kSchemas['contractmanageschedule'] = array(

    'object' => array(
        'RECORDNO',
    ),


    'schema' => array(
        'RECORDNO' => 'record#',
    ),


    'fieldinfo' => array (
        array(
            'path' => 'RECORDNO',
            'desc' => 'IA.RECORD_NUMBER',
            'fullname' => 'IA.RECORD_NUMBER',
            'type' => array(
                'ptype' => 'sequence',
                'type' => 'integer',
                'maxlength' => 8,
                'size' => 8,
                'format' => $gRecordNoFormat,
            ),
            'readonly' => true
        ),
        [
            'path' => 'SCHEDULESELIGIBLETO',
            'desc' => 'IA.ACTION',
            'fullname' => 'IA.ACTION',
            'type' => [
                'type' => 'enum',
                'ptype' => 'enum',
                'validlabels' => [
                    'IA.POST',
                    'IA.CLEAR',
                    'IA.HOLD',
                    'IA.RESUME',
                    'IA.DELIVER',
                ],
                'validvalues' => [
                    'Post',
                    'Clear',
                    'Hold',
                    'Resume',
                    'Deliver',
                ],
            ],
            'default' => 'Post',
            'required' => true,
        ],
        [
            'path' => 'ASOFDATE',
            'desc' => 'IA.AS_OF_DATE',
            'fullname' => 'IA.AS_OF_DATE',
            'type' => [
                'type' => 'date',
                'ptype' => 'date',
            ],
            'required' => true,
        ],
        array(
            'path' => 'CUSTOMERNGROUPID',
            'fullname' => 'IA.CUSTOMER_AND_GROUP',
            'desc' => 'IA.CUSTOMER_AND_GROUP',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'customerngrouppick',
                'pickentity' => 'customerngrouppick',
                'pickfield' => ['PICKID', 'TYPE'],
                'hideNonPostingDim' => true,
                'pick_url' => 'picker.phtml?.hideNonPostingDim=1'
            ),
        ),
        [
            'path' => 'CUSTOMERTYPE',
            'desc' => 'IA.CUSTOMER_TYPE',
            'fullname' => 'IA.CUSTOMER_TYPE',
            'type' => [
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'custtype',
                'maxlength' => 40,
                'size' => 20,
            ],
            'nonew' => true,
        ],
        array (
            'path' => 'CUSTOMERNGROUP_TYPE',
            'fullname' => 'IA.CUSTOMER_GROUP_PICKER_TYPE',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 20,
            ],
            'hidden' => true,
        ),
        array(
            'path' => 'CONTRACTNGROUPID',
            'fullname' => 'IA.CONTRACT_AND_GROUP',
            'desc' => 'IA.CONTRACT_AND_GROUP',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'contractngrouppick',
                'pickentity' => 'contractngrouppick',
                'pickfield' => ['PICKID', 'TYPE']
            ),
        ),
        array (
            'path' => 'CONTRACTNGROUP_TYPE',
            'fullname' => 'IA.CONTRACT_GROUP_PICKER_TYPE',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 20,
            ],
            'hidden' => true,
        ),
        array(
            'path' => 'PROJECTNGROUPID',
            'fullname' => 'IA.PROJECT_AND_GROUP',
            'desc' => 'IA.PROJECT_AND_GROUP',
            'type' => array(
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'projectngrouppick',
                'pickentity' => 'projectngrouppick',
                'pickfield' => array('PICKID', 'TYPE'),
            )
        ),
        array (
            'path' => 'PROJECTNGROUP_TYPE',
            'fullname' => 'IA.PROJECT_GROUP_PICKER_TYPE',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 20,
            ],
            'hidden' => true,
        ),
        [
            'path' => 'TASKNGROUPID',
            'fullname' => 'IA.TASK_AND_GROUP',
            'desc' => 'IA.TASK_AND_GROUP',
            'type' => [
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'taskngrouppick',
                'pickentity' => 'taskngrouppick',
                'pickfield' => ['PICKID', 'TYPE'],
            ],
        ],
        [
            'path' => 'TASKNGROUP_TYPE',
            'fullname' => 'IA.TASK_GROUP_PICKER_TYPE',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
            ],
            'hidden' => true,
        ],
        [
            'path' => 'DOCUMENT_NUMBER',
            'fullname' => 'IA.DOCUMENT_NUMBER',
            'type' => [
                'type' => 'text',
                'ptype' => 'text',
                'size' => 20,
                'maxlength' => 100,
            ],
        ],
        [
            'path' => 'TRANSACTIONCURR',
            'desc' => 'IA.TRANSACTION_CURRENCY',
            'fullname' => 'IA.TRANSACTION_CURRENCY',
            'type' => [
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'trxcurrencies',
                'maxlength' => 40,
                'size' => 20,
            ],
            'nonew' => true,
        ],
        [
            'path' => 'REVRECTEMPLATE',
            'desc' => 'IA.REVENUE_RECOGNITION_TEMPLATE',
            'fullname' => 'IA.TEMPLATE',
            'type' => [
                'ptype' => 'ptr',
                'type' => 'text',
                'entity' => 'contractrevenuetemplate',
                'pickentity' => 'contractrevenuetemplateallpick',
                'pickfield' => ['PICKID', 'METHOD', 'RECORDNO'],
            ],
            'isInSpecialDiv' => true,
            'required' => false,
            'nonew' => true,
            'renameable' => true,
        ],
        [
            'path' => 'EXPENSETEMPLATE',
            'desc' => 'IA.EXPENSE_TEMPLATE',
            'fullname' => 'IA.TEMPLATE',
            'type' => [
                'ptype' => 'ptr',
                'type' => 'text',
                'entity' => 'contractexpensetemplate',
                'pickentity' => 'contractexpensetemplatepick',
                'pickfield' => ['PICKID', 'METHOD', 'RECORDNO'],
            ],
            'isInSpecialDiv' => true,
            'required' => false,
            'nonew' => true,
            'renameable' => true,
        ],
        [
            'path' => 'MAXRECCNT',
            'desc' => 'IA.MAXIMUM_NUMBER_OF_RECORDS_TO_SELECT',
            'fullname' => 'IA.MAXIMUM_NUMBER_OF_RECORDS_TO_SELECT',
            'type' => [
                'type' => 'enum',
                'ptype' => 'enum',
                'validlabels' => [
                    'IA.TWENTY_IN_NUMBER',
                    'IA.FIFTY_IN_NUMBER',
                    'IA.HUNDRED_IN_NUMBER',
                    'IA.HUNDRED_AND_FIFTY_IN_NUMBER',
                    'IA.TWO_HUNDRED_IN_NUMBER',
                    'IA.THREE_HUNDRED_IN_NUMBER',
                    'IA.FOUR_HUNDRED_IN_NUMBER',
                    'IA.FIVE_HUNDRED_IN_NUMBER',
                    'IA.SIX_HUNDRED_IN_NUMBER',
                    'IA.SEVEN_HUNDRED_IN_NUMBER',
                    'IA.EIGHT_HUNDRED_IN_NUMBER',
                    'IA.NINE_HUNDRED_IN_NUMBER',
                    'IA.THOUSAND_IN_NUMBER',
                    'IA.ALL',
                ],
                'validvalues' => [
                    20, 50, 100, 150, 200, 300, 400, 500, 600, 700, 800, 900, 1000, ContractManageScheduleFormEditor::MAX_REC_COUNT_ALL
                ],
            ],
        ],
        [
            'path' => 'SORTBY',
            'desc' => 'IA.SORT_BY',
            'fullname' => 'IA.SORT_BY_SORT_ORDER',
            'type' => [
                'type' => 'enum',
                'ptype' => 'enum',
                'validlabels' => [
                    'IA.ACTUAL_POSTING_DATE',
                    'IA.AMOUNT',
                    'IA.CONTRACT',
                    'IA.CUSTOMER',
                    'IA.ITEM',
                    'IA.LINE_START_DATE',
                    'IA.LINE_END_DATE',
                    'IA.PROJECT',
                    'IA.SCHEDULED_POSTING_DATE',
                    'IA.SCHEDULE_STATUS',
                    'IA.SCHEDULE_START_DATE',
                    'IA.SCHEDULE_END_DATE',
                    'IA.TEMPLATE',
                ],
                'validvalues' => ['APD', 'A', 'CN', 'CT', 'IT', 'LSD', 'LED', 'PJ', 'PD', 'S', 'SD', 'ED', 'TN',],
            ],
        ],
        [
            'path' => 'SORTORDER',
            'desc' => 'IA.SORT_ORDER',
            'fullname' => '',
            'type' => [
                'type' => 'enum',
                'ptype' => 'enum',
                'validlabels' => ['IA.ASCENDING', 'IA.DESCENDING'],
                'validvalues' => ['ASC', 'DESC'],
            ],
        ],
        array(
            'path' => 'DEPTNGROUPID',
            'fullname' => 'IA.DEPARTMENT_AND_GROUP',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'departmentfilterpick',
                'pickentity' => 'departmentfilterpick',
                'pickfield' => array('PICKID', 'TYPE'),
                'hideNonPostingDim' => true,
                'pick_url' => 'picker.phtml?.hideNonPostingDim=1'
            )
        ),
        array(
            'path' => 'DEPTNGROUP_TYPE',
            'fullname' => 'IA.DEPARTMENT_GROUP_PICKER_TYPE',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 20,
            ),
            'hidden' => true,
        ),
        array(
            'path' => 'CLASSNGROUPID',
            'fullname' => 'IA.CLASS_AND_GROUP',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'classngrouppick',
                'pickentity' => 'classngrouppick',
                'pickfield' => array('PICKID', 'TYPE'),
                'hideNonPostingDim' => true,
                'pick_url' => 'picker.phtml?.hideNonPostingDim=1'
            )
        ),
        array(
            'path' => 'CLASSNGROUP_TYPE',
            'fullname' => 'IA.CLASS_GROUP_PICKER_TYPE',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 20,
            ),
            'hidden' => true,
        ),
        array(
            'path' => 'ASSETNGROUPID',
            'fullname' => 'IA.ASSET_AND_GROUP',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'assetngrouppick',
                'pickentity' => 'assetngrouppick',
                'pickfield' => array('PICKID', 'TYPE'),
                'hideNonPostingDim' => true,
                'pick_url' => 'picker.phtml?.hideNonPostingDim=1'
            )
        ),
        array(
            'path' => 'ASSETNGROUP_TYPE',
            'fullname' => 'IA.ASSET_GROUP_PICKER_TYPE',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 20,
            ),
            'hidden' => true,
        ),
        array(
            'path' => 'ITEMNGROUPID',
            'fullname' => 'IA.ITEM_AND_GROUP',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'itemngrouppick',
                'pickentity' => 'itemngrouppick',
                'pickfield' => ['PICKID', 'TYPE'],
            ),
        ),
        array(
            'path' => 'ITEMNGROUP_TYPE',
            'fullname' => 'IA.ITEM_GROUP_PICKER_TYPE',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 20,
            ],
            'hidden' => true,
        ),
        array(
            'path' => 'LOCATIONNGROUPID',
            'fullname' => 'IA.LOCATION_AND_GROUP',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'locationfilterpick',
                'pickentity' => 'locationfilterpick',
                'pickfield' => array('PICKID', 'TYPE'),
                'hideNonPostingDim' => true,
                'pick_url' => 'picker.phtml?.hideNonPostingDim=1'
            ),
        ),
        array(
            'path' => 'LOCATIONNGROUP_TYPE',
            'fullname' => 'IA.LOCATION_GROUP_PICKER_TYPE',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 20,
            ),
            'hidden' => true,
        ),
        array (
            'path' => 'JOURNALFILTER',
            'fullname' => 'IA.JOURNAL',
            'type' => array (
                'ptype' => 'radio',
                'type' => 'text',
                'validlabels' => array('IA.JOURNAL_1', 'IA.JOURNAL_2'),
                'validvalues' => array('J1', 'J2'),
                '_validivalues' => array('J1', 'J2'),
            ),
            'default' => 'J1',
        ),
        array (
            'path' => 'TYPEFILTER',
            'fullname' => 'IA.SCHEDULE_TYPE',
            'type' => array (
                'ptype' => 'radio',
                'type' => 'text',
                'validlabels' => array('IA.REVENUE', 'IA.EXPENSE'),
                'validvalues' => array('R', 'E'),
                '_validivalues' => array('R', 'E'),
            ),
            'default' => 'R',
        ),
        array(
            'path' => 'WHENCREATED',
            'fullname' => 'IA.POSTING_DATE',
            'required' => true,
            'type' => $gDateType,
        ),
        array (
            'path' => 'POSTINGOPTIONS',
            'fullname' => 'IA.POSTING_OPTIONS',
            'type' => array (
                'ptype' => 'radio',
                'type' => 'text',
                'validlabels' => array('IA.USE_SCHEDULED_POSTING_DATE', 'IA.SPECIFY_POSTING_DATE'),
                'validvalues' => array('P1', 'P2'),
                '_validivalues' => array('P1', 'P2'),
            ),
            'default' => 'P1',
        ),
        array (
            'path' => 'GENERATEPROJECTREVRECMESSAGE',
            'fullname' => 'IA.PROJECT_ENTRIES_WILL_ALWAYS_USE_SCHEDULED_DATE',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
            ),
            'readonly' => true,
            'hidden' => true,
            'layout' => 'landscape',
        ),
        array (
            'path' => 'GENERATEPROJECTREVREC',
            'fullname' => 'IA.CALCULATE_PERCENT_COMPLETE_REVENUE_FOR_PROJECTS',
            'type' => $gBooleanType,
            'layout' => 'landscape',
            'hidden' => true,
            'default' => true,
        ),
        array(
            'path' => 'ITEMID',
            'fullname' => 'IA.ITEM',
            'desc' => 'IA.ITEM',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'align' => 'left',
            'readonly' => true,
        ),
        array(
            'path' => 'CUSTOMERID',
            'fullname' => 'IA.CUSTOMER',
            'desc' => 'IA.CUSTOMER',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'align' => 'left',
            'readonly' => true,
        ),
        array(
            'path' => 'HEADERORLINE',
            'fullname' => 'IA.TYPE',
            'desc' => 'IA.TYPE',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'align' => 'left',
            'readonly' => true,
        ),
        array(
            'path' => 'JOURNAL',
            'fullname' => 'IA.JOURNAL',
            'desc' => 'IA.JOURNAL',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'align' => 'left',
            'readonly' => true,
        ),
        array(
            'path' => 'CONTRACTNO',
            'fullname' => 'IA.CONTRACT',
            'desc' => 'IA.CONTRACT',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'align' => 'left',
            'readonly' => true,
        ),
        array(
            'path' => 'CONTRACTNAME',
            'fullname' => 'IA.CONTRACT_NAME',
            'desc' => 'IA.CONTRACT_NAME',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'readonly' => true,
        ),
        array(
            'path' => 'PROJECTNAME',
            'fullname' => 'IA.PROJECT_NAME',
            'desc' => 'IA.PROJECT_NAME',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                ),
            'readonly' => true,
        ),
        array(
            'path' => 'CONTRACTLINENO',
            'fullname' => 'IA.CONTRACT_LINE_NO',
            'desc' => 'IA.CONTRACT_LINE_NO',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
            'readonly' => true,
        ),
        [
            'path' => 'EXPENSELINENO',
            'fullname' => 'IA.EXPENSE_LINE_NO',
            'desc' => 'IA.EXPENSE_LINE_NO',
            'type' => [
                'ptype' => 'integer',
                'type' => 'integer',
            ],
            'readonly' => true,
        ],
        array(
            'path' => 'AMOUNT',
            'fullname' => 'IA.AMOUNT',
            'desc' => 'IA.AMOUNT',
            'type' => array(
                'ptype' => 'text',
                'type' => 'decimal',
            ),
           'align' => 'right',
            'readonly' => true,
        ),
        array(
            'path' => 'BASEAMOUNT',
            'fullname' => 'IA.BASE_AMOUNT',
            'desc' => 'IA.BASE_AMOUNT',
            'type' => array(
                'ptype' => 'text',
                'type' => 'decimal',
            ),
            'align' => 'right',
            'readonly' => true,
        ),
        array(
            'path' => 'POSTINGDATE',
            'fullname' => 'IA.SCHEDULED_POSTING_DATE',
            'desc' => 'IA.POSTING_DATE',
            'type' => array(
                'ptype' => 'date',
                'type' => 'date',
            ),
            'readonly' => true,
        ),
        [
            'path' => 'ACTUALPOSTINGDATE',
            'fullname' => 'IA.ACTUAL_POSTING_DATE',
            'desc' => 'IA.ACTUAL_POSTING_DATE',
            'type' => [
                'ptype' => 'date',
                'type' => 'date',
            ],
            'readonly' => true,
        ],
        array(
            'path' => 'SCHEDULEENTRYKEY',
            'fullname' => 'IA.SCHEDULE_ENTRY_KEY',
            'desc' => 'IA.SCHEDULE_ENTRY_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
            'required' => true,
            'hidden' => true,
        ),
        array(
            'path' => 'SCHEDULEKEY',
            'fullname' => 'IA.SCHEDULE_KEY',
            'desc' => 'IA.SCHEDULE_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
            'required' => true,
            'hidden' => true,
        ),
        array(
            'path' => 'ITEMIDHTML',
            'fullname' => 'IA.ITEM',
            'desc' => 'IA.ITEM',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'align' => 'left',
            'isHTML' => true,
            'readonly' => true,
        ),
        array(
            'path' => 'CUSTOMERIDHTML',
            'fullname' => 'IA.CUSTOMER',
            'desc' => 'IA.CUSTOMER',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'align' => 'left',
            'isHTML' => true,
            'readonly' => true,
        ),
        array(
            'path' => 'JOURNALHTML',
            'fullname' => 'IA.JOURNAL',
            'desc' => 'IA.JOURNAL',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'align' => 'left',
            'isHTML' => true,
            'readonly' => true,
        ),
        array(
            'path' => 'CONTRACTNOHTML',
            'fullname' => 'IA.CONTRACT',
            'desc' => 'IA.CONTRACT',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'align' => 'left',
            'isHTML' => true,
            'readonly' => true,
        ),
        array(
            'path' => 'PROJECTHTML',
            'fullname' => 'IA.PROJECT',
            'desc' => 'IA.PROJECT',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'align' => 'left',
            'isHTML' => true,
            'readonly' => true,
        ),
        array(
            'path' => 'AMOUNTHTML',
            'fullname' => 'IA.AMOUNT',
            'desc' => 'IA.AMOUNT',
            'type' => array(
                'ptype' => 'text',
                'type' => 'decimal',
            ),
            'align' => 'right',
            'isHTML' => true,
            'readonly' => true,
        ),
        [
            'path' => 'TOTALAMOUNTHTML',
            'fullname' => 'IA.TOTAL_AMOUNT',
            'desc' => 'IA.TOTAL_AMOUNT',
            'type' => [
                'ptype' => 'text',
                'type' => 'decimal',
            ],
            'align' => 'right',
            'isHTML' => true,
            'readonly' => true,
        ],
        [
            'path' => 'CONTRACTLINENOHTML',
            'fullname' => 'IA.CONTRACT_LINE_NO',
            'desc' => 'IA.CONTRACT_LINE_NO',
            'type' => [
                'ptype' => 'text',
                'type' => 'integer',
            ],
            'align' => 'right',
            'isHTML' => true,
            'readonly' => true,
        ],
        [
            'path' => 'EXPENSELINENOHTML',
            'fullname' => 'IA.EXPENSE_LINE_NO',
            'desc' => 'IA.EXPENSE_LINE_NO',
            'type' => [
                'ptype' => 'text',
                'type' => 'integer',
            ],
            'align' => 'right',
            'isHTML' => true,
            'readonly' => true,
        ],
        array(
            'path' => 'COMPUTATIONMEMO',
            'desc' => 'IA.COMPUTATION_MEMO',
            'fullname' => 'IA.COMPUTATION_MEMO',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'isHTML' => true,
            'readonly' => true,
        ),
        [
            'path' => 'SCHEDULESTOHOLD',
            'fullname' => 'IA.SCHEDULES_TO_HOLD',
            'type' => [
                'ptype' => 'checkbox',
                'type' => 'checkbox',
                'delimiter' => ',',
                'validlabels' => [
                    'IA.BILLING_SCHEDULES',
                    'IA.REVENUE_SCHEDULES',
                    'IA.EXPENSE_SCHEDULES'
                ],
                'validvalues' => ['B', 'R', 'E'],
            ],
            'layout' => 'portrait',
        ],
        [
            'path' => 'HOLDDATE',
            'fullname' => 'IA.HOLD_DATE',
            'type' => $gDateType,
            'required' => true,
        ],
        [
            'path' => 'HOLDMEMO',
            'fullname' => 'IA.HOLD_MEMO',
            'type' => [
                'ptype' => 'multitext',
                'type' => 'text',
                'maxlength' => 500,
            ],
        ],
        [
            'path' => 'NO_HOLD_LINES_TEXT',
            'fullname' => '',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
            ],
            'isHTML' => true,
            'default' => 'IA.NO_CONTRACT_LINE_REVENUE_BILLING_OR_EXPENSE',
        ],
        [
            'path' => 'DELIVERYDATE',
            'desc' => 'IA.DELIVERY_DATE',
            'fullname' => 'IA.DELIVERY_DATE',
            'type' => $gDateType,
            'required' => true,
        ],
        [
            'fullname' => "",
            'type' => [
                'ptype' => 'label',
                'type' => 'label',
            ],
            'path' => 'LINESTOHOLD',
            'default' => 'IA.LINES_TO_HOLD',
            'readonly' => true,
        ],
        [
            'path' => 'SCHEDULESTORESUME',
            'fullname' => 'IA.SCHEDULES_TO_RESUME',
            'type' => [
                'ptype' => 'checkbox',
                'type' => 'checkbox',
                'delimiter' => ',',
                'validlabels' => [
                    'IA.BILLING_SCHEDULES',
                    'IA.REVENUE_SCHEDULES',
                    'IA.EXPENSE_SCHEDULES',
                ],
                'validvalues' => ['B', 'R', 'E'],
            ],
            'layout' => 'portrait',
        ],
        [
            'fullname' => "",
            'type' => [
                'ptype' => 'label',
                'type' => 'label',
            ],
            'path' => 'LINESTORESUME',
            'default' => 'IA.LINES_TO_RESUME',
            'readonly' => true,
        ],
        [
            'path' => 'RESUMEDATE',
            'desc' => 'IA.RESUME_DATE',
            'fullname' => 'IA.RESUME_DATE',
            'type' => $gDateType,
            'required' => true,
        ],
        [
            'path' => 'RESUMEMEMO',
            'fullname' => 'IA.RESUME_MEMO',
            'type' => [
                'ptype' => 'multitext',
                'type' => 'text',
                'maxlength' => 500,
            ],
        ],
        [
            'path' => 'REVENUEADJUSTMENTTYPE',
            'fullname' => 'IA.REVENUE_ADJUSTMENT_TYPE',
            'type' => [
                'ptype' => 'enum',
                'type' => 'text',
                'delimiter' => ',',
                'validlabels' => ['IA.USE_TEMPLATE_VALUE', 'IA.CATCH_UP_ONE_TIME', 'IA.CATCH_UP_DISTRIBUTED', 'IA.WALK_FORWARD'],
                'validvalues' => array('Template', 'One time', 'Distributed', 'Walk forward'),
            ],
            'hidden' => false,
            'default' => 'Template',
            'layout' => 'portrait',
        ],
        [
            'path' => 'NO_RESUME_LINES_TEXT',
            'fullname' => '',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
            ],
            'isHTML' => true,
            'default' => 'IA.NO_CONTRACT_LINE_REVENUE_BILLING_OR_EXPENSE',
        ],
        [
            'path' => 'CONTRACTTYPE',
            'fullname' => 'IA.CONTRACT_TYPE',
            'type' => [
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'contracttype'
            ],
            'showInGroup' => true,
        ],
        [
            'path' => 'PROJECTTYPE',
            'fullname' => 'IA.PROJECT_TYPE',
            'type' => [
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'projecttype',
                'pickentity' => 'projecttype',
                'forceCombo' => true
            ],
        ],
        [
            'path' => 'PROJECTMANAGERID',
            'fullname' => 'IA.PROJECT_MANAGER',
            'type' => [
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'employee',
                'pickentity' => 'employeepick',
                'forceCombo' => true
            ],
        ],
        [
            'path' => 'EMPLOYEENGROUPID',
            'fullname' => 'IA.EMPLOYEE_AND_GROUP',
            'desc' => 'IA.NAME',
            'type' => [
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'employeengrouppick',
                'pickentity' => 'employeengrouppick',
                'pickfield' => array('PICKID', 'TYPE'),
            ]
        ],
        [
            'path' => 'EMPLOYEENGROUP_TYPE',
            'fullname' => 'IA.EMPLOYEE_GROUP_PICKER_TYPE',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
            ],
            'hidden' => true,
        ],
        [
            'path' => 'OFFLINE',
            'fullname' => 'IA.OFFLINE',
            'desc' => 'IA.OFFLINE',
            'type' => $gBooleanType,
            'layout' => 'landscape',
            'hidden' => true,
            'default' => false,
        ],
        [
            'path' => 'TEMPLATEHTML',
            'desc' => 'IA.TEMPLATE',
            'fullname' => 'IA.TEMPLATE',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
            ],
            'readonly' => true,
            'isHTML' => true,
        ],
        [
            'path' => 'CONTRACTTYPEHTML',
            'desc' => 'IA.CONTRACT_TYPE',
            'fullname' => 'IA.CONTRACT_TYPE',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
            ],
            'readonly' => true,
            'isHTML' => true,
        ],
        [
            'path' => 'CUSTOMERTYPEHTML',
            'desc' => 'IA.CUSTOMER_TYPE',
            'fullname' => 'IA.CUSTOMER_TYPE',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
            ],
            'readonly' => true,
            'isHTML' => true,
        ],
        [
            'path' => 'PROJECTTYPEHTML',
            'desc' => 'IA.PROJECT_TYPE',
            'fullname' => 'IA.PROJECT_TYPE',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
            ],
            'readonly' => true,
            'isHTML' => true,
        ],
        [
            'path' => 'TASKHTML',
            'desc' => 'IA.TASK',
            'fullname' => 'IA.TASK',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
            ],
            'readonly' => true,
            'isHTML' => true,
        ],
        [
            'path' => 'PERCENTRECOGNIZED',
            'desc' => 'IA.PERCENTAGE_TO_RECOGNIZE',
            'fullname' => 'IA.PERCENTAGE_TO_RECOGNIZE',
            'type' => [
                'ptype' => 'currency',
                'type' => 'decimal',
            ],
            'readonly' => true,
        ],
    ),

    'table' => 'contractscheduleentry',
    'autoincrement' => 'RECORDNO',
    'vid' => 'RECORDNO',
    'module' => 'cn',
    'printas' => 'IA.CONTRACTS_SCHEDULE',
    'pluralprintas' => 'IA.CONTRACTS_SCHEDULES',
);
