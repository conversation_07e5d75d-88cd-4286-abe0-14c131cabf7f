<?php

/**
 * Manager class for ContractACPRun
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Sage Intacct Inc., All Rights Reserved
 */

require_once 'ContractUtil.cls';

class ContractACPRunManager extends EntityManager
{
    const OBJECT_NAME = 'IA.PROCESS_CONTRACT_SCHEDULES';
    const OBJECT_NAME_AFTER_RUN = 'IA.PROCESS_CONTRACT_SCHEDULES_RESULTS';
    const CONFIGURATION_PAGE_NAME = 'Process Contract Schedules Configuration';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @param array $values 'Contract ACP Run' object values.
     *
     * @return bool True if successful or false.
     */
    protected function regularAdd(&$values)
    {
        global $gErr;
        $source = __METHOD__;
        $historicalContracts = [];
        $ok = $this->_QM->beginTrx($source);
        if ($ok) {
            $ok = $ok && parent::regularAdd($values);
            $ok = $ok && $this->scheduleOffline($values, $historicalContracts);
        }
        $ok = $ok && $this->_QM->commitTrx($source);

        if ( !$ok ) {
            $msg = "Could not process contracts!";
            $gErr->addError('CN-0158', __FILE__ . ':' . __LINE__, $msg);

            $this->_QM->rollbackTrx($source);
        } else {
            $params = [];
            $params['ASOFDATE'] = $values['ASOFDATE'];
            $ok = $this->createIMSJobsAfterCommit($historicalContracts, $values['RECORDNO'], $params);
        }

        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    public function ValidateRecord(&$values)
    {
        $ok = true;
        $asOfDate = $values['ASOFDATE'];
        $cnSetup = Globals::$g->gManagerFactory->getManager('cnsetup');
        $prefs = $cnSetup->getACPPreferences();
        if ($prefs) {
            $goLiveDate = $prefs['ACPENT_GOLIVEDATE'];
            if (DateCompare($goLiveDate, $asOfDate) <= 0) {
                $ok = false;
                $msg = "As of date ($asOfDate) needs to be earlier than the Go live date $goLiveDate.";
                Globals::$g->gErr->addIAError('CN-0159', __FILE__ . ':' . __LINE__, $msg,
                                              ['ASOFDATE' => $asOfDate, 'GOLIVEDATE' => $goLiveDate], '');
            }
        } else {
            $ok = false;
            $msg = "Go live date must be set in " . self::CONFIGURATION_PAGE_NAME;
            Globals::$g->gErr->addError('CN-0160', __FILE__ . ':' . __LINE__, $msg, '');
        }
        return $ok && parent::ValidateRecord($values);
    }

    /**
     * Creates multiple parallel offline jobs
     *
     * @param array $runValues Run values
     * @param array $historicalContracts
     *
     * @return bool
     */
    protected function scheduleOffline($runValues, &$historicalContracts)
    {
        $ok = true;
        $runKey = $runValues['RECORDNO'];
        if (!$runKey) {
            Globals::$g->gErr->addError('CN-0161', __FILE__ . ':' . __LINE__, 'Missing ACP run key.', '', '');
            return false;
        }
    
        $params['ASOFDATE'] = $runValues['ASOFDATE'];

        $contractHandler = new ContractHandler();
        $historicalContracts = $contractHandler->findHistoricalContracts($runValues['ASOFDATE']);
        $ok = $ok && $this->createRunEntries($historicalContracts, $runValues['RECORDNO']);

        return $ok;
    }

    /**
     * @param array     $historicalContracts
     * @param int       $runKey
     * @param array     $params
     *
     * @return bool
     */
    private function createIMSJobsAfterCommit($historicalContracts, $runKey, $params) {
        $ok = true;
        $contractCount = count($historicalContracts);
        if ($contractCount > ContractUtil::CONTRACT_ACP_MAX_CONTRACTS_PERJOB) {

            $numParallelJobs = ceil($contractCount/ContractUtil::CONTRACT_ACP_MAX_CONTRACTS_PERJOB);
            $startIndex = 0;

            $totalInvoicesPerJob = ceil($contractCount / $numParallelJobs);

            for ($i=0; $i<$numParallelJobs; $i++) {
                $currContractIds = [];

                for ($j=0; $j<$totalInvoicesPerJob; $j++) {

                    // Last job may not have enough
                    if (!$historicalContracts[$startIndex]) {
                        break;
                    }

                    $currContractIds[] = $historicalContracts[$startIndex++];
                }

                if (!$this->createOfflineJob($runKey, $currContractIds, $params)) {
                    $ok = false;
                    break;
                }
            }

        } else {
            $ok = $ok && $this->createOfflineJob($runKey, $historicalContracts, $params);
        }

        return $ok;
    }

    /**
     * @param string[][] $histContracts
     * @param int        $runKey
     *
     * @return bool
     */
    private function createRunEntries($histContracts, $runKey)
    {
        $ok = true;
        $cnACPRunEntryManager = Globals::$g->gManagerFactory->getManager('contractacprunentry');
        foreach ($histContracts as $contract) {
            if (!$ok) {
                break;
            }
            $values = ['CONTRACTID' => $contract['CONTRACTID'], 'CONTRACTRUNKEY' => $runKey];
            $ok = $cnACPRunEntryManager->add($values);
        }
        return $ok;
    }

    /**
     * Create offline job to process ACP Contract (that have Historical schedules).
     *
     * @param int   $runKey       Run key
     * @param array $contractIds  Header key
     * @param array $params       Params
     * @param int   $currentIndex Current index
     * @param array $docIds       Doc ids
     * @param array $errors       Errors
     *
     * @return bool
     */
    protected function createOfflineJob($runKey, $contractIds, $params, $currentIndex=0, $docIds=[], $errors=[])
    {
        $payload = [
            'RUNKEY'        => $runKey,
            'CONTRACT_IDS' => $contractIds,
            'CURRENT_INDEX' => $currentIndex,
            'RUN_PARAMS'    => $params,
            'DOCIDS'        => $docIds,
            'ERRORS'        => $errors
        ];

        $publisher = new ims_publish_1(IMS_MODE_NONBLOCKING, IMS_PROCESS_REMOTE, IMS_MODE_QUEUED);

        $ok = $publisher->PublishMsg('ACPRUN', 'ACP', 'ACP_RUN', '', $payload, [], $response);

        return $ok;
    }

    /**
     * @param array $args
     *
     * @return bool
     */
    public function runOffline($args)
    {
        $ok = true;
        if($args == null) {
            $ok = false;
        }
        $runKey = $args['RUNKEY'];
        $values = $this->GetByRecordNo($runKey);
        $values['STATE'] = ContractRunState::STATE_INPROGRESS;
        $ok = $ok && $this->set($values);
        if (!$ok) {
            $msg = 'Failure in updating the state of the Retrspective contract schedules run: ' . $runKey;
            Globals::$g->gErr->addIAError('CN-0162', __FILE__ . ':' . __LINE__, $msg, ['RUNKEY' => $runKey]);

        } else {
            $historicalContracts = $args['CONTRACT_IDS'];
            $params = $args['RUN_PARAMS'];
            $contractHandler = ContractHandler::getInstance();
            $cnACPRunEntryManager = Globals::$g->gManagerFactory->getManager('contractacprunentry');
            foreach ($historicalContracts as $contract) {
                $contractId = $contract['CONTRACTID'];
                $ok = $cnACPRunEntryManager->setState($contractId, $runKey, ContractRunEntryState::STATE_INPROGRESS);
                try {
                    ContractUtil::assert(!XACT_STARTED());
                    $ok = $ok && $contractHandler->processACP(null, $contractId, $params['ASOFDATE']);
                    $postResponse = $contractHandler->getContractSchedulePostResponse();
                    $this->setEntryStateAfterProcessACP($cnACPRunEntryManager, $contractId, $runKey, $postResponse, $ok);
                }
                catch (Exception $e){
                    $cnACPRunEntryManager->setState($contractId, $runKey, ContractRunEntryState::STATE_FAILURE, $e->getMessage());
                }

            }
            $this->checkAndUpdateState($runKey);
            $this->addAdditionalData($values);
            $this->sendCompletionEmail($values, $historicalContracts, $contractHandler->getContractSchedulePostResponse());
        }
        return $ok;
    }

    /**
     * @param array $values
     */
    public function addAdditionalData(&$values)
    {
        $cnSetupMgr = Globals::$g->gManagerFactory->getManager('cnsetup');
        $acpPrefs = $cnSetupMgr->getACPPreferences();
        if ($acpPrefs) {
            $values['GOLIVEDATE'] = $acpPrefs['ACPENT_GOLIVEDATE'];
            $values['ACP_TD'] = $acpPrefs['ACP_TD'];
        }
        $contextLocationName = GetContextLocationName();
        if ($contextLocationName === false) {
            $contextLocationName = _('Top level');
        }
        $values['ENTITY'] = $contextLocationName;
    }

    /**
     * @param int   $runKey
     *
     * @return bool
     */
    private function checkAndUpdateState($runKey)
    {
        $cnACPRunEntryManager = Globals::$g->gManagerFactory->getManager('contractacprunentry');
        $querySpec = [
            'filters' => [
                [['CONTRACTRUNKEY', '=', $runKey]],
            ],
            'orders' => [
                ['CONTRACTID']
            ],
        ];
        $successCnt = 0;
        $failCnt = 0;
        $runEntries = $cnACPRunEntryManager->GetList($querySpec);
        foreach ($runEntries as $runEntry) {
            if ($runEntry['STATE'] == ContractRunEntryState::STATE_FAILURE) {
                $failCnt++;
            } elseif ($runEntry['STATE'] == ContractRunEntryState::STATE_SUCCESS) {
                $successCnt++;
            }
        }
        if ($successCnt + $failCnt == count($runEntries)) {
            $state = $failCnt == 0 ? ContractRunState::STATE_SUCCESS : ($successCnt == 0 ? ContractRunState::STATE_FAILURE : ContractRunState::STATE_PARTIALSUCCESS);
        } else {
            $state = ContractRunState::STATE_INPROGRESS;
        }
        $values = $this->GetByRecordNo($runKey);
        $values['STATE'] = $state;
        $ok = $this->set($values);
        if (!$ok) {
            $msg = 'Failure in updating the state of the Retrspective contract schedules run: ' . $runKey;
            Globals::$g->gErr->addIAError('CN-0162', __FILE__ . ':' . __LINE__, $msg, ['RUNKEY' => $runKey]);
        }
        return $ok;
    }

    /**
     * @param ContractACPRunEntryManager      $cnACPRunEntryManager
     * @param string                          $contractId
     * @param int                             $runKey
     * @param ContractSchedulePostResponseACP $postResponse
     * @param bool                            $ok
     */
    private function setEntryStateAfterProcessACP($cnACPRunEntryManager, $contractId, $runKey, $postResponse, $ok)
    {
        $state = $ok ? ContractRunEntryState::STATE_SUCCESS : ContractRunEntryState::STATE_FAILURE;
        $numProcessed = null;

        if (!$ok) {
            $this->processErrorResponse($contractId, $notes, $postResponse->getContractBillingSchedulePostErrorResponse(), 'generating invoice');
            $this->processErrorResponse($contractId, $notes, $postResponse->getContractRevSchedulePostErrorResponse(), 'posting revenue');
            $this->processErrorResponse($contractId, $notes, $postResponse->getContractExpSchedulePostErrorResponse(), 'posting expense');
        } else {
            $numberOfBillingSchedulesPosted = $postResponse->getNumberOfBillingSchedulesPosted($contractId);
            $numberOfRevenueSchedulesPosted = $postResponse->getNumberOfRevenueSchedulesPosted($contractId);
            $numberOfExpenseSchedulesPosted = $postResponse->getNumberOfExpenseSchedulesPosted($contractId);
            $numProcessed = $numberOfBillingSchedulesPosted + $numberOfRevenueSchedulesPosted + $numberOfExpenseSchedulesPosted;
            if ($numProcessed == 0) {
                $notes = 'No scheduled entries found.';
            } else {
                $needComma = false;
                $notes = '';
                if ($numberOfBillingSchedulesPosted > 0) {
                    $notes = 'Billing entries: ' . $numberOfBillingSchedulesPosted;
                    $needComma = true;
                }
                if ($numberOfRevenueSchedulesPosted > 0) {
                    if ($needComma) {
                        $notes .= ', ';
                    }
                    $notes .= 'Revenue entries: ' . $numberOfRevenueSchedulesPosted;
                    $needComma = true;
                }
                if ($numberOfExpenseSchedulesPosted > 0) {
                    if ($needComma) {
                        $notes .= ', ';
                    }
                    $notes .= 'Expense entries: ' . $numberOfExpenseSchedulesPosted;
                }
            }
        }

        $cnACPRunEntryManager->setState($contractId, $runKey, $state, $notes, $numProcessed);
    }

    /**
     * @param string                              $contractId
     * @param string                              $errMsg
     * @param ContractSchedulePostErrorResponse[] $errResps
     * @param string                              $action
     */
    private function processErrorResponse($contractId, &$errMsg, $errResps, string $action)
    {
        $errsRespsForContract = [];
        foreach ($errResps as $errResp) {
            if ($errResp->getContractId() == $contractId) {
                $errsRespsForContract[] = $errResp;
            }
        }
        if ($errsRespsForContract) {
            $errMsg .= "Error while $action -- ";
            foreach ($errsRespsForContract as $errResp) {
                if ($errResp->getContractLineNumber()) {
                    $errMsg .= 'Line ' . $errResp->getContractLineNumber() . ' ';
                }
                if ($errResp->getPostingDate()) {
                    $errMsg .= 'Posting date ' . $errResp->getPostingDate() . ' ';
                }
                $errMsg .= 'Message "' . $errResp->getExceptionMessage() . '". ';
            }
        }
    }

    /**
     * @param array                           $values
     * @param array                           $historicalContracts
     * @param ContractSchedulePostResponseACP $contractSchedulePostResponse
     *
     * @return int
     */
    public function sendCompletionEmail($values, $historicalContracts, $contractSchedulePostResponse)
    {
        $emailTo = $values['EMAIL'];
        $asOfDate = FormatDateForDisplay($values['ASOFDATE']);
        $fromEmail = "<EMAIL>";
        $replyTo = "<EMAIL>";
        $companyName = GetMyCompanyTitle();

        $bodyRunInfo = '';
        $bodyRunDetails = '';
        $bodyRunErrors = '';

        $emailToken = I18NEmailToken::buildFromResource("IA.EMAIL.CN.CONTRACT_ACP_RUN");

        $subject = $emailToken->applyPlaceholders("subject.text", [
            'RECORD_NO' => $values['RECORDNO'],
            'COMPANY_NAME' => $companyName,
            'ENTITY' => $values['ENTITY'],
            'AS_OF_DATE' => $asOfDate
        ]);

        $bodyStart = $emailToken->applyPlaceholders("body.start", [
            'RECORD_NO' => $values['RECORDNO'],
            'COMPANY_NAME' => $companyName,
            'ENTITY' => $values['ENTITY'],
            'AS_OF_DATE' => $asOfDate,
            'WHEN_CREATED' => FormatTimestampForDisplay($values['WHENCREATED'])
        ]);

        $billingErrorCount = count($contractSchedulePostResponse->getContractBillingSchedulePostErrorResponse());
        $revErrorCount = count($contractSchedulePostResponse->getContractRevSchedulePostErrorResponse());
        $expErrorCount = count($contractSchedulePostResponse->getContractExpSchedulePostErrorResponse());

        $bodyRunInfo .= $emailToken->applyPlaceholders("body.bsSuccessMsg", [
            'SUCCESS_COUNT' => $contractSchedulePostResponse->getTotalNumberOfBillingSchedulesPosted()
        ]);

        if ($billingErrorCount > 0) {
            $bodyRunInfo .= $emailToken->applyPlaceholders("body.bsErrorMsg", [
                'ERROR_COUNT' => $billingErrorCount
            ]);
        }

        $bodyRunInfo .= $emailToken->applyPlaceholders("body.rsSuccessMsg", [
            'SUCCESS_COUNT' => $contractSchedulePostResponse->getTotalNumberOfRevenueSchedulesPosted()
        ]);

        if ($revErrorCount > 0) {
            $bodyRunInfo .= $emailToken->applyPlaceholders("body.rsErrorMsg", [
                'ERROR_COUNT' => $revErrorCount
            ]);
        }

        $bodyRunInfo .= $emailToken->applyPlaceholders("body.esSuccessMsg", [
            'SUCCESS_COUNT' => $contractSchedulePostResponse->getTotalNumberOfExpenseSchedulesPosted()
        ]);

        if ($expErrorCount > 0) {
            $bodyRunInfo .= $emailToken->applyPlaceholders("body.esErrorMsg", [
                'ERROR_COUNT' => $expErrorCount
            ]);
        }

        $bodyRunDetails .= $emailToken->applyPlaceholders("body.tableHeader", []);

        foreach ($historicalContracts as $historicalContract) {
            $contractId = $historicalContract['CONTRACTID'];
            $bodyRunDetails .= $emailToken->applyPlaceholders("body.tableRow", [
                'CONTRACT_ID' => $contractId,
                'BILLING_COUNT' => $contractSchedulePostResponse->getNumberOfBillingSchedulesPosted($contractId),
                'REVENUE_COUNT' => $contractSchedulePostResponse->getNumberOfRevenueSchedulesPosted($contractId),
                'EXPENSE_COUNT' => $contractSchedulePostResponse->getNumberOfExpenseSchedulesPosted($contractId)
            ]);
        }
        $bodyRunDetails .= "</table><br/>";

        if ($billingErrorCount > 0) {
            $bodyRunErrors .= $emailToken->applyPlaceholders("body.bsErrorHeader", []);

            $contractBillingSchedulePostErrorResponseArray = $contractSchedulePostResponse->getContractBillingSchedulePostErrorResponse();

            foreach ($contractBillingSchedulePostErrorResponseArray as $contractSchedulePostErrorResponse) {
                $bodyRunErrors .= $emailToken->applyPlaceholders("body.bsErrorRow", [
                    'CONTRACT_ID' => $contractSchedulePostErrorResponse->getContractId(),
                    'POSTING_DATE' => $contractSchedulePostErrorResponse->getPostingDate(),
                    'EXCEPTION_MESSAGE' => $contractSchedulePostErrorResponse->getExceptionMessage()
                ]);
            }
        }

        if ($revErrorCount > 0) {
            $bodyRunErrors .= $emailToken->applyPlaceholders("body.rsErrorHeader", []);

            $contractRevSchedulePostErrorResponseArray = $contractSchedulePostResponse->getContractRevSchedulePostErrorResponse();

            foreach ($contractRevSchedulePostErrorResponseArray as $contractSchedulePostErrorResponse) {
                $bodyRunErrors .= $emailToken->applyPlaceholders("body.rsErrorRow", [
                    'CONTRACT_ID' => $contractSchedulePostErrorResponse->getContractId(),
                    'LINE_NO' => $contractSchedulePostErrorResponse->getContractLineNumber(),
                    'EXCEPTION_MESSAGE' => $contractSchedulePostErrorResponse->getExceptionMessage()
                ]);
            }
        }

        if ($expErrorCount > 0) {
            $bodyRunErrors .= $emailToken->applyPlaceholders("body.esErrorHeader", []);

            $contractExpSchedulePostErrorResponseArray = $contractSchedulePostResponse->getContractExpSchedulePostErrorResponse();

            foreach ($contractExpSchedulePostErrorResponseArray as $contractSchedulePostErrorResponse) {
                $bodyRunErrors .= $emailToken->applyPlaceholders("body.esErrorRow", [
                    'CONTRACT_ID' => $contractSchedulePostErrorResponse->getContractId(),
                    'LINE_NO' => $contractSchedulePostErrorResponse->getContractLineNumber(),
                    'EXCEPTION_MESSAGE' => $contractSchedulePostErrorResponse->getExceptionMessage()
                ]);
            }
        }

        $bodyEnd = $emailToken->applyPlaceholders("body.end", []);

        $message = $bodyStart . $bodyRunInfo . $bodyRunDetails . $bodyRunErrors .$bodyEnd;

        $email = I18NEmail::constructFromToken($emailToken, $emailTo);
        $email->contenttype = 'text/html; charset="' . isl_get_charset() . '"';
        $email->mime_headers = "Importance: high\n";
        $email->setFrom($fromEmail);
        $email->setReplyTo($replyTo);
        $email->setSubject($subject);
        $email->setBody($message);
        $ok = $email->send();

        if ( !$ok ) {
            Globals::$g->gErr->addIAError(
                'CN-0020',
                __FILE__.":".__LINE__ ,
                "Oops, '$emailTo' email address is invalid. <br />Enter a valid address, then try again.",
                ['EMAILTO' => $emailTo]
            );
        }
        return $ok;
    }
}
