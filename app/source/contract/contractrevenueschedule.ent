<?php
/**
 * File contractrevenueschedule.ent contains entity definition for contractrevenueschedule
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2015 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

$kSchemas['contractrevenueschedule'] = array(

    'object' => array(
        'RECORDNO',
        'TYPE',
        'CONTRACTKEY',
        'CONTRACTID',
        'CONTRACTDETAILKEY',
        'STATE',
        'CONTRACTDETAIL.TEMPLATENAME',
        'LINENO',
        'CONTRACTDETAILSTATE',
        'STARTDATE',
        'ENDDATE',
        'CANCELDATE',
        'CNDET<PERSON>ILG<PERSON>OSTINGDATE',
        'DELIVERYDATE',
        'REVENUEHOLDDATE',
        'REVENUERESUMEDATE',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
        'COMPLETEDATE',
        'BILLINGMETHOD'
    ),

    'schema' => array (
        'RECORDNO'              => 'record#',
        'TYPE'                  => 'type',
        'CONTRACTKEY'           => 'contractkey',
        'CONTRACTID'            => 'contract.contractid',
        'CONTRACTDETAILKEY'     => 'contractdetailkey',
        'STATE'                 => 'state',
        'CONTRACTDETAIL'        => array(
            'TEMPLATENAME' => 'template.name',
            'METHOD'       => 'template.method',
            'POSTINGTYPE'  => 'template.postingtype',
        ),
        'LINENO'                => 'contractdetail.lineno',
        'CONTRACTDETAILSTATE'   => 'contractdetail.state',
        'STARTDATE'             => 'contractdetail.revenuestartdate',
        'ENDDATE'               => 'contractdetail.revenueenddate',
        'CNDETAILGLPOSTINGDATE' => 'contractdetail.glpostingdate',
        'CANCELDATE'            => 'canceldate',
        'DELIVERYDATE'          => 'contractdetail.deliverydate',
        'REVENUEHOLDDATE'       => 'contractdetail.revenueholddate',
        'REVENUERESUMEDATE'     => 'contractdetail.revenueresumedate',
        'WHENMODIFIED'          => 'whenmodified',
        'WHENCREATED'           => 'whencreated',
        'CREATEDBY'             => 'createdby',
        'MODIFIEDBY'            => 'modifiedby',
        'REVENUETOTALQUANTITY'  => 'record#',
        'REMAININGUSAGE'        => 'record#',
        'COMPLETEDATE'          => 'completedate',
        'BILLINGMETHOD'         => 'contractdetail.billingmethod'
    ),


    'children' => array(
        'contract' => array (
            'fkey' => 'contractkey', 'invfkey' => 'record#',
            'table' => 'contract', 'join' => 'outer'
        ),
        'contractdetail' => array( 'fkey' => 'contractdetailkey', 'invfkey' => 'record#', 'join' => 'outer',
            'table' => 'contractdetail',
            'children' => array(
                'template' => array(
                    'fkey' => 'revenuetemplatekey',
                    'invfkey' => 'record#',
                    'table' => 'contractrevenuetemplate',
                    'join' => 'outer'
                )
            )
        ),
    ),

    'nexus' => array(
        'contract' => array (
            'object' => 'contract', 'relation' => MANY2ONE, 'field' => 'CONTRACTID'
        ),
        'contractdetail' => array(
            'object' => 'contractdetail', 'relation' => MANY2ONE, 'field' => 'CONTRACTDETAILKEY'
        ),
    ),

    'ownedobjects' => array (
        array (
            'fkey' => 'SCHEDULEKEY',
            'invfkey' => 'RECORDNO',
            'entity' => 'CONTRACTREVENUESCHEDULEENTRY',
            'path' => 'SCHEDULEENTRY'
        ),
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENCREATED',
        'WHENMODIFIED'
    ),

    'fieldinfo' => array (
        array(
            'id' => 1,
            'path' => 'RECORDNO',
            'desc' => 'IA.RECORD_NUMBER',
            'fullname' => 'IA.RECORD_NUMBER',
            'type' => array(
                'ptype' => 'sequence',
                'type' => 'integer',
                'maxlength' => 8,
                'size' => 8,
                'format' => $gRecordNoFormat,
            ),
            'readonly' => true,
        ),
        array(
            'id' => 2,
            'path' => 'TYPE',
            'fullname' => 'IA.SCHEDULE_TYPE',
            'type' => array (
                'ptype' => 'radio',
                'type' => 'text',
                'validlabels' => array('IA.REVENUE', 'IA.REVENUE2'),
                'validvalues' => array('Revenue', 'Revenue2'),
                '_validivalues' => array('R', 'R2'),
            ),
            'required' => false,
            'desc' => 'IA.SCHEDULE_TYPE',
        ),
        array(
            'id' => 3,
            'path' => 'CONTRACTKEY',
            'fullname' => 'IA.CONTRACT_KEY',
            'desc' => 'IA.CONTRACT_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
        ),
        array(
            'id' => 4,
            'path' => 'CONTRACTDETAILKEY',
            'fullname' => 'IA.CONTRACT_DETAIL_KEY',
            'desc' => 'IA.CONTRACT_DETAIL_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
        ),
        array (
            'id' => 5,
            'path' => 'CONTRACTDETAIL.TEMPLATENAME',
            'desc' => 'IA.REVENUE_TEMPLATE',
            'fullname' => 'IA.REVENUE_TEMPLATE',
            'type' => array (
                'ptype' => 'ptr',
                'type' => 'text',
                'entity' => 'contractrevenuetemplate',
                'pickentity' => 'contractrevenuetemplate',
            ),
        ),
        array(
            'id' => 6,
            'path' => 'STATE',
            'fullname' => 'IA.SCHEDULE_STATUS',
            'type' => array (
                'ptype' => 'enum',
                'type' => 'text',
                'validlabels' => array('IA.DRAFT', 'IA.IN_PROGRESS', 'IA.ON_HOLD', 'IA.COMPLETED', 'IA.TERMINATED', 'IA.PENDING_DELIVERY', 'IA.PENDING_DELIVERY_OF_ALL_ITEMS', 'IA.ESTIMATE_REVALUED', 'IA.RENEWAL_FORECAST'),
                'validvalues' => array('Draft', 'In progress', 'On hold', 'Completed', 'Terminated', 'Pending delivery', 'Pending delivery of all items', 'Estimate revalued', 'Renewal forecast'),
                '_validivalues' => array('D', 'I', 'H', 'C', 'T', 'P', 'A' ,ContractSchedule::STATE_ESITMATE_COMPLETED, ContractSchedule::STATE_RENEWAL_FORECAST),
            ),
            'default' => 'In progress',
            'required' => false,
            'desc' => 'IA.SCHEDULE_STATUS',
        ),
        array (
            'id' => 7,
            'path' => 'CONTRACTID',
            'renameable' => true,
            'desc' => 'IA.CONTRACT',
            'fullname' => 'IA.CONTRACT',
            'type' => array (
                'ptype' => 'ptr',
                'type' => 'text',
                'entity' => 'contract',
            ),
        ),
        array (
            'id' => 8,
            'path' => 'LINENO',
            'desc' => 'IA.CONTRACT_LINE_NO',
            'fullname' => 'IA.CONTRACT_LINE_NO',
            'type' => array (
                'ptype' => 'integer',
                'type' => 'integer',
            ),
        ),
        array (
            'id' => 9,
            'path' => 'STARTDATE',
            'desc' => 'IA.START_DATE',
            'fullname' => 'IA.START_DATE',
            'type' => array (
                'ptype' => 'date',
                'type' => 'date',
            ),
        ),
        array (
            'id' => 10,
            'path' => 'ENDDATE',
            'desc' => 'IA.END_DATE',
            'fullname' => 'IA.END_DATE',
            'type' => array (
                'ptype' => 'date',
                'type' => 'date',
            ),
        ),
        array(
            'path' => 'CANCELDATE',
            'desc' => 'IA.CANCELLATION_DATE',
            'fullname' => 'IA.CANCELLATION_DATE',
            'type' => $gDateType,
            'readonly' => true,
            'id' => 11
        ),
        array(
            'path' => 'REVENUETOTALQUANTITY',
            'fullname' => 'IA.TOTAL_REVENUE_QUANTITY',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 10,
                'size' => 8,
                'format' => $gDecimalFormat
            ),
            'readonly' => true,
            'hidden' => true,
            'id' => 12
        ),
        array(
            'path' => 'REMAININGUSAGE',
            'fullname' => 'IA.TOTAL_UNUSED_QUANTITY',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 10,
                'size' => 8,
                'format' => $gDecimalFormat
             ),
            'readonly' => true,
            'hidden' => true,
            'id' => 13
        ),
        array(
            'path' => 'DELIVERYDATE',
            'desc' => 'IA.DELIVERY_DATE',
            'fullname' => 'IA.DELIVERY_DATE',
            'type' => $gDateType,
            'id' => 14
        ),
        [
            'path' => 'REVENUEHOLDDATE',
            'fullname' => 'IA.HOLD_DATE',
            'type' => $gDateType,
            'readonly' => true,
            'required' => false,
            'desc' => 'IA.HOLD_DATE',
            'id' => 15
        ],
        [
            'path' => 'REVENUERESUMEDATE',
            'fullname' => 'IA.RESUME_DATE',
            'type' => $gDateType,
            'readonly' => true,
            'required' => false,
            'desc' => 'IA.RESUME_DATE',
            'id' => 16
        ],
        [
            'path' => 'CNDETAILGLPOSTINGDATE',
            'fullname' => 'IA.CONTRACT_LINE_GL_POSTING_DATE',
            'type' => $gDateType,
            'readonly' => true,
            'required' => false,
            'id' => 17
        ],

        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
        //UI-ONLY FIELDS
        array(
            'path'      => 'ASOFDATE',
            'fullname'  => 'IA.AS_OF_DATE_USED_FOR_LAST_UPDATE',
            'type'      => array(
                'type'      => 'text',
                'ptype'     => 'text'
            ),
            'hidden' => true,
        ),
        array(
            'path'      => 'PROJECTEST',
            'fullname'  => 'IA.PROJECT_ESTIMATED_HOURS',
            'type'      => array(
                'type'      => 'decimal',
                'ptype'     => 'decimal'
            ),
            'hidden' => true,
        ),
        array(
            'path'      => 'PROJECTOSBCOMPLT',
            'fullname'  => 'IA.PROJECT_OBSERVED_PERCENT_COMPLETE',
            'type'      => array(
                'type'      => 'decimal',
                'ptype'     => 'decimal'
            ),
            'hidden' => true,
        ),
        array(
            'path'      => 'PROJECTBDGT',
            'fullname'  => 'IA.PROJECT_BUDGETED_HOURS',
            'type'      => array(
                'type'      => 'decimal',
                'ptype'     => 'decimal'
            ),
            'hidden' => true,
        ),
            
        array(
            'path'      => 'PROJECTPLND',
            'fullname'  => 'IA.PROJECT_PLANNED_HOURS',
            'type'      => array(
                'type'      => 'decimal',
                'ptype'     => 'decimal'
            ),
            'hidden' => true,
        ),
        array(
            'path'      => 'TASKEST',
            'fullname'  => 'IA.TASK_ESTIMATED_HOURS',
            'type'      => array(
                'type'      => 'decimal',
                'ptype'     => 'decimal'
            ),
            'hidden' => true,
        ),
        array(
            'path'      => 'TASKOSBCOMPLT',
            'fullname'  => 'IA.TASK_OBSERVED_PERCENT_COMPLETE',
            'type'      => array(
                'type'      => 'decimal',
                'ptype'     => 'decimal'
            ),
            'hidden' => true,
        ),
        array(
            'path'      => 'TASKPLND',
            'fullname'  => 'IA.TASK_PLANNED_HOURS',
            'type'      => array(
                'type'      => 'decimal',
                'ptype'     => 'decimal'
            ),
            'hidden' => true,
        ),
        array(
            'path'      => 'LASTRUNCOMMENTS',
            'fullname'  => 'IA.UPDATE_PERCENTAGE_COMPLETE_SCHEDULES_RESULT',
            'type'      => array(
                'type'      => 'text',
                'ptype'     => 'text'
                ),
            'hidden' => true,
        ),
        array(
            'path'      => 'PROJECT',
            'fullname'  => 'IA.PROJECT',
            'type'      => array(
                'type'      => 'text',
                'ptype'     => 'text',
            ),
            'hidden' => true,
        ),
        array(
            'path'      => 'TASK',
            'fullname'  => 'IA.TASK',
            'type'      => array(
                'type'      => 'text',
                'ptype'     => 'text',
            ),
            'hidden' => true,
        ),
        array(
            'path'      => 'POSTINGTYPE',
            'fullname'  => 'IA.POSTING_TYPE',
            'type' => array (
                'ptype' => 'enum',
                'type' => 'text',
                'validlabels' => array('IA.AUTOMATIC', 'IA.MANUAL'),
                'validvalues' => array('Automatic', 'Manual'),
                '_validivalues' => array('A', 'M'),
            ),
            'readonly' => true,
        ),
        array(
            'path' => 'REALLOCSTARTDATE',
            'fullname' => 'IA.REALLOCATION_START_DATE',
            'desc' => 'IA.REALLOCATION_START_DATE',
            'type' => $gDateType,
        ),
        array(
            'path' => 'REALLOCENDDATE',
            'fullname' => 'IA.REALLOCATION_END_DATE',
            'desc' => 'IA.REALLOCATION_END_DATE',
            'type' => $gDateType,
        ),
        array(
            'path' => 'POSTPASTSCHEDULE',
            'fullname' => 'IA.POST_PRIOR_PERIOD_ENTRIES',
            'desc' => 'IA.POST_PRIOR_PERIOD_ENTRIES',
            'type' => $gBooleanType,
            'hidden' => true,
            'readonly' => true,
        ),
        array(
            'path' => 'COMPLETEDATE',
            'desc' => 'IA.ESTIMATE_REVALUATION_DATE',
            'fullname' => 'IA.ESTIMATE_REVALUATION_DATE',
            'type' => $gDateType,
            'readonly' => true,
            'hidden' => true
        ),
        array(
            'path' => 'BILLINGMETHOD',
            'fullname' => 'IA.BILLING_METHOD',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'text',
                'validlabels' => array('IA.FIXED_PRICE', 'IA.QUANTITY_BASED', 'IA.PROJECT_TIME_AND_MATERIALS' ,'IA.PROJECT_TIME','IA.PROJECT_MATERIALS'),
                'validvalues' => array('Fixed price', 'Quantity based', 'Project T&M' ,'Project time', 'Project materials'),
                '_validivalues' => array('F', 'Q', 'P' , 'T', 'M'),
            ),
            'default' => 'Fixed price',
            'desc' => 'IA.BILLING_METHOD',
            'hidden' => true,
        )
    ),


    'dbfilters' => array(
        array(
            'contractrevenueschedule.type', '=', 'R'
        )
    ),
    'api' => array(
        'ITEMS_ALIAS' => array('CONTRACTREVENUESCHEDULEENTRIES'),
        'ITEM_ALIAS' => array('CONTRACTREVENUESCHEDULEENTRY'),
        'ITEMS_INTERNAL' => array('SCHEDULEENTRY'),
        'PERMISSION_CREATE' => 'NONE',
        'PERMISSION_DELETE' => 'NONE',
        'LESS_GET_FIELDS' => ['BILLINGMETHOD']
    ),
    'module' => 'cn',
    'table' => 'contractschedule',
    'hasdimensions' => false,
    'auditcolumns' => true,
    'ignoredimensions' => array('contract'),
    'autoincrement' => 'RECORDNO',
    'vid' => 'RECORDNO',
    'nosysview' => true,
    
    'upsertEntries' => true,
    'printas' => 'IA.CONTRACT_REVENUE_SCHEDULE_1',
    'pluralprintas' => 'IA.CONTRACT_REVENUE_SCHEDULES_1',
    'platformProperties' => array(
        SOBJECT_CAN_HAVE_RELATIONSHIPS => false,
    ),
    'allowDDS' => true,
    'description' => 'IA.HEADER_INFORMATION_FOR_CONTRACT_REVENUE_SCHEDULES',
    'nameFields' => [ 'CONTRACTID', 'CONTRACTDETAILKEY' ],
);
