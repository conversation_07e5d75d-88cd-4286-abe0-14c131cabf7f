<?

/**
 * Entity for Evergreen Template
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 Sage Intacct Inc., All Rights Reserved
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

$kSchemas['evergreenmacro'] = [
    'children' => [
        'documentparams' => [
            'fkey' => 'salesdocrecordkey',
            'invfkey' => 'record#',
            'join' => 'outer',
            'table' => 'docpar'
        ],
    ],
    'object' => [
        'RECORDNO',
        'MACROID',
        'DESCRIPTION',
        'CREATESALESTRANS',
        'SALESDOCID',
        'SALESDOCRECORDKEY',
        'CREATESALESTRANSWHEN',
        'CREATESALESTRANSDAYS',
        'RENEWEDDOCDATE',
        'PRICINGTYPE',
        'PRICINGMARKUP',
        'PRICINGMARKUPVALUE',
        'EMAILTOCUSTOMER',
        'EMAILALERT',
        '<PERSON><PERSON><PERSON><PERSON><PERSON>TWH<PERSON>',
        'EMAILALERTDAYS',
        '<PERSON>MA<PERSON><PERSON><PERSON><PERSON><PERSON>ERWH<PERSON>',
        'EMAIL<PERSON>CU<PERSON>OMERDAYS',
        'CREATESALESOPP',
        'CREATESALESOPPWHEN',
        'CREATESALESOPPDAYS',
        'OPPCUSTOMNAME',
        'INHERITOPPPRODUCTS',
        'STAGEOFOPPORTUNITY',
        'LATESTVERSIONKEY',
        'STATUS',
        'TRANSACTIONTYPE',
        'TERMLENGTH',
        'TERMPERIOD',
        'CUSTOMEREMAILTEMPLATE',
        'ALERTEMAILTEMPLATE',
    ],
    'schema' => [
        'RECORDNO' => 'record#',
        'MACROID' => 'macroid',
        'DESCRIPTION' => 'description',
        'CREATESALESTRANS' => 'createsalestrans',
        'SALESDOCID' => 'documentparams.docid',
        'SALESDOCRECORDKEY' => 'salesdocrecordkey',
        'CREATESALESTRANSWHEN' => 'createsalestranswhen',
        'CREATESALESTRANSDAYS' => 'createsalestransdays',
        'RENEWEDDOCDATE' => 'reneweddocdate',
        'RENSTARTDATEOPT' => 'renstartdateopt',
        'PRICINGTYPE' => 'pricingtype',
        'PRICINGMARKUP' => 'pricingmarkup',
        'PRICINGMARKUPVALUE' => 'pricingmarkupvalue',
        'EMAILTOCUSTOMER' => 'emailtocustomer',
        'EMAILTOCUSTOMERFROMEMAILID' => 'emailtocustomerfromemailid',
        'EMAILCONTACT' => 'emailcontact',
        'EMAILALERTFROMEMAILID' => 'emailalertfromemailid',
        'EMAILCONTACTFILENAME' => 'emailcontactfilename',
        'EMAILALERT' => 'emailalert',
        'EMAILALERTFILENAME' => 'emailalertfilename',
        'EMAILALERTADDRESSES' => 'emailalertaddresses',
        'EMAILALERTWHEN' => 'emailalertwhen',
        'EMAILALERTDAYS' => 'emailalertdays',
        'EMAILTOCUSTOMERWHEN' => 'emailtocustomerwhen',
        'EMAILTOCUSTOMERDAYS' => 'emailtocustomerdays',
        'CREATESALESOPP' => 'createsalesopp',
        'CREATESALESOPPWHEN' => 'createsalesoppwhen',
        'CREATESALESOPPDAYS' => 'createsalesoppdays',
        'OPPCUSTOMNAME' => 'oppcustomname',
        'INHERITOPPPRODUCTS' => 'inheritoppproducts',
        'STAGEOFOPPORTUNITY' => 'stageofopportunity',
        'LATESTVERSIONKEY' => 'latestversionkey',
        'STATUS' => 'status',
        'TRANSACTIONTYPE' => 'transactiontype',
        'TERMLENGTH' => 'termlength',
        'TERMPERIOD' => 'termperiod',
        'CUSTOMEREMAILTEMPLATE' => 'customeremailtemplatekey',
        'ALERTEMAILTEMPLATE' => 'alertemailtemplatekey',
        'RENEWALSTATE' => 'renewalstate',
        'SYNCCONTRACT' => 'is_sync'
    ],
    'publish' => [
        'MACROID',
        'DESCRIPTION',
        'CREATESALESTRANS',
        'CREATESALESTRANSWHEN',
        'CREATESALESTRANSDAYS',
        'PRICINGTYPE',
        'PRICINGMARKUP',
        'PRICINGMARKUPVALUE',
        'EMAILTOCUSTOMER',
        'EMAILALERT',
        'EMAILALERTFILENAME',
        'EMAILALERTWHEN',
        'EMAILTOCUSTOMERWHEN',
        'CREATESALESOPP',
        'CREATESALESOPPWHEN',
        'CREATESALESOPPDAYS',
        'STAGEOFOPPORTUNITY',
        'STATUS',
        'TRANSACTIONTYPE',
        'TERMLENGTH',
        'TERMPERIOD',
        'CUSTOMEREMAILTEMPLATE',
        'ALERTEMAILTEMPLATE',
    ],
    'fieldinfo' => [
        [
            'path' => 'RECORDNO',
            'desc' => 'IA.RECORD_NUMBER',
            'fullname' => 'IA.RECORD_NUMBER',
            'hidden' => true,
            'type' => [
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'size' => 8,
                'format' => $gRecordNoFormat,
            ],
            'id' => 1
        ],
        [
            'fullname' => 'IA.EVERGREEN_TEMPLATE_ID',
            'desc' => 'IA.EVERGREEN_TEMPLATE_ID',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 30,
                'size' => 45,
                'format' => $gTemplateIDFormat
            ],
            'required' => true,
            'path' => 'MACROID',
            'id' => 2
        ],
        [
            'fullname' => 'IA.DESCRIPTION',
            'desc' => 'IA.DESCRIPTION',
            'type' => [
                'ptype' => 'textarea',
                'type' => 'textarea',
                'maxlength' => 100,
                'size' => 30,
            ],
            'numofrows' => 3,
            'numofcols' => 40,
            'required' => true,
            'path' => 'DESCRIPTION',
            'id' => 3
        ],
        [
            'path' => 'CREATESALESTRANS',
            'fullname' => 'IA.CREATE_SALES_TRANSACTION',
            'desc' => 'IA.CREATE_SALES_TRANSACTION',
            'type' => $gBooleanType,
            'default' => 'true',
            'id' => 4
        ],
        [
            'fullname' => 'IA.SALES_TRANSACTION_TYPE',
            'desc' => 'IA.SALES_TRANSACTION_TYPE',
            'disabled' => true,
            'hidden' => true,
            'path' => 'SALESDOCID',
            'id' => 5
        ],
        [
            'fullname' => 'IA.WHEN_CREATED_SALES_TRANSACTION',
            'hidelabel' => true,
            'desc' => '',
            'path' => 'CREATESALESTRANSWHEN',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 4,
            ],
            'default' => 10,
            'id' => 6
        ],
        [
            'fullname' => 'IA.AFTER_OR_BEFORE_FOR_SO_TRANSACTION',
            'hidelabel' => true,
            'desc' => '',
            'type' => [
                'ptype' => 'radio',
                'type' => 'radio',
                'validlabels' => ['IA.BEFORE', 'IA.AFTER'],
                'validvalues' => ['Before', 'After'],
                '_validivalues' => ['B', 'A'],
            ],
            'layout' => 'landscape',
            'required' => false,
            'default' => 'Before',
            'path' => 'CREATESALESTRANSDAYS',
            'id' => 8
        ],
        [
            'fullname' => 'IA.DATE_ON_RENEWED_DOCUMENT',
            'disabled' => true,
            'hidden' => true,
            'default' => 'E',
            'path' => 'RENEWEDDOCDATE',
            'id' => 9
        ],
        [
            'fullname' => 'IA.START_DATE_OF_RENEWED_CONTRACT',
            'disabled' => true,
            'hidden' => true,
            'default' => 'D',
            'path' => 'RENSTARTDATEOPT',
            'id' => 10
        ],
        [
            'fullname' => 'IA.PRICING_TYPE',
            'hidelabel' => true,
            'desc' => '',
            'type' => [
                'ptype' => 'radio',
                'type' => 'radio',
                'validlabels' => ['IA.SAME_AS_ORIGINAL', 'IA.SUGGESTED_PRICE', 'IA.MARKUP_MARKDOWN'],
                'validvalues' => ['Same as original', 'Default pricing', 'Markup / Markdown'],
                '_validivalues' => ['O', 'T', 'M'],
            ],
            'required' => false,
            'default' => 'Same as original',
            'path' => 'PRICINGTYPE',
            'onchange_js' => "javascript:ControlPricingMarkUpDown(this.value);",
            'id' => 13
        ],
        [
            'fullname' => 'IA.PRICING_MARKUP',
            'hidelabel' => true,
            'desc' => '',
            'type' => [
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => ['IA.PERCENTAGE_MARKUP', 'IA.PERCENTAGE_DISCOUNT', 'IA.ACTUAL_MARKUP', 'IA.ACTUAL_DISCOUNT'],
                'validvalues' => ['Percentage Markup', 'Percentage Discount', 'Actual Markup', 'Actual Discount'],
                '_validivalues' => ['PM', 'PD', 'AM', 'AD'],
            ],
            'required' => false,
            'default' => 'Percentage Markup',
            'onchange' => 'javascript:ControlPercentSign(this.value);',
            'path' => 'PRICINGMARKUP',
            'id' => 15
        ],
        [
            'fullname' => 'IA.PRICING_MARKUP_BY_VALUE',
            'hidelabel' => true,
            'desc' => '',
            'path' => 'PRICINGMARKUPVALUE',
            'type' => [
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 6,
                'size' => 10,
                'format' => $gDecimalFormat
            ],
            'id' => 17
        ],
        [
            'path' => 'EMAILTOCUSTOMER',
            'fullname' => 'IA.EMAIL_TO_CUSTOMER',
            'desc' => 'IA.EMAIL_TO_CUSTOMER',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 18
        ],
        [
            'fullname' => 'IA.EMAIL_ID_FOR_FROM_PART_OF_THE_EMAIL_WHICH_WILL',
            'disabled' => true,
            'hidden' => true,
            'path' => 'EMAILTOCUSTOMERFROMEMAILID',
            'id' => 19
        ],
        [
            'fullname' => 'IA.CONTACT',
            'disabled' => true,
            'hidden' => true,
            'default' => 'CC',
            'path' => 'EMAILCONTACT',
            'id' => 20
        ],
        [
            'fullname' => 'IA.FILE_UPLOADED_FOR_EMAIL_TEXT_FOR_EMAIL_ALERT',
            'hidelabel' => true,
            'desc' => '',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'size' => 100,
            ],
            'disabled' => true,
            'path' => 'EMAILCONTACTFILENAME',
            'id' => 22
        ],
        [
            'fullname' => 'IA.FILE_UPLOADED_FOR_EMAIL_TEXT_FOR_EMAIL_ALERT',
            'path' => 'EMAILCONTACTBLOB',
            'required' => false,
            'disabled' => true,
            'hidden' => true,
            'id' => 23
        ],
        [
            'fullname' => 'IA.FILE_UPLOADED_FOR_EMAIL_TEXT_FOR_EMAIL_ALERT',
            'hidelabel' => true,
            'desc' => '',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'size' => 100,
            ],
            'disabled' => true,
            'path' => 'EMAILALERTFILENAME',
            'id' => 24
        ],
        [
            'path' => 'EMAILALERTBLOB',
            'fullname' => 'IA.UPLOAD_TEMPLATE',
            'required' => false,
            'disabled' => true,
            'hidden' => true,
            'id' => 25
        ],
        [
            'path' => 'EMAILALERT',
            'fullname' => 'IA.ALERT_FOR_SALES_TEAM',
            'desc' => 'IA.ALERT_FOR_SALES_TEAM',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 27
        ],
        [
            'fullname' => 'IA.EMAIL_ID_FOR_FROM_PART_OF_ALERT_EMAIL',
            'disabled' => true,
            'hidden' => true,
            'path' => 'EMAILALERTFROMEMAILID',
            'id' => 28
        ],
        [
            'fullname' => 'IA.EMAIL_ADDRESSES_FOR_ALERTS',
            'hidelabel' => true,
            'disabled' => true,
            'hidden' => true,
            'path' => 'EMAILALERTADDRESSES',
            'id' => 29
        ],
        [
            'fullname' => 'IA.WHEN_TO_SEND_EMAIL_ALERT',
            'hidelabel' => true,
            'desc' => '',
            'path' => 'EMAILALERTWHEN',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 4,
            ],
            'default' => 1,
            'id' => 31
        ],
        [
            'fullname' => 'IA.AFTER_OR_BEFORE_FOR_SALES_TEAM_ALERT',
            'disabled' => true,
            'hidden' => true,
            'required' => false,
            'default' => 'A',
            'path' => 'EMAILALERTDAYS',
            'id' => 32
        ],
        [
            'path' => 'CREATESALESOPP',
            'fullname' => 'IA.CREATE_SALESFORCE_OPPORTUNITY',
            'desc' => 'IA.CREATE_SALESFORCE_OPPORTUNITY',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 33
        ],
        [
            'fullname' => 'IA.WHEN_CREATE_SALESFORCE_OPPORTUNITY',
            'hidelabel' => true,
            'desc' => '',
            'path' => 'CREATESALESOPPWHEN',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 4,
            ],
            'default' => 10,
            'id' => 34
        ],
        [
            'fullname' => 'IA.AFTER_OR_BEFORE_FOR_SALESFORCE',
            'hidelabel' => true,
            'desc' => '',
            'type' => [
                'ptype' => 'radio',
                'type' => 'radio',
                'validlabels' => ['IA.BEFORE', 'IA.AFTER'],
                'validvalues' => ['Before', 'After'],
                '_validivalues' => ['B', 'A'],
            ],
            'layout' => 'landscape',
            'required' => false,
            'default' => 'Before',
            'path' => 'CREATESALESOPPDAYS',
            'id' => 35
        ],
        [
            'fullname' => 'IA.RENEWAL_NAME',
            'desc' => 'IA.CUSTOMIZABLE_NAME_FOR_RENEWAL_SALES',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
            ],
            'path' => 'OPPCUSTOMNAME',
            'id' => 36
        ],
        [
            'fullname' => '',
            'desc' => '',
            'type' => [
                'ptype' => 'enum',
                'type' => 'enum',
            ],
            'required' => false,
            'path' => 'OPPCUSTOMNAMEONE',
            'id' => 37
        ],
        [
            'fullname' => '',
            'desc' => '',
            'type' => [
                'ptype' => 'enum',
                'type' => 'enum',
            ],
            'required' => false,
            'path' => 'OPPCUSTOMNAMETWO',
            'id' => 40
        ],
        [
            'fullname' => '',
            'desc' => '',
            'type' => [
                'ptype' => 'enum',
                'type' => 'enum',
            ],
            'required' => false,
            'path' => 'OPPCUSTOMNAMETHREE',
            'id' => 41
        ],
        [
            'path' => 'INHERITOPPPRODUCTS',
            'fullname' => 'IA.INHERIT_PRODUCTS_FROM_PARENT_SALES_OPPORTUNITY',
            'desc' => 'IA.INHERIT_PRODUCTS_FROM_PARENT_SALES_OPPORTUNITY',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 42
        ],
        [
            'fullname' => 'IA.STAGE_OF_OPPORTUNITY',
            'desc' => 'IA.STAGE_OF_OPPORTUNITY',
            'type' => [
                'ptype' => 'enum',
                'type' => 'enum',
            ],
            'layout' => 'portrait',
            'required' => false,
            'path' => 'STAGEOFOPPORTUNITY',
            'id' => 43
        ],
        [
            'path' => 'LATESTVERSIONKEY',
            'desc' => 'IA.LATEST_VERSION_KEY',
            'fullname' => 'IA.LATEST_VERSION_KEY',
            'hidden' => true,
            'type' => [
                'type' => 'decimal',
                'maxlength' => 10,
            ],
            'id' => 46
        ],

        $gStatusFieldInfo,

        ['path' => 'SALESDOCRECORDKEY', 'id' => 47],

        [
            'path' => 'TRANSACTIONTYPE',
            'fullname' => 'IA.TRANSACTION_TYPE',
            'type' => [
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => ['IA.SALES_TRANSACTION', 'IA.CONTRACT', 'IA.EVERGREEN'],
                'validvalues' => ['Sales Transaction', 'Contract', 'Evergreen'],
                '_validivalues' => ['SO', 'CN', 'EG'],
            ],
            'default' => 'Evergreen',
            'required' => true,
            'id' => 48
        ],
        [
            'path' => 'TERMLENGTH',
            'fullname' => 'IA.DEFAULT_RENEWAL_TERM_LENGTH',
            'hidelabel' => true,
            'hidden' => true,
            'type' => [
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 2,
                'format' => '/^[0-9]{1,2}$/',
            ],
            'id' => 49
        ],
        [
            'path' => 'TERMPERIOD',
            'fullname' => 'IA.DEFAULT_RENEWAL_TERM_PERIOD',
            'hidelabel' => true,
            'hidden' => true,
            'type' => [
                'ptype' => 'enum',
                'type' => 'text',
                'validvalues' => ['Years', 'Months', 'Days'],
                '_validivalues' => ['Y', 'M', 'D'],
                'validlabels' => ['IA.YEARS', 'IA.MONTHS', 'IA.DAYS'],
            ],
            'default' => 'Months',
            'id' => 50
        ],
        [
            'path' => 'CUSTOMEREMAILTEMPLATE',
            'fullname' => 'IA.CUSTOMER_EMAIL_TEMPLATE',
            'type' => [
                'ptype' => 'ptr',
                'type' => 'integer',
                'entity' => 'emailtemplate',
                'pickentity' => 'emailtemplate',
            ],
            'id' => 51
        ],
        [
            'path' => 'ALERTEMAILTEMPLATE',
            'fullname' => 'IA.ALERT_EMAIL_TEMPLATE',
            'type' => [
                'ptype' => 'ptr',
                'type' => 'integer',
                'entity' => 'emailtemplate',
                'pickentity' => 'emailtemplate',
            ],
            'id' => 52
        ],
        [
            'fullname' => 'IA.WHEN_TO_SEND_EMAIL_ALERT_TO_CUSTOMER',
            'hidelabel' => true,
            'desc' => '',
            'path' => 'EMAILTOCUSTOMERWHEN',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 4,
            ],
            'default' => '1',
            'id' => 53
        ],
        [
            'fullname' => 'IA.AFTER_OR_BEFORE_FOR_CUSTOMER_FOR_CONTRACT_RENEWAL',
            'disabled' => true,
            'hidden' => true,
            'default' => 'A',
            'path' => 'EMAILTOCUSTOMERDAYS',
            'id' => 54
        ],

        [
            'fullname' => 'IA.CREATE_RENEWAL_AS',
            'disabled' => true,
            'hidden' => true,
            'default' => 'P',
            'path' => 'RENEWALSTATE',
            'id' => 55
        ],
        [
            'path' => 'SYNCCONTRACT',
            'fullname' => 'IA.SYNCCONTRACT',
            'desc' => 'IA.SYNCCONTRACT',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 56
        ],
    ],
    'nexus' => [
        'documentparams' => [
            'object' => 'sodocumentparams',
            'relation' => MANY2ONE,
            'field' => 'salesdocrecordkey',
            'printas' => 'IA.SALES_ORDER_PARAMETERS'
        ],
    ],
    'table' => 'renewalmacro',
    'module' => 'cn',
    'dbfilters' => [['evergreenmacro.latestversionkey', 'ISNULL'], ['TRANSACTIONTYPE','=','Evergreen']],
    'vid' => 'RECORDNO',
    'autoincrement' => 'RECORDNO',
    'renameable' => true,
    'printas' => 'IA.EVERGREEN_TEMPLATE',
    'pluralprintas' => 'IA.EVERGREEN_TEMPLATES',
    'nosysview' => true,
    'nochatter' => true,
    'description' =>'IA.HEADER_INFORMATION_FOR_EVERGREEN_CONTRACT_RENEWAL',
    'nameFields' => [ 'MACROID' ],
];
