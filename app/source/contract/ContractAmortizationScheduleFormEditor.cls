<?php
/**
 * File ContractAmortizationScheduleFormEditor.cls contains the class ContractAmortizationScheduleFormEditor
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2015 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * Abstract base class for amortization schedule FormEditors
 *
 * Class ContractAmortizationScheduleFormEditor
 */
abstract class ContractAmortizationScheduleFormEditor extends ContractScheduleFormEditor
{
    protected const additionalTokens = [
        'IA.CLEAR',
        'IA.REALLOCATE',
    ];
    /** @var string $kReallocateRevRecAction */
    var $kReallocateRevRecAction = 'reallocate';
    /**
     * @param array $params the parameters of the class
     */
    public function __construct($params = array())
    {
        $this->additionalTokens = array_merge($this->additionalTokens, self::additionalTokens);
        parent::__construct($params);
        $this->kActionHandlers[$this->kReallocateRevRecAction] = array ( 'handler' => 'ProcessReallocateAction',
                                                                         'states' => array( $this->kShowEditState));
        $this->addClearAction();
    }

    /**
     * Builds dynamic meta-data updates
     *
     * @param array $params Array of form fields
     */
    protected function buildDynamicMetadata(&$params)
    {
        $verb = $this->getVerb();

        if ($verb == "view") {
            self::findAndSetMetadata($params, array('path' => 'GLBATCHLINK'), array('hidden' => false));
        }
    }

    /**
     * Sets derived data
     *
     * @param array $obj Array of form fields
     *
     * @return bool
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        $ok = parent::mediateDataAndMetadata($obj);
        if ( !$ok ) {
            return false;
        }

        $glb_op = GetOperationId('gl/lists/glbatch/view');

        $sess = Session::getKey();
        $verb = $this->getVerb();

        if ($this->getState() != $this->kShowEditState) {
            $this->getView()->findAndSetProperty(['path' => 'SELECTED'], ['hidden' => true]);
        } else {
            if ($obj['STATE'] === ContractScheduleState::STATE_TERMINATED || $obj['STATE'] === ContractScheduleState::STATE_COMPLETED) {
                $this->hideSaveButtonAndLockGrid($obj);
            }
        }

        foreach ($obj['SCHEDULEENTRY'] as $key => $schEntry) {

            if ($verb == "view" && $schEntry['GLBATCHKEY']) {
                $view_url = "editor.phtml?.op=$glb_op&.sess=$sess&.r=".$schEntry['GLBATCHKEY'].'&.popup=1';

                $obj['SCHEDULEENTRY'][$key]['GLBATCHLINK'] = '<a href=\'javascript:Launch( "' . $view_url
                    . '" , "glbatch");\' target1="_blank">'.$schEntry['GLBATCHNO'].'</a>';
            }

        }

        //To display the journal names on the revenue schedule pages.
        GetModulePreferences(Globals::$g->kCNid, $prefs);
        $view = $this->getView();
        $journalField = [];
        $view->findComponents(['path' => 'REVENUE1JOURNAL'], EditorComponentFactory::TYPE_FIELD, $journalField);
        if (isset($journalField[0])) {
            $journalField[0]->setProperty('default', $prefs['REVENUEJOURNAL1']);
        }
        $journalField = [];
        $view->findComponents(['path' => 'REVENUE2JOURNAL'], EditorComponentFactory::TYPE_FIELD, $journalField);
        if(isset($journalField[0])) {
            $journalField[0]->setProperty('default', $prefs['REVENUEJOURNAL2']);
        }
        return $ok;
    }

    /**
     * @param array $obj
     */
    protected function hideSaveButtonAndLockGrid(&$obj)
    {
        $view = $this->getView();
        $view->findAndSetProperty(['path' => 'SCHEDULEENTRY'], ['readonly' => true], EditorComponentFactory::TYPE_GRID);

        $obj['__HIDESAVE__'] = true;
    }

    /**
     * Add the 'Clear' action button handling
     */
    private function addClearAction()
    {
        $this->kActionHandlers['clear'] = array(
            'handler' => 'processClearAction',
            'states' => array(
                $this->kShowViewState,
                $this->kShowEditState,
            )
        );
        $this->kDefaultVerbActions['submit'] = $this->kSubmitAction;
        $this->kRequireVerbForAction['submit'] = $this->kSubmitAction;
    }

    /**
     * Get the editor button list
     *
     * @param string $state the editor state
     *
     * @return array the buttons list
     */
    public function getStandardButtons($state)
    {
        $buttons = array();

        if ( $state == $this->kShowEditState ) {
            if ($this->canClear()) {
                $this->setButtonDetails(
                    $buttons,
                    Editor_SubmitBtnID,
                    'submitbutton',
                    'IA.CLEAR',
                    'clear'
                );
            }
        }

        $buttons = INTACCTarray_merge(parent::getStandardButtons($state), $buttons);

        if ( $state == $this->kShowEditState ) {
            if ($this->canReallocate()) {
                $reallocateButton = array();
                $this->createMoreActionEntry(
                    $reallocateButton, 'reallocatebuttnid', 'reallocatebuttnid', 'IA.REALLOCATE', 'create', true, "openReallocate()");

                $buttons = INTACCTarray_merge($reallocateButton, $buttons);
            }
        }

        foreach($buttons as &$button) {
            if ($button['id'] === 'savebuttid') {
                $button['jsCode'] = 'BeforeScheduleSubmit()';
            }
            if ($button['id'] === 'cancelbuttonid') {
                $button['jsCode'] = 'checkonCancel()';
            }
        }
        return $buttons;
    }

    /**
     * Process the clear button action
     *
     * @param array $_params the metedata
     *
     * @return bool     false if error else true
     */
    protected function processClearAction(&$_params)
    {
        $ok = true;
        // TODO: transactions?
        $this->retrieveObjectFromView($_params, $obj);

        if (!isset($obj['BILLINGMETHOD']) || !ContractBillingMethodTypes::isProjectTime($obj['BILLINGMETHOD'])) {
            $scheduleEntryCount = count($obj['SCHEDULEENTRY']);
            //foreach ($obj['SCHEDULEENTRY'] as $entry) {
            for ($i = $scheduleEntryCount; $i >= 0; $i--) {

                $entry = $obj['SCHEDULEENTRY'][$i];
                if ($entry['SELECTED'] != 'true') {
                    continue;
                }

                // TODO: is schedulekey required?
                assert($entry['SCHEDULEKEY'], "Schedule key is missing");
                assert($entry['SCHEDULEENTRYKEY'], "Schedule entry key is missing");

                $schType = $this->getScheduleType();

                $contractObjHandler = new ContractObjHandler();
                $schedule = $contractObjHandler->getContractScheduleWithTemplate($entry['SCHEDULEKEY'], $schType);
                //$schedule = ContractScheduleFactory::createSchedule($schType, $entry['SCHEDULEKEY']);
                try {
                    if ($entry['TYPE'] == ContractSchedule::TYPE_EXPENSE || $entry['TYPE'] == ContractSchedule::TYPE_EXPENSE2) {
                        $ok = ContractMissingEventAssertionsUtil::assertByExpScheduleKey([$entry['SCHEDULEKEY']]);
                    } else {
                        $ok = ContractMissingEventAssertionsUtil::assertByScheduleKey([$entry['SCHEDULEKEY']]);
                    }

                    if (!$ok) {
                        break;
                    }
                    if (ContractUtil::isQuantityBasedMEAAdjustmentEnabled() && $entry['ADJUSTEDFOR'] == 'MEA-PostQ') {
                        //we do not allow unposting of the adjustment entries
                        $actualPostingDate = $entry['POSTINGDATE'];
                        throw IAException::newIAException('CN-0691',
                                                          "The revenue posted on $actualPostingDate is tied to the ".
                                                          "adjustment resulting from the MEA allocation effective " .
                                                          "$actualPostingDate. To clear this amount, clear the MEA " .
                                                          "allocation. See 'MEA allocations' in the online help for " .
                                                          "details.",
                                                          ['ACTUAL_POSTING_DATE' => FormatDateForDisplay($actualPostingDate)]);
                    } else {
                        $schedule->unpost($entry['SCHEDULEENTRYKEY']);
                    }
                } catch (IAException $ex) {
                    ContractUtil::addThrowableError($ex, __FILE__ . ':' . __LINE__);
                    break;
                }
            }
        }else{
            Globals::$g->gErr->addError('CN-0231', __FILE__ . ':' . __LINE__, "For contract line having billing method 'Project time' , clear revenue functionality isn’t applicable.");
        }
        
        //Reset the readtime so that the form picks the latest readtime.
        Request::$r->_readtimegmt = null;

        if ( Globals::$g->gErr->hasErrors() ) {
            $this->state = $this->kErrorState;
        } else {
            $this->ProcessViewAction($_params);
        }

        return $ok;
    }

    /**
     * @param array $_params
     */
    protected function ProcessReallocateAction(&$_params)
    {
        $ok = $this->retrieveObjectFromView($_params, $obj);
        if ( $ok ) {
            $scheduleKey = $obj['RECORDNO'];
            $reallocateData = $obj['REALLOCATE_PAGE'];

            $startDate = $reallocateData['REALLOCSTARTDATE'];
            $endDate = $reallocateData['REALLOCENDDATE'];

            $currentDate = GetCurrentDate();
            $hasPastStartDate = false;
            if ( DateCompare($currentDate,$startDate) > 0) {
                $hasPastStartDate = true;
            }
            $contractObjHandler = new ContractObjHandler();
            $schedule = $contractObjHandler->reAllocateSchedule($scheduleKey,$startDate,$endDate,$hasPastStartDate, $showWarning, false,$obj,true );
            $obj['SHOWWARNINGONREALLOCATE'] = $showWarning;
            $obj['HASNEWDATA'] = true;
            $obj[':REALLOCSTARTDATE'] = $startDate;
            $obj[':REALLOCENDDATE'] = $endDate;
            if ( $showWarning ) {
                $view = $this->getView();
                $journalField = [];
                $view->findComponents(['path' => 'POSTPASTSCHEDULE'], EditorComponentFactory::TYPE_FIELD, $journalField);
                if (isset($journalField[0])) {
                    //$journalField[0]->setProperty('hidden', false);
                }
            }
            //unset all the deleted entries
            $resultEntries = array();
            $adjustedEntries = $schedule->getContractScheduleInfo()->getEntries();
            foreach ( $adjustedEntries as $oneEntry ) {

                if ( $oneEntry->getStateChanged() !== ContractScheduleEntry::CONTRACT_SCHEDULE_ENTRY_DELETED_NO_UNPOST && $oneEntry->getStateChanged() !== ContractScheduleEntry::CONTRACT_SCHEDULE_ENTRY_DELETED ) {
                    //check here for respecting GL posting date
                    $contractObjHandler->respectGLPostingDate($oneEntry, $schedule->getType());
                    $emValues = $oneEntry->getValues();
                    $resultEntries[] = $emValues;
                }
            }
            $obj['SCHEDULEENTRY'] = $resultEntries;
            //$obj['STARTDATE'] = $startDate;
            $obj['ENDDATE'] = $endDate;
            //add remaining to the array
            Request::$r->SetCurrentObject($obj);
        }
    }

    /**
     * @return bool
     */
    protected function canClear()
    {
        return true;
    }

    /**
     * @return bool
     */
    protected function canReallocate()
    {
        return true;
    }
}