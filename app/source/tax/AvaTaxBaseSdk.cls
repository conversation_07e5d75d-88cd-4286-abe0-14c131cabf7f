<?php

/**
 * class for Avalara Tax Base methodes for SDK
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */
// location of the AvaTax.PHP Classes - Required
define('SOAP_AVASDK_BASEDIR', '../../private/lib/AvaTax4PHP');
require SOAP_AVASDK_BASEDIR .'/AvaTax.php';
importAvaClass('ATConfig');
importAvaClass('BaseResult');
importAvaClass('SeverityLevel');

/**
 * class for Avalara Tax Base for SDK
 */
class AvaTaxBaseSdk
{
    /** @var TaxServiceSoap $client */
    var $client;
    /** @var  array $config */
    var $config;

    /**
     * Current error string (manipulated by getError/setError)
     *
     * @var string $error_str
     * @access private
     */
    var $error_str = '';

    /**
     * function __construct
     *
     * @param array $config class arguments
     */
    public function __construct($config)
    {
        new ATConfig(
            'AvaTaxEnv', array(
                'url' => $config['ENVIRONMENT'],    //URL to the Development - AvaTax Web Service
                'account' => $config['USERNAME'],   //Enter your Development Account number here
                'license' => $config['PASSWORD'],   //Enter your Development License Key Here
                'trace'     => true,                // change to false for production
            )
        );
    }

    /**
     * function Ping
     *
     * @access public
     * @param  string|array $request request to Avalara
     * @param  PingResult &$result response from Avalara
     *
     * @return bool
     */
    public function Ping($request, &$result)
    {
        try
        {
            $result = $this->client->ping($request);
            $ret = true;
        }
        catch(SoapFault $exception)
        {
            //Set Exception as error message
            $msg = "Exception: ";
            /**
             * @var nusoap_fault $exception
             */
            $msg .= $exception->faultstring;
            $this->setErrorMsg($msg);
            $ret = false;
        }
        return $ret;
    }


    /**
     * returns error string if present
     *
     * @access public
     * @return mixed error string or false
     */
    function getErrorMsg()
    {
        if ($this->error_str != '') {
            return $this->error_str;
        }
        return false;
    }

    /**
     * sets error string
     *
     * @access private
     * @param  string $str error message to set
     */
    function setErrorMsg($str)
    {
        $this->error_str = $str;
    }

    /**
     * This function can be used to get last xml request sent to Avalara
     *
     * @return string
     */
    public function GetLastRequest()
    {
        $lastRequest = new SimpleXMLElement($this->client->__getLastRequest());
        //replace username & passwords with '*********' for logs
        //$lastRequest->Header->Security->UsernameToken->Username = '*********';
        //$lastRequest->Header->Security->UsernameToken->Password = '*********';

        //replace username & passwords with '*********' for logs
        $lastRequest->registerXPathNamespace('wwse', 'http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd');
        $lastRequest->xpath("//wwse:Username")[0][0] = '*********';
        $lastRequest->xpath("//wwse:Password")[0][0] = '*********';

        return $lastRequest->asXML();
    }
    /**
     * This function can be to get last xml response from Avalara
     *
     * @return string
     */
    public function GetLastResponse()
    {
        return $this->client->__getLastResponse();
    }
}

/**
 * Class AvaLogCallback used during saving the logs for request and response to Avalara for a transaction
 * commit callback function.
 */
class AvaLogCallback
{

    /** @var SyncEvent $logger */
    private $logger;

    /**
     * creates a new callback object that will save the logs from Avalara soap calls.
     *
     * @param SyncActivityLogger $logger the approval manager
     *
     * @return array
     */
    public static function createCallback($logger) 
    {
        $avaLogger = new AvaLogCallback($logger);
        return array($avaLogger, 'createAvaLog');
    }

    /**
     * @param SyncActivityLogger $logger SyncEvent::factory $logger the SyncEvent::factory
     */
    public function __construct($logger)
    {
        $this->logger = $logger;
    }

    /**
     * Save call to create the logs
     *
     * @return bool
     */
    public function createAvaLog() 
    {
        $ok = $this->logger->save();
        return $ok;
    }
}

