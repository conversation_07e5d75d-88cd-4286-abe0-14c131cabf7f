<?php
/**
 * POTaxSchedMapAllowedOperationsHandler
 *
 * <AUTHOR>
 * @copyright Copyright (C)2022 Sage Intacct Corporation, All Rights Reserved
 *
 */

class POTaxSchedMapAllowedOperationsHandler extends AllowedOperationsHandler
{
    /**
     * @inheritDoc
     */
     public function __construct(EntityManager $entManager)
     {
         parent::__construct($entManager);
     }

    /**
     * Record Level Check for Delete Operation
     *
     * @param array $record
     *
     * @return bool
     */
    protected function canDelete(array $record, string $moduleKey = null) : bool {
        $operationModuleKey = $moduleKey ?? $this->entManager->GetHomeModule();
        return $record['SYSGENERATED'] != "true" && $this->isAllowedOperation(AllowedOperations::CAN_DELETE, $operationModuleKey);
    }

    /**
     * Record Level Check for Edit Operation
     *
     * @param array $record
     *
     * @return bool
     */
    protected function canEdit(array $record) : bool {
        $operationModuleKey = $moduleKey ?? $this->entManager->GetHomeModule();
        return $record['SYSGENERATED'] != "true" && $this->isAllowedOperation(AllowedOperations::CAN_EDIT, $operationModuleKey);
    }

    /**
     * @inheritDoc
     */
     protected function getFieldsForPermissionChecks() : array
     {
         $fields = parent::getFieldsForPermissionChecks();
         $fields = array_merge($fields, ['SYSGENERATED']);

         return $fields;
     }
}
