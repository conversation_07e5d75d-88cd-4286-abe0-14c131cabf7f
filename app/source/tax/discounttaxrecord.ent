<?

//=============================================================================
//  In order to make taxrecord manager object oriented we had to add this ent
//
//	FILE:			discounttaxrecord.ent
//	AUTHOR:			Iris Zhou
//
//	(C)2024, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information herein
//	may not be used, copied or disclosed in whole or part without prior
//	written consent from Intacct Corporation.
//
//=============================================================================


require 'taxrecord.ent';
$kSchemas['discounttaxrecord'] = $kSchemas['taxrecord'];
$kSchemas['discounttaxrecord']['customComponentsEntity'] = 'taxrecord'; // smart events will trigger based on taxrecord entity
