<?
$kSchemas['taxscheddetails'] = array(

    'children'  => array (
        'schedule'  => array (
            'fkey' => 'schedulekey',
            'invfkey' => 'record#',
            'table' => 'taxschedule',
            'join' => 'outer',
        ),
        'detail'  => array (
            'fkey' => 'detailkey',
            'invfkey' => 'record#',
            'table' => 'taxdetail',
            'join' => 'outer',
        ),
    ),


    'schema' => array(
        'RECORDNO'      =>  'record#',
        'SCHEDULENO'    =>  'schedulekey',
        'DETAILID'      =>  'detail.detailid',
        'SCHEDULEID'    =>  'schedule.scheduleid',
        'EFFECTIVEDATE' =>  'effectivedate'
    ),


    'object' => array(
        'SCHEDULENO',
        'RECORDNO',
        'DETAILID',
        'SCHEDULEID',
        'EFFECTIVEDATE'
    ),


    'fieldinfo' => array(

        array (
            'fullname' => 'IA.RECORD_NUMBER',
            'type' => array (
                'ptype' => 'sequence',
                'type'  => 'text',
                'maxlength' => 8
            ),
            'readonly' => true,
            'hidden'   => true,
            'desc'     => 'IA.RECORD_NUMBER',
            'path'     => 'RECORDNO'
        ),


        array (
            'fullname' => 'IA.SCHEDULE_NUMBER',
            'type' => array ( 
                'ptype'     => 'integer', 
                'type'      => 'integer',
                'format'    => $gRecordNoFormat
            ),
            'desc' => 'IA.SCHEDULE_NUMBER',
            'path' => 'SCHEDULENO'
        ),


        array (
            'fullname' => 'IA.DETAIL_ID',
            'type' => array ( 
                'ptype'     => 'ptr', 
                'type'      => 'ptr',
                'entity'    => 'taxdetail',
            ),
            'desc' => 'IA.DETAIL_ID',
            'path' => 'DETAILID'
        ),

        array(
            'path'     => 'EFFECTIVEDATE',
            'fullname' => 'IA.EFFECTIVE_DATE',
            'type'     => array(
                'ptype'     => 'date',
                'type'      => 'date',
                'format'    => $gDateFormat,
            ),
        ),
    ),

    'parententity'  => 'taxschedule',
    'table'         => 'taxscheddetails',
    'printas'       => 'IA.TAX_SCHEDULE_DETAIL',
    'pluralprintas' => 'IA.TAX_SCHEDULE_DETAILS',
    'vid'           => 'RECORDNO',
    'module'        => 'co'
);
