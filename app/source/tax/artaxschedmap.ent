<?

/**
 *    FILE: artaxschedmap.ent
 *    AUTHOR: NaveenS
 *    DESCRIPTION:
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */


require 'taxschedmap.ent';
$kSchemas['artaxschedmap'] = $kSchemas['taxschedmap'];
$objects = array(
    'ITEMGROUPKEY',
    'ACCOUNTGROUPKEY',
    'ENTITYGROUPKEY',
    'TAXSCHEDKEY'
);
$schemas = array(
    'ITEMGROUPKEY'  =>    'itemgrpkey',
    'ACCOUNTGROUPKEY'  =>  'acctlabelgrpkey',
    'ENTITYGROUPKEY'  =>  'entgrpkey',
    'TAXSCHEDKEY'  =>  'schedkey'
);
$kSchemas['artaxschedmap']['object'] = array_merge($kSchemas['artaxschedmap']['object'], $objects);
$kSchemas['artaxschedmap']['schema'] = array_merge($kSchemas['artaxschedmap']['schema'], $schemas);
$kSchemas['artaxschedmap']['fieldinfo'][2]['required'] = false;
$kSchemas['artaxschedmap']['fieldinfo'][3]['required'] = false;
$kSchemas['artaxschedmap']['fieldinfo'][4]['required'] = false;
$kSchemas['artaxschedmap']['fieldinfo'][] = array (
    'path'        => 'MODULE',
    'fullname'    => 'IA.MODULE',
    'hidden' => true,
    'desc'        => 'IA.MODULE',
    'type'         => array (
        'type'          => 'enum',
        'ptype'         => 'enum',
        'validvalues'   => array ('Sales','AR'),
        '_validivalues' => array ('S','R'),
        'validlabels'   => array ('IA.SALES','IA.AR'),
    ),
    'default' => 'AR',
    'id' => 7,
);
$kSchemas['artaxschedmap']['dbfilters'] = array(array('artaxschedmap.module', '=', TaxSetupManager::isVATEnabled() ? 'S' : 'R'));
$kSchemas['artaxschedmap']['module'] = 'ar';
$kSchemas['artaxschedmap']['module_list'] = array('ar', 'so');
$kSchemas['artaxschedmap']['table'] = 'taxschedmap';
$kSchemas['artaxschedmap']['printas'] = 'IA.TAX_SCHEDULE_MAP';
$kSchemas['artaxschedmap']['pluralprintas'] = 'IA.TAX_SCHEDULE_MAPS';
$kSchemas['artaxschedmap']['nochatter'] = true;
$kSchemas['artaxschedmap']['description'] = 'IA.LIST_OF_THE_AR_TAX_SCHEDULE_MAPS';
$kSchemas['artaxschedmap']['nameFields'] = ['DESCRIPTION'];
