<?php
/**
 * Subject Area Admin Tool
 *  nexus structure fetcher
 *
 * <AUTHOR> <<EMAIL>>
 *            modified - <PERSON><PERSON><PERSON> <<EMAIL>>
 * @copyright 2017 Intacct Corporation, All Rights Reserved
 */

require_once "util.inc";
require_once "CustomERP.inc";
require_once "nexusutils.inc";
require_once "nexusobjectinfo.inc";

/**
 * @return array
 */
function nexus_build()
{
    //ini_set("include_path", ini_get("include_path") . ":../../link/private/inc:../../source/*:../../link/private/lib/log4php:../../link/private/lib/php4:.:../../link/private/lib/misc");

    $obieeInstance = OBIEEInstance::getInstance();
    $formerUserId = Globals::$g->_userid;

    // we need a cny context in order to deal with dimensions properly
    $cny = arrayExtractValue(GetValueForIACFGProperty("REPORTING"), "RPD_CNY");
    if ($cny) {
        Backend_Init::SetEnvironment($cny);
        SetDBSchema($cny, "");
    }
    if (!$cny or !GetMyCompanyTitle()) {
        dieFL("The given company, " . $cny . ", was not found in ia_init.cfg: REPORTING\\RPD_CNY.");
    }

    IADimensions::setForceAllStdDimensions(true);

    // let's initialize all global static objects we need for the system to function
    InitGlobalObjectVars();
    $allEntities = array();
    InitSchemas($allEntities);
    InitNexusObjectInfoOverrideObi();
    $gNoVIDObjects = array();
    InitNoVIDObjects($gNoVIDObjects);

    $kNexusAll = array();
    $allEntities = getAllEntities($allEntities, false);
    mapAllNexus($kNexusAll, $allEntities);

    $allEntities = getAllEntities($allEntities, false);
    $kNexusObjectInfo = mapNexusObjectInfoObi($gNoVIDObjects, $allEntities);

    // remove platform objects
    // TODO: om - do we need this? the company for RPD/OBI should be "clean", so no platform objects...
    $objects = array();
    foreach ($kNexusAll as $name => $children) {
        if (!count($children)) {
            $objects[$name] = array();
        } else {
            foreach ($children as $key => $object) {
                if (!isset($object["is_platform"])) {
                    $objects[$name][$key] = $object;
                }
            }
        }
    }
    $kNexusAll = $objects;

    Globals::$g->_userid = $formerUserId;

    if ( $obieeInstance ) {
        OBIEEInstance::destroyInstance();
        OBIEEInstance::init($obieeInstance->getInstanceId());
    }

    return [$kNexusAll, $kNexusObjectInfo];
}

/**
 * Extracts info about nexus object
 *
 * @return array
 */
function mapNexusObjectInfoObi($gNoVIDObjects, $allEntities)
{
    $arr = [];
    global $kSchemas, $kOverrideNexusObjectInfo;

    // find shorts in all entities
    foreach ($allEntities as $entity) {
        // unexpected
        if (!isset($kSchemas[$entity])) {
            continue;
        }

        // map nexus structures by owner object
        $thisEntity =& $kSchemas[$entity];
        $myEntity = isl_strtoupper($entity);

        $printas = $kOverrideNexusObjectInfo[isl_strtoupper($entity)]["printas"] ?? ($thisEntity["printas"] ?? "");
        if ($printas === "") {
            $printas = isl_ucwords($entity);
        }
        $vid = isl_strtoupper($thisEntity["vid"] ?? "");
        if (in_array(isl_strtoupper($entity), $gNoVIDObjects)) {
            $vid = null;
        }
        $arr[$myEntity] = [
            "module" => $thisEntity["module"] ?? "",
            "vid" => $vid,
            "allowDDS" => $thisEntity["allowDDS"] ?? false,
            "printas" => $printas,
            "hasdimensions" => $thisEntity["hasdimensions"] ?? false,
            "followgldimensions" => $thisEntity["followgldimensions"] ?? false,
            "hideOBI" => $thisEntity["hideOBI"] ?? false,
            "description" => $thisEntity["description"] ?? "",
        ];
        if (isset($thisEntity["ignoredimensions"]) and is_array($thisEntity["ignoredimensions"])) {
            $arr[strtoupper($entity)]["ignoredimensions"] = $thisEntity["ignoredimensions"];
        }
    }
    return $arr;
}

/**
 * NexusObjectInfo override struct
 */
function InitNexusObjectInfoOverrideObi()
{
    global $kOverrideNexusObjectInfo;
    $kOverrideNexusObjectInfo = [
        "ALLOCATION" => ["printas" => "IA.ALLOCATION"],
        /*"APBILLITEM" => array("printas" => "Bill Detail"),*/
        "APPAYMENTREQUEST" => ["printas" => "IA.AP_PAYMENT_REQUEST"],
        "APADJUSTMENT" => ["printas" => "IA.AP_ADJUSTMENT"],
        "APADJUSTMENTITEM" => ["printas" => "IA.AP_ADJUSTMENT_DETAIL"],
        "APPAYMENT" => ["printas" => "IA.AP_PAYMENT"],
        "ARINVOICE" => ["printas" => "IA.INVOICE"],
        "ARADJUSTMENTITEM" => ["printas" => "IA.AR_ADJUSTMENT_DETAIL"],
        "ARINVOICEITEM" => ["printas" => "IA.INVOICE_DETAIL"],
        "ARPAYMENT" => ["printas" => "IA.AR_PAYMENT"],
        "MYEXPENSES" => ["printas" => "IA.EMPLOYEE_EXPENSE"],
        "MYEXPENSESITEM" => ["printas" => "IA.EMPLOYEE_EXPENSE_DETAIL"],
        "CONTACT" => ["printas" => "IA.CONTACT"],
        "CUSTOMER" => ["printas" => "IA.CUSTOMER"],
        "DEPARTMENT" => ["printas" => "IA.DEPARTMENT"],
        "EMPLOYEE" => ["printas" => "IA.EMPLOYEE"],
        "GLBATCH" => ["printas" => "IA.GL_TRANSACTION"],
        "ITEM" => ["printas" => "IA.ITEM"],
        "JOURNAL" => ["printas" => "IA.JOURNAL"],
        "LOCATION" => ["printas" => "IA.LOCATION"],
        "PODOCUMENTPARAMS" => ["printas" => "IA.PURCHASE_ORDER_TRANSACTION_DEFINITION"],
        "PODOCUMENT" => ["printas" => "IA.PURCHASE_ORDER_TRANSACTION"],
        "PODOCUMENTENTRY" => ["printas" => "IA.PURCHASE_ORDER_TRANSACTION_DETAIL"],
        "SODOCUMENTPARAMS" => ["printas" => "IA.ORDER_ENTRY_TRANSACTION_DEFINITION"],
        "SODOCUMENT" => ["printas" => "IA.ORDER_ENTRY_TRANSACTION"],
        "SODOCUMENTENTRY" => ["printas" => "IA.ORDER_ENTRY_TRANSACTION_DETAIL"],
        "INVDOCUMENTPARAMS" => ["printas" => "IA.INVENTORY_CONTROL_TRANSACTION_DEFINITION"],
        "INVDOCUMENT" => ["printas" => "IA.INVENTORY_CONTROL_TRANSACTION"],
        "INVDOCUMENTENTRY" => ["printas" => "IA.INVENTORY_CONTROL_TRANSACTION_DETAIL"],
        "TERM" => ["printas" => "IA.TERM"],
        "TERRITORY" => ["printas" => "IA.TERRITORY"],
        "USERINFO" => ["printas" => "IA.USER"],
        "VENDOR" => ["printas" => "IA.VENDOR"],
        "WAREHOUSE" => ["printas" => "IA.WAREHOUSE"],
        "TAXGROUP" => ["printas" => "IA.TAX_GROUP"],
        "RECURGLBATCH" => ["printas" => "IA.RECURRING_GL_TRANSACTION"],
        "RECURGLENTRY" => ["printas" => "IA.RECURRING_GL_ENTRY"],
    ];
}