<?php
/**
 * DESCRIPTION: Class used for SSO IdP
 *
 * @file      SSOIdP.cls
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Intacct Corporation, All Rights Reserved
 */

if (!defined('INTACCTCONTEXT')) {
    // Don't need seclib while running make
    require_once '../../private/lib/robrichards/autoload.php';
}

use <PERSON><PERSON><PERSON><PERSON>\XMLSecLibs\XMLSecurityDSig as XMLSecurityDSig;
use <PERSON><PERSON><PERSON><PERSON>\XMLSecLibs\XMLSecurityKey as XMLSecurityKey;

/**
 * Class SSOIdP - used for SSO IdP
 */
class SSOIdP
{
    const USER_ID = "uid";
    const GROUPS = "Groups";
    const CRW_SESSION_COOKIE = 'ia_crws';
    const IVE_SESSION_COOKIE = 'ia_ive';

    /**
     * @var string $samlId ID extracted from including AuthRequest
     */
    private $samlId;
    /**
     * @var string $spEntityId The entity ID extracted from the AuthRequest
     */
    private $spEntityId;
    /**
     * @var DOMNode $authRequestNode DOM AuthRequest node
     */
    private $authRequestNode;
    /**
     * @var DOMNode $rootNode DOM Root node
     */
    private $rootNode;
    /**
     * @var XMLSecurityDSig $secSig xml security signature
     */
    private $secSig;
    /**
     * @var string|null $referenceId Generated reference id
     */
    private $referenceId;
    /**
     * @var array $attributes
     */
    private $attributes = [];
    /**
     * @var OBIEEInstance $obieeInstance the object providing the obiee instance configuration
     */
    private $obieeInstance;

    /**
     * @var array|bool $reportingConfig the reporting configuration from ia_init.cfg
     */
    private $reportingConfig;

    function __construct()
    {
        $this->reportingConfig = GetValueForIACFGProperty('REPORTING');
    }

    public function initAttributes()
    {
        $attributes[self::USER_ID] = OBIEEInstance::getOBIEEUserId();
        // Here we only pass in the group that gives the user access to analytics EAR. The rest of the groups will be
        // provided by LDAPQuery
        $attributes[self::GROUPS] = [ LDAPQuery::BI_EAR_ACCESS_GROUP, LDAPQuery::BI_RO_GROUP, ];
        $this->attributes = $attributes;
    }

    /**
     * Parses the SAML request
     *
     * @param string $samlRequest base64 encoded zipped string
     */
    public function parseAuthRequest($samlRequest)
    {
        $request = base64_decode($samlRequest);
        if (!is_string($request)) {
            // we shouldn't actually get to this ever
            dieFL("SSOIdP: invalid samlRequest");
        } else {
            $samlRequest = $request;
        }
        $samlRequest = gzinflate($samlRequest);

        $document = new DOMDocument();
        if ($samlRequest === false) {
            LogToFile("SSOIdP: Invalid samlRequest data");
            Fwd("login.phtml", "frameset.phtml");
        }
        $document->loadXML($samlRequest);

        $xpath = new DOMXPath($document);
        $xpath->registerNamespace('samlp', 'urn:oasis:names:tc:SAML:2.0:protocol');
        $xpath->registerNamespace('ds', 'http://www.w3.org/2000/09/xmldsig#');

        $signatureQuery = '/samlp:AuthnRequest';
        $this->authRequestNode = $xpath->query($signatureQuery)->item(0);
        if (!$this->authRequestNode) {
            LogToFile("SSOIdP: Can't find AuthnRequest node");
            Fwd("login.phtml", "frameset.phtml");
        }
        $this->spEntityId = $this->authRequestNode->textContent;
        $this->samlId = $this->authRequestNode->attributes->getNamedItem('ID')->nodeValue;
        $instanceId = OBIEEInstance::getInstanceIdForIssuer($this->spEntityId);
        if ($instanceId > 0) {
            OBIEEInstance::destroyInstance(); // destroys the default instance
            OBIEEInstance::init($instanceId); // sets up the needed one
        }
        $this->obieeInstance = OBIEEInstance::getInstance();
    }

    /**
     * Returns the consumer URL (the URL which takes the SAML Response as parameter)
     *
     * @return string
     */
    public function getConsumerURL()
    {
        return $this->obieeInstance->getTopDomainURL().OBIEEInstance::SAML2_URL;
    }

    /**
     * Returns the Service Provider URL (the URL which takes us to the service provider, aka OBIEE)
     *
     * @return string
     */
    public function getSPHome()
    {
        return $this->obieeInstance->getURL();
    }

    /**
     * Returns the Service Provider's entity ID (extracted from the SAML Request)
     *
     * @return string
     */
    public function getSPEntityId()
    {
        return $this->spEntityId;
    }

    /**
     * Retrieves the X509 certificate from the configuration file
     *
     * This is used to sign the SAML response
     *
     * @return null|string
     */
    private function getX509Certificate()
    {
        $cert = arrayExtractValue($this->reportingConfig, 'SSO_CERT');
        if ( $cert ) {
            return ia_cfg::getCfgDir() . $cert;
        } else {
            return null;
        }
    }

    /**
     * Retrieves the IdP Issuer id
     *
     * It is used in the SAML Response, so that the SP knows who the IdP is
     *
     * @return string the IdP issuer id
     */
    private function getIssuer()
    {
        return arrayExtractValue($this->reportingConfig, 'SSO_ISSUER');
    }

    /**
     * Retrieves the IdP certificate private key
     *
     * We use this to sign the SAML Response
     *
     * @return null|string
     */
    private function getPrivateKey()
    {
        $key = arrayExtractValue($this->reportingConfig, 'SSO_PRIVATE_KEY');
        if ( $key ) {
            return ia_cfg::getCfgDir() . $key;
        } else {
            return null;
        }
    }

    /**
     * Retrieves the IdP private key password
     *
     * @return null|string
     */
    private function getPrivateKeyPassword()
    {
        $pwd = arrayExtractValue($this->reportingConfig, 'SSO_PRIVATE_KEY_PWD');
        if ( $pwd ) {
            $pwd = TwoWayDecryptWithKey($pwd, 'IA_INIT');
        }
        return $pwd;
    }
    /**
     * Returns the name of federated ID attribute we add in the SAML Response
     *
     * We need this if the user is known to the providers in multiple disguises
     *
     * @return string
     */
    private function getFederatedId()
    {
        return $this->attributes[self::USER_ID];
    }

    /**
     * Returns the signature of the SAML Response
     *
     * @return XMLSecurityDSig
     */
    private function createSignature()
    {
        $keyParams['type'] = 'private';
        $secKey = new XMLSecurityKey(XMLSecurityKey::RSA_SHA1, $keyParams);
        $secKey->passphrase = $this->getPrivateKeyPassword();
        $secKey->loadKey($this->getPrivateKey(), true, false);

        $this->secSig = new XMLSecurityDSig();

        $this->secSig->setCanonicalMethod(XMLSecurityDSig::EXC_C14N_COMMENTS);
        $transforms[] = "http://www.w3.org/2000/09/xmldsig#enveloped-signature";
        $transforms[] = "http://www.w3.org/2001/10/xml-exc-c14n#";
        $options['id_name'] = 'ID';
        /** @noinspection PhpParamsInspection - https://github.com/robrichards/xmlseclibs/issues/172 */
        $this->secSig->addReference($this->rootNode, XMLSecurityDSig::SHA1, $transforms, $options);
        //  Add the namespaces so that the signature will generate with them (these namespaces must
        //   be present for later verification to work).
        $namespaces[] = array(
            'URI' => 'http://www.w3.org/2000/xmlns/', 'NAME' => 'xmlns:samlp',
            'VALUE' => 'urn:oasis:names:tc:SAML:2.0:protocol',
        );
        $namespaces[] = array(
            'URI' => 'http://www.w3.org/2000/xmlns/', 'NAME' => 'xmlns:saml',
            'VALUE' => 'urn:oasis:names:tc:SAML:2.0:assertion',
        );
        $this->secSig->sign($secKey, null);
        $this->secSig->add509Cert($this->getX509Certificate(), true, true);
        $this->secSig->canonicalizeSignedInfo();

        //  Remember the reference id.
        $refs = $this->secSig->getRefIDs();
        //the verification does not work. The xmlseclibs.php we use is modified and buggy! TODO: use a good version.
        //        $objKey = $this->secSig->locateKey();
        //        $objKey->loadKey($this->getX509Certificate(), true, true);
        //        if (!$this->secSig->verify($objKey)) {
        //            LogToFile("Signature not verified.");
        //            exit(1);
        //        }
        $this->referenceId = $refs[0];

        return $this->secSig;
    }

    /**
     * Returns the issuer of the SAML Response in canonical form
     *
     * @return string
     */
    private function canonicalizeIssuer()
    {
        return '<saml:Issuer>' . $this->getIssuer() . '</saml:Issuer>';
    }

    /**
     * Returns the status of the SAML Response in canonical form
     *
     * @return string
     */
    private function canonicalizeStatus()
    {
        return '<samlp:Status><samlp:StatusCode '.
            'Value="urn:oasis:names:tc:SAML:2.0:status:Success"/></samlp:Status>';
    }

    /**
     * Returns the start of the SAML Response in canonical form
     *
     * @return string
     */
    private function canonicalizeResponseStart()
    {
        return '<samlp:Response Destination="'.$this->getConsumerURL()
            .'" IssueInstant="'.gmdate('Y-m-d\TH:i:s\Z')
            .'" Version="2.0" ID="'.XMLSecurityDSig::generate_GUID()
            .'" xmlns:samlp="urn:oasis:names:tc:SAML:2.0:protocol" '
            .'xmlns:saml="urn:oasis:names:tc:SAML:2.0:assertion" '
            .($this->samlId ? 'InResponseTo="'.$this->samlId.'"' : "").'>';
    }

    /**
     * Returns the end of the SAML Response in canonical form
     *
     * @return string
     */
    private function canonicalizeResponseEnd()
    {
        return '</samlp:Response>';
    }

    /**
     * Returns the assertion start of the SAML Response in canonical form
     *
     * @return string
     */
    private function canonicalizeAssertionStart()
    {
        return '<saml:Assertion xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" '
            .'xmlns:xs="http://www.w3.org/2001/XMLSchema" '
            .'IssueInstant="'.gmdate('Y-m-d\TH:i:s\Z').'" '
            .'Version="2.0" ID="'.XMLSecurityDSig::generate_GUID().'"  >';
    }

    /**
     * Returns the assertion end of the SAML Response in canonical form
     *
     * @return string
     */
    private function canonicalizeAssertionEnd()
    {
        return '</saml:Assertion>';
    }

    /**
     * Returns the attributes of the SAML Response in canonical form
     *
     * For now, we add uid (federated ID) and groups without values, since the authorization is done in OBIEE
     *
     * @return string
     */
    public function canonicalizeAttributes()
    {
        $attrs = [self::USER_ID, self::GROUPS];
        $ret = '<saml:AttributeStatement>';
        foreach ($attrs as $nextAttr) {
            $ret .= '<saml:Attribute ';
            $format = 'urn:oasis:names:tc:SAML:2.0:attrname-format:basic';
            $ret .= 'NameFormat="'.$format.'" ';

            $ret .= 'Name="'.$nextAttr.'">';
            static $valuePrefix = '<saml:AttributeValue>';
            static $valueSuffix = '</saml:AttributeValue>';
            $value = $this->attributes[$nextAttr];
            if (is_array($value)) {
                $ret .= $valuePrefix.
                    implode($valueSuffix.$valuePrefix, $value).$valueSuffix;
            } else {
                $ret .= $valuePrefix . $value . $valueSuffix;
            }
            $ret .= '</saml:Attribute>';
        }
        $ret .= '</saml:AttributeStatement>';

        return $ret;
    }

    /**
     * Returns the subject of the SAML Response in canonical form
     *
     * Most importantly, this contains the NameID which identifies the user to the Service Provider
     *
     * @return string
     */
    public function canonicalizeSubject()
    {
        $fedId = $this->getFederatedId() . '<!-- Yay! -->';
        $ret = '<saml:Subject>
         <saml:NameID Format="urn:oasis:names:tc:SAML:2.0:nameid-format:persistent">'.$fedId.'</saml:NameID>
         <saml:SubjectConfirmation Method="urn:oasis:names:tc:SAML:2.0:cm:bearer">
         <saml:SubjectConfirmationData NotBefore="'.gmdate("Y-m-d\TH:i:s\Z")
            .'" NotOnOrAfter="'.gmdate("Y-m-d\TH:i:s\Z", time() + 330)
            .'" Recipient="'.$this->getConsumerURL().'" '
            .($this->samlId ? 'InResponseTo="'.$this->samlId.'"' : "").'/>
         </saml:SubjectConfirmation>
         </saml:Subject>';

        return $ret;
    }

    /**
     * Returns the subject of the SAML Response in canonical form
     *
     * These restrict the conditions in which the assertion is valid (time and SP entity ID)
     *
     * @return string
     */
    public function canonicalizeConditions()
    {
        $ret = '<saml:Conditions'
            .' NotBefore="'.gmdate("Y-m-d\TH:i:s\Z").'"'
            .' NotOnOrAfter="'.gmdate("Y-m-d\TH:i:s\Z", time() + 330).'">'
            .'<saml:AudienceRestriction>'
            .'<saml:Audience>'.$this->getSPEntityId().'</saml:Audience>'
            .'</saml:AudienceRestriction>'
            .'</saml:Conditions>';

        return $ret;
    }

    /**
     * Does a post with redirect
     *
     * Used for submitting the SAML Response to the SP
     *
     * @param string   $url  the target URL
     * @param string[] $data the data to be posted in name - value format
     */
    public function redirectPost($url, $data)
    {
        ?>
        <html xmlns="http://www.w3.org/1999/xhtml">
        <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>
    <head>
        <script>
        function closethisasap() {
            document.forms["redirectpost"].submit();
        }
        </script>
    </head>
    <body onload="closethisasap();">
        <form name="redirectpost" method="post" action="<?=$url;?>" >
        <?
        if (!is_null($data)) {
            foreach ($data as $k => $v) {
                echo '<input type="hidden" name="'.$k.'" value="'.$v.'"/>';
            }
        }
        ?>
        </form>
        </body>
        </html>
        <?
        exit;
    }

    /**
     * Creates the SAML Response for the SP.
     *
     * @return string
     */
    public function createResponse()
    {
        $response = $this->canonicalizeResponseStart();
        $response .= $this->canonicalizeIssuer();
        $response .= $this->canonicalizeStatus();
        $response .= $this->canonicalizeAssertionStart();
        $response .= $this->canonicalizeIssuer();
        $response .= $this->canonicalizeSubject();
        $response .= $this->canonicalizeConditions();
        $response .= $this->canonicalizeAttributes();
        $response .= $this->canonicalizeAssertionEnd();
        $response .= $this->canonicalizeResponseEnd();

        /*
         This code doesn't produce the AuthnStatement element which defines the length of the session

        <saml:AuthnStatement AuthnInstant="2017-02-16T23:57:54Z"
                             SessionNotOnOrAfter="2017-02-17T07:57:54Z"
                             SessionIndex="_e01022e3783fc1df1610ec423df9d55172831f8d4c">
            <saml:AuthnContext>
                <saml:AuthnContextClassRef>urn:oasis:names:tc:SAML:2.0:ac:classes:Password</saml:AuthnContextClassRef>
            </saml:AuthnContext>
        </saml:AuthnStatement>

         */
        $document = new DOMDocument();
        $document->loadXML($response);

        $xpath = new DOMXPath($document);
        $xpath->registerNamespace('samlp', 'urn:oasis:names:tc:SAML:2.0:protocol');
        $xpath->registerNamespace('saml', 'urn:oasis:names:tc:SAML:2.0:assertion');
        $xpath->registerNamespace('ds', 'http://www.w3.org/2000/09/xmldsig#');
        $xpath->registerNamespace('xsi', "http://www.w3.org/2001/XMLSchema-instance");
        $xpath->registerNamespace('xs', "http://www.w3.org/2001/XMLSchema");

        $this->rootNode = $xpath->query('/*')->item(0);
        if (!$this->rootNode) {
            LogToFile("Can't find root node");
            exit(1);
        }

        $statusNode = $xpath->query('samlp:Status')->item(0);
        $sigNode = $this->createSignature();
        $sigNode->insertSignature($this->rootNode, $statusNode);

        $response = $document->C14N(true, true);

        return $response;
    }

    /**
     * Returns the Reference ID from the SAML Request.
     * <p>
     * This is used in the SAML Response so that the SP can determine the session it is receiving an answer to.
     *
     * @return string
     */
    public function getReferenceId()
    {
        return $this->referenceId;
    }

    /**
     * Generates the IdP metadata.
     * <p>
     * Used by the SP to recognize the IdP.
     *
     * @return string
     */
    public function generateIdPMetadata()
    {
        $cert = XMLSecurityDSig::get509XCert(file_get_contents($this->getX509Certificate()));

        $url = AcctUrl().'SSOService.phtml';

        return '<?xml version="1.0"?><md:EntityDescriptor xmlns:md="urn:oasis:names:tc:SAML:2.0:metadata"'
            .' xmlns:ds="http://www.w3.org/2000/09/xmldsig#"'
            .' entityID="'.$this->getIssuer().'">'
            .'<md:IDPSSODescriptor protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol">'
            .'<md:KeyDescriptor use="signing"><ds:KeyInfo xmlns:ds="http://www.w3.org/2000/09/xmldsig#"><ds:X509Data>'
            .'<ds:X509Certificate>'
            .$cert
            .'</ds:X509Certificate>'
            .'</ds:X509Data></ds:KeyInfo></md:KeyDescriptor><md:KeyDescriptor use="encryption">
            <ds:KeyInfo xmlns:ds="http://www.w3.org/2000/09/xmldsig#"><ds:X509Data>'
            .'<ds:X509Certificate>'
            .$cert
            .'</ds:X509Certificate>'
            .'</ds:X509Data></ds:KeyInfo></md:KeyDescriptor>
        <md:SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect" Location="'
            .$url.'?.action=doLogout"/> '
            .'<md:NameIDFormat>urn:oasis:names:tc:SAML:2.0:nameid-format:unspecified</md:NameIDFormat>
        <md:SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect" Location="'
            .$url.'"/>'
            .'</md:IDPSSODescriptor><md:ContactPerson contactType="technical"><md:GivenName>Administrator'
            .'</md:GivenName>'
            .'<md:EmailAddress><EMAIL></md:EmailAddress></md:ContactPerson></md:EntityDescriptor>';

    }
}