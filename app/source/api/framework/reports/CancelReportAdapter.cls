<?php

/**
 * Class CancelReportAdapter
 *
 * <AUTHOR>
 * @copyright 2024 Sage Intacct, Inc. -- All Rights Reserved.
 */
class CancelReportAdapter extends ReportAPIAdapterBase
{

    /**
     * StatusReportAdapter constructor.
     *
     * @param string      $serviceName
     * @param string      $entityName
     * @param string|null $serviceClassName
     * @param string      $requestVersion
     * @param string      $schemaRevision
     *
     * @throws APIInternalException
     * @throws APIOrchestratorException
     */
    public function __construct(string $serviceName, string $entityName, ?string $serviceClassName,
                                string $requestVersion, string $schemaRevision)
    {
        parent::__construct($serviceName, $entityName, $serviceClassName ?? "", $requestVersion, $schemaRevision);
        $this->entityName = APIReportConstants::REPORT_STORE_ENTITY_NAME;
    }

    /**
     * @inheritDoc
     */
    protected function executeRequest(array $request, string $objectName, string $operation, array &$extraParams) : array
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        /* var ReportStoreManager $mgr  */
        $mgr = $gManagerFactory->getManager(APIReportConstants::REPORT_STORE_ENTITY_NAME);

        if ( ! ( isset($request[APIReportConstants::REPORTID_FIELD_NAME]) ) ) {
            throw ( new APIException() )->setAPIError(APIError::getInstance(
                APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0205,
                [ "PARAMETER" => APIReportConstants::REPORTID_API_FIELD_NAME ], true));
        }

        $reportId = $request[APIReportConstants::REPORTID_FIELD_NAME];
        $outputType = $request[APIReportConstants::OUTPUT_TYPE_FIELD_NAME];
        $storeType = $request[APIReportConstants::OUTPUT_LOCATION_FIELD_NAME];
        if ($mgr->cancelQueuedReport($reportId, $outputType, $storeType)) {
            return [
                APIReportConstants::REPORTID_FIELD_NAME => $reportId,
                APIReportConstants::STATUS_FIELD_NAME => "Z",
                APIReportConstants::STATUS_IN_RESPONSE => HTTPAPIResponse::HTTP_OK
            ];
        }

        return [];
    }
}