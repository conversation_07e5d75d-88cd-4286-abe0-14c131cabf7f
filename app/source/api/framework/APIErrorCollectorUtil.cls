<?php

/**
 * Utility Singleton class for collecting API Errors for report all feature of NextGen API
 *
 * <AUTHOR>
 * @copyright Copyright (C)2020 Sage Intacct Corporation, All Rights Reserved
 */

/**
 * APIErrorCollectorUtil
 */
class APIErrorCollectorUtil {

    /**
     * Collector Singleton instance
     * @var APIErrorCollectorUtil|null
     */
    private static ?APIErrorCollectorUtil $instance = null;

    /**
     * ApiError collection
     * @var array
     */
    private static array $APIErrorCollection = [];

    /**
     * Initialize instance
     *
     * @return APIErrorCollectorUtil
     */
    public static function getInstance() : APIErrorCollectorUtil
    {
        if (self::$instance == null) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * Collect error
     *
     * @param APIError $error
     *
     * @return void
     */
    public function collectAPIError(APIError $error) : void
    {
        self::$APIErrorCollection[] = $error;
    }

    /**
     * Collect an array of ApiErrors
     *
     * @param array $errors
     *
     * @return void
     */
    public function collectAPIErrors(array $errors) : void
    {
        self::$APIErrorCollection = [...self::$APIErrorCollection, ...$errors];
    }

    /**
     * Return ApiError collection
     *
     * @param bool $purgeErrors
     *
     * @return array
     */
    public function getAPIErrors(bool $purgeErrors = true) : array
    {
        $apiErrors = self::$APIErrorCollection;

        if( $purgeErrors ) {
            $this->clearAPIErrors();
        }

        return $apiErrors;
    }

    /**
     * Check if there are ApiErrors collected
     *
     * @return bool
     */
    public function hasAPIErrors() : bool
    {
        return !empty(self::$APIErrorCollection);
    }

    /**
     * Clear collected errors
     *
     * @return void
     */
    public function clearAPIErrors() : void
    {
        self::$APIErrorCollection = [];
    }

    /**
     * Return the count of api errors.
     *
     * @return int
     */
    public function getAPIErrorCount() : int
    {
        return count(self::$APIErrorCollection) ;
    }
}