<?php

/**
 * GenericFunctionServiceOrchestrator is a generic API Adapter for Domain Services
 *
 * <AUTHOR>
 * @copyright Copyright (C)2020 Sage Intacct Corporation, All Rights Reserved
 */

require_once 'util.inc';
require_once 'xml_api.inc';
require_once 'backend_error.inc';

class GenericFunctionServiceAdapter extends FunctionServiceAPIAdapterBase
{
    /**
     * @inheritDoc
     */
    public function executeOperation($operation, $objectName, $version, $request, &$ownedRequest, &$extraParams,
                                     $isOwned = false, $caller = ObjectAPIAdapter::CALLER_ADAPTER_PRE)
    {
        $instance = new $this->serviceClassName();
        return $instance->{$this->functionName}($ownedRequest, $extraParams);
    }
}
