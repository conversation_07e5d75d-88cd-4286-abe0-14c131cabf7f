<?php
/**
 * File productionunits.ent contains entity definition for productionunits
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

$kSchemas['productionunits'] = array(
    'object' => array(
        'RECORDNO',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',

        'TASKKEY',
        'ASOFDATE',
        'PRODUCTIONUNITS',
        'NOTE',
    ),
    'schema' => array (
        'RECORDNO' => 'record#',
        'WHENCREATED' => 'whencreated',
        'WHENMODIFIED' => 'whenmodified',
        'CREATEDBY' => 'createdby',
        'MODIFIEDBY' => 'modifiedby',

        'TASKKEY' => 'taskkey',
        'ASOFDATE' => 'asofdate',
        'PRODUCTIONUNITS' => 'productionunits',
        'NOTE' => 'note',
    ),
    'children' => [
        'task' => [
            'fkey' => 'taskkey', 'invfkey' => 'record#', 'table' => 'task', 'join' => 'inner',
        ],
    ],
    'nexus' => [
        'task' => [
            'object' => 'task', 'relation' => MANY2ONE, 'field' => 'TASKKEY', 'printas' => 'IA.TASK'
        ],
    ],

    'fieldinfo' => [
        $gRecordNoFieldInfo,
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
        [
            'id' => 1,
            'path' => 'TASKKEY',
            'fullname' => 'IA.TASK_KEY',
            'type' => $gIntegerType,
        ],
        [
            'id' => 2,
            'path' => 'ASOFDATE',
            'fullname' => 'IA.AS_OF_DATE',
            'type' => $gDateType,
            'required' => true
        ],
        [
            'id' => 3,
            'path' => 'PRODUCTIONUNITS',
            'fullname' => 'IA.PRODUCTION_UNITS',
            'type' => [
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 30,
                'size' => 30,
                'format' => $gDecimalFormat,
            ],
            'required' => true,
            'noformat' => true,
        ],
        [
            'id' => 4,
            'path' => 'NOTE',
            'fullname' => 'IA.NOTE',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
            ],
        ],
    ],

    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENCREATED',
        'WHENMODIFIED'
    ),

    'dbsorts' => [
        ['ASOFDATE']
    ],

    'module' => 'pa',
    'table' => 'productionunits',
    'autoincrement' => 'RECORDNO',
    'vid' => 'RECORDNO',
    'parententity' => 'task',
    'auditcolumns' => true,
    'nosysview' => true,

    'api' => array(
        'PERMISSION_READ' => 'ALL',
        'PERMISSION_CREATE' => 'ALL',
        'PERMISSION_UPDATE' => 'ALL',
        'PERMISSION_DELETE' => 'ALL'
    ),
    'printas' => 'IA.PRODUCTION_UNITS',
    'pluralprintas' => 'IA.PRODUCTION_UNITS',
);
