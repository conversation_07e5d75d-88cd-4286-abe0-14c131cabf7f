<?php

/**
 * Entity for the Payroll Report Union Local Object
 *
 * <AUTHOR> M <<EMAIL>>
 * @copyright 2025 Sage Intacct Inc.
 *
 */
global $gRecordNoFormat, $gWhenCreatedFieldInfo, $gWhenModifiedFieldInfo, $gCreatedByFieldInfo, $gModifiedByFieldInfo, $gBooleanType, $gDateType;
$kSchemas['payrollreportunionlocal'] = [
    'children' => [
        'payrollreportunionlocalreciprocityrules' => [
            'fkey' => 'record#',
            'invfkey' => 'unionlocalkey',
            'table' => 'payrollreportunionlocalreciprocityrules',
            'join' => 'outer',
        ],
    ],
    'nexus' => [
        'PAYROLLREPORTUNIONLOCALRECIPROCITYRULES' => [
            'object' => 'payrollreportunionlocalreciprocityrules',
            'relation' => ONE2MANY,
            'field' => 'RECORDNO',
        ],
    ],
    'schema' => [
        'RECORDNO'            => 'record#',
        'UNIONLOCALID'        => 'unionlocalid',
        'EFFECTIVEDATE'       => 'effectivedate',
        'EXPIRATIONDATE'      => 'expirationdate',
        'TRADEID'             => 'tradeid',
        'STANDARDCOMPENSATIONTABLEID' => 'standardcompensationtableid',
        'REVISIONNUMBER'      => 'revisionnumber',
        'WHENCREATED'         => 'whencreated',
        'WHENMODIFIED'        => 'whenmodified',
        'CREATEDBY'           => 'createdby',
        'MODIFIEDBY'          => 'modifiedby',
    ],

    'object' => [
        'RECORDNO',
        'UNIONLOCALID',
        'EFFECTIVEDATE',
        'EXPIRATIONDATE',
        'ACTIVERECORD',
        'TRADEID',
        'STANDARDCOMPENSATIONTABLEID',
        'REVISIONNUMBER',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ],
    'publish' => [
        'RECORDNO',
        'UNIONLOCALID',
        'EFFECTIVEDATE',
        'EXPIRATIONDATE',
        'ACTIVERECORD',
        'TRADEID',
        'STANDARDCOMPENSATIONTABLEID',
        'REVISIONNUMBER',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ],
    'fieldinfo' => [
        [
            'path' => 'RECORDNO',
            'fullname' => 'IA.RECORD_NO',
            'desc' => 'IA.RECORD_NO',
            'type' => [
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 15,
                'format' => $gRecordNoFormat
            ],
            'disableReportSelection' => true,
            'hidden' => true,
            'readonly' => true,
            'noapiadd' => true,
            'id' => 1,
        ],
        [
            'path' => 'UNIONLOCALID',
            'fullname' => 'IA.UNION_LOCAL_ID',
            'desc' => 'IA.UNION_LOCAL_ID',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 100,
            ],
            'required' => true,
            'id' => 2,
        ],
        [
            'path' => 'EFFECTIVEDATE',
            'fullname' => 'IA.EFFECTIVE_DATE',
            'desc' => 'IA.EFFECTIVE_DATE',
            'type' => $gDateType,
            'required' => true,
            'id' => 3,
        ],
        [
            'path' => 'EXPIRATIONDATE',
            'fullname' => 'IA.EXPIRATION_DATE',
            'desc' => 'IA.EXPIRATION_DATE',
            'type' => $gDateType,
            'id' => 4,
        ],
        [
            'path' => 'REVISIONNUMBER',
            'fullname' => 'IA.REVISION_NUMBER',
            'desc' => 'IA.REVISION_NUMBER',
            'type' => [
                'type' => 'decimal',
                'ptype' => 'decimal',
                'maxlength' => 30,
            ],
            'id' => 5,
        ],
        [
            'path' => 'TRADEID',
            'fullname' => 'IA.TRADE_ID',
            'desc' => 'IA.TRADE_ID',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 100,
            ],
            'required' => true,
            'id' => 6,
        ],
        [
            'path' => 'STANDARDCOMPENSATIONTABLEID',
            'fullname' => 'IA.STANDARD_COMPENSATION_TABLE_ID',
            'desc' => 'IA.STANDARD_COMPENSATION_TABLE_ID',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 100,
            ],
            'required' => true,
            'id' => 7,
        ],
        [
            'path' => 'ACTIVERECORD',
            'fullname' => 'IA.ACTIVE_RECORD',
            'desc' => 'IA.ACTIVE_RECORD',
            'type' => $gBooleanType,
            'formula' => array(
                'fields' => array('EFFECTIVEDATE', 'EXPIRATIONDATE'),
                'typeOf' => 'UNIONLOCALID',
                'function' => "CASE WHEN \${1} <= CURRENT_DATE AND CURRENT_DATE <= NVL( \${2}, CURRENT_DATE) THEN 'true' ELSE 'false' END",
            ),
            'rpdFormula' => array(
                'fields' => array('EFFECTIVEDATE', 'EXPIRATIONDATE'),
                'function' => "CASE WHEN \${1} <= CURRENT_DATE AND CURRENT_DATE <= IFNULL( \${2}, CURRENT_DATE) THEN 'IA.TRUE' ELSE 'IA.FALSE' END",
                'tokenMap' => [ // Do not change the tokanMap entries unless you clear this up with someone who knows the RPD build process
                                // These values are used in the current ICRW reports' results, filters and prompts
                                'IA.TRUE' => 'true',
                                'IA.FALSE' => 'false',
                ],
            ),
            'calculated' => true,
            'readonly' => true,
            'noapiadd'  =>  true,
            'noapiset'  =>  true,
            'id' => 8,
        ],
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
    ],
    'ownedobjects' => [
        [
            'fkey'    => 'UNIONLOCALKEY',
            'invfkey' => 'RECORDNO', // the field with which the owned object points to the parent
            'entity'  => 'payrollreportunionlocalreciprocityrules',
            'path'    => 'PAYROLL_REPORT_UNION_LOCAL_RECIPROCITY_RULE',
        ],
    ],
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENMODIFIED',
        'WHENCREATED'
    ),
    'url' => [
        'no_short_url' => true,    // Don't allow short url
    ],
    'table' => 'payrollreportunionlocal',
    'printas' => 'IA.PAYROLL_REPORT_UNION_LOCAL',
    'pluralprintas' => 'IA.PAYROLL_REPORT_UNION_LOCALS',
    'autoincrement' => 'RECORDNO',
    'vid' => 'RECORDNO',
    'module' => 'pa',
    'description' => 'IA.PAYROLL_REPORT_UNION_LOCAL',
    'allowDDS' => false,
    'platformProperties' => [
        SOBJECT_CAN_HAVE_RELATIONSHIPS => false,
    ],
    'allowTriggers' => false,
    'api' => [
        'PERMISSION_MODULES' => ['pay'],
        'ITEM_ALIAS' => ['PAYROLLREPORTUNIONLOCALRECIPROCITYRULE'],
        'ITEMS_ALIAS' => ['PAYROLLREPORTUNIONLOCALRECIPROCITYRULES'],
        'ITEMS_INTERNAL' => ['PAYROLL_REPORT_UNION_LOCAL_RECIPROCITY_RULE'],
        'GET_BY_GET' => true,
    ],
    'auditcolumns' => true,
];