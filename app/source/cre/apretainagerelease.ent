<?php
/**
 * Entity for the AR retainage release records header
 *
 * <AUTHOR>
 * @copyright 2019 Intacct Corporation All, Rights Reserved
 */

$kSchemas['apretainagerelease'] = [
    'children'      => [
        'apretainagereleaseentry' => [
            'fkey' => 'record#', 'invfkey' => 'rrkey', 'table' => 'retainagereleaseentrymst', 'join' => 'outer',
        ],
        'vendor'                  => [
            'fkey' => 'vendorkey', 'invfkey' => 'record#', 'table' => 'vendormst', 'join' => 'outer',
        ],
        'project'                 => [
            'fkey' => 'projectkey', 'invfkey' => 'record#', 'table' => 'projectmst', 'join' => 'outer',
        ],
        'created_userinfo'        => [
            'fkey' => 'createdby', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'userinfomst',
        ],
        'modified_userinfo'       => [
            'fkey' => 'modifiedby', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'userinfomst',
        ],
    ],
    'object'        => [
        'RECORDNO',
        'RECORDTYPE',
        'DESCRIPTION',
        'RELEASEDATE',
        'GLPOSTINGDATE',
        'STATE',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'CREATEDUSER',
        'MODIFIEDBY',
        'MODIFIEDUSER',
    ],
    'schema'        => [
        'RECORDNO'      => 'record#',
        'RECORDTYPE'    => 'recordtype',
        'DESCRIPTION'   => 'description',
        'RELEASEDATE'   => 'releasedate',
        'GLPOSTINGDATE' => 'glpostingdate',
        'STATE'         => 'state',
        'WHENCREATED'   => 'whencreated',
        'WHENMODIFIED'  => 'whenmodified',
        'CREATEDBY'     => 'createdby',
        'CREATEDUSER'   => 'created_userinfo.loginid',
        'MODIFIEDBY'    => 'modifiedby',
        'MODIFIEDUSER'  => 'modified_userinfo.loginid',
        'SI_UUID'       => 'si_uuid',
    ],
    'nexus'         => [
        'apretainagereleaseentry' => [
            'object'   => 'apretainagereleaseentry',
            'relation' => ONE2MANY,
            'field'    => 'RRKEY',
        ],
        'vendor'                  => [
            'object'   => 'vendor',
            'relation' => ONE2MANY,
            'field'    => 'VENDORID',
        ],
    ],
    'publish'       => [
        'RECORDNO',
        'DESCRIPTION',
        'RELEASEDATE',
        'GLPOSTINGDATE',
        'STATE',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'CREATEDUSER',
        'MODIFIEDBY',
        'MODIFIEDUSER',
    ],
    'ownedobjects'  => [
        [
            'fkey'    => 'RRKEY',
            'invfkey' => 'RECORDNO',
            'entity'  => 'apretainagereleaseentry',
            'path'    => 'RETAINAGERELEASEENTRIES',
        ],
    ],
    'fieldinfo'     => [
        $gRecordNoFieldInfo,
        [
            'path'     => 'RECORDTYPE',
            'fullname' => 'IA.TYPE',
            'type'     => [
                'type'          => 'text',
                'ptype'         => 'radio',
                'validlabels' => [ 'IA.APBILL', 'IA.AR_INVOICE', ],
                'validvalues'   => [
                    'pi', 'ri',
                ],
                '_validivalues' => [ 'pi', 'ri' ],
            ],
            'default'  => 'pi',
            'required' => false,
            'desc'     => 'IA.TYPE',
            'readonly' => true,
            'platform' => false,
        ],
        [
            'path'     => 'DESCRIPTION',
            'fullname' => 'IA.DESCRIPTION',
            'type'     => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 1000,
            ],
            'required' => false,
            'desc'     => 'IA.DESCRIPTION',
        ],
        [
            'path'     => 'RELEASEDATE',
            'fullname' => 'IA.RELEASE_DATE',
            'type'     => [
                'ptype'     => 'date',
                'type'      => 'date',
                'format'    => $gDateFormat,
                'maxlength' => 10,
            ],
            'required' => false,
        ],
        [
            'path'     => 'GLPOSTINGDATE',
            'fullname' => 'IA.GL_POSTING_DATE',
            'type'     => [
                'ptype'     => 'date',
                'type'      => 'date',
                'format'    => $gDateFormat,
                'maxlength' => 10,
            ],
            'required' => false,
        ],
        [
            'path'     => 'VENDORKEY',
            'fullname' => 'IA.VENDOR_KEY',
            'type'     => [
                'ptype'     => 'integer',
                'type'      => 'integer',
                'maxlength' => 8,
                'format'    => $gRecordNoFormat,
            ],
            'required' => false,
            'derived'  => true,
            'hidden'   => true,
            'desc'     => 'IA.VENDOR_KEY',
            'platform' => false,
        ],
        [
            'path'       => 'VENDORID',
            'fullname'   => 'IA.VENDOR',
            'type'       => [
                'type'       => 'text',
                'ptype'      => 'ptr',
                'pickentity' => 'vendorpick',
                'entity'     => 'vendor',
            ],
            'required'   => false,
            'renameable' => true,
            'platform'   => false,
        ],
        [
            'path'       => 'VENDORNAME',
            'fullname'   => 'IA.VENDOR_NAME',
            'type'       => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 100,
                'format'    => '/^.{0,100}$/',
            ],
            'renameable' => true,
            'platform'   => false,
        ],
        [
            'path'     => 'PROJECTKEY',
            'fullname' => 'IA.PROJECT_KEY',
            'type'     => [
                'ptype'     => 'integer',
                'type'      => 'integer',
                'maxlength' => 8,
                'format'    => $gRecordNoFormat,
            ],
            'required' => false,
            'derived'  => true,
            'hidden'   => true,
            'desc'     => 'IA.PROJECT_KEY',
            'platform' => false,
        ],
        [
            'path'       => 'PROJECTID',
            'fullname'   => 'IA.PROJECT',
            'type'       => [
                'type'       => 'text',
                'ptype'      => 'ptr',
                'entity'     => 'project',
                'pickentity' => 'projectpick',
                'maxlength'  => 20,
            ],
            'required'   => false,
            'renameable' => true,
            'platform'   => false,
        ],
        [
            'path'       => 'PROJECTNAME',
            'fullname'   => 'IA.PROJECT_NAME',
            'type'       => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 200,
            ],
            'required'   => false,
            'renameable' => true,
            'platform'   => false,
        ],
        [
            'path'     => 'STATE',
            'fullname' => 'IA.STATE',
            'type'     => [
                'type'          => 'text',
                'ptype'         => 'enum',
                'validlabels' => [ 'IA.DRAFT', 'IA.RELEASED', 'IA.REVERSAL', ],
                'validvalues'   => [
                    'Draft', 'Released', 'Reversal',
                ],
                '_validivalues' => [ 'D', 'R', 'V' ],
                'maxlength'     => 10,
            ],
            'required' => true,
            'default'  => 'Draft',
            'layout'   => 'landscape',
        ],
        [
            'path'     => 'PARAMETERS',
            'fullname' => 'IA.PARAMETERS',
            'type'     => [
                'type'  => 'text',
                'ptype' => 'text',
            ],
            'platform' => false,
        ],
        [
            'path'     => 'ERRORDATA',
            'fullname' => 'IA.ERROR_DATA',
            'type'     => [
                'ptype' => 'text',
                'type'  => 'text',
            ],
            'platform' => false,
        ],
        array(
            'path' => 'WHENCREATED',
            'fullname' => 'IA.WHEN_CREATED',
            'type' => $gDateType,
            'desc'      => 'IA.TIMESTAMP_MARKING_LAST_TIME_THIS_WAS_CHANGED',
            'hidden'    =>  true,
            'readonly'  =>  true,
            'noapiadd'  =>  true,
            'noapiset'  =>  true,
            'id' => 970
        ),
        array(
            'path' => 'WHENMODIFIED',
            'fullname' => 'IA.WHEN_MODIFIED',
            'type' => $gDateType,
            'desc'      => 'IA.TIMESTAMP_MARKING_LAST_TIME_THIS_WAS_CHANGED',
            'hidden'    =>  true,
            'readonly'  =>  true,
            'noapiadd'  =>  true,
            'noapiset'  =>  true,
            'id' => 969
        ),
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
        [
            'fullname' => 'IA.CREATED_BY_USER',
            'type'     => [
                'ptype' => 'text',
                'type'  => 'text',
            ],
            'path'     => 'CREATEDUSER',
            'platform' => false,
        ],
        [
            'fullname' => 'IA.MODIFIED_BY_USER',
            'type'     => [
                'ptype' => 'text',
                'type'  => 'text',
            ],
            'path'     => 'MODIFIEDUSER',
            'platform' => false,
        ],
        $gSiUuidFieldInfo,
    ],
    'pairedFields'  => [
        'VENDORID'  => 'VENDORNAME',
        'PROJECTID' => 'PROJECTNAME',
    ],
    'printas'       => 'IA.AP_RETAINAGE_RELEASE',
    'pluralprintas' => 'IA.AP_RETAINAGE_RELEASES',
    'sicollaboration' => true,
    'vid'           => 'RECORDNO',
    'autoincrement' => 'RECORDNO',
    'module'        => 'ap',
    'table'         => 'retainagerelease',
    'dbfilters'     => [
        [
            'apretainagerelease.recordtype', '=', 'pi',
        ],
    ],
    'upsertEntries' => true,
    'allowDDS'      => true,
    'auditcolumns'  => true,
    'nosysview'     => true,
    'customerp'     => [
        'SLTypes'  => [
            CUSTOMERP_SMARTLINKFETCH,
            CUSTOMERP_SMARTLINKCLICK,
            CUSTOMERP_SMARTLINKVALIDATE,
            CUSTOMERP_SMARTLINKWORKFLOW,
        ],
        'SLEvents' => [
            CUSTOMERP_EVENT_ADD,
            CUSTOMERP_EVENT_SET,
            CUSTOMERP_EVENT_ADDSET,
            CUSTOMERP_EVENT_DELETE,
            CUSTOMERP_EVENT_CLICK,
        ],
        'AllowCF'  => true,
    ],
    'platformProperties' => [
        SOBJECT_CAN_HAVE_RELATIONSHIPS => false,
    ],
    'api'           => [
        'GET_BY_GET'      => true,
        'ITEMS_ALIAS'     => [ 'APRETAINAGERELEASEENTRIES' ],
        'ITEM_ALIAS'      => [ 'APRETAINAGERELEASEENTRY' ],
        'ITEMS_INTERNAL'  => [ 'RETAINAGERELEASEENTRIES' ],
        'LESS_GET_FIELDS' => [
            'RECORDTYPE',
            'CREATEDUSER',
            'MODIFIEDUSER',
        ],
    ],
    'description' => 'IA.HEADER_INFORMATION_FOR_AP_RETAINAGE_RELEASE_DESC',
];

$kSchemas['apretainagerelease'] = ( static function($meta) {
    //add the ids
    foreach ( $meta['fieldinfo'] as $id => &$field ) {
        $field['id'] = $id + 1;
    }

    return $meta;
} )($kSchemas['apretainagerelease']);
