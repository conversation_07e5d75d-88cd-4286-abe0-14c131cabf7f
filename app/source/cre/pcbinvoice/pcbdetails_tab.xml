<child>
    <flexsection id="pcbdetails_actions">
        <flexrow justify_content="space-between">
            <flexchild>
                <button id="pcb_update_details">
                    <name>IA.UPDATE_PCB_DETAILS</name>
                    <events>
                        <click>pcbInvoiceHandler.updatePcbDetails();</click>
                    </events>
                </button>
            </flexchild>
            <flexchild ml="5px">
                <button id="pcb_audit_details">
                    <name>IA.VIEW_AUDIT_TRAIL</name>
                    <events>
                        <click>pcbInvoiceHandler.viewPcbAuditDetails();</click>
                    </events>
                </button>
            </flexchild>
        </flexrow>
    </flexsection>
</child>
<child>
    <section id="pcbSummary" columnCount="4">
        <field readonly="true" hidden="true" noLabel="true" isHTML="true" path="PCB_AUDIT_URL"/>
        <field readonly="true" fullname="IA.PROJECT_CONTRACT_ID" isHTML="true" path="PCB_PROJECTCONTRACTID_URL"/>
        <field readonly="true" fullname="IA.PROJECT_CONTRACT_NAME">PCB_PROJECTCONTRACTNAME</field>
        <field readonly="true" fullname="IA.PROJECT_ID" isHTML="true"><path>PCB_PROJECT_ID_URL</path></field>
        <field readonly="true" fullname="IA.PROJECT_NAME"><path>PCB_PROJECT</path></field>
        <field path="PCB_ARINVOICENUMBER" fullname="IA.AR_INVOICE_NUMBER" readonly="true">
            <type type="text" ptype="href"/>
            <events>
                <click>pcbInvoiceHandler.drilldownAndShow(this.meta, 'PRRECORDKEY', 'ri');</click>
            </events>
        </field>
        <field readonly="true" fullname="IA.BILLING_THROUGH_DATE"><path>PCB_BILLTHROUGHDATE</path></field>
        <field readonly="true" fullname="IA.BILLING_APPLICATION_NO"><path>PCB_BILLAPPLICATIONNO</path></field>
        <field readonly="true" fullname="IA.DOCUMENT_ID">PCB_DOCID</field>
        <field readonly="true" fullname="IA.DOCUMENT_NUMBER">PCB_DOCNO</field>
        <field readonly="true" fullname="IA.ORIGINAL_CONTRACT_AMOUNT">
            <path>PCB_ORIGINALCONTRACTAMOUNT</path>
            <type assoc="T">
                <ptype>currency</ptype>
                <type>decimal</type>
            </type>
        </field>
        <field readonly="true" fullname="IA.TOTAL_CHANGES_APPROVED_IN_PRIOR_MONTHS">
            <path>PCB_TCAPMADDITION</path>
            <type assoc="T">
                <ptype>currency</ptype>
                <type>decimal</type>
            </type>
        </field>
        <field readonly="true" fullname="IA.TOTAL_CHANGES_APPROVED_IN_PRIOR_MONTHS_1">
            <path>PCB_TCAPMDEDUCTION</path>
            <type assoc="T">
                <ptype>currency</ptype>
                <type>decimal</type>
            </type>
        </field>
        <field readonly="true" fullname="IA.TOTAL_CHANGES_APPROVED_THIS_MONTH_ADDITIONS">
            <path>PCB_TCATMADDITION</path>
            <type assoc="T">
                <ptype>currency</ptype>
                <type>decimal</type>
            </type>
        </field>
        <field readonly="true" fullname="IA.TOTAL_CHANGES_APPROVED_THIS_MONTH_DEDUCTIONS">
            <path>PCB_TCATMDEDUCTION</path>
            <type assoc="T">
                <ptype>currency</ptype>
                <type>decimal</type>
            </type>
        </field>
        <field readonly="true" fullname="IA.TOTAL_NET_CHANGES">
            <path>PCB_TOTALNETAPPROVEDCHANGES</path>
            <type assoc="T">
                <ptype>currency</ptype>
                <type>decimal</type>
            </type>
        </field>
        <field readonly="true" fullname="IA.REVISED_CONTRACT_AMOUNT">
            <path>PCB_TOTALREVISEDCONTRACTAMOUNT</path>
            <type assoc="T">
                <ptype>currency</ptype>
                <type>decimal</type>
            </type>
        </field>
        <field readonly="true" fullname="IA.TOTAL_COMPLETED_FROM_PRIOR_APPLICATIONS">
            <path>PCB_TOTALCOMPLETEDFROMPRIORAPPLICATIONS</path>
            <type assoc="T">
                <ptype>currency</ptype>
                <type>decimal</type>
            </type>
        </field>
        <field readonly="true" fullname="IA.TOTAL_COMPLETED_TO_DATE">
            <path>PCB_TOTALCOMPLETEDTODATE</path>
            <type assoc="T">
                <ptype>currency</ptype>
                <type>decimal</type>
            </type>
        </field>
        <field readonly="true" fullname="IA.INVOICE_AMOUNT_RETAINED">
            <path>PCB_INVOICEAMOUNTRETAINED</path>
            <type assoc="T">
                <ptype>currency</ptype>
                <type>decimal</type>
            </type>
        </field>
        <field readonly="true" fullname="IA.INVOICE_RETAINAGE_BILLED">
            <path>PCB_INVOICERETAINAGEBILLED</path>
            <type assoc="T">
                <ptype>currency</ptype>
                <type>decimal</type>
            </type>
        </field>
        <field readonly="true" fullname="IA.NET_CHANGE_RETAINAGE_HELD">
            <path>PCB_NETCHANGERETAINAGEHELD</path>
            <type assoc="T">
                <ptype>currency</ptype>
                <type>decimal</type>
            </type>
        </field>
        <field readonly="true" fullname="IA.RETAINAGE_HELD_TO_DATE">
            <path>PCB_RETAINAGEHELDTODATE</path>
            <type assoc="T">
                <ptype>currency</ptype>
                <type>decimal</type>
            </type>
        </field>
        <field readonly="true" fullname="IA.RETAINAGE_BILLED_TO_DATE">
            <path>PCB_RETAINAGEBILLEDTODATE</path>
            <type assoc="T">
                <ptype>currency</ptype>
                <type>decimal</type>
            </type>
        </field>
        <field readonly="true" fullname="IA.RETAINAGE_BALANCE_TO_DATE">
            <path>PCB_RETAINAGEBALANCETODATE</path>
            <type assoc="T">
                <ptype>currency</ptype>
                <type>decimal</type>
            </type>
        </field>
        <field readonly="true" fullname="IA.PREVIOUS_RETAINAGE_BALANCE">
            <path>PCB_PREVIOUSRETAINAGEBALANCE</path>
            <type assoc="T">
                <ptype>currency</ptype>
                <type>decimal</type>
            </type>
        </field>
        <field readonly="true" fullname="IA.TOTAL_EARNED_LESS_RETAINAGE">
            <path>PCB_TOTALEARNEDLESSRETAINAGE</path>
            <type assoc="T">
                <ptype>currency</ptype>
                <type>decimal</type>
            </type>
        </field>
        <field readonly="true" fullname="IA.LESS_PREVIOUS_BILLINGS">
            <path>PCB_LESSPREVIOUSBILLINGS</path>
            <type assoc="T">
                <ptype>currency</ptype>
                <type>decimal</type>
            </type>
        </field>
        <field readonly="true" fullname="IA.CURRENT_AMOUNT_DUE">
            <path>PCB_CURRENTAMOUNTDUE</path>
            <type assoc="T">
                <ptype>currency</ptype>
                <type>decimal</type>
            </type>
        </field>
        <field readonly="true" fullname="IA.BALANCE_TO_FINISH">
            <path>PCB_BALANCETOFINISH</path>
            <type assoc="T">
                <ptype>currency</ptype>
                <type>decimal</type>
            </type>
        </field>
        <field readonly="true" fullname="IA.TAXES">
            <path>PCB_TOTALTAX</path>
            <type assoc="T">
                <ptype>currency</ptype>
                <type>decimal</type>
            </type>
        </field>
        <field readonly="true" fullname="IA.DISCOUNT">
            <path>PCB_TOTALDISCOUNT</path>
            <type assoc="T">
                <ptype>currency</ptype>
                <type>decimal</type>
            </type>
        </field>
        <field readonly="true" fullname="IA.CHARGES">
            <path>PCB_TOTALCHARGE</path>
            <type assoc="T">
                <ptype>currency</ptype>
                <type>decimal</type>
            </type>
        </field>
    </section>
</child>
<child>
    <grid hasFixedNumOfRows="true" readonly="true" allowEditPage="true">
        <path>PCBINVOICEDETAILS</path>
        <enableEmptyMessage>"true"</enableEmptyMessage>
        <emptyMessage>IA.NO_RECORDS_TO_DISPLAY</emptyMessage>
        <title>IA.PROJECT_CONTRACT_BILLING_INVOICE_DETAIL</title>
        <column>
            <gridHeading className="center"><field noLabel="true" searchPath="PROJECTCONTRACTID"><path>SEARCH_PROJECTCONTRACTID</path></field></gridHeading>
            <field path="PROJECTCONTRACTID_URL" fullname="IA.PROJECT_CONTRACT_ID" isHTML="true" readonly="true" sortable="true"/>
        </column>
        <column>
            <gridHeading className="center"><field noLabel="true" searchPath="PROJECTCONTRACTLINEID"><path>SEARCH_PROJECTCONTRACTLINEID</path></field></gridHeading>
            <field path="PROJECTCONTRACTLINEID_URL" fullname="IA.PROJECT_CONTRACT_LINE_ID" isHTML="true" readonly="true" sortable="true"/>
        </column>
        <column>
            <gridHeading className="center"><field noLabel="true" searchPath="PCBLNAME"><path>SEARCH_PCBLNAME</path></field></gridHeading>
            <field path="PCBLNAME" fullname="IA.PROJECT_CONTRACT_LINE_NAME" readonly="true" sortable="true"/>
        </column>
        <column>
            <gridHeading className="center"><field noLabel="true" searchPath="PROJECTID"><path>SEARCH_PROJECTID</path></field></gridHeading>
            <field path="PROJECTID_URL" fullname="IA.PROJECT_ID" isHTML="true" readonly="true" sortable="true"/>
        </column>
        <column>
            <gridHeading className="center"><field noLabel="true" searchPath="TASKID"><path>SEARCH_TASKID</path></field></gridHeading>
            <field path="TASKID_URL" fullname="IA.TASK_ID" isHTML="true" readonly="true" sortable="true"/>
        </column>
        <column>
            <gridHeading className="center"><field noLabel="true" searchPath="TASKNAME"><path>SEARCH_TASKNAME</path></field></gridHeading>
            <field path="TASKNAME" fullname="IA.TASK_NAME" isHTML="true" readonly="true" sortable="true"/>
        </column>
        <column>
            <gridHeading className="center"><field noLabel="true" searchPath="ORIGINALCONTRACTAMOUNT"><path>SEARCH_ORIGINALCONTRACTAMOUNT</path></field></gridHeading>
            <field path="ORIGINALCONTRACTAMOUNT" fullname="IA.ORIGINAL_CONTRACT_AMOUNT" readonly="true" hasTotal="true" sortable="true"><type type="decimal" ptype="currency"/></field>
        </column>
        <column>
            <gridHeading className="center"><field noLabel="true" searchPath="TCAPMADDITION"><path>SEARCH_TCAPMADDITION</path></field></gridHeading>
            <field path="TCAPMADDITION" fullname="IA.TOTAL_CHANGES_APPROVED_IN_PRIOR_MONTHS" readonly="true" hasTotal="true" sortable="true"><type type="decimal" ptype="currency"/></field>
        </column>
        <column>
            <gridHeading className="center"><field noLabel="true" searchPath="TCAPMDEDUCTION"><path>SEARCH_TCAPMDEDUCTION</path></field></gridHeading>
            <field path="TCAPMDEDUCTION" fullname="IA.TOTAL_CHANGES_APPROVED_IN_PRIOR_MONTHS_1" readonly="true" hasTotal="true" sortable="true"><type type="decimal" ptype="currency"/></field>
        </column>
        <column>
            <gridHeading className="center"><field noLabel="true" searchPath="TCATMADDITION"><path>SEARCH_TCATMADDITION</path></field></gridHeading>
            <field path="TCATMADDITION" fullname="IA.TOTAL_CHANGES_APPROVED_THIS_MONTH_ADDITIONS" readonly="true" hasTotal="true" sortable="true"><type type="decimal" ptype="currency"/></field>
        </column>
        <column>
            <gridHeading className="center"><field noLabel="true" searchPath="TCATMDEDUCTION"><path>SEARCH_TCATMDEDUCTION</path></field></gridHeading>
            <field path="TCATMDEDUCTION" fullname="IA.TOTAL_CHANGES_APPROVED_THIS_MONTH_DEDUCTIONS" readonly="true" hasTotal="true" sortable="true"><type type="decimal" ptype="currency"/></field>
        </column>
        <column>
            <gridHeading className="center"><field noLabel="true" searchPath="TNACHANGES"><path>SEARCH_TNACHANGES</path></field></gridHeading>
            <field path="TNACHANGES" fullname="IA.TOTAL_NET_APPROVED_CHANGES" readonly="true" hasTotal="true" sortable="true"><type type="decimal" ptype="currency"/></field>
        </column>
        <column>
            <gridHeading className="center"><field noLabel="true" searchPath="TRCLAMOUNT"><path>SEARCH_TRCLAMOUNT</path></field></gridHeading>
            <field path="TRCLAMOUNT" fullname="IA.TOTAL_REVISED_CONTRACT_LINE_AMOUNT" readonly="true" hasTotal="true" sortable="true"><type type="decimal" ptype="currency"/></field>
        </column>
        <column>
            <gridHeading className="center"><field noLabel="true" searchPath="COMPPRIORAPP"><path>SEARCH_COMPPRIORAPP</path></field></gridHeading>
            <field path="COMPPRIORAPP" fullname="IA.COMPLETED_FROM_PRIOR_APPLICATIONS" readonly="true" hasTotal="true" sortable="true"><type type="decimal" ptype="currency"/></field>
        </column>
        <column>
            <gridHeading className="center"><field noLabel="true" searchPath="COMPTHISPERIOD"><path>SEARCH_COMPTHISPERIOD</path></field></gridHeading>
            <field path="COMPTHISPERIOD" fullname="IA.COMPLETED_THIS_PERIOD" readonly="true" hasTotal="true" sortable="true"><type type="decimal" ptype="currency"/></field>
        </column>
        <column>
            <gridHeading className="center"><field noLabel="true" searchPath="STOREDMATERIALS"><path>SEARCH_STOREDMATERIALS</path></field></gridHeading>
            <field path="STOREDMATERIALS" fullname="IA.STORED_MATERIALS" readonly="true" hasTotal="true" sortable="true"><type type="decimal" ptype="currency"/></field>
        </column>
        <column>
            <gridHeading className="center"><field noLabel="true" searchPath="TCTODATE"><path>SEARCH_TCTODATE</path></field></gridHeading>
            <field path="TCTODATE" fullname="IA.TOTAL_COMPLETED_TO_DATE" readonly="true" hasTotal="true" sortable="true"><type type="decimal" ptype="currency"/></field>
        </column>
        <column>
            <gridHeading className="center"><field noLabel="true" searchPath="PERCCOMPTODATE"><path>SEARCH_PERCCOMPTODATE</path></field></gridHeading>
            <field path="PERCCOMPTODATE" fullname="IA.PERCENTAGE_COMPLETED_TO_DATE" readonly="true" sortable="true"><type type="decimal" ptype="currency"/></field>
        </column>
        <column>
            <gridHeading className="center"><field noLabel="true" searchPath="RETAINAGEPERCENTAGE"><path>SEARCH_RETAINAGEPERCENTAGE</path></field></gridHeading>
            <field path="RETAINAGEPERCENTAGE" fullname="IA.RETAINAGE_PERCENTAGE" readonly="true" sortable="true"><type type="decimal" ptype="currency"/></field>
        </column>
        <column>
            <gridHeading className="center"><field noLabel="true" searchPath="INVAMOUNTRETAINED"><path>SEARCH_INVAMOUNTRETAINED</path></field></gridHeading>
            <field path="INVAMOUNTRETAINED" fullname="IA.INVOICE_AMOUNT_RETAINED" readonly="true" hasTotal="true" sortable="true"><type type="decimal" ptype="currency"/></field>
        </column>
        <column>
            <gridHeading className="center"><field noLabel="true" searchPath="INVRETAINAGEBILLED"><path>SEARCH_INVRETAINAGEBILLED</path></field></gridHeading>
            <field path="INVRETAINAGEBILLED" fullname="IA.INVOICE_RETAINAGE_BILLED" readonly="true" hasTotal="true" sortable="true"><type type="decimal" ptype="currency"/></field>
        </column>

        <lineDetails className="columns3">
            <pages>
                <page title="IA.DETAILS">
                    <section title="IA.DETAILS" columnCount="5">
                        <child>
                            <field path="BILLABLE" fullname="IA.BILLABLE" readonly="true" sortable="true"/>
                        </child>
                        <child>
                            <field path="PCLDESCRIPTION" fullname="IA.DESCRIPTION" readonly="true" sortable="true"/>
                        </child>
                        <child>
                            <field path="EXTERNALREFNO" fullname="IA.EXTERNAL_REFERENCE_NO" readonly="true" sortable="true"/>
                        </child>
                        <child>
                            <field path="INTERNALREFNO" fullname="IA.INTERNAL_REFERENCE_NO" readonly="true" sortable="true"/>
                        </child>
                        <child>
                            <field path="PROJECTNAME" fullname="IA.PROJECT_NAME" isHTML="true" readonly="true" sortable="true"/>
                        </child>
                        <child>
                            <field path="NETCHANGERETHELD" fullname="IA.NET_CHANGE_RETAINAGE_HELD" readonly="true" hasTotal="true" sortable="true"><type type="decimal" ptype="currency"/></field>
                        </child>
                        <child>
                            <field path="RETHELDTODATE" fullname="IA.RETAINAGE_HELD_TO_DATE" readonly="true" hasTotal="true" sortable="true"><type type="decimal" ptype="currency"/></field>
                        </child>
                        <child>
                            <field path="RETBILLTODATE" fullname="IA.RETAINAGE_BILLED_TO_DATE" readonly="true" hasTotal="true" sortable="true"><type type="decimal" ptype="currency"/></field>
                        </child>
                        <child>
                            <field path="RETBALTODATE" fullname="IA.RETAINAGE_BALANCE_TO_DATE" readonly="true" hasTotal="true" sortable="true"><type type="decimal" ptype="currency"/></field>
                        </child>
                        <child>
                            <field path="PREVRETBALANCE" fullname="IA.PREVIOUS_RETAINAGE_BALANCE" readonly="true" hasTotal="true" sortable="true"><type type="decimal" ptype="currency"/></field>
                        </child>
                        <child>
                            <field path="BALANCETOFINISH" fullname="IA.BALANCE_TO_FINISH" readonly="true" hasTotal="true" sortable="true"><type type="decimal" ptype="currency"/></field>
                        </child>
                        <child>
                            <field path="AMOUNTDUE" fullname="IA.LINE_AMOUNT_DUE" readonly="true" hasTotal="true" sortable="true"><type type="decimal" ptype="currency"/></field>
                        </child>
                        <child>
                            <field path="TAXES" fullname="IA.TAXES" readonly="true" hidden="true" sortable="true"><type type="decimal" ptype="currency"/></field>
                        </child>
                        <child>
                            <field path="CHARGES" fullname="IA.CHARGES" readonly="true" hidden="true" sortable="true"><type type="decimal" ptype="currency"/></field>
                        </child>
                        <child>
                            <field path="DISCOUNTS" fullname="IA.DISCOUNTS" readonly="true" hidden="true" sortable="true"><type type="decimal" ptype="currency"/></field>
                        </child>
                    </section>
                </page>
            </pages>
        </lineDetails>
    </grid>
</child>