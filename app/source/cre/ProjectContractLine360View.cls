<?php
/**
 * Implements CRE 360 view feature for Project contract line
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2022 Sage Intacct Inc.
 *
 * This document contains trade secret data that belongs to Sage Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
class ProjectContractLine360View extends ProjectContract360View implements CRE360ViewInterface
{
    // Initialization required for unit tests
    private string $projectContractLineKey = '';
    
    /**
     * Constructor for ProjectContractLine360View
     *
     * @param string $projectContractKey     project contract key
     * @param string $projectContractLineKey project contract line key
     */
    public function __construct(string $projectContractKey, string $projectContractLineKey)
    {
        parent::__construct($projectContractKey, CRE360ViewQueries::CRE360VIEW_OBJECT_PROJECT_CONTRACT_LINE);
        $this->projectContractLineKey = $projectContractLineKey;
    }
    
    /**
     * Implements getInvoices, for final 360 view
     *
     * @return array invoices array
     */
    public function getInvoices() : array
    {
        $pclPREntries = $this->getPREntriesForPCL();
        
        if ($pclPREntries === null || isEmptyArray($pclPREntries)) {
            return [];
        }
    
        // Create a set of prentry records for pcl
        $prRecordKeysForPCL = [];
        foreach ($pclPREntries as $pclPREntry) {
            array_push($prRecordKeysForPCL, $pclPREntry['RECORDKEY']);
        }
    
        $values = $this->getDocumentRecordsForPCL($prRecordKeysForPCL);
    
        if ($values === null || isEmptyArray($values)) {
            return [];
        }
    
        // Create sets of records by document key, prrecord key, and a map for them
        $docHdrKeys = [];
        $docHdrToPrRecordMap = [];
        foreach ($values as $value) {
            array_push($docHdrKeys, $value['RECORDNO']);
            $docHdrToPrRecordMap[$value['PRRECORDKEY']] = $value['RECORDNO'];
        }
    
        self::$INCLUDE_SUBTOTALS = false;
        $this->buildInvoiceRecords($pclPREntries, $docHdrKeys, $docHdrToPrRecordMap, $values);
    
        return $values;
    }
    
    /**
     * Compose final record structure for invoices, for consumption in view
     * Overridden for PCL 360 view to remove zero dollar invoices
     *
     * @param array $values reference to final records array to be updated
     * @param array $invoiceRecords invoice records array
     * @param array $subtotalsRecords subtotal records array
     *
     * @return void
     * @throws Exception
     */
    public function buildRecordStructureForInvoicesFromInvoiceAndSubtotalRecords(array &$values,
                                                                                 array $invoiceRecords,
                                                                                 array $subtotalsRecords)
    {
        foreach ($values as $key => &$value) {
            $recordIndex = $value['RECORDNO'];
            // Remove zero dollar invoice - Ref: IA-57750
            if ($invoiceRecords[$recordIndex]['TOTAL_BILLED'] === 0) {
                unset($values[$key]);
                continue;
            }
            $value['TOTAL_BILLED'] = $invoiceRecords[$recordIndex]['TOTAL_BILLED'];
            $value['TOTAL_RETAINAGE_HELD'] = $invoiceRecords[$recordIndex]['TOTAL_RETAINAGE_HELD'];
            $value['TOTAL_RETAINAGE_RELEASED'] = $invoiceRecords[$recordIndex]['TOTAL_RETAINAGE_RELEASED'];
            $value['TOTAL_PAID'] = $invoiceRecords[$recordIndex]['TOTAL_PAID'];
            if (self::$INCLUDE_SUBTOTALS) {
                $value['SUBTOTAL_CHARGES'] = $subtotalsRecords[$recordIndex]['SUBTOTAL_CHARGES'];
                $value['SUBTOTAL_DISCOUNT'] = ibcabs($subtotalsRecords[$recordIndex]['SUBTOTAL_DISCOUNT']);
                $value['SUBTOTAL_TAX'] = $subtotalsRecords[$recordIndex]['SUBTOTAL_TAX'];
            }
        }
        // re-index from 0, for client side js to treat returned value as an array
        $values = array_values($values);
    }
    
    /**
     * Get prentry records for PCL
     *
     * @return array|false|mixed|null
     */
    protected function getPREntriesForPCL()
    {
        $pclPREntries = $this->invokeQuery(CRE360ViewQueries::KEY_PCL_PRENTRIES,
                                           null, $this->projectContractKey, $this->projectContractLineKey);
        return $pclPREntries;
    }
    
    /**
     * Get document records for PCL
     *
     * @param array $prRecordKeysForPCL
     *
     * @return array|false|mixed|null
     */
    protected function getDocumentRecordsForPCL(array $prRecordKeysForPCL)
    {
        $values = $this->invokeQuery(CRE360ViewQueries::KEY_PCL_DOC_RECORDS,
                                     $prRecordKeysForPCL, $this->projectContractKey);
        CRE360ViewI18N::decodeRecordData($values, CRE360ViewI18N::STATE);
        return $values;
    }
    
    /**
     * Implements getRetainageReleaseInvoices, for final 360 view
     *
     * @return array retainage release invoices array
     */
    public function getRetainageReleaseInvoices() : array
    {
        $values = $this->getRetainageReleaseEntriesForProjectContractLine();
        
        return $this->buildRetainageReleaseRecords($values);
    }
    
    /**
     * Get retainage release entry records for PCL
     *
     * @return array|false|mixed|null
     */
    protected function getRetainageReleaseEntriesForProjectContractLine()
    {
        $values = $this->invokeQuery(CRE360ViewQueries::KEY_PCL_RETAINAGERELEASE_ENTRIES,
                                     null, $this->projectContractKey, $this->projectContractLineKey);
        return $values;
    }
    
    /**
     * Implements getPayments, for final 360 view
     *
     * @return array payments array
     */
    public function getPayments() : array
    {
        $payments = $this->getPaymentsForProjectContractLine($this->projectContractKey,
                                                                   $this->projectContractLineKey) ?? null;
        
        $paymentRecords = $this->addHyperlinkToDocNoForPaymentRecords($payments);
    
        return $paymentRecords;
    }
    
    /**
     * Get payment details for project contract line
     *
     * @param string $projectContractKey project contract key
     * @param string $projectContractLineKey project contract line key
     *
     * @return array payment details
     */
    protected function getPaymentsForProjectContractLine(string $projectContractKey, string $projectContractLineKey)
    {
        // This returns an array of prentrypymtrecs.paymentkey and prrecord.recordid values
        $paymentRecords = $this->invokeQuery(CRE360ViewQueries::KEY_PCL_PAYMENTS,
                                              null, $projectContractKey, $projectContractLineKey) ?? null;
        $paymentDetails = $this->getPaymentDetails($paymentRecords);
        $this->setPaymentAmount($paymentDetails, $paymentRecords);
        // Remove zero dollar payments - Ref: IA-57750
        $nonZeroPaymentDetails = array_filter($paymentDetails, fn($paymentDetail) =>
            $paymentDetail['PAYMENTAMOUNT'] !== floatval(0));

        // re-index from 0, for client side js to treat returned value as an array
        return array_values($nonZeroPaymentDetails);
    }
    
    /**
     * Sets total payment amount for PCL in the paymentDetails records
     *
     * Drill down to the line items within each invoice for a payment record
     * that match the PCL, to sum up the total for that PCL.
     *
     * An example:
     *  Consider 2 PCLs within a PCN - PCL01, PCL02 whose payments are stored in PaymentRecord1
     *  Thus, PaymentRecord1 has 2 invoices - INV01, INVO2
     *
     *  INV01 - contains 2 line items: line1 - for PCL01 - amount 150
     *                                line2 - for PCL02 - amount 250
     *
     *  INV02 - contains 2 line items: line1 - with PCL01 - amount 75
     *                                line2 - with PCL02 - amount 90
     *
     *  The total should return:
     *   for PCL01 -  225 (150+75);
     *   for PCL02 -  340 (250+90)
     *
     * @param array $paymentDetails reference to payment details
     * @param array $paymentRecords payment records, with payment key and recordid
     *
     * @return void
     */
    protected function setPaymentAmount(array &$paymentDetails, array $paymentRecords) : void
    {
        foreach($paymentDetails as &$paymentDetail) {
            // Find all the invoice ids for this payment record
            $invoiceIds = $this->getAllInvoiceIdsForPaymentRecord($paymentDetail, $paymentRecords);
            
            // Now query the amount for each invoice record in the payment that match the PCL,
            // and fetch total payment amount to assign to paymentDetail record
            $paymentKey = $paymentDetail['RECORDNO'];
            $paymentAmount = $this->getPaymentAmount(CRE360ViewQueries::KEY_PCL_PAYMENT_AMOUNT,
                                                     $invoiceIds, $this->projectContractKey, $this->projectContractLineKey,
                                                     $paymentKey);
            $paymentDetail[self::PAYMENT_AMOUNT_FIELD] = $paymentAmount;
        }
    }
}