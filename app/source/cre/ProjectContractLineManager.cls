<?php
/**
 * Manager class for the Project Contract Line object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2021 Sage Intacct Inc.
 *
 * This document contains trade secret data that belongs to Sage Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

class ProjectContractLineManager extends HierarchicalEntityManager
{
    use GatewayEntityTrait;
    use AttachmentTrait;
    use ProjectContractBillingTaxSchedTrait;

    const BILLING_TYPE_PB = 'Progress';
    const BILLING_TYPE_TM = 'TM';

    const MAX_BILLING_TP = 'Total revised price';
    const MAX_BILLING_SA = 'Specified amount';
    const MAX_BILLING_NM = 'No maximum';

    const WF_TYPE_ORIGINAL = 'original';
    const WF_TYPE_REVISION = 'revision';
    const WF_TYPE_APPROVED_CHANGE = 'approved change';

    const RETAINAGE_PCT_LESSTHAN_AMOUNT = 100;
    const MAX_BILLING_LESSTHAN_AMOUNT = 100000000000;

    private const PROJECTCONTRACTLINE = 'PROJECTCONTRACTLINE';

    /* @var string $renamedProjectTxt */
    private static $renamedProjectTxt = '';

    /* @var string $renamedLocationTxt */
    private static $renamedLocationTxt = '';

    /* @var string $renamedProjectContractTxt */
    private static $renamedProjectContractTxt = '';

    /* @var string $renamedProjectContractLineTxt */
    private static $renamedProjectContractLineTxt = '';

    /* @var string $renamedItemTxt */
    protected static $renamedItemTxt = '';

    /** @var string[][] $projectContractMap */
    private static $projectContractMap = [];

    /** @var string[][] $projectMap */
    private static $projectMap = [];

    /** @var string[][] $locationMap */
    private static $locationMap = [];

    /** @var string[][] $itemMap */
    private static $itemMap = [];

    /** @var string[][] $employeeMap */
    private static $employeeMap = [];

    /** @var string[][] $contactMap */
    private static $contactMap = [];

    /** @var bool $summaryBillingFeatureEnabled */
    private static bool $summaryBillingFeatureEnabled = false;

    /** @var string[] $updateRequest */
    private static $updateRequest = [];

    /**
     * @param array $params the parameters of the class
     */
    public function __construct($params = array())
    {
        parent::__construct($params);

        self::init();
    }

    public static function init()
    {
        self::$renamedProjectTxt = I18N::getSingleToken('IA.PROJECT');
        self::$renamedLocationTxt = I18N::getSingleToken('IA.LOCATION');
        self::$renamedProjectContractTxt = sprintf(_('%1$s contract'), self::$renamedProjectTxt);
        self::$renamedProjectContractLineTxt = sprintf(_('%1$s line'), self::$renamedProjectContractTxt);
        self::$renamedItemTxt = I18N::getSingleToken('IA.ITEM');

        self::$projectContractMap = [];
        self::$projectMap = [];
        self::$locationMap = [];
        self::$itemMap = [];
        self::$employeeMap = [];
        self::$contactMap = [];
        self::$summaryBillingFeatureEnabled = true;
    }

    /**
     * @param string $projectContractID
     *
     * @return array|null
     */
    private static function getProjectContractInfoFromCache(string $projectContractID)
    {
        if ( isset(self::$projectContractMap[$projectContractID]) ) {
            $projectContractRec = self::$projectContractMap[$projectContractID];
        } else {
            $params = [
                'selects' => [ 'RECORDNO', 'PROJECTCONTRACTID', 'PROJECTID', 'PROJECTLOCATIONID', 'CUSTOMERID', 'STATUS' ],
                'filters' => [ [ [ 'PROJECTCONTRACTID', '=', $projectContractID ] ] ]
            ];
            $gManagerFactory = Globals::$g->gManagerFactory;
            $projectContractManager = $gManagerFactory->getManager('projectcontract');
            $projectContract = $projectContractManager->GetList($params);
            $projectContractRec = $projectContract[0];

            if ( isset($projectContractRec) && isset($projectContractRec['RECORDNO']) ) {
                self::$projectContractMap[$projectContractID] = $projectContractRec;
            }
        }

        return $projectContractRec;
    }

    /**
     * @param string $accountNo
     *
     * @return array|null
     */
    private static function getAccountInfoFromCache(string $accountNo)
    {
        $params = [
            'selects' => [ 'RECORDNO', 'ACCT_NO', 'TITLE', 'STATUS', 'CLOSEABLE', 'STATISTICAL' ],
            'filters' => [ [ [ 'ACCT_NO', '=', $accountNo ] ] ]
        ];
        $gManagerFactory = Globals::$g->gManagerFactory;
        $accountManager = $gManagerFactory->getManager('baseaccount');
        $account = $accountManager->GetList($params);
        $accountRec = $account[0];

        return $accountRec;
    }

    /**
     * @param string $projectID
     *
     * @return array|null
     */
    private static function getProjectInfoFromCache(string $projectID)
    {
        if ( isset(self::$projectMap[$projectID]) ) {
            $projectRec = self::$projectMap[$projectID];
        } else {
            $params = [
                'selects' => [ 'RECORDNO', 'PROJECTID', 'LOCATIONID', 'STATUS', 'PARENTID' ],
                'filters' => [ [ [ 'PROJECTID', '=', $projectID ] ] ]
            ];
            $gManagerFactory = Globals::$g->gManagerFactory;
            $projectManager = $gManagerFactory->getManager('project');
            $project = $projectManager->GetList($params);
            $projectRec = $project[0];

            if ( isset($projectRec) && isset($projectRec['RECORDNO']) ) {
                self::$projectMap[$projectID] = $projectRec;
            }
        }

        return $projectRec;
    }

    /**
     * @param string $projectID
     *
     * @return array
     */
    private static function getParentHierarchyForProject(string $projectID)
    {
        $projectIDList = [];

        if ( isset($projectID) && ! empty($projectID) ) {
            $projectRec = self::getProjectInfoFromCache($projectID);

            if ( isset($projectRec) && is_array($projectRec) ) {
                $parentID = $projectRec['PARENTID'];

                while ( isset($parentID) && ! empty($parentID) ) {
                    $projectIDList [] = $parentID;
                    $parentProjectRec = self::getProjectInfoFromCache($parentID);

                    if ( isset($parentProjectRec) && is_array($parentProjectRec) ) {
                        $parentID = $parentProjectRec['PARENTID'];
                    }
                }
            }
        }

        return $projectIDList;
    }

    /**
     * @param string $locationID
     *
     * @return array
     */
    private static function getParentHierarchyForLocation(string $locationID)
    {
        $locationIDList = [];

        if ( isset($locationID) && ! empty($locationID) ) {
            $locationRec = self::getLocationInfoFromCache($locationID);

            if ( isset($locationRec) && is_array($locationRec) ) {
                $parentID = $locationRec['PARENTID'];

                while ( isset($parentID) && ! empty($parentID) ) {
                    $locationIDList [] = $parentID;
                    $parentLocationRec = self::getLocationInfoFromCache($parentID);

                    if ( isset($parentLocationRec) && is_array($parentLocationRec) ) {
                        $parentID = $parentLocationRec['PARENTID'];
                    }
                    else {
                        $parentID = null;
                    }
                }
            }
        }

        return $locationIDList;
    }

    /**
     * @param string $locationID
     *
     * @return array|null
     */
    private static function getLocationInfoFromCache(string $locationID)
    {
        if ( isset(self::$locationMap[$locationID]) ) {
            $locationRec = self::$locationMap[$locationID];
        } else {
            $params = [
                'selects' => [ 'RECORDNO', 'LOCATIONID', 'PARENTID' ],
                'filters' => [ [ [ 'LOCATIONID', '=', $locationID ] ] ]
            ];
            $gManagerFactory = Globals::$g->gManagerFactory;
            $locationManager = $gManagerFactory->getManager('location');
            $location = $locationManager->GetList($params);
            $locationRec = $location[0];

            if ( isset($locationRec) && isset($locationRec['RECORDNO']) ) {
                self::$locationMap[$locationID] = $locationRec;
            }
        }

        return $locationRec;
    }

    /**
     * @param string $itemID
     *
     * @return array|null
     */
    private static function getItemInfoFromCache(string $itemID)
    {
        if ( isset(self::$itemMap[$itemID]) ) {
            $itemRec = self::$itemMap[$itemID];
        } else {
            $params = [
                'selects' => [ 'RECORDNO', 'ITEMID', 'NAME', 'STATUS', 'ITEMTYPE' ],
                'filters' => [ [ [ 'ITEMID', '=', $itemID ] ] ]
            ];
            $gManagerFactory = Globals::$g->gManagerFactory;
            $itemManager = $gManagerFactory->getManager('item');
            $item = $itemManager->GetList($params);
            $itemRec = $item[0];

            if ( isset($itemRec) && isset($itemRec['RECORDNO']) ) {
                self::$itemMap[$itemID] = $itemRec;
            }
        }

        return $itemRec;
    }

    /**
     * @param string $employeeID
     *
     * @return array|null
     */
    private static function getEmployeeInfoFromCache(string $employeeID)
    {
        if ( isset(self::$employeeMap[$employeeID]) ) {
            $employeeRec = self::$employeeMap[$employeeID];
        } else {
            $params = [
                'selects' => [ 'RECORDNO', 'EMPLOYEEID', 'STATUS' ],
                'filters' => [ [ [ 'EMPLOYEEID', '=', $employeeID ] ] ]
            ];
            $gManagerFactory = Globals::$g->gManagerFactory;
            $employeeManager = $gManagerFactory->getManager('employee');
            $employee = $employeeManager->GetList($params);
            $employeeRec = $employee[0];

            if ( isset($employeeRec) && isset($employeeRec['RECORDNO']) ) {
                self::$employeeMap[$employeeID] = $employeeRec;
            }
        }

        return $employeeRec;
    }

    /**
     * @param string $contactName
     *
     * @return array|null
     */
    private static function getContactInfoFromCache(string $contactName)
    {
        if ( isset(self::$contactMap[$contactName]) ) {
            $contactRec = self::$contactMap[$contactName];
        } else {
            $params = [
                'selects' => [ 'RECORDNO', 'CONTACTNAME', 'STATUS' ],
                'filters' => [ [ [ 'CONTACTNAME', '=', $contactName ] ] ]
            ];
            $gManagerFactory = Globals::$g->gManagerFactory;
            $contactManager = $gManagerFactory->getManager('contact');
            $contact = $contactManager->GetList($params);
            $contactRec = $contact[0];

            if ( isset($contactRec) && isset($contactRec['RECORDNO']) ) {
                self::$contactMap[$contactName] = $contactRec;
            }
        }

        return $contactRec;
    }

    /**
     * @param array &$values for processing
     *
     * @return bool
     */
    private function translateProjectContract(array &$values) : bool
    {
        $ok = true;
        $gErr = Globals::$g->gErr;

        if ( isset($values['PROJECTCONTRACTID']) && $values['PROJECTCONTRACTID'] != '' ) {
            [ $projectContractID ] = explode('--', $values['PROJECTCONTRACTID']);

            $projectContractRec = self::getProjectContractInfoFromCache($projectContractID);

            if ( isset($projectContractRec) && isset($projectContractRec['RECORDNO']) ) {
                if ( $projectContractRec['STATUS'] == 'active' ) {
                    $values['PROJECTCONTRACTKEY'] = $projectContractRec['RECORDNO'];
                    $values['HDR_PROJECTID'] = $projectContractRec['PROJECTID'];
                } else {
                    $gErr->addIAError(
                        number: 'CRE-0334',
                        source: __FILE__ . ':' . __LINE__,
                        desc1PHs: ['PROJECT_CONTRACT' => strtolower(self::$renamedProjectContractTxt)]
                    );
                    $ok = false;
                }
            } else {
                $gErr->addIAError(
                    number: 'CRE-0335',
                    source: __FILE__ . ':' . __LINE__,
                    desc1PHs: ['PROJECT_CONTRACT' => strtolower(self::$renamedProjectContractTxt)]
                );
                $ok = false;
            }
        } else {
            $gErr->addIAError(
                number: 'CRE-0335',
                source: __FILE__ . ':' . __LINE__,
                desc1PHs: ['PROJECT_CONTRACT' => strtolower(self::$renamedProjectContractTxt)]
            );
            $ok = false;
        }

        return $ok;
    }

    /**
     * @param array &$values for processing
     *
     * @return bool
     */
    private function translateParent(array &$values) : bool
    {
        $ok = true;

        $gErr = Globals::$g->gErr;
        $gManagerFactory = Globals::$g->gManagerFactory;

        if ( isset($values['PARENTID']) && $values['PARENTID'] != '' ) {
            [ $parentID ] = explode('--', $values['PARENTID']);
            // Check valid parent-child relation
            if ( isset($values['PROJECTCONTRACTLINEID'])
                 && $values['PROJECTCONTRACTLINEID'] != ''
                 && $parentID == $values['PROJECTCONTRACTLINEID']
            ) {
                $gErr->addIAError(
                    number: 'CRE-0336',
                    source: __FILE__ . ':' . __LINE__
                );

                $ok = false;
            }

            if ( $ok ) {
                // Make sure we have the parent PCL
                $params = [
                    'selects' => [ 'RECORDNO', 'PROJECTCONTRACTLINEID' ],
                    'filters' => [
                        [
                            [ 'PROJECTCONTRACTLINEID', '=', $parentID ],
                            [ 'PROJECTCONTRACTID', '=', $values['PROJECTCONTRACTID'] ],
                            [ 'STATUS', '=', 'active' ],
                        ]
                    ]
                ];
                $pclMgr = $gManagerFactory->getManager('projectcontractline');
                $parentPCL = $pclMgr->GetList($params);

                // If the PCL does not exist throw an error
                if ( ! isset($parentPCL[0]) ) {
                    $gErr->addIAError(
                        number: 'CRE-0337',
                        source: __FILE__ . ':' . __LINE__,
                        desc1PHs: ['PROJECT_CONTRACT_LINE' => strtolower(self::$renamedProjectContractLineTxt)]
                    );

                    $ok = false;
                }

                $values['PARENTKEY'] = $parentPCL[0]['RECORDNO'];

                // Check valid parent-child relation
                if ( isset($values['PROJECTCONTRACTLINEID'])
                     && $values['PROJECTCONTRACTLINEID'] != ''
                     && isset($values['RECORDNO']) // We only want to validate the hierarchy if there is already a hierarchy
                     && ! $this->IsValidHierarchy($values['RECORDNO'], $values['PARENTKEY'])
                ) {
                    $ok = false;
                }
            }
        } else if ( isset($values['PARENTID']) && $values['PARENTID'] === '' ) {
            $values['PARENTKEY'] = null;
        }

        return $ok;
    }

    /**
     * @param array &$values for processing
     *
     * @return bool
     */
    private function translateGLAccount(array &$values) : bool
    {
        $ok = true;
        $gErr = Globals::$g->gErr;

        $accountSupplied = isset($values['ACCOUNTNO']) && $values['ACCOUNTNO'] != '';
        if ( $accountSupplied ) {
            [ $accountNo ] = explode('--', $values['ACCOUNTNO']);

            $accountRec = self::getAccountInfoFromCache($accountNo);

            if ( isset($accountRec) && isset($accountRec['RECORDNO']) ) {
                $values['ACCOUNTKEY'] = $accountRec['RECORDNO'];
                $values['ACCOUNT_STATUS'] = $accountRec['STATUS'];
                $values['CLOSEABLE_ACCOUNT'] = $accountRec['CLOSEABLE'];
                $values['STATISTICAL_ACCOUNT'] = $accountRec['STATISTICAL'];
            } else {
                $gErr->addIAError(
                    number: 'CRE-0338',
                    source: __FILE__ . ':' . __LINE__,
                    desc1PHs: ['PROJECT_CONTRACT_LINE' => strtolower(self::$renamedProjectContractLineTxt)]
                );
                $ok = false;
            }
        } else {
            if ( isset($values['ACCOUNTNO']) && $values['ACCOUNTNO'] === '' ) {
                $values['ACCOUNTKEY'] = null;
                $values['ACCOUNT_STATUS'] = null;
                $values['CLOSEABLE_ACCOUNT'] = null;
                $values['STATISTICAL_ACCOUNT'] = null;
            }
        }

        return $ok;
    }

    /**
     * @param array &$values for processing
     *
     * @return bool
     */
    private function translateRetainagePercent(array &$values) : bool
    {
        $ok = true;

        if ( ! isset($values['RETAINAGEPERCENTAGE']) || $values['RETAINAGEPERCENTAGE'] == '' ) {
            $values['RETAINAGEPERCENTAGE'] = 0.00;
        }

        return $ok;
    }

    /**
     * @param array &$values for processing
     *
     * @return bool
     */
    private function translateBillingType(array &$values) : bool
    {
        $ok = true;
        $gErr = Globals::$g->gErr;

        // TODO: BILLINGTYPE is a required field. Should we return an error or set the BILLINGTYPE to a default value when BILLINGTYPE is not supplied?
        // TODO Consider doing this translation ahead of time to avoid the generic validation for required fields
        if ( ! isset($values['BILLINGTYPE']) || $values['BILLINGTYPE'] == '' ) {
            // $values['BILLINGTYPE'] = BILLTYPE_PROGRESS;
            $gErr->addIAError(
                number: 'CRE-0339',
                source: __FILE__ . ':' . __LINE__,
                desc1PHs: ['PROJECT_CONTRACT_LINE' => strtolower(self::$renamedProjectContractLineTxt)]
            );
            $ok = false;
        }

        return $ok;
    }

    /**
     * @param array &$values for processing
     *
     * @return bool
     */
    private function translateMaxBilling(array &$values) : bool
    {
        $ok = true;
        $gErr = Globals::$g->gErr;

        // TODO: MAXIMUMBILLING is a required field. Should we return an error or set the MAXIMUMBILLING to a default value when MAXIMUMBILLING is not supplied?
        if ( $ok && ( ! isset($values['MAXIMUMBILLING']) || $values['MAXIMUMBILLING'] == '' ) ) {
            // $values['MAXIMUMBILLING'] = MAXBILL_TOTAL;                           );
            $gErr->addIAError(
                number: 'CRE-0340',
                source: __FILE__ . ':' . __LINE__,
                desc1PHs: ['PROJECT_CONTRACT_LINE' => strtolower(self::$renamedProjectContractLineTxt)]
            );
            $ok = false;
        }else{
            if(isset($values['MAXIMUMBILLING']) && $values['MAXIMUMBILLING'] != self::MAX_BILLING_SA){
                $values['MAXIMUMBILLINGAMOUNT'] = 0.00;
            }
        }

        $ok = $ok && $this->validateRetainagePercentage($values);
        $ok = $ok && $this->validateMaximumBillingAmount($values);

        if ( $ok && $values['BILLINGTYPE'] == self::BILLING_TYPE_PB ) {
            if ( $values['MAXIMUMBILLING'] != self::MAX_BILLING_TP ) {
                $gErr->addIAError(
                    number: 'CRE-0341',
                    source: __FILE__ . ':' . __LINE__,
                    desc1PHs: [
                            'PROJECT_CONTRACT_LINE' => strtolower(self::$renamedProjectContractLineTxt),
                            'MAXIMUMBILLING' => $values['MAXIMUMBILLING'],
                            'BILLINGTYPE' => $values['BILLINGTYPE'],
                            'MAX_BILLING_TYPE' => self::MAX_BILLING_TP,
                            'BILLING_TYPE_TO' => self::BILLING_TYPE_TM
                    ]
                );
                $ok = false;
            }
        }

        return $ok;
    }

    /**
     * Ensures retainage percentage is not over 100% and non-negative
     *
     * @param array  $values
     *
     * @return bool
     */
    protected function validateRetainagePercentage(array $values) : bool
    {
        $ok = true;
        $gErr = Globals::$g->gErr;

        $retainagePercentage = $values['RETAINAGEPERCENTAGE'] ?? 0;

        // Check if value is too high
        if ( $ok && $retainagePercentage > 100 ) {
            $gErr->addIAError(
                number: 'CRE-0342',
                source: __FILE__ . ':' . __LINE__
            );
            $ok = false;
        }

        // Check if value is negative
        if ( $ok && $retainagePercentage < 0 ) {
            $gErr->addIAError(
                number: 'CRE-0343',
                source: __FILE__ . ':' . __LINE__
            );
            $ok = false;
        }

        return $ok;
    }

    /**
     * Ensures maximum billing amount is not over 100,000,000,000 and non-negative
     *
     * @param array  $values
     *
     * @return bool
     */
    protected function validateMaximumBillingAmount(array $values) : bool
    {
        $ok = true;
        $gErr = Globals::$g->gErr;

        $maximumBillingAmount = $values['MAXIMUMBILLINGAMOUNT'] ?? 0;

        // Check if value is too high
        if ( $ok && $maximumBillingAmount > self::MAX_BILLING_LESSTHAN_AMOUNT ) {
            $gErr->addIAError(
                number: 'CRE-0344',
                source: __FILE__ . ':' . __LINE__,
                desc1PHs: ['AMOUNT' => Currency(self::MAX_BILLING_LESSTHAN_AMOUNT)]
            );
            $ok = false;
        }

        // Check if value is negative
        if ( $ok && $maximumBillingAmount < 0 ) {
            $gErr->addIAError(
                number: 'CRE-0345',
                source: __FILE__ . ':' . __LINE__
            );
            $ok = false;
        }

        return $ok;
    }

    /**
     * @param array &$values for processing
     *
     * @return bool
     */
    private function translateMaxBillingAmt(array &$values) : bool
    {
        $ok = true;
        $gErr = Globals::$g->gErr;

        // If the MAXIMUMBILLINGAMOUNT is not supplied or is set to an empty value, then set it to 0.00
        // PHP8_NUMERIC_STRING_COMPARE; Priority: low; Behavior: same, Risk: low, Solution: php7eqEmptyStr
        if ( Util::php7eqEmptyStr($values['MAXIMUMBILLINGAMOUNT'] ?? '')  ) {
            $values['MAXIMUMBILLINGAMOUNT'] = 0.00;
        }

        if ( $ok && $values['MAXIMUMBILLINGAMOUNT'] < 0 ) {
            $gErr->addIAError(
                number: 'CRE-0346',
                source: __FILE__ . ':' . __LINE__
            );
            $ok = false;
        }

        return $ok;
    }

    /**
     * @param array &$values for processing
     *
     * @return bool
     */
    private function translateSummarizeBill(array &$values) : bool
    {
        $ok = true;

        if ( ! self::$summaryBillingFeatureEnabled || $values['BILLINGTYPE'] == self::BILLING_TYPE_PB ) {
            $values['SUMMARIZEBILL'] = 'false';
        }

        return $ok;
    }

    /**
     * @param array &$values for processing
     *
     * @return bool
     */
    private function translateProject(array &$values) : bool
    {
        $ok = true;
        $gErr = Globals::$g->gErr;

        if ( isset($values['PROJECTID']) && $values['PROJECTID'] != '' ) {
            [ $projectID ] = explode('--', $values['PROJECTID']);

            $projectRec = self::getProjectInfoFromCache($projectID);

            if ( isset($projectRec) && isset($projectRec['RECORDNO']) ) {
                if ( $projectRec['STATUS'] == 'active' ) {
                    if ( IsMultiEntityCompany() && ! GetContextLocation() && empty($projectRec['LOCATIONID']) ) {
                        $gErr->addIAError(
                            number: 'CRE-0305',
                            source: __FILE__ . ':' . __LINE__,
                            desc1PHs: ['PROJECT' => strtolower(self::$renamedProjectTxt)]
                        );
                        $ok = false;
                    } else {
                        $values['PROJECTDIMKEY'] = $projectRec['RECORDNO'];
                    }
                } else {
                    $gErr->addIAError(
                        number: 'CRE-0304',
                        source: __FILE__ . ':' . __LINE__,
                        desc1PHs: ['PROJECT' => strtolower(self::$renamedProjectTxt)]
                    );
                    $ok = false;
                }
            } else {
                $gErr->addIAError(
                    number: 'CRE-0303',
                    source: __FILE__ . ':' . __LINE__,
                    desc1PHs: ['PROJECT' => strtolower(self::$renamedProjectTxt)]
                );
                $ok = false;
            }
        } else {
            $gErr->addIAError(
                number: 'CRE-0303',
                source: __FILE__ . ':' . __LINE__,
                desc1PHs: ['PROJECT' => strtolower(self::$renamedProjectTxt)]
            );
            $ok = false;
        }

        return $ok;
    }

    /**
     * @param array &$values for processing
     *
     * @return bool
     */
    private function translateDepartment(array &$values) : bool
    {
        $ok = true;

        if ( isset($values['DEPARTMENTID']) && $values['DEPARTMENTID'] != '' ) {
            $departmentIdAndName = $values['DEPARTMENTID'];
            [ $departmentIDValue ] = explode('--', $values['DEPARTMENTID']);
            $values['DEPARTMENTID'] = $departmentIDValue;
            $ok = $ok
                  && $this->translateAndSetKeyForObject(
                    $values,
                    'department',
                    'DEPARTMENTID',
                    'DEPARTMENTKEY',
                    'department',
                    __FILE__ . '.' . __LINE__
                );
            $values['DEPARTMENTID'] = $departmentIdAndName;
        } else {
            $values['DEPARTMENTKEY'] = null;
        }

        return $ok;
    }

    /**
     *
     * @param array &$values
     * @param true $ignoreEmpty
     * @return bool
     */
    public function translateDimensionFields(&$values, $ignoreEmpty = true) : bool
    {
        // If user provides a blank value for dimension field in XML API request,
        // the corresponding dimkey field value also needs to be null.
        // Dimension fields that aren't nullable should already be checked before this function
        $dimensions = IADimensions::getAllDimensionObjectProperties();
        foreach( $dimensions as $field )
        {
            $fieldId = strtoupper($field['path']);
            $val = $values[$fieldId];

            if (!isset($val) || $val === '') {
                $values[strtoupper($field['dimdbkey'])] = null;
            }
        }

        return parent::translateDimensionFields($values, $ignoreEmpty);
    }

    /**
     * Translate all rate table fields from ID to Key
     */
    protected function translateRateTables(array &$values) : bool
    {
        static $rtMapCache = [];

        $ok = true;
        $gErr = Globals::$g->gErr;
        $fields = [
            'DEFAULTRATETABLE',
            'TSRATETABLE',
            'PORATETABLE',
            'APRATETABLE',
            'GLRATETABLE',
            'CCRATETABLE',
            'EERATETABLE',
        ];

        $object = 'ratetable';
        foreach ( $fields as $field ) {
            $vidName = $field . 'ID';
            $keyName = $field . 'KEY';

            // If the vid key exists then clear the key
            if ( array_key_exists( $vidName, $values) ) {
                $values[$keyName] = null;
            }
            $vidValue = $values[$vidName] ?? null;

            if ( ! $vidValue ) {
                continue;
            }

            $values[$vidName] = explode('--', $vidValue)[0];
            if ( $this->fromGateway && $values['BILLINGTYPE'] !== self::BILLING_TYPE_TM ) {
                $ok = false;
                $gErr->addIAError(
                    number: 'CRE-0347',
                    source: __FILE__ . ':' . __LINE__,
                    desc1PHs: [
                            'RATE_TABLE' => $vidName,
                            'PROJECT_CONTRACT_LINE' => strtolower(self::$renamedProjectContractLineTxt)
                    ]
                );
                break;
            }

            // Use a cache so we don't lookup the same values
            if ( ! empty($rtMapCache[$vidValue]) ) {
                $values[$keyName] = $rtMapCache[$vidValue];
                continue;
            }

            $ok = $ok
                  && $this->translateAndSetKeyForObject(
                    $values,
                    $object,
                    $vidName,
                    $keyName,
                    $object,
                    __FILE__ . '.' . __LINE__
                );

            if ( $ok ) {
                // Save vid to key mapping in cache
                $rtMapCache[$vidValue] = $values[$keyName];
            } else {
                break;
            }
        }

        return $ok;
    }

    /**
     * @param string $pclProjectID
     * @param string $contractProjectID
     *
     * @return bool
     */
    private function validateSameProjectTree(string $pclProjectID, string $contractProjectID) : bool
    {
        $ok = true;
        $gErr = Globals::$g->gErr;

        [ $pclProjectID ] = explode('--', $pclProjectID);

        if ( ! strcmp($pclProjectID, $contractProjectID) == 0 ) {
            $ancestorProjects = self::getParentHierarchyForProject($pclProjectID);
            $isDescendant = in_array($contractProjectID, $ancestorProjects);

            if ( ! $isDescendant ) {
                $gErr->addIAError(
                    number: 'CRE-0348',
                    source: __FILE__ . ':' . __LINE__,
                    desc1PHs: [
                            'PROJECT' => strtolower(self::$renamedProjectTxt),
                            'PROJECT_CONTRACT' => strtolower(self::$renamedProjectContractTxt)
                    ]
                );
                $ok = false;
            }
        }

        return $ok;
    }

    /**
     * @param string $pclProjectID
     * @param string $pcnProjectID
     *
     * @return bool
     */
    private static function validateSameProjectLocationTree(string $pclProjectID, string $pcnProjectID) : bool
    {
        $ok = true;
        $gErr = Globals::$g->gErr;

        $gManagerFactory = Globals::$g->gManagerFactory;
        $projectManager = $gManagerFactory->getManager('project');

        $pclLocationID = $projectManager->get($pclProjectID)['LOCATIONID'];
        $pcnLocationID = $projectManager->get($pcnProjectID)['LOCATIONID'];
        if (empty($pclLocationID)) {
            $gErr->addIAError(
                number: 'CRE-3325',
                source: __FILE__ . ':' . __LINE__,
                desc1PHs: [
                            'PROJECT_CONTRACT_LINE' => strtolower(self::$renamedProjectContractLineTxt),
                            'PROJECT' => strtolower(self::$renamedProjectTxt),
                            'LOCATION' => strtolower(self::$renamedLocationTxt),
                        ]
            );
            $ok = false;
        } else if ( ! strcmp($pclLocationID, $pcnLocationID) == 0 ) {
            $ancestorLocations = self::getParentHierarchyForLocation($pclLocationID);
            $isDescendant = in_array($pcnLocationID, $ancestorLocations);

            if ( ! $isDescendant ) {
                $gErr->addIAError(
                    number: 'CRE-0349',
                    source: __FILE__ . ':' . __LINE__,
                    desc1PHs: [
                            'PROJECT_CONTRACT_LINE' => strtolower(self::$renamedProjectContractLineTxt),
                            'PROJECT' => strtolower(self::$renamedProjectTxt),
                            'LOCATION' => strtolower(self::$renamedLocationTxt),
                            'PROJECT_CONTRACT' => strtolower(self::$renamedProjectContractTxt)
                    ]
                );
                $ok = false;
            }
        }

        return $ok;
    }

    /**
     * validates that the change request can only be created at the same level as the project ownership
     *
     * @param string $projectContractID
     *
     * @return bool
     */
    public function validateProjectContractMultiEntityOwnership(string $projectContractID)
    {
        $ok = true;
        $gErr = Globals::$g->gErr;

        if ( IsMultiEntityCompany() ) {
            $contextLocation = GetContextLocation();
            if ( $contextLocation != '' ) {
                if ( isset($projectContractID) && $projectContractID != '' ) {
                    $query = "select nvl(locationkey, -1) locationkey from projectcontractmst where cny# = :1 and projectcontractid = :2";
                    $resultSet = QueryResult(array($query, GetMyCompany(), $projectContractID));

                    if ($resultSet[0]['LOCATIONKEY'] != $contextLocation) {
                        $gErr->addIAError(
                            number: 'CRE-0350',
                            source: __FILE__ . ':' . __LINE__,
                            desc1PHs: [
                                    'PROJECT_CONTRACT_LINE' => strtolower(self::$renamedProjectContractLineTxt),
                                    'PROJECT_CONTRACT' => strtolower(self::$renamedProjectContractTxt)
                            ]
                        );
                        $ok = false;
                    }
                }
            }
        }

        return $ok;
    }

    /**
     * validate $values for processing
     *
     * @param array &$values
     *
     * @return bool
     */
    private function translateItem(array &$values) : bool
    {
        $ok = true;
        $gErr = Globals::$g->gErr;

        if ( isset($values['ITEMID']) && $values['ITEMID'] != '' ) {
            [ $itemID ] = explode('--', $values['ITEMID']);

            $itemRec = self::getItemInfoFromCache($itemID);

            if ( isset($itemRec) && isset($itemRec['RECORDNO']) ) {
                if ( $itemRec['STATUS'] == 'active' ) {
                    if ( in_array($itemRec['ITEMTYPE'], [ NONINV, NONINV_SO]) ) {
                        $values['ITEMDIMKEY'] = $itemRec['RECORDNO'];
                    } else {
                        // Only Non-Inventory and Non-Inventory (Sales only) item types are valid for project contract lines
                        $gErr->addIAError(
                            number: 'CRE-0351',
                            source: __FILE__ . ':' . __LINE__,
                            desc1PHs: [
                                    'PROJECT_CONTRACT_LINE' => strtolower(self::$renamedProjectContractLineTxt),
                                    'NONINV' => NONINV,
                                    'NONINV_SO' => NONINV_SO,
                                    'ITEM' => strtolower(self::$renamedItemTxt)
                            ]
                        );
                        $ok = false;
                    }
                } else {
                    $gErr->addIAError(
                        number: 'CRE-0352',
                        source: __FILE__ . ':' . __LINE__,
                        desc1PHs: ['ITEM' => strtolower(self::$renamedItemTxt)]
                    );
                    $ok = false;
                }
            } else {
                $gErr->addIAError(
                    number: 'CRE-0353',
                    source: __FILE__ . ':' . __LINE__,
                    desc1PHs: ['ITEM' => strtolower(self::$renamedItemTxt)]
                );
                $ok = false;
            }
        } else {
            $gErr->addIAError(
                number: 'CRE-0353',
                source: __FILE__ . ':' . __LINE__,
                desc1PHs: ['ITEM' => strtolower(self::$renamedItemTxt)]
            );
            $ok = false;
        }

        return $ok;
    }

    /**
     * @param array  $values
     * @param string $employeeIDFieldName
     * @param string $employeeKeyFieldName
     * @param string $fieldDesc
     * @param string $source
     *
     * @return bool
     */
    private function translateAndSetKeyForEmployeeField(array &$values, string $employeeIDFieldName, string $employeeKeyFieldName, string $fieldDesc, string $source)
    {
        $ok = true;
        $gErr = Globals::$g->gErr;

        if ( isset($values[$employeeIDFieldName]) && $values[$employeeIDFieldName] != '' ) {
            [ $employeeID ] = explode('--', $values[$employeeIDFieldName]);

            $employeeRec = self::getEmployeeInfoFromCache($employeeID);

            if ( isset($employeeRec) && isset($employeeRec['RECORDNO']) ) {
                if ( $employeeRec['STATUS'] == 'active' ) {
                    $values[$employeeKeyFieldName] = $employeeRec['RECORDNO'];
                } else {
                    $gErr->addIAError(
                        number: 'CRE-0312',
                        source: $source,
                        desc1PHs: ['FIELD' => $fieldDesc]
                    );
                    $ok = false;
                }
            } else {
                $gErr->addIAError(
                    number: 'CRE-0313',
                    source: $source,
                    desc1PHs: ['FIELD' => $fieldDesc]
                );
                $ok = false;
            }
        } else if ( isset($values[$employeeIDFieldName]) && $values[$employeeIDFieldName] === '' ) {
            $values[$employeeKeyFieldName] = null;
        }

        return $ok;
    }

    /**
     * @param array  $values
     * @param string $contactNameFieldName
     * @param string $contactKeyFieldName
     * @param string $fieldDesc
     * @param string $source
     *
     * @return bool
     */
    private function translateAndSetKeyForContactField(array &$values, string $contactNameFieldName, string $contactKeyFieldName, string $fieldDesc, string $source)
    {
        $ok = true;
        $gErr = Globals::$g->gErr;

        if ( isset($values[$contactNameFieldName]) && $values[$contactNameFieldName] != '' ) {
            [ $contactName ] = explode('--', $values[$contactNameFieldName]);

            $contactRec = self::getContactInfoFromCache($contactName);

            if ( isset($contactRec) && isset($contactRec['RECORDNO']) ) {
                if ( $contactRec['STATUS'] == 'active' ) {
                    $values[$contactKeyFieldName] = $contactRec['RECORDNO'];
                } else {
                    $gErr->addIAError(
                        number: 'CRE-0314',
                        source: $source,
                        desc1PHs: ['FIELD' => $fieldDesc]
                    );
                    $ok = false;
                }
            } else {
                $gErr->addIAError(
                    number: 'CRE-0315',
                    source: $source,
                    desc1PHs: ['FIELD' => $fieldDesc]
                );
                $ok = false;
            }
        } else if ( isset($values[$contactNameFieldName]) && $values[$contactNameFieldName] === '' ) {
            $values[$contactKeyFieldName] = null;
        }

        return $ok;
    }


    /**
     * Gets change request values to be used to set dimension values later and leaves $values unchanged for
     * entity manager to handle XML API requests
     *
     * @param string         $objectName
     * @param array          $values
     * @param bool           $isDocType
     * @param IAIssueHandler $gErr
     *
     * @return array|null
     */
    public function doUpdateForAPI(string $objectName, $values, bool $isDocType, IAIssueHandler $gErr) {
        self::$updateRequest = $values;
        return parent::doUpdateForAPI($objectName, $values, $isDocType, $gErr);
    }

    /**
     * Sets up default values for each PCL Entry
     *
     * @@param array &$values the PCL data
     *
     * @return bool
     */
    private function setProjectContractLineEntries(array &$values) : bool
    {
        $ok = true;

        if ($ok && is_array($values) && isset($values['ITEMS']) && !isNullOrBlank($values['ITEMS']) && is_array($values['ITEMS'])) {
            // If $updateRequest has been set for REST or XML API call, $values needs to be checked and updated for
            // user proved PCL dimension changes
            if (isNonEmptyArray(self::$updateRequest)) {
                $this->updatePCLEDimensions($values);
            }
            foreach ($values['ITEMS'] as &$item ) {
                $this->setDefaultValuesForProjectContractLineEntry($item, $values);
            }
            unset($item);
        }

        return $ok;
    }

    /**
     * Updates PCLE dimension fields with values from the change request
     *
     * @@param array &$values the PCL data
     *
     * @return void
     */
    private function updatePCLEDimensions(array &$values) : void
    {
        // Retrieve the API user update request from merged record data in order to determine which dimension values
        // the API user provided.
        $updateFields = self::$updateRequest;
        $updateEntries = [];

        // Entries are called 'ITEMS' when coming from REST/XML API create and REST API update
        // Entries are called 'PROJECTCONTRACTLINEENTRIES' when coming from the XML API update
        // Need to restructure request from REST and XML API calls to be the same
        if (!is_null($updateFields['ITEMS'])) {
            $updateEntries = $updateFields['ITEMS'];
        } else if (!is_null($updateFields['PROJECTCONTRACTLINEENTRIES']['PROJECTCONTRACTLINEENTRY'])) {
            $updateEntries = $updateFields['PROJECTCONTRACTLINEENTRIES']['PROJECTCONTRACTLINEENTRY'];
        }

        $dimensionIDList = [];
        $dimensionObjList = IADimensions::getAllDimensionObjectProperties();
        // Get list of dimension IDs
        foreach( $dimensionObjList as $dimensionObj )
        {
            array_append($dimensionIDList, strtoupper($dimensionObj['path']));
        }

        // Use the update request to unset PCLE dimension fields that the user did not provide values for
        for ($i = 0; $i < count($values['ITEMS']); $i++) {
            foreach ($updateFields as $updateField => $value) {
                if (in_array($updateField, $dimensionIDList) && is_null($updateEntries[array_keys($updateEntries)[$i]][$updateField])) {
                    unset($values['ITEMS'][$i][$updateField]);
                }
            }
        }
    }

    /**
     * Sets up default values for a single PCL Entry by copying data from the project contract line
     *
     * @param array &$item   the PCL Entry data
     * @@param array $values the PCL data
     */
    private function setDefaultValuesForProjectContractLineEntry(array &$item, array $values) : void
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $pcleManager = $gManagerFactory->getManager('projectcontractlineentry');

        $oldValues = false;
        if (isset($item['RECORDNO']))
        {
            $oldValues = $pcleManager->get($item['RECORDNO']);
        }

        if (!$oldValues) {
            // Push the Project Contract Line key from the header to the detail.
            $item['PROJECTCONTRACTLINEKEY'] = $values['RECORDNO'];
        }
        else {
            $item['RECORDTYPE'] = $oldValues['RECORDTYPE'];
        }

        // Set default values for new items.
        if (!isset($item['RECORDTYPE']) || isNullOrBlank($item['RECORDTYPE'])) {
            if (!isset($item['CHANGEREQUESTID']) || isNullOrBlank($item['CHANGEREQUESTID'])) {
                $item['RECORDTYPE'] = 'cl';
            } else {
                $item['RECORDTYPE'] = 'cr';
            }
        }

        // these two fields exist in the CRDETAIL database but are not used by PCL entries. Both fields have NN constraints
        // setting both fields to 0 to bypass constraint
        if (!isset($item['PRODUCTIONUNITS']) || isNullOrBlank($item['PRODUCTIONUNITS'])) {
            $item['PRODUCTIONUNITS'] = 0;
        }
        if (!isset($item['COST']) || isNullOrBlank($item['COST'])) {
            $item['COST'] = 0;
        }
        $this->setDimensionsForProjectContractLineEntry($item, $values);
        $this->setLocationForProjectContractLineEntry($item, $values);
    }
    
    /**
     *
     * Sets the location for PCL entry, necessary for API save/update
     *
     * @param array $item Reference to a PCL entry
     * @param array $values Project contract line
     */
    private function setLocationForProjectContractLineEntry(array &$item, array $values)
    {
        $projectContractRec = self::getProjectContractInfoFromCache($values['PROJECTCONTRACTID']);
        if ( isset($projectContractRec) && isset($projectContractRec['RECORDNO']) ) {
            $item['LOCATIONID'] = $projectContractRec['PROJECTLOCATIONID'];;
        }
    }

    /**
     *
     * Sets the dimension values from PCL if values are not provided in the PCL entry
     *
     * @param array $item Reference to a PCL entry
     * @param array $values Project contract line
     */
    private function setDimensionsForProjectContractLineEntry(array &$item, array $values)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $projectManager = $gManagerFactory->getManager('project');

        // In the PCL, the CUSTOMERID is derived from the project contract. If user provides PROJECTID in
        // the PCL without a CUSTOMERID in the PCLE, then the CUSTOMERID in the PCLE should also be derived
        // from the project contract.
        if (is_null($item['CUSTOMERID']) && !isNullOrBlank($values['PROJECTID'])) {
            $item['CUSTOMERID'] = $projectManager->get($values['PROJECTID'])['CUSTOMERID'];
        }

        // Overwrite PCLE dimensions that don't have values or are blank from PCL dimensions
        $dimensionObjList = IADimensions::getAllDimensionObjectProperties();
        foreach ($dimensionObjList as $dimensionObj) {
            $dimensionId = strtoupper($dimensionObj['path']);
            $dimensionKey = strtoupper($dimensionObj['dimdbkey']);
            // Set entry dimension IDs and keys with header dimension IDs and keys
            if (is_null($item[$dimensionId]) && !is_null($values[$dimensionId])) {
                $item[$dimensionId] = $values[$dimensionId];
                // Use 'DEPARTMENTKEY' instead of the 'DEPTKEY' value that returns from IADimensions call
                if ($dimensionId == 'DEPARTMENTID') {
                    $item['DEPARTMENTKEY'] = $values['DEPARTMENTKEY'];
                } else {
                    $item[$dimensionKey] = $values[$dimensionKey];
                }
            }
        }
    }

    /**
     * add a PCL record to the database from a PCN duplicate
     *
     * @param array &$values the PCL data
     *
     * @return bool false if error, else true
     */
    public function addFromPCNDuplicate(&$values)
    {
        $values['DUPLICATINGPROJECTCONTRACT'] = true;

        return $this->regularAdd($values);
    }

    /**
     * add a PCL record to the database
     *
     * @param array &$values the PCL data
     *
     * @return bool false if error, else true
     */
    public function regularAdd(&$values)
    {
        $source = "ProjectContractLineManager::regularAdd";
        $ok = $this->_QM->beginTrx($source);

        $duplicatingParent = isset($values['DUPLICATINGPROJECTCONTRACT']);

        ProjectContractManager::emptyLinesCache();

        // When adding a new line, ignore the total values that might get passed in.
        $totalFields = ['ORIGINALPRICE', 'REVISIONPRICE', 'FORECASTPRICE', 'APPROVEDCHANGEPRICE', 'PENDINGCHANGEPRICE', 'OTHERPRICE', 'TOTALREVISEDPRICE'];
        foreach ($totalFields as $totalField) {
            unset($values[$totalField]);
        }

        //Set total fields value to zero by default.
        $totalFields = ['BILLEDPRICE', 'RETAINAGEHELD', 'RETAINAGERELEASED', 'PAYMENTSRECEIVED'];
        foreach ($totalFields as $totalField) {
            $values[$totalField] = 0;
        }
        
        $ok = $ok && $this->translateValues($values);

        $ok = $ok && $this->validateValues($values);

        $gManagerFactory = Globals::$g->gManagerFactory;
        $pcnMgr = $gManagerFactory->getManager('projectcontract');
        $pcn = $pcnMgr->get($values['PROJECTCONTRACTID']);
        if ( isset($pcn['POSTED']) && $pcn['POSTED'] === 'true' && !$duplicatingParent) {
            $ok = $ok && $this->validatePostingPeriod($values, $pcn);
            $ok = $ok && $this->validateProjectAndCustomer($values, $pcn);
            $ok = $ok && $pcnMgr->unpostFromGL($pcn);
        }

        if(Globals::$g->gManagerFactory->getManager('document')->isVatTypeOverrideTaxSchedForEntryEnabled() && !empty($values['PCLTAXSCHEDULEID'])){
            $ok = $ok && $this->validatePclTaxSchedule($values);
        }

        $ok = $ok && parent::regularAdd($values);

        if ( isset($pcn['POSTED']) && $pcn['POSTED'] === 'true'  && !$duplicatingParent) {
            $ok = $ok && $pcnMgr->postToGL($pcn);
        }

        $ok = $ok && $this->addAttachmentMap($values, 'RECORDNO', self::PROJECTCONTRACTLINE);

        $ok = $ok && $this->_QM->commitTrx($source);
        if ( ! $ok ) {
            // do *not* add error, if it has only warnings
            if ( !HasWarnings() || HasErrors() ) {
                Globals::$g->gErr->addIAError(
                    number: 'CRE-0354',
                    source: __FILE__ . ':' . __LINE__,
                    desc1PHs: ['PROJECT_CONTRACT_LINE' => strtolower(self::$renamedProjectContractLineTxt)]
                );
            }
            $this->_QM->rollbackTrx($source);
        }

        return $ok;
    }

    /** Modify the PAYMENTSRECEIVED total by the amount of the adjustment.
     *
     * @param int $key
     * @param float $adjustment
     * @param float $invoiceExchangeRate
     * @param float $paymentExchangeRate
     *
     * @return bool
     */
    public function adjustPaymentsTotal($key, $adjustment, $invoiceExchangeRate, $paymentExchangeRate)
    {
        $pclObject = parent::getByKeys([$key]);
        if (!isNullOrBlank($pclObject) && !isNullOrBlank($pclObject[0])) {
            $pclObject[0]['PAYMENTSRECEIVED'] += $adjustment;
            $pclObject[0]['TOTALFXGAIN'] +=
                ibcmul(ibcsub($invoiceExchangeRate, $paymentExchangeRate, 10),
                        ibcdiv($adjustment, $paymentExchangeRate, 10), 2, true);

            return parent::regularSet($pclObject[0]);
        }

        return false;
    }

    /**
     * update the PCL record in the database
     *
     * @param array &$values the PCL data
     *
     * @return bool false if error, else true
     */
    protected function regularSet(&$values)
    {
        $source = "ProjectContractLineManager::regularSet";
        $ok = $this->_QM->beginTrx($source);

        ProjectContractManager::emptyLinesCache();

        // Set change request data from REST API
        if ($values['UPDATEREQUEST']) {
            self::$updateRequest = $values['UPDATEREQUEST'];
        }

        $ok = $ok && $this->validateBilledPrice($values);

        $ok = $ok && $this->translateValues($values);

        // Removed call to validateTotalRevisedPrice() for IA-85164 and IA-143308.
        // The validation was removed to allow project contract and project contract line values to be below billed value.

        $ok = $ok && $this->validateValues($values);

        $gManagerFactory = Globals::$g->gManagerFactory;
        $pcnMgr = $gManagerFactory->getManager('projectcontract');
        $pcn = $pcnMgr->get($values['PROJECTCONTRACTID']);
        if ( isset($pcn['POSTED']) && $pcn['POSTED'] === 'true' ) {
            $ok = $ok && $this->validatePostingPeriod($values, $pcn);
            $ok = $ok && $this->validateProjectAndCustomer($values, $pcn);
            $ok = $ok && $pcnMgr->unpostFromGL($pcn);
        }

        if(Globals::$g->gManagerFactory->getManager('document')->isVatTypeOverrideTaxSchedForEntryEnabled() && !empty($values['PCLTAXSCHEDULEID'])){
            $ok = $ok && $this->validatePclTaxSchedule($values);
        }
        $ok = $ok && parent::regularSet($values);

        if (isset($pcn['POSTED']) && $pcn['POSTED'] === 'true' ) {
            $ok = $ok && $pcnMgr->postToGL($pcn);
        }

        $ok = $ok && $this->setAttachmentMap($values, 'RECORDNO', self::PROJECTCONTRACTLINE);

        // Make sure that Progress Bill Project Contract lines never have Task Mappings saved. 
        if ($values['BILLINGTYPE'] == self::BILLING_TYPE_PB) {
            $ok = $ok && PclTaskManager::deleteAllPCLTasksByKey($values['RECORDNO']);
        }

        $ok = $ok && $this->_QM->commitTrx($source);
        if ( ! $ok ) {
            // do *not* add error, if it has only warnings
            if ( !HasWarnings() || HasErrors() ) {
                Globals::$g->gErr->addIAError(
                    number: 'CRE-0355',
                    source: __FILE__ . ':' . __LINE__,
                    desc1PHs: ['PROJECT_CONTRACT_LINE' => strtolower(self::$renamedProjectContractLineTxt)]
                );
            }
            $this->_QM->rollbackTrx($source);
        }

        return $ok;
    }

    /**
     * Delete a record from the database
     *
     * This implementation is usually sufficient for single table objects
     *
     * @param string|int $ID vid of entity
     *
     * @return bool false if error, else true
     */
    public function Delete($ID)
    {
        $source = "ProjectContractLineManager::Delete";
        $ok = $this->_QM->beginTrx($source);

        $ok = $ok && parent::Delete($ID);

        $ok = $ok && $this->deleteAttachmentMap($ID, self::PROJECTCONTRACTLINE);

        $ok = $ok && $this->_QM->commitTrx($source);
        if ( ! $ok ) {
            // do *not* add error, if it has only warnings
            if ( !HasWarnings() || HasErrors() ) {
                Globals::$g->gErr->addIAError(
                    number: 'CRE-0356',
                    source: __FILE__ . ':' . __LINE__,
                    desc1PHs: ['PROJECT_CONTRACT_LINE' => strtolower(self::$renamedProjectContractLineTxt)]
                );
            }
            $this->_QM->rollbackTrx($source);
        }

        return $ok;
    }

    /**
     * Determines if a Project Contract Line is deletable or not
     *
     * @param string|int $_id   RECORDNO of the project contract line to be deleted
     * @param bool       $doget not used in this function, it is passed to the parent class
     * @param array      $raw   data for the project contract line to be deleted
     *
     * @return bool             true if deletable, else false
     */
    function IsDeletable($_id, $doget = true, $raw = null)
    {
        $ok = parent::IsDeletable($_id, $doget, $raw);

        if ( is_array($raw) && is_array($raw[0]) && ! isNullOrBlank($raw[0]['RECORD#']) ) {
            // check if this project contract line has been billed
            $ok = $ok && ! self::doesBillingExistForContractLine($raw[0]['RECORD#']);
        }

        /*
         * There is no need to check if there are references to this project contract line (PCL) from any change request (CR),
         * change request entry (CRE) or project change order (PCO) objects because we already have database level constraints
         * on the CRHEADER, CRDETAIL and PJCHANGEORDER tables that will prevent deletion of the linked PCL.
        */

        if ( ! $ok ) {
            // do *not* add error, if it has only warnings
            if ( !HasWarnings() || HasErrors() ) {
                Globals::$g->gErr->addIAError(
                    number: 'CRE-0357',
                    source: __FILE__ . ':' . __LINE__,
                    desc1PHs: ['PROJECT_CONTRACT_LINE' => strtolower(self::$renamedProjectContractLineTxt)]
                );
            }
        }

        return $ok;
    }

    /**
     * Hook function for the sub classes to perform specific tasks right before the record is deleted
     *
     * @param array &$values the raw record
     *
     * @return bool true on success and false on failure
     */
    protected function beforeDelete(&$values)
    {
        $ok = true;
        $values = $values[0];

        // We need to query the full object here
        $values['RECORDNO'] = ( $values['RECORDNO'] ?? $values['RECORD#'] );
        $currObj = $this->get($values['RECORDNO']);

        // Delete the project contract line entries
        $ok = $ok && $this->deleteEntries($currObj);

        // Delete the PCL Tasks
        $ok = $ok && $this->deletePCLTasks($currObj);

        // Unpost from GL
        if ( ! $GLOBALS['no-posting'] ) {
            $gManagerFactory = Globals::$g->gManagerFactory;
            $pcnMgr = $gManagerFactory->getManager('projectcontract');
            $pcn = $pcnMgr->get($currObj['PROJECTCONTRACTID']);
            if ( isset($pcn['POSTED']) && $pcn['POSTED'] === 'true' ) {
                $ok = $ok && $pcnMgr->unpostFromGL($pcn);
            }
        }

        $ok = $ok && parent::beforeDelete($values);

        return $ok;
    }

    /**
     * Hook function for the sub classes to perform specific tasks right after the record is deleted
     *
     * @param array &$raw the raw record
     *
     * @return bool true on success and false on failure
     */
    protected function afterDelete(/** @noinspection PhpUnusedParameterInspection */ &$raw)
    {
        $ok = true;

        // Post to GL
        if ( ! $GLOBALS['no-posting'] ) {
            $gManagerFactory = Globals::$g->gManagerFactory;
            $pcnMgr = $gManagerFactory->getManager('projectcontract');

            $params = [
                'selects' => [ 'RECORDNO', 'PROJECTCONTRACTID' ],
                'filters' => [ [ [ 'RECORDNO', '=', $raw['PROJECTCONTRACTKEY'] ] ] ]
            ];
            $pcn = $pcnMgr->GetList($params)[0];

            $pcn = $pcnMgr->get($pcn['PROJECTCONTRACTID']);
            if ( isset($pcn['POSTED']) && $pcn['POSTED'] === 'true' ) {
                $ok = $ok && $pcnMgr->postToGL($pcn);
            }
        }

        return $ok;
    }

    /**
     * handle deleting ChangeRequestEntry object records
     *
     * @param array $obj
     *
     * @return bool
     */
    private function deleteEntries(array &$obj)
    {
        $ok = true;

        if ( !isset($obj['ITEMS']) || count($obj['ITEMS']) <= 0) {
            return $ok;
        }

        $pclEntryManager = Globals::$g->gManagerFactory->getManager('projectcontractlineentry');

        foreach ( $obj['ITEMS'] as &$entry) {
            $ok = $ok && $pclEntryManager->Delete($entry['RECORDNO']);
        }
        unset($entry);

        return $ok;
    }

    /**
     * handle deleting PCLTask object records
     *
     * @param array $obj
     *
     * @return bool
     */
    private function deletePCLTasks(array &$obj)
    {
        $ok = true;

        if ( !isset($obj['PCLTASKS']) || count($obj['PCLTASKS']) <= 0) {
            return $ok;
        }

        $pclTaskManager = Globals::$g->gManagerFactory->getManager('pcltask');

        foreach ( $obj['PCLTASKS'] as &$pclTask) {
            $ok = $ok && $pclTaskManager->Delete($pclTask['RECORDNO']);
        }
        unset($pclTask);

        return $ok;
    }

    /**
     * Allow parent entity to skip fetching the ownedobjects
     *
     * @param array $objRec
     * @param bool  $validateReadOnly true if you want to skip for reads, false otherwise
     *
     * @return bool true if you want to read the owned object, false if you want caller to skip retrieval
     */
    protected function useOwnedObject(/** @noinspection PhpUnusedParameterInspection */ $objRec,
        /** @noinspection PhpUnusedParameterInspection */ $validateReadOnly)
    {
        if ($objRec['path'] == 'CRENTRIES') {
            return false;
        }
        return true;
    }

    /**
     * Validate if the PCL has been invoiced by checking if the BILLEDPRICE has a non-zero value
     *
     * @param array $values the PCL data
     *
     * @return bool false if error, else true
     */
    protected function validateBilledPrice(array $values) : bool
    {
        $ok = true;
        $gErr = Globals::$g->gErr;

        $oldvalues = $this->get($values['RECORDNO']);

        // Any change in any of these columns is only allowed prior to a bill being generated
        // for this Project Contract Line.
        $skipValidation = true;
        $columns = ['CONTRACTLINEDATE', 'ACCOUNTNO', 'BILLINGTYPE'];
        foreach ($columns as $column) {
            if ($values[$column] != $oldvalues[$column]) {
                $skipValidation = false;
                break;
            }
        }

        if ($skipValidation) {
            return true;
        }

        if ( $values['BILLEDPRICE'] != 0 ) {
            $gErr->addIAError(
                number: 'CRE-0358',
                source: __FILE__ . ':' . __LINE__,
                desc1PHs: ['PROJECT_CONTRACT_LINE' => strtolower(self::$renamedProjectContractLineTxt)]
            );
            $ok = false;
        }

        return $ok;
    }

    /**
     * translate the PCL values for processing
     *
     * @param array &$values the PCL data
     *
     * @return bool false if error, else true
     */
    protected function translateValues(&$values)
    {
        $ok = true;

        $ok = $ok && $this->translateGLExclude($values);

        $ok = $ok && $this->translateProjectContract($values);

        $ok = $ok && $this->translateParent($values);

        $ok = $ok && $this->translateGLAccount($values);

        $ok = $ok && $this->translateRetainagePercent($values);

        $ok = $ok && $this->translateBillingType($values);

        $ok = $ok && $this->translateMaxBilling($values);

        $ok = $ok && $this->translateMaxBillingAmt($values);

        $ok = $ok && $this->translateSummarizeBill($values);

        $ok = $ok && $this->translateProject($values);

        $ok = $ok && $this->translateDepartment($values);

        $ok = $ok && $this->validateSameProjectTree($values['PROJECTID'], $values['HDR_PROJECTID']);

        $ok = $ok && $this->validateSameProjectLocationTree($values['PROJECTID'], $values['HDR_PROJECTID']);

        $ok = $ok && $this->validateProjectContractMultiEntityOwnership($values['PROJECTCONTRACTID']);

        $ok = $ok && $this->translateItem($values);

        $ok = $ok && $this->translateRateTables($values);

        $ok = $ok && $this->translateDimensionFields($values, false);

        // No need to save CUSTOMERDIMKEY
        unset($values['CUSTOMERDIMKEY']);

        // Translate 'employee' fields
        $ok = $ok && $this->translateAndSetKeyForEmployeeField($values, 'INTERNALINITIATEDBY', 'INTERNALINITIATEDBYKEY', 'Internal initiated by', __FILE__ . ':' . __LINE__);
        $ok = $ok && $this->translateAndSetKeyForEmployeeField($values, 'INTERNALVERBALBY', 'INTERNALVERBALBYKEY', 'Internal verbal by', __FILE__ . ':' . __LINE__);
        $ok = $ok && $this->translateAndSetKeyForEmployeeField($values, 'INTERNALISSUEDBY', 'INTERNALISSUEDBYKEY', 'Internal issued by', __FILE__ . ':' . __LINE__);
        $ok = $ok && $this->translateAndSetKeyForEmployeeField($values, 'INTERNALAPPROVEDBY', 'INTERNALAPPROVEDBYKEY', 'Internal approved by', __FILE__ . ':' . __LINE__);
        $ok = $ok && $this->translateAndSetKeyForEmployeeField($values, 'INTERNALSIGNEDBY', 'INTERNALSIGNEDBYKEY', 'Internal signed by', __FILE__ . ':' . __LINE__);

        // Translate 'contact' fields
        $ok = $ok && $this->translateAndSetKeyForContactField($values, 'EXTERNALVERBALBY', 'EXTERNALVERBALBYKEY', 'External verbal by', __FILE__ . ':' . __LINE__);
        $ok = $ok && $this->translateAndSetKeyForContactField($values, 'EXTERNALAPPROVEDBY', 'EXTERNALAPPROVEDBYKEY', 'External approved by', __FILE__ . ':' . __LINE__);
        $ok = $ok && $this->translateAndSetKeyForContactField($values, 'EXTERNALSIGNEDBY', 'EXTERNALSIGNEDBYKEY', 'External signed by', __FILE__ . ':' . __LINE__);

        $ok = $ok && $this->setProjectContractLineEntries($values);

        return $ok;
    }

    /**
     * validate the PCL values for processing
     *
     * @param array &$values the PCL data
     *
     * @return bool false if error, else true
     */
    private function validateValues(array &$values)
    {
        $ok = true;

        $ok = $ok && $this->validateGLAccount($values);
        $ok = $ok && $this->validateProjectContract($values);

        return $ok;
    }

    /** Validate the parent Project Contract
     * @param array $values
     * @return bool
     */
    private function validateProjectContract($values)
    {
        $ok = true;
        
        if (!empty($values['PROJECTCONTRACTID'])) {
            $parentManager = Globals::$g->gManagerFactory->getManager('projectcontract');
            $parent = $parentManager->get($values['PROJECTCONTRACTID']);
            
            if ($parent) {
                $ok = $parentManager->validateGLBudgets($parent);
            } else {
                $ok = false;
            }
        }
        
        return $ok;
    }

    /**
     * validates the GL account
     *
     * @param array $values the PCL data
     *
     * @return bool false if error, else true
     */
    private function validateGLAccount(array $values)
    {
        $ok = true;
        $gErr = Globals::$g->gErr;

        if ( $ok && (! isset($values['ACCOUNTNO']) || $values['ACCOUNTNO'] == '' )) {
            $gErr->addIAError(
                number: 'CRE-0338',
                source: __FILE__ . ':' . __LINE__,
                desc1PHs: ['PROJECT_CONTRACT_LINE' => strtolower(self::$renamedProjectContractLineTxt)]
            );
            $ok = false;
        }

        if ( $ok && isset($values['ACCOUNT_STATUS']) && $values['ACCOUNT_STATUS'] == 'F' ) {
            $gErr->addIAError(
                number: 'CRE-0359',
                source: __FILE__ . ':' . __LINE__,
                desc1PHs: ['PROJECT_CONTRACT_LINE' => strtolower(self::$renamedProjectContractLineTxt)]
            );
            $ok = false;
        }

        if ( $ok && isset($values['CLOSEABLE_ACCOUNT']) && $values['CLOSEABLE_ACCOUNT'] == 'R' ) {
            $gErr->addIAError(
                number: 'CRE-0360',
                source: __FILE__ . ':' . __LINE__,
                desc1PHs: ['PROJECT_CONTRACT_LINE' => strtolower(self::$renamedProjectContractLineTxt)]
            );
            $ok = false;
        }

        if ( $ok && isset($values['STATISTICAL_ACCOUNT']) && $values['STATISTICAL_ACCOUNT'] == 'T' ) {
            $gErr->addIAError(
                number: 'CRE-0361',
                source: __FILE__ . ':' . __LINE__,
                desc1PHs: ['PROJECT_CONTRACT_LINE' => strtolower(self::$renamedProjectContractLineTxt)]
            );
            $ok = false;
        }

        return $ok;
    }

    /**
     * Return error if selected date for posting has no posting period
     * @param array $values
     * @param array $pcn
     *
     * @return bool
     * @throws Exception
     */
    private function validatePostingPeriod(array $values, array $pcn)
    {
        $postingPeriod = $pcn['POSTTO'];

        if ( $postingPeriod == ProjectContractManager::POSTINGPERIOD_PROJECTBEGIN ) {
            $gManagerFactory = Globals::$g->gManagerFactory;
            $projectMgr = $gManagerFactory->getManager('project');
            $projectId = explode('--', $values['PROJECTID'])[0];
            $project = $projectMgr->get($projectId);

            $period = ProjectContractManager::getPeriodForDate($project['BEGINDATE']);

            if ($period === false || count($period) === 0) {
                Globals::$g->gErr->addIAError(
                    number: 'CRE-0329',
                    source: __FILE__ . ':' . __LINE__,
                    desc1PHs: ['DATE' => $project['BEGINDATE']]
                );
                return false;
            }

        }

        else if ( $postingPeriod == ProjectContractManager::POSTINGPERIOD_LINE ) {
            $period = ProjectContractManager::getPeriodForDate($values['CONTRACTLINEDATE']);

            if (($period === false || count($period) === 0) && $this->hasPostedEntries($values, $pcn)) {
                Globals::$g->gErr->addIAError(
                    number: 'CRE-0329',
                    source: __FILE__ . ':' . __LINE__,
                    desc1PHs: ['DATE' => $values['CONTRACTLINEDATE']]
                );
                return false;
            }
        }

        else if ( $postingPeriod == ProjectContractManager::POSTINGPERIOD_ENTRY ) {
            $gManagerFactory = Globals::$g->gManagerFactory;
            $crMgr = $gManagerFactory->getManager('changerequest');
            $crEntryMgr = $gManagerFactory->getManager('changerequestentry');
            $pcnMgr = $gManagerFactory->getManager('projectcontract');
            foreach ($values['ITEMS'] as $entry) {
                if ($entry['RECORDTYPE'] == 'cl') {
                    // only check entries that have WFTypes that post to a GL budget
                    // if WFTYPE is null, that means the value is set to 'original'
                    $budget = $pcnMgr->getBudgetForWFType($pcn, $entry['WFTYPE'] ?? 'original');
                    if (isset($budget) && $budget != '') {
                        $period = ProjectContractManager::getPeriodForDate($entry['PRICEEFFECTIVEDATE']);

                        if ($period === false || count($period) === 0) {
                            Globals::$g->gErr->addIAError(
                                number: 'CRE-0329',
                                source: __FILE__ . ':' . __LINE__,
                                desc1PHs: ['DATE' => $entry['PRICEEFFECTIVEDATE']]
                            );
                            return false;
                        }
                    }
                }
                else {
                    $crEntry = $crEntryMgr->get($entry['RECORDNO'], ['WFTYPE', 'UPDATEPJCONTRACT', 'CHANGEREQUESTID']);
                    if (is_array($crEntry)) {
                        // only check entries that have WFTypes that post to a GL budget
                        $budget = $pcnMgr->getBudgetForWFType($pcn, $crEntry['WFTYPE']);
                    }
                    if (isset($budget) && $budget != '') {
                        if ($crEntry['UPDATEPJCONTRACT'] == 'yes') {
                            $crHeader = $crMgr->get($crEntry['CHANGEREQUESTID'], ['PRICEEFFECTIVEDATE']);
                            if (is_array($crHeader)) {
                                $period = ProjectContractManager::getPeriodForDate($crHeader['PRICEEFFECTIVEDATE']);
                            }

                            if (!isset($period) || $period === false || count($period) === 0) {
                                Globals::$g->gErr->addIAError(
                                    number: 'CRE-0329',
                                    source: __FILE__ . ':' . __LINE__,
                                    desc1PHs: ['DATE' => $crHeader['PRICEEFFECTIVEDATE']]
                                );
                                return false;
                            }
                        }
                    }
                }
            }
        }

        return true;
    }

    /**
     * Return error if contract set to post to GL and combination of GL budget, Project, and Customer
     * is the same as another PCN/PCL posted to GL
     *
     * @param array $values
     * @param array $pcn
     *
     * @return bool
     * @throws Exception
     */
    private function validateProjectAndCustomer(array $values, array $pcn)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $pcnMgr = $gManagerFactory->getManager('projectcontract');
        // if PCL doesn't have a customer ID, this validation is unnecessary
        if (!isNullOrBlank($values['CUSTOMERID'])) {
            // retrieve list of budgets on PCN
            $budgets = $pcnMgr->retrieveBudgets($pcn);
            // retrieve list of PCNs that post to GL
            $pcnParams = [
                'selects' => [ 'RECORDNO', 'PROJECTCONTRACTID', 'POSTED', 'ORIGINALGLBUDGETID', 'REVISIONGLBUDGETID',
                               'APPROVEDGLBUDGETID', 'PENDINGGLBUDGETID', 'FORECASTGLBUDGETID', 'OTHERGLBUDGETID' ],
                'filters' => [
                    [
                        [ 'PROJECTCONTRACTID', '!=', $values['PROJECTCONTRACTID'] ],
                        [ 'POSTED', '=', 'true' ]
                    ]
                ]
            ];
            $postedContracts = $pcnMgr->GetList($pcnParams);
            // filter PCN list to only include ones with budgets in the list of budgets
            $overlappedContracts = [];
            foreach ($postedContracts as $pcn) {
                $budgetList = $pcnMgr->retrieveBudgets($pcn);
                foreach ($budgetList as $budget) {
                    if (in_array($budget, $budgets)) {
                        $overlappedContracts[] = $pcn;
                        break;
                    }
                }
            }
            // if any PCNs remainings, create combinations of Project and Customer from PCLs
            if (!empty($overlappedContracts)) {
                // when only updating PCL, thisProjectCustomerMap only includes one pair
                $thisProjectCustomerMap = [];
                [ $projectID ] = explode('--', $values['PROJECTID']);
                [ $customerID ] = explode('--', $values['CUSTOMERID']);
                $thisProjectCustomerMap[$projectID][] = $customerID;
                foreach ($overlappedContracts as $pcn) {
                    $pcnValues = $pcnMgr->get($pcn['PROJECTCONTRACTID'], ['PROJECTCONTRACTID', 'CUSTOMERID']);
                    $map = $pcnMgr->getProjectCustomerMap($pcnValues);
                    // if any overlap between contracts, return error
                    foreach ($thisProjectCustomerMap as $key => $vals) {
                        if (isset($map[$key])) {
                            foreach ($vals as $val) {
                                if (in_array($val, $map[$key])) {
                                    Globals::$g->gErr->addIAError(
                                        number: 'CRE-0332',
                                        source: __FILE__ . ':' . __LINE__
                                    );
                                    return false;
                                }
                            }
                        }
                    }
                }
            }
        }
        return true;
    }

    /**
     * Use function to check if entries use workflow type that posts to GL budget
     * @param $values
     * @param $pcn
     *
     * @return bool
     */
    public function hasPostedEntries($values, $pcn) {
        if (isset($values['ITEMS']) && isset($values['GLEXCLUDE']) && $values['GLEXCLUDE'] == 'false') {
            $gManagerFactory = Globals::$g->gManagerFactory;
            $crEntryMgr = $gManagerFactory->getManager('changerequestentry');
            $pcnMgr = $gManagerFactory->getManager('projectcontract');
            foreach ($values['ITEMS'] as $entry) {
                if ($entry['RECORDTYPE'] == 'cl') {
                    // if WFTYPE is null, that means the value is set to 'original'
                    $budget = $pcnMgr->getBudgetForWFType($pcn, $entry['WFTYPE'] ?? 'original');
                    if ( isset($budget) && $budget != '' ) {
                        return true;
                    }
                }
                else {
                    $crEntry = $crEntryMgr->get($entry['RECORDNO'], ['WFTYPE']);
                    $budget = $pcnMgr->getBudgetForWFType($pcn, $crEntry['WFTYPE']);
                    if (isset($budget) && $budget != '') {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     *  Given an array of key field values (e.g. record#), queries and returns full objects (or,
     *   fields as given).
     *
     * @param array $keys   an array of key values.
     * @param array $fields an optional list of fields to retrieve (null means get all fields).
     *
     * @return  array|false   list of objects matching the query.
     */
    public function getByKeys($keys, $fields = null)
    {
        $values = parent::getByKeys($keys, $fields);
        if (!is_array($values)) {
            return $values;
        }

        foreach ($values as &$object) {
            // Set the customer to the project contract customer
            if (is_null($object['CUSTOMERID']) && !isNullOrBlank($object['PCCUSTOMERKEY'])) {
                if (isset($object['PCCUSTOMERID'])) {
                    $object['CUSTOMERID'] = $object['PCCUSTOMERID'];
                }
                if (isset($object['PCCUSTOMERNAME'])) {
                    $object['CUSTOMERNAME'] = $object['PCCUSTOMERNAME'];
                }
            }

            $this->setProjectLocationFields($object);
            if (is_array($object['ITEMS'])) {
                $this->separateCREntriesFromPCLEntries($object, $object['ITEMS']);
            }
        }
        unset($object);

        return $values;
    }

    /**
     * Get a single record by Key, or by ProjectContractID--ProjectContractLineID.  (ProjectContractLineID is not unique by itself.)
     *
     * @param string        $pclKeyOrRecordNo
     * @param string[]|null $fields
     *
     * @return array|false
     */
    function get($pclKeyOrRecordNo, $fields = null)
    {
        // If we are being passed a string that has both PCID and PCLID, use the appropriate helper function
        if (strcontains($pclKeyOrRecordNo, '--')) {
            return $this->getByPCLid($pclKeyOrRecordNo, $fields);
        }

        // Cache the object when querying for all columns.
        static $recordCache = [];
        if ($fields == null) {
            if (!isset($recordCache[$pclKeyOrRecordNo])) {
                $recordCache[$pclKeyOrRecordNo] = parent::get($pclKeyOrRecordNo, $fields);
            }
            $object = $recordCache[$pclKeyOrRecordNo];
        } else {
            $object = parent::get($pclKeyOrRecordNo, $fields);
        }

        if ( $object ) {
            // Set the customer to the project contract customer
            $object['CUSTOMERID'] = $object['PCCUSTOMERID'];
            $object['CUSTOMERNAME'] = $object['PCCUSTOMERNAME'];

            $this->setProjectLocationFields($object);

            $this->separateCREntriesFromPCLEntries($object, $object['ITEMS']);
        }

        return $object;
    }

    /**
     * @param string $pcID
     * @param string $pclID
     * @return array|false
     */
    public function getByPCidAndPCLid($pcID, $pclID, $fields = null)
    {
        $pclKey = $this->getRecordNoWithIDs($pcID, $pclID);

        return $this->get($pclKey, $fields);
    }

    /**
     * The $pcAndPclID parameter must be formatted as ProjectContractID--ProjectContractLineID
     * @param string $pcAndPclID
     * @return array|false
     */
    public function getByPCLid($pcAndPclID, $fields = null)
    {
        // Need to lookup with PCid and PCLid.  This can not be used for record#.
        [$pcID, $pclID] = explode('--', $pcAndPclID);
        if (!isNullOrBlank($pcID) && !isNullOrBlank($pclID)) {
            return $this->getByPCidAndPCLid($pcID, $pclID, $fields);
        }

        return false;
    }

    /**
     * Return a list of entities
     *
     * @param array $params    a structure used to build the custom query
     * @param bool  $_crosscny if true do not add the var.cny# = ... code
     * @param bool  $nocount   don't generate a count column
     *
     * @return array[] $newResult  result of query
     */
    function GetList($params = [], $_crosscny = false, $nocount = true)
    {
        $projectLocationFieldMap = [
            'PROJECTLOCATIONKEY' => 'MEGAENTITYKEY',
            'PROJECTLOCATIONID' => 'MEGAENTITYID',
            'PROJECTLOCATIONNAME' => 'MEGAENTITYNAME',
        ];

        $customerFieldMap = [
            'CUSTOMERDIMKEY' => 'PCCUSTOMERKEY',
            'CUSTOMERID' => 'PCCUSTOMERID',
            'CUSTOMERNAME' => 'PCCUSTOMERNAME'
        ];

        $addedFields = [];

        foreach ($projectLocationFieldMap as $projLocFld => $megaEntFld) {
            if (!empty($params['selects']) && in_array($projLocFld, $params['selects'])) {
                if (!in_array($megaEntFld, $params['selects'])) {
                    $params['selects'][] = $megaEntFld;
                    $addedFields[] = $megaEntFld;
                }
            }
        }

        foreach ($customerFieldMap as $customerField => $pcCustomerField) {
            if (!empty($params['selects']) && in_array($customerField, $params['selects'])) {
                if (!in_array($pcCustomerField, $params['selects'])) {
                    $params['selects'][] = $pcCustomerField;
                    $addedFields[] = $pcCustomerField;
                }
            }
        }

        $recordID = true;
        // Remove SUPDOCID parameter if present in the input request as we do not have this field in the DB.
        $supDocID = array_search('SUPDOCID', $params['selects'] ?? []);
        if ( $supDocID !== false ) {
            array_splice($params['selects'], $supDocID, 1);

            $recordID = array_search('RECORDNO', $params['selects'] ?? []);
            if ( $recordID === false ) {
                $params['selects'][] = 'RECORDNO';
            }
        }

        $records = parent::GetList($params, $_crosscny, $nocount);

        $keyedSupDoc = [];
        if ( $supDocID !== false ) {
            $tempRecordIDs = [];
            foreach ( $records as $record ) {
                $tempRecordIDs [] = $record['RECORDNO'];
            }
            if ( $supDocID !== false ) {
                $gManagerFactory = Globals::$g->gManagerFactory;
                $SupDocMapMgr = $gManagerFactory->getManager('supdocmaps');
                $supDocs = $SupDocMapMgr->GetListSupDocIDs($tempRecordIDs, 'PROJECTCONTRACTLINE');

                foreach ( $supDocs as $supDoc ) {
                    $keyedSupDoc[$supDoc['RECORDID']] = $supDoc;
                }

                for ( $i = 0; $i < count($records); $i++ ) {
                    if ( isset($keyedSupDoc[$records[$i]['RECORDNO']]) && $keyedSupDoc[$records[$i]['RECORDNO']] != '' ) {
                        $records[$i]['SUPDOCID'] = $keyedSupDoc[$records[$i]['RECORDNO']]['DOCUMENTID'];
                    }
                    if ( $recordID === false ) {
                        unset($records[$i]['RECORDNO']);
                    }
                }
            }
        }

        foreach ( $records as &$record ) {
            $this->setProjectLocationFields($record);
            $this->setCustomerFields($record);

            foreach ( $addedFields as $fld ) {
                unset($record[$fld]);
            }
        }
        unset($record);

        return $records;
    }

    /**
     * @param int $recordNo
     *
     * @return string[]
     */
    public static function GetRateTableOptions(int $recordNo)
    {
        $stmt = "SELECT DEFAULTRATETABLEKEY, TSRATETABLEKEY, EERATETABLEKEY, APRATETABLEKEY, PORATETABLEKEY, GLRATETABLEKEY, CCRATETABLEKEY FROM projectcontractline WHERE record# = :1 AND cny# = :2";
        $res = QueryResult([$stmt, $recordNo, GetMyCompany()]);

        return $res[0];
    }

    /**
     * Since vid=RECORDNO, helper function gets record no of PCL using IDs
     *
     * @param string $pcID
     * @param string $pclID
     *
     * @return string|false
     */
    public function getRecordNoWithIDs($pcID, $pclID) {

        [$pcID] = explode("--", $pcID);
        [$pclID] = explode("--", $pclID);

        $filter = [
            'selects' => ['RECORDNO', 'PROJECTCONTRACTLINEID', 'PROJECTCONTRACTID' ],
            'filters' => [[[ 'PROJECTCONTRACTLINEID', '=', $pclID ], [ 'PROJECTCONTRACTID', '=', $pcID ]]]
        ];
        $pclObj = $this->GetList( $filter );
        if (count($pclObj) != 0) {
            return $pclObj[0]['RECORDNO'];
        }
        else {
            return false;
        }
    }

    /**
     * @param string $pclID
     * @param string $pcKey
     * @return false|mixed
     */
    public function getRecordNoWithIDAndPCKey($pclID, $pcKey) {
        $filter = [
            'selects' => ['RECORDNO'],
            'filters' => [[[ 'PROJECTCONTRACTLINEID', '=', $pclID ], [ 'PROJECTCONTRACTKEY', '=', $pcKey ]]]
        ];
        $pclObj = $this->GetList( $filter );
        if (count($pclObj) != 0) {
            return $pclObj[0]['RECORDNO'];
        }
        else {
            return false;
        }
    }

    /**
     * Ensures that the value of the checkbox will be normalized so it can be checked in conditionals prior to saving
     *
     * @param array $values
     *
     * @return bool
     */
    function translateGLExclude(&$values)
    {
        if ((isset($values['GLEXCLUDE']) && empty($values['GLEXCLUDE'])) || !isset($values['GLEXCLUDE']) || $values['GLEXCLUDE'] == null) {
            $values['GLEXCLUDE'] = 'false';
        } else {
            $values['GLEXCLUDE'] = strtolower($values['GLEXCLUDE']);
        }

        return true;
    }

    /**
     * Overridden to do API specific validation
     *
     * @param array $values Set of values to update
     *
     * @return bool signifying success or failure
     */
    function API_Set(&$values)
    {
        $ok = true;
        $this->fromGateway = true;

        $pclData = null;

        // TODO Research this - we can not read PCL just by supplying a PCLID
        // Read the existing values
        $recordNumSupplied = isset($values['RECORDNO']) && $values['RECORDNO'] != '';
        $pclIdSupplied = isset($values['PROJECTCONTRACTLINEID']) && $values['PROJECTCONTRACTLINEID'] != '';
        if ( $recordNumSupplied ) {
            $pclData = $this->GetByRecordNo($values['RECORDNO']);
        } else if ( $pclIdSupplied ) {
            $pclData = $this->get($values['PROJECTCONTRACTLINEID']);
        }

        // Do not allow changing the PROJECTCONTRACTLINEID
        $ok = $ok && $this->validateID($recordNumSupplied, $pclIdSupplied, $values, $pclData);

        $ok = $ok && $this->validateEntriesForProjectContractLine($values);

        $ok = $ok && parent::API_Set($values);

        return $ok;
    }

    /**
     * @param bool       $recordNumSupplied
     * @param bool       $pclIdSupplied
     * @param array      $values
     * @param array|null $pclData (only field used is 'PROJECTCONTRACTLINEID')
     *
     * @return bool
     */
    private function validateID(bool $recordNumSupplied, bool $pclIdSupplied, array $values, ?array $pclData)
    {
        $ok = true;

        if ( $pclData != null ) {
            if ( $recordNumSupplied && $pclIdSupplied ) {
                if ( $values['PROJECTCONTRACTLINEID'] != $pclData['PROJECTCONTRACTLINEID'] ) {
                    Globals::$g->gErr->addIAError(
                        number: 'CRE-0364',
                        source: __FILE__ . ':' . __LINE__,
                        desc1PHs: ['PROJECT_CONTRACT_LINE' => strtolower(self::$renamedProjectContractLineTxt)]
                    );
                    $ok = false;
                }
            }
        }

        return $ok;
    }

    /**
     * Validates that the RECORDNO values supplied for the PCL Entries are valid.
     *
     * @param array $values project contract line data (includes project contract line entries for add/set)
     *
     * @return bool
     */
    private function validateEntriesForProjectContractLine(array $values)
    {
        $ok = true;

        $pclEntryMgr = Globals::$g->gManagerFactory->getManager('projectcontractlineentry');

        if ( isset($values['PROJECTCONTRACTLINEENTRIES']) && ! empty($values['PROJECTCONTRACTLINEENTRIES']) ) {
            $pclEntryValuesForUpdate = $values['PROJECTCONTRACTLINEENTRIES']['PROJECTCONTRACTLINEENTRY'];

            foreach ( $pclEntryValuesForUpdate as $pclEntryForUpdate ) {
                $recordNo = $pclEntryForUpdate['RECORDNO'];
                if ( ! isNullOrBlank($recordNo) ) {
                    $pclEntryData = $pclEntryMgr->GetByRecordNo($recordNo, ['PROJECTCONTRACTLINEID']);
                    $entryIsChildOfPCL = false;
                    if ( $pclEntryData != null ) {
                        $entryIsChildOfPCL = ($pclEntryData['PROJECTCONTRACTLINEID'] == $values['PROJECTCONTRACTLINEID']);
                    }
                    if ( $pclEntryData == null || !$entryIsChildOfPCL ) {
                        Globals::$g->gErr->addIAError(
                            number: 'CRE-0362',
                            source: __FILE__ . ':' . __LINE__,
                            desc1PHs: ['PROJECT_CONTRACT_LINE' => strtolower(self::$renamedProjectContractLineTxt)]
                        );
                        $ok = false;
                        break;
                    }
                }
            }
        }

        return $ok;
    }

    /**
     * API_UpdateMerge
     *    This function takes the existing values for a given object, and the new values requested
     *   to be applied by an API_Set, and merges them into a single object values array.  By default
     *   this function first checks to see if there is an attempt to update any fields in the optional
     *   API metadata 'CANT_UPDATE_FIELDS' list.  If so, an error is generated.  If not, then it simply
     *   lays the new values on top of the existing values.  However, subclasses can override to perform
     *   class-specific value merging.
     *
     * @param array $object Set of read object values (associative name/value pairs)
     * @param array $values Set of given input values to update (associative name/value pairs)
     *
     * @return array Merged array of read object and given input values.
     */
    function API_UpdateMerge(&$object, &$values)
    {
        // Owned objects that are to be merged will be added to the array below with the respective child KEY and the Key Field to compare.
        $ownedObjs = [
            // PROJECTCONTRACTLINEENTRIES has PROJECTCONTRACTLINEENTRY as child with RECORDNO as key
            'PROJECTCONTRACTLINEENTRIES' => [
                'child' => 'PROJECTCONTRACTLINEENTRY',
                'key'   => 'RECORDNO'
            ],
        ];

        // The business logic to merge the Existing data with the data passed from Gateway is handled here.

        // We loop through all the owned objects to check if they are to be updated with respect to the
        // input from the user and do the merge or insert if the values are provided.

        foreach ( $ownedObjs as $obj => $info ) {
            // Fetch the values from existing data into $oldObjs and values provided by the user to $newObjs.
            // KeyField will be stored in $keyFld.

            $oldObjs = $object[$obj][$info['child']];
            $newObjs = ! empty($values[$obj]) ? $values[$obj][$info['child']] : null;
            $keyFld = $info['key'];

            // We do not need to perform an insert or update when either one of new or old objects are empty.
            if ( ! empty($oldObjs) && ! empty($newObjs) ) {
                // If the old objects or new objects are having just single element being retrieved or passed, We are creating
                // a structure of obj[0] else we do not change anything existing on new or old objects.

                if ( ! $oldObjs[0] ) {
                    $temp = $oldObjs;
                    $oldObjs = [];
                    $oldObjs[0] = $temp;
                }

                if ( ! $newObjs[0] ) {
                    $temp = $newObjs;
                    $newObjs = [];
                    $newObjs[0] = $temp;
                }

                // We store the existing data in finalObjs which will be updated on $object and will be passed to API_UpdateMerge.
                $finalObjs = $oldObjs;

                // We go through all the new objects to check if a record with same keyfield already exists for the record that user
                // is trying to update. If the record Exists We Merge the user data to existing data, else we add this entry to finalObjs.
                foreach ( $newObjs as $newObj ) {
                    $newObj['API_USER_SUPPLIED'] = true;
                    // boolean $set  is the flag we use to check if an entry is being merged or has to be inserted
                    $set = false;
                    foreach ( $oldObjs as $key => $oldObj ) {
                        if ( $newObj[$keyFld] == $oldObj[$keyFld] ) {
                            // If the KeyFields in existing and new entries are matched, then we set the flag for update to true
                            // and store the values that are merged into finalObjs of oldObject Key.

                            $set = true;
                            $newObj['API_USER_UPDATED'] = true;
                            $finalObjs[$key] = array_merge_clobber($finalObjs[$key], $newObj);
                        }
                    }

                    // If there is no set happened the newEntry will be added to $finalObjs.
                    if ( $set == false ) {
                        $newObj['API_USER_UPDATED'] = false;
                        $finalObjs[] = $newObj;
                    }
                }

                // Here we are unsetting the $values and $objects children with current owned object as we are
                // inserting the updated $final objs to the object that is going to be returned to api_update.

                unset($values[$obj], $object[$obj][strtolower($info['child'])]);
                $object[$obj][$info['child']] = $finalObjs;
            } else if (empty($newObjs)) {
                unset($values[$obj]);
            }
        }

        return parent::API_UpdateMerge($object, $values);
    }

    /**
     * @param array &$object
     */
    private function setProjectLocationFields(array &$object) : void
    {
        if ( $object ) {
            if ( array_key_exists('PROJECTLOCATIONKEY', $object) && isNullOrBlank($object['PROJECTLOCATIONKEY']) ) {
                $object['PROJECTLOCATIONKEY'] = $object['MEGAENTITYKEY'];
            }
            if ( array_key_exists('PROJECTLOCATIONID', $object) && isNullOrBlank($object['PROJECTLOCATIONID']) ) {
                $object['PROJECTLOCATIONID'] = $object['MEGAENTITYID'];
            }
            if ( array_key_exists('PROJECTLOCATIONNAME', $object) && isNullOrBlank($object['PROJECTLOCATIONNAME']) ) {
                $object['PROJECTLOCATIONNAME'] = $object['MEGAENTITYNAME'];
            }
        }
    }

    /**
     * @param array &$object
     */
    private function setCustomerFields(array &$object) : void
    {
        if ( $object ) {
            if ( array_key_exists('CUSTOMERDIMKEY', $object) && isNullOrBlank($object['CUSTOMERDIMKEY']) ) {
                $object['CUSTOMERDIMKEY'] = $object['PCCUSTOMERKEY'];
                $object['CUSTOMERID'] = $object['PCCUSTOMERID'];
                $object['CUSTOMERNAME'] = $object['PCCUSTOMERNAME'];
            }
        }
    }

    /**
     * @param int $projectContractLineKey
     * @param string $releasedAmount
     * @return bool
     */
    public function adjustRetainageReleasedAmount($projectContractLineKey, $releasedAmount)
    {
        $ok = true;

        if (!isNullOrBlank($projectContractLineKey)) {
            
            $pclObject = $this->getByKeys([$projectContractLineKey]);
            
            if (is_array($pclObject) && !isNullOrBlank($pclObject[0])) {
                $pclObject = $pclObject[0];
            }
            
            if (!isNullOrBlank($pclObject)) {
                $pclObject['RETAINAGERELEASED'] += $releasedAmount;

                $ok = parent::regularSet($pclObject);
            }
            
            if (!$ok) {
                Globals::$g->gErr->addIAError(
                    number: 'CRE-0363',
                    source: __FILE__ . ':' . __LINE__,
                    desc1PHs: ['PROJECT_CONTRACT_LINE' => strtolower(self::$renamedProjectContractLineTxt)]
                );
            }
        }

        return $ok;
    }

    /**
     * Override this function since 'primaryfield' is not set but we want to trim PROJECTCONTRACTLINEID
     *
     * @param array $values
     *
     * @return bool
     */
    protected function trimSpacesAndRestrictHyphens(&$values)
    {
        $keyField = 'PROJECTCONTRACTLINEID';
        if (isset($values[$keyField]) && $values[$keyField] != '') {
            //to trim leading/trailing spaces if any
            $values[$keyField] = trim($values[$keyField]);

            //remove unwanted spaces in between
            $values[$keyField] = isl_preg_replace('/\s\s+/', ' ', $values[$keyField]);

            if (substr($values[$keyField], -1) == '-' || substr($values[$keyField], 0, 1) == '-' || strpos($values[$keyField], '--') !== false) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * This method is invoked from FormEditor::MergeOwnedDimensions. It is typically available for owned objects
     * subclassed from OwnedObjectManager. Since ProjectContractLineManager is not subclassed from OwnedObjectManager,
     * we need to include this method explicitly, so its FormEditor is able to invoke it from MergeOwnedDimension.
     *
     * @return string
     */
    public function getLocationPTRPath()
    {
        return "PROJECTLOCATIONID";
    }
    
    /**
     * This method is invoked from FormEditor::MergeOwnedDimensions. It is typically available for owned objects
     * subclassed from OwnedObjectManager. Since ProjectContractLineManager is not subclassed from OwnedObjectManager,
     * we need to include this method explicitly, so its FormEditor is able to invoke it from MergeOwnedDimension.
     *
     * @return string
     */
    public function getDepartmentPTRPath()
    {
        return "DEPARTMENTID";
    }

    /**
     * Tries to look up OE doc entries that reference the supplied Project Contract Line Key.
     * If it finds any, then it returns true, else false is returned.
     *
     * @param string|int    $projectContractLineKey     RECORDNO of the Project Contract Line
     *
     * @return bool                                     true if OE doc entries exists, else false
     * @throws Exception
     */
    static function doesBillingExistForContractLine($projectContractLineKey)
    {
        $foundDocEntry = false;

        if (CRESetupManager::isCREInstalled()) {
            $docEntryParams = [
                'selects' => [ 'RECORDNO' ],
                'filters' => [ [ [ 'PROJECTCONTRACTLINEKEY', '=', $projectContractLineKey ] ] ]
            ];
            $soDocEntryManager = Globals::$g->gManagerFactory->getManager('sodocumententry');
            $soDocEntry = $soDocEntryManager->GetList($docEntryParams);
            if ($soDocEntry && is_array($soDocEntry)) {
                $foundDocEntry = true;
            }
        }

        $ok = $foundDocEntry;

        return $ok;
    }

    /**
     * Helper function to return a static list of "billable" fields - the fields that have calculated values and
     * are not set by the user.
     *
     * @return array
     */
    public static function getBillableFields() : array
    {
        return [
            'BILLEDPRICE',
            'BALANCETOBILL',
            'BILLEDNETRETAINAGE',
            'PERCENTAGEBILLED',
            'PERCENTAGEBILLEDNETRETAINAGE',
            'RETAINAGEHELD',
            'RETAINAGERELEASED',
            'RETAINAGEBALANCE',
            'PAYMENTSRECEIVED',
        ];
    }

    /**
     * The ITEMS array in the object values array returned by the get() function contains both PCL Entries
     * as well as CR Entries. This function separates them into two arrays - the CR Entries are added to a
     * CRENTRIES array and the PCL Entries are put back in the ITEMS array.
     *
     * This was being done inside the API_Get() override. With the REST API implementation, we need to do this
     * outside of API_Get(). So it was refactored into this function, which now gets called from the get() function.
     *
     * @param $pcl
     * @param $entries
     * @return void
     */
    protected function separateCREntriesFromPCLEntries(&$pcl, $entries)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $crMgr = $gManagerFactory->getManager('changerequest');
        $crEntryMgr = $gManagerFactory->getManager('changerequestentry');
        $pclEntries = [];
        $crEntries = [];
        foreach ($entries as $entry) {
            if ($entry['RECORDTYPE'] == "cr") {
                $crEntry = $crEntryMgr->get($entry['RECORDNO']);
                $header = $crMgr->get($crEntry['CHANGEREQUESTID'], ['PRICEEFFECTIVEDATE']);
                $crEntry['PRICEEFFECTIVEDATE'] = $header['PRICEEFFECTIVEDATE'];
                $crEntry['WFTYPE_VALUE'] = $crEntry['WFTYPE'];
                if ($crEntry['UPDATEPJCONTRACT'] == "yes") {
                    $crEntries[] = $crEntry;
                }
            } else {
                $pclEntries[] = $entry;
            }
        }

        $pcl['ITEMS'] = $pclEntries;
        $pcl['CRENTRIES'] = $crEntries;
    }

    /**
     * @param array $values
     *
     * @return bool
     * @throws Exception
     */
    private function validatePclTaxSchedule(array &$values)
    {
        $ok = true;

        $gManagerFactory = Globals::$g->gManagerFactory;
        $pcnMgr = $gManagerFactory->getManager('projectcontract');
        $pcn = $pcnMgr->get($values['PROJECTCONTRACTID']);

        $taxSolutionId = $values['TAXSOLUTIONID'] ?? $pcn['TAXSOLUTIONID'] ?? null;
        $taxSolutionManager = Globals::$g->gManagerFactory->getManager('taxsolution');
        $taxSolution = $taxSolutionManager->get($taxSolutionId);

        $result = $this->getEntityFromPrjCntLocation($pcn['PROJECTLOCATIONKEY'], $taxSolution['RECORDNO']);

        // Check if the tax schedule is valid for the project location. Return immediately if not.
        if(empty($result)){
            Globals::$g->gErr->addIAError(number: 'CRE-6001', source: __FILE__ . ':' . __LINE__);
            return false;
        }

        $values['TAXSOLUTIONKEY'] = $taxSolution['RECORDNO'];
        $taxScheduleKeyIdMaps = $this->taxScheduleIdForPC($values);
        if(empty($taxScheduleKeyIdMaps) || empty($taxScheduleKeyIdMaps[$values['PCLTAXSCHEDULEID']])){
            Globals::$g->gErr->addIAError(number: 'CRE-6002', source: __FILE__ . ':' . __LINE__);
            return false;
        }
        $values['TAXSCHEDULEKEY'] = $taxScheduleKeyIdMaps[$values['PCLTAXSCHEDULEID']];

        //$values['PROJECTLOCATIONKEY']
        return $ok;
    }
}
