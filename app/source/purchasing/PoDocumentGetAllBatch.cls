<?php
/**
 * DDS PoDocumentGetAllBatch
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2015  Intacct Corporation, All Rights Reserved
 *
 * This is only needed in order to match the creation pattern in GetListBatchFactory::getGetAllBatch()
 */

/**
 * Class PoDocumentGetAllBatch
 */
class PoDocumentGetAllBatch extends DocumentBaseBatch
{

    /**
     * @param string $entity        Name of Entity
     * @param int    $readTimestamp Current time stamp
     * @param int    $pageSize      Page size for data
     * @param array  $params        Query params optional
     */
    function __construct( $entity, $readTimestamp, $pageSize, $params = array() )
    {
        parent::__construct($entity, $readTimestamp, $pageSize, $params);
    }


}