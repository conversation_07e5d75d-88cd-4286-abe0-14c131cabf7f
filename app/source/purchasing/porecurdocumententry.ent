<?php

/**
 *    FILE: porecurdocumententry.ent
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025, Intacct Corporation, All Rights Reserved
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */


require 'recurdocumententry.ent';
$kSchemas['porecurdocumententry'] = $kSchemas['recurdocumententry'];

$kSchemas['porecurdocumententry']['parententity'] = 'porecurdocument';
$kSchemas['porecurdocumententry']['module'] = 'po';

$kSchemas['porecurdocumententry']['dbfilters'] = [
        [ 'documentparams.SALE_PUR_TRANS', '=', 'P' ]
    ];
$kSchemas['porecurdocumententry']['platform_entity'] = 'recurdocumententry';
$kSchemas['porecurdocumententry']['customComponentsEntity'] = 'recurdocumententry';
?>