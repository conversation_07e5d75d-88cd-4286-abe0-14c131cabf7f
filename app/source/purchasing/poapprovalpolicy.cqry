<?php
/**
 * Created by PhpStorm.
 * User: kghosh
 * Date: 12/10/2016
 * Time: 8:48 AM
 */

$kpoapprovalpolicyQueries['QRY_APPROVALPOLICY_SELECT_ALL_BY_MODULE'] = array(
    'QUERY' => "SELECT podocpar.docid, detail.seqno, detail.rulename FROM approvalpolicy policy, approvalpolicydetail detail,docparmst podocpar WHERE policy.cny# = ? and detail.cny# = policy.cny# and detail.policykey = policy.record# and podocpar.cny# = policy.cny# and policy.docparkey = podocpar.record#
                                and policy.modulekey = ? and policy.obj_type = ? ORDER BY policy.record#, detail.seqno",
    'ARGTYPES' => array('integer','text', 'text'),
);
$kpoapprovalpolicyQueries['QRY_EMPLOYEE_WITH_PO_PERMISSION_USR_BASED'] = array(
    'QUERY' => "SELECT E.EMPLOYEEID,  USERINFO.UNRESTRICTED, USERINFO.LOGINID, USERINFO.TYPE, USERINFO.ADMIN, USERINFO.DESCRIPTION  FROM USERINFOMST USERINFO, EMPLOYEE E, POLICYASSIGNMENT P, IAPOLICY IAP WHERE USERINFO.STATUS = 'T' AND USERINFO.CONTACTKEY = E.CONTACTKEY AND USERINFO.CNY# = ? AND E.CNY# = USERINFO.CNY# AND IAP.MODULE = 'po' AND IAP.NAME = 'Purchasing Transactions' AND P.CNY# = USERINFO.CNY# AND P.POLICYKEY = IAP.RECORD# AND P.USER_ROLE_KEY = USERINFO.RECORD# AND P.POLICYVAL LIKE '%add%' AND E.PARENTKEY IS NULL",
    'ARGTYPES' => array('integer'),
);
$kpoapprovalpolicyQueries['QRY_EMPLOYEE_WITH_PO_PERMISSION_ROLE_BASED'] = array(
    'QUERY' => "SELECT E.EMPLOYEEID, USERINFO.RECORD#,  USERINFO.UNRESTRICTED, USERINFO.LOGINID, USERINFO.TYPE, USERINFO.ADMIN, USERINFO.DESCRIPTION FROM USERINFOMST USERINFO, ROLEPOLICYASSIGNMENT P, IAPOLICY IAP, ROLE_USERS UROLES, ROLES ROLES, EMPLOYEE E WHERE USERINFO.STATUS = 'T' AND USERINFO.CONTACTKEY = E.CONTACTKEY AND USERINFO.CNY# = ? AND E.CNY# = USERINFO.CNY# AND UROLES.CNY# = USERINFO.CNY# AND UROLES.USERKEY = USERINFO.RECORD# AND ROLES.CNY# = UROLES.CNY# AND ROLES.RECORD# = UROLES.ROLEKEY AND ROLES.APPLYTO IN ('B','L' ) AND IAP.MODULE = 'po' AND IAP.NAME = 'Purchasing Transactions' AND P.CNY# = USERINFO.CNY# AND P.POLICYKEY = IAP.RECORD# AND P.ROLEKEY = ROLES.RECORD# AND P.POLICYVAL LIKE '%add%' AND E.PARENTKEY IS NULL UNION SELECT E.EMPLOYEEID, USERINFO.RECORD#,  USERINFO.UNRESTRICTED, USERINFO.LOGINID, USERINFO.TYPE, USERINFO.ADMIN, USERINFO.DESCRIPTION FROM USERINFOMST USERINFO, EMPLOYEE E, POLICYASSIGNMENT P, IAPOLICY IAP, EXTERNASSOC EA WHERE USERINFO.STATUS = 'T' AND USERINFO.CONTACTKEY = E.CONTACTKEY AND USERINFO.CNY# = ? AND EA.EXTERNUSERKEY = USERINFO.RECORD# AND EA.CNY# = USERINFO.CNY# AND EA.TYPE = 'P' AND E.CNY# = USERINFO.CNY# AND IAP.MODULE = 'PO' AND IAP.NAME = 'Purchasing Transactions' AND P.CNY# = USERINFO.CNY# AND P.POLICYKEY = IAP.RECORD# AND P.USER_ROLE_KEY = USERINFO.RECORD# AND P.POLICYVAL LIKE '%add%' AND E.PARENTKEY IS NULL",
    'ARGTYPES' => array('integer', 'integer'),
);
$kpoapprovalpolicyQueries['QRY_VALUE_APPROVAL_DEPT_APPROVERS'] = array(
    'QUERY' => "SELECT APPROVALRULEDETAIL.APPROVER FROM  APPROVALRULE APPROVALRULE , APPROVALRULEDETAIL APPROVALRULEDETAIL WHERE    APPROVALRULE.CNY#= ? AND APPROVALRULEDETAIL.CNY#= APPROVALRULE.CNY# AND APPROVALRULE.RULESETKEY= ? AND APPROVALRULE.RECORD#=APPROVALRULEDETAIL.RULEKEY",
    'ARGTYPES' => array('integer', 'integer'),
);

