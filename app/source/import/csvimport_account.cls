<?  
//=============================================================================
//
//	FILE:					imp_accounts.inc
//	AUTHOR:					<PERSON>
//	DESCRIPTION:			Account/Account Balance import
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================

	require_once('csvimport_base.cls');
	
	class csvimport_account extends csvimport_base {

        /* @var array $lookupcloseto */
		var $lookupcloseto;

        /* @var array $accttypes */
		var $accttypes;

        /* @var array  $closetype */
		var $closetype;

        /* @var string $imptype */
		var $imptype = 'gl account';

        /* @var array $acctinfo */
		var $acctinfo;

        /* @var int $primacctnolen */
		var $primacctnolen;

        /* @var string $acctnoseperator */
		var $acctnoseperator;

        /* @var int $subacctnolen */
		var $subacctnolen;

        /**
         * @param array $args
         */
		function __construct(/** @noinspection PhpUnusedParameterInspection */ $args = []) {
			include ('csv_metadata_account.inc');
			import('ManagerFactory');	//handles all the entitymanagers

			global $gManagerFactory;

			$this->acctMgr = $gManagerFactory->getManager('glaccount');
			
            /** @noinspection PhpUndefinedVariableInspection */
            $this->imeta = $imeta;
            /** @noinspection PhpUndefinedVariableInspection */
            $this->imetagroom = $imetagroom;
            /** @noinspection PhpUndefinedVariableInspection */
            $this->imetaDict = $imetaDict;
			$this->accttypes = $this->buildMetaEnumFromDict($imetaDict, 'ACCOUNTTYPE');
			$this->closetype = $this->buildMetaEnumFromDict($imetaDict, 'CLOSINGTYPE');
			
			$accts = $this->acctMgr->DoQuery('QRY_GLACCOUNT_SELECT_ACCT_NO_INFO',array(),true);
            foreach( $accts as $acctval){
				$this->acctinfo[$acctval['ACCT_NO']]['STATUS'] = $acctval['STATUS'] ;
			}
			global $_userid;
			$co = GetAcctCompany($_userid);
			$this->primacctnolen = $co['PRIMACCTNOLEN'];
			$this->acctnoseperator = $co['ACCTNOSEPERATOR'];
			$this->subacctnolen = $co['SUBACCTNOLEN'];

			// current entity manager
			$this->entMgr = $this->acctMgr;

			// merge imeta and imetagroom structures
			$this->MergeMeta();
            
            if ( !util_isPlatformDisabled() ) {
                $this->MergeRequireCustomDimensions();
            }
		}

        /**
         * @param array $importParams
         * @param array $returnParams
         *
         * @return bool
         */
		function ImportCSVFile($importParams, &$returnParams) {

			$gErr = Globals::$g->gErr;

			if ($this->primacctnolen == '') {
				$gErr->addError("GL-0591", __FILE__ . ":" . __LINE__, "The Account Length has not been selected.
				Goto Company->Setup->Company Info and select the Primary Account# Length");
				if ($this->processOffline != true) {
					require_once('popuperror.phtml');
					exit();
				}else {
					$this->errCount++;
				}
			}
			return parent::ImportCSVFile($importParams, $returnParams);

		}

        /**
         * @param array $rowarray
         *
         * @return bool
         */
		function Import($rowarray) {
            epp('$rowarray:');eppp($rowarray);
            //dieFL();
            
			global $gErr;
			$result = true;

			if ($rowarray['CLOSETOACCT_NO']) {
				if ($this->acctinfo[$rowarray['CLOSETOACCT_NO']]['STATUS'] == 'F'){
					$gErr->addError('GL-0592', __FILE__ . ':' . __LINE__,
					"Cannot close into a INACTIVE account number");
					$result = false;
				}

				if (!$this->lookupcloseto[$rowarray['CLOSETOACCT_NO']]) {
					$acct = $this->acctMgr->DoQuery('QRY_GLACCOUNT_SELECT_RAW_VID',array($rowarray['CLOSETOACCT_NO']),true);
					if(!$acct) {
						$gErr->addIAError('GL-0593', __FILE__ . ':' . __LINE__,
						    "CLOSETOACCT_NO does not contain a valid account number",
						    ['CLOSETOACCT_NO' => 'CLOSETOACCT_NO']
						);
						$result = false;
					} else {
						$this->lookupcloseto[$rowarray['CLOSETOACCT_NO']] = $rowarray['CLOSETOACCT_NO'];
#						$rowarray['CLOSETOACCT_NO'] = $closetoacctkey;
					}
					unset($acct);
				} else {
					$rowarray['CLOSETOACCT_NO'] = $this->lookupcloseto[$rowarray['CLOSETOACCT_NO']];
				}
			}
	
			if ($rowarray['ACCT_TYPE'] == 'I' && $rowarray['CLOSEABLE'] == 'R'){
				$gErr->addError('GL-0594', __FILE__ . ':' . __LINE__,
				"Income Statement account cannot be 'closed to'");
				$result = false;
			}

			if (!preg_match('/^(DB|CR)/', $this->upper($rowarray['NORMAL_BALANCE']))) {
				$gErr->addIAError('GL-0595', __FILE__ . ':' . __LINE__,
				    "NORMAL_BALANCE may only contain the following values:  DB,CR ",
				    ['NORMAL_BALANCE'=>'NORMAL_BALANCE', 'DB' => 'DB', 'CR' => 'CR']
				);
				$result = false;
			} else {
				if (isl_strtoupper($rowarray['NORMAL_BALANCE']) == 'DB') {
					$rowarray['NORMAL_BALANCE'] = 'debit';	//changed 1 to Debit
				} else {
					$rowarray['NORMAL_BALANCE'] = 'credit';	//changed -1 to Credit		
				}
			}
			// Compare account lengths, separators
			if ($this->subacctnolen && $this->acctnoseperator) {
				// Escape the Pattern before search 
				$splitpattern = $this->acctnoseperator;
				$accts = explode($splitpattern, $rowarray['ACCT_NO']);
				foreach ($accts as $id => $acct) {
					if (!$id) {
						// Make sure primary account length is ok.
						if ((isl_strlen($acct)!= $this->primacctnolen)) {
							$gErr->addIAError('GL-0596', __FILE__ . ':' . __LINE__,
							    "Primary Account number invalid length, expecting(" . $this->primacctnolen . ").
							    Or an invalid Account Field# Seperator is used.",
							    ['PRIMACCTNOLEN' => $this->primacctnolen]
							);
							$result = false;
							break;
						}
					}
					else {
						// Check the secondary accounts
						if (isl_strlen($acct) != $this->subacctnolen) {
							$gErr->addIAError('GL-0597', __FILE__ . ':' . __LINE__,
							    "Subaccount $acct has invalid length, expecting(" . $this->subacctnolen . ")",
							    ['ACCT' => $acct, 'SUBACCTNOLEN' => $this->subacctnolen]
							);
							$result = false;
							break;
						}
					}
				}
			}
			else {
				// No subaccounts
				if (isl_strlen($rowarray['ACCT_NO']) != $this->primacctnolen) {
					$gErr->addIAError('GL-0598', __FILE__ . ':' . __LINE__,
					    "Account number invalid length, expecting(" . $this->primacctnolen . ")",
					    ['PRIMACCTNOLEN' => $this->primacctnolen]);
					$result = false;
				}
			}

			//To Set Default value for Require Department and Require Location
			if (!isset($rowarray['REQ_DEPT']) || $rowarray['REQ_DEPT']=='' ) {
				$rowarray['REQ_DEPT'] = 'F';
			}
			if (!isset($rowarray['REQ_LOC']) || $rowarray['REQ_LOC']=='' ) {
				$rowarray['REQ_LOC'] = 'F';
			}
			
			// to set default values for Require dimensions
			$dimInfo = $this->acctMgr->GetDimensionFields();
			$bool = array('T', 'F');

			if (!empty($dimInfo)) {
                foreach ( $dimInfo as $val) {
					if (!isset($rowarray[$val['path']]) || $rowarray[$val['path']] == '') {
						$rowarray[$val['path']] = 'F';
					} else {
						if (!in_array($rowarray[$val['path']], $bool)) {
							$gErr->addIAError(
								'GL-0599', __FILE__ . ':' . __LINE__,
								"Invalid Require " . substr($val['path'], 7) . " " . $rowarray[$val['path']],
								['PATH' => substr($val['path'], 7), 'ROWPATH' => $rowarray[$val['path']]]
							);
							$result = false;
						}
					}
				}
			}

			// to validate custom diamentions
			$customDiamentions = GLEntryManager::getPTFieldsMap();
			if (count($customDiamentions) > 0) {
				foreach ($customDiamentions as $diamention) {
					$key = 'REQUIRE' . $diamention;
					if (!isset($rowarray[$key]) || $rowarray[$key] == '') {
						$rowarray[$key] = 'F';
					} else {
						if (!in_array($rowarray[$key], $bool)) {
							$gErr->addIAError(
								'GL-0600', __FILE__ . ':' . __LINE__,
								"Invalid Require " . $diamention . " " . $rowarray[$key],
								['DIAMENTION' => $diamention, 'ROWARRKEY' => $rowarray[$key]]
							);
							$result = false;
						}
					}
				}
			}

			if (!in_array($rowarray['REQ_DEPT'], $bool)){
				$gErr->addIAError('GL-0601', __FILE__ . ':' . __LINE__,
				    "Invalid Require Department " . $rowarray['REQ_DEPT'], ['REQ_DEPT' => $rowarray['REQ_DEPT']]
				);
				$result = false;
			}
			if (!in_array($rowarray['REQ_LOC'], $bool)) {
				$gErr->addIAError(
					'GL-0602', __FILE__ . ':' . __LINE__, "Invalid Require Location " . $rowarray['REQ_LOC'],
					['REQ_LOC'=>$rowarray['REQ_LOC']]);
				$result = false;
			}

			// validate active field
			if (!isset($rowarray['ACTIVE']) || $rowarray['ACTIVE'] == '') {
				$rowarray['ACTIVE'] = 'T';
			} else {
				if (!in_array($rowarray['ACTIVE'], $bool)) {
					$gErr->addIAError(
						'GL-0603', __FILE__ . ':' . __LINE__,
						"Invalid Require Active " . $rowarray['ACTIVE'], ['ACTIVE' => $rowarray['ACTIVE']]
					);
					$result = false;
				}
			}
			
			// if there is no industry selected make category null
			$industry = GetMyIndustryType();
			if (!isset($industry) || $industry=='') {
				$rowarray['CATEGORY'] = ''; 
			}

			if ($result) {
				foreach ($this->imeta as $key => $value) {
					if ($this->imetagroom[$value]) {
						$function = $this->imetagroom[$value];
						$rowarray[$value] = $this->$function($rowarray[$value]);
					}
					$account[$key] = $rowarray[$value];
				}
                /** @noinspection PhpUndefinedVariableInspection */
                $account['ACCOUNTTYPE'] = $this->accttypes[$account['ACCOUNTTYPE']];
				$account['CLOSINGTYPE'] = $this->closetype[$account['CLOSINGTYPE']];

				$account = $this->acctMgr->FlatToStructured($account);

                if (!util_isPlatformDisabled() && !$this->validateAndTranslatePlatformRels(
                        $this->entMgr->_entity, $account)) {
                    return false;
                }
                //epp('$account:');eppp($account);dieFL();
                
				$result = $this->acctMgr->add($account);
			}
			
			if ($result){
                /** @noinspection PhpUndefinedVariableInspection */
                $this->acctinfo[$account['ACCOUNTNO']]['STATUS'] = $rowarray['STATUS'];
			}

			return $result;
		}
        
        function MergeRequireCustomDimensions() {
            $map = GLEntryManager::getPTFieldsMap();

            foreach ( $map as $path ) {
                $requirepath = "REQUIRE" . isl_strtoupper($path);
                $this->imeta[ $requirepath ] = $requirepath;
                $this->imetagroom[ $requirepath ] = 'booleanF';
            }
        }

        /**
         * @param string $objRec
         * @param array $nodes
         *
         * @return  bool
         */
		function MergeMetaGroom($objRec, $nodes) {
            $ok = parent::MergeMetaGroom($objRec, $nodes);

            if (!$ok) {
                return false;
            }

            foreach ( $nodes as $dim ) {
				$this->imetagroom[$dim['path']]  = 'booleanT';
                $this->imetaDict[$dim['path']]['default'] = 'F';
			}

			return $ok;
		}


        /**
         * @param string $entity
         *
         * @return string
         */
		function GetPrefixForDimension($entity) {
			return '';
		}

	}
				
