<?
//=============================================================================
//
//	FILE:			csvimport_exchangerates.cls
//	AUTHOR:			Nithin MG
//	DESCRIPTION:	Class for the CSV Import of Exchange rates
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information herein
//	may not be used, copied or disclosed in whole or part without prior
//	written consent from Intacct Corporation.
//
//=============================================================================

class csvimport_exchangerates extends csvimport_base {

    /* @var string $imptype */
    var $imptype = 'exchangerates';

    /* @var string $basecurr */
    var $basecurr;

    /* @var ExchangeRateTypesManager $entMgrExchangeType */
    var $entMgrExchangeType;

    /* @var bool $atlas */
    var $atlas;

    /**
     * @param string $args
     */
    public function __construct(/** @noinspection PhpUnusedParameterInspection */ $args = '') {
        include('csv_metadata_exchangerates.inc');

        $gManagerFactory = Globals::$g->gManagerFactory;
        $this->entMgr = $gManagerFactory->getManager('exchangerate');
        $this->entMgrExchangeType = $gManagerFactory->getManager('exchangeratetypes');

        /** @noinspection PhpUndefinedVariableInspection */
        $this->imeta = $imeta;
        /** @noinspection PhpUndefinedVariableInspection */
        $this->imetagroom = $imetagroom;

        $this->basecurr = GetBaseCurrency();
        $this->atlas = IsMCMESubscribed();

        // merge imeta and imetagroom structures
        $this->MergeMeta();
    }

    /**
     * Override function of the base to get file instead of row contents
     * @param array $obj
     *
     * @return bool
     */
    public function _GetNextImportObject(&$obj)
    {
        return $this->_GetImportCSVTransaction($this->impFP, $this->objMap, $obj);
    }

    /**
     * Import the exchange rates
     *
     * @param array $rowarray The CSV row to import
     *
     * @return bool
     */
    public function Import($rowarray)
    {
        $ok = $this->ConstructRecord($rowarray, $exChangeRate);

        if (!$ok) {
            return false;
        }

        // Get the exchange rate type ID
        $id = $this->entMgrExchangeType->GetExchangeRateTypeID($exChangeRate['TYPENAME']);
        if (empty($id)) {
            $msg = "Unknown Exchange Rate Type Name - " . $exChangeRate['TYPENAME'];
            Globals::$g->gErr->addIAError('CO-0997', __FILE__ . ':' . __LINE__, $msg,
                                          [ 'TYPENAME' => $exChangeRate['TYPENAME']]);
            return false;
        }
        // Translate the given exchange rate type name to its found type id.
        $exChangeRate['TYPE_ID'] = $id;

        // Check for duplicate exchange rate type. If duplicate, update the existing rates
        if ($id > 0 && $id != '') {
            $isUpdate = $this->entMgr->IsDuplicateExchangeRate(
                $id, $exChangeRate['FROM_CURRENCY'], $exChangeRate['TO_CURRENCY']
            );
        } else {
            $isUpdate = false;
        }

        if ($isUpdate) {
            $params = array(
                'selects' => array(
                    'RECORDNO'
                ),
                'filters' => array(
                    array(
                        array('TYPE_ID', '=', $id),
                        array('FROM_CURRENCY', '=', $exChangeRate['FROM_CURRENCY']),
                        array('TO_CURRENCY', '=', $exChangeRate['TO_CURRENCY']),
                    ),
                ),
            );

            $exchangeRateId = $this->entMgr->GetList($params);

            if (!isset($exchangeRateId[0]['RECORDNO']) || $exchangeRateId[0]['RECORDNO'] == '') {
                return false;
            }

            // Fetch the existing exchange rate
            $rowDatas = $this->entMgr->get($exchangeRateId[0]['RECORDNO']);

            // Compare the effective state date. If date already exists, update the rate and reciprocal rate
            // else add as a new line item
            $lineItems = array();
            $index = 0;
            foreach ($rowDatas['EXCHANGE_RATE_ENTRY'] as $rowData) {
                $lineItems[$rowData['EFFECTIVE_START_DATE']] = $index;
                $index++;
            }

            foreach ($exChangeRate['EXCHANGE_RATE_ENTRY'] as $rate) {
                $reciprocalRate = ibcdiv('1', $rate['EXCHANGE_RATE'], 8, true);
                if (isset($lineItems[$rate['EFFECTIVE_START_DATE']])) {
                    $arrayPos = $lineItems[$rate['EFFECTIVE_START_DATE']];
                    $rowDatas['EXCHANGE_RATE_ENTRY'][$arrayPos]['EXCHANGE_RATE'] = $rate['EXCHANGE_RATE'];
                    $rowDatas['EXCHANGE_RATE_ENTRY'][$arrayPos]['RECIPROCAL_RATE'] = $reciprocalRate;
                } else {
                    // Add the new line item
                    $rowDatas['EXCHANGE_RATE_ENTRY'][] = array(
                        '__dummy' => '',
                        '_isNewLine' => 1,
                        'EFFECTIVE_START_DATE' => $rate['EFFECTIVE_START_DATE'],
                        'EXCHANGE_RATE' => $rate['EXCHANGE_RATE'],
                        'RECIPROCAL_RATE' => $reciprocalRate
                    );
                }
            }

            $ok = $this->entMgr->set($rowDatas);
        } else {
            $ok = $this->entMgr->add($exChangeRate);
        }

        return $ok;
    }

    /**
     * The function construct the record for importing
     *
     * @param array $rowarray CSV row to import
     * @param array &$exChangeRate
     *
     * @return bool
     */
    protected function ConstructRecord($rowarray, &$exChangeRate)
    {
        $ok = true;
        foreach ($rowarray as $lineitem) {
            foreach ($this->imeta as $key => $value) {

                if ($this->imetagroom[$value]) {
                    $function = $this->imetagroom[$value];
                    $lineitem[$value] = $this->$function($lineitem[$value]);
                }

                $exChangeRateArr[$key] = $lineitem[$value];
            }

            /** @noinspection PhpUndefinedVariableInspection */
            $structarray = $this->entMgr->FlatToStructured($exChangeRateArr);

            if ($structarray['LINE_NO'] == '1') {
                $toCurrency = (!$this->atlas) ? $this->basecurr : $structarray['TO_CURRENCY'];
                $exChangeRate = array(
                    'TYPENAME' => $structarray['TYPE_NAME'],
                    'FROM_CURRENCY' => $structarray['FROM_CURRENCY'],
                    'TO_CURRENCY' => $toCurrency
                );
            }

            // Entries
            $lineItems = array(
                'EFFECTIVE_START_DATE' => $structarray['EFFECTIVE_START_DATE'],
                'EXCHANGE_RATE' => $structarray['EXCHANGE_RATE'],
            );

            $exChangeRate['EXCHANGE_RATE_ENTRY'][] = $lineItems;

        }

        return $ok;
    }

    /**
     * Override function for writing the error file
     *
     * @param array     $obj       the data
     * @param resource  $errfp     the file pointer
     * @param int       $noheaders true if no header else false
     */
    public function _WriteToImportErrorFile($obj, $errfp, $noheaders = 1)
    {
        $this->_writeUTF8Bom($errfp);
        foreach ( $obj as $values) {
            $csvstring = ArrayToCSVString($values, [], $noheaders);
            fwrite($errfp, $csvstring);
        }
    }
}

