<?php
/**
 * Gantt Chart configuration entity Editor
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 Intacct Corporation, All Rights Reserved
 */

/**
 * Editor class for the Gantt Chart configuration entity
 */
class GanttChartEditor extends FormEditor
{
    /**
     * @param array $_params the initialization parameters of the class
     */
    public function __construct($_params)
    {
        parent::__construct($_params);

    }

    /**
     * getCssFileNames - return list of css files needed
     *
     * @return array of css files to be included
     */
    protected function getCssFileNames()
    {
        $cssfiles[] = "../resources/css/project.css";
        return $cssfiles;
    }

    /**
     * mediateDataAndMetadata - override base class to adjust data as necessary
     *
     * At the time this function is called:
     *   - the data is available and it is in view format.
     *   - the metadata is expanded and the view objects are built - use $this->getView() call to get a reference to
     *     the view object.
     *
     * @param array $obj  the data
     *
     * @return bool  true on success and false on failure
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        parent::mediateDataAndMetadata($obj);

        // fetch user preferences, substituting defaults for any not specified
        $userPrefs = GetUserPreferencesLike('GANTT_%');
        $ganttPrefs = [];
        foreach (GanttChartManager::$defaultPrefs as $key => $value) {
            if (array_key_exists($key, $userPrefs)) {
                $ganttPrefs[$key] = $userPrefs[$key];
            } else {
                $ganttPrefs[$key] = $value;
            }
        }

        // if employee user, hide Project manager filter
        $userType = GetMyUserType();
        $view = $this->getView();

        if ($userType == 'E') {
            $fields = array();
            $view->findComponents(array('path' => 'PROJECTMANAGER'), EditorComponentFactory::TYPE_FIELD, $fields);
            if (isset($fields[0])) {
                $fields[0]->setProperty('hidden', true);
            }
        }

        if (!IsOperationAllowed(GetOperationId('pa/activities/ganttchart/financial')) ) {
            $ganttPrefs['GANTT_SHOWPROGRESS'] = "true";
            $ganttPrefs['GANTT_SHOWCOST'] = "false";
            $ganttPrefs['GANTT_SHOWMARGIN'] = "false";
            $ganttPrefs['GANTT_SHOWBILLING'] = "false";

            // disable the showbars controls
            $paths = ['SHOW_PROGRESS', 'SHOW_COST', 'SHOW_BILLING', 'SHOW_MARGIN'];
            $fields = array();
            foreach ($paths as $path) {
                $view->findComponents(array('path' => $path), EditorComponentFactory::TYPE_FIELD, $fields);
            }
            foreach ( $fields as $key => $field) {
                $fields[$key]->setProperty('readonly', true);
            }
        }

        // now set the current state for all controls
        $obj['SHOW_RESOURCES']   = $ganttPrefs['GANTT_SHOWRESOURCES'];
        $obj['PROJECTGROUPPICK'] = $ganttPrefs['GANTT_PROJECTGROUP'];
        $obj['SHOW_PROGRESS']    = $ganttPrefs['GANTT_SHOWPROGRESS'];
        $obj['SHOW_COST']        = $ganttPrefs['GANTT_SHOWCOST'];
        $obj['SHOW_BILLING']     = $ganttPrefs['GANTT_SHOWBILLING'];
        $obj['SHOW_MARGIN']      = $ganttPrefs['GANTT_SHOWMARGIN'];
        $obj['FROMDATE']         = $ganttPrefs['GANTT_STARTDATE'];
        $obj['TODATE']           = $ganttPrefs['GANTT_ENDDATE'];
        $obj['DAYS_BEFORE']      = $ganttPrefs['GANTT_DAYSBEFORE'];
        $obj['DAYS_AFTER']       = $ganttPrefs['GANTT_DAYSAFTER'];
        $obj['MAX_PROJECTS']     = $ganttPrefs['GANTT_MAXPROJECTS'];
        $obj['TIME_UNITS']       = $ganttPrefs['GANTT_DATEUNIT'];
        $obj['PROJECTMANAGER']   = $ganttPrefs['GANTT_PROJECTMANAGER'];
        $obj['PROJECTSTATUS']    = $ganttPrefs['GANTT_PROJECTSTATUS'];
        $obj['CUSTOMER']         = $ganttPrefs['GANTT_CUSTOMER'];
        $obj['SHOW_TODAY']       = $ganttPrefs['GANTT_SHOWTODAY'];
        $obj['TIME_SPAN']        = $ganttPrefs['GANTT_TIMESPAN'];
        $obj['PROJECT_COLOR']    = $ganttPrefs['GANTT_PROJECTCOLOR'];
        $obj['TASK_COLOR']       = $ganttPrefs['GANTT_TASKCOLOR'];
        $obj['RESOURCE_COLOR']   = $ganttPrefs['GANTT_RESOURCECOLOR'];
        $obj['PROGRESS_COLOR']   = $ganttPrefs['GANTT_PROGRESSCOLOR'];
        $obj['COST_COLOR']       = $ganttPrefs['GANTT_COSTCOLOR'];
        $obj['BILLING_COLOR']    = $ganttPrefs['GANTT_BILLINGCOLOR'];
        $obj['MARGIN_COLOR']     = $ganttPrefs['GANTT_MARGINCOLOR'];
        $obj['OVER_COLOR']       = $ganttPrefs['GANTT_OVERCOLOR'];

        return true;
    }

    /**
     * canSaveAndNew - hide the 'Save and New' button
     *
     * @return bool false
     */
    protected function canSaveAndNew()
    {
        return false;
    }

}
