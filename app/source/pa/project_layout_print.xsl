<?xml version="1.0" encoding="ISO-8859-1"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format">
	<xsl:template match="/">
		<xsl:apply-templates select="ROOT"/>
	</xsl:template>
	<xsl:template match="ROOT">
		<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:svg="http://www.w3.org/2000/svg">
			<!--  master template -->
			<fo:layout-master-set>
				<!-- <fo:simple-page-master margin-right="0.60in" margin-left="0.60in" margin-bottom="0in" margin-top="0.5in" page-width="8.5in" page-height="11.0in" master-name="first"> -->
				<fo:simple-page-master 
						margin-right="0.50in" 
						margin-left="0.5in" 
						margin-bottom="0in" 
						margin-top="0.5in" 
						page-width="8.5in" 
						page-height="11.0in" 
						master-name="first">
					<!-- <fo:region-body overflow="auto" margin-top="0.20in" margin-bottom="0.60in"/> -->
					<fo:region-body overflow="visible" margin-top="0.20in" margin-bottom="0.60in"/> 
					<fo:region-before extent="1.00in"/>
					<fo:region-after extent="0.60in"/>
				</fo:simple-page-master>
			</fo:layout-master-set>
			<!--  page printing -->
			<fo:page-sequence master-reference="first">
				<fo:static-content flow-name="xsl-region-before">
					<!-- static page content -->
					<!-- Letterhead -->
					<!-- company logo -->
					<fo:block-container height="0.6in" width="1.0in" top="0in" left="0.08in" position="absolute">
						<fo:block>
							<fo:external-graphic max-height="0.6in" max-width="1.0in" src="{COMPANY/LOGO}"/>
						</fo:block>
					</fo:block-container>
					<!-- company fields -->
					<fo:block-container height="0.7in" width="2.5in" top="0.55in" left="0in" position="absolute">
						<fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="10pt">
							<xsl:value-of select="COMPANY/TITLE"/>
						</fo:block>
						<fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="10pt">
							<xsl:value-of select="COMPANY/ADDRESS1"/>
						</fo:block>
						<fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="10pt">
							<xsl:value-of select="COMPANY/ADDRESS2"/>
						</fo:block>
						<fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="10pt">
							<xsl:value-of select="COMPANY/CITY"/>
							<xsl:text>, </xsl:text>
							<xsl:value-of select="COMPANY/STATE"/>
							<xsl:text> </xsl:text>
							<xsl:value-of select="COMPANY/ZIPCODE"/>
						</fo:block>
						<fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="10pt">
							<xsl:value-of select="COMPANY/CONTACTPHONE"/>
						</fo:block>
					</fo:block-container>
					<!-- Title -->
					<!-- Title background -->
					<fo:block-container height="0.61in" width="2.6in" top="0in" left="3.88in" position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="2.6in" height="0.61in">
									<svg:rect x="0.15in" y="0in" width="2.3in" height="0.3in" style="fill: #BBBBBB"/>
									<svg:circle cx="0.15in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
									<svg:circle cx="2.45in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- Title Text -->
					<fo:block-container height="0.61in" width="2.3in" top=".08in" left="4.28in" position="absolute">
						<fo:block text-align="start" color="#ffffff" line-height="15pt" font-family="Times Roman" font-size="13pt" font-weight="bold">
<xsl:value-of select="RENAMETERM/Term_Project"/><xsl:text> Information</xsl:text>						</fo:block>
					</fo:block-container>
					<!-- Up-right static -->
					<!-- Vertical Line under title -->
					<fo:block-container height="0.6in" width="0.001in" top="0.3in" left="5.23in" position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="0.001in" height="0.6in">
									<svg:rect x="0in" y="0in" width="0.0001in" height="0.6in" style="fill: #FFFFFF; stroke:black"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- Text at the right of the vertical line -->
					<fo:block-container height="0.6in" width="0.5in" top="0.60in" left="5.33in" position="absolute">
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="12pt">
							<xsl:value-of select="REC/PROJECTID"/>
						</fo:block>
					</fo:block-container>
					<!-- Text at the left of the vertical line -->
					<fo:block-container height="0.6in" width="0.5in" top="0.6in" left="4.63in" position="absolute">
						<fo:block text-align="end" line-height="11pt" font-family="Helvetica" font-size="8pt">
<xsl:value-of select="RENAMETERM/Term_Project"/>						</fo:block>
						<fo:block text-align="center" line-height="11pt" font-family="Helvetica" font-size="7pt">
							<xsl:if test="REC/STATUS and (REC/STATUS != 'Active')">
								(<xsl:value-of select="REC/STATUS"/>)
							</xsl:if> 
						</fo:block>
					</fo:block-container>
				</fo:static-content>
				<fo:static-content flow-name="xsl-region-after">
					<!-- footer - page number -->
					<fo:block-container height="0.2in" width="7.38in" top="0.01in" left="0in" position="absolute">
						<fo:block>
							<fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
						</fo:block>
					</fo:block-container>
					<fo:block-container height="0.5in" width="7.38in" top="0.20in" left="0in" position="absolute">
						<fo:table height="0.25in" width="7.38in">
							<fo:table-column column-width="3.69in"/>
							<fo:table-column column-width="3.69in"/>
							<fo:table-body font-family="Helvetica" font-weight="normal" font-size="8pt">
								<fo:table-row line-height="11pt">
									<fo:table-cell>
										<fo:block border-width="1.0pt" border-color="white" text-align="start" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">
<xsl:value-of select="RENAMETERM/Term_Project"/>										</fo:block>
									</fo:table-cell>
									<fo:table-cell>
										<fo:block border-width="1.0pt" border-color="white" text-align="end" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">IA.PAGE <fo:page-number/>
										</fo:block>
									</fo:table-cell>
								</fo:table-row>
							</fo:table-body>
						</fo:table>
					</fo:block-container>
				</fo:static-content>
				<fo:flow flow-name="xsl-region-body">
			    <!-- dynamic page content -->
					<!-- Fields 1 -->
					<fo:block-container width="6.0in" height="9.0in" top="1.82in" left="0in" position="absolute">
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:value-of select="RENAMETERM/Term_Project"/><xsl:text> ID</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/PROJECTID"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:value-of select="RENAMETERM/Term_Project"/><xsl:text> Name</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/NAME"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:value-of select="RENAMETERM/Term_Project"/><xsl:text> Category</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/PROJECTCATEGORY"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>Description</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/DESCRIPTION"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>Parent </xsl:text><xsl:value-of select="RENAMETERM/Term_Project"/><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/PARENTID"/>
						</fo:block>
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                            <xsl:text>Invoice with Parent</xsl:text><xsl:text>: </xsl:text>
                            <xsl:value-of select="REC/INVOICEWITHPARENT"/>
                        </fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:value-of select="RENAMETERM/Term_Project"/><xsl:text> Type</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/PROJECTTYPE"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:value-of select="RENAMETERM/Term_Project"/><xsl:text> Status</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/PROJECTSTATUS"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:value-of select="RENAMETERM/Term_Customer"/><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/CUSTOMERID"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:value-of select="RENAMETERM/Term_Project"/><xsl:text> Manager</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/MANAGERID"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>External user (customer)</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/CUSTUSERID"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>Sales Contact</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/SALESCONTACTID"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>Reference Number</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/DOCNUMBER"/>
						</fo:block>
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                            <xsl:text>Timesheet and Expense user restrictions</xsl:text><xsl:text>: </xsl:text>
                            <xsl:value-of select="REC/USERRESTRICTIONS"/>
                        </fo:block>
                        <!-- Timesheet and Expense user restrictions -->
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>Status</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/STATUS"/>
						</fo:block>
					</fo:block-container>
				</fo:flow>
			</fo:page-sequence>
			<!--  page printing -->
			<fo:page-sequence master-reference="first">
				<fo:static-content flow-name="xsl-region-before">
					<!-- static page content -->
					<!-- Letterhead -->
					<!-- company logo -->
					<fo:block-container height="0.6in" width="1.0in" top="0in" left="0.08in" position="absolute">
						<fo:block>
							<fo:external-graphic max-height="0.6in" max-width="1.0in" src="{COMPANY/LOGO}"/>
						</fo:block>
					</fo:block-container>
					<!-- company fields -->
					<!-- Title -->
					<!-- Title background -->
					<fo:block-container height="0.61in" width="2.6in" top="0in" left="3.88in" position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="2.6in" height="0.61in">
									<svg:rect x="0.15in" y="0in" width="2.3in" height="0.3in" style="fill: #BBBBBB"/>
									<svg:circle cx="0.15in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
									<svg:circle cx="2.45in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- Title Text -->
					<fo:block-container height="0.61in" width="2.3in" top=".08in" left="4.28in" position="absolute">
						<fo:block text-align="start" color="#ffffff" line-height="15pt" font-family="Times Roman" font-size="13pt" font-weight="bold">
<xsl:value-of select="RENAMETERM/Term_Project"/><xsl:text> Information</xsl:text>						</fo:block>
					</fo:block-container>
					<!-- Up-right static -->
					<!-- Vertical Line under title -->
					<fo:block-container height="0.6in" width="0.001in" top="0.3in" left="5.23in" position="absolute">
						<fo:block>
							<fo:instream-foreign-object>
								<svg:svg width="0.001in" height="0.6in">
									<svg:rect x="0in" y="0in" width="0.0001in" height="0.6in" style="fill: #FFFFFF; stroke:black"/>
								</svg:svg>
							</fo:instream-foreign-object>
						</fo:block>
					</fo:block-container>
					<!-- Text at the right of the vertical line -->
					<fo:block-container height="0.6in" width="0.5in" top="0.60in" left="5.33in" position="absolute">
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="12pt">
							<xsl:value-of select="REC/PROJECTID"/>
						</fo:block>
					</fo:block-container>
					<!-- Text at the left of the vertical line -->
					<fo:block-container height="0.6in" width="0.5in" top="0.6in" left="4.63in" position="absolute">
						<fo:block text-align="end" line-height="11pt" font-family="Helvetica" font-size="8pt">
<xsl:value-of select="RENAMETERM/Term_Project"/>						</fo:block>
						<fo:block text-align="center" line-height="11pt" font-family="Helvetica" font-size="7pt">
							<xsl:if test="REC/STATUS and (REC/STATUS != 'Active')">
								(<xsl:value-of select="REC/STATUS"/>)
							</xsl:if> 
						</fo:block>
					</fo:block-container>
				</fo:static-content>
				<fo:static-content flow-name="xsl-region-after">
					<!-- footer - page number -->
					<fo:block-container height="0.2in" width="7.38in" top="0.01in" left="0in" position="absolute">
						<fo:block>
							<fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
						</fo:block>
					</fo:block-container>
					<fo:block-container height="0.5in" width="7.38in" top="0.20in" left="0in" position="absolute">
						<fo:table height="0.25in" width="7.38in">
							<fo:table-column column-width="3.69in"/>
							<fo:table-column column-width="3.69in"/>
							<fo:table-body font-family="Helvetica" font-weight="normal" font-size="8pt">
								<fo:table-row line-height="11pt">
									<fo:table-cell>
										<fo:block border-width="1.0pt" border-color="white" text-align="start" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">
<xsl:text>Additional Info</xsl:text>										</fo:block>
									</fo:table-cell>
									<fo:table-cell>
										<fo:block border-width="1.0pt" border-color="white" text-align="end" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">IA.PAGE <fo:page-number/>
										</fo:block>
									</fo:table-cell>
								</fo:table-row>
							</fo:table-body>
						</fo:table>
					</fo:block-container>
				</fo:static-content>
				<fo:flow flow-name="xsl-region-body">
			    <!-- dynamic page content -->
					<!-- Fields 1 -->
					<fo:block-container width="6.0in" height="9.0in" top="1.82in" left="0in" position="absolute">
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>Primary Contact</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/CONTACTINFO/CONTACTNAME"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>Bill To Contact</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/BILLTO/CONTACTNAME"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>Ship To Contact</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/SHIPTO/CONTACTNAME"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>Term</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/TERMNAME"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>Billing Type</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/BILLINGTYPE"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>Begin Date</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/BEGINDATE"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>End Date</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/ENDDATE"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:value-of select="RENAMETERM/Term_Department"/><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/DEPARTMENTID"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:value-of select="RENAMETERM/Term_Location"/><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/LOCATIONID"/>
						</fo:block>
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:value-of select="RENAMETERM/Term_Class"/><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/CLASSID"/>
						</fo:block>
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                            <xsl:text>Attachments</xsl:text><xsl:text>: </xsl:text>
                            <xsl:value-of select="REC/SUPDOCID"/>
                        </fo:block>
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                            <xsl:text>Billable Employee Expenses</xsl:text><xsl:text>: </xsl:text>
                            <xsl:value-of select="REC/BILLABLEEXPDEFAULT"/>
                        </fo:block>
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                            <xsl:text>Billable AP / PO</xsl:text><xsl:text>: </xsl:text>
                            <xsl:value-of select="REC/BILLABLEAPPODEFAULT"/>
                        </fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>Percent Completed</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/PERCENTCOMPLETE"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>Estimated Duration</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/ESTQTY"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>Actual Duration</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/ACTUALQTY"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>Approved Duration</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/APPROVEDQTY"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>Remaining Duration</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/REMAININGQTY"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>Currency</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/CURRENCY"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>Sales Order Number</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/SONUMBER"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>Purchase Order Number</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/PONUMBER"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>Purchase Order Amount</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/POAMOUNT"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>Purchase Quote Number</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/PQNUMBER"/>
						</fo:block>
						<fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
							<xsl:text>Contract Amount</xsl:text><xsl:text>: </xsl:text>
							<xsl:value-of select="REC/CONTRACTAMOUNT"/>
						</fo:block>
					</fo:block-container>
				</fo:flow>
			</fo:page-sequence>
			<!--  page printing -->
			<!--  page printing -->
		</fo:root>
	</xsl:template>
</xsl:stylesheet>
