<?php
/**
 * Employee Out of Office entity
 * 
 * Header entity connecting the employee entity to its associated out of office records. Maintained as a 
 * separate table so that employees can personally edit their out of office entries without requiring 
 * that they have direct access to the employee editor.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 Intacct Corporation, All Rights Reserved
 */

$kSchemas['employeeoutofoffice'] = array (
    'children' => array(
        'employee' => array(
            'fkey' => 'employeekey', 
            'invfkey' => 'record#', 
            'join' => 'inner', 
            'table' => 'employee',
            'children' => array(
                'contact' => array(
                    'fkey' => 'contactkey',
                    'invkey' => 'record#',
                    'table' => 'contact',
                    'join' => 'outer'
                ),
            ),
        ),
    ),
    'nexus' => array(
        'EMPLOYEE' => array(
            'object' => 'employee', 'relation' => MANY2ONE, 'field' => 'EMPLOYEEKEY'
        )
    ),
    'ownedobjects' => array(
        array(
            'fkey' => 'EMPOUTOFOFFICEKEY',
            'invfkey' => 'RECORDNO',
            'entity' => 'outofoffice',
            'path' => 'OUTOFOFFICEITEMS'
        )
    ),
    'object' => array (
        'RECORDNO',
        'EMPLOYEEKEY', 
        'EMPLOYEEID',
        'EMPLOYEECONTACTNAME',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ),
    'schema' => array (
        'RECORDNO'     => 'record#',
        'EMPLOYEEKEY'  => 'employeekey', 
        'EMPLOYEEID'   => 'employee.employeeid',
        'EMPLOYEECONTACTNAME' => 'contact.name',
        'WHENMODIFIED' => 'whenmodified',
        'WHENCREATED'  => 'whencreated',
        'CREATEDBY'    => 'createdby',
        'MODIFIEDBY'   => 'modifiedby',
        'SI_UUID'      => 'si_uuid',
    ),
    'publish' => array (
        'RECORDNO',
        'EMPLOYEEID',
    //        'EMPLOYEECONTACTNAME',    // system doesn't support exposing grandchild's field
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ),
    'fieldinfo' => array (
        $gRecordNoFieldInfo,
        array(
            'id' => 1,
            'path' => 'EMPLOYEEKEY',
            'fullname' => 'IA.EMPLOYEE_KEY',
            'derived' => true,
            'type' => array (
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat,
            ),
        ),
        array(
            'id' => 2,
            'path' => 'EMPLOYEEID',
            'fullname' => 'IA.EMPLOYEE_ID',
            'required' => true,
            'type' => array( 
                'ptype' => 'ptr', 
                'type' => 'ptr',
                'entity' => 'employee',
                'pickentity' => 'employeepick',
                'maxlength' => 20, 
                'restrict' => array(
                    array(
                        'value' => 'ressched',
                        'pickField' => 'CONTEXT',
                        'entityContext' => true
                    )
                )
            ),
        ),
        array(
            'id' => 3,
            'path' => 'EMPLOYEECONTACTNAME',
            'fullname' => 'IA.EMPLOYEE_CONTACT_NAME',
            'type' => array( 
                'ptype' => 'text', 
                'type' => 'text',
            ),
        ),
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
        $gSiUuidFieldInfo
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENMODIFIED',
        'WHENCREATED'
    ),
    'customerp' => array(
        'SLTypes' => array(
            CUSTOMERP_SMARTLINKCLICK,
            CUSTOMERP_SMARTLINKVALIDATE,
            CUSTOMERP_SMARTLINKFETCH,
            CUSTOMERP_SMARTLINKWORKFLOW
        ),
        'SLEvents' => array(
            CUSTOMERP_EVENT_ADD,
            CUSTOMERP_EVENT_SET,
            CUSTOMERP_EVENT_ADDSET,
            CUSTOMERP_EVENT_DELETE,
            CUSTOMERP_EVENT_CLICK
        ),
        'AllowCF' => true,
    ),
    'table'     => 'empoutofoffice',
    'printas'     => 'IA.EMPLOYEE_OUT_OF_NAME',
    'pluralprintas' => 'IA.EMPLOYEE_OUT_OF_NAME',
    'sicollaboration' => true,
    'module'    => 'pa',
    'vid'         => 'RECORDNO',
    'autoincrement' => 'RECORDNO',
    'auditcolumns' => true,
    'pairedFields' => array(
        'EMPLOYEEID' => 'EMPLOYEECONTACTNAME',
    ),
    'description' => 'IA.HEADER_INFORMATION_FOR_EMPLOYEE_OUT_OF_OFFICE',
    'nameFields' => [ 'EMPLOYEEID' ],
);
