<?php
/**
 * TimesheetApproval entity Lister
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2015 Intacct Corporation, All Rights Reserved
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * Lister class for the TimesheetApproval entity
 */
class TimesheetApprovalLister extends NLister
{

    function __construct()
    {
        $_params = array (
        'entity'        => 'timesheetapproval',
        'title'     => 'IA.TIMESHEET_APPROVAL_HISTORY',
        'fields'    =>  array(
            'APPROVAL_STAGE',
            'APPROVAL_TYPE',
            'APPROVERID',
            'APPROVEDBY',
            'EVENTDATE',
            'STATE',
            'COMMENTS',
        ),
        'sortcolumn'    => 'RECORDNO:a, APPROVAL_LEVEL:a, APPROVAL_STAGE:a',
        'disableedit'   => true,
        'disableadd'    => true,
        'disabledelete' => true,
        'disablefilter' => true,
        'entitynostatus' => true,
        'hide_cancel'    => true,
        'suppressPrivate' => true,
        'helpfile'    => 'Transactions_Lister_PA',
        );

        parent::__construct($_params);
    }
}
