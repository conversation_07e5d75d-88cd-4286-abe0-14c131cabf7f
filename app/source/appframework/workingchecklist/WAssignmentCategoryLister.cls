<?php
/**
 * Working assignment category lister file
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Intacct Corporation All, Rights Reserved
 */


class WAssignmentCategoryLister extends NLister
{
    public function __construct()
    {
        $_params = [
            'entity' => 'wassignmentcategory',
            'title'  => "IA.ASSIGNMENT_CATEGORIES",
            // Make sure 'CREATIONTYPE' is last because we dont want to show it in UI.
            // It is used to determine if we show the delete button or not.
            'fields' => [
                'NAME',
                'STATUS',
                'CREATIONTYPE',
            ],
            'enablemultidelete' => true,
            'nonencodedfields' => [ 'RECORD_URL' ],
        ];
        parent::__construct($_params);
    }

    /**
     * Override as necessary in sub-class to provide alternate dst, args, text or tip
     * based on the current row
     *
     * @param int   $i
     * @param array $vals
     *
     * @return array
     */
    function calcDeleteUrlParms($i, $vals)
    {
        if ($i > -1 && $this->values[$i]['CREATIONTYPE'] === 'S') {
            $vals['nolink'] = true;
            $vals['text'] = '';
            return $vals;
        } else {
            return parent::calcDeleteUrlParms($i, $vals);
        }
    }

    function BuildTable()
    {
        parent::BuildTable();
        // The default view will not contain CREATIONTYPE. Only custom views that are including it.
        if ( $this->isCustomView($this->_params['userviewid']) ) {
            if ( array_search('CREATIONTYPE', $this->_params['_fields']) ) {
                foreach ( $this->table as $key => $row ) {
                    $this->table[$key]['CREATIONTYPE'] =
                        $this->entityMgr->_TransformInternalValue('CREATIONTYPE', $row['CREATIONTYPE'], true);
                }
            }
        } else {
            $fields = [ 'NAME', 'STATUS'];
            $this->SetOutputFields($fields, []);
        }
    }
}
