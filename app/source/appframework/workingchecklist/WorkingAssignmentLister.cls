<?php
/**
 * Working assignment lister file
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Intacct Corporation All, Rights Reserved
 */

class WorkingAssignmentLister extends NLister
{
    public function __construct()
    {
        $_params = [
            'entity' => 'workingassignment',
            'title'  => 'IA.ASSIGNMENTS',
            'fields' => [
                'ASSIGNMENTID',
                'NAME',
                'DESCRIPTION',
                'ASSIGNEE',
                'PLANNEDSTARTDATE',
                'PLANNEDENDDATE',
                'STATUSNAME',
                'ACTUALENDDATE',
                'STATUS',
            ],
            self::ENABLE_ATTACHMENT_COLUMN => true,
            'enablemultidelete'            => true,
            'nonencodedfields'             => [ 'RECORD_URL' ], // encode all fields
            'importtype'                   => 'workingassignment',
            'importperm'                   => 'co/lists/workingassignment/create',
            'sortcolumn'                   => 'RECORDNO:d'
        ];
        parent::__construct($_params);
    }

    /**
     * @param int $i
     *
     * @return bool
     */
    public function canEdit($i) : bool
    {
        $ok = true;
        if ( WorkingchecklistUtils::cnyAllowsChecklistRules() ) {
            if ( !empty($this->table[$i]['ASSIGNEE'])) {
                $contactMgr = Globals::$g->gManagerFactory->getManager('contact');
                $currentAssignmentContactKey = (int) $contactMgr->GetRecordNoFromVid($this->table[$i]['ASSIGNEE']);
            }
            if ( ! empty($currentAssignmentContactKey) && !empty($this->table[$i]['ASSIGNMENTID']) ) {
                if ( empty($this->table[$i]['CHECKLISTKEY']) ) {
                    /** @var BaseWorkingAssignmentManager $entityManager */
                    $entityManager = $this->entityMgr;
                    $checklist = $entityManager->getById($this->table[$i]['ASSIGNMENTID'], ['CHECKLISTKEY']);
                    $ok = WorkingchecklistUtils::isUserAllowedToUpdateAssignment(
                        $currentAssignmentContactKey,
                        $checklist['CHECKLISTKEY'] ?? '',
                        false
                    );
                }
            }
        }
        return $ok;
    }
}
