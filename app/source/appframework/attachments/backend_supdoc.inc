<?  

//	FILE:			backend_supdoc.inc
//	AUTHOR:			
//	DESCRIPTION:	
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//



import('SupDocMapsManager');
import('SupportingDocumentsManager');

/**
 * @param string $_doc
 * @param bool   $edit
 * @param string $form
 * @param bool   $bold
 * @param bool   $onchange
 * @param bool   $eefilter
 */
function PrintSupportingDocumentTextField($_doc, $edit = true, $form = 'je',
    /** @noinspection PhpUnusedParameterInspection */ $bold = true,
    /** @noinspection PhpUnusedParameterInspection */ $onchange = true, $eefilter=false)
{

    global $_userid, $gWarnOnSaveJS;

    I18N::addTokens(I18N::tokenArrayToObjectArray(['IA.VIEW_ATTACHMENTS', 'IA.SUPPORTING_DOCUMENTS']));
    //Since there are two sets of permissions for supdocs we need to know which module we are in.
    $app = explode('@', $_userid)[2] ?? null;
    $mod = $app == 'A'?'co':'mp';
    if( $eefilter ) {
        $pickArr = array(0);
        $createEditArr = array(3,4);   //Create & Edit links for filtered supporting docs in ee
        $viewAttachArr = array(6);
    } else {
        $pickArr = array(0);
        $createEditArr = array(3,7);   //CreateEdit for supporting docs
        $viewAttachArr = array(6);
    }

    $onchangeJS = $gWarnOnSaveJS;
?>
    <? if (QXCommon::isQuixote()) { ?>
    <div class="form-group">
        <label><?= I18N::getSingleToken('IA.ATTACHMENTS'); ?></label>
        <? if ($edit) {
            // Construct combo params
            $params = array();
            $params['path'] = "_doc";
            $params['value'] = $_doc;
            $params['onchange'] = $onchangeJS;
            $params['filtered'] = $eefilter;
            $params['type']['size'] = 20;
            $params['type']['forceCombo'] = true;
            $params['assists'] = array();
            if ($edit) {
                $params['assists'] = array_merge($params['assists'], PrintEntityLink($pickArr, $form, '_doc', 'supportingdocuments', $mod, $mod . '/lists/supportingdocuments/pick', 'IA.ATTACHMENTS', '', '', '', $onchangeJS, '', false, true));
                $params['assists'] = array_merge($params['assists'], PrintEntityLink($createEditArr, $form, '_doc', 'supportingdocuments', $mod, $mod . '/lists/supportingdocuments', 'IA.ATTACHMENTS', '', '', '', $onchangeJS, '', false, true));
                $params['assists'] = array_merge($params['assists'], PrintEntityLink($viewAttachArr, $form, '_doc', 'supportingdocumentdata', $mod, $mod . '/lists/supportingdocumentdata', 'IA.SUPPORTING_DOCUMENTS', '', '', '', '', '', false, true));
            } else {
                if ($_doc != '') {
                    $params['assists'] = PrintEntityLink($viewAttachArr, $form, '_doc', 'supportingdocumentdata', $mod, $mod . '/lists/supportingdocumentdata', 'IA.ATTACHMENTS', '', '', '', '', '', false, true);
                }
            }

            // Render combo
            $rendercmbo = PrintEntityCombo('supportingdocuments', $params);
            if (!$rendercmbo) {
                ?>
                <input type="text" class="form-control" name="_doc" value="<? echo $_doc ?>" onchange="<?= $onchangeJS ?>">
                <? if ($edit) { ?>
                    <?= PrintEntityLink($pickArr, $form, '_doc', 'supportingdocuments', $mod, $mod . '/lists/supportingdocuments/pick', 'IA.ATTACHMENTS', '', '', '', $onchangeJS); ?>
                    <?= PrintEntityLink($createEditArr, $form, '_doc', 'supportingdocuments', $mod, $mod . '/lists/supportingdocuments', 'IA.ATTACHMENTS', '', '', '', $onchangeJS); ?>
                    <?= PrintEntityLink($viewAttachArr, $form, '_doc', 'supportingdocumentdata', $mod, $mod . '/lists/supportingdocumentdata', 'IA.SUPPORTING_DOCUMENTS'); ?>
                    <?
                } else {
                    if ($_doc != '') { ?>
                        <?= PrintEntityLink($viewAttachArr, $form, '_doc', 'supportingdocumentdata', $mod, $mod . '/lists/supportingdocumentdata', 'IA.ATTACHMENTS'); ?>
                        <?
                    } ?>
                    <?
                } ?>
                <?
            }
        } else { ?>
            <input type="hidden" name="_doc" value="<? echo $_doc ?>">
            <? echo isl_htmlspecialchars($_doc) ?>
            <?
        } ?>
    </div>
<? } else { ?>
	<tr>
		<td align=right valign=center nowrap class="label_cell_mult">
				 Attachments
		</td>
		<td class="value_cell">
			<table border=0 cellpadding=0 cellspacing=0><tr>
				<td align=left nowrap>

				<? if ($edit) {
        // Construct combo params
        $params = array();
        $params['path'] = "_doc";
        $params['value'] = $_doc;
        $params['onchange'] = $onchangeJS;
        $params['filtered'] = $eefilter;
        $params['type']['size'] = 20;

        // Render combo
        $rendercmbo = PrintEntityCombo('supportingdocuments', $params);
        if (!$rendercmbo) {
        ?>
       <input type="text" name="_doc" value="<? echo $_doc ?>" onchange="<?=$onchangeJS?>">
				<?
        }
} else { ?>
					<input type="hidden" name="_doc" value="<? echo $_doc ?>">
        <? echo isl_htmlspecialchars($_doc) ?>
				<?
} ?>

				</td>
				<td>&nbsp;</td>
				<td nowrap>
				<? if ($edit) { ?>
        <?=PrintEntityLink($pickArr, $form, '_doc', 'supportingdocuments', $mod, $mod.'/lists/supportingdocuments/pick', 'IA.ATTACHMENTS', '', '', '', $onchangeJS); ?>
        <?=PrintEntityLink($createEditArr, $form, '_doc', 'supportingdocuments', $mod, $mod.'/lists/supportingdocuments', 'IA.ATTACHMENTS', '', '', '', $onchangeJS); ?>
                    <?=PrintEntityLink($viewAttachArr, $form, '_doc', 'supportingdocumentdata', $mod, $mod.'/lists/supportingdocumentdata', 'IA.SUPPORTING_DOCUMENTS'); ?>
				<?
} else {
    if ($_doc != '') { ?>
        <?=PrintEntityLink($viewAttachArr, $form, '_doc', 'supportingdocumentdata', $mod, $mod.'/lists/supportingdocumentdata', 'IA.ATTACHMENTS'); ?>
        <?
    } ?>
				<?
} ?>
			</td></tr></table>
		</td>
<? if ($form != 'main') { ?>
	</tr>
<?
}
}
}

/**
 * @param string $recordID
 * @param string $documentID
 * @param string $externalURL
 * @param string $transactionType
 *
 * @return bool
 */
function SetSupportingDocumentMap($recordID, $documentID, $externalURL, $transactionType)
{

    global $gManagerFactory;
    $supdocmapsMgr = $gManagerFactory->getManager('supdocmaps');

    if ($documentID || $externalURL) {
        $values = [
            'DOCUMENTID'      => $documentID,
            'RECORDID'        => $recordID,
            'EXTERNALURL'     => $externalURL,
            'TRANSACTIONTYPE' => $transactionType,
        ];

        return $supdocmapsMgr->set($values);

    } else {
        return $supdocmapsMgr->myDelete($recordID, $transactionType);
    }
}

/**
 * @param int    $recordID
 * @param string $documentID
 * @param string $externalURL
 * @param string $transactionType
 *
 * @return bool
 */
function AddSupportingDocumentMap($recordID, $documentID, $externalURL, $transactionType)
{

    global $gManagerFactory;
    $supdocmapsMgr = $gManagerFactory->getManager('supdocmaps');

    $values = array (
    'DOCUMENTID'        => $documentID,
    'RECORDID'            => $recordID,
    'EXTERNALURL'        => $externalURL,    
    'TRANSACTIONTYPE'    => $transactionType,
    );

    return $supdocmapsMgr->add($values);
}

/**
 * @param int    $recordID
 * @param string $transactionType
 *
 * @return mixed
 */
function GetSupportingDocumentID($recordID, $transactionType)
{
    global $gManagerFactory;
    $supdocmapsMgr = $gManagerFactory->getManager('supdocmaps');
    return $supdocmapsMgr->GetSupportingDocumentID($recordID, $transactionType);
}

/**
 * @param int    $recordID
 * @param string $transactionType
 *
 * @return string
 */
function GetQuickSupportingDocumentID($recordID, $transactionType)
{

    global $gQueryMgr;

    // GET THE PAYMENTID
    $queryItems = array(
    'QUERY' => 'select payment.record# ' .
                   'FROM prrecord payment, prpaymentrecords prpaymentrecords ' .
                   'WHERE prpaymentrecords.recordkey = ? AND ' .
                   'payment.record# = prpaymentrecords.paymentkey(+) AND ' .
                   'prpaymentrecords.cny# = ? AND '.
                   'payment.cny# = ?',
    'ARGTYPES' => array('integer', 'integer', 'integer')
    );
    
    $paymentIDs = $gQueryMgr->DoCustomQuery($queryItems, array($recordID));
    $paymentRec = $paymentIDs[0];

    return GetSupportingDocumentID($paymentRec[0], $transactionType);
}
