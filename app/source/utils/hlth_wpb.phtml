<?php

include_once('util.inc');

class WPBService
{
    const MONGO_COLLECTION_SAGE_PING = 'SAGE_LINK';
    const MONGO_SAGE_HEALTH_CHECK = 'SAGE_HEALTH_CHECK';
    const MONGO_SAGE_FILE_FORMAT = 'MONG<PERSON>_SAGE_FILE_FORMAT';

    /**
     * @var array $contextData
     */
    private $contextData = [];

    /**
     * @var array $GSHandler
     */
    private $GSHandler = [];

    /**
     *
     * @return string
     * @throws StorageWriteException
     */
    public function healthcheck(  ){

	// Do not include newrelic injects script code
        if (extension_loaded('newrelic')) {
          newrelic_disable_autorum();
        }

        $functionData = [
            'url'   =>  GetValueForIACFGProperty('SBC_ENDPOINT_PAYMENTS').'/v3/healthcheck'
        ];

        $returnMessage = 'status=';

        Util::httpCall(
            $functionData['url'],
            [],
            $response,
            true,
            null,
            null,
            true,
            null,
            null,
            null,
            true,
            $headers,
            false,
            $curlInfo
        );


        if($curlInfo['http_code'] == 200){

            $jsonData = json_decode($response, true);

            if($jsonData == null){
                $returnMessage .= 'error; Reponse is not JSON format';
            }else{

                $cachedJsonData = $this->getHealthCheckDataPreserved();
                $difference = version_compare($cachedJsonData['version'] ?? '', $jsonData['version']);

                if($difference == 1){

                    $this->preserveHealthCheckData($jsonData);
                    $returnMessage .= 'error; Version Downgraded from: '.$cachedJsonData['version']. ' to: '. $jsonData['version'];
                }else if($difference == -1){

                    $this->preserveHealthCheckData($jsonData);
                    $returnMessage .= 'error; Version Upgraded from: '.$cachedJsonData['version']. ' to: '. $jsonData['version'];;
                } else{
                    $returnMessage .= 'ok';
                }
            }
        }else{
            $returnMessage .= 'error; HTTP response - '.$curlInfo['http_code'];
        }

        return $returnMessage;
    }

     /**
     *
     * @return string
     */
    public function bankfeedhealthcheck(  ) {
        //Request to get query response
        Util::httpCall(
            GetValueForIACFGProperty('SBC_ENDPOINT_HEALTHCHECK'),
            [],
            $response,
            true,
            null,
            null,
            true,
            null,
            null,
            null,
            true,
            $retHeaders,
            false,
            $curlInfo
        );

        if ( $curlInfo['http_code'] !== 200 ) {
            return 'status= error; Retrieving query response; HTTP response - ' . $curlInfo['http_code'];
        }

        $json = json_decode($response, true);

        $msg= "status=";
        if ($json['general']['ok'] !== true || $json['transactions']['ok'] !== true) {

            if ($json['general']['ok'] !== true) {
                $msg .= ' error; General APIs are not working';
            }

            if ($json['transactions']['ok'] !== true) {
                $msg .= ' error; Transactions APIs are not working';
            }
        } else {
            $msg .= "ok";
        }
        return $msg;
    }

    /**
     *
     * @return string
     * @throws StorageWriteException
     */
    public function ep_healthcheck(  ){
        $iaSbIntegration = new IaSbcIntegration(1);
        $body = [
            'grant_type'    => 'client_credentials',
            'client_id'     => $iaSbIntegration->getCloudIDClientID(),
            'client_secret' => $iaSbIntegration->getCloudIDClientKey(),
            'audience'      => $iaSbIntegration->getPaymentsAudience(),
        ];
        $applicationId = $iaSbIntegration->getApplication();

        //Request to get access token
        Util::httpCall(
            $iaSbIntegration->getCloudIDUrl(),
            $body,
            $response,
            false,
            null,
            null,
            true,
            null,
            null,
            null,
            true,
            $retHeaders,
            false,
            $curlInfo
        );

        if ( $curlInfo['http_code'] != 200 ) {
            return 'status= error; Generating access token; HTTP response - ' . $curlInfo['http_code'];
        }

        $jsonData = json_decode($response, true);
        if ( $jsonData == null ) {
            return 'status= error; Generating access token; Reponse is not JSON format';
        }
        $accessToken = $jsonData['access_token'];

        $headers[] = 'Content-Type: application/json';
        $headers[] = 'Authorization: Bearer ' . $accessToken;
        $headers[] = 'x-api-key: ' . $iaSbIntegration->getConsumerAPIKey();

        $body = [
            "data" => [ "queryName" => "webhookerrors" ],
        ];

        //Request to crete query id
        Util::httpCall(
            GetValueForIACFGProperty('SBC_ENDPOINT_PAYMENTS') . "/v3/applications/$applicationId/logs/queries",
            json_encode($body),
            $response,
            false,
            null,
            null,
            true,
            $headers,
            null,
            null,
            true,
            $retHeaders,
            false,
            $curlInfo
        );

        if ( $curlInfo['http_code'] != 201 ) {
            return 'status= error; Creating query; HTTP response - ' . $curlInfo['http_code'];
        }
        $jsonData = json_decode($response, true);

        //Request to get query response
        Util::httpCall(
            GetValueForIACFGProperty('SBC_ENDPOINT_PAYMENTS') . "/v3/applications/$applicationId/logs/queries/"
            . urlencode($jsonData['data']['queryId']),
            [],
            $response,
            true,
            null,
            null,
            true,
            $headers,
            null,
            null,
            true,
            $retHeaders,
            false,
            $curlInfo
        );

        if ( $curlInfo['http_code'] != 200 ) {
            return 'status= error; Retrieving query response; HTTP response - ' . $curlInfo['http_code'];
        }

        $json = json_decode($response, true);

        if ( isset($_GET['log']) && $_GET['log'] == true) {
            return json_encode($json, JSON_PRETTY_PRINT);
        } else if(count($json['data']['results']) > 0){
            if ( ( isset($_GET['include_app_errors']) && $_GET['include_app_errors'] === 'true' ) ) {
                $webhookErrors = $json['data']['results'];
            } else {
                $webhookErrors = $this->filterOutApplicationErrors($json['data']['results']);
            }

            $now = time();
            $webhooktime = strtotime($webhookErrors[0]['timestamp']);

            if($now - $webhooktime <= 300){
                return 'status=error; Queries found for last five minutes';
            }
        }


        return 'status=ok';
    }

    /**
     * @return string
     * @throws StorageWriteException
     */
    public function bankfileformat( ){
        $iaSbIntegration = new IaSbcIntegration(2);
        $applicationId = $iaSbIntegration->getApplication();

        $functionData = [
            'url'           => GetValueForIACFGProperty('SBC_ENDPOINT_PAYMENTS').'/v3/fileformats',
            'headers'       => [ 'x-application: "' . $applicationId . '"' ]
        ];

        $returnMessage = 'status=';

        Util::httpCall(
            $functionData['url'],
            [],
            $response,
            true,
            null,
            null,
            true,
            $functionData['headers'],
            null,
            null,
            true,
            $headers,
            false,
            $curlInfo
        );


        if($curlInfo['http_code'] == 200){

            $jsonData = json_decode($response, true);

            if($jsonData == null){
                $returnMessage .= 'error; Reponse is not JSON format';
            }else{

                $cachedJsonData = $this->getFileFormatDataPreserved();
                $this->findDiff($cachedJsonData ?? [], $jsonData);
                if(!empty($this->contextData)){
                    $this->preserveFileFormatData($jsonData);
                    $returnMessage .= "error; \n\nAffected values \n\n" . implode("\n\n", $this->contextData);;
                }else{
                    $returnMessage .= 'ok';
                }
            }
        }else{
            $returnMessage .= 'error; HTTP response - '.$curlInfo['http_code'];
        }

        return $returnMessage;
    }

    /**
     * @return mixed|null
     */
    private function getHealthCheckDataPreserved(){
        $keyValueStoreHanlder = $this->getGSHandler(self::MONGO_COLLECTION_SAGE_PING);
        return $keyValueStoreHanlder->get(self::MONGO_SAGE_HEALTH_CHECK);
    }

    /**
     * @param array $jsonData
     *
     * @throws StorageWriteException
     */
    private function preserveHealthCheckData($jsonData){
        $keyValueStoreHanlder = $this->getGSHandler(self::MONGO_COLLECTION_SAGE_PING);
        $keyValueStoreHanlder->set(self::MONGO_SAGE_HEALTH_CHECK, $jsonData);
    }

    /**
     * @return mixed|null
     */
    private function getFileFormatDataPreserved(){
        $keyValueStoreHanlder = $this->getGSHandler(self::MONGO_COLLECTION_SAGE_PING);
        return $keyValueStoreHanlder->get(self::MONGO_SAGE_FILE_FORMAT);
    }

    /**
     * @param array $jsonData
     *
     * @throws StorageWriteException
     */
    private function preserveFileFormatData($jsonData){
        $keyValueStoreHanlder = $this->getGSHandler(self::MONGO_COLLECTION_SAGE_PING);
        $keyValueStoreHanlder->set(self::MONGO_SAGE_FILE_FORMAT, $jsonData);
    }

    /**
     * @param string $collectionName
     *
     * @return mixed
     */
    private function getGSHandler($collectionName){
        if(!isset($this->GSHandler[$collectionName])){
            $this->GSHandler[$collectionName] = KVSFactory::getGS($collectionName);
        }
        return $this->GSHandler[$collectionName];
    }

    /**
     * @param array|null $array1
     * @param array|null $array2
     * @param string $parentKey
     */
    private function findDiff($array1, $array2, $parentKey = ''){
        $ta = json_encode($array1);
        $tb = json_encode($array2);
        if( $ta == $tb ){
            return;
        }


        if( is_array($array1) && is_array($array2) ){
            $diffKeys1 = array_keys(array_diff_key($array1, $array2));
            $diffKeys2 = array_keys(array_diff_key($array2, $array1));
            if(!empty($diffKeys1)){
                $record = 'Key: ' . ($parentKey != '' ? $parentKey : 'root' );
                $record .= "\n\nKeys Removed: ".json_encode($diffKeys1);
                $this->contextData[] = $record;
            }

            if(!empty($diffKeys2)){
                $record = 'Key: ' . ($parentKey != '' ? $parentKey : 'root' );
                $record .= "\n\nKeys Added: ".json_encode($diffKeys2);
                $this->contextData[] = $record;
            }

            $localParentKey = $parentKey;
            foreach ($array1 as $arrayKey => $array1Value ){
                if( in_array($arrayKey, $diffKeys1) || in_array($arrayKey, $diffKeys2)){
                    continue;
                }
                $array2Value = $array2[$arrayKey];
                if($localParentKey != ''){
                    $localParentKey .= '.';
                }
                $localParentKey .= $arrayKey;
                $this->findDiff($array1Value, $array2Value, $localParentKey);
                $localParentKey = $parentKey;
            }
        }else if(is_array($array1)){
            $record = 'Key: ' . ($parentKey != '' ? $parentKey : 'root' );
            $record .= "\n\nFrom: ".json_encode($array1);
            $record .= "\n\nTo: ".$array2;
            $this->contextData[] = $record;
        }else if(is_array($array2)){
            $record = 'Key: ' . ($parentKey != '' ? $parentKey : 'root' );
            $record .= "\n\nFrom: ".$array1;
            $record .= "\n\nTo: ".json_encode($array2);
            $this->contextData[] = $record;
        }else{
            if($array1 != $array2){
                $record = 'Key: ' . ($parentKey != '' ? $parentKey : 'root' );
                $record .= "\n\nFrom: ".$array1;
                $record .= "\n\nTo: ".$array2;
                $this->contextData[] = $record;
            }
        }
    }

    /**
     * @param array $allErrors
     *
     * @return array
     */
    private function filterOutApplicationErrors(array $allErrors)
    {
        $transmissionErrors = [];
        foreach ( $allErrors as $error ) {
            $errorCode = (int) $error['httpStatusCode'];
            if ( $errorCode >= 500 ) {
                $transmissionErrors[] = $error;
            }
        }

        return $transmissionErrors;
    }

}
?>
<html>
<head>
    <title>Health Check - Sage </title>
</head>
<body>
<?php
echo '<pre>';
echo $_SERVER['SERVER_NAME'] . "\n" . date('l j M, Y H:i:s') . "\n";

$option = $_GET['option'] ?? null;
if(in_array($option, ['healthcheck', 'ep_healthcheck', 'bankfileformat', 'bankfeedhealthcheck'])){
    echo (new WPBService())->$option();
}else{
    echo 'status=error; Unknown option';
}

echo '</pre>';
?>
</body>
</html>
