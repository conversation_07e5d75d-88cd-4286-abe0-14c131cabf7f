<?php
//=============================================================================
//
//	FILE:			login.phtml
//	AUTHOR:			
//	DESCRIPTION:	
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================

require_once 'util.inc';
require_once 'html_header.inc';

// to prevent JS/SQL injections
Request::FilterRequest();
InitBase();
InitGlobals();

$_msg              = &Request::$r->_msg;
$hlpfile           = &Request::$r->hlpfile;
$query             = &Request::$r->query;
$_company          = Request::$r->_company;
$_cpaassoc         = Request::$r->_cpaassoc;
$contact_email     = Request::$r->contact_email;
$_authcode         = Request::$r->_authcode;
$privacypolicylink = GetValueForIACFGProperty("IA_PRIVACYLINK");

if (GetValueForIACFGProperty("POD_CONFIGURATION")['IA_PODGROUPS_CONFIG_ENABLED']) {
    $_email    = Request::$r->contact_email;

    $currentPODId = Globals::$g->gPODManager->getCurrentPOD()->getId();
    $podinfo = Globals::$g->gPODManager->getPODInfoForCny($_company);

    if ($podinfo == false || !isset($podinfo[PODInfo::POD_ID])) {
        // company was not replicated
        Globals::$g->gErr->addIAError(
            "CORE-1232",
            "",
            "Invalid company $_company", ['COMPANY'=>$_company],
            "Please make sure that you have a valid url from Intacct"
        );

        include_once "popuperror.phtml";
        exit;
    }

    if ($currentPODId != $podinfo[PODInfo::POD_ID]) {
        Redirect(
            'authorization.phtml?.company=' . urlencode(URLCleanParams::insert('.company', $_company))
            . '&contact_email=' . $_email
            . '&.authcode=' . $_authcode,
            $podinfo[PODInfo::POD_EXTHOST]
        );
    }
}

global $gErr;
global $_userid;
$_sess = Session::getKey();
$msg = "";
$error = false;

if (SetDBSchema($_company)) {
    $query = "select record#, authcode from company where company.title = :1";
    $resquery = QueryResult(array($query, $_company));
}

if (sizeof($resquery) == 0) {
    $gErr->addIAError(
        "CORE-1233",
        "",
        "Invalid company $_company", ['COMPANY'=>$_company],
        "Please make sure that you have a valid url from Intacct"
    );
}
 
if ($gErr->hasErrors()) {
    global $noErrorHeader;
      
    $noErrorHeader = true;
    include_once "popuperror.phtml";
    exit;
}

if ($resquery[0]['AUTHCODE'] == '') {
    // authorization code has been reset => it has been authorized. 
    Fwd("login.phtml", "frameset.phtml");
}

if ($_authcode) {
    // user has provide the authorization code. Check for validitiy.
    if ($resquery[0]['AUTHCODE'] == $_authcode) {
        ExecStmt(array("update company set AUTHCODE = '' where title = :1", $_company));
        // we need to invalidate/refresh the company cache to reflect the changes made in the database 
        InValidateSession(ProfileHandler::COMPANY_CACHE_FLAG, Session::getKey());
        Fwd("login.phtml", "frameset.phtml");
    } else {
        $msg = "Invalid Authorization code $_authcode";
        $error = true;
    }
}  

$hlpfile = 'SignUp_Account_Activation';


$props = array();
$props['nocheck'] = true;
$props['nojs'] = true;
$props['nohotkey'] = true;
$props['nobeancss'] = true;
$props['incJSValidationFiles'] = false;
$props['nocache'] = true;
/** @noinspection PhpUndefinedVariableInspection */
$props['bgcolor'] = $color0;
$props['title'] = $title;
PrintCommonHtmlHeader($props);
?>

    <form action="<?=util_encode($_SERVER['PHP_SELF'])?>" method="post">
  <table border="0" width="100%" height="80%">
    <tr>
       <td width="100%" height="100%" align="center" valign="middle">
<table width="350" border="0" cellpadding="0" cellspacing="0" class="loginTable" >
    <tr>
<td class="tdLogoCell">
    <img src="../resources/images/ia-app/login/intacct_logo.gif" alt=""  border="0">
    </td><td width="139"><img src="../resources/images/ia-app/login/<? /** @noinspection PhpUndefinedVariableInspection */
            echo $_needsauth ? 'account_activation' : 'secure_login' ; ?>.gif" alt="" width="139" height="55" border="0"></td></tr>

<tr>
<td colspan="2" class="tdLoginCell">
 <table width="350" border=0 cellpadding=0 cellspacing=0 class="tableLoginData">
    <? if ($error) { ?>
      <tr>
        <td colspan="2"><font color="red"><?=$msg?></font></td>
      </tr>
    <? 
} ?>
	<tr>
		<td width="100" class="tdLabel" >Enter Authorization Code :
		</td><td align="left">
			<input name=.authcode size=20  maxlength=20>
			<input type=hidden name=.company value="<?=URLCleanParams::insert('.company', $_company); ?>" >
		</td></tr>
</table>
</td></tr>
	<tr><td align="middle" style="padding-bottom:15px;" colspan="2">
			<input name=".ret" type="submit" class="nosavehistory" value="Login" style="height:25px;background:#E5C646;font-size:10px;" />
		</td></tr>
	<tr>
		<td align="center" colspan="2">
    <p>
				Your authorization code was emailed to you
				at <? /** @noinspection PhpUndefinedVariableInspection */
        echo $contact_email ?>.<br>
				You may need to wait a few minutes
				to receive the email.</p></td></tr>  
               <tr height="40">
    <td>&nbsp;</td>
                  <td class="tdHelpLinks">
                      <a href="<? echo $privacypolicylink;?>" target="_blank" <? echo (HREFUpdateStatus('Privacy Policy')); ?> >
                          <?=getPrivacyLabel();?>
                      </a>
		&nbsp; 
<a href="javascript:Launch('<?= GetLocalizedDocsUrl() ?>miscapp/su010_sign_in.html', 'HelpWin', 575, 450);" <? echo (HREFUpdateStatus('Help')); ?> >
				Help</a></td>
               </tr>
             </table>
          </form>
        </center>
      </td>
    </tr>
  </table>
</body>
</html>
