<?php

require_once('PayflowProCCProcessor.cls');

/**
 * Maps the API for the PayPal Payflow Pro payment gateway to the
 * interface expected by the Billing code
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright Intacct 2009
 * @package intacct
 */
class PayflowProBilling extends PayflowProCCProcessor {
	
	// these public properties are for backwards compatility with the
	// old billing PayPal class
    /**
     * @var string $Comment1
     */
    public $Comment1;
    /**
     * @var string $Comment2
     */
    public $Comment2;

    /**
     * @var array $result
     */
    protected $result;
	

	/**
	 * reads the login information from a config file and passes it to
	 * the base constructor
   *
   * @param array $account_info billing login information
   */
   public function __construct($account_info = []) {
        if ( empty($account_info) ) {
            $billingConfig = GetBillingConfig();
            //eppp_p($billingConfig);
            $account_info = $billingConfig['PAYFLOWPRO'];
            $account_info['PAYFLOW_PASSWORD'] = TwoWayDecryptWithKey($account_info['PAYFLOW_PASSWORD'], 'IA_INIT');
        }
        parent::__construct($account_info);
	}
  
	/**
	 * Process a transaction throught the payment gateway
	 * 
	 * subclasses the _process fuction include billing specific functionality
	 *
	 * @return    array Associative array containg approval status and transaction ID 
	 */
	protected function _process() {
		$this->transaction['COMMENT1'] = $this->Comment1;
		$this->transaction['COMMENT2'] = $this->Comment2;
		$result = parent::_process();
		$this->result = $result;
		$this->_storeResult($result);
		return $result;
	}
			
	/**
	 * Logs the result to the database
	 *
	 * @param    array $result Associative array containg approval status and transaction ID 
	 */
	protected function _storeResult($result) {
		// If there is no $_cny, we can't backlog the transaction resultant
		if (GetMyCompany()) {
			$storagearray = array(
				'ref' => $result['TRANSACTION_ID'],
				'result' => (string)$result['RESULTNO'],
				'response' => $result['MESSAGE'],
				'authcode' => $result['AUTHCODE'],
				'trxtype' => $this->transaction['TRXTYPE'],
				'amount' => (string)$this->transaction['AMT'],
				'origref' => $this->transaction['ORIGID'],
			);
			//eppp_p($storagearray);
            CreateNObject('ia_trx', $storagearray, 1, null, false, new QueryLoggerSensitiveInfo());
		}
	}

    /**
     * Authorize a credit card payment
     *
     * @param string $CC
     * @param string $ExpMonth
     * @param string $ExpYear
     * @param float $Amount
     * @return array Associative array containg approval status and transaction ID
     */
	public function AuthorizeFunds($CC, $ExpMonth, $ExpYear, $Amount) {
		$this->transaction['EXPDATE'] = $ExpMonth . mb_substr($ExpYear,-2);
		$this->transaction['ACCT'] = $CC;
		$this->transaction['AMT'] = $Amount; 
		$this->transaction['TRXTYPE'] = 'A';
		$this->transaction['TENDER'] = 'C'; 
		return $this->_process();
	}

    /**
     * Prenotify Funds
     *
     * @param string $AccountNo
     * @param string $RoutingNo
     * @param string $Name
     * @param string $Desc
     * @return array Associative array containg approval status and transaction ID
     */
	public function PrenotifyFunds($AccountNo, $RoutingNo, $Name, $Desc) {
		$this->transaction['TRXTYPE'] = 'S';
		$this->transaction['PRENOTE'] = 'Y';
		$this->transaction['TENDER'] = 'A';
		$this->transaction['ACCT'] = $AccountNo;
		$this->transaction['AMT'] = '0.00';
		$this->transaction['ABA'] = $RoutingNo;
		$this->transaction['ACCTTYPE'] = 'C';	// checking
		$this->transaction['NAME'] = $Name;
		$this->transaction['DESC'] = $Desc;
		return $this->_process();
	}

    /**
     * Debit a credit card
     *
     * @param string $CC
     * @param string $ExpMonth
     * @param string $ExpYear
     * @param float $Amount
     * @return array Associative array containg approval status and transaction ID
     */
	public function Debit($CC, $ExpMonth, $ExpYear, $Amount) {
		$this->transaction['EXPDATE'] = $ExpMonth . mb_substr($ExpYear,-2);
		$this->transaction['ACCT'] = $CC;
		$this->transaction['AMT'] = $Amount; 
		$this->transaction['TRXTYPE'] = 'S';
		$this->transaction['TENDER'] = 'C'; 
		return $this->_process();
	}

    /**
     * Debit a checking account
     *
     * @param  string $AccountNo
     * @param string $RoutingNo
     * @param string $Name
     * @param string $Desc
     * @param float $Amount
     * @return array Associative array containg approval status and transaction ID
     */
	public function DebitChecking($AccountNo, $RoutingNo, $Name, $Desc, $Amount) {
		$this->transaction['TRXTYPE'] = 'S';
		$this->transaction['PRENOTE'] = 'N';
		$this->transaction['TENDER'] = 'A';
		$this->transaction['ACCT'] = $AccountNo;
		$this->transaction['AMT'] = $Amount; 
		$this->transaction['ABA'] = $RoutingNo;
		$this->transaction['ACCTTYPE'] = 'C';	// checking
		$this->transaction['NAME'] = $Name;
		$this->transaction['DESC'] = $Desc;
		return $this->_process();
	}

    /**
     * Credit a checking account
     *
     * @param string $AccountNo
     * @param string $RoutingNo
     * @param string $Name
     * @param string $Desc
     * @param float $Amount
     * @return array Associative array containg approval status and transaction ID
     */
	public function CreditChecking($AccountNo, $RoutingNo, $Name, $Desc, $Amount) {
		$this->transaction['TRXTYPE'] = 'C';
		$this->transaction['PRENOTE'] = 'N';
		$this->transaction['TENDER'] = 'A';
		$this->transaction['ACCT'] = $AccountNo;
		$this->transaction['AMT'] = $Amount;
		$this->transaction['ABA'] = $RoutingNo;
		$this->transaction['ACCTTYPE'] = 'C';	// checking
		$this->transaction['NAME'] = $Name;
		$this->transaction['DESC'] = $Desc;
		return $this->_process();
	}

    /**
     * Capture a credit card payment
     *
     * @param string $Ref
     * @return array Associative array containg approval status and transaction ID
     */
	public function CaptureFunds($Ref) {
		$this->transaction['ORIGID'] = $Ref;
		$this->transaction['TRXTYPE'] = 'D';
		$this->transaction['TENDER'] = 'C';
		return $this->_process();
	}

    /**
     * Void a credit card payment
     *
     * @param string $Ref
     * @return array Associative array containg approval status and transaction ID
     */
	public function VoidFundsCC($Ref) {
		$this->transaction['ORIGID'] = $Ref;
		$this->transaction['TRXTYPE'] = 'V';
		$this->transaction['TENDER'] = 'C';
		return $this->_process();
	}

    /**
     * Void an ACH transaction
     *
     * @param string $Ref
     * @return array Associative array containg approval status and transaction ID
     */
	public function VoidFundsACH($Ref) {
		$this->transaction['ORIGID'] = $Ref;
		$this->transaction['TRXTYPE'] = 'V';
		$this->transaction['TENDER'] = 'A';
		return $this->_process();
	}

    /**
     * Inquire Settlement on an ACH transaction
     *
     * @param string $Ref
     * @return array Associative array containg approval status and transaction ID
     */
	public function ACHInquireSettlement($Ref) {
		$this->transaction['ORIGID'] = $Ref;
		$this->transaction['TRXTYPE'] = 'I';
		$this->transaction['TENDER'] = 'A';
		return $this->_process();
	}

    /**
     * Credit a credit card account using the id from a previous transaction
     *
     * @param string $Ref
     * @return array Associative array containg approval status and transaction ID
     */
	public function CreditCCByRef($Ref) {
		$this->transaction['ORIGID'] = $Ref;
		$this->transaction['TRXTYPE'] = 'C';
		$this->transaction['TENDER'] = 'C';
		return $this->_process();
	}

    /**
     * Credit a credit card account using the card number
     *
     * @param string $CC
     * @param string $ExpMonth
     * @param string $ExpYear
     * @param float $Amount
     * @return array Associative array containg approval status and transaction ID
     */
	public function CreditCCByNum($CC, $ExpMonth, $ExpYear, $Amount) {
		$this->transaction['EXPDATE'] = $ExpMonth . mb_substr($ExpYear,-2);
		$this->transaction['ACCT'] = $CC;
		$this->transaction['AMT'] = $Amount;
		$this->transaction['TRXTYPE'] = 'C';
		$this->transaction['TENDER'] = 'C';
		return $this->_process();
	}

    /**
     * Void an ACH transaction
     *
     * @param string $Ref
     * @return array Associative array containg approval status and transaction ID
     */
	public function VoidChecking($Ref ) {
		$this->transaction['ORIGID'] = $Ref;
		$this->transaction['TRXTYPE'] = 'V';
		$this->transaction['TENDER'] = 'A';
		return $this->_process();
	}
	
	/**
	 * Get the error message for the last transaction
	 *
	 * @return    string error message if it existed for the last transaction
	 */
	public function GetResultHash() {
		return $this->result['MAPMESSAGE'];
	}

	/**
	 * Get the success status for the last transaction
	 *
	 * @return bool true if last transaction was successful
	 */
	public function Success() {
		return ($this->result['RESULTNO'] === '0');
	}
	
	/**
	 * Get the status message for the last transaction
	 *
	 * @return    string status message if it existed for the last transaction
	 */
	public function GetStatus() {
		return $this->result['STATUS'];
	}

	/**
	 * Get the failure status for the last transaction
	 *
	 * @return bool true if last transaction failed
	 */
	public function Failure() {
		return ($this->result['RESULTNO'] !== '0');
	}
	
}

