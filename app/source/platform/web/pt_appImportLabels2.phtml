<?
try {
    include 'pt_setupHeader.inc';
    
    $applicationId = http_getIntParameter(FIELD_APPLICATION_ID);
    $app = Pt_ApplicationManager::getById($applicationId);
    if ( $app == null ) {
        throw new Pt_I18nException('PAAS-0929', "Application is null");
    }
    
    $appName = $app->__toString();
    $doneURL = Pt_WebUtil::url('pt_appView.phtml');
    $doneURL .= "&applicationId=" . $app->getId();
    
    
    $tokens = [ "IA.IMPORT_COMPLETED", "IA.DONE" ];
    $placeholderTokens = [];
    $textMap = getIntlTextMap($tokens, $placeholderTokens);
    $title = GT($textMap, "IA.IMPORT_COMPLETED");
    
    $report = Session::getProperty(ATTR_LABELS_REPORT);
    Session::deleteProperty(ATTR_LABELS_REPORT);
    
    ?>
    
    <table class="rbs_mainComponentTable" cellpadding=0 cellspacing=0>
        <tr>
            <td>
                
                <?= Pt_SetupComponents::$rbs_roundedTableTopOrEmpty ?>
                <table class="<?= Pt_SetupComponents::$rbs_lightsilverTableOrEmpty . Pt_SetupComponents::$emptyOrSectionClass ?>">
                    <tr>
                        <td class='center'>
                            <table>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td class='rbs_PageTopicWide'><?= util_encode($title) ?>&nbsp;&nbsp;</td>
                                    <td class='rbs_recordActionCol' nowrap>
                                        <input type="submit" class="<?= Pt_SetupComponents::$emptyOrBtnPrimaryClass ?>" value=' <?= GT($textMap, "IA.DONE"); ?> '
                                               onClick="window.location.href='<?= $doneURL ?>'">&nbsp;&nbsp;
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
                <?= Pt_SetupComponents::$rbs_roundedTableBottomOrEmpty ?>
                <table height='10' class='wide'>
                    <tr>
                        <td></td>
                    </tr>
                </table>
                <pre>
<?= util_encode($report) ?>
                </pre>
            
            </td>
        </tr>
        
        <?= Pt_SetupComponents::$trHeight10OrEmpty ?>
    </table>
    
    <?
    include 'pt_setupFooter.inc';
} catch ( Exception $ex ) {
    error($ex);
}

