<?
/**
 * Wrapper methods to store/retrieve BLOBs of data
 * Important: these methods will create their own transactions, not to be included into XACT_BEGIN
 */
require_once 'BlobStoreManager.cls';
require_once "Pt_FileUpload.cls";

class Pt_StorageProxy
{

    const PLATFORM = 'PLATFORM';

    /**
     * Create BLOB from local file
     *
     * @param Pt_FileUpload $fu
     * @param string        $parentId
     *
     * @throws Exception
     */
    public static function createFromFile(Pt_FileUpload $fu, $parentId) 
    {
        global $gManagerFactory;
        $blobStoreMgr = $gManagerFactory->getManager('blobstore');
        if (!$blobStoreMgr->addBlob($fu->getData(), $parentId, 'PLATFORM', '', false)) {
            throw new Pt_I18nException('PAAS-0643', "Error creating BLOB for $parentId", [ 'PARENTID' => $parentId ]);
        }
    }

    /**
     * Create BLOB from binary data
     *
     * @param string $data
     * @param int    $parentId
     *
     * @throws Exception
     */
    public static function createFromData($data, $parentId) 
    {
        global $gManagerFactory;
        $blobStoreMgr = $gManagerFactory->getManager('blobstore');
        if (!$blobStoreMgr->addBlob($data, $parentId, 'PLATFORM', '', false)) {
            throw new Pt_I18nException('PAAS-0644', "Error creating BLOB for $parentId", [ 'PARENTID' => $parentId ]);
        }
    }

    /**
     * Update BLOB from local file
     *
     * @param Pt_FileUpload $fu
     * @param string        $parentId
     *
     * @throws Exception
     */
    public static function updateFromFile(Pt_FileUpload $fu, $parentId)
    {
        global $gManagerFactory;
        $blobStoreMgr = $gManagerFactory->getManager('blobstore');
        if ( !$blobStoreMgr->setBlob($fu->getData(), $parentId, 'PLATFORM', false) ) {
            throw new Pt_I18nException('PAAS-0645', "Error updating BLOB for $parentId", [ 'PARENTID' => $parentId ]);
        }
    }

    /**
     * Update BLOB from binary data
     *
     * @param string $data
     * @param int    $parentId
     *
     * @throws Exception
     */
    public static function updateFromData($data, $parentId) 
    {
        global $gManagerFactory;
        $blobStoreMgr = $gManagerFactory->getManager('blobstore');
        if (!$blobStoreMgr->setBlob($data, $parentId, 'PLATFORM', false)) {
            throw new Pt_I18nException('PAAS-0646', "Error updating BLOB for $parentId", [ 'PARENTID' => $parentId ]);
        }
    }

    /**
     * Get BLOB by parent ID
     *
     * @param string $parentId
     *
     * @return mixed
     */
    public static function get($parentId)
    {
        global $gManagerFactory;
        $blobStoreMgr = $gManagerFactory->getManager('blobstore');

        return $blobStoreMgr->GetByParent($parentId, 'PLATFORM');
    }

    /**
     * Delete BLOB by parent ID
     *
     * @param int $parentId
     *
     * @throws Exception
     */
    public static function delete($parentId) 
    {
        global $gManagerFactory;
        $blobStoreMgr = $gManagerFactory->getManager('blobstore');
        if (!$blobStoreMgr->DeleteByParent($parentId, 'PLATFORM')) {
            throw new Pt_I18nException('PAAS-0647', "Error deleting BLOB for $parentId", [ 'PARENTID' => $parentId ]);
        }
    }
}
