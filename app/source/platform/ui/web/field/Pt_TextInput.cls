<?
/**
 * Text input control.
 */
require_once 'Pt_AbstractTextBox.cls';

class Pt_TextInput extends Pt_AbstractTextBox
{
    public function __construct(Pt_DataFieldDef $fieldDef)
    {
        parent::__construct($fieldDef);
        $tokens = ["IA.TEXT"];
        $placeholderTokens = [];
        $this->textMap = array_merge($this->textMap, getIntlTextMap($tokens, $placeholderTokens));
    }

    /**
     * Get localized description of field's type.
     *
     * @return string
     */
    public function getDisplayType() 
    {
        $maxLength = $this->getMaxLength();
        if ($maxLength > 0) {
            $tokens = [];
            $placeholderTokens = [
                [
                    'id'           => 'IA.TEXT_OBJ',
                    'placeHolders' => [
                        [ 'name' => 'MAX_OBJ', 'value' => $maxLength],
                    ],
                ],
            ];
            $this->textMap = array_merge($this->textMap, getIntlTextMap($tokens, $placeholderTokens));
            return GT($this->textMap, "IA.TEXT_OBJ");
        }
        else {
            return GT($this->textMap, "IA.TEXT");
        }
    }

    /**
     * True, if this field produces text output
     *
     * @return bool
     */
    public function isTextField() 
    {
        return true;
    }

    /**
     * Get conversion hints
     * 
     * @return string|null
     */
    public function getConversionHints() 
    {
        if ($this->fieldDef instanceof Pt_FieldEncrypted) {
            return null; 
        }
        return "Pt_AutoNumber,Pt_EmailInput,Pt_SelectList,Pt_RadioButtons,Pt_TextArea,Pt_URLInput";
    }

    /**
     * Get input mask
     *
     * @return string
     */
    public function getInputMask() 
    {
        return $this->getFieldProperty(FIELD_INPUT_MASK);
    }

    /**
     * @param string $value
     *
     * @throws Exception
     */
    private function validateInputMask($value)
    {
        $mask = $this->getInputMask();
        if ( strlen($mask ?? '') > 0 && !util_matchInputMask($value, $mask) ) {
            throw new Pt_I18nException('PAAS-0627', "$value does not match the required format $mask",
                                       [ 'VALUE' => $value, 'MASK' => $mask ]);
        }
    }

    /**
     * Check whether given $value is a valid input.
     *
     * @param mixed             $value
     * @param string            $strValue
     * @param bool              $isRequired
     * @param Pt_IncomingValues $incomingValues
     */
    public function validateUI($value, $strValue, $isRequired, $incomingValues)
    {
        $this->validateRequired($strValue, $isRequired);
        if ( strlen($strValue ?? '') != 0 ) {
            $this->validateMaxLength($strValue);
            $this->validateInputMask($strValue);
        }
    }

    /**
     * Check whether given $value is unique.
     *
     * @param array   $fieldValues  values for all fields in the record
     * @param int     $recordId     the id of the record being validated
     */
    public function validateUniqueness($fieldValues, $recordId)
    {
        $strValue = strval($this->getValueByFieldName($fieldValues));
        if ( strlen($strValue) != 0 ) {
            Pt_DataFieldManager::checkUniqueValue($this, $strValue, $recordId);
        }
    }

    /**
     * Validate API input. $values must be array fieldName => fieldValue
     *
     * @param array $values
     */
    public function validateAPI(& $values)
    {
        $strValue = strval($this->getValueByFieldName($values));
        $this->validateRequired($strValue, $this->isFieldRequired());
        if ( strlen($strValue) != 0 ) {
            $this->validateMaxLength($strValue);
            $this->validateInputMask($strValue);
            Pt_DataFieldManager::checkUniqueValue($this, $strValue, intval($values[FIELD_ID]));
        }

        $this->validateScriptAPI($values);
    }

    /**
     * Append HTML to edit field to page's buffer.
     *
     * @param string        &$buff
     * @param mixed         $value
     * @param Pt_PageParams $params
     * @param Pt_PageCell   $cell
     * @param bool          $disabled
     */
    public function appendEditHTML(& $buff, $value, Pt_PageParams $params, $cell, $disabled=false)
    {
        $fieldName = $this->getFieldName();
        $size = $this->getCellSize($cell);
        $maxLength = $this->getMaxLength();

        $str = $this->format($value, $params);
        Pt_WebUtil::appendInput($buff, $fieldName, null, $str, $size, $maxLength, $params->getTabIndex());

        $mask = $this->getInputMask();
        if (strlen($mask ?? '') > 0) {
            $tokens = [];
            $placeholderTokens = [
                [
                    'id'           => 'IA.FORMAT_MASK',
                    'placeHolders' => [
                        [ 'name' => 'MASK', 'value' => util_encode($mask)],
                    ],
                ],
            ];
            $this->textMap = array_merge($this->textMap, getIntlTextMap($tokens, $placeholderTokens));
            $buff .= "<font size='-2'><br/>" . GT($this->textMap, "IA.FORMAT_MASK") . "</font>";
        }

        $params->requestFocus($fieldName);
    }

    /**
     * Format object $value as string
     *
     * @param mixed         $value
     * @param Pt_PageParams $params
     *
     * @return string
     */
    protected function format($value, Pt_PageParams $params)
    {
        if ($value==null && $params->getData()==null) {
            return $this->getDefaultValue(true, null, false, $params->isPageEditor());
        }
        return strval($value);
    }

}
