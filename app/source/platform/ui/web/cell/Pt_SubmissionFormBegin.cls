<?
/**
 * Cell which begins portal's form to submit data.
 */
require_once "Pt_AbstractFormBegin.cls";

class Pt_SubmissionFormBegin extends Pt_AbstractFormBegin
{
    
    /**
     * @param int      $id
     * @param string   $origId
     * @param int      $sectionId
     * @param int      $orderNo
     * @param int      $fieldId
     * @param string   $text
     * @param array    $props
     * @param string[] $appOriginalIds
     * @param string   $name
     * @param string   $description
     */
    public function __construct($id, $origId, $sectionId, $orderNo, $fieldId, $text, $props, $appOriginalIds,
                                $name = '', $description = '')
    {
        parent::__construct($id, $origId, $sectionId, $orderNo, $fieldId, $text, $props, $appOriginalIds, $name,
                            $description);
        $tokens = [ "IA.SUBMISSION_FORM_BEGIN" ];
        $placeholderTokens = [];
        $this->textMap = array_merge($this->textMap, getIntlTextMap($tokens, $placeholderTokens));
    }

    /**
     * Get localized description of cell's type.
     *
     * @return string|null
     */
    public function getDisplayType() 
    {
        return GT($this->textMap, "IA.SUBMISSION_FORM_BEGIN");
    }

    /**
     * Get id of destination page
     *
     * @return int
     */
    public function getDestPageId()  
    {
        return intval($this->props[FIELD_DEST_ID]);
    }

    /**
     * Append opening FORM tag
     *
     * @param string        &$buff
     * @param Pt_PageParams $params
     */
    public function appendFormTag(& $buff, Pt_PageParams $params) 
    {
        $objDef = $params->getObjectDef();
        if ($objDef == null) {
            throw new Pt_I18nException('PAAS-0578', "No object definition for this page");
        }
        $objDefId = $objDef->getId();

        $createMode = ($params->getData() == null);
        $thisPageId = $params->getPageId();

        $destPageId = http_getIntParameter(FIELD_D);
        if ($destPageId <= 0) {
            $destPageId = http_getIntParameter(FIELD_DEST_ID); 
        }
        if ($destPageId <= 0) {
            $destPageId = $this->getDestPageId(); 
        }
        if ($destPageId <= 0) {
            $destPageId = Pt_WebUtil::getPageId($objDefId, TYPE_VIEW); 
        }
        if ($destPageId <= 0) {
            $destPageId = $params->getPortal()->getMainPageId(); 
        }

        $act = ($createMode ? ACTION_OBJ_CREATE : ACTION_OBJ_UPDATE);
        $servletURL = BaseUrl().'pt_p.phtml';
        $cnyTitle = Profile::getCompanyCacheProperty('company', 'TITLE');

        $buff .= "<form action='".$servletURL."' method='post' name='theForm' enctype='multipart/form-data'>\n";
        $buff .= Pt_WebUtil::hidden(OP_RUNTIME);

        $buff .= "<input type='hidden' name='".FIELD_ACTION."' value='".$act."'>\n";
        $buff .= "<input type='hidden' name='".FIELD_C."' value='$cnyTitle'>\n";
        $buff .= "<input type='hidden' name='".FIELD_P."' value='".$params->getPortalId()."'>\n";
        $buff .= "<input type='hidden' name='".FIELD_D."' value='".$destPageId."'>\n";
        $buff .= "<input type='hidden' name='".FIELD_G."' value='".$thisPageId."'>\n";
        $buff .= "<input type='hidden' name='".FIELD_OBJ_DEF_ID."' value='".$objDefId."'>\n";
        $id = $params->getDataId();
        if ($id > 0) {
            $buff .= "<input type='hidden' name='".FIELD_ID."' value='".$id."'>\n"; 
        }

        if ($params->getReturnId() > 0) {
            $buff .= "<input type='hidden' name='".FIELD_RETURN_ID."' value='".$params->getReturnId()."'>\n"; 
        }
    }

    /**
     * Append HTML to render this cell
     *
     * @param string        &$buff
     * @param Pt_PageParams $params
     */
    public function appendHTML(& $buff, Pt_PageParams $params) 
    {
    }

    /**
     * Generate Javascript for page editor.
     *
     * @param string        &$buff
     * @param string        $sectionJSName
     * @param Pt_PageParams $params
     *
     * @return string
     */
    public function toEditorJS(& $buff, $sectionJSName, Pt_PageParams $params) 
    {
        parent::toEditorJS($buff, $sectionJSName, $params);

        $portal = $params->getPortal();
        if ($portal == null) {
            return $buff;
        }
        $pages = [];
        $allPages = Pt_WebPageManager::getAllPortals();
        foreach ($allPages as $page) {
            if ($page->getId() == $params->getPageId()) {
                continue; 
            }
            $pages[] = $page;
        }
        usort($allPages, "Pt_PageStub::compareByName");

        $cellJSName = 'cell'.$this->id;
        $buff .= $cellJSName.".portalPages=new Array();\n";
        foreach ($pages as $page) {
            $buff .= $cellJSName.".portalPages[".$cellJSName.".portalPages.length] = {\n";
            $buff .= "\"pageId\":".$page->getId().",\n";
            $buff .= "\"pageName\":\"".util_jsEncode($page->__toString())."\"\n";
            $buff .= "};\n";
        }
        $buff .= "\n";

        return $buff;
    }

}
