<?
/**
 * Proxy of PageableList, which can be added to web page by Page Designer
 */
require_once 'Pt_AbstractProxy.cls';
require_once 'Pt_DataObjectDef.cls';

class Pt_ProxyList extends Pt_AbstractProxy
{
    /* @var Pt_DataObjectDef $tableObjDef */
    private $tableObjDef;

    /**
     * @param Pt_DataObjectDef $tableObjDef
     */
    public function __construct(Pt_DataObjectDef $tableObjDef) 
    {
        parent::__construct('list'.$tableObjDef->getId());
        $this->tableObjDef = $tableObjDef;

        $tokens = [ "IA.VIEW" ];
        $placeholderTokens = [];
        $this->textMap = array_merge($this->textMap, getIntlTextMap($tokens, $placeholderTokens));
    }

    /**
     * Get UI class name for proxy cell
     *
     * @return string
     */
    public function getCellClassName() 
    {
        return 'Pt_PageableList';
    }

    /**
     * Generate Javascript for page editor
     *
     * @param string        &$buff
     * @param Pt_PageParams $params
     */
    public function toEditorJS(& $buff, Pt_PageParams $params) 
    {
        $listDefs = Pt_ListDefManager::getByObjectDef($this->tableObjDef->getId());
        $listDefId = 0;
        if ($listDefs) {
            $listDefId = $listDefs[0]->getId(); 
        }

        $cellLabel = GT($this->textMap, "IA.VIEW");
        $cellValue = $this->tableObjDef->getPluralName();

        $props = [];
        $props[FIELD_OBJ_DEF_ID2] = $this->tableObjDef->getId();
        $props[FIELD_SEL_LIST_ID] = $listDefId;

        $this->appendEditorJS($buff, $listDefId, $cellLabel, $cellValue, $props);

        $buff .= $this->cellJSName.".listViews=new Array();\n";
        foreach ($listDefs as $ld) {
            $buff .= $this->cellJSName.".listViews[".$this->cellJSName.".listViews.length] = {\n";
            $buff .= "\"listId\":".$ld->getId().",\n";
            $buff .= "\"listName\":\"".util_jsEncode($ld->getDisplayLabel())."\"\n";
            $buff .= "};\n";
        }
        $buff .= "\n";

        if ($params->getPage()->isPortal()) {
            $pages = Pt_WebPageManager::getByPortal($params->getPortal());
            $buff .= $this->cellJSName.".viewPages=new Array();\n";
            foreach ($pages as $page) {
                if ($this->tableObjDef != null && util_inArgs($page->getPageType(), TYPE_VIEW, TYPE_GENERIC, TYPE_LIST) && $page->getObjectDefId()==$this->tableObjDef->getId()) {
                    $buff .= $this->cellJSName.".viewPages[".$this->cellJSName.".viewPages.length] = {\n";
                    $buff .= "\"viewPageId\":".$page->getId().",\n";
                    $buff .= "\"pageName\":\"".util_jsEncode($page->__toString())."\"\n";
                    $buff .= "};\n";
                }
            }
        }
        $buff .= "\n";
    }
}

