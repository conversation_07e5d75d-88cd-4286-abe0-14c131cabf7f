<?
/**
 * Manages HTTP requests to create/modify $data objects.
 */


require_once('Pt_Includes.inc');

require_once("Pt_SecurityUtil.inc");
require_once("Pt_DataObjectManager.cls");
require_once("Pt_DataObjectDefManager.cls");
require_once("Pt_RelationshipManager.cls");
require_once("Pt_RelationshipDefManager.cls");
require_once("Pt_DataFieldManager.cls");
require_once("Pt_FlagManager.cls");
require_once("Pt_ActionManager.cls");
require_once("Pt_StatusManager.cls");
require_once("Pt_ProcessManager.cls");
require_once("Pt_EventRunner.cls");
require_once("Pt_DataMapController.cls");
require_once("Pt_Filter.cls");
require_once("Pt_FilterList.cls");

class Pt_ObjectController {

    /**
     * @return int
     *
     * @throws Exception
     */
    public static function getMatchedObjectCount() {
        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $objDef = Pt_DataObjectDefManager::getById($objDefId);
        if ($objDef == null) {
            throw new Pt_I18nException('PAAS-0245', "Object definition with id $objDefId not found",
                                       [ 'OBJDEFID' => $objDefId ]);
        }

        Globals::$g->gPtSecurity->checkExtendedPermission($objDef, http_getRefererOperation($_SERVER));

        $query = http_getParameter(FIELD_QUERY);
        
        if ($objDef->isPlatform()) {
            $fieldName = http_getParameter(FIELD_NAME);
            $field = $objDef->getByName($fieldName);
            if ($field == null) {
                throw new Pt_I18nException('PAAS-0246', "Field $fieldName not found", [ 'FIELDNAME' => $fieldName ]);
            }
        } else {
            $field = $objDef->getByName($objDef->getNameColumn());
        }
        
        return Pt_DataObjectManager::getMatchedObjectCount($objDef, $field, $query);
    }

    /**
     * Find number of objects of given type, which match to given text pattern.
     * Returns formatted string with $ids and names of matched objects.
     *
     * @param int     $maxRecords
     * @param bool    $sort_results
     *
     * @return string
     */
    public static function getMatchedObjects($maxRecords, $sort_results = false)
    {
        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $objDef = Pt_DataObjectDefManager::getById($objDefId);
        if ($objDef == null) {
            throw new Pt_I18nException('PAAS-0247', "Object definition with id $objDefId not found",
                                       [ 'OBJDEFID' => $objDefId ]);
        }

        Globals::$g->gPtSecurity->checkExtendedPermission($objDef, http_getRefererOperation($_SERVER));

        $query = http_getParameter(FIELD_QUERY);

        if ($objDef->isPlatform()) {
            $fieldName = http_getParameter(FIELD_NAME);
            $field = $objDef->getByName($fieldName);
            if ($field == null) {
                throw new Pt_I18nException('PAAS-0248', "Field $fieldName not found", [ 'FIELDNAME' => $fieldName ]);
            }
        } else {
            $field = $objDef->getByName($objDef->getNameColumn());
        }
        $mainFilter = null;
        $filterField = http_getParameter(FIELD_FILTER_FIELD);
        if ($filterField != null) {
            $mainField = $objDef->getByName($filterField);
            $filterValue = http_getParameter(FIELD_FILTER_VALUE);
            if ($mainField != null && $filterValue != null) {
                $mainFilter = new Pt_Filter(0, $mainField, OP_EQ, $filterValue, (int) $filterValue);
            }
        }

        $objs = null;
        $selectorViewId = http_getIntParameter(FIELD_SELECTOR_VIEW_ID);

        if ($selectorViewId > 0) {
            $listDef = Pt_ListDefManager::getById($selectorViewId);
            if ($listDef != null) {
                $lq = new Pt_ListQuery($listDef, null);
                if ($mainFilter != null) {
                    $lq->addFilter($mainFilter);
                }
                $lq->addFilter(new Pt_Filter($lq->getNumFilters(), $field, OP_ST, $query, $query));
                $objs = $lq->getDataObjectsCollection();
            }
        }
        

        if ($objs == null) {
            $objs = Pt_DataObjectManager::getMatchedObjects($objDef, $field, $query, $maxRecords, $mainFilter);
        }
        $buff = [];
        if($sort_results) {
        	$raw_results = [];
        	foreach ($objs as $data) {
        		$id = $data->getId();
        		$value = $data->getLookupLabel();
        		$raw_results[$id] = $value;
        	}
            uasort($raw_results, function($a, $b) {
               return strcasecmp($a, $b);
            });
        	foreach($raw_results as $id=>$value) {
        		$buff[] = $id;
        		$buff[] = $value;
        	}
        } else {
        	foreach ($objs as $data) {
        		$buff[] = $data->getId();
        		$buff[] = $data->getLookupLabel();
        	}
        }
        $tokens = ["IA.NO_MATCHES_FOUND"];
        $placeholderTokens = [];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        if(!$buff) {
            return "<Error>" . GT($textMap, "IA.NO_MATCHES_FOUND") . "</Error>";
        }

        return join("\n", $buff);
    }

    /**
     * Find number of objects of given type related to given object
     * Returns formatted string with $ids and names of related objects.
     *
     * @return string
     */
    public static function getRelatedObjects()
    {
        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $objDef = Pt_DataObjectDefManager::getById($objDefId);
        if ($objDef == null) {
            throw new Pt_I18nException('PAAS-0249', "Object definition with id $objDefId not found",
                                       [ 'OBJDEFID' => $objDefId ]);
        }
    
        Globals::$g->gPtSecurity->checkPermission($objDef, [ 'list' ]);

        $id = http_getIntParameter(FIELD_ID);
        $mainData = Pt_DataObjectManager::getById($objDef, $id);
        if ($mainData == null) {
            throw new Pt_I18nException('PAAS-0250', "Data object $objDef with id $id not found",
                                       [ 'OBJDEF' => "$objDef", 'ID' => $id ]);
        }

        $relId = http_getIntParameter(FIELD_REL_ID);
        $rel = Pt_RelationshipDefManager::getById($relId);
        if ($rel == null) {
            throw new Pt_I18nException('PAAS-0251', "Relationship definition with id $relId not found",
                                       [ 'ID' => $relId ]);
        }

        $objs = Pt_RelationshipManagerChoose::getObjects($rel, $mainData);

        $buff = [];
        foreach ($objs as $data) {
            $buff[] = $data->getId();
            $buff[]  = $data->__toString();
        }
        return join("\n", $buff);
    }

    /**
     * Fetch and populate data needed for create call.
     *
     * @param Pt_WebPage         $page             the origin web page
     * @param Pt_DataObjectDef   $objDef           the obj def we're creating
     * @param Pt_DataObject      $cloneData        data from the object being cloned
     *
     * @return array  the new object's fieldData
     *
     * @throws Exception              on error
     */
    public static function getDataForCreate($page, &$objDef=null, &$cloneData=null) {

        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $objDef = Pt_DataObjectDefManager::getById($objDefId);
        if ( $objDef == null ) {
            throw new Pt_I18nException('PAAS-0252', "Cannot find object definition");
        }
        util_checkPermission($objDef, 'add');

        $cloneId = http_getIntParameter(FIELD_CLONE_ID);
        $cloneData = Pt_DataObjectManager::getById($objDef, $cloneId);
        $clonedValues = null;
        if ( $cloneData !=null ) {
            $clonedValues = [];
            foreach ( $cloneData->getFullFieldMap() as $fieldName => $fieldValue ) {
                if ( $fieldValue == null ) {
                    continue;
                }
                $field = $objDef->getByName($fieldName);
                if ( $field == null || $field->isNoClone() ) {
                    continue;
                }
                $clonedValues[$fieldName] = $fieldValue;
            }
        }

        $fieldData = $page->getAttributes($clonedValues);

        return $fieldData;

    }

    /**
     * Create data object.
     *
     * @param array $validationContext
     *
     * @return Pt_DataObject|null
     */
    public static function create($validationContext) {

        $pageId = http_getIntParameter(FIELD_SRC_ID);
        $page = Pt_WebPageManager::getById($pageId);
        if ( $page == null ) {
            throw new Pt_I18nException('PAAS-0253', "Cannot find web page");
        }

        $fieldData = $validationContext['fieldData'];
        $objDef = $validationContext['objDef'];
        $cloneData = $validationContext['cloneData'];

        $relId = http_getIntParameter(FIELD_REL_ID);    // relationship id

        $relatedId = http_getIntParameter(FIELD_RELATED_ID);
        $relatedData = Pt_DataObjectManager::getById2($relatedId);
        $updateRelated = false;

        $actionId = http_getIntParameter(FIELD_ACTION_ID);
        $nextStatus = null;
        $map = null;
        if ($actionId > 0) {
            $a = Pt_ActionManager::getById($actionId);
            if ($a != null) {
                $nextStatus = $a->getNextStatus();
                $map = $a->getConversionMap();
            }
        }
        
        $runner = new Pt_EventRunner(CTX_UI);
        $debug = Pt_EventDebugger::getInstance();
        if ($debug != null) {
            $runner->setDebugger($debug);
        }

        XACT_BEGIN('PLATFORM');
        $data = null;
        try {

            // Create by cloning - clone dependent
            if ($cloneData != null) {
                Pt_DataObjectManager::cloneDependent($cloneData, $fieldData, $runner);
            }

            $data = Pt_DataObjectManager::create($objDef, $fieldData, $runner);

            if ($relatedData != null) {
                if (Pt_RelationshipManagerChoose::hasRelationship($relId, $data->getId(), $relatedData->getId())) {
                    Pt_DataObjectManager::setLastUpdate($relatedData);
                    $updateRelated = true;
                }
                if ($nextStatus != null && $relatedData->getObjectDefId()==$nextStatus->getObjectDefId()) {
                    Pt_DataObjectManager::changeStatus($relatedData, $nextStatus);
                    $updateRelated = true;
                }

                if ($map != null && $map->getDeleteSrc()) {
                    Pt_DataObjectManager::delete($relatedData);
                    /** @noinspection PhpUnusedLocalVariableInspection */
                    $relatedData = null;
                    $updateRelated = false;
                }
            }

            XACT_COMMIT('PLATFORM');

        } catch (Exception $ex) {
            //I18N : TODO
            db_rollback($ex);
        }
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.OBJ_DATA_HAS_BEEN_CREATED",
                'placeHolders' => [
                    [ 'name' => 'OBJ', 'value' => $objDef->__toString()],
                    [ 'name' => 'DATA', 'value' => Pt_WebUtil::getViewLink($data)]
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.OBJ_DATA_HAS_BEEN_CREATED");

        if ($updateRelated) {
            $runner->runEvents(Pt_DataObjectManager::getById2($relatedId), ON_AFTER_UPDATE);
        }
        $runner->finalize();

        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
        Pt_ObjectStack::setObject($data);
        return $data;
    }

    /**
     * Create data object (quick form).
     *
     * @return Pt_DataObject|null
     */
    public static function createQuick() {
        $srcPageId = http_getIntParameter(FIELD_SRC_ID);
        $page = Pt_WebPageManager::getById($srcPageId);
        if ($page == null) {
            throw new Pt_I18nException('PAAS-0254', "Cannot find web page");
        }

        if ( ! $page->validate(ACTION_QUICK_CREATE, 'Pt_ObjectController', $validationContext) ) {
            $funcNameParam = "";
            $funcName = http_getParameter(FIELD_FUNC_NAME);
            if ( $funcName ) {
                $funcNameParam .= "&" . FIELD_FUNC_NAME . "=" . urlencode($funcName);
            }
            $fieldId = http_getParameter("fieldId");
            if ( $fieldId !== null && $fieldId != "" ) {
                $funcNameParam .= "&fieldId=" . urlencode($fieldId);
            }
            $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
            $returnURL = Pt_WebUtil::url('pt_objectQuickCreate.phtml', OP_RUNTIME).'&objDefId='.$objDefId.'&pageId='.$srcPageId.$funcNameParam;
            http_redirect($returnURL);
            return null;
        }

        return self::create($validationContext);
    }

    /**
     * Fetch and populate data needed for update call.
     *
     * @param Pt_DataObjectDef &$objDef the obj def we're creating
     * @param Pt_DataObject    &$data   old version of the object being updated
     *
     * @return array  the new object's fieldData
     *
     * @throws Exception  on error
     */
    public static function getDataForUpdate(&$objDef = null, &$data = null) {

        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $objDef = Pt_DataObjectDefManager::getById($objDefId);
        if ( $objDef == null ) {
            throw new Pt_I18nException('PAAS-0255', "Cannot find object definition");
        }
        util_checkPermission($objDef, 'modify');

        $pageId = http_getIntParameter(FIELD_SRC_ID);
        $page = Pt_WebPageManager::getById($pageId);
        if ( $page == null ) {
            throw new Pt_I18nException('PAAS-0256', "Cannot find web page");
        }

        $id = http_getIntParameter(FIELD_ID);
        $data = Pt_DataObjectManager::getById($objDef, $id);
        if ( $data == null ) {
            throw new Pt_I18nException('PAAS-0257', "DataObject with id $id not found", [ 'ID' => $id ]);
        }
        // 22818: Must call getFullFieldMap to populate full record
        // for the returned fieldData.
        $data->getFullFieldMap();

        $fieldData = $page->getAttributes($data->getShortFieldMap());

        return $fieldData;
    }

    /**
     * Update single data object.
     *
     * @return Pt_DataObject
     */
    public static function update()
    {

        /* @var Pt_DataObjectDef $objDef  (PhpStorm bug) */
        /* @var Pt_DataObject    $data    (PhpStorm bug) */
        $fieldData = self::getDataForUpdate($objDef, $data);
        $oldData = $data->getFullFieldMap();

        $runner = new Pt_EventRunner(CTX_UI);
        $runner->setOldData($oldData);
        $debug = Pt_EventDebugger::getInstance();
        if ($debug) {
            $runner->setDebugger($debug);
        }

        XACT_BEGIN('PLATFORM');
        try {
            Pt_DataObjectManager::update($data, $fieldData, $runner);
            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            //I18N : TODO
            db_rollback($ex);
        }
        $runner->finalize();
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.OBJ_DATA_HAS_BEEN_UPDATED",
                'placeHolders' => [
                    [ 'name' => 'OBJ', 'value' => util_encode($objDef->__toString()) ],
                    [ 'name' => 'DATA', 'value' => Pt_WebUtil::getViewLink($data) ],
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.OBJ_DATA_HAS_BEEN_UPDATED");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);

        Pt_ObjectStack::setObject($data);
        return $data;
    }

    /**
     * Update single field on data object. Used by AJAX inline editing.
     *
     * @return Pt_DataObject
     */
    public static function updateField()
    {
        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $objDef = Pt_DataObjectDefManager::getById($objDefId);
        if ($objDef == null) {
            throw new Pt_I18nException('PAAS-0258', "Cannot find object definition");
        }
        util_checkPermission($objDef, 'modify');

        $id = http_getIntParameter(FIELD_ID);
        $data = Pt_DataObjectManager::getById($objDef, $id);
        if ($data == null) {
            throw new Pt_I18nException('PAAS-0259', "DataObject with id $id not found", [ 'ID' => $id ]);
        }
        $oldData = $data->getFullFieldMap();

        $fieldName = http_getParameter(FIELD_FIELD_NAME);
        $field = $objDef->getByName($fieldName);
        if ($field == null) {
            throw new Pt_I18nException('PAAS-0260', "Field with name $fieldName not found",
                                       [ 'FIELDNAME' => $fieldName ]);
        }
        $uiField = $field->getUIField();
        $newValue = $uiField->getHttpValue();

        $pageId = http_getParameter(FIELD_PAGE_ID);
        $page = Pt_WebPageManager::getById($pageId);
        if ($page == null) {
            throw new Pt_I18nException('PAAS-0261', "Cannot find web page");
        }
        $cell = $page->getCellByFieldId($field->getId());

        $errs = new Pt_ValidationErrors();
        $errs->setValues($oldData);
        $cell->validate($errs, TYPE_EDIT);
        $errs->setValue($fieldName, $newValue);
        Pt_UniqueValueController::validate($objDef, $id, $errs->getAllValues(), $errs,
            Pt_UniqueValueController::VALIDATE_ALL_FIELDS);

        $dataMap = $data->getShortFieldMap();
        $dataMap[$fieldName] = $newValue;
        $tempData = new Pt_DataObject($data->getId(), $data->getObjectDefId(), $dataMap);

        if ($uiField->hasValidationScript()) {
            $parser = new Pt_TemplateParser(JAVA_SCRIPT, $objDef);
            $parser->setActivation(ACTIV_VALIDATION);
            $parser->setContext(CTX_UI);
            $parser->setOldData($oldData);    // T 3929

            $customMessage = $uiField->validateScript($parser, $tempData);
            if (strlen($customMessage) > 0) {
                $errs->setError($uiField->getFieldName(), $customMessage);
            }
        }

        $runner = new Pt_EventRunner(CTX_UI);
        $runner->setOldData($oldData);
        $debug = Pt_EventDebugger::getInstance();
        if ($debug != null) {
            $runner->setDebugger($debug);
        }

        if (Pt_EventDefManager::hasValidation($objDefId, ON_BEFORE_UPDATE)) {
            try {
                $runner->runValidation($tempData, ON_BEFORE_UPDATE);    // T 9544
            } catch (Exception $ex) {
                //I18N : TODO
                $errs->addErrorMessage($ex->getMessage());
            }
        }

        if ($errs->hasErrors()) {
            //I18N : TODO
            Session::setProperty(ATTR_INFO_MESSAGE, "Error: ".$errs->getSummaryErrors());
            return null;
        }

        XACT_BEGIN('PLATFORM');
        try {
            Pt_DataFieldManager::setField($data, $field, $newValue, $runner);
            Pt_DataObjectManager::setLastUpdate($data); // Bug 39268

            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            //I18N : TODO
            db_rollback($ex);
        }
        $runner->finalize();
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.FIELD_FIELD_HAS_BEEN_UPDATED",
                'placeHolders' => [
                    [ 'name' => 'FIELD', 'value' => util_encode($field->__toString()) ],
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.FIELD_FIELD_HAS_BEEN_UPDATED");

        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
        return $data;
    }

    /**
     * Create a group of data objects.
     */
    public static function massCreate() {
        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $objDef = Pt_DataObjectDefManager::getById($objDefId);
        if ($objDef == null) {
            throw new Pt_I18nException('PAAS-0262', "Cannot find object definition");
        }
        util_checkPermission($objDef, 'add');

        $actionId = http_getIntParameter(FIELD_ACTION_ID);
        $a = Pt_ActionManager::getById($actionId);
        if ($a == null) {
            throw new Pt_I18nException('PAAS-0263', "Action with id $actionId not found", [ 'ACTIONID' => $actionId ]);
        }
        $objDef2 = $a->getObjectDef();

        $ids = http_getIntArray(FIELD_IDS);
        if (!$ids) {
            throw new Pt_I18nException('PAAS-0264', "Cannot find object ids");
        }
        $objs = [];
        foreach ($ids as $id) {
            $data = Pt_DataObjectManager::getById($objDef2, $id);
            if ($data != null) {
                $objs[] = $data;
            }
        }

        $pageId = http_getIntParameter(FIELD_SRC_ID);
        $page = Pt_WebPageManager::getById($pageId);
        if ($page == null) {
            throw new Pt_I18nException('PAAS-0265', "Cannot find web page");
        }

        $relFields = [];
        foreach ($objDef->getFields() as $field) {
            if ($field instanceof Pt_FieldRelationship) {
                /* @var Pt_Relationship $rel */
                $rel = $field->getUIField();
                if ($objDef2->getId() == $rel->getObjectDefId2()) {
                    $relFields[$field->getFieldName()] = $field;
                }
            }
        }

        $pageData = $page->getAttributes([]);

        $runner = new Pt_EventRunner(CTX_UI);
        $debug = Pt_EventDebugger::getInstance();
        if ($debug != null) {
            $runner->setDebugger($debug);
        }

        $newData = [];
        XACT_BEGIN('PLATFORM');
        try {
            foreach ($objs as $odata) {
                $fieldData = array_merge([], $pageData);
                foreach ( $relFields as $key => $field) {
                    $fieldData[$key] = [$odata->getId()];
                }
                self::populateAutoFields($objDef, $fieldData);
                self::populateAutoNumberFields($objDef, $fieldData);

                $data = Pt_DataObjectManager::create($objDef, $fieldData, $runner);
                /** @noinspection OnlyWritesOnParameterInspection */
                $newData[] = $data;

                Pt_DataObjectManager::setLastUpdate($odata);
                $runner->reset();
            }
            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            //I18N : TODO
            db_rollback($ex);
        }
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.COUNT_OBJS_HAVE_BEEN_CREATED",
                'placeHolders' => [
                    [ 'name' => 'COUNT', 'value' => count($objs)],
                    [ 'name' => 'OBJS', 'value' => util_encode($objDef->getPluralName())]
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.COUNT_OBJS_HAVE_BEEN_CREATED");

        // TODO: This should probably loop over $newData
        foreach ($objs as $odata) {
            $runner->runEvents($odata, ON_AFTER_UPDATE);
            $runner->reset();
        }
        $runner->finalize();

        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
    }

    /**
     * Fetch and populate data needed for mass update call.  Most of the return parameters will be passed
     * to mergeDataForMassUpdate.
     *
     * @param bool                    $doAction    is true to process the action to get a new status
     * @param Pt_DataObjectDef|null   &$objDef     the obj def we're creating (null on exceptions)
     * @param int                     &$newStatus  the resulting status if $doAction was true
     * @param array                   &$pageData   the fields from the source page, not a fully-merged object
     * @param Pt_FieldRelationship[]  &$relFields  any to-many relationship lookup fields
     * @param Pt_DataObject[]         &$objs       the old version of all the existing objects to update
     *
     * @throws Exception              on error
     */
    public static function getDataForMassUpdate($doAction, &$objDef, &$newStatus, &$pageData, &$relFields, &$objs) {

        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $objDef = Pt_DataObjectDefManager::getById($objDefId);
        if ( $objDef == null ) {
            throw new Pt_I18nException('PAAS-0266', "Cannot find object definition");
        }
        /* @var Pt_DataObjectDef $objDef */
        util_checkPermission($objDef, 'modify');

        $newStatus = -1;
        /** @noinspection PhpUnusedLocalVariableInspection */
        $a = null;
        if ( $doAction ) {
            $actionId = http_getIntParameter(FIELD_ACTION_ID);
            $a = Pt_ActionManager::getById($actionId);
            if ( $a == null ) {
                throw new Pt_I18nException('PAAS-0267', "Action with id $actionId not found",
                                           [ 'ACTIONID' => $actionId ]);
            }
            $newStatus = $a->getNextStatusId();
        }

        $ids = http_getIntArray(FIELD_IDS);
        if ( ! $ids ) {
            throw new Pt_I18nException('PAAS-0268', "Cannot find object ids");
        }

        $fieldNamesByRelId = [];
        $relFields = [];
        foreach ( $objDef->getByDataClass('Pt_FieldRelationship') as $field ) {
            $fieldNamesByRelId[$field->getRelationshipDefId()] = $field->getFieldName();
            /* @var Pt_Relationship $rel */
            $rel = $field->getUIField();
            if ( $rel->isMultiple() ) {
                $relFields[$field->getFieldName()] = $field;
            }
        }

        $objs = [];
        foreach ($ids as $id) {

            $data = Pt_DataObjectManager::getById($objDef, $id);
            if ( $data == null ) {
                continue;
            }

            if ( $fieldNamesByRelId ) {
                $relationships = Pt_RelationshipManagerChoose::getAllRelatedObjectIds($id, $objDef->getId());

                // Clear out all the cached lookup field values
                foreach ( $fieldNamesByRelId as $fieldName ) {
                    $data->setFieldValue($fieldName, 0);
                }

                foreach ( $relationships as $relationshipId => $relatedObjIds ) {

                    if ( ! isset($fieldNamesByRelId[$relationshipId]) ) {
                        // Hmm, a relationship we don't have a field for.
                        continue;
                    }

                    $fieldData = new Pt_ArrayIds();
                    foreach ( $relatedObjIds as $relatedObjId ) {
                        $fieldData->add($relatedObjId);
                    }
                    $fieldName = $fieldNamesByRelId[$relationshipId];
                    $data->setFieldValue($fieldName, $fieldData);

                }
            }

            $objs[] = $data;
        }

        $pageId = http_getIntParameter(FIELD_SRC_ID);
        $page = Pt_WebPageManager::getById($pageId);
        if ( $page == null ) {
            throw new Pt_I18nException('PAAS-0269', "Cannot find web page");
        }

        $pageData = $page->getAttributes([]);

    }

    /**
     * Merge old and new data for a specific mass update record.
     *
     * @param Pt_DataObject           $data        the old version of a record being updated.  Should be
     *                                             one of the $objs returned from getDataForMassUpdate.
     * @param int                     $newStatus   the new status from getDataForMassUpdate
     * @param Pt_DataObject           $pageData    the updated field values from getDataForMassUpdate
     * @param Pt_FieldRelationship[]  $relFields   any to-many relationship lookup fields from
     *                                             getDataForMassUpdate
     * @param array                   &$oldData    The short field map of the old data
     *
     * @return array the merged field data
     *
     */
    public static function mergeDataForMassUpdate($data, $newStatus, $pageData, $relFields, &$oldData) {

        $oldData = $data->getShortFieldMap();

        $fieldData = array_merge([], $oldData);
        foreach ( $pageData as $key => $value ) {
            if ( $relFields[$key] != null ) {
                $existing = $fieldData[$key] ?? null;
                if ( is_array($existing)) {
                    $x = $existing;
                } else if ( $existing instanceof Pt_ArrayIds ) {
                    $x = $existing->getIds();
                } else {
                    $x = [];
                }
                $y = (is_array($value) ? $value : [$value]);
                $fieldData[$key] = array_merge($y, $x);
            } else if ( isset($value) ) {
                if (is_array($value) && !$value) {
                    continue;
                }
                $fieldData[$key] = $value;
            }
        }
        if ( $newStatus > 0 ) {
            $fieldData[FIELD_STATUS] = $newStatus;
        }

        return $fieldData;
    }

    /**
     * Update group of data objects.
     *
     * @param bool  $doAction
     */
    public static function massUpdate($doAction)
    {

        /* @var Pt_DataObjectDef $objDef (PhpStorm bug) */
        self::getDataForMassUpdate($doAction, $objDef, $newStatus, $pageData, $relFields, $objs);

        $runner = new Pt_EventRunner(CTX_UI);
        $debug = Pt_EventDebugger::getInstance();
        if ( $debug != null ) {
            $runner->setDebugger($debug);
        }

        XACT_BEGIN('PLATFORM');
        try {
            foreach ($objs as $data) {

                $fieldData = self::mergeDataForMassUpdate($data, $newStatus, $pageData, $relFields, $oldData);
                $runner->setOldData($oldData);
                Pt_DataObjectManager::update($data, $fieldData, $runner);

                $runner->reset();
            }
            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            //I18N : TODO
            db_rollback($ex);
        }
        $runner->finalize();
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.COUNT_OBJS_HAVE_BEEN_UPDATED",
                'placeHolders' => [
                    [ 'name' => 'COUNT', 'value' => count($objs)],
                    [ 'name' => 'OBJS', 'value' => util_encode($objDef->getPluralName())]
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.COUNT_OBJS_HAVE_BEEN_UPDATED");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
        Pt_SelectedRecords::clearRecords($objDef->getId());
    }

    /**
     * Fetch and populate data needed for change status call.
     *
     * @param Pt_DataObject           &$data   old version of the object being updated
     * @param Pt_Action               &$a      the action being performed
     *
     * @return array                  the new object's fieldData
     *
     * @throws Exception              on error
     */
    public static function getDataForChangeStatus(&$data=null, &$a=null)
    {

        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $objDef = Pt_DataObjectDefManager::getById($objDefId);
        if ( $objDef == null ) {
            throw new Pt_I18nException('PAAS-0270', "Cannot find object definition");
        }

        $id = http_getIntParameter(FIELD_ID);
        $data = Pt_DataObjectManager::getById($objDef, $id);
        if ( $data == null ) {
            throw new Pt_I18nException('PAAS-0271', "DataObject with id $id not found", [ 'ID' => $id ]);
        }
        // 22818: Must call getFullFieldMap to populate full record
        // for the returned fieldData.
        $data->getFullFieldMap();

        $actionId = http_getIntParameter(FIELD_ACTION_ID);
        $a = Pt_ActionManager::getById($actionId);
        if ( $a == null ) {
            throw new Pt_I18nException('PAAS-0272', "Action with id $actionId not found", [ 'ACTIONID' => $actionId ]);
        }
        if ( ! util_hasPermission('ACTION', $actionId, 'readonly') ) {
            throw new Pt_I18nException('PAAS-0273',
                                       "Permission Denied: Please check with your Administrator. You do not have permission to perform this action.");
        }

        $newStatus = $a->getNextStatusId();

        $pageId = http_getIntParameter(FIELD_SRC_ID);
        $page = Pt_WebPageManager::getById($pageId);
        if ( $page == null ) {
            throw new Pt_I18nException('PAAS-0274', "Cannot find web page");
        }

        $fieldData = $page->getAttributes($data->getShortFieldMap());
        if ( $newStatus > 0 ) {
            $fieldData[FIELD_STATUS] = $newStatus;
        }

        return $fieldData;

    }

    /**
     * Change status of data object.
     *
     * @return Pt_DataObject
     */
    public static function changeStatus()
    {
        /* @var Pt_DataObject $data  (PhpStorm bug) */
        /* @var Pt_Action $a         (PhpStorm bug) */
        $fieldData = self::getDataForChangeStatus($data, $a);
        $oldData = $data->getFullFieldMap();

        $runner = new Pt_EventRunner(CTX_UI);
        $runner->setOldData($oldData);
        $debug = Pt_EventDebugger::getInstance();
        if ( $debug != null ) {
            $runner->setDebugger($debug);
        }

        XACT_BEGIN('PLATFORM');
        try {
            Pt_DataObjectManager::update($data, $fieldData, $runner);

            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            //I18N : TODO
            db_rollback($ex);
        }
        $runner->finalize();
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.ACTION_HAS_BEEN_PERFORMED_ON_OBJ",
                'placeHolders' => [
                    [ 'name' => 'ACTION', 'value' => util_encode($a->__toString())],
                    [ 'name' => 'OBJ', 'value' => Pt_WebUtil::getViewLink($data)]
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.ACTION_HAS_BEEN_PERFORMED_ON_OBJ");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);

        return $data;
    }

    /**
     * Convert data object.
     *
     * @return Pt_DataObject
     */
    public static function convert() {
        $map = Pt_DataMapController::readConversionMap();
        if ($map == null) {
            throw new Pt_I18nException('PAAS-0275', "No mapping info provided");
        }
        $destDef = $map->getDestObjectDef();
        
        util_checkPermission($destDef, 'add');

        $actionId = http_getIntParameter(FIELD_ACTION_ID);
        $a = Pt_ActionManager::getById($actionId);
        $newStatus = ($a==null ? null : $a->getNextStatus());

        $srcDefId = http_getIntParameter(FIELD_SRC_DEF_ID);
        $srcObjDef = Pt_DataObjectDefManager::getById($srcDefId);
        
        if ($srcObjDef == null) {
            throw new Pt_I18nException('PAAS-0276', "DataObjectDef with id $srcDefId not found",
                                       [ 'OBJDEFID' => $srcDefId ]);
        }
        
        if ( ($destDef != null && $destDef->isGLDimension()) || ($srcObjDef != null && $srcObjDef->isGLDimension()) ) {
            throw new Pt_I18nException('PAAS-0277', "Cannot convert to or from GL Dimension records");
        }

        $id = http_getIntParameter(FIELD_IDS);
        $srcData = Pt_DataObjectManager::getById($srcObjDef, $id);
        if ($srcData == null) {
            throw new Pt_I18nException('PAAS-0278', "DataObject with id $id not found", [ 'ID' => $id ]);
        }
        $fieldData = $map->getMappedValues($srcData);
        self::populateAutoFields($destDef, $fieldData);
        self::validateFields($destDef, $fieldData, self::VALIDATE_SKIP_AUTONUMBER);
        self::populateAutoNumberFields($destDef, $fieldData);
        self::validateFields($destDef, $fieldData, self::VALIDATE_ONLY_AUTONUMBER);

        $runner = new Pt_EventRunner(CTX_UI);
        $debug = Pt_EventDebugger::getInstance();
        if ($debug != null) {
            $runner->setDebugger($debug);
        }

        XACT_BEGIN('PLATFORM');
        $destData = null;
        try {
            $destData = Pt_DataObjectManager::create($destDef, $fieldData, $runner);

            if ($map->getDeleteSrc()) {
                Pt_DataObjectManager::delete($srcData);
            } else if ($newStatus != null) {
                Pt_DataObjectManager::changeStatus($srcData, $newStatus);
            }

            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            //I18N : TODO
            db_rollback($ex);
        }
        $runner->finalize();
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.OBJ_HAS_BEEN_CONVERTED_TO_OBJ",
                'placeHolders' => [
                    [ 'name' => 'OBJ1', 'value' => Pt_WebUtil::getViewLink($srcData)],
                    [ 'name' => 'OBJ2', 'value' => Pt_WebUtil::getViewLink($destData)]
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.OBJ_HAS_BEEN_CONVERTED_TO_OBJ");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
        return $destData;
    }

    /**
     * Convert array of data objects.
     */
    public static function convertArr() {
        $map = Pt_DataMapController::readConversionMap();
        if ($map == null) {
            throw new Pt_I18nException('PAAS-0279', "No mapping info provided");
        }
        $destDef = $map->getDestObjectDef();
        util_checkPermission($destDef, 'add');

        $actionId = http_getIntParameter(FIELD_ACTION_ID);
        $a = Pt_ActionManager::getById($actionId);
        $newStatus = ($a==null ? null : $a->getNextStatus());

        $srcDefId = http_getIntParameter(FIELD_SRC_DEF_ID);
        $srcObjDef = Pt_DataObjectDefManager::getById($srcDefId);
        if ($srcObjDef == null) {
            throw new Pt_I18nException('PAAS-0280', "DataObjectDef with id $srcDefId not found",
                                       [ 'OBJDEFID' => $srcDefId ]);
        }
        
        if ( ($destDef != null && $destDef->isGLDimension()) || ($srcObjDef != null && $srcObjDef->isGLDimension()) ) {
            throw new Pt_I18nException('PAAS-0281', "Cannot convert to or from GL Dimension records");
        }
        
        $ids = http_getIntArray(FIELD_IDS);
        $arr = [];
        $runner = new Pt_EventRunner(CTX_UI);
        $debug = Pt_EventDebugger::getInstance();
        if ($debug != null) {
            $runner->setDebugger($debug);
        }

        XACT_BEGIN('PLATFORM');
        try {
            foreach ($ids as $id) {
                $srcData = Pt_DataObjectManager::getById($srcObjDef, $id);
                if ($srcData == null) {
                    continue;
                }

                $fieldData = $map->getMappedValues($srcData);
                self::populateAutoFields($destDef, $fieldData);
                self::validateFields($destDef, $fieldData, self::VALIDATE_SKIP_AUTONUMBER);
                self::populateAutoNumberFields($destDef, $fieldData);
                self::validateFields($destDef, $fieldData, self::VALIDATE_ONLY_AUTONUMBER);

                $destData = Pt_DataObjectManager::create($destDef, $fieldData, $runner);
                if ($map->getDeleteSrc()) {
                    Pt_DataObjectManager::delete($srcData);
                } else if ($newStatus != null) {
                    Pt_DataObjectManager::changeStatus($srcData, $newStatus);
                }

                $arr[] = $destData;
                $runner->reset();
            }

            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            //I18N : TODO
            db_rollback($ex);
        }
        $runner->finalize();
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.COUNT_OBJS_HAVE_BEEN_CONVERTED_TO_DEST",
                'placeHolders' => [
                    [ 'name' => 'COUNT', 'value' => count($arr)],
                    [ 'name' => 'OBJS', 'value' => util_encode($srcObjDef->getPluralName())],
                    [ 'name' => 'DEST', 'value' => util_encode($destDef->getPluralName())]
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.COUNT_OBJS_HAVE_BEEN_CONVERTED_TO_DEST");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
    }

    /**
     * Merge data objects.
     *
     * @return Pt_DataObject
     */
    public static function merge() {
        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $objDef = Pt_DataObjectDefManager::getById($objDefId);
        if ( $objDef == null ) {
            throw new Pt_I18nException('PAAS-0282', "DataObjectDef with id $objDefId not found",
                                       [ 'OBJDEFID' => $objDefId ]);
        }

        if ( $objDef->isGLDimension() ) {
            throw new Pt_I18nException('PAAS-0283', "Cannot merge GL Dimension records");
        }
        
        util_checkPermission($objDef, 'add');

        $ids = http_getIntArray(FIELD_IDS);
        /* @var Pt_DataObject[] $arr (PhpStorm's not smart enough) */
        $arr = [];
        $numData = 0;
        foreach ( $ids as $id ) {
            $data = Pt_DataObjectManager::getById($objDef, $id);
            if ( $data != null ) {
                $arr[] = $data;
                $numData++;
            }
        }

        $fieldData = [];
        $autoNumberFieldIds = [];
        foreach ( $objDef->getFields() as $field ) {
            if ( $field == null ) {
                continue;
            }
            $uiField = $field->getUIField();
            if ( $uiField instanceof Pt_CommentsArea || $uiField instanceof Pt_DependentList
                 || ( $field->isReadOnly() && !( $uiField instanceof Pt_AutoNumber ) ) ) {
                continue;
            }
            $fieldName = $field->getFieldName();
            $fieldId = $field->getId();
            $index = http_getIntParameter("fld$fieldName", -1);
            if ( $uiField instanceof Pt_AutoNumber && $index == -1 ) {
                $fieldData[$fieldName] = $uiField->getNextNumber();
                $autoNumberFieldIds[] = $fieldId;
            } else if ( $index >= 0 && $index < $numData ) {
                $data = $arr[$index];
                $value = $uiField->getDataValue($data);     // Bug 39119
                if ( $value != null || ( $field instanceof Pt_FieldBoolean ) ) {
                    if ( $value instanceof Pt_FileUpload ) {
                        $value->setParentId($data->getId() . "." . $fieldId);
                    }
                    if ( $value instanceof Pt_DataObject ) {
                        $value = $value->__toString();
                    }
                    $fieldData[$fieldName] = $value;
                    if ( $uiField instanceof Pt_AutoNumber ) {
                        $autoNumberFieldIds[] = $fieldId;
                    }
                }
            }
        }
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.COUNT_OBJS_HAVE_BEEN_MERGED:INFO_AUDIT",
                'placeHolders' => [
                    [ 'name' => 'COUNT', 'value' => $numData ],
                    [ 'name' => 'OBJS', 'value' => $objDef->getPluralName() ],
                ],
            ],
            [
                'id'           => "IA.COUNT_OBJS_HAVE_BEEN_MERGED:INFO",
                'placeHolders' => [
                    [ 'name' => 'COUNT', 'value' => $numData ],
                    [ 'name' => 'OBJS', 'value' => util_encode($objDef->getPluralName()) ],
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessageAudit = $placeholderTokens[0];
        $infoMessage = GT($textMap, "IA.COUNT_OBJS_HAVE_BEEN_MERGED:INFO");

        $resData = $arr[0];
        $oldData = $resData->getFullFieldMap();

        $runner = new Pt_EventRunner(CTX_UI);
        $runner->setOldData($oldData);
        $debug = Pt_EventDebugger::getInstance();
        if ($debug != null) {
            $runner->setDebugger($debug);
        }

        XACT_BEGIN('PLATFORM');
        try {
            $uniqueValueData = new Pt_UniqueValueDataManager();
            foreach ( $autoNumberFieldIds as $fieldId ) {
                $uniqueValueData->deleteIndex($objDefId, -$fieldId);
            }
            Pt_DataObjectManager::update($resData, $fieldData, $runner);
            for ($k=1; $k<$numData; $k++) {
                $data = $arr[$k];
                Pt_CommentManager::merge($resData, $data);
                Pt_ActivityTrailManager::merge($resData, $data);
                Pt_DataObjectManager::delete($data, $runner);
            }

            Pt_ActivityTrailManager::createI18N($resData, $infoMessageAudit);
            XACT_COMMIT('PLATFORM');

        } catch (Exception $ex) {
            //I18N : TODO
            db_rollback($ex);
        }
        $runner->finalize();
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
        Pt_SelectedRecords::clearRecords($objDefId);

        return $resData;
    }

    /**
     * Run selected triggers.
     */
    public static function runTriggers() {
        $actionId = http_getIntParameter(FIELD_ACTION_ID);
        $a = Pt_ActionManager::getById($actionId);
        if ($a == null) {
            throw new Pt_I18nException('PAAS-0284', "Action with id $actionId not found", [ 'ACTIONID' => $actionId ]);
        }
        $newStatus = Pt_StatusManager::getById($a->getNextStatusId());

        $objDef = $a->getObjectDef();
        util_checkPermission($objDef, 'modify');
        
        // T 6499
        $pageData = null;
        $pageId = $a->getStatusPageId();
        $page = Pt_WebPageManager::getById($pageId);
        if ($page != null) {
            $pageData = $page->getAttributes([]);
        }

        $relFields = [];
        foreach ($objDef->getFields() as $field) {
            if ($field instanceof Pt_FieldRelationship) {
                /* @var Pt_Relationship $rel */
                $rel = $field->getUIField();
                if ($rel->isMultiple()) {
                    $relFields[$field->getFieldName()] = $field;
                }
            }
        }

        $triggers = [];
        foreach ($a->getTriggerIds() as $triggerId) {
            $trigger = Pt_EventDefManager::getById($triggerId);
            if ($trigger != null) {
                $triggers[] = $trigger;
            }
        }
        if (!$triggers) {
            throw new Pt_I18nException('PAAS-0285', "No triggers selected to run");
        }

        $ids = http_getIntArray(FIELD_IDS);
        if (!$ids) {
            throw new Pt_I18nException('PAAS-0286', "Cannot find object ids");
        }
        $objs = [];
        foreach ($ids as $id) {
            $data = Pt_DataObjectManager::getById($objDef, $id);
            if ($data == null) {
                continue;
            }
            $objs[] = $data;
        }

        $runner = new Pt_EventRunner(CTX_UI);
        $debug = Pt_EventDebugger::getInstance();
        if ($debug != null) {
            $runner->setDebugger($debug);
        }

        XACT_BEGIN('PLATFORM');
        try {
            foreach ($objs as $data) {
                if ($pageData != null) {    // T 6499
                    $fieldData = array_merge([], $data->getShortFieldMap());
                    foreach ($pageData as $key => $value) {
                        if ($relFields[$key] != null) {
                            $x = (is_array($fieldData[$key]) ? $fieldData[$key] : []);
                            $y = (is_array($value) ? $value : [$value]);
                            $fieldData[$key] = array_merge($y, $x);
                        } else if (isset($value)) {
                            if (is_array($value) && !$value) {
                                continue;
                            }
                            $fieldData[$key] = $value;
                        }
                    }
                    Pt_DataObjectManager::update($data, $fieldData, null);
                }

                $runner->runSelected($data, $triggers);

                if ($newStatus != null) {
                    Pt_DataObjectManager::changeStatus($data, $newStatus);
                }

                $runner->reset();
            }
            XACT_COMMIT('PLATFORM');

        } catch (Exception $ex) {
            //I18N : TODO
            db_rollback($ex);
        }
        $runner->finalize();
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.SELECTED_TRIGGERS_RAN_ON_COUNT_OBJS",
                'placeHolders' => [
                    [ 'name' => 'COUNT', 'value' => count($objs)],
                    [ 'name' => 'OBJS', 'value' => util_encode($objDef->getPluralName())]
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.SELECTED_TRIGGERS_RAN_ON_COUNT_OBJS");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
        Pt_SelectedRecords::clearRecords($objDef->getId());
    }

    /**
     * Delete data object.
     *
     * @return Pt_DataObject
     */
    public static function delete() {
        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $objDef = Pt_DataObjectDefManager::getById($objDefId);
        if ($objDef == null) {
            throw new Pt_I18nException('PAAS-0287', "Cannot find object definition");
        }
        util_checkPermission($objDef, 'delete');

        $id = http_getIntParameter(FIELD_ID);
        $data = Pt_DataObjectManager::getById($objDef, $id);
        if ($data == null) {
            return null;
        }

        $runner = new Pt_EventRunner(CTX_UI);
        $debug = Pt_EventDebugger::getInstance();
        if ($debug != null) {
            $runner->setDebugger($debug);
        }

        $oldData = $data->getFullFieldMap();
        $runner->setOldData($oldData);

        XACT_BEGIN('PLATFORM');
        try {
            Pt_DataObjectManager::delete($data, $runner);
            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            //I18N : TODO
            db_rollback($ex);
        }
        $runner->finalize();
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.OBJ_DATA_HAS_BEEN_DELETED",
                'placeHolders' => [
                    [ 'name' => 'OBJ', 'value' => $objDef->__toString()],
                    [ 'name' => 'DATA', 'value' => util_encode($data->__toString())]
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.OBJ_DATA_HAS_BEEN_DELETED");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);

        Pt_SelectedRecords::clearRecords($objDefId);
        return $data;
    }

    /**
     * Delete group of data objects.
     */
    public static function deleteArr() {
        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $objDef = Pt_DataObjectDefManager::getById($objDefId);
        if ($objDef == null) {
            throw new Pt_I18nException('PAAS-0288', "Cannot find object definition");
        }
        $returnId = http_getIntParameter(FIELD_RETURN_ID);
        util_checkPermission($objDef, 'delete');

        $ids = Pt_SelectedRecords::getRecords($objDefId, $returnId);
        $arr = [];
        $runner = new Pt_EventRunner(CTX_UI);

        XACT_BEGIN('PLATFORM');
        try {
            foreach ($ids as $id) {
                $data = Pt_DataObjectManager::getById($objDef, $id);
                if ($data == null) {
                    continue;
                }
                $oldData = $data->getFullFieldMap();
                $runner->setOldData($oldData);
                Pt_DataObjectManager::delete($data, $runner);
                $arr[] = $data;
                $runner->reset();
            }
            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            //I18N : TODO
            db_rollback($ex);
        }
        $runner->finalize();
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.COUNT_DATA_HAVE_BEEN_DELETED",
                'placeHolders' => [
                    [ 'name' => 'COUNT', 'value' => count($arr)],
                    [ 'name' => 'DATA', 'value' => $objDef->getPluralName()]
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.COUNT_DATA_HAVE_BEEN_DELETED");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);

        Pt_SelectedRecords::clearRecords($objDefId);
    }

    /**
     * Set "viewed" flag for a group of data objects.
     *
     * @param bool  $isViewed
     */
    public static function setViewedArr($isViewed)
    {
        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $objDef = Pt_DataObjectDefManager::getById($objDefId);
        if ($objDef == null) {
            throw new Pt_I18nException('PAAS-0289', "Cannot find object definition");
        }
        $returnId = http_getIntParameter(FIELD_RETURN_ID, null);

        $ids = Pt_SelectedRecords::getRecords($objDefId, $returnId);
        XACT_BEGIN('PLATFORM');
        try {
            Pt_FlagManager::setViewedArr($isViewed, $ids, GetMyUserid());
            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            //I18N : TODO
            db_rollback($ex);
        }

        if ($isViewed)
        {
            $tokens = [];
            $placeholderTokens = [
                [
                    'id'           => "IA.COUNT_OBJS_HAVE_BEEN_MARKED_AS_VIEWED",
                    'placeHolders' => [
                        [ 'name' => 'COUNT', 'value' => count($ids)],
                        [ 'name' => 'OBJS', 'value' => util_encode($objDef->getPluralName())]
                    ],
                ],
            ];
            $textMap = getIntlTextMap($tokens, $placeholderTokens);
            $infoMessage = GT($textMap, "IA.COUNT_OBJS_HAVE_BEEN_MARKED_AS_VIEWED");
        } else {
            $tokens = [];
            $placeholderTokens = [
                [
                    'id'           => "IA.COUNT_OBJS_HAVE_BEEN_MARKED_AS_UNVIEWED",
                    'placeHolders' => [
                        [ 'name' => 'COUNT', 'value' => count($ids)],
                        [ 'name' => 'OBJS', 'value' => util_encode($objDef->getPluralName())]
                    ],
                ],
            ];
            $textMap = getIntlTextMap($tokens, $placeholderTokens);
            $infoMessage = GT($textMap, "IA.COUNT_OBJS_HAVE_BEEN_MARKED_AS_UNVIEWED");
        }
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
    }

    /**
     * Set "flagged" flag for a group of data objects.
     *
     * @param bool  $isFlagged
     */
    public static function setFlaggedArr($isFlagged) {
        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $objDef = Pt_DataObjectDefManager::getById($objDefId);
        if ($objDef == null) {
            throw new Pt_I18nException('PAAS-0290', "Cannot find object definition");
        }
        $returnId = http_getIntParameter(FIELD_RETURN_ID);

        $ids = Pt_SelectedRecords::getRecords($objDefId, $returnId);
        XACT_BEGIN('PLATFORM');
        try {
            Pt_FlagManager::setFlaggedArr($isFlagged, $ids, GetMyUserid());
            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            //I18N : TODO
            db_rollback($ex);
        }

        if ($isFlagged){
            $tokens = [];
            $placeholderTokens = [
                [
                    'id'           => "IA.COUNT_OBJS_HAVE_BEEN_MARKED_AS_FLAGGED",
                    'placeHolders' => [
                        [ 'name' => 'COUNT', 'value' => count($ids)],
                        [ 'name' => 'OBJS', 'value' => util_encode($objDef->getPluralName())]
                    ],
                ],
            ];
            $textMap = getIntlTextMap($tokens, $placeholderTokens);
            $infoMessage = GT($textMap, "IA.COUNT_OBJS_HAVE_BEEN_MARKED_AS_FLAGGED");
        } else {
            $tokens = [];
            $placeholderTokens = [
                [
                    'id'           => "IA.COUNT_OBJS_HAVE_BEEN_MARKED_AS_UNFLAGGED",
                    'placeHolders' => [
                        [ 'name' => 'COUNT', 'value' => count($ids)],
                        [ 'name' => 'OBJS', 'value' => util_encode($objDef->getPluralName())]
                    ],
                ],
            ];
            $textMap = getIntlTextMap($tokens, $placeholderTokens);
            $infoMessage = GT($textMap, "IA.COUNT_OBJS_HAVE_BEEN_MARKED_AS_UNFLAGGED");
        }
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
    }

    /**
     * Remove attached file.
     */
    public static function removeFile()
    {
        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $objDef = Pt_DataObjectDefManager::getById($objDefId);
        if ($objDef == null) {
            throw new Pt_I18nException('PAAS-0291', "Cannot find object definition");
        }

        Globals::$g->gPtSecurity->checkPermission($objDef, [ 'modify', 'delete' ]);

        $id = http_getIntParameter(FIELD_ID);
        $data = Pt_DataObjectManager::getById($objDef, $id);
        if ($data == null) {
            throw new Pt_I18nException('PAAS-0292', "Cannot find object with id $id", [ 'ID' => $id ]);
        }

        $fieldName = http_getParameter(FIELD_NAME);
        $field = $data->getObjectDef()->getByName($fieldName);
        if ($field == null) {
            throw new Pt_I18nException('PAAS-0293', "Incorrect field name provided");
        }
        if (!($field instanceof Pt_FieldFile)) {
            throw new Pt_I18nException('PAAS-0294', "Incorrect field name provided");
        }

        XACT_BEGIN('PLATFORM');
        try {
            Pt_DataFieldManager::setField($data, $field, null);
            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            //I18N : TODO
            db_rollback($ex);
        }
    }

    /**
     * Get filters for parameters search.
     *
     * @return Pt_FilterList
     */
    public static function getFilters()
    {
        $filters = new Pt_FilterList();

        for ( $k = 0; $k < MAX_FILTERS; $k++ ) {
            $fieldId = http_getIntParameter("field" . $k);
            if ( $fieldId <= 0 ) {
                continue;
            }
            $fieldName = FIELD_OP_CODE . $k;
            $opCode = http_getParameter($fieldName);
            $opValue = http_getParameter(FIELD_OP_VALUE . $k);

            $fieldDef = Pt_DataFieldDefManager::getById($fieldId);
            if ( $fieldDef instanceof Pt_FieldDateTime && $fieldDef->isGmt() && $opValue != null
                 && !Pt_Filter::hasToken($opValue) ) {
                if ( is_string($opValue) && strlen($opValue) <= 10 ) {
                    $opValue .= ' 00:00:00';
                }
                $opValue = ReformatTimestamp($opValue, GetUserDateFormat(), GetUserTimeFormat(), IADATE_STDFORMAT, IATIME_STDFORMAT);
                $opValue = UserTZToGMT(convert8601ToLocalTimestamp($opValue));
            }

            $filter = Pt_Filter::getInstance($fieldId, $opCode, $opValue, $k);
            if ( $filter != null ) {
                $filters->add($filter);
            }
        }
        $filters->setJoinType(http_getIntParameter(FIELD_JOIN_TYPE, JOIN_AND));
        $filters->setExpression(http_getParameter(FIELD_EXPRESSION));

        return $filters;
    }

    /**
     * Populate default values for the object except auto-number fields
     *
     * @param Pt_DataObjectDef $objDef
     * @param array            $fieldData
     * @param bool             $isAPI
     *
     */
    public static function populateAutoFields(Pt_DataObjectDef $objDef, & $fieldData,
        /** @noinspection PhpUnusedParameterInspection */ $isAPI = false) {
        if ($objDef->isWorkflow()) {
            $processId = intval($fieldData[FIELD_PROCESS]);
            $p = Pt_ProcessManager::getById($processId);
            if ($p == null || $p->getObjectDefId() != $objDef->getId()) {
                $p = $objDef->getDefaultProcess();
                if ($p != null) {
                    $fieldData[FIELD_PROCESS] = $p->getId();
                    if ($fieldData[FIELD_STATUS] == null) {
                        $fieldData[FIELD_STATUS] = $p->getDefaultStatusId();
                    }
                }
            }
            $statusId = intval($fieldData[FIELD_STATUS]);
            $s = Pt_StatusManager::getById($statusId);
            if (($s == null || $s->getObjectDefId() != $objDef->getId()) && $p != null) {
                $fieldData[FIELD_STATUS] = $p->getDefaultStatusId();
            }
        }

        //  Go through the fields, finding any that are null.  If null, call that classes getDefaultValue so
        //   it gets a chance to set the default value.
        // Ignore auto-number fields, they must be populated by Pt_ObjectController::populateAutoNumberFields
        // since, in most cases, we want to populate auto-number fields as late as possible to avoid burning
        // unused numbers.
        foreach ($objDef->getFields() as $field) {
            $fieldName = $field->getFieldName();
            $uiField = $field->getUIField();
            if ( !isset($fieldData[$fieldName]) && ! $uiField instanceof Pt_AutoNumber ) {
                // Pass true for isAPI so that date fields default using internal, rather than
                // user date format.
                $fieldData[$fieldName] = $uiField->getDefaultValue(true, $fieldData, true, false);
            }
        }
    }

    /**
     * unconditionally populate all the auto-number fields
     *
     * @param Pt_DataObjectDef $objDef       the object metadata
     * @param array            &$fieldData   the object values array
     */
    public static function populateAutoNumberFields(Pt_DataObjectDef $objDef, & $fieldData)
    {

        foreach ($objDef->getFields() as $field) {
            $fieldName = $field->getFieldName();
            $uiField = $field->getUIField();
            if ( $uiField instanceof Pt_AutoNumber ) {
                // Pass true for isAPI so that date fields default using internal, rather than
                // user date format.
                $fieldData[$fieldName] = $uiField->getDefaultValue(true, $fieldData, true);
            }
        }
    }

    const VALIDATE_ALL_FIELDS = 0;
    const VALIDATE_SKIP_AUTONUMBER = 1;
    const VALIDATE_ONLY_AUTONUMBER = 2;

    /**
     * Validate all the fields.  This includes executing validation scripts as well as checking for required values
     *
     * @param Pt_DataObjectDef $objDef       the object metadata
     * @param array            &$fieldData   the object values array
     * @param int              $strategy     one of the VALIDATE_*_ constants that indicate how we should
     *                                       process auto-number fields
     */
    public static function validateFields(Pt_DataObjectDef $objDef, $fieldData, $strategy)
    {
        foreach ($objDef->getFields() as $field) {
            $isAutoNum = $field instanceof Pt_FieldAuto;
            if (      $strategy == self::VALIDATE_ALL_FIELDS
                 || ( $strategy == self::VALIDATE_SKIP_AUTONUMBER && ! $isAutoNum )
                 || ( $strategy == self::VALIDATE_ONLY_AUTONUMBER && $isAutoNum )
            ) {
                $field->getUIField()->validateAPI($fieldData);
            }
            
            //if ($field != null && $field->isRequired() && $fieldData[$field->getFieldName()] == null)
            //    throw new Exception("Field \"$field\" must have a value");
        }
    }

    /**
     * Verify that all unique fields are
     *
     * @param Pt_DataObjectDef     $objDef      the data object
     * @param array                $fieldData   values for all fields in the record
     * @param int                  $recordId    the id of the record being validated
     * @param Pt_ValidationErrors  $errs        a place to put any errors
     */
    public static function validateUniqueFields(Pt_DataObjectDef $objDef, $fieldData, $recordId, $errs)
    {
        foreach ($objDef->getFields() as $field) {
            try {
                $field->getUIField()->validateUniqueness($fieldData, $recordId);
            } catch ( Exception $ex ) {
                //I18N : TODO
                $errs->setError($field->getFieldName(), $ex->getMessage());
            }
        }
    }

}
