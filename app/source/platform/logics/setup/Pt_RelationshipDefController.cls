<?
/**
 * Manages HTTP requests to create/modify relationship definitions.
 */
require_once('Pt_Includes.inc');

require_once("Pt_RelationshipDefManager.cls");

class Pt_RelationshipDefController {

    /**
     * Create new definition for relationships between two different object types.
     */
    public static function create() {
        $objDefId1 = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $objDefId2 = http_getIntParameter(FIELD_OBJ_DEF_ID2);

        $relName = http_getParameter(FIELD_REL_NAME);
        $customLabels = Pt_I18nLabelsController::getIdAndLabels(Pt_RelationshipDef::CUSTOM_LABELS_INDEXES);
        $singularName1 = http_getParameter(FIELD_SINGULAR_NAME1);
        $pluralName1 = http_getParameter(FIELD_PLURAL_NAME1);
        $name1 = http_getParameter(FIELD_NAME.'1');
        $singularName2 = http_getParameter(FIELD_SINGULAR_NAME2);
        $pluralName2 = http_getParameter(FIELD_PLURAL_NAME2);
        $name2 = http_getParameter(FIELD_NAME.'2');

        $cardinality = http_getIntParameter(FIELD_MULTIPLE);

        $orphanControl1 = http_isBoxChecked(FIELD_ORPHAN1);
        $orphanControl2 = http_isBoxChecked(FIELD_ORPHAN2);
        $cloneRelated1 = http_isBoxChecked(FIELD_CLONE1);
        $cloneRelated2 = http_isBoxChecked(FIELD_CLONE2);
        $delRec1 = http_isBoxChecked(FIELD_DEL_REC1);
        $delRec2 = http_isBoxChecked(FIELD_DEL_REC2);

        $lookupFieldProps1 = [
            FIELD_IS_AUDITABLE => http_isBoxChecked(FIELD_IS_AUDITABLE.'1'),
            FIELD_IS_REQUIRED => http_isBoxChecked(FIELD_IS_REQUIRED.'1'),
            FIELD_IS_SEARCHABLE => http_isBoxChecked(FIELD_IS_SEARCHABLE.'1'),
            FIELD_IS_TABLE_COLUMN => http_isBoxChecked(FIELD_IS_TABLE_COLUMN.'1'),
            FIELD_IS_TEMPLATE_FIELD => http_isBoxChecked(FIELD_IS_TEMPLATE_FIELD.'1'),
            FIELD_NO_CLONE => http_isBoxChecked(FIELD_NO_CLONE.'1'),
        ];
        $lookupFieldProps2 = [
            FIELD_IS_AUDITABLE => http_isBoxChecked(FIELD_IS_AUDITABLE.'2'),
            FIELD_IS_REQUIRED => http_isBoxChecked(FIELD_IS_REQUIRED.'2'),
            FIELD_IS_SEARCHABLE => http_isBoxChecked(FIELD_IS_SEARCHABLE.'2'),
            FIELD_IS_TABLE_COLUMN => http_isBoxChecked(FIELD_IS_TABLE_COLUMN.'2'),
            FIELD_IS_TEMPLATE_FIELD => http_isBoxChecked(FIELD_IS_TEMPLATE_FIELD.'2'),
            FIELD_NO_CLONE => http_isBoxChecked(FIELD_NO_CLONE.'2'),
        ];


        //if (http_isBoxChecked('field1_'.$stub->getId()))
        $field1s = http_getParametersStartingWith('field1_');
        //eppp($field1s);

        $field1_IDs = [];
        foreach($field1s as $field1) {
            if(($field1 != 'field1_All') && http_isBoxChecked($field1)) {
                $field1_IDs[] = substr($field1, strlen('field1_'), strlen($field1)-1);
            }
        }
        //eppp($field1_IDs);

        //if (http_isBoxChecked('field2_'.$stub->getId()))
        $field2s = http_getParametersStartingWith('field2_');
        //eppp($field2s);

        $field2_IDs = [];
        foreach($field2s as $field2) {
            if(($field2 != 'field2_All') && http_isBoxChecked($field2)) {
                $field2_IDs[] = substr($field2, strlen('field2_'), strlen($field2)-1);
            }
        }
        //eppp($field2_IDs);

        //if ($stub->getPageType() != TYPE_VIEW || !http_isBoxChecked('table1_'.$stub->getId()))
        $table1s = http_getParametersStartingWith('table1_');
        //eppp($table1s);

        $table1_IDs = [];
        foreach($table1s as $table1) {
            if(($table1 != 'table1_All') && http_isBoxChecked($table1)) {
                $table1_IDs[] = substr($table1, strlen('table1_'), strlen($table1)-1);
            }
        }
        //eppp($table1_IDs);

        //if ($stub->getPageType() != TYPE_VIEW || !http_isBoxChecked('table2_'.$stub->getId()))
        $table2s = http_getParametersStartingWith('table2_');
        //eppp($table2s);

        $table2_IDs = [];
        foreach($table2s as $table2) {
            if(($table2 != 'table2_All') && http_isBoxChecked($table2)) {
                $table2_IDs[] = substr($table2, strlen('table2_'), strlen($table2)-1);
            }
        }
        //eppp($table2_IDs);
        //dieFL();

        XACT_BEGIN('PLATFORM');
        try {
            self::createAPI($objDefId1, $objDefId2, $relName, $customLabels, $singularName1, $pluralName1, $name1,
                            $singularName2, $pluralName2, $name2, $cardinality, $orphanControl1, $orphanControl2,
                            $cloneRelated1, $cloneRelated2, $delRec1, $delRec2, $field1_IDs, $field2_IDs, $table1_IDs,
                            $table2_IDs, false, false, $lookupFieldProps1, $lookupFieldProps2, '');
            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            //I18N : TODO
            db_rollback($ex);
        }
    }

    /**
     * Create new definition for relationships between two different object types.
     *
     * @param int             $objDefId1
     * @param int             $objDefId2
     * @param string          $relName
     * @param Pt_I18nLabels[] $customLabels
     * @param string          $singularName1
     * @param string          $pluralName1
     * @param string          $name1
     * @param string          $singularName2
     * @param string          $pluralName2
     * @param string          $name2
     * @param int             $cardinality
     * @param bool            $orphanControl1
     * @param bool            $orphanControl2
     * @param bool            $cloneRelated1
     * @param bool            $cloneRelated2
     * @param bool            $delRec1
     * @param bool            $delRec2
     * @param int[]           $field1_IDs
     * @param int[]           $field2_IDs
     * @param int[]           $table1_IDs
     * @param int[]           $table2_IDs
     * @param bool            $isSystem
     * @param bool            $isGLDimension
     * @param array           $lookupFieldProps1
     * @param array           $lookupFieldProps2
     * @param string          $appOrigId
     * @param bool            $validateNamesSingularity
     *
     * @throws Exception|Pt_I18nException
     */
    public static function createAPI($objDefId1, $objDefId2, $relName, $customLabels, $singularName1, $pluralName1, $name1,
            $singularName2, $pluralName2, $name2, $cardinality, $orphanControl1, $orphanControl2, $cloneRelated1,
            $cloneRelated2, $delRec1, $delRec2, $field1_IDs, $field2_IDs, $table1_IDs, $table2_IDs,
        /** @noinspection PhpUnusedParameterInspection */ $isSystem,
            $isGLDimension, $lookupFieldProps1, $lookupFieldProps2, $appOrigId, $validateNamesSingularity = true) {
        Pt_RelationshipDef::validate($objDefId1, $objDefId2, $customLabels);

        $objDef1 = Pt_DataObjectDefManager::getById($objDefId1);
        $objDef2 = Pt_DataObjectDefManager::getById($objDefId2);

        if ( $validateNamesSingularity ) {
            if ( $relName && Pt_RelationshipDefManager::getByName($relName) ) {
                throw new Pt_I18nException('PAAS-0875',
                                           "Relationship def with integration name \"$relName\" already exists",
                                           [ 'FIELDNAME' => "$relName" ]);
            }
            if ( $relName ) {
                self::validateUniqueRelFields($relName, $objDefId1, $objDefId2);
            }
            if ( Pt_DataFieldDefManager::getFromObjIdByName($objDefId2, $name1) ) {
                throw new Pt_I18nException('PAAS-0870', "Field name \"$name1\" is already in use.",
                                           [ 'FIELDNAME' => "$name1" ]);
            }
            if ( Pt_DataFieldDefManager::getFromObjIdByName($objDefId1, $name2) ) {
                throw new Pt_I18nException('PAAS-0871', "Field name \"$name2\" is already in use.",
                                           [ 'FIELDNAME' => "$name2" ]);
            }
            Pt_FieldDefController::validateUniqueFieldRels($name1, $objDef2);
            Pt_FieldDefController::validateUniqueFieldRels($name2, $objDef1);
        }

        $isMultiple1 = ($cardinality==2) || ($cardinality==3);
        $isMultiple2 = ($cardinality==1) || ($cardinality==3);

        $rel = Pt_RelationshipDefManager::create(-1, $relName, $objDefId1, $objDefId2, $customLabels, $singularName1, $pluralName1,
                                                 $singularName2, $pluralName2, $isMultiple1, $isMultiple2,
                                                 $orphanControl1, $orphanControl2, $cloneRelated1, $cloneRelated2,
                                                 false, $delRec1, $delRec2, $appOrigId, $isGLDimension);

        $relProps = $rel->getProperties();
        if (!is_array($relProps)) {
            $relProps = [];
        }

        $newRelationshipRedesign = false;
        if ( Pt_RelationshipManagerChoose::newRelationshipRedesignWriteBothPlaces($rel)
            || Pt_RelationshipManagerChoose::newRelationshipRedesignReadNew($rel) ) {
            $newRelationshipRedesign = true;
        }

        if ( !$newRelationshipRedesign ) {
            $createCustomField1 = false;
            $createCustomField2 = false;
        } else {
            $createCustomField1 = !$isMultiple2 && !$objDef1->isPlatform();
            $createCustomField2 = !$isMultiple1 && !$objDef2->isPlatform();
        }

        if ( $rel->isGLDimension() ) {
            $type = CUSTOMERP_RELATIONSHIPFIELDTYPE_DIMENSION;
        } else {
            $type = CUSTOMERP_RELATIONSHIPFIELDTYPE;
        }

        // Create related lookup fields
        if ( $isMultiple1 ) {
            $displayName1 = $singularName2;
            $field1CustomLabels =
                Pt_DataFieldDef::initializeLabels($customLabels, Pt_RelationshipDef::SINGULAR2_LABELS_IDX,
                                                     Pt_DataFieldDef::DISPLAY_LABEL_IDX);
        } else {
            $displayName1 = $pluralName2;
            $field1CustomLabels =
                Pt_DataFieldDef::initializeLabels($customLabels, Pt_RelationshipDef::PLURAL2_LABELS_IDX,
                                                     Pt_DataFieldDef::DISPLAY_LABEL_IDX);
        }
        $field1 = self::createRelatedFieldLookup($objDef1, $rel, $type, $field1CustomLabels, $displayName1, $name2,
                                                 $lookupFieldProps2, $createCustomField1);
        $pages1 = Pt_WebPageManager::getByObjectDef($objDef1->getId());
        $relProps[FIELD_FIELD_ID1] = $field1->getId();

        if ( $isMultiple2 ) {
            $displayName2 = $singularName1;
            $field2CustomLabels =
                Pt_DataFieldDef::initializeLabels($customLabels, Pt_RelationshipDef::SINGULAR1_LABELS_IDX,
                                                     Pt_DataFieldDef::DISPLAY_LABEL_IDX);
        } else {
            $displayName2 = $pluralName1;
            $field2CustomLabels =
                Pt_DataFieldDef::initializeLabels($customLabels, Pt_RelationshipDef::PLURAL1_LABELS_IDX,
                                                     Pt_DataFieldDef::DISPLAY_LABEL_IDX);
        }
        $field2 = self::createRelatedFieldLookup($objDef2, $rel, $type, $field2CustomLabels, $displayName2, $name1,
                                                 $lookupFieldProps1, $createCustomField2);
        $pages2 = Pt_WebPageManager::getByObjectDef($objDef2->getId());
        $relProps[FIELD_FIELD_ID2] = $field2->getId();

        // Store ids of created fields into props.
        Pt_RelationshipDefManager::setProperties($rel, $relProps);

        /** @noinspection PhpUndefinedVariableInspection */
        foreach ( $pages1 as $stub) {
            //if (http_isBoxChecked('field1_'.$stub->getId()))
            if (in_array($stub->getId(), $field1_IDs)) {
                /** @noinspection PhpUndefinedVariableInspection */
                self::addRelatedField($stub->getWebPage(), $field1, $appOrigId);
            }
        }

        foreach ($pages2 as $stub) {
            //if (http_isBoxChecked('field2_'.$stub->getId()))
            if (in_array($stub->getId(), $field2_IDs)) {
                self::addRelatedField($stub->getWebPage(), $field2, $appOrigId);
            }
        }

        if ($isMultiple2) {
            foreach ($pages1 as $stub) {
                //if ($stub->getPageType() != TYPE_VIEW || !http_isBoxChecked('table1_'.$stub->getId()))
                if ($stub->getPageType() != TYPE_VIEW || !in_array($stub->getId(), $table1_IDs)) {
                    continue;
                }
                self::createRelatedList($stub->getWebPage(), $objDef2, $rel, $pluralName2, $appOrigId);
            }
        }

        if ($isMultiple1) {
            foreach ($pages2 as $stub) {
                //if ($stub->getPageType() != TYPE_VIEW || !http_isBoxChecked('table2_'.$stub->getId()))
                if ($stub->getPageType() != TYPE_VIEW || !in_array($stub->getId(), $table2_IDs)) {
                    continue;
                }
                self::createRelatedList($stub->getWebPage(), $objDef1, $rel, $pluralName1, $appOrigId);
            }
        }
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.A_RELATIONSHIP_BETWEEN_SRC_AND_DST_HAS_BEEN_CRE",
                'placeHolders' => [
                    [ 'name' => 'NAME_SRC', 'value' => util_encode($singularName1) ],
                    [ 'name' => 'NAME_DST', 'value' => util_encode($singularName2) ]
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.A_RELATIONSHIP_BETWEEN_SRC_AND_DST_HAS_BEEN_CRE");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
    }

    /**
     * Create new definition for relationships between the same object types (hierarchy).
     */
    public static function create2() {
        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $objDef = Pt_DataObjectDefManager::getById($objDefId);
        if ($objDef == null) {
            throw new Pt_I18nException('PAAS-0405', "Object definition with id $objDefId not found",
                                       [ 'OBJDEFID' => $objDefId ]);
        }

        $relName = http_getParameter(FIELD_REL_NAME);
        if ( $relName && Pt_RelationshipDefManager::getByName($relName) ) {
            throw new Pt_I18nException('PAAS-0876',
                                       "Relationship def with integration name \"$relName\" already exists",
                                       [ 'FIELDNAME' => "$relName" ]);
        }
        $customLabels = Pt_I18nLabelsController::getIdAndLabels(Pt_RelationshipDef::CUSTOM_LABELS_INDEXES);
        $singularName1 = http_getParameter(FIELD_SINGULAR_NAME1);
        $pluralName1 = http_getParameter(FIELD_PLURAL_NAME1);
        $singularName2 = http_getParameter(FIELD_SINGULAR_NAME2);
        $pluralName2 = http_getParameter(FIELD_PLURAL_NAME2);

        $isMultiple1 = false;
        $isMultiple2 = true;

        XACT_BEGIN('PLATFORM');
        try {
            $rel = Pt_RelationshipDefManager::create(-1, $relName, $objDefId, $objDefId, $customLabels, $singularName1,
                                                     $pluralName1, $singularName2, $pluralName2, $isMultiple1,
                                                     $isMultiple2, false, false, false, false, false, false, false, '');

            $pageStubs = Pt_WebPageManager::getByObjectDef($objDef->getId());
            $dataClassName = "Pt_FieldRelationship";
            $uiClassName = "Pt_Relationship";

            $props1 = [];
            $props1[FIELD_OBJ_DEF_ID2] = $objDef->getId();
            $props1[FIELD_REL_ID] = $rel->getId();
            $props1[FIELD_IS_PARENT] = true;
            $props1[FIELD_IS_SEARCHABLE] = true;
            $props1[FIELD_IS_TABLE_COLUMN] = true;
            $props1[FIELD_IS_TEMPLATE_FIELD] = true;

            $columnName1 = $objDef->getNewColumnName($dataClassName);
            $fieldName1 = Pt_DataFieldDefManager::getUniqueName($objDef, 'P'.$objDef->getObjectDefName());  // Bug 39321
            
            $field1CustomLabels =
                Pt_DataFieldDef::initializeLabels($customLabels, Pt_RelationshipDef::SINGULAR1_LABELS_IDX,
                                                     Pt_DataFieldDef::DISPLAY_LABEL_IDX);
            $field1 = Pt_DataFieldDefManager::create(0, $objDef, $dataClassName, $uiClassName,
                $columnName1, $fieldName1, null, $field1CustomLabels, $singularName1, null, $props1);
            foreach ($pageStubs as $stub) {
                if (http_isBoxChecked('field1_'.$stub->getId())) {
                    self::addRelatedField($stub->getWebPage(), $field1, '');
                }
            }

            $props2 = util_arrayClone($props1);
            $props2[FIELD_IS_PARENT] = false;

            $columnName2 = $objDef->getNewColumnName($dataClassName);
            $fieldName2 = Pt_DataFieldDefManager::getUniqueName($objDef, 'C'.$objDef->getObjectDefName());  // Bug 39321

            $field2CustomLabels =
                Pt_DataFieldDef::initializeLabels($customLabels, Pt_RelationshipDef::PLURAL2_LABELS_IDX,
                                                     Pt_DataFieldDef::DISPLAY_LABEL_IDX);
            $field2 = Pt_DataFieldDefManager::create(0, $objDef, $dataClassName, $uiClassName,
                $columnName2, $fieldName2, null, $field2CustomLabels, $pluralName2, null, $props2);
            foreach ($pageStubs as $stub) {
                if (http_isBoxChecked('field2_'.$stub->getId())) {
                    self::addRelatedField($stub->getWebPage(), $field2, '');
                }
            }

            foreach ($pageStubs as $stub) {
                if ($stub->getPageType() != TYPE_VIEW || !http_isBoxChecked('table1_'.$stub->getId())) {
                    continue;
                }
                self::createRelatedList($stub->getWebPage(), $objDef, $rel, $pluralName2, '');
            }

            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            //I18N : TODO
            db_rollback($ex);
        }
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.HIERARCHY_OF_OBJ_HAS_BEEN_CREATED",
                'placeHolders' => [
                    [ 'name' => 'OBJ', 'value' => util_encode($objDef) ],
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.HIERARCHY_OF_OBJ_HAS_BEEN_CREATED");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
    }

    /**
     * Update existing relationship definition.
     */
    public static function update() {
        $id = http_getIntParameter(FIELD_ID);
        $rel = Pt_RelationshipDefManager::getById($id);
        if ($rel == null) {
            throw new Pt_I18nException('PAAS-0406', "Relationship definition with id $id not found", [ 'ID' => $id ]);
        }

        if ( $rel->isGLDimension() ) {
            throw new Pt_I18nException('PAAS-0407', "GL Dimension relationship cannot be changed");
        }
        
        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $objDef = Pt_DataObjectDefManager::getById($objDefId);
        if ($objDef == null) {
            throw new Pt_I18nException('PAAS-0408', "Object definition with id $objDefId not found",
                                       [ 'OBJDEFID' => $objDefId ]);
        }

        $relName = http_getParameter(FIELD_REL_NAME);
        if ( $relName && $relName != $rel->getRelationshipName() && Pt_RelationshipDefManager::getByName($relName) ) {
            throw new Pt_I18nException('PAAS-0877',
                                       "Relationship def with integration name \"$relName\" already exists",
                                       [ 'FIELDNAME' => "$relName" ]);
        }
        $objDefId2 = $rel->getOtherId($objDefId);
        self::validateUniqueRelFields($relName, $objDefId, $objDefId2, $rel);

        $customLabels = Pt_I18nLabelsController::getIdAndLabels(Pt_RelationshipDef::CUSTOM_LABELS_INDEXES);
        $singularName1 = http_getParameter(FIELD_SINGULAR_NAME1);
        $pluralName1 = http_getParameter(FIELD_PLURAL_NAME1);
        $singularName2 = http_getParameter(FIELD_SINGULAR_NAME2);
        $pluralName2 = http_getParameter(FIELD_PLURAL_NAME2);

        if ( !$rel->isHierarchy() ) {
            $cardinality = http_getIntParameter(FIELD_MULTIPLE);
            $isMultiple1 = ( $cardinality == 2 ) || ( $cardinality == 3 );
            $isMultiple2 = ( $cardinality == 1 ) || ( $cardinality == 3 );
        } else {
            $isMultiple1 = $rel->isMultiple1();
            $isMultiple2 = $rel->isMultiple2();
        }

        $orphanControl1 = http_isBoxChecked(FIELD_ORPHAN1);
        $orphanControl2 = http_isBoxChecked(FIELD_ORPHAN2);
        $cloneRelated1 = http_isBoxChecked(FIELD_CLONE1);
        $cloneRelated2 = http_isBoxChecked(FIELD_CLONE2);
        $delRec1 = http_isBoxChecked(FIELD_DEL_REC1);
        $delRec2 = http_isBoxChecked(FIELD_DEL_REC2);

        XACT_BEGIN('PLATFORM');
        try {
            Pt_RelationshipDefManager::update($rel, $relName, $customLabels, $singularName1, $pluralName1, $singularName2,
                                              $pluralName2, $isMultiple1, $isMultiple2, $orphanControl1,
                                              $orphanControl2, $cloneRelated1, $cloneRelated2, $delRec1, $delRec2, '');
            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            //I18N: TODO
            db_rollback($ex);
        }
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.A_RELATIONSHIP_BETWEEN_SRC_AND_DST_HAS_BEEN_UP",
                'placeHolders' => [
                    [ 'name' => 'NAME_SRC', 'value' => util_encode($singularName1) ],
                    [ 'name' => 'NAME_DST', 'value' => util_encode($singularName2) ]
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.A_RELATIONSHIP_BETWEEN_SRC_AND_DST_HAS_BEEN_UP");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
    }

    /**
     * Delete existing relationship definition.
     */
    public static function delete() {
        $id = http_getIntParameter(FIELD_ID);
        $rel = Pt_RelationshipDefManager::getById($id);
        if ($rel == null) {
            return;
        }

        //eppp("rel->getRelationshipName() : ".$rel->getRelationshipName());
        if($rel->isGLDimension()) {
            throw new Pt_I18nException('PAAS-0409', "System generated GL Dimension relationship cannot be deleted");
        }

        XACT_BEGIN('PLATFORM');
        try {
            Pt_RelationshipDefManager::delete($rel);
            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            //I18N : TODO
            db_rollback($ex);
        }
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.A_RELATIONSHIP_BETWEEN_SRC_AND_DST_HAS_BEEN_DEL",
                'placeHolders' => [
                    [ 'name' => 'NAME_SRC', 'value' => util_encode($rel->getSingularName1()) ],
                    [ 'name' => 'NAME_DST', 'value' => $rel->getSingularName2() ]
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.A_RELATIONSHIP_BETWEEN_SRC_AND_DST_HAS_BEEN_DEL");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
    }

    /**
     * Add related field to  Web Page
     *
     * @param Pt_WebPage      $page
     * @param Pt_DataFieldDef $relField
     * @param string          $appOrigId
     */
    private static function addRelatedField(Pt_WebPage $page, Pt_DataFieldDef $relField, $appOrigId) {
        $section = $page->getDefaultSection();
        if ($section == null) {
            return;
        }
        Pt_PageCellManager::createCell($section, "Pt_FieldWrapper", $appOrigId, $relField->getId(),
                                       $section->getDefaultAlignment());
        Pt_Cache::setWebPage($page);
    }

    /**
     * Create RelatedList call for  Web Page
     *
     * @param Pt_WebPage         $page
     * @param Pt_DataObjectDef   $tableObjDef
     * @param Pt_RelationshipDef $rel
     * @param string             $presentationName
     * @param string             $appOrigId
     */
    public static function createRelatedList(Pt_WebPage $page,
            Pt_DataObjectDef $tableObjDef, Pt_RelationshipDef $rel, $presentationName, $appOrigId)
    {
        $section = Pt_PageSectionManager::createSection($page, $presentationName, BORDER_THICK, ONE_COLUMN, true, false,
                                                        $appOrigId, false, $page->enableTabs() ? 0 : -1);

        $props = [];
        $props[FIELD_OBJ_DEF_ID2] = ($tableObjDef==null ? 0 : $tableObjDef->getId());
        $props[FIELD_REL_ID] = $rel->getId();
        $props[FIELD_SHOW_ATTACH_LINK] = true;
        $props[FIELD_SHOW_DETACH_LINK] = true;
        $props[FIELD_SHOW_VIEW_SELECTOR] = true;
        $props[FIELD_ORDER_VIEW_SELECTOR] = false;
        $props[FIELD_SHOW_NEW_LINK] = true;
        $props[FIELD_SHOW_QUICK_CREATE] = true;
        $props[FIELD_SHOW_CHECK_BOXES] = true;
        /** @noinspection PhpUnusedLocalVariableInspection */
        $cell =
            Pt_PageCellManager::createCell($section, "Pt_RelatedList", $appOrigId, 0, ALIGNMENT_LEFT, $presentationName,
                                           $props);

        if ($tableObjDef==null) {
            return;
        }
        $listDefs = Pt_ListDefManager::getByObjectDef($tableObjDef->getId());
        if (!$listDefs) {
            $tokens = [];
            $placeholderTokens = [
                [
                    'id'           => "IA.ALL_NAMES",
                    'placeHolders' => [
                        [ 'name' => 'NAMES', 'value' => $presentationName],
                    ],
                ],
            ];
            $textMap = getIntlTextMap($tokens, $placeholderTokens);
            $viewName = GT($textMap, "IA.ALL_NAMES");
            $columnIds = [
                $tableObjDef->getIdByName(FIELD_NAME),
                $tableObjDef->getIdByName(FIELD_UPDATED_AT)
            ];
            $props = [];
            $props[FIELD_COLUMNS] = util_converge($columnIds);
            $props[FIELD_DEF_SORT_COL] = $columnIds[1];
            $props[FIELD_DEF_SORT_ASC] = false;
            $props[FIELD_SHOW_ACTIONS] = true;
            Pt_ListDefManager::create(null, $tableObjDef->getId(), 0,
                                      Pt_ListDefManager::getCustomLabelsDefaults($viewName), $viewName, $props,
                                      $appOrigId);
        }

        Pt_Cache::setWebPage($page);
    }

    /**
     * @param Pt_DataObjectDef   $objDef
     * @param Pt_RelationshipDef $rel
     * @param string             $type
     * @param Pt_I18nLabels[]    $customLabels
     * @param string             $displayName
     * @param string             $name
     * @param string[]           $lookupFieldProps
     * @param bool               $createCustomField
     *
     * @return Pt_DataFieldDef
     */
    public static function createRelatedFieldLookup(Pt_DataObjectDef $objDef, Pt_RelationshipDef $rel, $type,
                                                                     $customLabels, $displayName, $name,
                                                                     $lookupFieldProps, $createCustomField)
    {
        if ( $createCustomField ) {
            $lookupFieldProps['columnName'] =
                self::createRelatedCustomField($objDef, $rel, $type, $customLabels, $displayName, $name);
        }
        
        $result = Pt_DataFieldDefManager::createRelatedField($objDef, $rel, $name, $customLabels, $displayName,
                                                             $lookupFieldProps);
        
        if ( $createCustomField ) {
            InValidateSession(ProfileHandler::COMPANY_CACHE_FLAG, '', GetMyCompany());
            Reporting::refreshCRWDataModel();

            if ( $objDef instanceof Pt_StdDataObjectDef ) {
                Pt_Cache::updateStandardObjectDef($objDef->getId(), $objDef->getObjectDefName(), $objDef->getDocType());
            }
        }
        
        return $result;
    }
    
    /**
     * @param Pt_DataObjectDef   $objDef
     * @param Pt_RelationshipDef $rel
     * @param string             $type
     * @param Pt_I18nLabels[]    $customLabels
     * @param string             $displayName
     * @param string             $name
     *
     * @return string
     * @throws Pt_I18nException
     */
    public static function createRelatedCustomField(Pt_DataObjectDef $objDef, Pt_RelationshipDef $rel, $type,
                                                    array            $customLabels, $displayName, $name)
    {
        $objectFieldId = null;
        if ( $objDef instanceof Pt_StdDataObjectDef ) {
            $docType = $objDef->getDocType();
            $entity = $objDef->getEntityName();
            $values['OWNEROBJECT'] = $entity;
            $values['DOCTYPE'] = $docType;
            $values['TYPE'] = $type;
            $values[CUSTOMERP_RELATIONSHIPFIELDTYPE . 'LABEL'] = strtoupper($displayName);
            $values[CUSTOMERP_RELATIONSHIPFIELDTYPE . 'CUSTOMFIELDID'] = strtoupper($name);
            $values['CUSTOMFIELDID'] = strtoupper($name);
            $description = util_arrayToText([ 'relatedObjDefId' => $rel->getOtherId($objDef->getId()),
                                              'isMultiple1'     => (int) $rel->isMultiple1(),
                                              'isMultiple2'     => (int) $rel->isMultiple2() ]);
            $values['DESCRIPTION'] = $description;
            $values['DATADESCRIPTION'] = '';
            $values['REQUIRED'] = '';
            $values['STATUS'] = 'active';
            $entityMgr = Globals::$g->gManagerFactory->getManager('customfield');
            $values['RECORDNO'] = db_nextId('CUSTOMFIELD');
            $objectFieldId = $entityMgr->addLookup($values);
            
            if ( !$objectFieldId ) {
                throw new Pt_I18nException('PAAS-0941',"Error creating lookup field" );
            } else {
                $customLabelId = 0;
                if (isset($customLabels[Pt_DataFieldDef::DISPLAY_LABEL_IDX])) {
                    $customLabelId = $customLabels[Pt_DataFieldDef::DISPLAY_LABEL_IDX]->getCustomLabelId();
                }
                $component = [
                    'COMPONENTTYPE'  => 'C',
                    'PATH'           => $values['CUSTOMFIELDID'],
                    'LABEL'          => strtoupper($displayName),
                    'HIDDEN'         => '',
                    'CUSTOMFIELDKEY' => $values['RECORDNO'],
                    'CUSTOMLABELID'  => $customLabelId
                ];
                $compMgr = Globals::$g->gManagerFactory->getManager('customcomponent');
                $ok = $objectFieldId && $compMgr->add($component);
                if ( !$ok ) {
                    throw new Pt_I18nException('PAAS-0942',"Error creating lookup field" );
                }
            }
        }

        return $objectFieldId;
    }

    /**
     * Validate the uniqueness of the integration name of a relationship with all the fields
     *
     * @param string                  $relName
     * @param int                     $objDefId1
     * @param int                     $objDefId2
     * @param Pt_RelationshipDef|null $rel
     *
     * @throws Pt_I18nException
     */
    private static function validateUniqueRelFields(string $relName, int $objDefId1, int $objDefId2,
                                                    Pt_RelationshipDef $rel = null)
    {
        $objDef1 = Pt_DataObjectDefManager::getById($objDefId1);
        $objDef2 = Pt_DataObjectDefManager::getById($objDefId2);

        $lookupField = $rel ? $rel->getField($objDefId1) : null;
        $lookupField2 = $rel ? $rel->getField($objDefId2) : null;
        $fieldName = $lookupField ? isl_strtoupper($lookupField->getFieldName()) : "";
        $fieldName2 = $lookupField2 ? isl_strtoupper($lookupField2->getFieldName()) : "";

        $relNameUpper = isl_strtoupper($relName);
        if ( $fieldName !== $relNameUpper && $fieldName2 !== $relNameUpper ) {
            if ( $relName && ( $objDef1->getByName($relName) || $objDef2->getByName($relName) ) ) {
                throw new Pt_I18nException('PAAS-0872',
                                           "Field name \"$relName\" is already in use. Change the integration name of the relationship.",
                                           [ 'FIELDNAME' => "$relName" ]);
            }
        }
    }
    
    /**
     * Generate labels for component
     *
     * @param Pt_RelationshipDef $relDef
     * @param int                $applicationId
     *
     */
    private static function generateLabelForComponent(Pt_RelationshipDef $relDef, int $applicationId)
    {
        $labels = $relDef->createMissingObjectLabels(Pt_RelationshipDef::CUSTOM_LABELS_INDEXES,
                                                     $relDef->getAllLabels(),
                                                     [ $relDef->getSingularName1(),
                                                       $relDef->getPluralName1(),
                                                       $relDef->getSingularName2(),
                                                       $relDef->getPluralName2() ]);
        $relDef->setAllLabels($labels);
        Pt_RelationshipDefManager::update($relDef, $relDef->__toString(), $labels,
                                          $relDef->getUntranslatedSingularName1(),
                                          $relDef->getUntranslatedPluralName1(),
                                          $relDef->getUntranslatedSingularName2(),
                                          $relDef->getUntranslatedPluralName2(), $relDef->isMultiple1(),
                                          $relDef->isMultiple2(), $relDef->isOrphanControl1(),
                                          $relDef->isOrphanControl2(), $relDef->isCloneRelated1(),
                                          $relDef->isCloneRelated2(), $relDef->disallowDeleteRec1(),
                                          $relDef->disallowDeleteRec2(), $applicationId);
    }
    
    /**
     * Get relationship's labels
     *
     * @param Pt_RelationshipDef $relDef
     * @param string             $baseLanguage
     * @param string             $desiredLanguage
     * @param int                $applicationId
     *
     * @return array
     */
    public static function exportLabelForComponent(Pt_RelationshipDef $relDef, string $baseLanguage,
                                                   string             $desiredLanguage, int $applicationId)
    {
        $customLabels = [];
        self::generateLabelForComponent($relDef, $applicationId);
        $sing1LabelIdx = (int) $relDef::SINGULAR1_LABELS_IDX;
        $sing1BaseLang = $relDef->getSpecificLabelForComponent("", $baseLanguage, $sing1LabelIdx);
        $sing1DesiredLang = $relDef->getSpecificLabelForComponent("", $desiredLanguage, $sing1LabelIdx);
        if ( $labelId = $relDef->getLabels($sing1LabelIdx)
                               ->getCustomLabelId() ?? null ) {
            $customLabels[$labelId] = [ $sing1BaseLang, $sing1DesiredLang ];
        }
        $plural1LabelIdx = (int) $relDef::PLURAL1_LABELS_IDX;
        $plural1BaseLang = $relDef->getSpecificLabelForComponent("", $baseLanguage, $plural1LabelIdx);
        $plural1DesiredLang = $relDef->getSpecificLabelForComponent("", $desiredLanguage, $plural1LabelIdx);
        if ( $labelId = $relDef->getLabels($plural1LabelIdx)
                               ->getCustomLabelId() ?? null ) {
            $customLabels[$labelId] = [ $plural1BaseLang, $plural1DesiredLang ];
        }
        $sing2LabelIdx = (int) $relDef::SINGULAR2_LABELS_IDX;
        $sing2BaseLang = $relDef->getSpecificLabelForComponent("", $baseLanguage, $sing2LabelIdx);
        $sing2DesiredLang = $relDef->getSpecificLabelForComponent("", $desiredLanguage, $sing2LabelIdx);
        if ( $labelId = $relDef->getLabels($sing2LabelIdx)
                               ->getCustomLabelId() ?? null ) {
            $customLabels[$labelId] = [ $sing2BaseLang, $sing2DesiredLang ];
        }
        $plural2LabelIdx = (int) $relDef::PLURAL2_LABELS_IDX;
        $plural2BaseLang = $relDef->getSpecificLabelForComponent("", $baseLanguage, $plural2LabelIdx);
        $plural2DesiredLang = $relDef->getSpecificLabelForComponent("", $desiredLanguage, $plural2LabelIdx);
        if ( $labelId = $relDef->getLabels($plural2LabelIdx)
                               ->getCustomLabelId() ?? null ) {
            $customLabels[$labelId] = [ $plural2BaseLang, $plural2DesiredLang ];
        }
        
        return $customLabels;
    }
}
