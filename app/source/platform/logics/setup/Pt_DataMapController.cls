<?
/**
 * Manages HTTP requests to serialize conversion maps.
 */
require_once('Pt_Includes.inc');

require_once('Pt_DataMapManager.cls');
require_once('Pt_ConversionMap.cls');
require_once('Pt_ImportMap.cls');
require_once('Pt_ConversionUtil.cls');

class Pt_DataMapController {

    /**
     * Dispatch request to right Data Map API.
     */
    public static function dispatch() {
        $action = http_getParameter(FIELD_ACTION);
        if ($action==null) {
            return;
        }

        if ( ! Pt_WebUtil::verifyCsrfToken() ) {
            // No csrf token, don't process but keep going.
            $tokens = [ "IA.INVALID_REQUEST" ];
            $placeholderTokens = [];
            $textMap = getIntlTextMap($tokens, $placeholderTokens);
            Session::setProperty(ATTR_INFO_MESSAGE, GT($textMap, "IA.INVALID_REQUEST"));
            return;
        }

        switch ($action) {
        case 'saveConversionMap':
            $map = self::create(false);
            http_setSessionObject(ATTR_CONVERSION_MAP, $map);
            break;

        case 'saveImportMap':
            self::create(true);
            break;

        case 'deleteMap':
            self::delete();
            break;
        }

    }

    /**
     * Create new conversion map.
     *
     * @param bool $isImport
     *
     * @return Pt_AbstractDataMap
     */
    public static function create($isImport) {
        $mapName = http_getParameter(FIELD_MAP_NAME);
        $map = ($isImport ? self::readImportMap() : self::readConversionMap());

        if ($isImport && Pt_DataMapManager::getImportByDisplayName($mapName)!=null) {
            throw new Pt_I18nException('PAAS-0328', "Import map with display name $mapName already exists",
                                       [ 'MAPNAME' => $mapName ]);
        }
        
        $map2 = null;

        XACT_BEGIN('PLATFORM');
        try {
            $map2 = Pt_DataMapManager::create($mapName, $map, '', '');
            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            db_rollback($ex);
        }

        $tokens = [];
        $placeholderTokens = [
            [
                "id"           => "IA.MAP_MAP_HAS_BEEN_SAVED",
                "placeHolders" => [
                    [ "name" => "MAP", "value" => util_encode($map2->__toString()) ],
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);

        $infoMessage = GT($textMap, "IA.MAP_MAP_HAS_BEEN_SAVED");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);

        return $map2;
    }

    /**
     * Update existing conversion map.
     *
     * @param bool $isImport
     */
    public static function update($isImport) {
        $mapName = http_getParameter(FIELD_MAP_NAME);
        $map = ($isImport ? self::readImportMap() : self::readConversionMap());

        if ($isImport && Pt_DataMapManager::getImportByDisplayName($mapName, $map->getId())!=null) {
            throw new Pt_I18nException('PAAS-0329', "Import map with display name $mapName already exists",
                                       [ 'MAPNAME' => $mapName ]);
        }

        XACT_BEGIN('PLATFORM');
        try {
            Pt_DataMapManager::update($mapName, $map, '');
            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            db_rollback($ex);
        }

        $tokens = [];
        $placeholderTokens = [
            [
                "id"           => "IA.MAP_MAP_HAS_BEEN_UPDATED",
                "placeHolders" => [
                    [ "name" => "MAP", "value" => util_encode($map->__toString()) ],
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);

        $infoMessage = GT($textMap, "IA.MAP_MAP_HAS_BEEN_UPDATED");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
    }

    /**
     * Read conversion map info from HTTP request.
     *
     * @return Pt_ConversionMap
     */
    public static function readConversionMap() {
        $mapId = http_getIntParameter(FIELD_MAP_ID);
        $map = Pt_DataMapManager::getConversionById($mapId);
        if ($map == null) {
            $destDefId = http_getIntParameter(FIELD_DEST_DEF_ID);
            $srcDefId = http_getIntParameter(FIELD_SRC_DEF_ID);
            if ($destDefId <= 0 || $srcDefId <= 0) {
                throw new Pt_I18nException('PAAS-0330', "No mapping info provided");
            }
            $map = Pt_ConversionMap::getEmpty($destDefId, $srcDefId, '');
        }

        $map->clear();
        foreach (Pt_ConversionUtil::getDestFields($map->getDestObjectDef()) as $destField) {
            $destName = $destField->getFieldName();
            $srcName = http_getParameter("map".$destName);

            $constName = "const".$destName;
            $constValue = http_getParameter($constName);
            if ($destField->getUIField() instanceof Pt_SelectListMultiple) {
                $arr = http_getIntParameters($constName);
                $constValue = util_converge($arr);
            }

            if ($destField->isRequired() && $srcName == null && $constValue == null) {
                throw new Pt_I18nException('PAAS-0331', "No mapping for required fields $destName",
                                           [ 'DESTNAME' => $destName ]);
            }
            $map->addMapNode($destName, $srcName, $constValue);
        }

        $map->setDeleteSrc(http_isBoxChecked(FIELD_DELETE_SRC));
        return $map;
    }

    /**
     * Read import map info from HTTP request.
     *
     * @return Pt_ImportMap
     */
    public static function readImportMap() {
        $mapId = http_getIntParameter(FIELD_MAP_ID);
        $map = Pt_DataMapManager::getImportById($mapId);
        if ($map == null) {
            $destDefId = http_getIntParameter(FIELD_DEST_DEF_ID);
            if ($destDefId <= 0) {
                throw new Pt_I18nException('PAAS-0332', "No mapping info provided");
            }
            $map = Pt_ImportMap::getEmpty($destDefId, '');
        }

        $map->clear();
        foreach ($map->getDestObjectDef()->getFields() as $field) {
            if ($field->isReadOnly()) {
                continue;
            }
            $uiField = $field->getUIField();
            $fieldName = $field->getFieldName();
            $constName = '#C_'.$fieldName;
            $colNo = http_getIntParameter($fieldName, -1);

            $constValue = http_getParameter($constName);
            if ($uiField instanceof Pt_SelectListMultiple) {
                $constValue = util_converge(http_getIntParameters($constName));
            } else if ($uiField instanceof Pt_TimeInput) {
                $constValue = $uiField->getTime($constName);
            }

            $map->add($fieldName, $colNo, $constValue);
        }

        return $map;
    }

    /**
     * Delete saved Data Map.
     */
    public static function delete() {
        $mapId = http_getIntParameter(FIELD_MAP_ID);
        $map = Pt_DataMapManager::getById($mapId);
        if ($map == null) {
            throw new Pt_I18nException('PAAS-0333', "Cannot find map with id $mapId",
                                       [ 'MAPID' => $mapId ]);
        }

        XACT_BEGIN('PLATFORM');
        try {
            Pt_DataMapManager::delete($map);
            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            db_rollback($ex);
        }

        $tokens = [];
        $placeholderTokens = [
            [
                "id"           => "IA.MAP_MAP_HAS_BEEN_DELETED",
                "placeHolders" => [
                    [ "name" => "MAP", "value" => util_encode($map->__toString()) ],
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);

        $infoMessage = GT($textMap, "IA.MAP_MAP_HAS_BEEN_DELETED");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
    }

}
