<?php

/**
 * Class PackagePushQueueManager
 * TABLE: packagepushqueue (global schema)
 */
class PackagePushQueueManager extends EntityManager
{

    const PENDING_STATE = 'pending';
    const SUCCES_STATE = 'success';
    const FAILED_STATE = 'failed';
    const IN_PROGRESS_STATE = 'inProgress';
    const SKIPPED_STATE = 'skipped';

    /**
     * @param $values
     *
     * @return bool
     */
    function regularAdd(&$values)
    {
        $values['CREATEDBY'] = $values['CREATEDBY'] ?? GetMyUserid();
        $values['EXECUTION_TIME'] = $values['EXECUTION_TIME'] ?? GetCurrentUTCTimestamp();
        $values['WHENCREATED'] = $values['WHENCREATED'] ?? GetCurrentUTCTimestamp();
        $values['PUSHED_FROM'] = GetMyCompany();

        return parent::regularAdd($values);
    }

    /**
     * @param $ID
     *
     * @return array|false
     */
    public function getByRecordNoDbFilter($ID)
    {
        $repositoryManager = Globals::$g->gManagerFactory->getManager('packagepushqueue');
        $list = $repositoryManager->GetList(
            [
                "nodbfilters" => true,
                'selects'     => [],
                'filters'     => [ [ [ 'RECORDNO', '=', $ID ] ] ] ]
        );

        return $list[0] ?? [];
    }

    public static function pushThroughModal()
    {
        $selectedCompanies = http_getParameter(SELECTED_COMPANIES);
        $companies = json_decode($selectedCompanies, true);
        $packageId = http_getParameter('packageId');
        $packageKey = http_getParameter('packageKey');
        $packagePushQueueManager = Globals::$g->gManagerFactory->getManager('packagepushqueue');

        if ( is_array($companies) ) {
            foreach ( $companies as $company ) {
                if ( $company !== "" ) {
                    $values = [
                        'COMPANYID'     => $company,
                        'RESOURCE_TYPE' => 'APP',
                        'PACKAGE_ID'    => $packageId,
                        'PACKAGE_KEY'   => $packageKey,
                        'PUSHED_FROM'   => GetMyCompany(),
                        'STATUS'        => self::PENDING_STATE,
                    ];
                    $packagePushQueueManager->add($values);
                }
            }
        }
    }
    
    /**
     * @param int $messageKey
     *
     * @return bool
     */
    public static function updateQueueStatusMessage(int $messageKey, string $status) : bool
    {
        $queueManager = Globals::$g->gManagerFactory->getManager('packagepushqueue');
        $message = $queueManager->get($messageKey);
        if ( $message ) {
            $message['STATUS'] = $status;
            
            return $queueManager->set($message);
        }
        
        return false;
    }
}
