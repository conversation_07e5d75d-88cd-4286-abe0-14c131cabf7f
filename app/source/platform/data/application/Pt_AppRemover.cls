<?
/**
 * Remove (uninstall) application for given customer.
 */
require_once 'Pt_Includes.inc';

require_once 'Pt_Application.cls';
require_once 'Pt_PacketProxy.cls';

class Pt_AppRemover
{

    /**
     * Remove existing application.
     *
     * @param Pt_Application $app
     *
     * @throws Exception
     */
    public function remove(Pt_Application $app)
    {
        if ($app == null) {
            throw new Pt_I18nException('PAAS-0113', "Application is null");
        }
        $proxy = new Pt_PacketProxy($app);

        // Select tabs and definitions to remove
        $tabsRemove = $app->getTabs();
        $portalsRemove = $app->getPortals();
        $defsRemove = $proxy->getCoreDefs();

        $removeByLinks = true;
        $cny = GetMyCompany();
        $apps = Pt_ApplicationManager::getAll();
        foreach ($apps as $app2) {
            [$appCny, ] = explode('@', $app2->getOriginalId());
            if ( $cny == $appCny) {
                $removeByLinks = false;
            }
            if ($app->getId() == $app2->getId()) {
                continue;
            }

            for ($k=count($tabsRemove)-1; $k>=0; $k--) {
                $tab = $tabsRemove[$k];
                if ($tab!=null && $app2->hasTab($tab->getId())) {
                    unset($tabsRemove[$k]);
                }
            }

            for ($k=count($portalsRemove)-1; $k>=0; $k--) {
                $portal = $portalsRemove[$k];
                if ($portal!=null && $app2->hasPortal($portal->getId())) {
                    unset($portalsRemove[$k]);
                }
            }

            for ($k=count($defsRemove)-1; $k>=0; $k--) {
                $objDef = $defsRemove[$k];
                if ($objDef!=null && $app2->hasObjectDef($objDef->getId())) {
                    unset($defsRemove[$k]);
                }
            }
        }

        // Delete menus
        foreach ($tabsRemove as $tab) {
            Pt_MenuManager::deleteMenu($tab);
        }

        // Delete portals
        foreach ($portalsRemove as $portal) {
            Pt_PortalManager::delete($portal);
        }

        // Delete definitions
        foreach ($defsRemove as $objDef) {
            Pt_DataObjectDefManager::delete($objDef);
        }

        if ( FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('PT_ALL_COMPONENTS_UNINSTALL') ) {
            $appOrigId = $app->getOriginalId();
            Pt_AppLinkManager::deleteSetByAppOrigId(Pt_Cache::getDataMaps(), $appOrigId, $removeByLinks,
                                                    'Pt_DataMapManager', 'setDataMap');
            Pt_AppLinkManager::deleteSetByAppOrigId(Pt_Cache::getEventDefs(), $appOrigId, $removeByLinks,
                                                    'Pt_EventDefManager', 'setEventDef');
            Pt_AppLinkManager::deleteSetByAppOrigId(Pt_Cache::getListDefs(), $appOrigId, $removeByLinks,
                                                    'Pt_ListDefManager', 'setListDef');
            Pt_AppLinkManager::deleteSetByAppOrigId(Pt_Cache::getTemplates(), $appOrigId, $removeByLinks,
                                                    'Pt_TemplateManager', 'setTemplate');
            Pt_AppLinkManager::deleteSetByAppOrigId(Pt_Cache::getWebPages(), $appOrigId, $removeByLinks,
                                                    'Pt_WebPageManager', 'setWebPage');
            $stubsByObjDefId = Pt_Cache::getWebPagesByObjDefId();
            foreach ( array_merge($defsRemove, $app->getStObjectDefs()) as $objDef ) {
                /** @var Pt_DataObjectDef $objDef */
                $objPageStubs = $stubsByObjDefId[$objDef->getId()] ?? [];
                foreach ( $objPageStubs as $stub ) {
                    if ( ( $page = $stub->getWebPage() ) && $page instanceof Pt_WebPage ) {
                        $keptSections = [];
                        foreach ( $page->getSections() as $section ) {
                            $keptCells = [];
                            foreach ( $section->getCells() as $cell ) {
                                $isRemoved = Pt_AppLinkManager::deleteByAppOrigId($cell, $appOrigId, $removeByLinks,
                                                                                  'Pt_PageCellManager', '');
                                if ( !$isRemoved ) {
                                    $keptCells[] = $cell;
                                }
                            }
                            $section->setCells($keptCells);

                            $isRemoved = Pt_AppLinkManager::deleteByAppOrigId($section, $appOrigId, $removeByLinks,
                                                                              'Pt_PageSectionManager', '');
                            if ( !$isRemoved ) {
                                $keptSections[] = $section;
                            }
                        }
                        $page->setSections($keptSections);
                        Pt_Cache::setWebPage($page);
                    }
                }
            }
            Pt_AppLinkManager::deleteSetByAppOrigId(Pt_Cache::getRelationshipDefs(), $appOrigId, $removeByLinks,
                                                    'Pt_RelationshipDefManager', 'setRelationshipDef');
            Pt_AppLinkManager::deleteByObjTypeAndAppOrigId(Globals::$g->gManagerFactory->getManager('smartlink'),
                                                           SmartLinkManager::OBJECTTYPE, $appOrigId, $removeByLinks);
            Pt_AppLinkManager::deleteByObjTypeAndAppOrigId(Globals::$g->gManagerFactory->getManager('customfield'),
                                                           CustomFieldManager::OBJECTTYPE, $appOrigId, $removeByLinks);
        }

        Pt_AppPagePropertiesManager::deleteByApp($app);

        // Delete application record
        Pt_ApplicationManager::delete($app);

        Pt_Cache::refresh();
    }

}
