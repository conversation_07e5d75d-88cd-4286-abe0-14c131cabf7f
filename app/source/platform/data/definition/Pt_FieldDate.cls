<?
/**
 * Data field for date value (no time portion).
 */
require_once 'Pt_Includes.inc';

require_once 'Pt_DataFieldDef.cls';

class Pt_FieldDate extends Pt_DataFieldDef
{

    /**
     * Get field's type for reports
     *
     * @return string
     */
    public function getNexusType() 
    {
        return 'date';
    }

    /**
     * Generate the DDL SQL for adding the column to IDW
     *
     * @return string
     */
    public function idwDdlAddColumn() 
    {
        return $this->idwGetFieldName() . " date";
    }

    /**
     * Determines whether this fields participates in DDS
     *
     * @return bool
     */
    public function ddsInclude()
    {
        return true;
    }
}
