<?
/**
 * SQL Query for pageable List View.
 */
require_once 'Pt_Includes.inc';

require_once 'Pt_ListDef.cls';
require_once 'Pt_PrevNext.cls';
require_once 'Pt_QueryUtil.cls';
require_once 'Pt_RowData.cls';

class Pt_ListQuery
{
    /* @var Pt_ListDef $listDef */
    protected $listDef;
    /* @var Pt_RelationshipDef $rel */
    protected $rel;     // May be null
    /* @var Pt_DataObject $data */
    protected $data;    // May be null
    /* @var Pt_Filter[] $filters */
    protected $filters;
    /* @var int $joinType */
    protected $joinType;
    /* @var string $expression */
    protected $expression;
    /* @var string $customObjectInlineFilters */
    protected $customObjectInlineFilters;
    /* @var array $standardObjectInlineFilters */
    protected $standardObjectInlineFilters;
    /* @var string[][] $restrictions */
    protected $restrictions;
    /* @var int $sortColId */
    protected $sortColId;
    /* @var bool $isAscending */
    protected $isAscending;
    /* @var bool $hasPaging */
    protected $hasPaging;
    /* @var int $userId */
    protected $userId;
    /* @var string|array $querySQL */
    protected $querySQL;

    /**
     * @param Pt_ListDef         $listDef
     * @param Pt_DataObject|null $data
     */
    public function __construct(Pt_ListDef $listDef, $data)
    {
        $this->listDef = $listDef;
        $this->userId = GetMyUserid();
        $this->data = $data;
        $this->sortColId = $listDef->getDefaultSortId();
        $this->isAscending = $listDef->isDefaultSortAsc();
        $this->hasPaging = true;
        $this->filters = [];
        for ($k=0,$c=$listDef->getNumFilters(); $k<$c; $k++) {
            $this->filters[] = $listDef->getFilter($k);
        }
        $this->joinType = $listDef->getJoinType();
        $this->expression = $listDef->getExpression();
    }

    /**
     * Read variables for inline filters.
     *
     * @param string $formName
     */
    public function readInlineFilters($formName)
    {
        $action = http_getParameter(FIELD_ACTION);
        if ( $action == ACTION_CLEAR_INLINE || $action == ACTION_CLEAR_SEL_RECS ) {     // T 8940
            http_removeSessionObject(ATTR_INLINE_FILTERS);
            return;
        }

        $filtersMap = http_getSessionObject(ATTR_INLINE_FILTERS);
        $this->setInlineFilters($formName, $filtersMap);

        http_setSessionObject(ATTR_INLINE_FILTERS, $filtersMap);
    }

    /**
     * Read stored inline filters.
     *
     * @param string $formName
     */
    public function readStoredInlineFilters($formName)
    {
        $filtersMap = http_getSessionObject(ATTR_INLINE_FILTERS);
        $this->setInlineFilters($formName, $filtersMap);
    }

    /**
     * @param string $formName
     * @param array  $filtersMap
     */
    private function setInlineFilters($formName, &$filtersMap)
    {
        if ( $filtersMap == null ) {
            $filtersMap = [];
        }
        $fields = $this->listDef->getInlineFilterFields();

        $objDef = $this->listDef->getObjectDef();
        $entityMgr = $objDef->getEntityManager();

        $isStandard = !$objDef->isPlatform();
        foreach ( $fields as $field ) {
            $fieldName = $field->getFieldName();
            $filterName = $formName . '_' . str_replace('.', '_', $fieldName);
            $value = http_getParameter($filterName);

            if ( !isset($value) ) {
                $value = $filtersMap[$filterName] ?? null;
            }
            if ( $value == '-' ) {
                $value = null;
            }
            $filtersMap[$filterName] = $value;
            if ( $value === null ) {
                continue;
            }

            if ( $isStandard ) {
                $this->setStandardObjectInlineFilter($field, $fieldName, $entityMgr, $value);
            } else {
                $this->setCustomObjectInlineFilter($field, $value);
            }
        }
    }

    /**
     * @param string[] $validValues
     * @param string   $value
     *
     * @return string[]
     */
    private function getFilteredValidValues($validValues, $value)
    {
        $filteredValidValues = [];
        $value = isl_strtoupper($value);
        foreach ( $validValues as $validValue ) {
            if ( isl_strpos(isl_strtoupper($validValue), $value) === 0 ) {
                $filteredValidValues[] = $validValue;
            }
        }

        return $filteredValidValues;
    }

    /**
     * @param Pt_DataFieldDef $field
     * @param string           $fieldName
     * @param EntityManager    $entityMgr
     * @param string           $value
     */
    private function setStandardObjectInlineFilter($field, $fieldName, $entityMgr, $value)
    {
        $uiField = $field->getUIField();
        $fieldInfo = $entityMgr->GetFieldInfo($fieldName);
        $fieldPtype = $fieldInfo['type']['ptype'];
        $hasValidValues = isset($fieldInfo['type']['validvalues']);

        if ( $uiField instanceof Pt_CurrencyInput ) {
            $this->standardObjectInlineFilters[] = [ $fieldName, "=", Pt_CurrencyFormat::parse($value, 2) ];
        } else if ( $field instanceof Pt_FieldDouble ) {
            /* @var Pt_CurrencyInput|Pt_DecimalInput|Pt_FormulaField $uiField */
            $this->standardObjectInlineFilters[] = [ $fieldName, "=", Pt_CurrencyFormat::parse($value,
                                                                                               $uiField->getDecimalPlaces()) ];
        } else if ( $field instanceof Pt_FieldInt ) {
            $this->standardObjectInlineFilters[] = [ $fieldName, "=", (int) $value ];
        } else if ( $fieldPtype == 'multipick' ) {
            $this->standardObjectInlineFilters[] = [ $fieldName, "PADDEDLIKE", $value ];
        } else if ( $hasValidValues ) {
            $validValues = array_keys($entityMgr->GetFieldValidValues($fieldName));
            $filteredValidValues = $this->getFilteredValidValues($validValues, $value);
            if ( $filteredValidValues ) {
                $this->standardObjectInlineFilters[] = [ $fieldName, "IN", $filteredValidValues ];
            } else {
                $this->standardObjectInlineFilters[] = [ $fieldName, "NOT IN", $validValues ];
            }
        } else {
            $this->standardObjectInlineFilters[] = [ $fieldName, "ILIKE", $value . "%" ];
        }
    }

    /**
     * @param Pt_DataFieldDef $field
     * @param string          $value
     */
    private function setCustomObjectInlineFilter($field, $value)
    {
        if ( $this->customObjectInlineFilters != "" ) {
            $this->customObjectInlineFilters .= " AND ";
        } else {
            $this->customObjectInlineFilters = '';
        }

        $uiField = $field->getUIField();
        $columnName = $field->getColumnName();

        if ( $uiField instanceof Pt_CurrencyInput ) {   // Bug 37321
            $this->customObjectInlineFilters .= "A.$columnName=" . Pt_CurrencyFormat::parse($value, 2);
        } else if ( $field instanceof Pt_FieldDouble ) {
            /* @var Pt_CurrencyInput|Pt_DecimalInput|Pt_FormulaField $uiField */
            $this->customObjectInlineFilters .= "A.$columnName="
                                                . Pt_CurrencyFormat::parse($value,
                                                                           $uiField->getDecimalPlaces());
        } else if ( $field instanceof Pt_FieldInt ) {
            $this->customObjectInlineFilters .= "A.$columnName=" . (int) $value;
        } else if ( $field instanceof Pt_FieldString && !$this->listDef->getObjectDef()->isPlatform() ) {   // Bug 41305
            $external = $field->getProperty(FIELD_ENUM_EXTERNAL);
            $internal = $field->getProperty(FIELD_ENUM_INTERNAL);
            if ( is_array($external) && is_array($internal) ) {
                $v = isl_strtolower($value);
                for ( $k = 0, $c = count($external); $k < $c; $k++ ) {
                    if ( startsWith($external[$k], $v) ) {
                        $filterValue = $internal[$k];
                        $this->customObjectInlineFilters .= "A.$columnName='" . db_encode($filterValue) . "'";
                        break;
                    }
                }
            } else {
                $this->customObjectInlineFilters .= "UPPER(A.$columnName) LIKE '"
                                                    . db_encode(isl_strtoupper($value)) . "%'";
            }
        } else {
            $this->customObjectInlineFilters .= "UPPER(A.$columnName) LIKE '"
                                                . db_encode(isl_strtoupper($value)) . "%'";
        }
    }

    /**
     * Read restrictions imposed *-to-many relationships
     */
    public function readRestrictions()
    {
        $restrictionsStr = http_getParameter(ATTR_RESTRICTIONS);
        if ( !$restrictionsStr || $restrictionsStr == '' ) {
            return;
        }

        $relsStrArr = explode("|#|", $restrictionsStr);
        $this->restrictions = [];

        if ( $relsStrArr) {
            foreach ( $relsStrArr as $relsStr ) {
                $relsArr = explode('|~|', $relsStr);
                $this->restrictions[] = $relsArr;
            }
        }
    }


    /**
     * Get object definition id.
     *
     * @return int
     */
    public function getObjectDefId()
    {
        return $this->listDef->getObjectDefId();
    }

    /**
     * Get user's id.
     *
     * @return int
     */
    public function getUserId()
    {
        return $this->userId;
    }

    /**
     * Get main DataObject (for related lists).
     *
     * @return Pt_DataObject
     */
    public function getDataObject()
    {
        return $this->data;
    }

    /**
     * Set main DataObject.
     *
     * @param Pt_DataObject $data
     */
    public function setDataObject(Pt_DataObject $data)
    {
        $this->data = $data;
    }

    /**
     * Set sorting parameters.
     *
     * @param int  $sortColNo
     * @param bool $isAscending
     */
    public function setSorting($sortColNo, $isAscending)
    {
        if ($sortColNo == SEARCH_FLAG_ID) {
            $this->sortColId = $sortColNo;
        } else if ($sortColNo >= 0) {
            $this->sortColId = $this->listDef->getColId($sortColNo);
        }
        $this->isAscending = $isAscending;
    }

    /**
     * Get RelationshipDef (for related lists).
     *
     * @return Pt_RelationshipDef
     */
    public function getRelationshipDef()
    {
        return $this->rel;
    }

    /**
     * Set RelationshipDef.
     *
     * @param Pt_RelationshipDef $rel
     */
    public function setRelationshipDef(Pt_RelationshipDef $rel)
    {
        $this->rel  = $rel;
        $this->hasPaging = ($this->rel != null);
    }

    /**
     * Set Filters.
     *
     * @param Pt_FilterList $filterList
     */
    public function setFilterClauses(Pt_FilterList $filterList)
    {
        $this->filters = array_merge([], $filterList->getAll());
    }

    /**
     * Add list of new Filters.
     *
     * @param Pt_FilterList $filterList
     */
    public function addFilterClauses(Pt_FilterList $filterList)
    {
        $this->filters = array_merge($this->filters, $filterList->getAll());
    }

    /**
     * Add single Filter.
     *
     * @param Pt_Filter $filter
     */
    public function addFilter($filter)
    {
        if ($filter != null) {
            $this->filters[] = $filter;
        }
    }

    /**
     * Get number of Filters.
     *
     * @return int
     */
    public function getNumFilters()
    {
        return count($this->filters);
    }

    /**
     * Has view or flag Filter?
     *
     * @return bool
     */
    public function hasFlagFilter()
    {
        foreach ($this->filters as $filter) {
            $filterFieldId = $filter->getFieldDefId();
            if ($filterFieldId==SEARCH_FLAG_ID || $filterFieldId==SEARCH_VIEW_ID) {
                return true;
            }
        }
        return false;
    }

    /**
     * Has values for inline filters?
     *
     * @return bool
     */
    public function hasInlineFilters()
    {
        return $this->customObjectInlineFilters !== null || $this->standardObjectInlineFilters !== null;
    }

    /**
     * Has restrictions
     *
     * @return bool
     */
    public function hasRestrictions()
    {
        return (bool) $this->restrictions;
    }

    /**
     * Use AND, OR, or expression to joing Filters?
     *
     * @return int
     */
    public function getJoinType()
    {
        return $this->joinType;
    }

    /**
     * Set Join Type.
     *
     * @param int $joinType
     */
    public function setJoinType($joinType)
    {
        $this->joinType = $joinType;
    }

    /**
     * Get expression to joing filters.
     *
     * @return string
     */
    public function getExpression()
    {
        return $this->expression;
    }

    /**
     * Set expression to joing filters.
     *
     * @param string $expression
     */
    public function setExpression($expression)
    {
        $this->expression = $expression;
    }

    /**
     * Get total rows and grand totals in the list
     *
     * @param Pt_DataFieldDef $groupField
     * @param mixed           $groupValue
     *
     * @return int[]|float[]|null
     *
     * @throws Exception
     */
    public function getTotalCounts($groupField = null, $groupValue = null)
    {
        $objDef = $this->listDef->getObjectDef();
        $grandTotalFields = $this->listDef->getGrandTotalFields();

        if ( $objDef->isPlatform() ) {
            $resultSet = $this->getCustomObjectTotalCounts($objDef, $groupField, $groupValue, $grandTotalFields);
        } else {
            $resultSet = $this->getStandardObjectTotalCounts($objDef, $groupField, $groupValue, $grandTotalFields);
        }

        $arr = [];
        if ( !$resultSet ) {
            return $arr;
        }

        $r = $resultSet[0];
        $arr['COUNT(1)'] = (int) $r['COUNT(1)'];
        $colNum = 1;
        foreach ( $grandTotalFields as $column ) {
            $fieldName = $column->getFieldName();
            $grandTotal = $r['GT_' . $colNum++];
            if ( $column instanceof Pt_FieldInt ) {
                $arr[$fieldName] = (int) $grandTotal;
            } else {
                $arr[$fieldName] = (float) $grandTotal;
            }
        }

        return $arr;
    }

    /**
     *  Get total rows and grand totals in the list for standard objects
     *
     * @param Pt_DataObjectDef  $objDef
     * @param Pt_DataFieldDef   $groupField
     * @param string            $groupValue
     * @param Pt_DataFieldDef[] $grandTotalFields
     *
     * @return string[][]
     * @throws Exception
     */
    private function getStandardObjectTotalCounts($objDef, $groupField = null, $groupValue = null, $grandTotalFields = null)
    {
        $entityMgr = $objDef->getEntityManager();
        $params = [ 'selects' => [ 'COUNT(1)' ], 'columnaliases' => [ '' ] ];

        $colNum = 1;
        foreach ( $grandTotalFields as $field ) {
            $params['columnaliases'][] = 'GT_' . $colNum++;
            $params['selects'][] = [ 'fields' => $field->getFieldName(), 'function' => 'SUM' ];
        }

        if ( $this->standardObjectInlineFilters ) {
            $params['filters'][0] = $this->standardObjectInlineFilters;
        }

        if ( $groupField ) {
            $groupFieldName = $groupField->getFieldName();

            if ( $groupValue !== null && $groupValue !== "" ) {
                $params['filters'][0][] = [ $groupFieldName, '=', $groupValue ];
            } else {
                $params['filters'][0][] = [ $groupFieldName, "ISNULL" ];
            }
        }

        $this->addStandardObjectRelationFieldsViewFilters($params['filters'][0]);

        $this->addStandardObjectRelationCondition($params['filters'][0]);

        $qryarray = [];
        $systemViewArr = [];
        $processed = $entityMgr->_ProcessParams($params, 'normal', $qryarray, $systemViewArr);
        $args = $processed['ARGS'];

        $stmt = $entityMgr->_QM->_ProcessCustomQuery($processed['STMT'], $args, $blobdata);

        array_unshift($args, $stmt);

        $resultSet = db_query($args);

        return $resultSet;
    }

    /**
     * Get SQL SELECT query
     *
     * @param bool $needFlags
     * @param bool $returnAsQuery This is ignored for standard objects
     *
     * @return string|array
     *
     */
    public function getSelectQuery($needFlags, $returnAsQuery = false)
    {
        $objDef = $this->listDef->getObjectDef();

        if ( $objDef->isPlatform() ) {
            return $this->getCustomObjectSelectQuery($objDef, $needFlags, $returnAsQuery);
        } else {
            return $this->getStandardObjectSelectQuery($objDef);
        }
    }

    /**
     * Get SQL SELECT query for standard object.
     *
     * @param Pt_DataObjectDef $objDef
     *
     * @return array
     * @throws Exception
     */
    private function getStandardObjectSelectQuery($objDef)
    {
        $entityMgr = $objDef->getEntityManager();

        $params['orders'] = [];

        $fields = $objDef->getFields();
        foreach ( $fields as $field ) {
            if ( $field->getFieldName() === 'RECORDNO' ) {
                $params['selects'] = [ 'RECORDNO' ];
                break;
            }
        }

        $this->addStandardObjectSortClause($objDef, $params['orders']);

        if ( $this->standardObjectInlineFilters ) {
            $params['filters'][0] = $this->standardObjectInlineFilters;
        }

        $this->addStandardObjectRelationFieldsViewFilters($params['filters'][0]);

        $this->addStandardObjectRelationCondition($params['filters'][0]);

        $qryarray = [];
        $systemViewArr = [];
        $processed = $entityMgr->_ProcessParams($params, 'normal', $qryarray, $systemViewArr);
        $args = $processed['ARGS'];

        $stmt = $entityMgr->_QM->_ProcessCustomQuery($processed['STMT'], $args, $blobdata);

        array_unshift($args, $stmt);

        return $args;
    }

    /**
     *  Get total rows and grand totals in the list for custom object
     *
     * @param Pt_DataObjectDef  $objDef
     * @param Pt_DataFieldDef   $groupField
     * @param mixed             $groupValue
     * @param Pt_DataFieldDef[] $grandTotalFields
     *
     * @return string[][]|null
     * @throws Exception
     */
    private function getCustomObjectTotalCounts($objDef, $groupField = null, $groupValue = null, $grandTotalFields = null)
    {
        $idColumn = $this->listDef->getIdColumn();  // Will include alias
        $q = new Pt_SelectQuery();
        $q->addFrom($this->listDef->getBaseTableClause());

        $this->addRelationship($q, $idColumn, $objDef->getCompanyNo());

        if ( $this->hasFlagFilter() ) {
            $q->addJoin(
                "PT_USER_FLAGS F", "F.OBJ_ID(+)=" . $idColumn . " AND F.USER_ID(+)=" . $this->getUserId()
                                   . " AND F.CNY#(+)=" . $objDef->getCompanyNo()
            );
        }

        $q->addSelect("COUNT(1)");

        $colNum = 1;
        foreach ( $grandTotalFields as $column ) {
            $q->addSelect("SUM(A." . $column->getColumnName() . ") AS GT_" . $colNum++);
        }

        if ( $this->hasRestrictions() ) {
            $this->addRestrictionClause($q);
        }

        $q->addWhere("A.CNY#=:1");    // Bug 40700
        $q->addWhere("A.OBJ_DEF_ID=:2");

        Pt_QueryUtil::addFilterClauses($q, $idColumn, $this->joinType, $this->expression, $this->filters,
                                       $this->userId);
        if ( $this->customObjectInlineFilters !== null ) {    // T 8940
            $q->addWhere3($this->customObjectInlineFilters);
        }

        // T 2944
        if ( $groupField != null ) {
            if ( $groupField->getColumnName() == null ) {
                return null;    // Cannot proceed
            }
            if ( $groupValue !== null ) {
                $q->addWhere3('A.' . $groupField->getColumnName() . '='
                              . $groupField->toDBQueryValue(':3'));     // T 3448
            } else {
                $q->addWhere3('A.' . $groupField->getColumnName() . ' IS NULL');
            }
        }
        //eppp_p("getTotalCount: ".$q->__toString());

        $queryArr = [ $q->__toString() ];  // Bug 40700
        $queryArr[] = $objDef->getCompanyNo();
        $queryArr[] = $objDef->getId();
        if ( $groupField != null ) {  // T 2944
            // #110808: Relationship field values can be an instance of Pt_ArrayIds.  Deal with that and
            // try to ensure we don't generate invalid queries.
            $atomicGroupValue = $groupValue;
            if ( is_bool($groupValue) ) {
                // See Pt_Filter::getFilterClause (Boolean values should be treated as 0 or 1)
                $atomicGroupValue = $groupValue === true ? 1 : 0;
            }
            if ( $groupValue instanceof Pt_ArrayIds ) {
                $atomicGroupValue = $groupValue->getIds();
            }
            if ( is_array($atomicGroupValue) ) {
                if ( count($atomicGroupValue) != 1 ) {
                    // More than one value?  Query says = not in, can't do that.
                    return null;
                } else {
                    $atomicGroupValue = $atomicGroupValue[0];
                }
            } else if ( is_object($groupValue) ) {
                LogToFile(
                    "ERROR Unexpected group value type.  Field: " . $groupField->getFieldName()
                    . " type: " . get_class($atomicGroupValue) . "\n"
                );

                return null;
            }
            if ( $atomicGroupValue !== null ) {
                $queryArr[] = $atomicGroupValue;
            }
        }

        $resultSet = db_query($queryArr);

        return $resultSet;
    }

    /**
     * Get SQL SELECT query for custom object.
     *
     * @param Pt_DataObjectDef $objDef
     * @param bool             $needFlags
     * @param bool             $returnAsQuery
     *
     * @return array
     */
    private function getCustomObjectSelectQuery($objDef, $needFlags, $returnAsQuery = false)
    {
        if ( $this->querySQL != null && !$needFlags ) {
            return $this->querySQL;
        }

        $groupField = $this->listDef->getGroupByField();
        $sortCol = $objDef->getByFieldId($this->sortColId);
        $idColumn = $this->listDef->getIdColumn();  // Will include alias

        $q = new Pt_SelectQuery();
        $q->addFrom($this->listDef->getBaseTableClause());
        $q->addSelect($idColumn);

        if ( $needFlags || $this->hasFlagFilter() ) {
            $q->addJoin("PT_USER_FLAGS F",
                        "F.OBJ_ID(+)=" . $idColumn . " AND F.USER_ID(+)=" . $this->getUserId() . " AND F.CNY#(+)="
                        . $objDef->getCompanyNo());
            $q->addSelect("F.IS_VIEWED");
            $q->addSelect("F.IS_FLAGGED");
        }

        $this->addRelationship($q, $idColumn, $objDef->getCompanyNo());

        if ( $this->hasRestrictions() ) {
            $this->addRestrictionClause($q);
        }

        $q->addWhere("A.CNY#=:1");    // Bug 40700
        $q->addWhere("A.OBJ_DEF_ID=:2");

        Pt_QueryUtil::addFilterClauses($q, $idColumn, $this->joinType, $this->expression, $this->filters,
                                       $this->userId);
        if ( $this->customObjectInlineFilters !== null ) {   // T 8940
            $q->addWhere3($this->customObjectInlineFilters);
        }

        if ( $groupField != null ) {
            $this->addCustomObjectSortClause($q, $groupField, "L0", true);
        }

        if ( $sortCol != null ) {
            $this->addCustomObjectSortClause($q, $sortCol, "L1", $this->isAscending);
        } else if ( $needFlags && $this->sortColId == SEARCH_FLAG_ID ) {
            $q->addOrderBy("NVL(F.IS_FLAGGED, 'F')", $this->isAscending);       // T 7635
        }

        if ( $this->listDef->getDefaultSort2() != null ) {
            $this->addCustomObjectSortClause($q, $this->listDef->getDefaultSort2(), "L2", $this->listDef->isDefaultSortAsc2());
        }
        if ( $this->listDef->getDefaultSort3() != null ) {
            $this->addCustomObjectSortClause($q, $this->listDef->getDefaultSort3(), "L3", $this->listDef->isDefaultSortAsc3());
        }

        // Add final record# sort so that there's no chance oracle will sort differently based on different columns
        // in the select list.  Yea, it happens.
        $recordHashFieldId = $objDef->getIdByName("id");
        $recordHashField = $objDef->getByFieldId($recordHashFieldId);
        if ( $recordHashField ) {
            $this->addCustomObjectSortClause($q, $recordHashField, "RECORDHASH", $this->isAscending);
        }

        $queryArr = [];
        if ( $returnAsQuery ) {
            $queryArr[] = $q;
        } else {
            $queryArr[] = $q->__toString();    // Bug 40700
        }
        $queryArr[] = $objDef->getCompanyNo();
        $queryArr[] = $objDef->getId();

        if ( !$needFlags ) {
            $this->querySQL = $queryArr;
        }

        //eppp_p("getSelectQuery: $queryArr[0]");
        return $queryArr;
    }

    /**
     * @param string[][]           $orders
     * @param Pt_DataFieldDef|null $sortField
     * @param bool|null            $sortOrder
     */
    private function addStandardDefaultSortClause(&$orders, $sortField, $sortOrder)
    {
        if ( $sortField !== null && $sortOrder !== null ) {
            $fieldName = strtoupper($sortField->getFieldName());
            $orders[] = [ $fieldName, $sortOrder ? 'asc' : 'desc' ];
        }
    }

    /**
     * @param Pt_DataObjectDef $objDef
     * @param string[][]       $orders
     */
    private function addStandardObjectSortClause($objDef, &$orders)
    {
        $groupField = $this->listDef->getGroupByField();
        if ( $groupField != null ) {
            $orders[] = [ strtoupper($groupField->getFieldName()), 'asc' ];
        }

        $columnSortField = $objDef->getByFieldId($this->sortColId);
        if ( $columnSortField ) {
            $orders[] = [ strtoupper($columnSortField->getFieldName()), $this->isAscending ? 'asc' : 'desc' ];
        }

        $listDef = $this->listDef;
        $this->addStandardDefaultSortClause($orders, $listDef->getDefaultSort2(), $listDef->isDefaultSortAsc2());
        $this->addStandardDefaultSortClause($orders, $listDef->getDefaultSort3(), $listDef->isDefaultSortAsc3());
    }

    /**
     * @param Pt_Filter $filter
     *
     * @return string
     */
    private function getStandardObjectViewFiltersFieldRelationshipCondition($filter)
    {
        /* @var Pt_FieldRelationship $fieldDef */
        $fieldDef = $filter->getFieldDef();
        $rel = $fieldDef->getRelationshipDef();

        if ( Pt_RelationshipManagerChoose::newRelationshipRedesignReadNew($rel) ) {
            return $this->_getStandardObjectViewFiltersFieldRelationshipCondition($filter);
        }

        /* @var Pt_StdDataObjectDef $objDef */
        $objDef = $this->listDef->getObjectDef();
        Pt_DataObjectManager::getRelationshipDataQryColumns($rel, $objDef,
                                                            $fieldDef->getObjectDef2(),
                                                            $objIdColumnName, $objDefId,
                                                            $relatedObjIdColumnName, $relatedObjDefId);

        if ( $objDefId > $relatedObjDefId ) {
            $aux = $objDefId;
            $objDefId = $relatedObjDefId;
            $relatedObjDefId = $aux;
        }

        $entityName = $objDef->getEntityName(); // the name of the standard object that we want to list

        $condition = "SELECT $objIdColumnName FROM PT_RELATIONSHIP R0
                      where R0.RELATIONSHIP_ID = " . $rel->getId() . "
                      and R0.CNY# = " . GetMyCompany() . "
                      and R0.OBJ_DEF1_ID = $objDefId
                      and R0.OBJ_DEF2_ID = $relatedObjDefId
                      and R0.$objIdColumnName = $entityName.RECORD#";

        $opCode = $filter->getOpCode();
        if ( $opCode == OP_IN || $opCode == OP_NIN ) {
            $filterValues = $filter->getValue();
            $conditions = [];
            foreach ( $filterValues as $filterValue ) {
                $conditions[] = " R0.$relatedObjIdColumnName = $filterValue ";
            }
            $condition = ( $opCode == OP_NIN ? " NOT " : "" ) . " EXISTS ( $condition and ( " . implode(" or ", $conditions) . " ) ) ";
        } else if ( $opCode == OP_EQ ) {
            $condition = " EXISTS ( $condition and R0.$relatedObjIdColumnName = " . $filter->getValue() . " ) ";
        } else {
            $condition = ( $opCode == OP_NUL ? " NOT " : "" ) . " EXISTS ( $condition ) ";
        }

        return $condition;
    }

    /**
     * @param Pt_Filter $filter
     *
     * @return string
     */
    private function _getStandardObjectViewFiltersFieldRelationshipCondition($filter)
    {
        /* @var Pt_FieldRelationship $fieldDef */
        $fieldDef = $filter->getFieldDef();
        $rel = $fieldDef->getRelationshipDef();
        /* @var Pt_StdDataObjectDef $objDef */
        $objDef = $this->listDef->getObjectDef();
        $objDefId = $objDef->getId();
        $opCode = $filter->getOpCode();
        $cny = GetMyCompany();
        $entityName = $objDef->getEntityName(); // the name of the standard object that we want to list

        if ( !$rel->isOtherMultiple($objDefId) ){

            $relatedObjIdColumnName = $rel->getField($objDefId)->getColumnName();

            if ( $opCode == OP_IN || $opCode == OP_NIN ) {
                $filterValues = $filter->getValue();
                $conditions = [];
                foreach ( $filterValues as $filterValue ) {
                    $conditions[] = " $entityName.$relatedObjIdColumnName = $filterValue ";
                }
                $condition =
                    ( $opCode == OP_NIN ? " NOT " : "" ) . " ( " . implode(" or ", $conditions) . " ) ";
                if ( $opCode == OP_NIN ) {
                    $condition .= " OR $entityName.$relatedObjIdColumnName IS NULL ";
                }
            } else if ( $opCode == OP_EQ ) {
                $condition = " ( $entityName.$relatedObjIdColumnName = " . $filter->getValue() . " ) ";
            } else {
                if ( $opCode == OP_NUL ) {
                    $condition = "( $entityName.$relatedObjIdColumnName IS NULL OR $entityName.$relatedObjIdColumnName=0 )";
                } else {
                    $condition = "( $entityName.$relatedObjIdColumnName>0 )";
                }
            }
        } else {
            $otherObjDef = $rel->getOtherObjectDef($objDefId);
            $otherObjDefId = $otherObjDef->getId();
            $obj1Col = $rel->getField($otherObjDefId)->getColumnName();
            $relatedObjIdColumnName = "RECORD#";
            if ( !$otherObjDef->isPlatform() ) {
                $params = [ "selects" => [ $obj1Col ],
                            "options" => [
                                "noDBSorts" => true,
                                "joinCNY"   => true,
                            ],
                ];

                $entityManager = $otherObjDef->getEntityManager();

                /* @var Pt_StdDataObjectDef $otherObjDef */
                $entityNameOther = $otherObjDef->getEntityName();

                $stmt = $entityManager->GetListQuery($params);
                $condition = $stmt[0] . " ";
                unset($stmt[0]);
                foreach ( $stmt as $key => $query ) {
                    $condition = str_replace(":$key ", "'$query' ", $condition);
                }
            } else {
                $entityNameOther = "R" . $rel->getId();
                $condition =
                    "SELECT $obj1Col FROM PT_OBJ_DATA $entityNameOther WHERE $entityNameOther.CNY#=$cny AND $entityNameOther.OBJ_DEF_ID=$otherObjDefId";
            }
            $condition = $condition . " AND $entityNameOther.$obj1Col=$entityName.RECORD#";

            if ( $opCode == OP_IN || $opCode == OP_NIN ) {
                $filterValues = $filter->getValue();
                $conditions = [];
                foreach ( $filterValues as $filterValue ) {
                    $conditions[] = " $entityNameOther.$relatedObjIdColumnName = $filterValue ";
                }
                $condition =
                    ( $opCode == OP_NIN ? " NOT " : "" ) . " EXISTS ( $condition and ( " . implode(" or ", $conditions)
                    . " ) ) ";
            } else if ( $opCode == OP_EQ ) {
                $condition = " EXISTS ( $condition and $entityNameOther.$relatedObjIdColumnName = " . $filter->getValue() . " ) ";
            } else {
                $condition = ( $opCode == OP_NUL ? " NOT " : "" ) . " EXISTS ( $condition ) ";
            }
        }

        return $condition;
    }

    /**
     * @param string[] $conditions
     *
     * @return string
     */
    private function getExpressionWithConditions($conditions)
    {
        $expression = preg_split('/([0-9]+)/', $this->getExpression(), -1, PREG_SPLIT_DELIM_CAPTURE | PREG_SPLIT_NO_EMPTY);
        foreach ( $expression as &$expressionItem ) {
            if ( is_numeric($expressionItem) && isset($conditions[$expressionItem - 1]) ) {
                $expressionItem = $conditions[$expressionItem - 1];
            }
        }

        return implode(" ", $expression);
    }

    /**
     * @param string[]|null $filtersParams
     */
    private function addStandardObjectRelationFieldsViewFilters(&$filtersParams)
    {
        if ( !$this->filters ) {
            return;
        }

        $relationshipConditions = [];
        foreach ( $this->filters as $filter ) {
            $fieldDef = $filter->getFieldDef();
            if ( $fieldDef instanceof Pt_FieldRelationship ) {
                $relationshipConditions[] = $this->getStandardObjectViewFiltersFieldRelationshipCondition($filter);
            } else {
                $relationshipConditions[] = $filter->getFilterClause($fieldDef->getColumnName(), $fieldDef);
            }
        }

        if ( $relationshipConditions ) {
            $joinType = $this->getJoinType();
            $filtersParams[] = [ 'RECORDNO', 'CASE_CONDITION',
                                 [ " ( " . (
                                   $joinType == JOIN_EXPR
                                       ? $this->getExpressionWithConditions($relationshipConditions)
                                       : implode($joinType == JOIN_AND ? " and " : " or ", $relationshipConditions) )
                                   . " ) ",
                                 ],
            ];
        }
    }

    /**
     * This method allow us to list in a object view page the data from a related object
     *
     * @param string[]|null $filtersParams
     */
    private function addStandardObjectRelationCondition(&$filtersParams)
    {
        $rel = $this->rel;

        if ( !$rel ) {
            return;
        }

        if ( Pt_RelationshipManagerChoose::newRelationshipRedesignReadNew($rel) ) {
            $objDefId = $this->getObjectDefId();
            $objDef = Pt_DataObjectDefManager::getById($objDefId);
            if ( !$rel->isOtherMultiple($objDefId) ) {
                $alias = '';
                if ( $objDef instanceof Pt_StdDataObjectDef ) {
                    $alias = $objDef->getEntityName() . '.';
                }
                $objCol = $rel->getField($objDefId)->getColumnName();
                $filtersParams[] = [ $alias . $objCol, "=", $this->getDataObject()->getId() ];
            } else {
                $otherObjDefId = $rel->getOtherId($objDefId);
                $otherObjDef = Pt_DataObjectDefManager::getById($otherObjDefId);
                $objCol = $rel->getField($otherObjDefId)->getColumnName();

                if ( !$otherObjDef->isPlatform() ) {
                    $alias = $otherObjDef->getEntityName() . '.';
                    $params = [ "selects" => [ $alias . $objCol ],
                                "options" => [
                                    "noDBSorts" => true,
                                    "joinCNY"   => true,
                                ],
                                'filters' => [ [ [ "RECORDNO", '=', $this->getDataObject()->getId() ] ] ],
                    ];

                    $entityManager = $otherObjDef->getEntityManager();

                    $stmt = $entityManager->GetListQuery($params);
                    $innerQry2 = $stmt[0] . " ";
                    unset($stmt[0]);
                    foreach ( $stmt as $key => $query ) {
                        $innerQry2 = str_replace(":$key ", "'$query' ", $innerQry2);
                    }

                    $filtersParams[] = [ "RECORDNO", "INSUBQUERY",
                                         [ 'STMT' => $innerQry2 ],
                    ];
                } else {
                    $filtersParams[] = [ "RECORDNO", "INSUBQUERY",
                                         [ 'STMT'     => "SELECT $objCol FROM PT_OBJ_DATA WHERE CNY#=? AND
                                                  OBJ_DEF_ID=? AND RECORD#=?",
                                           'ARGS'     => [ GetMyCompany(), $otherObjDef->getId(),
                                                           $this->getDataObject()->getId() ],
                                           'ARGTYPES' => [ 'integer', 'integer', 'integer' ],
                                         ],
                    ];
                }
            }
        } else {
            Pt_DataObjectManager::getRelationshipDataQryColumns($rel, $this->listDef->getObjectDef(),
                                                                $this->getDataObject()->getObjectDef(),
                                                                $objIdColumnName, $objDefId,
                                                                $relatedObjIdColumnName, $relatedObjDefId);

            if ( $objDefId > $relatedObjDefId ) {
                $aux = $objDefId;
                $objDefId = $relatedObjDefId;
                $relatedObjDefId = $aux;
            }

            $filtersParams[] = [ "RECORDNO", "INSUBQUERY",
                                 [ 'STMT'     => "SELECT $objIdColumnName FROM PT_RELATIONSHIP R0
                                            WHERE R0.RELATIONSHIP_ID = ? 
                                            and R0.CNY# = ? 
                                            and R0.OBJ_DEF1_ID = ? 
                                            and R0.OBJ_DEF2_ID = ? 
                                            and R0.$relatedObjIdColumnName = ?",
                                   'ARGS'     => [ $rel->getId(), GetMyCompany(), $objDefId, $relatedObjDefId,
                                                   $this->getDataObject()->getId() ],
                                   'ARGTYPES' => [ 'integer', 'integer', 'integer', 'integer', 'integer' ]
                                 ]
            ];
        }
    }

    /**
     * Add sorting clause to SQL query.
     *
     * @param Pt_SelectQuery  $q
     * @param Pt_DataFieldDef $field
     * @param string          $alias
     * @param bool            $isAscending
     */
    private function addCustomObjectSortClause(Pt_SelectQuery $q, Pt_DataFieldDef $field, $alias, $isAscending)
    {
        if (!$field->isSortable()) {
            return;
        }
        $colName = $field->getColumnName();
        if (strpos($colName, '.')===false) {
            $colName = 'A.'.$colName;
        }

        if ($field->isPickItem()) {
            $q->addJoin("PT_LIST_ITEM ".$alias, $alias.".RECORD#(+)=".$colName." AND ".$alias.".CNY#(+)=".$field->getCompanyNo());
            $q->addSelect($alias.".ORDER_NO");
            $q->addOrderBy($alias.".ORDER_NO", $isAscending);
        } else if ($field instanceof Pt_FieldRelationship) {
            $rel = $field->getRelationshipDef();
            if ($rel == null) {
                return;
            }
            $objDef2 = $rel->getOtherObjectDef($this->listDef->getObjectDefId());
            if ($objDef2 == null) {
                return;
            }
            $q->addJoin($objDef2->getTableName()." ".$alias, $alias.".".$objDef2->getIdColumn()."(+)=".$colName." AND ".$alias.".CNY#(+)=".$field->getCompanyNo());
            $q->addSelect($alias.".".$objDef2->getNameColumn());
            $q->addOrderBy($alias.".".$objDef2->getNameColumn(), $isAscending);
        } else if ($colName=="A.PROCESS_ID") {
            $q->addJoin("PT_PROCESS ".$alias, $alias.".RECORD#(+)=".$colName." AND ".$alias.".CNY#(+)=".$field->getCompanyNo());
            $q->addSelect($alias.".ORDER_NO");
            $q->addOrderBy($alias.".ORDER_NO", $isAscending);
        } else if ($colName=="A.STATUS_ID") {
            $q->addJoin("PT_STATUS ".$alias, $alias.".RECORD#(+)=".$colName." AND ".$alias.".CNY#(+)=".$field->getCompanyNo());
            $q->addSelect($alias.".ORDER_NO");
            $q->addOrderBy($alias.".ORDER_NO", $isAscending);
        } else if ($field->getUIField() instanceof Pt_UserLink) { // Bug 36527
            $q->addJoin("USERINFO ".$alias, $alias.".RECORD#(+)=".$colName." AND ".$alias.".CNY#(+)=".$field->getCompanyNo());
            $q->addSelect($alias.".LOGINID");
            $q->addOrderBy($alias.".LOGINID", $isAscending);
        } else {
            $q->addSelect($colName);
            $q->addOrderBy($colName, $isAscending);
        }
    }

    /**
     * Get sorted page of objects.
     *
     * @param int  $rowFrom
     * @param int  $pageSize
     * @param bool $needFlags
     * @param int  $userId
     *
     * @return Pt_RowData[]
     */
    public function getListPage($rowFrom, $pageSize, $needFlags, /** @noinspection PhpUnusedParameterInspection */ $userId)
    {
        $arr = [];
        $queryArr = $this->getSelectQuery($needFlags);

        if ( $queryArr == null ) {
            return $arr;
        }
        if ( $this->rel != null && $this->data == null ) {
            return $arr;
        }

        $objDef = $this->listDef->getObjectDef();
        //  There may be limits on the page size.  If so, impose them now, during the list query.
        $restorer = $objDef->setListLimit($pageSize);
        $rowMax = $rowFrom + $pageSize;

        if ( !$this->hasPaging ) {
            $resultset = db_query($queryArr);
            $counter = 0;
        } else {
            $resultset = db_query($queryArr, $rowFrom, $pageSize);
            $counter = $rowFrom;
        }
        unset($restorer);                      // Cancel any imposed limit.

        if ( $resultset ) {
            $ids = array_column($resultset, 'RECORD#');
            Pt_DataObjectManager::getByIds($objDef, $ids); // preload in cache
        }
        foreach ($resultset as $row) {
            if (!$this->hasPaging || ($rowFrom <= $counter && $counter < $rowMax)) {
                $id = (int)$row['RECORD#'];
                $data = Pt_DataObjectManager::getById($objDef, $id);
                if ($data == null) {
                    continue;
                }

                $rowData = new Pt_RowData($data);
                if ($needFlags) {
                    $rowData->setViewed(db_bool($row['IS_VIEWED']));
                    $rowData->setFlagged(db_bool($row['IS_FLAGGED']));
                }
                $arr[] = $rowData;
            }
            if ($this->hasPaging) {
                $counter++;
                if ($counter >= $rowMax) {
                    break;
                }
            }
        }

        return $arr;
    }


    /**
     *  Get complete list of objects (without paging).
     *
     * @return Pt_DataObject[]
     */
     public function getDataObjectsCollection()
     {
        $queryArr = $this->getSelectQuery(false);
        if ($queryArr == null) {
            return [];
        }
        $objDef = $this->listDef->getObjectDef();
        $resultset = db_query($queryArr);
        $ids = [];
        foreach($resultset as $row) {
            $ids[] = $row['RECORD#'];
        }
        $arr = Pt_DataObjectManager::getDataCollectionById($objDef, $ids);
        return $arr;
     }

    /**
     * Get complete list of objects (without paging).
     *
     * @param int      $maxRecords
     * @param string[] $fields
     * @param bool     $failMax
     * @param bool     &$failedMax
     *
     * @return Pt_DataObject[]|false
     */
    public function getDataObjects($maxRecords, $fields=null, $failMax=false, &$failedMax=null)
    {
        $arr = [];
        $queryArr = $this->getSelectQuery(false, true);
        if ($queryArr == null) {
            return $arr;
        }
        if ($this->rel != null && $this->data == null) {
            return $arr;
        }

        // Execute the record# query and individual object-gets if
        //    - We're executing a standard object query
        //    - We've executed the query before so the objects are
        //      likely cached.  The record# query is cheaper than
        //      the full object query.  This ensures query compatible
        //      behavior to the May13 release.  We could consider caching
        //      the actual query results but that feels iffy. There will be a
        //      performance hit if clearLocalCache is called.
        $readSingly = true;
    
        $objDef = $this->listDef->getObjectDef();
        if ( $objDef->isPlatform() ) {
            static $queryAlreadyExecuted = [];
        
            $queryCacheKey = join(':', $queryArr);
            if ( $fields !== null || ! isset($queryAlreadyExecuted[$queryCacheKey]) ) {
                $arr = Pt_DataObjectManager::getSimpleByQuery($objDef, $queryArr, $maxRecords, "A.", $fields,
                                                              $failMax,$failedMax);
                $readSingly = false;
                $queryAlreadyExecuted[$queryCacheKey] = 1;
            }
        }
    
        if ( $readSingly ) {
            if ( $failMax ) {
                $resultset = db_query($queryArr);
                if ( count($resultset) >= $maxRecords ) {
                    $failedMax = true;

                    return false;
                }
            } else {
                $resultset = db_query($queryArr, 0, $maxRecords);
            }

            $ids = array_column($resultset, 'RECORD#');
            $arr = Pt_DataObjectManager::getByIds($objDef, $ids, $fields);
        }

        return $arr;
    }

    /**
     * Get complete list of objects (without paging).
     *
     * @param int $maxRecords
     *
     * @return int[]
     */
    public function getDataIds($maxRecords)
    {
        $arr = [];
        $queryArr = $this->getSelectQuery(false);
        if ($queryArr == null) {
            return $arr;
        }
        if ($this->rel != null && $this->data == null) {
            return $arr;
        }

        $counter = 0;
        $resultset = db_query($queryArr);
        foreach ($resultset as $row) {
            $id = (int)$row['RECORD#'];
            if ($id <= 0) {
                continue;
            }
            $arr[] = $id;
            $counter++;
            if ($maxRecords != -1 && $counter >= $maxRecords) {
                break;
            }
        }

        return $arr;
    }

    /**
     * Get previous and next ids relative to current id.
     *
     * @param int $currentId
     *
     * @return Pt_PrevNext
     */
    public function getPrevNext($currentId)
    {
        $objDefId = $this->getObjectDefId();
        if ($this->listDef == null) {
            return new Pt_PrevNext($objDefId, -1, -1);
        }

        $queryArr = $this->getSelectQuery(false);
        if ($queryArr == null) {
            return new Pt_PrevNext($objDefId, -1, -1);
        }

        $id1 = -1;
        $id2 = -1;
        $id3 = -1;
        /** @noinspection PhpUnusedLocalVariableInspection */
        $idColumn = $this->listDef->getIdColumn();  // Will include alias

        $resultset = db_query($queryArr);
        foreach ($resultset as $row) {
            $id1 = $id2;
            $id2 = $id3;
            $id3 = (int)$row['RECORD#'];
            if ($id2 == $currentId) {
                break;
            }
        }

        if ($id2 != $currentId) {
            if ($id3 == $currentId) {
                return new Pt_PrevNext($objDefId, $id2, -1);
            } else {
                return new Pt_PrevNext($objDefId, -1, -1);
            }
        } else {
            return new Pt_PrevNext($objDefId, $id1, $id3);
        }
    }

    /**
     * String representation.
     *
     * @return string
     */
    public function __toString()
    {
        return "Pt_ListQuery: ".$this->listDef;
    }

    /**
     * Add relationship.
     *
     * @param Pt_SelectQuery $q
     * @param string         $idColumn
     * @param int            $companyNo
     */
    private function addRelationship(Pt_SelectQuery $q, $idColumn, $companyNo)
    {
        if ($this->rel == null || $this->data == null) {
            return;
        }
        $rel = $this->rel;
        $objDefId = $this->data->getObjectDefId();
        $listObjDefId = $rel->getOtherId($objDefId);
        if ( !Pt_RelationshipManagerChoose::newRelationshipRedesignReadNew($rel) ) {
            $q->addFrom("PT_RELATIONSHIP R");

            if ( $this->rel->getObjectDefId2() == $objDefId ) {
                $q->addWhere("R.OBJ1_ID=" . $idColumn . " AND R.OBJ2_ID=" . $this->data->getId()
                             . " AND R.RELATIONSHIP_ID=" . $this->rel->getId() . " AND R.CNY#(+)=" . $companyNo);
            } else {
                $q->addWhere("R.OBJ1_ID=" . $this->data->getId() . " AND R.OBJ2_ID=" . $idColumn
                             . " AND R.RELATIONSHIP_ID=" . $this->rel->getId() . " AND R.CNY#(+)=" . $companyNo);
            }
        } else {
            if ( $rel->isHierarchy() ) {
                $multiple1 = $rel->isMultiple1();
                $column1 = $rel->getField1()->getColumnName();
                $column2 = ''; //it is never used
            } else {
                $multiple1 = $rel->isMultiple($objDefId);
                $column1 = $rel->getField($listObjDefId)->getColumnName();
                $column2 =  $rel->getField($objDefId)->getColumnName();
            }

            if ( !$multiple1 ) {
                $q->addWhere("A.$column1=" . $this->data->getId());
            } else {
                $objDef = Pt_DataObjectDefManager::getById($objDefId);

                if ($objDef->isPlatform()) {
                    $from = "SELECT $column2 from PT_OBJ_DATA WHERE CNY#=$companyNo AND OBJ_DEF_ID=$objDefId AND "
                            . "RECORD#=" . $this->data->getId();
                } else {
                    $entityManager = $objDef->getEntityManager();
                    $params = [ "selects" => [ $column2 ],
                                "options" => [
                                    "noDBSorts" => true,
                                    "joinCNY"   => true,
                                ],
                                'filters' => [ [ [ "RECORDNO", '=', $this->data->getId() ] ] ],
                    ];
                    $query = $entityManager->GetListQuery($params);
                    $from = $query[0] . " ";
                    foreach ( $query as $key => $value ) {
                        $from = str_replace(":$key ", "'$value' ", $from);
                    }
                }
                $q->addWhere("A.RECORD# IN ($from)");
            }
        }
    }

    /**
     * Add restrictions
     *
     * @param Pt_SelectQuery $q
     */
    private function addRestrictionClause(Pt_SelectQuery $q)
    {
        $i = 1;
        $companyNo = GetMyCompany();
        foreach( $this->restrictions as $restrict ) {
            if ( count($restrict) < 3 ) {
                continue;
            }

            $object = $restrict[0];
            $objDef = Pt_DataObjectDefManager::getByName($object);
            if ( !$objDef ) {
                continue;
            }

            $objId = $restrict[1];
            $relId = $restrict[2];
            //BUG: 86423 - When clicking on the dropdown for a UDD and then clicking "Find".
            //VID is been passed instead of Record# in case of non platform objects so adding a condition to Get Record# instead of VID incase of non platform.
            if (!$objDef->isPlatform()) {
                $mgr = Globals::$g->gManagerFactory->getManager($object);
                $objId = $mgr->GetRecordNoFromVid($objId);
            }

            $relDef = Pt_RelationshipDefManager::getById($relId);
            if ( $relDef ) {
                if ( !Pt_RelationshipManagerChoose::newRelationshipRedesignReadNew($relDef) ) {
                    $relAlias = "R" . $i++;
                    $where = "$relAlias.CNY# = :1 AND $relAlias.RELATIONSHIP_ID = $relId";

                    $obj_id_other_col = ( $relDef->getObjectDefId1() == $objDef->getId() ) ? "OBJ1_ID" : "OBJ2_ID";
                    $obj_id_my_col = ( $relDef->getObjectDefId1() == $objDef->getId() ) ? "OBJ2_ID" : "OBJ1_ID";
                    $where .= " AND $relAlias.$obj_id_other_col = $objId";
                    $where .= " AND $relAlias.$obj_id_my_col = A.RECORD#";

                    $q->addJoin("PT_RELATIONSHIP $relAlias", $where);
                } else {
                    if ( !$relDef->isOtherMultiple($objDef->getId()) ) {
                        $colName = $relDef->getField($objDef->getId())->getColumnName();
                        $q->addWhere("A.$colName IS NOT NULL");
                    } else {
                        $otherObjDef = $relDef->getOtherObjectDef($objDef->getId());
                        $otherObjDefId = $otherObjDef->getId();
                        $colum = $relDef->getOtherField($objDef->getId())->getColumnName();
                        if ($otherObjDef->isPlatform()) {
                            $from = "SELECT RECORD# from PT_OBJ_DATA WHERE CNY#=$companyNo AND OBJ_DEF_ID=$otherObjDefId AND $colum IS NOT NULL";
                        } else {
                            $colum = $relDef->getField($otherObjDefId)->getColumnName();
                            $entityManager = $otherObjDef->getEntityManager();
                            $params = [ "selects" => [ 'recordno' ],
                                        "options" => [
                                            "noDBSorts" => true,
                                            "joinCNY"   => true,
                                        ],
                                        'filters' => [ [ [ "A.$colum", 'IS NOT', "NULL" ] ] ],
                            ];
                            $query = $entityManager->GetListQuery($params);
                            $from = $query[0] . ' ';
                            foreach ( $query as $key => $value ) {
                                $from = str_replace(":$key ", "'$value' ", $from);
                            }
                        }
                        $q->addWhere("RECORD# IN ($from)");
                    }
                }
            }
        }
    }
}

