<?xml version="1.0" encoding="UTF-8"?>
<ROOT>
    <view system="true">
        <title>IA.BANK_TRANSACTION</title>
        <pages>
            <page title="IA.TRANSACTION">
                <section id="txnDetail" columnCount="3">
                    <field>FINANCIALENTITY</field>
                    <field>POSTINGDATE</field>
                    <field>DOCTYPE</field>
                    <field>DOCNO</field>
                    <field>CURRENCY</field>
                    <field>AMOUNT</field>
                    <field>PAYEE</field>
                    <field>DESCRIPTION</field>
                    <field>TRANSACTIONTYPE</field>
                    <field>CLEARED</field>
                    <field>AMOUNTTOMATCH</field>
                    <field>MATCHMODE</field>
                    <field fullname="IA.MATCH_DATE">
			    <path>MATCHEDDATE</path>
			    <type assoc="T">
                                    <type>timestamp</type>
                                    <ptype>timestamp</ptype>
                                </type>
                    </field>
                    <field fullname="IA.MATCHED_BY">
                        <path>MATCHEDBY</path>
                    </field>
                    <field>DISPLAYLETTRAGECODE</field>
                    <field hidden="true">RECORDNO</field>
                </section>
                <section>
                    <grid noDragDrop="true" className="AcctReconGrid" hasFixedNumOfRows="true"
                          noNewRows="true" deleteOnGrid="false" showDelete="false" customFields="no" hidden="true" maxRows="500">
                        <path>ALLMATCHEDBANKTXNS</path>
                        <title>IA.ALL_TRANSACTIONS_IN_THIS_GROUP</title>
                        <column>
                            <field label='Record No' hidden="true">
                                <path>RECORDNO</path>
                            </field>
                        </column>
                        <column>
                            <field label="IA.DATE" readonly="true">
                                <path>POSTINGDATE</path>
                                <type assoc="T">
                                    <type>date</type>
                                    <ptype>date</ptype>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field label="IA.CHECK_DOC_NO" readonly="true">
                                <path>DOCNO</path>
                            </field>
                        </column>
                        <column columnWidth="200px" hasFieldTooltip="true">
                            <field label="IA.DESCRIPTION" readonly="true">
                                <path>DESCRIPTION</path>
                            </field>
                        </column>
                        <column>
                            <field label="IA.BANK_AMOUNT" readonly="true">
                                <path>BAMOUNT</path>
                                <type assoc="T">
                                    <ptype>currency</ptype>
                                    <type>decimal</type>
                                    <maxlength>14</maxlength>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field label="IA.AMOUNT_TO_MATCH" readonly="true">
                                <path>AMOUNTTOMATCH</path>
                                <type assoc="T">
                                    <ptype>currency</ptype>
                                    <type>decimal</type>
                                    <maxlength>14</maxlength>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field label="IA.PAYEE" readonly="true">
                                <path>PAYEE</path>
                            </field>
                        </column>
                        <column>
                            <field label='IA.TXN_TYPE' readonly="true">
                                <path>TRXTYPEDESC</path>
                            </field>
                        </column>
                    </grid>
                </section>
                <section>
                    <grid noDragDrop="true" hasFixedNumOfRows="true" allowEditPage="false" noNewRows="true">
                        <path>MATCHEDTXNS</path>
                        <title>IA.MATCHED_TRANSACTIONS</title>
                        <column>
                            <field label='Record No' hidden="true">
                                <path>RECORDNO</path>
                            </field>
                        </column>
                        <column>
                            <field label="IA.GL_POST_DATE" readonly="true" >
                                <path>POSTINGDATE</path>
                                <type assoc="T">
                                    <type>date</type>
                                    <ptype>date</ptype>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field label='IA.CHECK_DOC_NO' readonly="true">
                                <path>DOCNO</path>
                            </field>
                        </column>
                        <column>
                            <field label="IA.DESCRIPTION" readonly="true">
                                <path>DESCRIPTION</path>
                            </field>
                        </column>
                        <column>
                            <field label="IA.BANK_AMOUNT" readonly="true" >
                                <path>BANKAMOUNT</path>
                                <type assoc="T">
                                    <ptype>currency</ptype>
                                    <type>decimal</type>
                                    <maxlength>14</maxlength>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field label="IA.PAYEE" readonly="true">
                                <path>PAYEE</path>
                            </field>
                        </column>
                        <column>
                            <field label="IA.TXN_DATE" readonly="true">
                                <path>DOCDATE</path>
                                <type assoc="T">
                                    <type>date</type>
                                    <ptype>date</ptype>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field label='IA.TXN_TYPE' readonly="true">
                                <path>TRXTYPEDESC</path>
                            </field>
                        </column>
                        <column>
                            <field label='IA.PYMT_TYPE' readonly="true">
                                <path>PYMTTYPEDESC</path>
                            </field>
                        </column>
                        <column>
                            <field label="IA.TXN_CURR" readonly="true">
                                <path>CURRENCY</path>
                            </field>
                        </column>
                        <column>
                            <field label="IA.TXN_AMOUNT" readonly="true">
                                <path>TRX_AMOUNT</path>
                                <type assoc="T">
                                    <ptype>currency</ptype>
                                    <type>decimal</type>
                                    <maxlength>14</maxlength>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field label="IA.BASE_AMOUNT" readonly="true"  clazz="LinkControlDecimal" userUIControl="InputControlDecimal">
                            <path>AMOUNT</path>
                                <type type='decimal' ptype='href'></type>
                            </field>
                        </column>
                        <column>
                            <field label="IA.MATCHED_AMOUNT" readonly="true">
                                <path>AMOUNTMATCHED</path>
                                <type assoc="T">
                                    <ptype>currency</ptype>
                                    <type>decimal</type>
                                    <maxlength>14</maxlength>
                                </type>
                            </field>
                        </column>
                    </grid>
                </section>
            </page>
        </pages>
    </view>
</ROOT>
