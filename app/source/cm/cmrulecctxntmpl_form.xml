<?xml version="1.0" encoding="iso-8859-1"?>
<ROOT>
    <entity>cmrulecctxntmpl</entity>
    <title>IA.TRANSACTION_TEMPLATE_FOR_CREDIT_CARD</title>
    <helpfile>Adding_Editing_and_Viewing_CMRule_CCTXN_Template</helpfile>
    <view system="true">
        <events>
            <load>onLoad();</load>
        </events>
        <pages>
            <page id="cctxntemplate" className="wideLabels">
                <title>IA.CREDIT_CARD_TRANSACTION_TEMPLATE</title>
                <section columnCount="3" className="noborder" forceColumn="true" customFields="no">
                    <field fullname="IA.ID">
                        <path>TEMPLATEID</path>
                    </field>
                    <field fullname="IA.NAME">
                        <path>TEMPLATENAME</path>
                    </field>
                    <field fullname="IA.DESCRIPTION">
                        <path>DESCRIPTION</path>
                    </field>
                    <field fullname="IA.CREATED_ON" readonly="true">
                        <path>AUWHENCREATED</path>
                    </field>
                    <field fullname="IA.PAYEE">
                        <path>PAYEE</path>
                    </field>
                    <field fullname="IA.LAST_MODIFIED" readonly="true">
                        <path>WHENMODIFIED</path>
                    </field>
                    <field fullname="Record number" hidden="true">
                        <path>RECORDNO</path>
                    </field>
                </section>
                <section id = "taxInfo" columnCount="1" className="noborder" hidden="true" forceColumn="true" customFields="no">
                    <title>IA.TAX_INFORMATION</title>
                    <subsection columnCount="3" className="noborder">
                        <field noLabel="true" path="INCLUSIVETAXALWAYSINBOUND" hidden="true">
                            <type type='textlabel' ptype='textlabel'></type>
                            <default>IA.INBOUND_TAXES_PURCHASES_INCLUSIVE_TAX</default>
                        </field>
                        <field hidden="true">
                            <path>TAXIMPLICATIONS</path>
                            <events>
                                <change>handleTaxImplicationsRadioBtnChangeInTemplates();</change>
                            </events>
                        </field>
                        <field hidden="true">
                            <path>TAXSOLUTIONID</path>
                            <events>
                                <change>handleTaxSolutionChangeInTemplates(this.meta);</change>
                            </events>
                        </field>
                        <field hidden="true">
                            <path>POTAXSCHEDULEID</path>
                        </field>
                    </subsection>
                </section>
                <section id="offsetGrid" title="IA.CREDIT_CARD_TRANSACTION_OFFSET" columnCount="1">
                    <grid clazz="EntriesGrid" hideLineNo="true" hasFixedNumOfRows="true" minRequiredLines="1"
                          deleteOnGrid="false" showDelete="false" allowEditPage="true" hideTitle="true">
                        <entity>cmrulecctxnentrytmpl</entity>
                        <path>ITEMS</path>
                        <maxRows>1</maxRows>
                        <title>IA.CREDIT_CARD_TRANSACTION_OFFSET</title>
                        <column>
                            <field required="true" hidden="true"
                                   clazz="accountLabelField">
                                <path>ACCOUNTLABEL</path>
                                <events>
                                    <change>
                                        populateGLAccount(this.meta, null);
                                        populateLineMemo(this.meta, 'ENTRYDESCRIPTION');
                                    </change>
                                </events>
                            </field>
                        </column>
                        <column>
                            <field required="true"  primaryField="true">
                                <path>GLACCOUNTNO</path>
                                <events>
                                    <change>
                                        populateLineMemo(this.meta, 'ENTRYDESCRIPTION');
                                    </change>
                                </events>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.DEPARTMENT">DEPARTMENTID</field>
                        </column>
                        <column>
                            <field fullname="IA.LOCATION">LOCATION</field>
                        </column>
                        <column>
                            <field>
                                <path>ENTRYDESCRIPTION</path>
                            </field>
                        </column>
                    </grid>
                </section>
            </page>
        </pages>
    </view>
</ROOT>

