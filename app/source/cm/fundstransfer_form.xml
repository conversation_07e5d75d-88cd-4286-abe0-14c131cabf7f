<?xml version="1.0" encoding="UTF-8"?>
<ROOT>
    <view system="true">
        <title>IA.FUNDS_TRANSFER</title>
        <pages>
            <page title="IA.TRANSACTION">
                <section id="header_bottom" className="horizontal headerCustom" customFields="no">
                    <field readonly="true">WHENCREATEDHEADER</field>
                    <field readonly="true">RECORDIDHEADER</field>
                    <field isHTML="true" readonly="true">TRX_TOTALENTEREDHEADER</field>
                    <field isHTML="true" readonly="true" hidden="true">TOTALENTEREDHEADER</field>
                    <field noLabel="true" readonly="true" isHTML="true" className="stamp">REVERSESTATEHEADER</field>
                </section>
                <section id="transferDetailSection"   columnCount="3">
                    <title>IA.FUNDS_TRANSFER_BETWEEN_ACCOUNTS</title>
                    <subsection id="fromAcctDetails" className="multiColumnSection" forceColumn="true" title="IA.TRANSFER_FROM">
                        <field  required="true" label="IA.FROM_ACCOUNT">
                            <path>FROMACCOUNTID</path>
                            <events>
                                <change>updateAccountBalance(this.meta);showCurrencyField(this.meta);;</change>
                            </events>
                        </field>
                        <field>FROMACCOUNTBALANCE</field>
                        <field>FROMACCOUNTCURR</field>
                        <field hidden="true">
                            <path>FROM_ACCOUNT_TRX_AMOUNT</path>
                            <events>
                                <change>checkOnInsufficientFunds();updateFromAccountLocationBaseCurrencyAndExchangeRate(this.meta);</change>
                            </events>
                        </field>
                    </subsection>
                    <subsection  id="toAcctDetails"  className="multiColumnSection" forceColumn="true" title="IA.TRANSFER_TO">
                        <field  required="true" label="IA.TO_ACCOUNT">
                            <path>TOACCOUNTID</path>
                            <events>
                                <change>updateAccountBalance(this.meta);showCurrencyField(this.meta);</change>
                            </events>
                        </field>
                        <field>TOACCOUNTBALANCE</field>
                        <field>
                            <path>TOACCOUNTCURR</path>
                            <events>
                                <change>updateExchangeRate(this.meta);</change>
                            </events>
                        </field>
                        <field hidden="true">
                            <path>RECORDNO</path>
                        </field>
                    </subsection>
                </section>
                <section id="additionalDetailsOld" hidden="true"  columnCount="2">  </section>
                <section id="mcpSection" columnCount="2" hidden="true">
                    <title>IA.CURRENCY</title>
                </section>
                <section id="baseCurrencyConversion" columnCount="2" hidden="true" isCollapsible="true" showCollapsed="true">
                    <title>IA.BASE_CURRENCY_CONVERSION</title>
                    <subsection id="fromAcctCurrencyConversion"  className="subSectionTitle noborder" forceColumn="true"
                             title="IA.TRANSFER_FROM">
                        <field>
                            <path>FROM_ACCOUNT_BASE_CURRENCY</path>
                            <events>
                                <change>updateFromAccountLocationBaseCurrencyAndExchangeRate(this.meta);</change>
                            </events>
                        </field>
                        <field>
                            <path>FROM_ACCOUNT_EXCH_RATE_DATE</path>
                            <events>
                                <change>updateFromAccountLocationBaseCurrencyAndExchangeRate(this.meta);</change>
                            </events>
                        </field>
                        <field>
                            <path>FROM_ACCOUNT_EXCH_RATE_TYPE_ID</path>
                            <events>
                                <change>updateFromAccountLocationBaseCurrencyAndExchangeRate(this.meta);</change>
                            </events>
                        </field>
                        <field>
                            <path>FROM_ACCOUNT_EXCHANGE_RATE</path>
                            <events>
                                <change>c_updateBaseExchangeRate(this.meta.getValue(), 'FROM_ACCOUNT_');</change>
                            </events>
                        </field>
                        <field>
                            <path>FROM_ACCOUNT_AMOUNT</path>
                        </field>
                    </subsection>
                    <subsection className="subSectionTitle noborder" forceColumn="true"
                             title="IA.TRANSFER_TO">
                    </subsection>
                </section>
                <section id="dimensions" columnCount="2">
                    <title>IA.DIMENSIONS</title>
                    <helpText>IA.TRACK_AND_REPORT_THIS_FUNDS_TRANSFER</helpText>
                </section>
            </page>
            <xi:include href="fundstransfer_prglposting_tab.xml" xmlns:xi="http://www.w3.org/2003/XInclude"/>
        </pages>
    </view>
    <helpfile></helpfile>
</ROOT>
