<?php
/**
 * Data object to process the financial account transaction records.
 *
 * <AUTHOR>
 * @copyright 2018 Sage Intacct Inc., All Rights Reserved
 */
class FinAcctTxnRecordDTO extends BaseDTO
{
    /* @var string $recordNo */
    private $recordNo;
    /* @var string $financialEntity */
    private $financialEntity;
    /* @var string $finAcctTxnFeedKey */
    private $finAcctTxnFeedKey;
    /* @var string $transactionId */
    private $transactionId;
    /* @var string $acctReconNo */
    private $acctReconNo;
    /* @var string $postingDate */
    private $postingDate;
    /* @var string $trxType */
    private $trxType;
    /* @var string $docType */
    private $docType;
    /* @var string $docNo */
    private $docNo;
    /* @var string $payee */
    private $payee;
    /* @var float|string $amount */
    private $amount;
    /* @var string $description */
    private $description;
    /* @var string $cleared */
    private $cleared;
    /* @var  string $whenCreated */
    private $whenCreated;
    /* @var  string $whenModified */
    private $whenModified;
    /* @var  string $createdBy */
    private $createdBy;
    /* @var  string $modifiedBy */
    private $modifiedBy;

    /**
     * BankReconrdsDTO constructor.
     *
     * @param array $arrParams Array of attributes
     */
    public function __construct(array $arrParams)
    {
        // set the base array params value
        $this->arrayParams = $arrParams;
        $this->recordNo = $arrParams['RECORDNO'] ?? null;
        $this->financialEntity = $arrParams['FINANCIALENTITY'];
        $this->finAcctTxnFeedKey = $arrParams['FINACCTTXNFEEDKEY'];
        $this->transactionId = $arrParams['TRANSACTIONID'];
        $this->acctReconNo = $arrParams['BANKACCTRECONKEY'];
        $this->postingDate = $arrParams['POSTINGDATE'];
        $this->trxType = $arrParams['TRANSACTIONTYPE'];
        $this->docType = $arrParams['DOCTYPE'];
        $this->docNo = $arrParams['DOCNO'];
        $this->payee = $arrParams['PAYEE'];
        $this->amount = $arrParams['AMOUNT'];
        $this->description = $arrParams['DESCRIPTION'];
        $this->cleared = $arrParams['CLEARED'];
        $this->whenCreated = $arrParams['WHENCREATED'] ?? null;
        $this->whenModified = $arrParams['WHENMODIFIED'] ?? null;
        $this->createdBy = $arrParams['CREATEDBY'] ?? null;
        $this->modifiedBy = $arrParams['MODIFIEDBY'] ?? null;
    }

    /**
     * @return string
     */
    public function getRecordNo()
    {
        return $this->recordNo;
    }

    /**
     * @param string $recordNo
     */
    public function setRecordNo(string $recordNo)
    {
        $this->recordNo = $recordNo;
    }

    /**
     * @return string
     */
    public function getFinancialEntity()
    {
        return $this->financialEntity;
    }

    /**
     * @param string $financialEntity
     */
    public function setFinancialEntity(string $financialEntity)
    {
        $this->financialEntity = $financialEntity;
    }

    /**
     * @return string
     */
    public function getFinAcctTxnFeedKey()
    {
        return $this->finAcctTxnFeedKey;
    }

    /**
     * @param string $finAcctTxnFeedKey
     */
    public function setFinAcctTxnFeedKey(string $finAcctTxnFeedKey)
    {
        $this->finAcctTxnFeedKey = $finAcctTxnFeedKey;
    }

    /**
     * @return string
     */
    public function getTransactionId()
    {
        return $this->transactionId;
    }

    /**
     * @param string $transactionId
     */
    public function setTransactionId(string $transactionId)
    {
        $this->transactionId = $transactionId;
    }

    /**
     * @return string
     */
    public function getAcctReconNo()
    {
        return $this->acctReconNo;
    }

    /**
     * @param string $acctReconNo
     */
    public function setAcctReconNo(string $acctReconNo)
    {
        $this->acctReconNo = $acctReconNo;
    }

    /**
     * @return string
     */
    public function getPostingDate()
    {
        return $this->postingDate;
    }

    /**
     * @param string $postingDate
     */
    public function setPostingDate(string $postingDate)
    {
        $this->postingDate = $postingDate;
    }

    /**
     * @return string
     */
    public function getTrxType()
    {
        return $this->trxType;
    }

    /**
     * @param string $trxType
     */
    public function setTrxType(string $trxType)
    {
        $this->trxType = $trxType;
    }

    /**
     * @return string
     */
    public function getDocType()
    {
        return $this->docType;
    }

    /**
     * @param string $docType
     */
    public function setDocType(string $docType)
    {
        $this->docType = $docType;
    }

    /**
     * @return string
     */
    public function getDocNo()
    {
        return $this->docNo;
    }

    /**
     * @param string $docNo
     */
    public function setDocNo(string $docNo)
    {
        $this->docNo = $docNo;
    }

    /**
     * @return string
     */
    public function getPayee()
    {
        return $this->payee;
    }

    /**
     * @param string $payee
     */
    public function setPayee(string $payee)
    {
        $this->payee = $payee;
    }

    /**
     * @return float|string
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * @param float|string $amount
     */
    public function setAmount($amount)
    {
        $this->amount = $amount;
    }

    /**
     * @return string
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * @param string $description
     */
    public function setDescription(string $description)
    {
        $this->description = $description;
    }

    /**
     * @return string
     */
    public function getCleared()
    {
        return $this->cleared;
    }

    /**
     * @param string $cleared
     */
    public function setCleared(string $cleared)
    {
        $this->cleared = $cleared;
    }

    /**
     * @return string
     */
    public function getWhenCreated()
    {
        return $this->whenCreated;
    }

    /**
     * @param string $whenCreated
     */
    public function setWhenCreated(string $whenCreated)
    {
        $this->whenCreated = $whenCreated;
    }

    /**
     * @return string
     */
    public function getWhenModified()
    {
        return $this->whenModified;
    }

    /**
     * @param string $whenModified
     */
    public function setWhenModified(string $whenModified)
    {
        $this->whenModified = $whenModified;
    }

    /**
     * @return string
     */
    public function getCreatedBy()
    {
        return $this->createdBy;
    }

    /**
     * @param string $createdBy
     */
    public function setCreatedBy(string $createdBy)
    {
        $this->createdBy = $createdBy;
    }

    /**
     * @return string
     */
    public function getModifiedBy()
    {
        return $this->modifiedBy;
    }

    /**
     * @param string $modifiedBy
     */
    public function setModifiedBy(string $modifiedBy)
    {
        $this->modifiedBy = $modifiedBy;
    }

    /**
     * Returns the values of the object.
     *
     * @return array
     */
    public function getValues()
    {
        $this->arrayParams['RECORDNO'] = $this->recordNo;
        $this->arrayParams['FINANCIALENTITY'] = $this->financialEntity;
        $this->arrayParams['FINACCTTXNFEEDKEY'] = $this->finAcctTxnFeedKey;
        $this->arrayParams['TRANSACTIONID'] = $this->transactionId;
        $this->arrayParams['BANKACCTRECONKEY'] = $this->acctReconNo;
        $this->arrayParams['POSTINGDATE'] = $this->postingDate;
        $this->arrayParams['TRANSACTIONTYPE'] = $this->trxType;
        $this->arrayParams['DOCTYPE'] = $this->docType;
        $this->arrayParams['DOCNO'] = $this->docNo;
        $this->arrayParams['PAYEE'] = $this->payee;
        $this->arrayParams['AMOUNT'] = $this->amount;
        $this->arrayParams['DESCRIPTION'] = $this->description;
        $this->arrayParams['CLEARED'] = $this->cleared;
        $this->arrayParams['WHENCREATED'] = $this->whenCreated;
        $this->arrayParams['WHENMODIFIED'] = $this->whenModified;
        $this->arrayParams['CREATEDBY'] = $this->createdBy;
        $this->arrayParams['MODIFIEDBY'] = $this->modifiedBy;
        return $this->arrayParams;
    }
}