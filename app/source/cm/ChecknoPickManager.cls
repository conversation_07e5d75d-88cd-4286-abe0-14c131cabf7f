<?
/**
*    FILE: checknopickmanager.cls
*    AUTHOR: 
*    DESCRIPTION:
*
*    (C) 2000, Intacct Corporation, All Rights Reserved
*
*    This document contains trade secret data that belongs to Intacct
*    Corporation and is protected by the copyright laws.  Information
*    herein may not be used, copied or disclosed in whole or in part
*    without prior written consent from Intacct Corporation.
*/

class ChecknoPickManager extends  EntityManager
{
    /**
     * @param array $params
     * @param bool  $_crosscny
     * @param bool  $nocount
     *
     * @return array
     */
    function GetList($params = [], $_crosscny = false, $nocount = true)
    {

        $list_of_recs = $this->GetCheckNoPickRecords($params);
        $newResult = array();
 
        foreach ($list_of_recs as $i => $row) {
            foreach($row as $key => $column) {
                $header = $key;
                if ($header) {
                    $newResult[$i][strval($header)] = $column;
                }
            }
        }
     
        $newResult = $this->_TransformFlatValuesToExternal($newResult);
        return ($newResult);
    }

    /**
     * @param array $params parameters for the query
     *
     * @return int
     */
    function GetCount($params=null)
    {
          $no_of_recs = $this->GetCheckNoPickRecords();

        return  count($no_of_recs);
    }

    /**
     * @param array $params
     *
     * @return string[][]|bool
     */
    function GetCheckNoPickRecords($params=array())
    {
        $mod = Request::$r->_mod;
        if (!is_array($params)) {
            // OTHERWISE, $params['orders'][0][0]  WILL KILL PHP.
            $params = array();
        }

        // Set the range of rows to retrieve
        if (isset($params['start'])) {
            $start = $params['start'];
        }
        if (isset($params['max'])) {
            $max = $params['max'];
        }

        // THIS IS A TEMPORARY KLUDGE: WE NEED TO FIGURE OUT HOW TO DO THIS WITHOUT MODIFYING THE CONSTANT
        $orderCol = $params['orders'][0][0];
        $orderDir = $params['orders'][0][1];
        include 'checknopick.qry';
        if ($mod == 'ap') {
            $qKey = 'QRY_CHECKNOPICK_AP';
        } else if ( in_array($mod, array('ee')) ) {
            $qKey = 'QRY_CHECKNOPICK_EE';
        } else {
            $qKey = 'QRY_CHECKNOPICK_ALL';
        }
        /** @noinspection PhpUndefinedVariableInspection */
        $origQuery = $kchecknopickQueries[$qKey]['QUERY'];
        if ($params) {
            $kchecknopickQueries[$qKey]['QUERY'] = "$origQuery order by $orderCol $orderDir";
        }
        $queryName = $kchecknopickQueries[$qKey];
        /** @noinspection PhpUndefinedVariableInspection */
        $result = $this->_QM->DoCustomQuery($queryName, array(), true, intval($start), intval($max));
        // AT LEAST WE'LL SET IT BACK
        $kchecknopickQueries[$qKey]['QUERY'] = $origQuery;

        return $result;   
    }
}
 

