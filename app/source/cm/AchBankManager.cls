<?
//	FILE:		ACHBankManager.cls
//	AUTHOR:		<PERSON><PERSON><PERSON><PERSON>
//	DESCRIPTION:	Manager for ACH Bank Configuration
//
//	(C)2012, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
// 

class AchbankManager extends EntityManager
{
    /**
     * @param array $params
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
    }

    /**
     * This function Adds the ACH Bank Configuration.
     *
     * @param array $values
     *
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        global $gErr;
        $source = "AchbankManager::Add";

        $ok = $this->_QM->beginTrx($source);
        $ok = $ok && $this->Validate($values);
        $ok = $ok && $this->Translate($values);
        $ok = $ok && parent::regularAdd($values);

        $ok = $ok && $this->_QM->commitTrx($source);
        if ( !$ok ) {
            $msg = _("Could not create Bank Account!");
            $gErr->addError('BL01001973', __FILE__ . ':' . __LINE__, $msg);
            epp("$source: Error: $msg");
            $this->_QM->rollbackTrx($source);
        }
        return $ok;
    }
    /**
    * This function updates the ACH Bank Configuration.
    *
    * @param array $values
    * 
    * @return bool
    */
    protected function regularSet(&$values)
    {
        global $gErr;
        $source = "AchbankManager::Set";
        
        $ok = $this->_QM->beginTrx($source);
        $ok = $ok && $this->Validate($values);
        $ok = $ok && $this->Translate($values);
        $ok = $ok && parent::regularSet($values);

        $ok = $ok && $this->_QM->commitTrx($source);
        if ( !$ok ) {
            $msg = _("Could not update Bank Account!");
            $gErr->addError('BL01001973', __FILE__ . ':' . __LINE__, $msg);
            epp("$source: Error: $msg");
            $this->_QM->rollbackTrx($source);
        }
        
        return $ok;
    }

    /**
     * This function delets the ACH Bank Configuration.
     *
     * @param int|string $ID
     *
     * @return bool
     */
    function Delete($ID)
    {
        global $gErr;
        $source = "AchbankManager::Delete";

        $ok = $this->_QM->beginTrx($source);
        $ok = $ok && parent::Delete($ID);

        $ok = $ok && $this->_QM->commitTrx($source);
        if ( !$ok ) {
            $msg = _("Could not Delete Bank Account!");
            $gErr->addError('BL01001973', __FILE__ . ':' . __LINE__, $msg);
            epp("$source: Error: $msg");
            $this->_QM->rollbackTrx($source);
        }
        
        return $ok;
    }

    /**
     * This function Gets the ACH Bank Configuration.
     *
     * @param array $qparams
     *
     * @return string
     */
    public function GetAchBankDetails($qparams)
    {
        $recordid = $qparams['achbankid'];
        $params['selects'] = array('DESTINATIONID', 'ORIGINID', 'ORIGINNAME');
        $params['filters'] = array(array(array('achbankid', '=', $recordid)));

        $results = $this->GetList($params);

        $xmlStr = "<achbank destinationid = \"" . $results[0]['DESTINATIONID'] . "\"  originid = \"" . $results[0]['ORIGINID'] . "\" originname = \"" . $results[0]['ORIGINNAME'] . "\"></achbank>";

        return $xmlStr;
    }

    /**
     * This function Validates the ACH Bank Configuration.
     *
     * @param array $values
     *
     * @return bool
     */
    function Validate(&$values)
    {
        global $gErr;
        $ok = true;
        if ( isset($values['RECORDTYPECODE']) && $values['RECORDTYPECODE'] != '' && $values['RECORDTYPECODE'] != '1' ) {
            $gErr->addError('BL03000046', __FILE__ . ':' . __LINE__, " Invalid Data for Field: Record Type Code.The value should be always 1. ");
            $ok = false;
        }
        if ( isset($values['PRIORITYCODE']) && $values['PRIORITYCODE'] != '' && $values['PRIORITYCODE'] != '01' ) {
            $gErr->addError('BL03000046', __FILE__ . ':' . __LINE__, " Invalid Data for Field: Priority Code.The value should be always 01. ");
            $ok = false;
        }

        if ( isset($values['FILEIDMODIFIER']) && $values['FILEIDMODIFIER'] != '' && !preg_match("/^[ A-Z0-9]{1}$/", isl_trim($values['FILEIDMODIFIER'])) ) {
            $gErr->addError('BL03000046', __FILE__ . ':' . __LINE__, " Invalid Data for Field: File ID Modifier. ");
            $ok = false;
        }
        if ( isset($values['RECORDSIZE']) && $values['RECORDSIZE'] != '' && !preg_match("/^[0-9]{1,3}$/", isl_trim($values['RECORDSIZE'])) ) {
            $gErr->addError('BL03000046', __FILE__ . ':' . __LINE__, " Invalid Data for Field: Record Size. ");
            $ok = false;
        }
        if ( isset($values['BLOCKINGFACTOR']) && $values['BLOCKINGFACTOR'] != '' && !preg_match("/^[0-9]{1,2}$/", isl_trim($values['BLOCKINGFACTOR'])) ) {
            $gErr->addError('BL03000046', __FILE__ . ':' . __LINE__, " Invalid Data for Field: Blocking Factor. ");
            $ok = false;
        }
        if ( isset($values['FORMATCODE']) && $values['FORMATCODE'] != '' && !preg_match("/^[0-9]{1,2}$/", isl_trim($values['FORMATCODE'])) ) {
            $gErr->addError('BL03000046', __FILE__ . ':' . __LINE__, " Invalid Data for Field: Format Code. ");
            $ok = false;
        }

        return $ok;
    }

    /**
     * This function Translates the field values of ACH Bank Configuration.
     *
     * @param array $values
     *
     * @return bool
     */
    public function Translate(&$values) 
    {
        $ok = true;
       
        $values['RECORDTYPECODE'] = 1;
        $values['PRIORITYCODE'] = '01';
        $values['FILECREATIONDATE'] = 'YYMMDD';
        $values['FILECREATIONDATETIME'] = 'HHMM';
        $values['FILEIDMODIFIER'] = 'A';
        $values['RECORDSIZE'] = '094';
        $values['BLOCKINGFACTOR'] = 10;
        $values['FORMATCODE'] = 1;
  
        return $ok;
    }

}
