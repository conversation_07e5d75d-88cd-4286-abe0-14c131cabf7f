<?php

/**
 * Manager class for PaymentProvider object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Intacct Corporation, All Rights Reserved
 */

class PaymentProviderManager extends EntityManager
{

    public const SSF_PAYMETHOD_KEY = 16;
    public const MODE_CREATE = 'C';
    public const MODE_UPDATE = 'U';
    public const STATE_REQ_INITIATED = 'Request initiated';
    public const STATE_INPROGRESS = 'In progress';
    public const STATE_RECIEVED = 'Request received';
    public const STATE_FAILED = 'Request failed';
    public const STATE_AWAIT_AUTH = 'Awaiting authorization';
    public const STATE_SUBSCRIBED = 'Subscribed';
    public const STATE_CANCELLED = 'Cancelled';
    public const STATE_SUSPENDED = 'Suspended';
    public const STATE_PENDING = 'Pending';
    public const SSF_RECEIVED = 'Received';
    public const SSF_AWAIT_AUTH = 'AwaitingAuthorisation';
    public const SSF_AUTHORIZED = 'Authorised';
    public const SSF_CANCELLED = 'Cancelled';
    public const SSF_REJECTED = 'Rejected';
    public const SSF_SUSPENDED = 'Suspended';
    public const SSF_PENDING = 'Pending';
    public const PAYMENT_TYPE_LUMPSUM = 'lumpSum';
    public const PAYMENT_TYPE_LUMPSUM_TRANSLATED = 'L';
    public const PAYMENT_TYPE_PER_TRANSACTION_TRANSLATED = 'P';
    public const API_PATH = 'Subscriptions';

    /**
     * @param array $values
     *
     * @return bool
     */
    function regularAdd(&$values)
    {
        $source = __CLASS__ . "::" . __FUNCTION__;

        $ok = $this->beginTrx($source);

        $ok = $ok && $this->translate($values, self::MODE_CREATE);
        $ok = $ok && parent::regularAdd($values);

        if ( $ok && $values['STATE'] == self::STATE_INPROGRESS ) {
            try {
                $this->subscribe($values['RECORDNO'], $values['GUID'], $values['PROVIDER_ADDITIONAL_FIELDS']);
            } catch ( Exception $e ) {
                $msg = 'Payment Provider subscription failed.';
                Globals::$g->gErr->addError('EP-0040', __FILE__ . ":" . __LINE__, $msg);
                $ok = false;
            }
        }

        if ( $ok ) {
            $ok = $this->commitTrx($source);
        }

        if ( ! $ok ) {
            $this->rollbackTrx($source);
        }

        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    function regularSet(&$values)
    {
        $source = __CLASS__ . "::" . __FUNCTION__;

        $ok = $this->beginTrx($source);

        $ok = $ok && $this->translate($values, self::MODE_UPDATE);

        if ( $values['STATE'] == self::STATE_FAILED ) {
            try {
                $this->subscribe($values['RECORDNO'], $values['GUID'], $values['PROVIDER_ADDITIONAL_FIELDS']);
                $values['STATE'] = self::STATE_INPROGRESS;
            } catch ( Exception $e ) {
                $msg = 'Payment Provider subscription failed.';
                Globals::$g->gErr->addError('EP-0040', __FILE__ . ":" . __LINE__, $msg);
                $ok = false;
            }
        }

        $ok = $ok && parent::regularSet($values);

        if ( $ok ) {
            $ok = $this->commitTrx($source);
        }

        if ( ! $ok ) {
            $this->rollbackTrx($source);
        }

        return $ok;
    }

    /**
     * @param int|string $ID
     *
     * @return bool
     */
    public function delete($ID)
    {
        $gErr = Globals::$g->gErr;

        $msg = "delete method not allowed on this object...";
        $gErr->addError('EP-0009', __FILE__ . ':' . __LINE__, $msg);

        return false;
    }

    /**
     * @param array  $values
     * @param string $mode
     *
     * @return bool
     */
    public function translate(array &$values, string $mode)
    {
        $ok = true;
        $providerDetail = [];

        try {
            $wpbProviderProcessor = new WPBProviderProcessor();
            $providerDetail =
                $wpbProviderProcessor->getPaymentProvider($values['GUID'], $values['COUNTRYCODE']);
        } catch ( Exception $e ) {
            $msg = 'Unable to fetch Payment Provider details.';
            Globals::$g->gErr->addError('EP-0041', __FILE__ . ":" . __LINE__, $msg);
            $ok = false;
        }
        $values['PROVIDERID'] = $providerDetail['ID'];
        $values['NAME'] = $providerDetail['NAME'];
        $values['IAPAYMETHODKEY'] = self::SSF_PAYMETHOD_KEY;
        if ( ! isset($values['DELETED']) ) {
            $values['DELETED'] = 'F';
        }
        if ( $mode == self::MODE_CREATE ) {
            if ( ! WPBSetupManager::isSubscribed() || WPBSetupManager::isOrgUpdateInProgress() ) {
                $values['STATE'] = self::STATE_REQ_INITIATED;
            } else {
                $values['STATE'] = self::STATE_INPROGRESS;
            }
            //All payment methods will be automatically enrolled during the provider setup.
            $values['PROVIDERPAYMENTMETHOD'] =
                ProviderPaymentMethodManager::filterAndFormatIntSupportedPayMethod($providerDetail['PAYMETHODS']);;
        }
        $foundPaymethod = false;
        foreach ( $values['PROVIDERPAYMENTMETHOD'] as $providerPaymethod ) {
             if ( $providerPaymethod['STATUS'] == 'active' ) {
                $foundPaymethod = true;
                break;
            }
        }
        if ( ! $foundPaymethod ) {
            $msg = 'At least one payment method must be active for this payment provider.';
            Globals::$g->gErr->addError('EP-0095', __FILE__ . ":" . __LINE__, $msg);
            $ok = false;
        }
        $paymentWithdrawalType = $values['PAYMENTWITHDRAWALTYPE'] ?? '';
        $userFirstName = $values['USERFIRSTNAME'] ?? '';
        $userLastName = $values['USERLASTNAME'] ?? '';
        $additonalData = $this->getAdditionalData($providerDetail, $paymentWithdrawalType, $values['USEREMAIL'], $userFirstName, $userLastName);
        $subscriptionJsonPaths = array_keys($additonalData);
        foreach ( $providerDetail['FIELDS'] as $field ) {
            if ( $field['APIPATH'] == self::API_PATH && in_array($field['JSONPATH'], $subscriptionJsonPaths)
                && ! PaymentProviderHandler::validateField($field, $additonalData[$field['JSONPATH']], '', '') ) {
                $ok = false;
            }
        }
        $values['PROVIDER_ADDITIONAL_FIELDS'] = $additonalData;

        return $ok;
    }

    /**
     * @param array  $providerMeta
     * @param string $paymentWithdrawalType
     * @param string $userEmail
     * @param string $userFirstName
     * @param string $userLastName
     *
     * @return array
     */
    private function getAdditionalData(array $providerMeta, string $paymentWithdrawalType, string $userEmail, string $userFirstName, string $userLastName)
    {
        $subscriptionFieldMap = PaymentProviderHandler::getProviderFieldMap(PaymentProviderHandler::TYPE_SUBSCRIPTION);
        $additonalData = [];
        if ( ! empty($providerMeta['FIELDS']) ) {
            foreach ( $providerMeta['FIELDS'] as $field ) {
                if ( $field['APIPATH'] == self::API_PATH ) {
                    if ( $subscriptionFieldMap[$field['JSONPATH']] == 'COMPANYNAME' ) {
                        $additonalData[$field['JSONPATH']] = GetMyCompanyName();
                    } else if ( $subscriptionFieldMap[$field['JSONPATH']] == 'COMPANYID' ) {
                        $additonalData[$field['JSONPATH']] = GetMyCompanyTitle();
                    } else if($subscriptionFieldMap[$field['JSONPATH']] == 'USEREMAIL') {
                        $additonalData[$field['JSONPATH']] = $userEmail;
                    } else if($subscriptionFieldMap[$field['JSONPATH']] == 'USERFIRSTNAME') {
                        $additonalData[$field['JSONPATH']] = $userFirstName;
                    } else if($subscriptionFieldMap[$field['JSONPATH']] == 'USERLASTNAME') {
                        $additonalData[$field['JSONPATH']] = $userLastName;
                    } else if($subscriptionFieldMap[$field['JSONPATH']] == 'PAYMENTWITHDRAWALTYPE') {
                        $additonalData['additionalData.company.debitByCheckNumber'] = in_array($paymentWithdrawalType,
                            [ self::PAYMENT_TYPE_LUMPSUM_TRANSLATED,
                                self::PAYMENT_TYPE_LUMPSUM ]) ? "FALSE"
                            : "TRUE";
                    }
                }
            }
        }

        return $additonalData;
    }

    /**
     * @param array $providerFields
     *
     * @return string[]
     */
    private function getAllProviderJsonPaths(array $providerFields)
    {
        $jsonPaths = [];
        foreach ( $providerFields as $providerField ) {
            $jsonPaths[] = $providerField['JSONPATH'];
        }

        return $jsonPaths;
    }

    /**
     * @param int    $recordNo
     * @param string $providerGUId
     * @param array  $additionalFields
     *
     * @throws Exception
     */
    public function subscribe(int $recordNo, string $providerGUId, array $additionalFields )
    {
        $source = __CLASS__ . "::" . __FUNCTION__;

        $ok = $this->beginTrx($source);

        $orgId = WPBSetupManager::getWPBOrgId();
        $factory = new IaSbcFactory(IaSbcFactory::SCOPE_PAYMENTS, $orgId);
        $request = $factory->getIaSbcRequest();
        $wpbEPSubMgr = new WPBEPSubscriptionProcessor($request, $orgId);
        $wpbEPSubMgr->subscribe($recordNo, $providerGUId, $additionalFields);

        if ( $ok ) {
            $ok = $this->commitTrx($source);
        }

        if ( ! $ok ) {
            $this->rollbackTrx($source);
        }
    }

    /**
     * @param string $providerGUId
     *
     * @throws IAException
     */
    public function unSubscribe(string $providerGUId)
    {
        $providerId = PaymentProviderHandler::getProviderIdFromGUID($providerGUId);
        throw IAException::newIAException('EP-0081', "Un-subscribe is not allowed for the provider $providerId. You can mark as Suspended", ['PROVIDER_ID' => $providerId]);
    }

    /**
     * @param string $providerGUId
     *
     * @throws IAException
     */
    public function suspendProvider(string $providerGUId)
    {
        $values = $this->getValuesByProviderGUId($providerGUId);
        if ( empty($values) ) {
            throw new IAException("Provider ID is not valid.", 'EP-0066');
        }
        if ( $values['STATE'] != self::STATE_SUBSCRIBED ) {
            throw IAException::newIAException('EP-0064', "Cannot suspend a payment provider record when the state is not in '"
                . self::STATE_SUBSCRIBED . "'", ['STATE_SUBSCRIBED' => self::STATE_SUBSCRIBED]);
        }
        if ( $values['SUBSCRIPTIONID'] == '' ) {
            throw new IAException("Cannot suspend a payment provider record when subscriptionid is null.", 'EP-0065');
        }

        $orgId = WPBSetupManager::getWPBOrgId();
        $factory = new IaSbcFactory(IaSbcFactory::SCOPE_PAYMENTS, $orgId);
        $request = $factory->getIaSbcRequest();
        $wpbEPSubMgr = new WPBEPSubscriptionProcessor($request, $orgId);
        $wpbEPSubMgr->suspend($values['RECORDNO'], $values['GUID'], $values['SUBSCRIPTIONID']);
        $this->updateState($values['RECORDNO'], self::STATE_INPROGRESS);
    }


    /**
     * @param string $providerGUId
     *
     * @throws IAException
     */
    public function unSuspendProvider(string $providerGUId)
    {
        $values = $this->getValuesByProviderGUId($providerGUId);
        if ( empty($values) ) {
            throw new IAException("Provider ID is not valid.", 'EP-0066');
        }
        if ( $values['STATE'] != self::STATE_SUSPENDED ) {
            throw IAException::newIAException('EP-0070',"Cannot unsuspend a payment provider record when the state is not in '"
                                  . self::STATE_SUSPENDED . "'", ['STATE_SUBSCRIBED' => self::STATE_SUBSCRIBED]);
        }
        if ( $values['SUBSCRIPTIONID'] == '' ) {
            throw new IAException("Cannot unsuspend a payment provider record when subscriptionid is null.", 'EP-0071');
        }

        $orgId = WPBSetupManager::getWPBOrgId();
        $factory = new IaSbcFactory(IaSbcFactory::SCOPE_PAYMENTS, $orgId);
        $request = $factory->getIaSbcRequest();
        $wpbEPSubMgr = new WPBEPSubscriptionProcessor($request, $orgId);
        $wpbEPSubMgr->unsuspend($values['RECORDNO'], $values['GUID'], $values['SUBSCRIPTIONID']);
        $this->updateState($values['RECORDNO'], self::STATE_INPROGRESS);
    }

    /**
     * @param string $providerGUId
     *
     * @throws IAException
     */
    public function refreshStatus(string $providerGUId)
    {
        $values = $this->getValuesByProviderGUId($providerGUId);
        if ( empty($values) || $values['SUBSCRIPTIONID'] == '' ) {
            throw new IAException("Provider ID is not valid.", 'EP-0066');
        }
        if ( in_array($values['STATE'], [ self::STATE_INPROGRESS, self::STATE_SUBSCRIBED ]) ) {
            throw IAException::newIAException('EP-0073',"Can't refresh when the status is in '" . $values['STATE'] . "'", ['VALUES_STATE' => $values['STATE']]);
        }

        $orgId = WPBSetupManager::getWPBOrgId();
        $factory = new IaSbcFactory(IaSbcFactory::SCOPE_PAYMENTS, $orgId);
        $request = $factory->getIaSbcRequest();
        $wpbEPSubMgr = new WPBEPSubscriptionProcessor($request, $orgId);
        $subscriptionResponse = $wpbEPSubMgr->get($values['SUBSCRIPTIONID'], $values['RECORDNO']);
        $currentValues = $this->get($values['RECORDNO']);
        $currentValues['STATE'] = $this->translateSubscriptionStatus($subscriptionResponse['STATUS']);
        if ( $subscriptionResponse['AUTHURL'] != '' ) {
            $currentValues['AUTHURL'] = $subscriptionResponse['AUTHURL'];
        }
        
        OPSSetupManager::storeActionLog(ActionLogManager::LOG_TYPE_SUCCESS,
            'Get Provider Subscription Status', json_encode($subscriptionResponse),
            ActionLogManager::OBJ_TYPE_PYMT_PROVIDER,
            $values['RECORDNO'],
            $values['RECORDNO']);

        if ( ! $this->regularSet($currentValues) ) {
            throw new IAException("Unable to update Payment provider susbcription record.", 'EP-0072');
        }
    }

    /**
     * @param string $providerGUId
     *
     * @return array
     */
    public function getValuesByProviderGUId(string $providerGUId)
    {
        if ( $providerGUId == '' ) {
            return [];
        }

        $currValues = [];
        $params = [
            'filters' =>
                [
                    [
                        [ 'GUID', '=', $providerGUId ],
                    ],
                ],
        ];

        $providerRes = $this->GetList($params);
        if ( ! empty($providerRes) ) {
            $currValues = $providerRes[0];
        }

        return $currValues;
    }

    /**
     * @param string $providerId
     *
     * @return array
     */
    public function getValuesByProviderID(string $providerId)
    {
        $providerValues = [];
        $params = [
            'filters' =>
                [
                    [
                        [ 'PROVIDERID', '=', $providerId ],
                        [ 'STATE', '=', self::STATE_SUBSCRIBED ],
                    ],
                ],
        ];

        $providerRes = $this->GetList($params);
        if ( ! empty($providerRes) ) {
            $providerValues = $providerRes[0];
        }

        return $providerValues;
    }

    /**
     * @return array[]
     */
    public static function getActiveTenantProviders()
    {
        $paymentProviderMgr = Globals::$g->gManagerFactory->getManager('paymentprovider');

        $params = [
            'selects' => [ 'GUID' ],
            'filters' =>
                [
                    [
                        [ 'STATE', '!=', self::STATE_SUSPENDED ],
                    ],
                ],
        ];
        $providerRes = $paymentProviderMgr->GetList($params);
        $activeProviders = [];
        if ( ! empty($providerRes) ) {
            foreach ( $providerRes as $activeProvider ) {
                $activeProviders[] = $activeProvider['GUID'];
            }
        }

        return $activeProviders;
    }

    /**
     * @return array[]
     */
    public static function getTenantProviders()
    {
        $paymentProviderMgr = Globals::$g->gManagerFactory->getManager('paymentprovider');

        $params = [
            'selects' => [ 'PROVIDERID', 'RECORDNO', 'NAME', 'GUID', 'COUNTRYCODE' ],
        ];
        $providerRes = $paymentProviderMgr->GetList($params);
        $tenantProviders = [];
        if ( ! empty($providerRes) ) {
            foreach ( $providerRes as $provider ) {
                $tenantProviders[] = [ 'PROVIDERID' => $provider['PROVIDERID'],
                                       'RECORDNO'   => $provider['RECORDNO'],
                                       'NAME'       => $provider['NAME'],
                                       'GUID'       => $provider['GUID'],
                                       'COUNTRYCODE' => $provider['COUNTRYCODE'],
                ];
            }
        }

        return $tenantProviders;
    }

    /**
     * @return array[]
     */
    public static function getSubscribedProviders()
    {
        $paymentProviderMgr = Globals::$g->gManagerFactory->getManager('paymentprovider');

        $params = [
            'selects' => [ 'RECORDNO', 'NAME', 'PROVIDERID', 'SUBSCRIPTIONID', 'COUNTRYCODE',
                           'ENABLEAUTOVENDORONBOARD', 'GUID', 'STATE' ],
            'filters' =>
                [
                    [
                        [ 'STATE', 'IN', [self::STATE_SUBSCRIBED, self::STATE_PENDING] ],
                    ],
                ],
        ];

        return $paymentProviderMgr->GetList($params);
    }

    /**
     * @param int    $recordNo
     * @param string $subscriptionId
     * @param string $state
     *
     * @throws IAException
     */
    public function updateIDAndState(int $recordNo, string $subscriptionId, string $state)
    {
        $currentValues = $this->get($recordNo);
        $currentValues['SUBSCRIPTIONID'] = $subscriptionId;
        $currentValues['STATE'] = $state;
        if ( ! $this->regularSet($currentValues) ) {
            throw new IAException("Unable to save Payment provider susbcription POST API response.", 'EP-0067');
        }
    }

    /**
     * @param int    $recordNo
     * @param string $state
     *
     * @throws IAException
     */
    public function updateState(int $recordNo, string $state)
    {
        $currentValues = $this->get($recordNo);
        $currentValues['STATE'] = $state;
        if ( ! $this->regularSet($currentValues) ) {
            throw new IAException("Unable to save Payment provider susbcription POST API response.", 'EP-0067');
        }
    }

    /**
     * @param array $values
     *
     * @throws IAException
     */
    public function markAsDeleted(array $values)
    {
        $values['DELETED'] = 'T';
        if ( ! $this->regularSet($values) ) {
            throw new IAException("Unable to mark the provider as deleted.", 'EP-INT-0015');
        }
    }

    /**
     * @throws Exception
     */
    public function execPendingSubscriptionRequests()
    {
        $params = [
            'selects' => [ 'RECORDNO', 'GUID', 'COUNTRYCODE', 'PAYMENTWITHDRAWALTYPE', 'USEREMAIL', 'USERFIRSTNAME', 'USERLASTNAME' ],
            'filters' =>
                [
                    [
                        [ 'STATE', '=', self::STATE_REQ_INITIATED ],
                    ],
                ],
        ];
        $results = $this->GetList($params);
        $wpbProviderProcessor = new WPBProviderProcessor();
        if ( ! empty($results) ) {
            foreach ( $results as $row ) {
                $providerMeta = $wpbProviderProcessor->getPaymentProvider($row['GUID'], $row['COUNTRYCODE']);
                $paymentWithdrawalType = $row['PAYMENTWITHDRAWALTYPE'] ?? '';
                $userFirstName = $row['USERFIRSTNAME'] ?? '';
                $userLastName = $row['USERLASTNAME'] ?? '';
                $additonalData = $this->getAdditionalData($providerMeta, $paymentWithdrawalType, $row['USEREMAIL'], $userFirstName, $userLastName);
                $this->subscribe($row['RECORDNO'], $row['GUID'], $additonalData);
                $this->updateState($row['RECORDNO'], self::STATE_INPROGRESS);
            }
        }
    }


    /**
     * @param array $notification
     *
     * @throws IAException
     */
    public function processSubscriptionNotification(array $notification)
    {
        $notificationParties = $notification['notificationParties'];
        $subscriptionId = $notificationParties['subscriptionId'];
        $resource = $notification['resource'];
        $state = $resource['subscription']['data']['status'];
        $authURL = '';
        if ( isset($resource['subscription']['links']) && isset($resource['subscription']['links']['authorise']) ) {
            $authURL = $resource['subscription']['links']['authorise'];
        }

        $values = self::getProviderBySubscriptionId($subscriptionId);
        if ( empty($values) || $values[0]['RECORDNO'] == '' ) {
            throw new IAException("Invalid Subscription Id '{$subscriptionId}'. ", 'EP-INT-0001');
        }
        $providerRec = $this->get($values[0]['RECORDNO']);
        $providerRec['AUTHURL'] = $authURL;
        if ( $state == self::SSF_AUTHORIZED ) {
            $providerRec['STATE'] = self::STATE_SUBSCRIBED;
        } else if ( $state == self::SSF_AWAIT_AUTH ) {
            $providerRec['STATE'] = self::STATE_AWAIT_AUTH;
        } else if($state == self::SSF_PENDING ) {
            $providerRec['STATE'] = self::SSF_PENDING;
        }
        if ( ! $this->regularSet($providerRec) ) {
            throw new IAException("Unable to save paymentprovider record, RECORDNO:" . $providerRec['RECORDNO'],
                                  'EP-INT-0002');
        }

        //send Email notification to notify the subscription only when not subscribed already
        if ( $values[0]['STATE'] != self::STATE_SUBSCRIBED && $providerRec['STATE'] == self::STATE_SUBSCRIBED ) {
            $this->sendSubscriptionEmailNotification($providerRec);
        }

    }

    /**
     * @param array $providerRec
     *
     * @return bool
     */
    protected function sendSubscriptionEmailNotification($providerRec)
    {
        $providerId = $providerRec['PROVIDERID'];
        $providerName = $providerRec['NAME'];
        $providerEmail = $providerRec['USEREMAIL'];

        $host = "https://" . getFrontEndFullyQualifiedDomainName() . GetLocalizedDocsUrlPath();
        $setUrl = $host . PaymentProviderHandler::getProviderHelpDocMap($providerId, 'SETUPURL');
        $enableBanksUrl = $host . PaymentProviderHandler::getProviderHelpDocMap($providerId, 'SETUPACCOUNTSURL');
        $enableVendorUrl = $host . PaymentProviderHandler::getProviderHelpDocMap($providerId, 'SETUPVENDORSURL');

        $emailTemplate = 'IA.EMAIL.EP.NOTIFY_SUBSCRIPTION_TO_PROVIDER_SUCCESS';
        $companyManager = Globals::$g->gManagerFactory->getManager('company');
        $queryParams = [
            'selects' => [ 'TITLE', 'NAME' ],
            'filters' => [[
                ['RECORDNO', '=', GetMyCompany()],
            ]]
        ];
        $qryResult = $companyManager->GetList($queryParams);
        $companyInfo = $qryResult[0]['TITLE'];
        if ( $qryResult[0]['NAME'] ) {
            $companyInfo .= ' -- ' . $qryResult[0]['NAME'];
        }

        $params = [
            'PROVIDER_NAME' => $providerName,
            'SETUP_URL'   => $setUrl,
            'ENABLE_BANKS'=> $enableBanksUrl,
            'ENABLE_VENDORS' => $enableVendorUrl,
            'COMPANY_INFO' => $companyInfo
        ];

        // SEND THE EMAIL
        $email = I18NEmail::build($emailTemplate, $params);
        $email->contentType('text/html; charset="' . isl_get_charset() . '"');
        $email->from("Intacct<<EMAIL>>");
        $email->replyTo("<EMAIL>");
        $email->to($providerEmail);
        return $email->send();
    }

    /**
     * @param string $subscriptionId
     *
     * @return array[]
     */
    public static function getProviderBySubscriptionId(string $subscriptionId)
    {
        $paymentProviderMgr = Globals::$g->gManagerFactory->getManager('paymentprovider');

        $params = [
            'selects' => ['RECORDNO', 'NAME', 'PROVIDERID', 'SUBSCRIPTIONID', 'COUNTRYCODE',
                'ENABLEAUTOVENDORONBOARD'],
            'filters' =>
                [
                    [
                        ['SUBSCRIPTIONID', '=', $subscriptionId],
                    ],
                ],
        ];
        return $paymentProviderMgr->GetList($params);
    }

    /**
     * @param string $status
     *
     * @return string
     */
    private function translateSubscriptionStatus(string $status)
    {
        $statusMap = [
            self::SSF_RECEIVED   => self::STATE_RECIEVED,
            self::SSF_AWAIT_AUTH => self::STATE_AWAIT_AUTH,
            self::SSF_AUTHORIZED => self::STATE_SUBSCRIBED,
            self::SSF_CANCELLED  => self::STATE_CANCELLED,
            self::SSF_SUSPENDED  => self::STATE_SUSPENDED,
            self::SSF_REJECTED   => self::STATE_FAILED,
            self::SSF_PENDING    => self::STATE_PENDING,
        ];

        return $statusMap[$status];
    }

}
