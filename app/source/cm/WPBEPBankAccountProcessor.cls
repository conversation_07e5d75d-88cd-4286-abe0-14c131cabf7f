<?php

/**
 * A class to make consumer API request for bank account sync
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Intacct Corporation, All Rights Reserved
 */

/**
 * Class WPBEPBankAccountProcessor
 */
class WPBEPBankAccountProcessor
{

    const API_POST_ENDPOINT = '/api-msg-v1/debtoraccountputs';
    const METRIC_TAG_NAME = 'ep_enrol_bankaccount';
    const API_STATUS_ENDPOINT = '/api-msg-v1/debtoraccountlistqueries';
    const METRIC_STATUS_TAG_NAME = 'ep_status_bankaccount';

    /**
     * @var IaSbcRequestInterface $sbcRequest
     */
    private $sbcRequest;

    /** @var string $orgId */
    private $orgId;

    /** @var string $bankAccountId */
    private $bankAccountId;

    /**
     * WPBEPBankAccountProcessor constructor.
     *
     * @param IaSbcRequestInterface $IaSbcRequest
     * @param string                $orgId
     * @param string                $bankAccountId
     */
    public function __construct(IaSbcRequestInterface $IaSbcRequest, string $orgId, string $bankAccountId)
    {
        $this->sbcRequest = $IaSbcRequest;
        $this->bankAccountId = $bankAccountId;
        $this->orgId = $orgId;
    }

    /**
     * @return string
     */
    private function getSubscriptionURL()
    {
        return self::API_POST_ENDPOINT;
    }

    /**
     * @return string
     */
    private function getStatusURL()
    {
        return self::API_STATUS_ENDPOINT;
    }

    /**
     * @param int    $recordNo
     * @param string $providerId
     * @param array  $apiFieldValues
     *
     * @throws Exception
     */
    public function subscribe(int $recordNo, string $providerId, array $apiFieldValues)
    {
        $sbcIntegration = $this->sbcRequest->getIaSbcIntegration();
        $sbcIntegration->setIAMetricTag(self::METRIC_TAG_NAME);
        $headers = [ 'X-API-Key: ' . $sbcIntegration->getConsumerAPIKey(),
                     'X-idempotency-key: ' . $sbcIntegration->generateNonce() ];
        $payload = $this->generatePostPayload($providerId, $apiFieldValues);
        $this->sbcRequest->post($this->getSubscriptionURL(), $payload,
                                new WPBEPBankAccountPostResponseHandler($recordNo, $this->bankAccountId, $payload),
                                $headers);
    }

    /**
     * @param string $providerId
     * @param array  $values
     *
     * @return false|string
     */
    private function generatePostPayload(string $providerId, array $values)
    {
        $additionlData['providerSpecificProperties'] = [];
        $arr = [];
        $providerFieldMap =
            PaymentProviderHandler::getProviderFieldMap(PaymentProviderHandler::TYPE_DEBTOR, $providerId);
        foreach ( $providerFieldMap as $providerFieldkey => $providerFieldValue ) {
            if ( isset($values[$providerFieldValue]) && $values[$providerFieldValue] != '' ) {
                $arr[$providerFieldkey] = $values[$providerFieldValue];
            }
        }
        $requestBody = EntityManager::FlatToStructuredStatic($arr);
        $additionlData = INTACCTarray_merge($requestBody['additionalData'], $additionlData);
        $requestBody['additionalData'] = json_encode($additionlData);
        return  json_encode(['data' =>$requestBody]);
    }

    /**
     * @param array $values
     *
     * @throws Exception
     */
    public function checkStatus(array $values)
    {
        $sbcIntegration = $this->sbcRequest->getIaSbcIntegration();
        $sbcIntegration->setIAMetricTag(self::METRIC_STATUS_TAG_NAME);
        $headers = ['X-API-Key: ' . $sbcIntegration->getConsumerAPIKey(),
            'X-idempotency-key: ' . $sbcIntegration->generateNonce()];
        $payload = $this->generateStatusPayload($values);
        $this->sbcRequest->post($this->getStatusURL(), $payload,
            new WPBEPBankAccountStatusResponseHandler($values['RECORDNO'], $this->bankAccountId, $payload), $headers);
    }

    /**
     * @param array $values
     *
     * @return false|string
     * @throws Exception
     */
    private function generateStatusPayload(array $values)
    {
        $requestBody = PaymentProviderHandler::generateRequestBody(PaymentProviderHandler::TYPE_DEBTOR, $values);
        return  json_encode(['data' =>$requestBody]);
    }
}