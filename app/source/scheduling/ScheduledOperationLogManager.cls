<?
require_once 'backend_error.inc';
import('errorlogmanager');
class ScheduledOperationLogManager extends EntityManager
{
    /* @var ErrorLogManager $ErrorLogMgr */
    protected $ErrorLogMgr;
    private const localTokens = ['IA.SUCCESS','IA.IN_TRANSIT','IA.SCHEDULE_ERROR','IA.FAILED','IA.UNKNOWN'];
    /**
     * Gets a UI friendly state (status) string, used by various listers to report schedule status
     *
     * @param string|null $internalState (accepts null only because of legacy code)
     *
     * @return string
     */
    public static function getFriendlyState($internalState)
    {
        $tokens = I18N::getTokensForArray(I18N::tokenArrayToObjectArray(self::localTokens));
        switch ($internalState) {
            case 'S':
                return GT($tokens, 'IA.SUCCESS');
            case 'T':
                return GT($tokens, 'IA.IN_TRANSIT');
            case 'E':
                return GT($tokens, 'IA.SCHEDULE_ERROR');
            case 'X':
                return GT($tokens, 'IA.FAILED');
            default :
                return GT($tokens, 'IA.UNKNOWN');
        }
    }

    /**
     * @param array $params
     */
    function __construct($params = array())
    { 
        $this->ErrorLogMgr = Globals::$g->gManagerFactory->getManager('errorlog');
        parent::__construct($params);
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        $source = "ScheduledOperationLog::Add";
        global $gErr;

        $ok = $this->_QM->beginTrx($source);

        $values['LOGTYPE'] = SCHEDULEOPLOG;

        // Add the ErrorLogRecords if any
        if (array_key_exists('ERRORS', $values) && array_key_exists('WARNINGS', $values)) {
            if ((is_array($values['ERRORS']) && count($values['ERRORS']) > 0)
                || (is_array($values['WARNINGS']) && count($values['WARNINGS']) > 0)) {
                        $logno = $this->ErrorLogMgr->GetNextLogNo();
                        $ok = $ok && $this->ErrorLogMgr->AddErrors($values, $logno, SCHEDULEOPLOG);
            }
        }

            
        // Create the new record
        $ok = $ok && parent::regularAdd($values);

        $ok = $ok && $this->_QM->commitTrx($source);
        if (!$ok) {
            $msg = _("Could not create ScheduledOperationLog record!");
            $gErr->addError('SCHED-0005', __FILE__ . ':' . __LINE__, $msg);
            epp("$source: Error: $msg");
            $this->_QM->rollbackTrx($source);
        }
        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularSet(&$values)
    {
        $source = "ScheduledOperationLog::Set";
        global $gErr;
        /** @noinspection PhpUnusedLocalVariableInspection */
        $ok = true;

        $ok = $this->_QM->beginTrx($source);
            
        $values['LOGTYPE'] = SCHEDULEOPLOG;

        // Add the ErrorLogRecords if they haven't been added yet
        $errCount = is_array($values['ERRORS']) ? count($values['ERRORS']) : 0 ;
        if(($errCount > 0) && !$values['ERRORLOGNO'] && $ok) {
            $logno = $this->ErrorLogMgr->GetNextLogNo();
            $ok = $ok && $this->ErrorLogMgr->AddErrors($values, $logno, SCHEDULEOPLOG);
        }
        // Create the new record
        $ok = $ok && parent::regularSet($values);

        $ok = $ok && $this->_QM->commitTrx($source);
        if (!$ok) {
            $msg = _("Could not set ScheduledOperationLog record!");
            $gErr->addError('SCHED-0006', __FILE__ . ':' . __LINE__, $msg);
            epp("$source: Error: $msg");
            $this->_QM->rollbackTrx($source);
        }
        return $ok;
    }

    /**
     * @param string        $ID
     * @param string[]|null $fields
     *
     * @return array|false
     */
    function get($ID, $fields=null)
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $source = "ScheduledOperationLog::Get";
        // Create the new record
        $values = parent::get($ID);
        if($values['ERRORLOGNO'] != '') {
            $values['ERRORS'] = $this->_GetErrors($values['ERRORLOGNO']);
        }

        return $values;
    }

    /**
     * Delete a record from the database
     *
     * This implementation is usually sufficient for single table objects
     *
     * @param string|int $ID vid of entity
     *
     * @return bool
     */
    public function Delete($ID)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $gErr = Globals::$g->gErr;

        $values = parent::get($ID);

        $currentEntity = $values['SCHEDULEDOPERATION']['OPERATION']['ENTITY'];

        // there is no report entity, replace with the correct one in order to check permissions
        if ( strtolower($currentEntity) === 'report' ) {
            $currentEntity = 'reportinfo';
        }
        try {
            $entityMngr = $gManagerFactory->getManager($currentEntity);
            $validRequest = $entityMngr->API_ValidateWithoutError(API_READ);
            if (!$validRequest) {
                $msg = _("You are not authorized to access this log");
                $gErr->addError('SCHED-0007', __FILE__ . ':' . __LINE__, $msg);
                return false;
            }
        } catch (RuntimeException $e) {
            // continue with execution, permissions cannot be checked against an unknown object
        }


        return parent::Delete($ID);
    }

    /**
     * Translates VIDs to foreign keys
     *
     * @param array $values        the object
     *
     * @return bool
     */
    function _Translate(&$values) 
    {
        // Before bothering with any database call, lets test to see if $values[':schedop#'] is set (for optimization) - RFP 20090903
        if ( ! array_key_exists(':schedop#', $values) ) {
            global $gManagerFactory;
            $schedopMgr = $gManagerFactory->getManager('scheduledoperation');
            $schedop = $schedopMgr->GetRaw($values['SCHEDULEDOPERATION']['NAME']);
            $values[':schedop#'] = $schedop[0]['RECORD#'];
        }

        return true;
    }

    /**
     * @param string $logno
     *
     * @return bool|string[][]
     */
    function _GetErrors($logno) 
    {
        //Create an object of type 'Error'
        $objError = new IAIssueHandler();
        $res = $this->ErrorLogMgr->DoQuery('QRY_ERRORLOG_SELECT_BY_SCH', array($logno));

        //Get the error details from the IAIssueHandler.inc file
        $tmpres = $objError->GetErrorDetailsFromINC($res[0]['ERR_NO']);

        //Populate the return array
        if(isset($tmpres[0]) && count($tmpres[0]) > 0) {
            $res[0]['DESCRIPTION'] = $tmpres[0]['DESCRIPTION'];
            $res[0]['CORRECTION'] = $tmpres[0]['CORRECTION'];
        }
        return $res;
    }

    /**
     * @return bool
     */
    public function IsAuditEnabled()
    {
        return false;
    }

    /**
     * @param string $verb   action on the entity (Set, Delete, Add, ...)
     * @param string $key    vid of the entity action is being perf ormed on
     * @param mixed  $param1 useless hack because people do not know how to declare a function properly
     * @param mixed  $param2 useless hack because people do not know how to declare a function properly
     * @param array  $values the object data
     *
     * @param bool   $fastUpdate
     *
     * @return bool
     */
    public function DoEvent($verb, $key, $param1 = null, $param2 = null, $values = [], $fastUpdate = false)
    {
        // disable user events
        return true;
    }

    /**
     * Take a flat array and transform values to external representations
     * Overwrite the EntityManager method to translate to labels, instead of values, when displaying in the lister
     *
     * @param array $array
     * @param array $mapping
     * @param bool  $showLabels if true, it will convert the internal values to labels, if not it will convert them to
     *                          what is specified in "validvalues"
     * @param array $aggregateFields
     *
     * @return array
     */
    function _TransformFlatValuesToExternal(&$array, $mapping = null, $showLabels = false, array $aggregateFields = [])
    {
        return parent::_TransformFlatValuesToExternal($array, $mapping, true, $aggregateFields);
    }

}
