<?php

/**
 *    SAILCreateCompanyHandler.cls
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025, Intacct Corporation, All Rights Reserved
 */
class SAILCreateCompanyHandler extends AbstractAutomatedTransactionDataHandler
{
    const SETUPKEY = 'SETUPKEY';
    const COMPANYID = 'COMPANYID';
    const LOCATIONKEY = 'LOCATIONKEY';
    const STATE = 'STATE';

    /**
     * @return string
     */
    public function getPayLoad(): string
    {
        $data['company'] = $this->buildPayload();

        return json_encode($data) ?: '';
    }

    /**
     * @return array
     */
    protected function buildPayload(): array
    {
        $data = Globals::$g->gManagerFactory->getManager('company')->Get(GetMyCompany()) ?: [];
        if (!empty($this->dataModifier)) {
            $data['GUID'] = $this->dataModifier[self::COMPANYID] ?: '';
        } else {
            $data['GUID'] = CompanyCacheHandler::getInstance()->getProperty('COMPANYPREF', 'GUID') . '_0' ?: '';
        }
        $requestMapper = new SAILCreateCompanyRequestMapper($this->interactions);

        return $requestMapper->map($data, 'company');
    }

    /**
     * @return string
     */
    public function getLogPrefix(): string
    {
        return __CLASS__;
    }

    /**
     * @inheritDoc
     */
    public function getEndpoint(): string
    {
        return $this->interactions->getIAConfig('SAIL_CREATE_COMPANY_ENDPOINT_URL');
    }

    /**
     * @inheritDoc
     */
    public function getMaxRetryLimit(): int
    {
        return 1;
    }

    /**
     * @inheritDoc
     */
    public function onSuccess(SimpleHttpResponse $response): bool
    {
        $ok = true;

        $ok = $ok && $this->addAutomatedTransaction(AutomatedTransactionCompanyManager::STATE_INPROGRESS );

        if (!$ok) {
            $this->metricObj->setError("Unable to add to AUTOMATEDTRANSACTIONCOMPANY");
            $this->log("Unable to add to AUTOMATEDTRANSACTIONCOMPANY", LogManager::ERROR);
        } else {
            $this->log("Company ".$this->dataModifier['COMPANYID']." created successfully");
        }
        $this->metricObj->setSuccess($ok);

        return $ok;
    }

    /**
     * @inheritDoc
     * @throws AutomatedTransactionException
     */
    public function onFailure(SimpleHttpResponse $response): bool
    {
        $this->metricObj->setSuccess(false);
        $this->metricObj->setError($response->getResponse());

        $ok = $this->addAutomatedTransaction(AutomatedTransactionCompanyManager::STATE_FAILED );
        if (!$ok) {
            $this->metricObj->setError("Unable to add to AUTOMATEDTRANSACTIONCOMPANY");
            $this->log("Unable to add to AUTOMATEDTRANSACTIONCOMPANY", LogManager::ERROR);
        }

        switch ($response->getResponseCode()) {
            case 404:
                throw new AutomatedTransactionException(AutomatedTransactionException::CNY_NOT_FOUND);
            case 500:
                throw new AutomatedTransactionException(AutomatedTransactionException::SERVER_ERROR);
            default:
                return false;
        }
    }

    /**
     * @inheritDoc
     */

    public function getMaxRunCount(): int
    {
        return 1;
    }

    /**
     * @param string $state
     * @return bool
     */
    private function addAutomatedTransaction(string $state): bool
    {
        $ok = true;
        $res = EntityManager::GetListQuick('automatedtransactionsetup',['RECORDNO'],
            ['MODULEKEY' => AutomatedTransactionInteractions::MODULEID, 'PROVIDER' => 'SAIL']);
        $atCompanyMgr = Globals::$g->gManagerFactory->getManager('automatedtransactioncompany');
        $values[self::COMPANYID] = $this->dataModifier[self::COMPANYID];
        $values[self::STATE] = $state;
        $values[self::SETUPKEY] = $res[0]['RECORDNO'];
        if ($this->dataModifier[self::LOCATIONKEY] != 0) {
            $values[self::LOCATIONKEY] = $this->dataModifier[self::LOCATIONKEY];
        }
        $ok = $ok && $atCompanyMgr->add($values);

        return $ok;
    }
}