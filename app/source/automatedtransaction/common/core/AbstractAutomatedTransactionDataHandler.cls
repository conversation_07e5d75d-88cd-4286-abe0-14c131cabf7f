<?php

/**
 *    FILE:          AbstractAutomatedTransactionDataHandler.cls
 *    AUTHOR:        <PERSON> <<EMAIL>>
 *    DESCRIPTION:   Abstract class for Automated Transaction Data Handlers
 *
 *    (C) 2025, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */
abstract class AbstractAutomatedTransactionDataHandler
{

    /**
     * Default HttpMethod for the Endpoints
     *
     * @var string $httpMethod
     */
    protected $httpMethod = "POST";

    /**
     * Company#
     *
     * @var int $cny
     */
    protected $cny;

    /**
     * Max Payload Size of the payload
     *
     * @var int $maxPayloadSize
     */
    protected $maxPayloadSize;

    /**
     * Max Payload Retry Limit
     *
     * @var int $maxRetryLimit
     */
    protected $maxRetryLimit;

    /**
     * Automated Transaction Interactions object
     *
     * @var AutomatedTransactionInteractions $interactions
     */
    protected $interactions;

    /**
     * @var array|null $dataModifier
     */
    protected $dataModifier = null;

    /**
     * @var SimpleHttpResponse|null $response
     */
    protected $response = null;

    /**
     * @var IAMetrics|null $metricObj
     */
    public $metricObj = null;

    /**
     * AbstractAutomatedTransactionDataHandler constructor.
     * @param AutomatedTransactionInteractions $interactions
     * @param array|null $dataModifier
     * @throws Exception
     */
    public function __construct(AutomatedTransactionInteractions $interactions, array $dataModifier =null)
    {
        $this->cny = GetMyCompany();
        if (empty($this->cny)) {
            throw new Exception("Unable to construct Data Handler with empty cny#");
        }
        $this->interactions = $interactions;
        $this->dataModifier = $dataModifier;
        $this->maxPayloadSize = (int)$this->getMaxPayloadSize();
        $this->maxRetryLimit = (int)$this->getMaxRetryLimit();
        $this->setMetricObj();
    }

    /**
     * Set Metric Object
     *
     * @throws Exception
     */
    protected function setMetricObj()
    {
        $this->metricObj = new MetricAutomatedTransactionDataHandlerRun();
        $this->metricObj->setCompany($this->cny);
        $this->metricObj->setAction($this->interactions->getAction());
    }

    /**
     * @return int
     * @throws Exception
     */
    /**
     * @inheritDoc
     */
    protected function getMaxPayloadSize(): int
    {
        if (empty($this->maxPayloadSize)) {
            return (int) $this->interactions->getIAConfig('AT_RUN_MAX_PAYLOAD_PER_REQUEST');
        } else {
            return (int)$this->maxPayloadSize;
        }
    }

    /**
     * HttpMethod Getter
     *
     * @return string
     */
    public function getHttpMethod(): string
    {
        return $this->httpMethod;
    }

    /**
     * Endpoint Getter
     *
     * @return string
     */
    abstract public function getEndpoint(): string;

    /**
     * Function to run on a successful response
     *
     * @param SimpleHttpResponse $response
     *
     * @return bool
     */
    abstract protected function onSuccess(SimpleHttpResponse $response): bool;

    /**
     * Function to run on a failure response
     *
     * @param SimpleHttpResponse $response
     *
     * @return bool
     */
    abstract protected function onFailure(SimpleHttpResponse $response): bool;

    /**
     * Process the HttpResponse recieved from AutomatedTransactionHttpRequest
     *
     * @param SimpleHttpResponse $response
     *
     * @return bool
     */
    public function processResponse(SimpleHttpResponse $response): bool
    {
        $this->response = $response;
        if ($response->getResponseCode() === SimpleHttpResponse::HTTP_CODE_SUCCESS) {
            return $this->onSuccess($response);
        } else {
            $this->log("HTTP Response: " . $response->getResponse());
            return $this->onFailure($response);
        }
    }

    /**
     * returns the HttpResponse recieved from AutomatedTransactionHttpRequest
     * @return SimpleHttpResponse
     */
    public function getResponseObject(): SimpleHttpResponse
    {
        return $this->response;
    }

    /**
     * Public function to get the build payload
     *
     * @return string
     */
    abstract public function getPayLoad(): string;

    /**
     * Public funtion to get the max retry limit
     *
     * @return int
     */
    public function getMaxRetryLimit(): int
    {
        if (empty($this->maxRetryLimit)) {
            return (int) $this->interactions->getIAConfig('AT_RUN_MAX_RETRY');
        } else {
            return (int)$this->maxRetryLimit;
        }
    }

    /**
     * Automated Transaction Logger
     *
     * @param string $msg
     * @param string $level
     */
    protected function log(string $msg, string $level = LogManager::INFO)
    {
        $this->interactions->log($msg, $level, $this->getLogPrefix());
    }

    /**
     * Public function to get the log prefix
     *
     * @return string
     */
    abstract public function getLogPrefix(): string;

    /**
     * Publish the Metrics when the object destroys
     */
    public function __destruct()
    {
        if ( $this->metricObj instanceof IAMetrics ) {
            $this->metricObj->publish();
        }
    }

    /**
     * @return int
     * @throws Exception
     */
    public function getMaxRunCount(): int
    {
        return (int)$this->interactions->getIAConfig('AT_PROCESS_MAX_LOOP_PER_CNY');
    }

    /**
     * @return bool
     */
    public function shouldCheckDuplicates(): bool
    {
        return true;
    }

    /**
     * @return array|null
     */
    public function getDataModifier(): array|null
    {
        return $this->dataModifier;
    }

}

