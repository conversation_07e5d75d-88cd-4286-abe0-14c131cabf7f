<?php
/**
 * Entity for Run Object Summary.
 *
 * @copyright 2021 Sage Intacct Inc., All Rights Reserved
 */
require 'globalrunobjectsummary.ent';
$kSchemas['deprschrunsummary'] = [];
$kSchemas['deprschrunsummary'] = EntityManager::inheritEnts($kSchemas['globalrunobjectsummary'], $kSchemas['deprschrunsummary']);

$kSchemas['deprschrunsummary']['fieldinfo'][] =
    [
        'path'     => 'ASOFDATE',
        'fullname' => 'IA.AS_OF_DATE',
        'type'     => $gDateType,
        'id'       => 101,
    ];

$kSchemas['deprschrunsummary']['module'] = 'fa';

$kSchemas['deprschrunsummary']['dbfilters'] = [
    [ 'deprschrunsummary.module', '=', 'FA']
];
