<?

//
//	FILE:
//	AUTHOR: <PERSON><PERSON><PERSON>
//	DESCRIPTION:
//
//	(C) 2001, Intacct Corporation, All Rights Reserved
//
//	This document contains trade secret data that belongs to Intacct
//	Corporation and is protected by the copyright laws.  Information
//	herein may not be used, copied or disclosed in whole or in part
//	without prior written consent from Intacct Corporation.

require 'eppaymentrequest.ent'; 
require 'prrecordhistory.ent';

$kSchemas['approvereimbursements'] = array(
    'fieldinfo' => [],
);
$kSchemas['approvereimbursements']['fieldinfo'] = array_merge(
    array(
        array(
            'fullname' => 'IA.REIMBURSEMENT_TRANSACTION_AMOUNT',
            'type' => array(
                'ptype' => 'currency',
                'type' => 'currency',
                'format' => $gCurrencyFormat,
            ),
            'desc' => 'IA.REIMBURSEMENT_TRANSACTION_AMOUNT',
            'path' => 'PAYMENTTRXAMOUNT',
            'renameable' => true,
        ),
        array(
            'fullname' => 'IA.REIMBURSEMENT_STATUS',
            'type' => array( 
                'ptype' => 'enum', 
                'type' => 'enum', 
                'format' => '/.{0,100}/',
                'validlabels' => array('IA.SUBMITTED', 'IA.PARTIALLY_APPROVED'),
                'validvalues' => array('S', 'PA'),
                '_validivalues' => array('Submitted', 'Partially Approved'),
            ),
            'calculated' => true,
            'formula' => array(
                    'fields' => array('RAWSTATE'),
                    'function' => " decode (\${1}, 'S', 'Submitted', 'PA', 'Partially Approved') ",
                    ),
            'desc' => "IA.STATE_OF_REIMBURSEMENT",
            'path' => 'STATE',
        ),
    ), $kSchemas['approvereimbursements']['fieldinfo']
);
       

// merge the schemas
require'eppaymentrequest.ent';
$kSchemas['approvereimbursements'] = EntityManager::inheritEnts($kSchemas['eppaymentrequest'], $kSchemas['approvereimbursements']);

$kSchemas['approvereimbursements']['dbfilters'] = array(
      array('approvereimbursements.recordtype', 'IN', array(PRRECORD_TYPE_REIMBURSEMENT, PRRECORD_TYPE_EEADVANCE)),
      array('approvereimbursements.state', 'IN', array(PRRECORD_STATE_SUBMITTED, PRRECORD_STATE_PARTIALLYAPPROVED)),
      );

$kSchemas['approvereimbursements']['nosysview'] = true;
$kSchemas['approvereimbursements'][SearchTable::GSALLOWED] = false;

$kSchemas['approvereimbursements']['nameFields'] = [ 'DOCNUMBER', 'EMPLOYEEID', 'PAYMENTDATE' ];

