<?php
/**
 *    FILE:    eeadvance.ent
 *    AUTHOR: <PERSON>
 *    DESCRIPTION:
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
*/

require 'eppayment.ent';

// UNTIL THEY DIVERGE IN A SIGNIFICANT WAY, THE EEADVANCE IS THE SAME KIND OF ENTITY AS THE EPPAYMENT.
$kSchemas['eeadvance'] = $kSchemas['eppayment'];

$kSchemas['eeadvance']['dbfilters'] = array(
    array ('eeadvance.recordtype', '=', PRRECORD_TYPE_EEADVANCE),
);
$kSchemas['eeadvance']['ownedobjects'][0]['entity'] = 'eppaymentitem';
$kSchemas['eeadvance']['platform_entity'] = 'eppayment';
$kSchemas['eeadvance']['nameFields'] = [ 'DOCUMENTNUMBER', 'EMPLOYEEID' ];
