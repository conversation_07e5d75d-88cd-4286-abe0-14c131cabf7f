<?php

/**
 * Reader class to read JSON file
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Sage Intacct Inc., All Rights Reserved
 */

namespace unitTest\core;


use DirectoryIterator;

class J<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends UnitTestDataHelper
{

    /**
     * @param string $directoryPath points to the directory that needs to be traversed
     *
     * @return array|mixed
     */
    public function readData(&$directoryPath)
    {
        $dataset = [];

        $dir = new DirectoryIterator($directoryPath);
        foreach ($dir as $file) {
            if ($file->getFilename() != '.' && $file->getFilename() != '..') {
                $json = file_get_contents($file->getPathname());
                $values = json_decode($json, true);
                array_push($dataset, $values);
            }
        }

        $sortedDataSet = $this->sortDataSet($dataset);
        $validDataset = $this->validateData($sortedDataSet);
        return $validDataset;


    }

    /**
     * @param array $datasets
     *
     * @return array|mixed
     */
    public function sortDataSet(&$datasets)
    {
        $result = array_merge_recursive(...$datasets);

        // TODO: Implement sort of 'testcases' array
        //
        // $result = [];
        //
        // /*reads from a single file*/
        // if (count($datasets) == 1) {
        //     $result = array_shift($datasets);
        // } /*reads from multiple files*/
        // else {
        //     foreach ($datasets as $row) {
        //         $keys = array_keys($row);
        //         foreach ($keys as $key) {
        //             $temp = $row[$key];
        //             if ($result[$key] == null) {
        //                 $result[$key] = array($temp);
        //             } else {
        //                 array_push($result[$key], $temp);
        //             }
        //         }
        //
        //     }
        // }
        return $result;
    }

    /**
     * @param $datasets
     *
     * @return mixed
     */
    public function validateData(&$datasets)
    {
        $testno = 0;

        foreach($datasets as $unitTestFuncName => $dataset) {
            $testids = [];
            $testcases = $dataset['testcases'];
            foreach ($testcases as $testcaseIdx => $testcase) {
                $testid = $testcase['ID'];
                if ($testid == null) {
                    LogToFile("No Test ID Found : " . $testid . " in " . ($testno + 1) . ". Please fix");
                    print "No Test ID Found : " . $testid . " in " . ($testno + 1) . ". Please fix\n";

                    unset($datasets[$unitTestFuncName]['testcase'][$testcaseIdx]);
                } else if ( ! in_array($testid, $testids)) {
                    array_push($testids, $testid);
                } else {
                    LogToFile("DUPLICATE TEST ID : " . $testid . " in " . ($testno + 1) . ". Please fix");
                    print "DUPLICATE TEST ID : " . $testid . " in " . ($testno + 1) . ". Please fix\n";
                    unset($datasets[$unitTestFuncName]['testcase'][$testcaseIdx]);
                }
                ++$testno;
            }
        }

        return $datasets;

    }

    /**
     * @param string|array $json
     *
     * @return array
     */
    public static function Deserialize($json)
    {
        if (is_string($json)) {
            $json = json_decode($json, true);
        }

        $usedParams = [];
        $className = $json['__class_name__'];
        $reflection = new \ReflectionClass($className);
        $constructParams = $reflection->getConstructor()->getParameters();
        foreach($constructParams as $constructParam){
            $usedParams[] = $constructParam->getName();
        }

        $classInstance = new $className(...$usedParams);
        foreach ($json as $key => $value) {
            if(!(in_array($key, $usedParams))) {
                $setter = 'set' . ucfirst($key);
                call_user_func([$classInstance, $setter], $value);
            }
        }
        return $classInstance;
    }

    /**
     * @param string $json
     *
     * @return array
     */
    public static function DeserializeArray($json)
    {
        if (is_string($json)) {
            $json = json_decode($json, true);
        }
        $items = [];
        foreach ($json as $item) {
            $items[] = self::Deserialize($item);
        }
        return $items;
    }

}