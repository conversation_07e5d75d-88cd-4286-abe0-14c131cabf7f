function toggleSuiteApps() {
    var enablesuiteexperience = did('enablesuiteexperience');
    if (!enablesuiteexperience.checked) {
        var checkboxes = document.querySelectorAll("input[name='suiteApps[]']");
        for (var i=0; i < checkboxes.length; i++){
            checkboxes[i].checked = false;
        }
    }
}


function toggleSuiteAppsSync(id) {
    var enablesuiteexperience = did('enablesuiteexperience');
    if (!enablesuiteexperience.checked) {
        alert("Please Enable Suite Experience first!");
        did(id).checked = false;
    }
}