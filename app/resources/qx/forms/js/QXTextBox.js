// Cache the currently active Text Box Object.
activeTextBox = '';

/**
 * The function that adds the popup text box into the UI. The form name along with the object id of the 
 * multi text box should be passed.
 **/
function AddText(objID, form, maxlength, uicontrol, boxsize, disabled, formField) {
	if (!form) {
		form = 1;
	}
	var obj = document.forms[form].elements[objID];			
	if (activeTextBox && activeTextBox != '') {
		//activeTextBox.dynamicObj.onfocusout = '';
		activeTextBox.dynamicObj.onblur = '';
		activeTextBox.CollapseDynamicObject();		
	} 
	//  Protect against invalid 'uicontrol'.
	var objToUse = obj;
	try {
		if (uicontrol != null && uicontrol.tagName != null)
			objToUse = uicontrol;
	} catch(e) { }
	var textBox = new TextBox(form, objToUse, boxsize, formField);
	textBox.ShowTextBox(obj, maxlength, disabled);
	activeTextBox = textBox;
	return true;
}

/**
 * function which collapses the current active text box. 
 * This can happen when the keys [Enter/Esc/Tab] on the pop-up box is pressed. [onkeydown event]
 * It can also happen when the focus is out of the pop-up text box.
 */
function CollapseTextBox(evt) {
	evt  = evt || window.event;
	if (evt != null && activeTextBox && activeTextBox != '') {
		var from = (typeof evt.srcElement=="undefined") ? evt.target : evt.srcElement;
		var to = (typeof evt.toElement=="undefined") ? evt.currentTarget : evt.toElement;
		if ((evt.keyCode==27 || evt.keyCode==9) || evt.type == 'blur') {
		 //|| ((evt.keyCode == 0 ||evt.keyCode == null)
		// 	&& from && to && from.type == 'textarea' && from.type != to.type)) {
			activeTextBox.CollapseDynamicObject(from.value, evt, this);		
		}
	}
	return true;
}

/**
 * This is the constructor for the object TextBox that we would be constructing.
 * Instantiate all the class levels attributes and the class methods.
 */
function TextBox(form,uicontrol,boxsize,formField) {
	this.textBoxID = '';
	this.textBoxButtonID = '';
	var bsize = 32;
	
	if (boxsize > 0) {
		bsize = boxsize;
	}
	
	if(uicontrol && uicontrol.meta) {
        var textboxWidth = jq(uicontrol).outerWidth();
		this.popTextBoxWidth = textboxWidth + 'px';
	} else {
		this.popTextBoxWidth = (bsize * 6).toString() + 'px';
	}
	
	this.form = form;
	this.uicontrol = uicontrol;
	this.formField = formField;
	this.dynamicObj = document.createElement("DIV");
	this.dynamicObj.setAttribute("ID","textBoxDiv");
	this.dynamicObj.setAttribute("tabIndex","-1"); 
	//this.dynamicObj.onfocusout = CollapseTextBox;
	this.dynamicObj.onblur = CollapseTextBox;
	this.dynamicObj.onkeydown = CollapseTextBox;
	this.dynamicObj.style.zIndex = 6; /* before changing this see the top comment in FormComponents.js */

	//fix zindex only if linedetails is visible
	if (jq('.line_details_switch').is(':visible')) {
        this.dynamicObj.style.zIndex = 2001;
	}

	if( window.editor ) {
	    if ( uicontrol && uicontrol.meta ) {
	        uicontrol.meta.getRoot().controls.content.appendChild(this.dynamicObj);
	    } else {
	        document.body.appendChild(this.dynamicObj);
	    }
	} else {
		document.forms[this.form].appendChild(this.dynamicObj);
	}

	this.ShowTextBox = ShowTextBox;
	this.findTBLeftOffset = findTBLeftOffset;
	this.findTBTopOffset = findTBTopOffset;
	this.CollapseDynamicObject = CollapseDynamicObject;
	return true;
}

/**
 * This would display the text box with the input text as the parameter, using which the pop-up rendering
 * would happen. The dynamic text box added would be placed based on the input text object and also 
 * a iframe is used inorder to get rid of the <select> element issues.
 **/
function ShowTextBox(obj, maxlength, disabled) {
	var iframeRef           = document.getElementById('DivShim');

    this.dynamicObj.style.position = 'absolute';
	this.dynamicObj.style.display = 'block';
	this.dynamicObj.style.visibility = 'visible';

	var topC = 0;
	var leftC = 0; 
	
	if( ! obj.meta ) {
        topC = this.uicontrol.offsetHeight;
        if (jq) {
            var offset =  jq(obj).position(); // if we were position = 'absolute' then use jq(obj).offset();
            topC += offset.top;
            leftC += offset.left;
        } else {
            //If your codce goes here and you have a bug add jquery to your page. This is crappy legacy.
            topC  += this.findTBTopOffset(obj, true);
            leftC += this.findTBLeftOffset(obj, true);
        }
	} else { // new form eidtor team should consider using the above code.
        topC = getTopOffset(obj);
    	leftC = getLeftOffset(obj);

    	elem = this.dynamicObj.offsetParent;
    	
	    while (elem && elem.tagName != "BODY" && elem.tagName != "HTML") {
	        if ( jq(elem).css('position') == 'relative' ) {
	            break;
	        }
	        elem = elem.offsetParent;
	    }
	    
	    topC = topC - getTopOffset(elem);
	    leftC = leftC - getLeftOffset(elem);
	}

    this.dynamicObj.style.top = (topC-1) + 'px';
	this.dynamicObj.style.left = leftC + 'px';

	// iframeRef.style.width =  obj.offsetWidth;
	if( iframeRef ) 
	{
		iframeRef.style.width =  (obj.offsetWidth-4) + 'px';
		iframeRef.style.height =  (obj.offsetHeight+27) + 'px';
		iframeRef.style.top = this.dynamicObj.style.top;
		iframeRef.style.left = this.dynamicObj.style.left;
	}

	this.textBoxID = obj.id;
	this.textBoxButtonID = "TB_But_"+obj.id;

	var textObj = document.forms[this.form].elements[this.textBoxID];
	textObj.onkeydown = CollapseTextBox;

	this.textEditor = document.createElement("TEXTAREA");
	this.textEditor.originalControl = obj;
	if( window.tabOnEnter )
    {
        YAHOO.util.Event.addListener(this.textEditor, "keydown", function() {
                toekFilterOnce('2');
            }, this.textEditor);
    }
	
	
	if((textObj.dimwidth) && textObj.dimwidth != null ){
		this.textEditor.setAttribute("rows", textObj.dimwidth);
		this.popTextBoxWidth = textObj.dimheight;
	}
	else{
		var defaultRows = 3;
		if(obj.value) {
			//show a small size text area when opened for the first time. If the 
			//number of character increaes then show a bigger box.
			//the 70 is arbitrary can be ajdusted based on reviews
			if(obj.value.length > 70) {
				defaultRows = 10;
			}
		}
		this.textEditor.setAttribute("rows", defaultRows);
	}
    if((textObj.dimheight) && textObj.dimheight != null ){
		this.popTextBoxWidth = textObj.dimheight;
	}

	if(disabled){
		this.textEditor.setAttribute("disabled", "true");
	}

	this.textEditor.setAttribute("ID", 'TB_'+obj.id);
	this.textEditor.maxlength = maxlength;
    this.textEditor.style.boxSizing = "border-box";
	this.dynamicObj.setAttribute("tabIndex","-1");
	this.dynamicObj.appendChild(this.textEditor);
//	this.textEditor.style.fontFamily = 'Verdana';
//	this.textEditor.style.fontSize = '11px';
	this.textEditor.onblur = CollapseTextBox;
	
	//  Make sure text box does not exceed window boundary.
	var maxX = jq(document).width();
	var textWidth = this.popTextBoxWidth.substring(0,(this.popTextBoxWidth).indexOf('px'));
	if (maxX < (leftC + textWidth)) {
		var newTextWidth = (maxX<leftC) ? maxX : maxX - leftC;
		if (newTextWidth > textWidth)	// Don't go bigger than requested.
			textWidth = textWidth;
		else
			textWidth = newTextWidth;
	}
	this.textEditor.style.width = textWidth + 'px';
	var parentsCollection = jq(obj).parents();
	var isOnLineDetail = false;
	//this section is needed for all the elements which do not have meta 
	//but are some how present on the line details
	//the code will look for 'lineDetails' class in all the 
	//parents of the elements
	for(var i=0;i<parentsCollection.length;i++) {
		var innerParent = parentsCollection[i];
		var isLineDetail = jq(innerParent).hasClass('lineDetails');
		var isEditorGrid = jq(innerParent).hasClass('editor_grid');
		
		if(isEditorGrid) {
			
		}
		
		if(isLineDetail || isEditorGrid) {
			isOnLineDetail = true;
			break;
		}
	}
	if(isOnLineDetail) {
		this.textEditor.className = 'dummyForEditor';
	}
	this.textEditor.value = obj.value;

	var isTextarea = this.uicontrol && this.uicontrol.tagName && this.uicontrol.tagName.toLowerCase() === 'textarea'; 
	var isEdge = navigator.appVersion && navigator.appVersion.indexOf('Edge') > -1;
	if (!(isEdge && isTextarea)){
		baseSetText(this.textEditor, obj.value);
	}

	this.textEditor.onkeyup = SliceText;

	
	//if (obj.style.width) { width = obj.style.width; } else if (obj.size) { width = obj.size; }
	//this.dynamicObj.innerHTML = "<textarea rows='3' ID= style='position:absolute;width:"+this.popTextBoxWidth+"' value='"+obj.value+"'>"+obj.value+"</textarea>";	
	if(disabled){
		this.dynamicObj.focus();
	} else {
		this.textEditor.focus();
        this.textEditor.select();
	}

	return true;
}

/**
 * This is a very important function that slices the text in the multi-text box when the character size
 * exceeds maximum length of the field.
 */
function SliceText() {
	var maxlength = this.maxlength;
	if (maxlength && this.value.length > maxlength) {
		this.value = this.value.slice(0, maxlength);
	}	
	return true;
}

function findTBLeftOffset(elem, stopAtDiv) {
    var left = 0;
    while (elem && elem.tagName != "BODY" && elem.tagName != "HTML") {
        if (stopAtDiv && elem.tagName == "DIV") {
            break;
        }
        left += elem.offsetLeft;
        elem = elem.offsetParent;
    }
    return left;
}

function findTBTopOffset(elem, stopAtDiv) {
    var top = 0;
    while (elem && elem.tagName != "BODY" && elem.tagName != "HTML") {
        if (stopAtDiv && elem.tagName == "DIV") {
            break;
        }
        top += elem.offsetTop;
        elem = elem.offsetParent;
    }
    return top;
}

function CollapseDynamicObject(value, event, textAreaElement) {
	var textObj = null;
	//try catch is needed for IE as it throws excpetion when we click on the different row 
	try{
		textObj = document.forms[this.form].elements[this.textBoxID]; 
	} catch(ex) {}
	
	var oldValue = textObj ? textObj.value : null; 
	
	var iframeRef = document.getElementById('DivShim');
	if(textObj) {
		if (value || value == '') {
			textObj.value = value; 
	        if (activeTextBox.formField) {//metadata needs to be set.
	            activeTextBox.formField.setValue(textObj.value);
	        }
		} else if (textObj && popTextBoxObj) {	
			var popTextBoxObj = document.forms[this.form].elements['TB_'+this.textBoxID];
			textObj.value = popTextBoxObj.value; 
			if (activeTextBox.formField) {//metadata needs to be set.
			    activeTextBox.formField.setValue(textObj.value);
			}
		}	
	}
	this.dynamicObj.style.display = 'none';
	this.dynamicObj.style.visibility = 'hidden';

	if( iframeRef ) 
	{
		iframeRef.style.display = "none";
	}
	activeTextBox = '';
	
    if(textObj && textObj.disabled!=true){
    	event  = event || window.event;
    	if(event != null) {
        	if(textObj.meta && event.type != 'blur') {
        		textObj.meta.openArea = false;
        	}
        	if(event.type != 'blur') {
        		textObj.focus();
        	}
    	} else {
    		textObj.meta.openArea = false;
    	}
    }
    if ( textObj && textObj.value != oldValue ) {
        baseSendUIEvent(textObj, 'change');
    }
    
    if ( this.dynamicObj.parentNode ) {
        this.dynamicObj.parentNode.removeChild(this.dynamicObj);
    }

    if (event) {
        if (event.type != 'blur' && window.editor && textObj.meta) {
            var grid = textObj.meta.getGrid();
            if (grid) {
                return gridKeyDownFunction(event, grid);
            }
        }
    }
	return true;
}

// Enforce the maximum length of an object. This method is designed to be hooked on the
// onkeydown and onkeyup events.
// The first argument is the control object and the second argument is the event object.
// The max length is read from the object's max length attribute.
function EnforceMaxLength(obj, e)
{
	var key = e.keyCode;

	var hasGetAttribute = false;
	try
	{
		var dummy = obj.getAttribute('abc');
		hasGetAttribute = true;
	}
	catch(e)
	{
		hasGetAttribute = false;
	}
	if( key >= 32 )
	{
		var maxLengthText = obj.maxlength ? obj.maxlength : hasGetAttribute ? obj.getAttribute('maxlength') : '0';
		if( ! maxLengthText || maxLengthText == '0' ) maxLengthText = obj.maxLength ? obj.maxLength : hasGetAttribute ? obj.getAttribute('maxLength') : '0';
		var maxLength = parseInt(maxLengthText);
		if ( maxLength != 0 && maxLength > 0 && obj.value.length >= maxLength )
		{
			if( obj.value.length > maxLength ) obj.value=obj.value.substring(0,maxLength);
			return false;
		}
	}
	return true;
}

function getTooltipDiv() {
	var tooltipDiv = document.getElementById('TOOL_TIP');
	
	if(tooltipDiv) {
		return tooltipDiv;
	}
	
	var divNode = document.createElement('div');
    divNode.id = 'TOOL_TIP';
    divNode.className = 'console-div qx-tooltip';
    divNode.style.display = 'none';
    divNode.style.backgroundColor = '#efefef';
    divNode.style.textAlign = 'left';
    divNode.style.width = '290px';	
    divNode.innerHTML = "<br>";
    document.body.appendChild(divNode);
    
    tooltipDiv = document.getElementById('TOOL_TIP');
	return tooltipDiv;
}

function showToolTipDiv(el,m, lineCount) {
	if (m) {
		m.style.display='';
	    var topInput = getTopOffset(el);
	    var leftInput = getLeftOffset(el);
        if (lineCount == 1) lineCount = 1.5;
	    var calculatedHeight = lineCount * 22;
		m.style.height = calculatedHeight + 'px';
	    var right = leftInput;
	    if((topInput - calculatedHeight) < 0) {
	    	m.style.top = (topInput - (calculatedHeight -(topInput - calculatedHeight))) + 'px';
	    	if( (topInput - (calculatedHeight -(topInput - calculatedHeight))) < 0) {
	    		m.style.top = (topInput + 20)+'px';
	    	}
	    } else {
	    	m.style.top = (topInput - calculatedHeight - 3) + 'px';
	    }
	    var elementWidth = jq(el).outerWidth();
		// Fix for DE15379, the tooltip needs to be wider then 175px or 
		// the lines are not calculated correctly
		elementWidth = elementWidth >= 175 ? elementWidth : 175;
	    m.style.width = elementWidth + 'px';
	    m.style.left = leftInput + 'px';
		m.style.padding = '6px 12px';
		// Fix for DE17228, the tooltip should have a z-index bigger than the
		// closest element with a fixed z-index
		m.style.zIndex = getClosestZIndex(el) + 1;
	}
}

function getClosestZIndex(target) {
	// Fix for DE18769, pickers have a hardcoded z-index of 3, so tooltips should 
	// have a z-index of at least 3, to not go under the picker
	return Math.max(findEffectiveZIndex(target, 0), 3);
}