//works
//import { OnLoad } from 'financeaccount.js';

function uploadSignature(imgno)
{
	if(imgno == '3'){
		Launch("logouploadforcheck.phtml?.op=" + _op + "&.imgno=" + imgno + "&.bankacct=" + bankaccountno + "&.fromFormEditor=1","upload", 500, 300);
	}
	else{
		Launch("signatureupload.phtml?.op=" + _op + "&.imgno=" + imgno + "&.bankacct=" + bankaccountno + "&.fromFormEditor=1","upload", 500, 300);
	}
	
}


function c_fail_ajaxRequest() {
    window.editor.hideLoadingBar();
    window.view.addMessage(MESSAGE_ERROR, GT("IA.ERROR_PROCESSING_REQUEST_TRY_AGAIN"));
}
//works
function removeSignature(imgno, ths)
{
	var checklayoutImage = window.editor.findComponents('CHECKLAYOUT.REMIMAGE' + imgno);
	if(checklayoutImage[0]){
		if (ths.checked) {
			checklayoutImage[0].setValue('Y');		
		} else {
			checklayoutImage[0].setValue('N');
		}
	}
}

function onLoadBankFileFields(action) {
    renderBankFileUI(action);
}

function onChangeBankFileFormat() {
    resetWPBFields();
    renderBankfileFields('edit');
}

function onChangeUseBankFile() {
    showHideBankfileSection();
}

function showHideBankfileSection() {
    var enableBankFile = window.editor.view.getFieldValue('FILEPAYMENTSERVICE') == 'BANKFILE';
    var enableACHFile = window.editor.view.getFieldValue('FILEPAYMENTSERVICE') == 'ACH';
    var bankfileinfoSection = window.editor.view.findComponentsById('bankfileinfo', 'Section');
    var achinfoSection = window.editor.view.findComponentsById('achinfo', 'Section');
    var outsourcedACHInfoSection = window.editor.view.findComponentsById('OutsourcedACHInfo','Section');
    bankfileinfoSection[0].showHide(enableBankFile);
    achinfoSection[0].showHide(enableACHFile);
    outsourcedACHInfoSection[0].showHide(enableACHFile);

    var achIndex = paymentProviders.indexOf('ACH');
    if ( enableACHFile ) {
        if ( achIndex == -1 ) {
            paymentProviders.push('ACH');
        }
    } else {
        if(achIndex != -1) {
            paymentProviders.splice(achIndex, 1);
        }
    }

    var useBankFile = window.editor.findComponents('USEBANKFILE');
    if(useBankFile){
        useBankFile[0].setValue(''+enableBankFile);
    }
    var achEnabled = window.editor.findComponents('ACHENABLED');
    if(achEnabled){
        achEnabled[0].setValue(''+enableACHFile);
    }

    showHideNoPaymentProviderSection();
}

function showHideNoPaymentProviderSection() {
    var noProviderMsgSection = window.editor.view.findComponentsById('NoProviderMsgSection','Section');
    if(paymentProviders.length == 0) {
        noProviderMsgSection[0].showHide(true);
    } else {
        noProviderMsgSection[0].showHide(false);
    }
}

function renderBankfileFields(action) {
    var countryPath = action == 'view' ? 'MAILADDRESS.COUNTRY' : 'MAILADDRESS.COUNTRYCODE';
    var country = window.editor.view.getFieldValue(countryPath);
    var bankFileFormat = window.editor.view.getFieldValue('BANKFILEFORMAT');
    if ( typeof gBankFileMeta[country][bankFileFormat] == 'undefined' ) {
        return;
    }
    var allFields = gBankFileMeta[country][bankFileFormat]['FIELDS'];
    for ( field in allFields ) {
        renderWPBField(action, allFields[field]);
    }
}

function renderWPBField(action, bankfileFieldInfo) {
    var jsonpath = bankfileFieldInfo.JSONPATH;
    var isRequired = bankfileFieldInfo.ISREQUIRED == 'T' ? true : false;
    var fullName = bankfileFieldInfo.UILABEL;
    var enumeration = bankfileFieldInfo.ENUMERATION;

    for ( fieldName in gFileSpecificFields ) {
        if ( gFileSpecificFields[fieldName] != jsonpath ) {
            continue;
        }
        var field = window.editor.view.findComponents(fieldName, 'Field');
        if ( action != 'view' ) {
            field[0].updateProperty('required', isRequired);
            if ( enumeration ) {
                field[0].renderer = null;
                var validvalues = [];
                for ( opt in enumeration ) {
                    if ( enumeration[opt]['description'] ) {
                        validvalues[validvalues.length] = enumeration[opt]['description'];
                    }
                }
                field[0].uiControl = 'ControlEnum';
                field[0].updateProperty(['type', 'type'], 'enum');
                field[0].updateProperty(['type', 'ptype'], 'enum');
                field[0].updateProperty(['type', 'validvalues'], validvalues);
                field[0].updateProperty(['type', 'validlabels'], validvalues);
            }
            if(fieldName == 'BANKFILECOUNTRY') {
                var countryPath = action == 'view' ? 'MAILADDRESS.COUNTRY' : 'MAILADDRESS.COUNTRYCODE';
                var country = window.editor.view.getFieldValue(countryPath);
                var bankFileCountry = window.editor.findComponents('BANKFILECOUNTRY');
                if(bankFileCountry){
                    bankFileCountry[0].setValue(country);
                }
            }
        }
        field[0].updateProperty('fullname', fullName);
        field[0].showHide(true);
        field[0].redraw();
    }
}

function resetWPBFields() {
    for ( fldName in gAllWPBFields ) {
        var field = window.editor.view.findComponents(fldName, 'Field');
        field[0].showHide(false);
        field[0].updateProperty('required', false);
    }
}

function renderBankFileUI(action) {

    var countryPath = action == 'view' ? 'MAILADDRESS.COUNTRY' : 'MAILADDRESS.COUNTRYCODE';
    var country = window.editor.view.getFieldValue(countryPath);
    var bankFiles = typeof gBankFileMeta != 'undefined' ? gBankFileMeta[country] : [];
    var bankFileEnabled = typeof bankFiles != 'undefined' && bankFiles.length != 0;

    if ( action != 'view' ) {
        if ( !bankFileEnabled ) {
            var field = window.editor.findComponents('USEBANKFILE');
            if ( field ) {
                field[0].setValue('false');
            }
        }
    }

    toggleBankFileAndACHTabs(bankFileEnabled);
    resetWPBFields();
    if ( bankFileEnabled ) {
        renderBankfileFields(action);
    }
}

function toggleBankFileAndACHTabs(bankFileEnabled) {
    var enabled = ['disabled','disabled','enabled'];
    var selected = 'NONE';

    var useBankFile = window.editor.findComponents('USEBANKFILE');
    var achEnabled = window.editor.findComponents('ACHENABLED');
    var currency = window.editor.view.getFieldValue('CURRENCY');
    var country = window.editor.view.getFieldValue('MAILADDRESS.COUNTRYCODE');


    if(bankFileEnabled && achEnabled[0]['parentValue']['ACHENABLED'] == 'false'){
        if ( achEnabled ) {
            achEnabled[0].setValue(false);
        }
        if ( (currency == '' || currency == 'USD') ) {
            enabled[0] = 'enabled';
        }
        enabled[1] = 'enabled';
    } else {
        if ( useBankFile ) {
            useBankFile[0].setValue(false);
        }
        if ( (currency == '' || currency == 'USD') ) {
            enabled[0] = 'enabled';
            if(country == 'US' && currency == 'USD'){
                enabled[1] = 'enabled';
            }
        }
    }


    if(useBankFile && useBankFile[0].getValue() === 'true'){
        selected = 'BANKFILE';
    } else if(achEnabled && achEnabled[0].getValue() === 'true'){
        selected = 'ACH';
    }
    var paymentServiceOptionField = window.editor.view.findComponentsById('FILEPAYMENTSERVICE', 'Field');
    paymentServiceOptionField[0].updateProperty('buttonstate',enabled);
    paymentServiceOptionField[0].setValue(selected);
    paymentServiceOptionField[0].redraw();

    showHideBankfileSection();
}

function isValidCountryCurrency(country, currency) {
    if ( typeof gCountryCurrencies !== 'undefined' ) {
        var currencies = gCountryCurrencies[country];
        if ( typeof currencies != 'undefined' && currencies.includes(currency) ) {
            return true;
        }
    }
    return false;
}

function getBankFilesForCountry() {
    var country = window.editor.view.getFieldValue('MAILADDRESS.COUNTRYCODE');
    var currency = window.editor.view.getFieldValue('CURRENCY');
    if ( country != '' && currency != '' && isValidCountryCurrency(country, currency) ) {
        window.editor.showLoadingBar();
        var args = {
            'COUNTRY': country,
            'CURRENCY' : currency,
            'BANKFILEFORMAT': window.editor.view.getFieldValue('BANKFILEFORMAT')
        };
        window.editor.ajax(true, 'getBankFilesForCountry', args, c_getBankFilesForCountry, c_fail_ajaxRequest);
    } else {
        toggleBankFileAndACHTabs(false);
    }
}

function c_getBankFilesForCountry(data) {
    window.editor.hideLoadingBar();
    var country = window.editor.view.getFieldValue('MAILADDRESS.COUNTRYCODE');
    if ( typeof gBankFileMeta == 'undefined' ) {
        gBankFileMeta = [];
    }
    gBankFileMeta[country] = data[country]['BANKFILE_META'];
    gFileSpecificFields = data[country]['FILE_SPECIFIC_FIELDS'];
    var bankFiles = gBankFileMeta[country];
    var validvalues = [''];
    for ( opt in bankFiles ) {
        validvalues[validvalues.length] = opt;
    }
    var bankFileFld = window.editor.view.findComponents('BANKFILEFORMAT', 'Field');
    bankFileFld[0].updateProperty(['type', 'validvalues'], validvalues);
    bankFileFld[0].updateProperty(['type', 'validlabels'], validvalues);
    bankFileFld[0].setValue('');
    bankFileFld[0].redraw();

    renderBankFileUI('edit');
}

//works
function setSigUseLimit(signo, value)
{
	var fieldName = 'CHECKLAYOUT.USESIG' + signo + 'BELOWAMT';
	var amtField = window.editor.findComponents(fieldName);
	if ( amtField[0] ) 
	{
		amtField[0].setValue(value);
	}
}

//works         
function disableInterEntityWarning(ths)
{	
	SetNoCheckRequired();
	if (warnOnDisableIET(ths)) 
	{ 
		RefreshEditor();
	}
}

//works 
function warnOnDisableIET(obj) 
{
		if (ietwarn == '1' && obj && obj.checked) {
			var message = GT('IA.BANK_ACCOUNT_IET_CHANGE_CONFIRMATION');
			if (!confirm(message))
			{ 
				obj.checked = false;
				return false; 
			}
		}
		return true;
}

//works   
function RefreshEditor() {

	if(BeforeSubmit())
	{
        window.editor.submit(true, 'refresh', true);
    }

    return true;
}

//works
function warnOnChangingCurrency(curval) {
	var checkprintformat = window.editor.findComponents('CHECKLAYOUT.CHECKPRINTFORMAT')
	if(checkprintformat) {

		checkPrintFormat(curval);  
	}
	var _do = editor.getGlobalFieldValue('_do');

	if ( _do != 'create') 
	{
		if (!confirm(GT('IA.BANK_ACCOUNT_CURRENCY_CHANGE_CONFIRMATION')))
		{
			window.editor.gatherData();
			var data = window.editor.getView().getValue();

			var field = window.editor.findComponents('CURRENCY');
			if(field) {
				field[0].setValue(data['OLD_CURRENCY']);
			}
			return false;
		}
	}
	return true;
}

//works
function checkPrintFormat(val) {
	var printon = window.editor.findComponents('PRINTON','Field')[0].value;
    var currency = val ? val : window.editor.findComponents('CURRENCY','Field')[0].value;
	var checkprintformat = window.editor.findComponents('CHECKLAYOUT.CHECKPRINTFORMAT');
	checkprintformat = checkprintformat[0];
	var validLabels, validValues;
    var validHelpText = GT("IA.THE_DEFAULT_FORMAT_IS_STANDARD_CHOOSE_BUSINESS");

	if(currency === 'MXN')
	{ 
		validLabels = [GT("IA.STANDARD")];
		validValues = ['standard'];
	}
	else if (currency === 'CAD')
	{
        if (printon == 'P')
        {
            validLabels = [GT("IA.STANDARD"), GT("IA.CAD_CHECK"), GT("IA.CAD_CHECK_2"), GT("IA.CAD_FRENCH_PREPRINTED_CHECK")];
            validValues = ['standard', 'cad check', 'cad check 2.0','cad french preprinted check'];
        }
        else
        {
            validLabels = [GT("IA.STANDARD"), GT("IA.CAD_BLANK_CHECK"), GT("IA.CAD_FRENCH_BLANK_CHECK")];
            validValues = ['standard', 'cad blank check', 'cad french blank check'];
        }
    }
	else if ( currency === 'USD')
    {
        if (printon == 'P')
        {
            validLabels = [GT("IA.STANDARD"), GT("IA.HIGH_SECURITY"), GT("IA.CAD_CHECK"), GT("IA.CAD_CHECK_2"), GT("IA.CAD_FRENCH_PREPRINTED_CHECK")];
            validValues = ['standard', 'high security', 'cad check','cad check 2.0','cad french preprinted check'];
        }
        else
        {
            validLabels = [GT("IA.STANDARD"), GT("IA.BUSINESS"), GT("IA.CHASE_BUSINESS_CHECK"), GT("IA.CHASE_STANDARD_CHECK"), GT("IA.CAD_BLANK_CHECK"), GT("IA.CAD_FRENCH_BLANK_CHECK")];
            validValues = ['standard', 'business', 'jpmorgan chase business','jpmorgan chase standard','cad blank check','cad french blank check'];
        }
    }
	else if (printon == 'P')
	{
		validLabels = [GT("IA.STANDARD"), GT("IA.HIGH_SECURITY"), GT("IA.CAD_CHECK_2")];
		validValues = ['standard', 'high security','cad check 2.0'];
        validHelpText = GT("IA.THE_DEFAULT_FORMAT_IS_STANDARD_HIGH_SECURITY");
	}
	else 
	{ 
		validLabels = [GT("IA.STANDARD"), GT("IA.BUSINESS"), GT("IA.CAD_BLANK_CHECK")];
		validValues = ['standard', 'business','cad blank check'];
	}
	checkprintformat.type.validlabels = validLabels; 
	checkprintformat.type.validvalues = validValues;
    checkprintformat.updateProperty('helpText', validHelpText, true);
    checkprintformat.redraw();
	checkprintformat.updateMetadata();
}


function checkPaperFormat() {

    var printon = window.editor.findComponents('PRINTON','Field');
    printon = printon[0].value;
    var checkPaperFormat = window.editor.findComponents('CHECKLAYOUT.CHECKPAPERFORMAT');
    checkPaperFormat = checkPaperFormat[0];
    var validLabels;
    var validValues;
    var validHelpText =  GT("IA.CHOOSE_WHERE_YOU_WANT_THE_CHECK_ON_2_PART_CHECK");

    var currency = window.editor.findComponents('CURRENCY','Field');
    if(currency){
        currency = currency[0].value;
    }

    var printformat = window.editor.findComponents('CHECKLAYOUT.CHECKPRINTFORMAT','Field');
    if(printformat){
        printformat = printformat[0].value;
    }

    if (printon == 'P') {
        if (currency == 'CAD' || currency == 'USD') {
            validLabels = [GT("IA.TOP"), GT("IA.MIDDLE"), GT("IA.BOTTOM")];
            validValues = ['top', 'middle', 'bottom'];
        } else {
            validLabels = [GT("IA.TOP"), GT("IA.MIDDLE")];
            validValues = ['top', 'middle'];
        }
    } else {
        validLabels = [GT("IA.TOP"), GT("IA.MIDDLE"), GT("IA.BOTTOM")];
        validValues = ['top', 'middle', 'bottom'];
        validHelpText = GT("IA.CHOOSE_WHERE_YOU_WANT_THE_CHECK_TO_APPEAR_ON");
    }


    checkPaperFormat.type.validlabels = validLabels;
    checkPaperFormat.type.validvalues = validValues;
    checkPaperFormat.updateProperty('helpText', validHelpText, true);
    checkPaperFormat.redraw();
    checkPaperFormat.updateMetadata();
}


//works
function enablePaymentManager(ths)
{
	window.editor.gatherData();
	if (ths.checked)
	{ 
		if (!confirm(wfpmmsg)) 
		{ 
			ths.meta.setValue(false);
			return false; 
		} 
	
	} 
}

function OnLoadActivities(from) {
    OnLoad(from);
    handleBankRestrictions(from);
}

//works
function handleBankRestrictions(from)
{
	window.editor.gatherData();	
	var viewObj = window.editor.view;
	 
	var restrictionTypeObj = viewObj.getField('OBJECTRESTRICTION');
	FillBankLocations(restrictionTypeObj);

	var RestrictedBYLoc = viewObj.getField('RESTRICTEDBYLOC');	
	var LocID = viewObj.getField('LOCATIONID');					
	var RestrictiontoLOCS = viewObj.getField('RESTRICTIONTOLOCS');

	//Everything has to be defined to proceed.
	if (!(RestrictedBYLoc && LocID && RestrictiontoLOCS )) return;
	//if ('ONLOAD' == from && 'shownew' == state && RestrictiontoLOCS.getValue().length > 0) return;
	if ( 'shownew' != state  && 'LOCATIONID' == from &&  RestrictedBYLoc.getValue() == 'true' ) 
	{
		var data = window.editor.getView().getValue();
		var message = GT("IA.CHANGE_LOCATION_RESETS_AVAILABLE_AND_SELECTED");

		if (!confirm(message)) 
		{ 
			LocID.setValue(data['OLD_LOCATIONID']);
			return false;
		}
		else 
		{ 
			data['OLD_LOCATIONID'] = LocID.getValue();			
		} 
	}

	if ( true == ShowBankRestrictionUI )
	{
		if('true' == RestrictedBYLoc.getValue() )
		{	
			RestrictiontoLOCS.showHide(true);
		}
		else
		{
			RestrictiontoLOCS.showHide(false);
		}

		if('true' == RestrictedBYLoc.getValue() && '' !=  LocID.getValue() && 'ONLOAD' != from)
		{			
			var qrequest = new QRequest();
			var url = "qrequest.phtml";
			url = url + getArgsForQRequest(LocID.value, _sess, _op);
			var updateFunc = "ResponseProcessorBankAccount";
			var updateArgs = '';
			var waitMessage = GT("IA.WAIT_WHILE_SCREEN_IS_BEING_UPDATED");
			window.editor.view.addMessage(MESSAGE_WARNING, waitMessage, 5000);
			qrequest.quickRequest(url, updateFunc, updateArgs, true);
		}
	}
}

//FIll the Selcted Locations with Default Locations and Owner Locations
function FillBankLocations(restrictionTypeObj){
	window.editor.gatherData();
	var field = editor.findComponents('RESTRICTEDLOCATIONS');
	var restrictionType = restrictionTypeObj.value;
    if( field ) {
        field = field[0];
        if(restrictionType == 'Restricted') {            
			AssignOwnerLocations(field);
			AssignDefaultBankLocs(field);
			DisableLocations();
        }
    }
}

var ownerlocmsg = GT("IA.OWNER_LOCATION_RESTRICTION_MESSAGE");
var banklocmsg  = GT("IA.BANK_ACCOUNT_LOCATION_RESTRICTION_MESSAGE");
//Assign Owner Lcoations
function AssignOwnerLocations(field){
	ownerloc = window.editor.view.findComponents('LOCATIONID','Field');
	if(ownerloc){
		var ownerlocId = ownerloc[0].getValue();	
		var relocs = field.getValue();
		if(ownerlocId){
			if(relocs && relocs != '') {
				if(relocs.indexOf(ownerlocId) < 0) {
					ownerlocId += '#~#' + relocs;
				} else {
					ownerlocId = relocs;
				}
			}
			field.setValue(ownerlocId);
		}else if(ownerlocId == ''){
			jq("option[owner='owner']").remove();
			relocs = field.getValue();
			field.setValue(relocs);
		}
	}
}

//Assign Bank Locations
function AssignDefaultBankLocs(field){
	defaultlocs = window.editor.view.findComponents('DEFAULTBANK_LOCS','Field');
	if(defaultlocs){
		var defaultlocIds = defaultlocs[0].getValue();
		var rlocs = field.getValue();
		var banklocs = ''
		if(defaultlocIds){
			for(var key in defaultlocIds){
				if(key && key != ''){
					if(rlocs && rlocs != '' && rlocs.indexOf(key) < 0) {
						banklocs += key+'#~#';
					}
				}
			}
		}
		if(banklocs){
			if(rlocs && rlocs != '') { banklocs += rlocs; }
			field.setValue(banklocs);
		}
	}
}

function disableOption(LocID, msg){
	var Dmsg = (msg == 'owner') ? ownerlocmsg : banklocmsg;
    // Add double quote to escape any special chars like single quote in the location name
    LocID = '"'+LocID+'"';
	jq("option[value="+LocID+"]").css("color", "gray");
	jq("option[value="+LocID+"]").attr("owner", msg);
}

function LoadDefaultLocIds(Locids){
	if(Locids){
		for(var key in Locids){
			if(key != ''){
				disableOption(key, 'dbank');
                // Add double quote to escape any special chars like single quote in the location name
                LocID = '"'+key+'"';
				jq("option[value="+LocID+"]").attr("module", Locids[key]);
			}
		}
	}
}

function DisableLocations(from){
	var defaultlocs = window.editor.view.findComponents('DEFAULTBANK_LOCS','Field');
	if (defaultlocs) {
		var object = window.editor.view.findComponents('LOCATIONID','Field');
		if(object){
			var ownerloc = object[0].getValue();
			if(ownerloc != ''){
				disableOption(ownerloc, 'owner');
			}
		}
		if(defaultlocs){
			var dfloc = defaultlocs[0].getValue();
			if(dfloc != ''){
				LoadDefaultLocIds(dfloc);
			}
		}
		if(from == 'multipick'){
			var slocs = document.getElementsByName('selected__obj__RESTRICTEDLOCATIONS')[0];
			if(slocs){
				for(var i =0; i<slocs.length; i++){
					if (slocs[i].selected)  {
                        LocID = '"'+ slocs[i].value +'"';
						var loc = jq("option[value="+LocID+"]").attr("owner");
						if(loc){
							var Dmsg = '';
							if(loc == 'dbank'){
                                LocID = '"'+ slocs[i].value +'"';
								var modules = jq("option[value="+LocID+"]").attr("module");
								var modulestring = '';
								if(modules){
									modulestring = modules.replace(new RegExp("#", "g"), '\n');
								}
								Dmsg = banklocmsg+modulestring;
							}else{
								Dmsg = ownerlocmsg;
							}
							alert(Dmsg);
							return false;
						}
					}
				}
			}
		}
        var $restLocSelect = jq("select#selected__obj__RESTRICTEDLOCATIONS");
        $restLocSelect.unbind('dblclick');
        $restLocSelect.dblclick(function() {
			return false;
		});

		// Refresh multiselect component
        if (from === 'multipick' && typeof PAGE_LAYOUT_TYPE !== 'undefined' && PAGE_LAYOUT_TYPE === 'Q') {
            $restLocSelect.parents('.qx-multi-select.qx-ms-2').qxmultiselect().data('qx_qxmultiselect').refresh();
        }
	}

	return true;
}

function doLOCBack(name) {
	var bj = true;
	//this function will work only in bank restrictions
	var defaultlocs = window.editor.view.findComponents('DEFAULTBANK_LOCS','Field');
	if(defaultlocs){
		bj = DisableLocations('multipick');
	}
	if (bj) {
		available = document.getElementsByName('available_'+name)[0];
		selected = document.getElementsByName('selected_'+name)[0];
		svalues = collectValues(selected);
		removeAll(selected);
		setValues(selected, svalues[UNCHECKED]);
		appendValues(available, svalues[CHECKED]);

		svalues = collectAll(selected);
		currval = document.getElementsByName(name)[0];
		if( currval ) currval.value = svalues[VALUE].join('#~#');
	}

	//Once Again Change that to our settings
	if(defaultlocs){
		DisableLocations();
	}
}

function autofill(from ) {
		window.editor.gatherData();	

		// get the current expnse report number
	var achbankid = window.editor.view.findComponents('ACHBANKID');
	var v_achbankid = achbankid[0].value;

			var qrequest = new QRequest();
			//var url = "qrequest.phtml";
	var url = 'qrequest.phtml?.function=GetAchBankDetails&.handler=QRequest&.entity=bankaccount&.sess='+sess+'&.otherparams=achbankid&.achbankid='+escape(v_achbankid);
			var updateFunc = "responseprocessorachbank";
			var updateArgs = '';
			qrequest.quickRequest(url, updateFunc, updateArgs, true);

}

//works
function getArgsForQRequest(val, sess, op) 
{
	var url  = '?.function=GetAvailableLocations&.entity=bankaccount&.sess='+sess+'&.qop='+op;
		url += '&.otherparams=LocationID&.LocationID='+val;
	return url;
}
function responseprocessorachbank(m_response) {

	var nodes = m_response.getElementsByTagName("achbank");
		var destinationid = nodes.item(0).getAttribute("destinationid");
		var originid = nodes.item(0).getAttribute("originid");
		var originname = nodes.item(0).getAttribute("originname");

	//Populates the Available section
	
var viewObj = window.editor.view;
	var companyname = viewObj.getField('COMPANYNAME');
	var companyidentification = viewObj.getField('COMPANYIDENTIFICATION');
	var financialinstitution = viewObj.getField('FINANCIALINSTITUTION');
	companyname.setValue(originname);
	companyidentification.setValue(originid);
	financialinstitution.setValue(destinationid);

}
//works
function ResponseProcessorBankAccount(m_response) {
	window.editor.gatherData();	
	var viewObj = window.editor.view;

	var RestrictiontoLOCS = viewObj.getField('RESTRICTIONTOLOCS');
	var availableList = m_response.getElementsByTagName('availableList');
	var availableListValues = m_response.getElementsByTagName('value');
	var selectedListValues = m_response.getElementsByTagName('selectedList');

	var AvailableValues = '';
	var AvailableValuesMap = new Array();
	var selectedValue =  '';

	if (availableListValues.length && availableList.length) 
	{			
		for (var i = 0; i < availableListValues.length; i++) 
		{
			AvailableValuesMap.push(new Array(availableListValues[i].firstChild.data));
		}
	}

	if (selectedListValues.length) 
	{
		if(selectedListValues[0])
		{
			selectedValue = selectedListValues[0].firstChild.data;
			AvailableValuesMap.push(new Array(selectedListValues[0].firstChild.data));
		}
	}
	//Populates the Available section
	RestrictiontoLOCS.updateValues(AvailableValuesMap);
	//the issue is, when availableValueMap has everything, then  it also populates to selected value
	//but when availableValueMap has actualy brought something then it does not populate the selectedValue thing.

	//Populates the Selected section 	
	RestrictiontoLOCS.setValue(selectedValue);

}


function Redirect(aUrl) 
{
	window.location.href = aUrl;
}

