function setDate(dat){
	inputDateRet=dat.value;
	if(inputDateRet==""){
		return inputDateRet;
	}

	var fval = ourFormatDate(inputDateRet);
	if (fval) {
		dat.value = fval;
	}
}

function ourFormatDate(inputDate) {
	//this function works only with numbers any thing other then number must be handled 
	//by date pciker control
	try{
		var intValue = parseInt(inputDate, 10);
		if(isNaN(intValue)) {
			return;
		}
	}catch(ex) {
		return;
	}
	
	var delimNo;
	var delim=dateType.substring(0,1);
	var pos;	
	if(dateType.indexOf("Y")!=-1)
	{
	  pos=dateType.indexOf("Y");
	  smallbig="b";
	}
	if(dateType.indexOf("y")!=-1)
	{
		pos=dateType.indexOf("y");
		smallbig="s";
	}
	var dateEntered;
	var groomedDate=groomDate(delim,inputDate,false);	
	if (((delim==".") || (delim=="-"))
	 ? ((groomedDate.indexOf("/"))!=-1)
	 : ((groomedDate.indexOf(delim))!=-1)) {	
		 if((delim==".")||(delim=="-")){		 
			 delimNo=(groomedDate.indexOf("/")==groomedDate.lastIndexOf("/"))?1:2;
		 }else{
			 delimNo=(groomedDate.indexOf(delim)==groomedDate.lastIndexOf(delim))?1:2;
		 }
	}	
	var fval=mapDates(groomedDate,dateType,delim,delimNo,pos,smallbig);	

	if (fval) {
		return groomDate(delim,fval,true);
	}
	return '';
}

function groomDate(delim,dateEntered,filter){

	dateEntered=trim(dateEntered,'both');
	dateEntered=trimLChar(dateEntered);

	if(!filter){
		delim=((delim==".")||(delim=="-"))?"/":delim;
	}
	var i;
	var fin="";	
	for(i=0;i<=dateEntered.length;i++){              
		if((isNaN(dateEntered.substring(i,i+1)))||(dateEntered.substring(i,i+1)==' ')){	    
		fin=fin+delim;		
		}else{
		fin=fin+dateEntered.substring(i,i+1);
		}		
	}	
	return fin;	
}

function trimLChar(arg){

	if(isNaN(arg.substring(arg.length-1,arg.length))){
		return arg.substring(0,arg.length-1);
	}else{
		return arg;
	}

}


function trim(arg,func) {
var trimvalue = "";
arglen = arg.length;
if (arglen < 1) return trimvalue;

if (func == "left" || func== "both") {
	i = 0;
	pos = -1;
	while (i < arglen) {
		if (arg.charCodeAt(i) != 32 && !isNaN(arg.charCodeAt(i))) {
			pos = i;
			break;
		}
		i++;
	}
}

if (func == "right" || func== "both") {
	var lastpos = -1;
	i = arglen;
	while (i >= 0) {
		if (arg.charCodeAt(i) != 32 && !isNaN(arg.charCodeAt(i))) {
			lastpos = i;
			break;
		}
		i--;
	}
}

if (func == "left") {
		trimvalue = arg.substring(pos,arglen-1);
	}

if (func == "right") {
	trimvalue = arg.substring(0,lastpos+1);
}

if (func == "both") {
	trimvalue = arg.substring(pos,lastpos + 1);
}

return trimvalue;

}

function mapDates(groomedDate,dateType,delim,delimNo,pos,smallbig){
	var da=new Date();
	var do_alerts = 0;
	try{
		if(suppress_alerts == 1) {
			do_alerts = 1;
		}
	} catch(ex){}
	//var year = da.getYear();

	// IE uses obsolete getYear behavior.  W3C-compliant return years since 1900.  For W3C, to convert to
	//   full year, add 1900.  NOTE: browser sniffing directly instead of globalIs for now - don't know if
	//   there is code that uses this without including base_lib.
	//if (navigator.userAgent.toLowerCase().indexOf('msie') == -1)
	//	year += 1900;

	delim=(delim==".")?"/":delim;
	if(isNaN(groomedDate)){             
			if(groomedDate.length >=10){			
				  if(groomedDate.length==10){					 
					  return  groomedDate;           
					}
			  if(do_alerts  == 0) {
				  alert ("........invalid length for date........."); 
			  }	  
			  return  groomedDate;           
			}      
			if(pos==1){			
			  return  front(groomedDate,dateType,delim,delimNo,pos,smallbig);           
			}
			if(pos==3){		
			   return last(groomedDate,dateType,delim,delimNo,pos,smallbig);
			}
	}
	else{
		if(groomedDate.length==8){
			 if(pos==3){
				 return groomedDate.substring(0,2)+delim+groomedDate.substring(2,4)+delim+groomedDate.substring(4,8);
			 }
			 if(pos==1){
				 return groomedDate.substring(0,4)+delim+groomedDate.substring(4,6)+delim+groomedDate.substring(6,8);
			 }
		} 
		if((groomedDate.length!=4)&&(groomedDate.length!=6)&&(groomedDate.length!=8)){
			if(do_alerts == 0) {
				alert("....Invalid Date - Date entered is not recognized as a valid date....");
			}
			 return ;
		}
		if((groomedDate.length==4)&&(pos==3)){
			 if(smallbig=="b"){
				 return groomedDate.substring(0,2)+delim+groomedDate.substring(2,4)+delim+da.getFullYear();
			 }
			 if(smallbig=="s"){			 
				 return groomedDate.substring(0,2)+delim+groomedDate.substring(2,4)+delim+(new String((da.getFullYear())).substring(2,4));
			 }
		}
		if((groomedDate.length==4)&&(pos==1)){		
			if(smallbig=="b"){
				 return da.getFullYear()+delim+groomedDate.substring(0,2)+delim+groomedDate.substring(2,4);
			 }
			 if(smallbig=="s"){			 
				 return (new String((da.getFullYear())).substring(2,4))+delim+groomedDate.substring(0,2)+delim+groomedDate.substring(2,4);
			 }
		}		
		if((groomedDate.length==6)&&(pos==3)){
			 if(smallbig=="b"){
			 return groomedDate.substring(0,2)+delim+groomedDate.substring(2,4)+delim+(new String(da.getFullYear())).substring(0,2)+groomedDate.substring(4,6);		        
			 }
			 if(smallbig=="s"){			 
				 return groomedDate.substring(0,2)+delim+groomedDate.substring(2,4)+delim+groomedDate.substring(4,6);
			 }		     
		}
		if((groomedDate.length==6)&&(pos==1)){
			 if(smallbig=="b"){
			 return "20"+groomedDate.substring(0,2)+delim+groomedDate.substring(2,4)+delim+groomedDate.substring(4,6);
				
			 }
			 if(smallbig=="s"){			 
				 return groomedDate.substring(0,2)+delim+groomedDate.substring(2,4)+delim+groomedDate.substring(4,6);
			 }
		}
	}
}
function front(groomedDate,dateType,delim,delimNo,pos,smallbig){
  if(smallbig=="s"){
   return  fsmall(groomedDate,dateType,delim,delimNo,pos,smallbig);
  }
  if(smallbig=="b"){
	return fbig(groomedDate,dateType,delim,delimNo,pos,smallbig);
  }   
}
function last(groomedDate,dateType,delim,delimNo,pos,smallbig){
	  if(smallbig=="s"){
	   return  lsmall(groomedDate,dateType,delim,delimNo,pos,smallbig);
	  }
	  if(smallbig=="b"){
		return lbig(groomedDate,dateType,delim,delimNo,pos,smallbig);
	  } 
}
function fsmall(groomedDate,dateType,delim,delimNo,pos,smallbig){
var da=new Date();
	if(((groomedDate.length==3)||(groomedDate.length==4)||(groomedDate.length==5))&&(delimNo==1)){
	 
	   groomedDate= (new String((da.getFullYear())).substring(2,4))+delim+groomedDate;
	  return groomedDate;
	} 
	if(delimNo==2){
		if((isNaN(groomedDate.substring(0,2)))||((groomedDate.substring(1,2))==' ')) {
		   groomedDate="0"+ groomedDate;
		   return groomedDate;
		}
		if(groomedDate.length==8){		   
		   return groomedDate;
		}
	}
}
function lsmall(groomedDate,dateType,delim,delimNo,pos,smallbig){ 
var da=new Date();

	if(isNaN(groomedDate))
	{
			if(((groomedDate.length==3)||(groomedDate.length==4)||(groomedDate.length==5))&&(delimNo==1)){			
			  groomedDate= groomedDate+delim+(new String((da.getFullYear())).substring(2,4));
			  return groomedDate;
			}
			if(delimNo==2){
				if((isNaN(groomedDate.substring(groomedDate.length-2,groomedDate.length)))||((groomedDate.substring(groomedDate.length-2,groomedDate.length-1))==' ')) {
				   groomedDate= groomedDate.substring(0,groomedDate.length-1)+'0'+groomedDate.substring(groomedDate.length-1,groomedDate.length);					
				   return groomedDate;
				}
				if(!(isNaN(groomedDate.substring(groomedDate.length-2,groomedDate.length)))&&(isNaN(groomedDate.substring(groomedDate.length-3,groomedDate.length-2))) ){
			   groomedDate= groomedDate.substring(0,groomedDate.length-2)+""+groomedDate.substring(groomedDate.length-2,groomedDate.length);
					return groomedDate;
			   }	

			}
	}
	else{
		  if(groomedDate.length==4){			
			  groomedDate= groomedDate.substring(0,2)+delim+groomedDate.substring(2,4)+delim+(new String((da.getFullYear())).substring(2,4));
			  return groomedDate;
			}
			if(groomedDate.length==6){			
			  groomedDate= groomedDate.substring(0,2)+delim+groomedDate.substring(2,4)+delim+groomedDate.substring(4,6);
			  return groomedDate;
			}
	}
}
function fbig(groomedDate,dateType,delim,delimNo,pos,smallbig){
	var da=new Date();
   if(((groomedDate.length==3)||(groomedDate.length==4)||(groomedDate.length==5))&&(delimNo==1)){     
	   groomedDate=da.getFullYear()+delim+groomedDate;
	   return groomedDate;
	} 
	if(delimNo==2){	
		if((isNaN(groomedDate.substring(0,2)))||(groomedDate.substring(1,2)==' ')||(groomedDate.substring(2,3)==' ')){
		   if(groomedDate.substring(2,3)==' '){		  
			   groomedDate="20"+ groomedDate;
		   }else{
			   groomedDate="200"+ groomedDate;
		   }
			return groomedDate;
		}
		if(groomedDate.length==8){		
		   if(!(isNaN(groomedDate.substring(3,1)))){
		   return groomedDate;
		   }else{
			   return 20+groomedDate;
		   }
		}
	}
}
function lbig(groomedDate,dateType,delim,delimNo,pos,smallbig){
var da=new Date();
   if(isNaN(groomedDate))
	{
		if(((groomedDate.length==3)||(groomedDate.length==4)||(groomedDate.length==5))&&(delimNo==1)){		
		  groomedDate=groomedDate+delim+da.getFullYear();
		  return groomedDate;
		}
		if(delimNo==2){
		
			if(isNaN(groomedDate.substring(groomedDate.length-2,groomedDate.length))||((groomedDate.substring(groomedDate.length-2,groomedDate.length-1))==' ')||((groomedDate.substring(groomedDate.length-3,groomedDate.length-2))==' ')){
			   groomedDate= groomedDate.substring(0,groomedDate.length-1)+"200"+groomedDate.substring(groomedDate.length-1,groomedDate.length);
			   return groomedDate;
			}			if(!(isNaN(groomedDate.substring(groomedDate.length-2,groomedDate.length)))&&(isNaN(groomedDate.substring(groomedDate.length-3,groomedDate.length-2))) ){
			   groomedDate= groomedDate.substring(0,groomedDate.length-2)+"20"+groomedDate.substring(groomedDate.length-2,groomedDate.length);
			   return groomedDate;
			}	
		}
	}
	else{
		  if((groomedDate.length)==4){			
			  groomedDate= groomedDate.substring(0,2)+delim+groomedDate.substring(2,4)+delim+(new String((da.getFullYear())));
			  return groomedDate;
			}			
			if(groomedDate.length==6){			
			  groomedDate= groomedDate.substring(0,2)+delim+groomedDate.substring(2,4)+delim+(new String((da.getFullYear())).substring(0,2))+groomedDate.substring(4,6);
			  return groomedDate;
			}
			if(groomedDate.length==8){			
			  groomedDate= groomedDate.substring(0,2)+delim+groomedDate.substring(2,4)+delim+groomedDate.substring(4,8);
			  return groomedDate;
			}
	}
}


function leadingZeros(num, totalChars, padWith)
{
	num = num + "";
	padWith = (padWith) ? padWith : "0";

	if (num.length < totalChars)
	{
		while (num.length < totalChars)
		{
			num = padWith + num;
		}
	}

	if (num.length > totalChars)
	{ //if padWith was a multiple character string and num was overpadded
		num = num.substring((num.length - totalChars), totalChars);
	}

	return num;
}

/**
 * Timestamp object that is 'derived' from the Date prototype to support time
 */
function Timestamp()
{
    this.date = new Date();
    this.dateDelim = ' ';
    this.timeDelim = ':';
}

/**
 * This function parse a timestamp in user format from string.
 */
Timestamp.prototype.parseFromUserFormat = function(dateStr)
{
    var ts =  this.parseFromDateFormat(window.UserDateFormat, window.UserTimeFormat, dateStr);
    var timeZoneOffset = window.UserTimeZoneOffset;
    if ( timeZoneOffset != 0 ) {
        var t = Math.floor(timeZoneOffset);
        ts.date.setUTCHours(ts.date.getUTCHours() - t);
        if ( t != timeZoneOffset ) {
            ts.date.setUTCMinutes(ts.date.getUTCMinutes() - (timeZoneOffset - t) * 60);
        }
    }
    return ts;
};

/**
 * This function parse a timestamp in system format from string.
 */
Timestamp.prototype.parseFromSystemFormat = function(dateStr)
{
    return this.parseFromDateFormat('/mdY', 'HH24:MI:SS', dateStr);
};

/**
 * This function converts a timestamp to a user format string.
 */
Timestamp.prototype.toUserFormat = function()
{
    return this.toDateFormat(window.UserDateFormat, window.UserTimeFormat, window.UserTimeZoneOffset);
};

/**
 * This function converts the time portion of a timestamp to the specified string format.
 */
Timestamp.prototype.toUserTimeFormat = function()
{
    return this.toDateFormat(false, window.UserTimeFormat, window.UserTimeZoneOffset);
};

/**
 * This function converts the time portion of a timestamp to the specified string format
 * without the seconds and no leading zero for the hours.
 */
Timestamp.prototype.toShortUserTimeFormat = function()
{
    return this.formatTimestamp(false, window.UserTimeFormat, window.UserTimeZoneOffset, false);
};

/**
 * This function converts the timestamp to an eloquent format with or without a [prefix].
 *
 * Output formats:
 *   [at] time - If the timestamp is any time today
 *   yesterday [at] time - If the timestamp is any time yesterday
 *   [on] date - If the timestamp is before yesterday
 *
 */
Timestamp.prototype.toUserEloquentFormat = function(includePrefix)
{
    var midnight = new Date();
    midnight.setHours(0, 0, 0, 0);
    var yesterday = new Date(midnight);
    yesterday.setDate(midnight.getDate() - 1);

    if ( this.date >= yesterday ) {
        var ret = "";
        if (this.date < midnight) {
            ret = " yesterday ";
        }
        var prefix;
        if (typeof includePrefix == "String") {
            prefix = includePrefix + ' at ';
        } else {
            prefix = includePrefix ? ' at ' : '';
        }
        return ret + prefix + this.toShortUserTimeFormat();
    } else {
        var prefix;
        if (typeof includePrefix == "String") {
            prefix = includePrefix + ' on ';
        } else {
            prefix = includePrefix ? ' on ' : '';
        }
        return prefix + this.date.toUserLongFormat();
    }

};


/**
 * This function converts a timestamp to a system format string.
 */
Timestamp.prototype.toSystemFormat = function()
{
    return this.toDateFormat('/mdY', 'HH24:MI:SS', 0);
};

/**
 * This function converts a timestamp to the specified string format.
 */
Timestamp.prototype.toDateFormat = function(dateFormat, timeFormat, timeZoneOffset)
{
    return this.formatTimestamp(dateFormat, timeFormat, timeZoneOffset, true);
};

Timestamp.prototype.formatTimestamp = function(dateFormat, timeFormat, timeZoneOffset, fullFormat)
{
    var newDate = new Date();
    newDate.setTime(this.date.getTime());

    if ( timeZoneOffset != 0 ) {
        var t = Math.floor(timeZoneOffset);
        newDate.setUTCHours(newDate.getUTCHours() + t);
        if ( t != timeZoneOffset ) {
            newDate.setUTCMinutes(newDate.getUTCMinutes() + (timeZoneOffset - t) * 60);
        }
    }

    // Build the date string
    var date = dateFormat == false ? false : newDate.toDateFormat(dateFormat, true);

    // Convert the hours on the timezone with the offset
    // If we arrive to a negative date it means we went back one day.
    var hours = newDate.getUTCHours();
    if ( hours < 0 ) {
        hours += 24;
    }

    // We need to handle HH12 format
    // If we are in HH12 format we need to adjust the hours and add the meridiem
    var meridiem = '';
    if ( timeFormat == 'HH12:MI:SSAM' ) {
        meridiem = ' AM';
        if ( hours >= 12 ) {
            hours -= 12;
            meridiem = ' PM';
        }
		if ( hours == 0 ) {
			hours = 12;
		}
    }

    // Build the final time and timestamp string
    var timeArr;

    if ( ! fullFormat ) {
        timeArr = [
            hours,
            leadingZeros(newDate.getUTCMinutes(), 2)
        ];
    } else {
        timeArr = [
            leadingZeros(hours, 2),
            leadingZeros(newDate.getUTCMinutes(), 2),
            leadingZeros(newDate.getUTCSeconds(), 2)
        ];
    }
    var time = timeArr.join(this.timeDelim);

    var ret;
    if ( date != false ) {
        ret = date + this.dateDelim;
    } else {
        ret = '';
    }
    return ret + time + meridiem;
};

/**
 * This function parse a timestamp in the specified format from a string.
 */ 
Timestamp.prototype.parseFromDateFormat = function(dateFormat, timeFormat, dateStr) 
{
    if ((dateFormat.charAt(0)) == " ") {
        var onlyDate = dateStr.split(" ");
        onlyDateStr = onlyDate[0]+' '+onlyDate[1]+' '+onlyDate[2];
    } else {
        onlyDateStr = dateStr.split(" ")[0];
    }
    // Parse the date
    var date = this.date.parseFromDateFormat(dateFormat, onlyDateStr, true);
    
    // Extract the time
    var timeRegEx = new RegExp('((0[1-9]|1[0-2])|[0-2][0-9]):[0-5][0-9]:[0-5][0-9]');
    var time = dateStr.match(timeRegEx);
    if ( !time || !time[0] ) {
        return this;
    }

    // Extract the hours/minutes/seconds
    var timeStr = time[0];  
    var timeArr = timeStr.split(this.timeDelim);
    var hours = timeArr[0] ? timeArr[0] : 0;
    var minutes = timeArr[1] ? timeArr[1] : 0;
    var seconds = timeArr[2] ? timeArr[2] : 0;
    
    // We need to handle HH12 format 
    // In JS all the date are in HH24 so we will convert it back
    if ( timeFormat == 'HH12:MI:SSAM' ) {
		if ( hours == 12 ) {
			hours = 0;
		}
        if ( dateStr.indexOf('PM') != -1 ) {
            hours = parseInt(hours) + 12;
        }
    }
    
    date.setUTCHours(hours, minutes, seconds);
    
    this.date = date;
        
    return this;
};


var iso8601DateFormatRegExp = /(\d\d\d\d-\d\d-\d\dT\d\d:\d\d:\d\d\.\d\d\d)(Z|(\+(\d\d):?(\d\d)?))/;

Timestamp.prototype.parseFrom8601Format = function (str) {

    // ECMAScript defines the timezone offset of a date format string as having a colon
    // (see http://www.ecma-international.org/ecma-262/5.1/#sec-*********).  IE only accepts
    // this format.  Other browsers seem to accept any ISO 8601 date format.  Salesforce returns timezone
    // offsets without the colon.  Convert the incoming date to ECMAScript format.
    var matches = str.match(iso8601DateFormatRegExp);
    if ( ! matches ) {
        return false;
    } else {
        if ( matches[4] ) {
            if ( matches[5] ) {
                str = matches[1] + "+" + matches[4] + ':' + matches[5];
            } else {
                str = matches[1] + "+" + matches[4] + ':00';
            }
        } else {
            str = matches[1] + matches[2];
        }
    }

    this.date = new Date(str);
    return this;

};

/**
 * This function parse a date in user format from string.
 * The user format is read from the window.UserDateFormat variable
 * 
 * @param dateStr the date string to parse
 */
Date.prototype.parseFromUserFormat = function(dateStr)
{
	return this.parseFromDateFormat(window.UserDateFormat, dateStr);
};

/**
 * This function parse a date in system format from string.
 * The system format is /dmY
 * 
 * @param dateStr the date string to parse
 */
Date.prototype.parseFromSystemFormat = function(dateStr)
{
	return this.parseFromDateFormat('/mdY', dateStr);
};

/**
 * This function converts the date to user format.
 * The user format is read from the window.UserDateFormat variable
 */
Date.prototype.toUserFormat = function()
{
	return this.toDateFormat(window.UserDateFormat);
};

/**
 * This function converts the date to a long form of the user format.
 * This is the user format with the month spelled out and appropriate
 * punctuation.
 *
 * The user format is read from the window.UserDateFormat variable
 */
Date.prototype.toUserLongFormat = function()
{
    return this.toLongDateFormat(window.UserDateFormat);
};

/**
 * This function converts the date to system format.
 * The system format is /dmY
 */
Date.prototype.toSystemFormat = function()
{
	return this.toDateFormat('/mdY');
};

/**
 * This function converts the date to the specified format.
 * 
 * @paran dateFormat - the format for the date
 * @param isUTC      - true if the caller wants UTC values
 */
Date.prototype.toDateFormat = function(dateFormat, isUTC)
{
	var twoYearDigits = false;
	var delim = dateFormat.substring(0, 1);
	var dayPos = dateFormat.indexOf('d') - 1;
	var monthPos = dateFormat.indexOf('m') - 1;
	var yearPos = dateFormat.indexOf('Y') - 1;
	
	if( yearPos < 0 )
	{
		twoYearDigits = true;
		yearPos = dateFormat.indexOf('y') - 1;
	}
	
	var day = isUTC ? this.getUTCDate() : this.getDate();
	var month = (isUTC ? this.getUTCMonth() : this.getMonth()) + 1;
	var year = isUTC ? this.getUTCFullYear() : this.getFullYear();
	
	if( twoYearDigits ) year = year % 100;
	
	var dateArr = [ '', '', ''];
	dateArr[dayPos] = leadingZeros(day, 2);
	dateArr[monthPos] = leadingZeros(month, 2);
	dateArr[yearPos] = leadingZeros( year, twoYearDigits ? 2 : 4 );
	
	return dateArr.join(delim);
};

/**
 * This function converts the date to the specified format.
 *
 * @paran dateFormat - the format for the date
 * @param isUTC      - true if the caller wants UTC values
 */
Date.prototype.toLongDateFormat = function(dateFormat, isUTC)
{
    var MONTHS = ['January', 'February', 'March', 'April', 'May', 'June',
                  'July', 'August', 'September', 'October', 'November', 'December'];
    var dayPos = 2 * ( dateFormat.indexOf('d') - 1 );
    var monthPos = 2 * ( dateFormat.indexOf('m') - 1 );
    var yearPos = 2 * ( dateFormat.indexOf('Y') - 1 );

    if( yearPos < 0 ) {
        yearPos = 2 * ( dateFormat.indexOf('y') - 1 );
    }

    var day = isUTC ? this.getUTCDate() : this.getDate();
    var month = (isUTC ? this.getUTCMonth() : this.getMonth());
    var year = isUTC ? this.getUTCFullYear() : this.getFullYear();

    var dateArr = [ '', ' ', '', ' ', ''];
    dateArr[dayPos] = day;
    dateArr[monthPos] = MONTHS[month];
    dateArr[yearPos] = year;

    if ( monthPos == 0 && dayPos == 2 && yearPos == 4 ) {
        dateArr[3] = ", ";
    }

    return dateArr.join('');
};



/**
 * This function parse a date in the specified format from string.
 * 
 * @paran dateFormat - the format for the date
 * @param dateStr the date string to parse
 * @param isUTC      - true if the caller wants UTC values
 */
Date.prototype.parseFromDateFormat = function(dateFormat, dateStr, isUTC)
{
	var twoYearDigits = false;
	var delim = dateFormat.substring(0, 1);
	var dayPos = dateFormat.indexOf('d') - 1;
	var monthPos = dateFormat.indexOf('m') - 1;
	var yearPos = dateFormat.indexOf('Y') - 1;
	
	if( yearPos < 0 )
	{
		twoYearDigits = true;
		yearPos = dateFormat.indexOf('y') - 1;
	}
	
	var dateArr = dateStr.split(delim);
	if( dateArr.length < 3 )
	{
		return this;
	}
	
	var day = Number(dateArr[dayPos]);
	if( isNaN(day) || day == 0 ) day = this.getDate();
	
	var month = Number(dateArr[monthPos]);
	if( isNaN(month) || month == 0 ) month = this.getMonth();
	else month--;
	
	var year = Number(dateArr[yearPos]);
	if( isNaN(year) || year == 0 ) year = this.getFullYear();
	
	if( twoYearDigits && year < 100 ) year = 2000 + year;
	
	if ( isUTC ) {
        this.setUTCFullYear(year, month, day);
        // let's reset the time value to avoid any issues when comparing same dates
        this.setUTCHours(0);
        this.setUTCMinutes(0);
        this.setUTCSeconds(0);
        this.setUTCMilliseconds(0);
    } else {
        this.setFullYear(year, month, day);
        // let's reset the time value to avoid any issues when comparing same dates
        this.setHours(0);
        this.setMinutes(0);
        this.setSeconds(0);
        this.setMilliseconds(0);
    }
	
	return this;
};

function DateAddDays(date, days)
{
	var inputWasString = false;
	if( typeof date == 'string' )
	{
		inputWasString = true;
		// here we asume the date is in the std format MM/DD/YYYY
		// we should change this code to deal with user formats
		var elements = date.split('/');
		if( elements.length != 3 ) return date;
		
		var month = parseInt(elements[0], 10) - 1;
		var day = parseInt(elements[1], 10);
		var year = parseInt(elements[2], 10);
		if( year < 100 ) year = 2000 + year;
		
		var date = new Date();
		date.setFullYear(year, month, day);
	}
	
	date.setTime( date.getTime() + days * 24 * 60 * 60 * 1000 );
	
	if( inputWasString )
	{
		output = leadingZeros(date.getMonth() + 1, 2) + '/' + leadingZeros(date.getDate(), 2) + '/' + date.getFullYear();
		return output;
	}
	else return date;
}

/**
 * This function will convert /mdY to mm/dd/yyyy
 * This utility is used for jquery datepick
 * @param input_string
 * @returns {String}
 */
function getDateFormatString(input_string){
	//this is the defualt format 
	var result_string = "mm/dd/YYYY";
	
	//default checking
	if(input_string.length > 4) {
		return result_string;
	}
	var internal_result = "";
	var delimiter = input_string.charAt(0);
	
	var yearDone = false;
	var monthDone = false;
	var dayDone = false;
	
	for(var i=1;i<input_string.length;i++) {
		var format_char = input_string.charAt(i);
		if(format_char == 'y' && !yearDone) {
			internal_result = internal_result + 'y';
			yearDone = true;
		}
		if(format_char == 'Y' && !yearDone) {
			internal_result = internal_result + 'yy';
			yearDone = true;
		}
		if(format_char == 'm' && !monthDone) {
			internal_result = internal_result + 'mm';
			monthDone = true;
		}
		if(format_char == 'd' && !dayDone) {
			internal_result = internal_result + 'dd';
			dayDone = true;
		}
		
		if(i != 3) {
			internal_result = internal_result + delimiter;
		}
	}
	//check one more time to make sure 
	//result has all the elements
	if(yearDone && monthDone && dayDone) {
		result_string = internal_result;
	}
	return result_string;
}