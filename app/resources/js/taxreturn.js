var TAXSOLUTION_GB = 'United Kingdom - VAT';
var TAXSOLUTION_ZA = 'South Africa - VAT';
var TAXSOLUTION_CA = 'Canadian Sales Tax - SYS';
var TAXSOLUTION_FR = 'TVA française - SYS';
var TAXSOLUTION_DE = 'Deutsche Umsatzsteuer - SYS';
var TAXSOLUTION_IE = 'Ireland VAT - SYS';
var TAXSOLUTION_AU = 'Australia - GST';
var TAXSOLUTION_NZ = 'New Zealand GST - SYS';
var TAXSOLUTION_SG = 'Singapore GST - SYS';
var tax_steps = ['prepare_tax_data' , 'transmit_tax_data' , 'prepare_tax_return' , 'transmits_rr_files_to_intacct' ,'submit_to_government','manual_adjustment'] ;

function showProcessTaxDataPage() {
    //We have to validate the view data ourselves since we override the the default "Save" action of our UI framework
    if (!view.validateData()) {
        return; //We can't validate, we return as validateData will prompt error msgs
    }

    var taxID = window.view.value.TAXID;
    var args = {taxID: taxID};
    window.editor.showLoadingBar();
    window.editor.ajax(true, 'getRecipientsEmailForTaxID', args, populateRecipientsEmail, populateRecipientsEmailError);
}
function submitForm(action) {
    var taxSolutionId = window.view.value.TAXSOLUTIONID;
    var taxID = window.view.value.TAXID;
    if (!isSageRRTaxSolution(taxSolutionId)) {
        var startDate = window.view.value.PROCESSTAXDATAOFFLINE.SUBMISSIONPERIODSTARTDATE;
        var endDate = window.view.value.PROCESSTAXDATAOFFLINE.SUBMISSIONPERIODENDDATE;

        var args = {taxID: taxID, taxSolutionId: taxSolutionId, submissionStartDate: startDate , submissionEndDate: endDate};
        window.editor.ajax(true, 'validateSubmissionDates', args, function(params) {
            validateSubmissionDatesSuccess(params, action);
        }, validateSubmissionDatesError);
    } else {
        var emailToNotify = window.editor.view.findComponents('RECIPIENTSEMAIL', 'Field')[1].value;
        var ok = true;
        if (!emailToNotify || emailToNotify === '') {
            alert(GT('IA.EMAIL_TO_NOTIFY_CANNOT_BE_EMPTY'));
            ok = false;
        } else {
            var validRegExp = /(^$|^[_\w\-!#\$%&\'\*\+\\/=\?\^`\{\|}~]+(\.[_\w\-!#\$%&\'\*\+\\/=\?\^`\{\|}~]+)*@([\w\-]+\.)+[a-zA-Z]{2,}$)/i;
            emailToNotify = emailToNotify.replace(";", ",");
            var strEmail = emailToNotify.split(",");
            for (i = 0; i < strEmail.length; i++) {
                var splitedEMail = strEmail[i];
                if (splitedEMail.search(validRegExp) == -1) {
                    alert(GT({
                        "id": "IA.EMAIL_SPLITEDEMAIL_IS_INVALID_EMAIL_ID",
                        "placeHolders": [
                            {"name": "SPLITEDEMAIL", "value": splitedEMail}
                        ]
                    }));
                    ok = false;
                }
            }
        }
        if (!ok) {
            return;
        }
        window.editor.submit(true, action, true);
    }
}

function validateSubmissionDatesSuccess(params, action) {
    var endDate = window.view.value.PROCESSTAXDATAOFFLINE.SUBMISSIONPERIODENDDATE;
    var taxSolutionId = window.view.value.TAXSOLUTIONID;
    window.view.clearMessages(MESSAGE_ERROR);

    if (params.SUCCESS === 1) {
        window.editor.submit(true, action, true);
    } else if (params.SUCCESS === 0) {
        var errorMessage = params.MESSAGE;
        window.view.addMessage(MESSAGE_ERROR, errorMessage);
    }
}

function validateSubmissionDatesError(params) {
}

function createRRTaxReturn() {
    var taxReturnKey = window.view.value.RECORDNO;
    var tokenUrl = window.view.value.TOKENURL;
    if (taxReturnKey && tokenUrl) {
        window.editor.showLoadingBar();
        var args = {taxReturnKey: taxReturnKey, tokenUrl: tokenUrl};
        for (var i = 0; i < tax_steps.length; i++) {
            if (window.view.value[tax_steps[i] + '_' + 'RECORDNO']) {
                args[tax_steps[i] + '_' + 'RECORDNO'] = window.view.value[tax_steps[i] + '_' + 'RECORDNO'];
            }
        }
        window.editor.ajax(true, 'createTaxReturn', args, createTaxReturnSuccess, createTaxReturnError);
    }
}

// method for Retrieve Files button under More actions
function createRetrieveFiles() {
    var taxReturnKey = window.view.value.RECORDNO;
    var tokenUrl = window.view.value.TOKENURL;
    if (taxReturnKey && tokenUrl) {
        window.editor.showLoadingBar();
        var args = {taxReturnKey: taxReturnKey, tokenUrl: tokenUrl, retrieveFilesCall: true};
        for (var i = 0; i < tax_steps.length; i++) {
            if (window.view.value[tax_steps[i] + '_' + 'RECORDNO']) {
                args[tax_steps[i] + '_' + 'RECORDNO'] = window.view.value[tax_steps[i] + '_' + 'RECORDNO'];
            }
        }
        window.editor.ajax(true, 'createTaxReturn', args, createRetrieveFilesSuccess, createRetrieveFilesFailure);
    }
}

// reload the screen when Get files action is done
function createRetrieveFilesSuccess(params) {
    window.editor.hideLoadingBar();
    location.reload();
}

function createRetrieveFilesFailure() {
    window.alert(GT('IA.CANNOT_RETRIEVE_TAX_SUBMISSION_FILES'));
    window.editor.hideLoadingBar();
}

function createTaxReturnSuccess(params) {
    window.editor.hidePage('confirmResumeTaxReturn', false);
    if (params === 'reload') {
        // this is the case when user clicks on Resume for tax return which has been already submitted with HMRC
        location.reload();
    } else {
        window.open(params, '_blank');
    }
    window.editor.hideLoadingBar();
}

function createTaxReturnError() {
    window.alert('Failed to redirect to government submission site. If you have not submitted the tax return already, please try again later');
    window.editor.hideLoadingBar();
    window.editor.hidePage('confirmResumeTaxReturn', false);
}

function resumeRRTaxReturn() {
    var taxReturnKey = window.view.value.RECORDNO;
    var tokenUrl = window.view.value.TOKENURL;

    if (taxReturnKey && tokenUrl) {
        window.editor.showLoadingBar();
        var args = {taxReturnKey: taxReturnKey, tokenUrl: tokenUrl};
        for (var i = 0; i < tax_steps.length; i++) {
            if (window.view.value[tax_steps[i] + '_' + 'RECORDNO']) {
                args[tax_steps[i] + '_' + 'RECORDNO'] = window.view.value[tax_steps[i] + '_' + 'RECORDNO'];
            }
        }
        window.editor.ajax(true, 'resumeTaxReturn', args, resumeTaxReturnSuccess, resumeTaxReturnError);
    }
}

function resumeTaxReturnSuccess(params) {
    window.editor.hideLoadingBar();
}

function resumeTaxReturnError() {
    window.editor.hideLoadingBar();
}

function populateRecipientsEmail(params) {
    window.editor.hideLoadingBar();
    window.editor.showPage('processTaxDataOffline', true);
    var taxSolutionId = window.view.value.TAXSOLUTIONID;
    var floatingPage = window.editor.findComponents('PROCESSTAXDATAOFFLINE', 'FloatingPage');
    var processTaxDataDatesSection =  floatingPage[0].findComponentsById('processTaxDataDates', 'Section');
    if (processTaxDataDatesSection && processTaxDataDatesSection[0]) {
        processTaxDataDatesSection[0].showHide(!isSageRRTaxSolution(taxSolutionId));
    }
    var processTaxDataEmailSection =  floatingPage[0].findComponentsById('processTaxDataEmailSubsection', 'Subsection');
    if (processTaxDataEmailSection && processTaxDataEmailSection[0]) {
        processTaxDataEmailSection[0].showHide(isSageRRTaxSolution(taxSolutionId));
    }
    if (!isSageRRTaxSolution(taxSolutionId)) {
        var dummy1 = floatingPage[0].getField('DUMMY1', 'Field');
        dummy1.setValue(GT('IA.AFTER_TAX_DATA_IS_SUCCESSFULLY_PREPARED_RETURN_CUSTOM'));
    } else {
        var recipientsEmail = window.editor.findComponents('RECIPIENTSEMAIL', 'Field')[1];
        params = params.length > 1 ? params : '';
        recipientsEmail.setValue(params);
    }
}

function populateRecipientsEmailError() {
    window.editor.hideLoadingBar();
    window.editor.showPage('processTaxDataOffline', true);
}

function AttachmentFileName(meta)
{
    this.meta = meta;
    this.view_url = meta.parentValue['SCRIPT'];
}

AttachmentFileName.inheritsFrom(InputControlAttachmentName);

AttachmentFileName.prototype.getViewURL = function()
{
    if (this.meta.parentValue['SCRIPT']) {
        return this.meta.parentValue['SCRIPT'];
    }

    return null;
};

AttachmentFileName.prototype.setViewURL = function(url)
{
    this.meta.parentValue['SCRIPT'] = url;
};

function showConfirmationPageOnResumeAction(){
    var dummy6 = window.editor.view.getField('DUMMY6', 'Field');
    var dummy7 = window.editor.view.getField('DUMMY7', 'Field');
    dummy6.setValue(GT('IA.YOU_ARE_RESUMING_YOUR_CONNECTION_TO_SAGE'));
    dummy7.setValue(GT('IA.YOUR_TAX_RETURN_WILL_BE_CREATED_IN_SAGE'));
    window.editor.showPage('confirmResumeTaxReturn', true);
}

// when we resume from 'submission_requested'stage in case of ZA electronic filing
function showConfirmationPageOnCheckStatusAction(){
    var dummy6 = window.editor.view.getField('DUMMY6', 'Field');
    var dummy7 = window.editor.view.getField('DUMMY7', 'Field');
    dummy6.setValue(GT('IA.YOU_ARE_RESUMING_YOUR_CONNECTION_TO_SAGE'));
    dummy7.setValue(GT('IA.YOUR_TAX_RETURN_WILL_BE_CREATED_IN_SAGE'));
    window.editor.showPage('confirmResumeTaxReturn', true);
}

function showConfirmationPageOnCreateAction() {
    var dummy6 = window.editor.view.getField('DUMMY6', 'Field');
    var dummy7 = window.editor.view.getField('DUMMY7', 'Field');
    dummy6.setValue(GT('IA.YOU_ARE_RESUMING_YOUR_CONNECTION_TO_SAGE'));
    dummy7.setValue(GT('IA.YOUR_TAX_RETURN_WILL_BE_CREATED_IN_SAGE'));
    dummy6.updateProperty('hidden', true, true);
    dummy7.updateProperty('hidden', false, true);
    window.editor.showPage('confirmResumeTaxReturn', true);
}

function hideShowReportingEntities()
{
    var taxID = window.view.value.TAXID;
    var args = {taxID: taxID};
    window.editor.ajax(true, 'getEntitiesByTaxId', args, showRptEntitiesSuccess, showRptEntitiesError);
}

function handleRptEntitypicker(pickObj)
{
    view.value.RPTENTITYKEY = pickObj.currentPickValues[pickObj.currentPickValues.mapped[pickObj.value]].RECORDNO;
}

function showRptEntitiesSuccess(entities) {
    var rObj = window.editor.view.findComponents('RPTENTITYID', 'Field');
    if (!rObj[0])
        return;

    if (entities != null && entities.length > 1) {
        //Our reporting entities picker start out with a full list of entities, we need to
        //restrict it to just the passed in entity records
        var inValues = new Array();
        for (var i = 0; i < entities.length; i++) {
            inValues.push(entities[i].RECORDNO);
        }
        rObj[0].type.restrict = [{'pickField': 'RECORDNO', 'value': inValues}];

        //Default the picked to select the first entity in list
        rObj[0].setValue(entities[0].ENTITYNAME);
        view.value.RPTENTITYKEY = entities[0].RECORDNO;

        rObj[0].hidden = false;
        rObj[0].required = true;
        rObj[0].redraw();
    } else if (rObj[0].hidden == false) {     //hide
        //We need to blank out the picker since there is no dupe tax id. We need to do this because the
        //biz logic relies on this behavior -- not good.
        rObj[0].setValue('');
        view.value.RPTENTITYKEY = '';

        rObj[0].hidden = true;
        rObj[0].required = false;
        rObj[0].redraw();
    }
}

function showRptEntitiesError() {

}

function isSageRRTaxSolution(solutionIDSelected) {
    return solutionIDSelected == TAXSOLUTION_CA ||
        solutionIDSelected == TAXSOLUTION_DE ||
        solutionIDSelected == TAXSOLUTION_FR ||
        solutionIDSelected == TAXSOLUTION_GB ||
        solutionIDSelected == TAXSOLUTION_ZA ||
        solutionIDSelected == TAXSOLUTION_AU;
}

function onLoad()
{
    // Logic to filter processing steps based on the selected tax solution for the Single Tax Jurisdiction on page load
    var taxSolutionId = window.view.value.TAXSOLUTIONID;
    if (taxSolutionId){
        filtertaxids(true);
    }
}

function filtertaxids(isOnLoad = false)
{
    //Our taxid picker start out with a full list of taxids, we need to
    //restrict it to just the taxids that matched the taxsolution selected
    var taxSolutionId = window.view.value.TAXSOLUTIONID;
    // Logic to filter the processing steps based on the tax solution
    var rObj = window.editor.view.findComponents('PROCESSING_STEPS', 'Grid');
    if (!this.isSageRRTaxSolution(taxSolutionId)) {
        if (rObj && rObj[0] && rObj[0].value) {
            rObj[0].originalValue = rObj[0].value;
            var newValues = [];
            for (var i = 0; i < rObj[0].value.length; i++) {
                var item = rObj[0].value[i];
                if (item.STEPID === 'prepare_tax_data' || item.STEPID === 'submit_to_government') {
                    newValues.push(item);
                }
            }
            rObj[0].value = newValues;
        }
    } else {
        if (rObj && rObj[0] && rObj[0].originalValue) {
            rObj[0].value = rObj[0].originalValue;
        }
    }

    var args = {taxSolutionId: taxSolutionId};
    var taxReturnFilesSection = window.editor.findComponentsById('taxReturnFiles', 'Section');
    taxReturnFilesSection[0].showHide(this.isSageRRTaxSolution(taxSolutionId));

    // Prevent ajax call if called from onLoad
    if (!isOnLoad) {
        window.editor.ajax(true, 'getTaxIdByTaxsolution', args, filtertaxidsSuccess, filtertaxidsError);
    }
}

function filtertaxidsSuccess(taxids) {
    //restrict it to just the taxids passed
    if (taxids && taxids.length == 0) {
        taxids = ['0']; //If 'getTaxIdByTaxsolution' returns null, then create an array of record '0' to empty the picker
    }
    var taxidfld = window.editor.view.findComponents('TAXID', 'Field')[0];
    if (taxidfld) {
        taxidfld.type.restrict = [{ 'pickField': 'PICKID', 'value': Object.values(taxids) }];
        taxidfld.setValue('');
        taxidfld.redraw();
    }
}

function openTaxRecordLister(url) {
    Launch(url, GT('IA.TAX_RECORDS'), 800, 600);
}

function filtertaxidsError() {

}

function validateForManualFiling() {
    var taxSolutionId = window.view.value.TAXSOLUTIONID;
    if (isSageRRTaxSolution(taxSolutionId)) {
        var submissionDate = window.editor.view.findComponents('SUBMISSIONDATE', 'Field')[0];
        var ok = true;
        window.view.clearMessages(MESSAGE_ERROR);
        if (isNull(submissionDate)) {
            window.view.addMessage(MESSAGE_ERROR, GT('IA.SUBMISSION_DATE_CANNOT_BE_EMPTY'));
            ok = false;
        }

        var submittersEmail = window.editor.view.findComponents('SUBMITTERSEMAIL', 'Field')[0];
        if (isNull(submittersEmail)) {
            window.view.addMessage(MESSAGE_ERROR, GT('IA.SUBMITTERS_EMAIL_ADDRESS_CANNOT_BE_EMPTY'));
            ok = false;
        }
        if (!ok) {
            return ok;
        }

    }
    window.editor.submit(true, 'markasfiled', true);
}

function isNull(obj) {

    if (typeof  obj == 'undefined') {
        return true;
    } else if (obj.value == null || obj.value == '') {
        return true;
    }
    return false;
}