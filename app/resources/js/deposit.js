/**
 * Select / unselect all deposits
 */
function selectAllToDeposit(fieldMeta)
{
    var value = fieldMeta.getValue();

    // Get the grid
    var fundsGrid = window.editor.findComponents('FUNDS', 'Grid');
    fundsGrid = fundsGrid[0];

    // Set the value of the checkbox for the visible fields ( pagination )
    // We do not want to refraw the grid since it may be to heavy on the browser
    var depositFields = window.editor.findComponents('DEPOSIT', 'Field');
    for ( var i = 0; i < depositFields.length; i++ ) {
        depositFields[i].setValue(value);
    }

    // Set the value of the checkbox for the other fields ( pagination )
    var rowCount = fundsGrid.getRowCount();
    for ( var i = 0; i < rowCount; i++ ) {
        fundsGrid.value[i]['DEPOSIT'] = value;
    }

    // Update the grid column title
    updateColumnHeader(fundsGrid, rowCount);

    // Update the totals
    updateTotal(fundsGrid);
}

/*
 * Get a list of undeposited funds
 */
function getFunds()
{
    // Make sure we have all the fields we need
    if ( !window.editor.validateData() ) { return; }

    var filters = [];
    var filterPath = [
        'CURRENCY', 'FILTER_DATE', 'FILTER_PAYEE', 'FILTER_CUSTOMERID',
        'FILTER_PAYMETHOD', 'FILTER_DOCNUMBER', 'FILTER_BATCHTITLE', 'FILTER_TRXTOTALENTERED'
    ];

    for ( var i = 0; i < filterPath.length ; i++ ) {
        var field = window.editor.view.getField(filterPath[i]);
        if ( field ) {
            var value = field.getValue();
            if ( value && filterPath[i] == 'FILTER_CUSTOMERID' ) {
                value = value.split('--')[0];
            }
            filters[filterPath[i]] = value;
        }
    }

    // Show the loading panel in case it takes some time
    window.editor.showLoadingBar();

    // Make the ajax call
    window.editor.ajax(false, 'getFunds', filters, c_getFunds);
}

/*
 * This is the callback function from getFunds
 */
function c_getFunds(result)
{
    // Reset the readtime to we dont get a concurrency error
    var readtime = result['readtime'];
    window.editor.setGlobalFieldValue('.readtime', readtime);

    // Refresh the grid
    var funds = result['funds'];
    transactionFilterCallback('FUNDS', funds);

    // Update the totals
    updateTotal();
}

/*
 * Default the bank currency in the filter for MCP screens
 */
function defaultCurrency(fieldMeta)
{
    if ( !fieldMeta ) {
        return;
    }
    var bank = fieldMeta.getValue();
    if ( !bank ) {
        return;
    }

    // Get the picker values
    var pickfields = fieldMeta.findPickerObject(bank);
    if( !pickfields ) {
        return;
    }

    var currencyField = window.editor.view.getField('CURRENCY');
    if ( currencyField ) {

        // Set the currency value
        currencyField.setValue(pickfields['CURRENCY']);

        // If the bank is a foreign currency bank we only allow the undeposited funds to be in the bank's currency
        var basecurr = window.editor.view.value['BASECURR'];
        if ( pickfields['CURRENCY'] && pickfields['CURRENCY'] != basecurr ) {
            currencyField.updateProperty('readonly', true);
        } else {
            currencyField.updateProperty('readonly', false);
        }

        // Redraw the field
        currencyField.parentComponent.redraw();
    }
}

/**
 * Override the deposit grid to handle the total on line selection
 */
function DepositGrid()
{
}

DepositGrid.inheritsFrom( Grid );

DepositGrid.prototype.acceptCellForColumnTotal = function(cellValue, cellContext, rowValues, rowIndex)
{
    if ( this.readonly ) {
        return Grid.prototype.acceptCellForColumnTotal.call(this, cellValue, cellContext, rowValues, rowIndex);
    } else {
        return ( cellValue && rowValues['DEPOSIT'] === "true" ) ? true: false;
    }
};

/*
 * Update the grid total based on the selected lines
 */
function updateTotal(grid)
{
    // get the grid
    if ( !grid ) {
        grid = window.editor.findComponents('FUNDS', 'Grid');
        grid = grid[0];
    }

    var selected = 0;
    for ( var i = 0; i < grid.getRowCount(); i++ ) {
        if ( grid.value[i]['DEPOSIT'] === "true" ) {
            selected++;
        }
    }

    // Update the column header
    updateColumnHeader(grid, selected);

    // Update the grid total
    grid.computeTotals();
    grid.refreshTotals();
}

/*
 * Update the pay full column header title
 */
function updateColumnHeader(grid, count)
{
    var depositColumnField = grid.findColumnField('DEPOSIT');
    var depositColumn = depositColumnField.getGridColumn();
    var index = depositColumn.getIndex();
    if ( count > 0 ) {
        depositColumn.updateLabel(index, index, GT('IA.DEPOSIT') + ' (' + count + ')');
    } else {
        depositColumn.updateLabel(index, index, GT('IA.DEPOSIT'));
    }
}
