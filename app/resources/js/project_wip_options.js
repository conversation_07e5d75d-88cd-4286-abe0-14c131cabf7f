var projectWipOptions = {
  DELIMITER: '#~#',
  rollUpOption: '',
  resolver: null,
  load: function() {
    var rollupField = window.editor.view.getField('WIP_ROLL_UP_OPTIONS');
    projectWipOptions.rollUpOption = rollupField.getValue();
    if (rollupField.getValue() === 'TARGET_PROJECT') {
      projectWipOptions.getRollupProjects();
    }
    projectWipOptions.hideWipTargetProject();
  },
  changedIncludeWIP: function (data) {
    var wipRollUpOptions = window.editor.findComponents('WIP_ROLL_UP_OPTIONS', 'Field')[0];
    function postChangeAction(data) {
      var hidden = data.value === 'false';
      wipRollUpOptions.updateProperty('hidden', hidden);
      wipRollUpOptions.redraw();
      if (hidden) {
        projectWipOptions.hideWipTargetProject(hidden);
      } else {
        projectWipOptions.changedRollUpOption({value: wipRollUpOptions.getValue()});
      }
    }
    if (data.value === 'false' && wipRollUpOptions.getValue() === 'TARGET_PROJECT') {
      projectWipOptions.openManageWipSubProjectsPopup().then(function(done) {
        if (done === true) {
          postChangeAction(data);
        } else {
          // Check the WIP include checkbox since the user has canceled out from the prompt.
          var wipInclude = window.editor.view.getField('WIP_INCLUDE');
          wipInclude.setValue('true');
          wipInclude.redraw();
          postChangeAction({value: 'true'});
        }
      });
    } else {
      postChangeAction(data);
    }
  },
  hideWipTargetProject: function () {
    var wipInclude = window.editor.view.getField('WIP_INCLUDE');
    var wipRollupOptions = window.editor.view.getField('WIP_ROLL_UP_OPTIONS');
    var hidden = wipInclude.getValue() === 'false' || wipRollupOptions.getValue() !== 'ROLL_UP_TO_PROJECT';
    var wipTargetProject = window.editor.findComponents('WIP_TARGET_PROJECT', 'Field')[0];
    if (wipTargetProject.readonly !== true) {
      wipTargetProject.updateProperty('required', !hidden, true);
    }
    jq('#_obj__WIP_TARGET_PROJECT').parent().css('display', hidden?'none':'list-item');
  },
  changedRollUpOption: function (data) {
    projectWipOptions.hideWipTargetProject();
    projectWipOptions.getRollupProjects();

    // Update the text that has the number of projects rolled up to this project.
    if (data.value === 'TARGET_PROJECT') {
      projectWipOptions.rollUpOption = data.value;
    } else {
      if (projectWipOptions.rollUpOption === 'TARGET_PROJECT') {
        projectWipOptions.openManageWipSubProjectsPopup().then(function(done) {
          if (done === true) {
            projectWipOptions.rollUpOption = data.value;
          } else {
            var wipRollUpOptions = window.editor.view.getField('WIP_ROLL_UP_OPTIONS');
            wipRollUpOptions.setValue(projectWipOptions.rollUpOption);
            wipRollUpOptions.redraw();
          }
          projectWipOptions.getRollupProjects();
          projectWipOptions.hideWipTargetProject();
        });
      } else {
        projectWipOptions.rollUpOption = data.value;
      }
    }
  },
  getRollupProjects: function() {
    var rollupProjectsField = window.editor.view.getField('WIP_ROLLUP_PROJECTS');
    var projectsString = rollupProjectsField.getValue();
    var projects = !!projectsString ? projectsString.split(projectWipOptions.DELIMITER) : [];
    projectWipOptions.setRollupProjectLink(projects);
  },
  setRollupProjectLink: function(projects) {
    var anchor = jq('#wipRollupProjectLink');
    if (!anchor || anchor.length === 0) {
      var element = jq('#_obj__WIP_ROLL_UP_OPTIONS0');
      if (element.length === 0) {
        element = jq('#_obj__WIP_ROLL_UP_OPTIONS');
      }
      anchor = jq('<a id="wipRollupProjectLink" href="javascript:void(0)"></a>');
      (jq('<div></div>').append(anchor)).appendTo(element.parent());
    }
    var token = {
      id: 'IA.VIEW_SUBPROJECTS',
      placeHolders: []
    };
    var text = GT(token);
    anchor.text(text);
    if (projects.length === 0) {
      anchor.remove();
    }
  },
  changedTargetProject: function (data) {
    if (data.value !== '') {
      var args = {
        "WIP_TARGET_PROJECT": data.value
      };
      window.editor.showLoadingBar();
      window.editor.ajax(false, 'isTargetWipProject', args, projectWipOptions.confirmTargetProject);
    }
  },
  confirmTargetProject: function (data) {
    if (data.isWipProject === false) {
      window.editor.showPage('ConvertToWipProjectPopup', false);
      var token = {
        id: 'IA.CONVERT_INTO_WIP_PROJECT',
        placeHolders: [
          {name: "SELECTED_PROJECT", value: data.targetProject }
        ]
      };
      var text = GT(token);
      var confirmCheckbox = window.editor.view.getField('CONVERT_WIP_PROJECT');
      confirmCheckbox.updateProperty('fullname', text);
      if (confirmCheckbox.getValue() !== 'false') {
        confirmCheckbox.setValue('false');
      }
      confirmCheckbox.redraw();
    }
    window.editor.hideLoadingBar();
  },
  handleConvertWipProjectChange: function (data) {
    jq('#CONTINUE_CONVERT_WIP_BUTTON').prop('disabled', data.value !== 'true');
  },
  closeConvertToWipProjectPopup: function (ok) {
    if (ok === false) {
      var wipTargetProject = window.editor.findComponents('WIP_TARGET_PROJECT', 'Field')[0];
      wipTargetProject.updateProperty('value', '');
      wipTargetProject.redraw();
    }
    window.editor.hidePage('ConvertToWipProjectPopup', false, false);
  },
  getProject: function() {
    var projectId = window.editor.view.getField('PROJECTID').getValue();
    var projectName = window.editor.view.getField('NAME').getValue();
    var label = '';
    if (projectId) {
      label = projectId;
    }
    if (projectName) {
      label += '--' + projectName;
    }
    return label;
  },
  openRollupProjectSelection: function() {
    window.editor.showLoadingBar();
    var projectKey = window.editor.view.value.RECORDNO;
    window.editor.ajax(false,
      'getProjectsForWipRollup',
      {
        RECORDNO: projectKey
      },
      projectWipOptions.receivedProjectsForWipRollup);
  },
  receivedProjectsForWipRollup: function(data) {
    var projectLabel = projectWipOptions.getProject();
    var token = {
      id: 'IA.ROLLUP_PROJECT_TITLE',
      placeHolders: [
        {name: "PROJECT", value: projectLabel }
      ]
    };
    var text = GT(token);

    window.editor.showPage('RollupProjectSelectionPopup', false);
    var popup = window.editor.view.getField('ROLLUPPROJECTSELECTIONPOPUP');
    popup.title = text;
    jq('#qxmodaltitleid').text(text);

    var wipRollupProjectsField = window.editor.view.getField('WIP_ROLLUP_PROJECTS');
    var projectsString = wipRollupProjectsField.getValue();
    var rollupProjectSelectionField = window.editor.view.getField('ROLLUPPROJECTSELECTION');
    rollupProjectSelectionField.type.validlabels = data.labels;
    rollupProjectSelectionField.setValue(projectsString);
    var rollupField = window.editor.view.getField('WIP_ROLL_UP_OPTIONS');
    if (rollupField.readonly === true) {
      rollupProjectSelectionField.updateProperty('readonly', true);
      rollupProjectSelectionField.type.validvalues = data.labels;
    } else {
      rollupProjectSelectionField.type.validvalues = data.keys;
    }
    rollupProjectSelectionField.redraw();
    jq('#ROLLUPPROJECTSELECTION_CONTINUE').prop('disabled', rollupField.readonly);
    window.editor.hideLoadingBar();
  },
  closeRollupProjectSelection: function(done) {
    if (done) {
      var projectsString = '';
      var rollupProjectSelectionField = window.editor.view.getField('ROLLUPPROJECTSELECTION');
      if (rollupProjectSelectionField) {
        projectsString = rollupProjectSelectionField.getValue();
      }
      var wipRollupProjectsField = window.editor.view.getField('WIP_ROLLUP_PROJECTS');
      wipRollupProjectsField.setValue(projectsString);
      var projects = !!projectsString ? projectsString.split(projectWipOptions.DELIMITER) : [];
      projectWipOptions.setRollupProjectLink(projects);
    }
    window.editor.hidePage('RollupProjectSelectionPopup', false, false);
  },
  changeParent: function(data) {
    var wipInclude = window.editor.view.getField('WIP_INCLUDE');
    if (wipInclude
      && (wipInclude.readonly === undefined || wipInclude.readonly === false)
      && (wipInclude.hidden === undefined || wipInclude.hidden === false)) {
      if (data.value) {
        var index = data.value.indexOf('--');
        if (index > 0) {
          var args = {
            "PARENTID": data.value.substring(0, index)
          };
          window.editor.showLoadingBar();
          window.editor.ajax(false, 'getTargetWipProjects', args, projectWipOptions.receivedTargetWipProjects);
        }
      } else {
        projectWipOptions.modifyRollUpOptions(null);
      }
    }
  },
  receivedTargetWipProjects: function(data) {
    var targetWipProjects = window.editor.view.getField('WIP_TARGET_PROJECT');
    targetWipProjects.type.validlabels.length = 0;
    targetWipProjects.type.validvalues.length = 0;
    targetWipProjects.type.validlabels = data.labels;
    targetWipProjects.type.validvalues = data.keys;
    var selectedTarget = targetWipProjects.getValue();
    if (!data.labels.includes(selectedTarget) && !data.keys.includes(selectedTarget)) {
      targetWipProjects.setValue('');
    }
    targetWipProjects.redraw();
    projectWipOptions.modifyRollUpOptions(data);
    window.editor.hideLoadingBar();
  },
  modifyRollUpOptions: function(data) {
    var rollupField = window.editor.view.getField('WIP_ROLL_UP_OPTIONS');
    var selectedOption = rollupField.getValue();
    rollupField.type.validlabels.length = 0;
    rollupField.type.validvalues.length = 0;
    if (data) {
      if (data.keys && data.keys.length > 0) {
        rollupField.type.validlabels = [GT('IA.DISPLAY_IN_WIP_PROJECT_GRID'), GT('IA.ROLL_UP_TO_ROOT'), GT('IA.ROLL_UP_TO_PROJECT')];
        rollupField.type.validvalues = ['TARGET_PROJECT', 'ROLL_UP_TO_ROOT', 'ROLL_UP_TO_PROJECT'];
      } else {
        rollupField.type.validlabels = [GT('IA.DISPLAY_IN_WIP_PROJECT_GRID'), GT('IA.ROLL_UP_TO_ROOT')];
        rollupField.type.validvalues = ['TARGET_PROJECT', 'ROLL_UP_TO_ROOT'];
        if (!rollupField.type.validvalues.includes(selectedOption)) {
          selectedOption = 'ROLL_UP_TO_ROOT';
        }
      }
    } else {
      rollupField.type.validlabels = [GT('IA.DISPLAY_IN_WIP_PROJECT_GRID')];
      rollupField.type.validvalues = ['TARGET_PROJECT'];
      selectedOption = 'TARGET_PROJECT';
    }
    rollupField.setValue(selectedOption);
    rollupField.redraw();
    projectWipOptions.changedRollUpOption({value: selectedOption});
    jq("input[name=_obj__WIP_ROLL_UP_OPTIONS]").val([selectedOption]);  // Fix a bug with the radio button not checking the correct option.
  },
  openManageWipSubProjectsPopup: function() {
    var promise = new Promise(function(resolve) {
      var projectKey = window.editor.view.value.RECORDNO;
      if (projectKey) {
        window.editor.showLoadingBar();
        projectWipOptions.resolver = resolve;
        window.editor.ajax(false,
          'getManageWipSubprojects',
          { RECORDNO: projectKey },
          projectWipOptions.receivedManagedSubProjects);
      } else {
        resolve(true);
      }
    });
    return promise;
  },
  setWipManageSubprojects: function(results) {
    var keys = [];
    for (var i=0; i<results.length; i++) {
      keys.push(results[i]['RECORDNO']);
    }
    var keysString = keys.join(projectWipOptions.DELIMITER);
    var wipManageSubprojectsField = window.editor.view.getField('WIP_MANAGE_SUBPROJECTS');
    wipManageSubprojectsField.setValue(keysString);
  },
  receivedManagedSubProjects: function(response) {
    window.editor.hideLoadingBar();
    if (response && response['subprojects'] && response['subprojects'].length > 0) {
      window.editor.showPage('ManageWipSubProjectsPopup', false);
      var token = {
        "id": "IA.MANAGE_WIP_SUBPROJECTS_TEXT",
        "placeHolders": [
          {
            "name": "NUMBER_ROLLUP_PROJECTS",
            "value": response['subprojects'].length + ' ' + (response['subprojects'].length===1?'project':'projects')
          }
        ]
      };
      var instructions = GT(token);
      var textField = window.editor.view.getField('MANAGE_WIP_SUBPROJECTS_TEXT');
      textField.setValue(instructions);
      projectWipOptions.setWipManageSubprojects(response['subprojects']);
      var rollupProjectSelection = window.editor.view.getField('MANAGEWIPROLLUPPPROJECTSELECTION');
      var manageSelection = window.editor.view.getField('MANAGEWIPSUBPROJECTSELECTION');
      if (response['targets']['keys'].length > 0) {
        manageSelection.type.validlabels = [
          'IA.SELECT_ONE',
          'IA.EXCLUDE_PROJECTS_FROM_WIP_SCHEDULE',
          'IA.CONVERT_PROJECTS_TO_WIP_PROJECTS',
          'IA.ROLL_UP_PROJECTS_TO_ANOTHER_PROJECT'
        ];
        manageSelection.type.validvalues = [
          '',
          'EXCLUDE_WIP',
          'CONVERT_TO_WIP_PROJECT',
          'ROLL_UP_TO_ANOTHER_PROJECT'
        ];
        rollupProjectSelection.type.validlabels = response['targets']['labels'];
        rollupProjectSelection.type.validvalues = response['targets']['keys'];
      } else {
        manageSelection.type.validlabels = [
          'IA.SELECT_ONE',
          'IA.EXCLUDE_PROJECTS_FROM_WIP_SCHEDULE',
          'IA.CONVERT_PROJECTS_TO_WIP_PROJECTS'
        ];
        manageSelection.type.validvalues = [
          '',
          'EXCLUDE_WIP',
          'CONVERT_TO_WIP_PROJECT'
        ];
      }
      manageSelection.setValue('');
      manageSelection.redraw();
      if (response['targets']['keys'].length > 0 || !rollupProjectSelection.hidden) {
        rollupProjectSelection.setValue('');
        rollupProjectSelection.updateProperty('hidden', true);
        rollupProjectSelection.redraw();
      }
    }
    jq('#MANAGEWIPSUBPROJECTSELECTION_CONTINUE').prop('disabled', true);
  },
  changedManageWipSubprojectOption: function(data) {
    var rollupProjectSelection = window.editor.view.getField('MANAGEWIPROLLUPPPROJECTSELECTION');
    var hidden = data.value !== 'ROLL_UP_TO_ANOTHER_PROJECT';
    if (hidden) {
      jq('#MANAGEWIPSUBPROJECTSELECTION_CONTINUE').prop('disabled', data.value === '');
    } else {
      jq('#MANAGEWIPSUBPROJECTSELECTION_CONTINUE').prop('disabled', !rollupProjectSelection.getValue());
    }
    if (rollupProjectSelection && rollupProjectSelection.hidden !== hidden) {
      rollupProjectSelection.updateProperty('hidden', hidden);
      rollupProjectSelection.redraw();
    }
  },
  changedManageWipRollUpProjectOption: function(data) {
    jq('#MANAGEWIPSUBPROJECTSELECTION_CONTINUE').prop('disabled', data.value === '');
  },
  closeManageWipSubProjectsPopup: function(done) {
    var wipManageSuprojectOption = window.editor.view.getField('WIP_MANAGE_SUBPROJECT_OPTION');
    var wipManageRollUpToProject = window.editor.view.getField('WIP_MANAGE_ROLL_UP_TO_PROJECT');
    if (done) {
      var manageWipSubprojectSelection = window.editor.view.getField('MANAGEWIPSUBPROJECTSELECTION');
      wipManageSuprojectOption.setValue(manageWipSubprojectSelection.getValue());
      var manageWipRollUpProjectSelection = window.editor.view.getField('MANAGEWIPROLLUPPPROJECTSELECTION');
      if (wipManageSuprojectOption.getValue() === 'ROLL_UP_TO_ANOTHER_PROJECT') {
        wipManageRollUpToProject.setValue(manageWipRollUpProjectSelection.getValue());
      } else {
        wipManageRollUpToProject.setValue('');
      }
    } else {
      wipManageSuprojectOption.setValue('');
      wipManageRollUpToProject.setValue('');
    }
    window.editor.hidePage('ManageWipSubProjectsPopup', false, false);
    projectWipOptions.resolver(done);
  }
};

jq(document).ready(function() {
    jq('#page_1_form').on('click', '#wipRollupProjectLink', function() {
        projectWipOptions.openRollupProjectSelection();
    });
});