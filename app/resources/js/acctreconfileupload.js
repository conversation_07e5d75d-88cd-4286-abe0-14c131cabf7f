function addFileUploadEventListener()
{
   //noinspection JSUnresolvedFunction JSUnresolvedVariable
   jq('#_obj__BANKFILEUPLOADPAGE-_obj__BANKTXNFILE').fileupload({
       url: CustomizeURL('editor.phtml', '.op=1324', '.action=ajax','.ajaxcmd=upload', '.sess=' + _sess, '.mod=cm',
                         '.finacct=' +getID(getFieldValue('FINANCIALENTITY'))),
       dataType:"json",
       formData: function() {
           return [
               { name: '.xsrf_token', value: window.editor.getGlobalFieldValue('.xsrf_token') },
               { name: '.form_editor_unique_id', value: window.editor.getGlobalFieldValue('.form_editor_unique_id') }
           ];
       },
      submit: function(e, data) {
            window.editor.setFileUploadingFlag(true);
            window.editor.showLoadingBar();
            var grid = getGrid('BANKTXNFILES');
            if (grid && data) {
                // Validate the uploaded file
                if ( validateFileUpload(data.files[0].name, true) ) {
                    // Replace row using same file name.
                    var lineno = grid_findFieldByValue(grid, 'FEEDTYPE', data.files[0].name);
                    if ( lineno == - 1 ) {
                        // Not found, find an empty row.
                        lineno = grid_findFieldByValue(grid, 'FEEDTYPE', null);
                    }
                    if ( lineno == - 1 ) {
                        // No empty row, add one.
                        lineno = grid.getRowCount();
                        grid.addRowData(lineno, { columnName: 'FILENAME' });
                    }
                    grid.value[lineno]['FILENAME'] = data.files[0].name;
                    //grid.value[lineno]['FEEDDATE'] = byteSize(data.files[0].size);
                    grid.value[lineno]['PROGRESS'] = 'Initializing';
                    grid.redraw();
                } else {
                    window.editor.hideLoadingBar();
                    return false;
                }
            }
       },

       send: function(e, data) {
           // var fields = window.editor.findComponents('DOCUMENTNAME', 'Field');
           // if (fields && !fields[0].value) {
           //     var name = data.files[0].name;
           //     fields[0].setValue(name.replace(/\.[^/.]+$/, ""));
           // }
           //updateProgress(data, 'Waiting');
       },

       progress: function (e, data) {
           //var progress = parseInt(data.loaded / data.total * 100, 10);
           //updateProgress(data, progress + '%');
       },

       done: function (e, data) {
           // If it is not valid, simply return
           if ( !validateFileUpload(data.files[0].name, false) ) {
               return;
           }
           updateProgress(data, '100%');

           window.editor.setChangedFlag(true);
           window.editor.setFileUploadingFlag(false);

           var grid = getGrid('BANKTXNFILES');
           if (grid && data) {
               //noinspection JSUnresolvedVariable
               var result = data.result;
               for (var i = 0; i < keyCount(result); i++) {
                   var obj = result[i];
                   var lineno = grid_findFieldByValue(grid, 'FILENAME', obj.FILENAME);
                   if (lineno != -1) {
                       var component = grid.findLineComponent('FEEDDATE', lineno, 'Field');
                       if (component) {
                           component.setValue(obj.FEEDDATE);
                           //component.renderer.setViewURL(obj.FEEDDATE);
                       }
                       // Set the record number
                       var recordNo = grid.findLineComponent('RECORDNO', lineno, 'Field');
                       if (recordNo) {
                           recordNo.setValue(obj.RECORDNO);
                       }
                       // Set the state
                       var state = grid.findLineComponent('STATE', lineno, 'Field');
                       if (state) {
                           state.setValue(obj.STATE);
                       }
                       var stateDesc = grid.findLineComponent('STATEDESC', lineno, 'Field');
                       if (stateDesc) {
                           stateDesc.setValue(obj.STATEDESC);
                       }
                       // Uploaded by and on
                       var upload = grid.findLineComponent('UPLOADEDBY', lineno, 'Field');
                       if (upload) {
                           upload.setValue(obj.UPLOADEDBY);
                       }
                       var uploadOn = grid.findLineComponent('FEEDDATE', lineno, 'Field');
                       if (uploadOn) {
                           uploadOn.setValue(obj.FEEDDATE);
                       }
                       var feedType = grid.findLineComponent('FEEDTYPE', lineno, 'Field');
                       if (feedType) {
                           feedType.setValue(obj.FEEDTYPE);
                       }
                       // Set the progress
                       // var progress = grid.findLineComponent('PROGRESS', lineno, 'Field');
                       // if (progress) {
                       //     progress.setValue('Completed');
                       // }
                   }
               }
               // Set the file upload data
               fileUploads = grid.value;
           }
           window.editor.hideLoadingBar();
       },

       fail: function (e, data) {
           updateProgress(data, 'FAILED');
           //alert('Upload failed');
       }
   });
}

function updateProgress(data, text)
{
    var grid = getGrid('BANKTXNFILES');
    if (grid) {
        var lineno = grid_findFieldByValue(grid, 'FILENAME', data.files[0].name);
        if (lineno != -1) {
            var component = grid.findLineComponent('PROGRESS', lineno, 'Field');
            if (component) {
                // var elem = jq(component.getRenderer().getElement()).next();
                // if (component.getRenderer().isPercent(text)) {
                //     elem.width(text);
                // } else {
                //     elem.width("0%");
                // }
                component.setValue(text);
            }
        }
    }
}

function keyCount (o) {
    if(typeof o == "object") {
        var i, count = 0;
        for(i in o) {
            if(o.hasOwnProperty(i)) {
                count++;
            }
        }
        return count;
    } else {
        return false;
    }
}

function grid_findFieldByValue(grid, columnName, value)
{
    var found = -1;
    if (grid.value && grid.value.length > 0) {
        for (var i = 0; i < grid.value.length; i++) {
            if (!value) {
                if (!grid.value[i][columnName]) {
                    found = i;
                    break;
                }
            } else if (0 == value.localeCompare(grid.value[i][columnName])) {
                found = i;
                break;
            }
        }
    }
    return found;
}

function FileUploadGrid()
{
}

FileUploadGrid.inheritsFrom(Grid);

FileUploadGrid.prototype.forceNoPlusButton = function()
{
    return true;
};

FileUploadGrid.prototype.forceMinusButton = function()
{
    return true;
};

FileUploadGrid.prototype.deleteRow = function(rowIndex)
{
    if(confirm(GT('IA.CONFIRM_DELETE_UPLOADED_FILE'))) {

        //window.editor.showLoadingBar();
        var recordNo = null;
        var grid = getGrid('BANKTXNFILES');
        if (grid) {
            var component = grid.findLineComponent('RECORDNO', rowIndex, 'Field');
            if (component) {
                recordNo = component.value;
            }
        }

        this.gatherData();
        this.deleteRowData(rowIndex, true);
        this.redraw();

        if (recordNo) {
            var errorCallback = function() {
                window.editor.hideLoadingBar();
            };
            window.editor.ajax(false, 'delete', {recordNo: recordNo}, null, errorCallback);
        }
    }
};

function BankFileName(meta)
{
    this.meta = meta;
    this.view_url = meta.parentValue['SCRIPT'];
}

BankFileName.inheritsFrom(InputControlAttachmentName);

BankFileName.prototype.getViewURL = function()
{
    if (this.meta.parentValue['SCRIPT']) {
        return this.meta.parentValue['SCRIPT'];
    }

    return null;
};

BankFileName.prototype.setViewURL = function(url)
{
    this.meta.parentValue['SCRIPT'] = url;
};

/**
 * Validates the uploaded file name.
 *
 * @param fileName
 * @param checkDuplicate
 * @returns {boolean}
 */
function validateFileUpload(newFileName, checkDuplicate) {
    if (newFileName) {
        var currentFeedType = getFeedType();
        var csvReg = /\.csv$/i;
        var qifReg = /\.qif$/i;
        // Validate duplicate file names
        if (checkDuplicate) {
            var grid = getGrid('BANKTXNFILES');
            var lineno = grid_findFieldByValue(grid, 'FILENAME', newFileName);
            if ( lineno != - 1 ) {
                alert(GT({
                    "id": "IA.RE_IMPORT_FILE_VALIDATION",
                    "placeHolders": [{
                        "name": "NEW_FILE_NAME", "value": newFileName
                    }]
                }));
                return false;
            }
        }

        // Only allow to upload csv file, otherwise return error
        if ( ! csvReg.test(newFileName) && ! qifReg.test(newFileName) ) {
            alert(GT("IA.CSV_QIF_FILE_VALIDATION"));
            return false;
        }
        // If the current feed type do not match with uploaded file then error
        else if(currentFeedType == 'xml' || (csvReg.test(newFileName)  && currentFeedType == 'qif') || (qifReg.test(newFileName)  && currentFeedType == 'csv')) {
            alert(GT({
                "id": "IA.DIFF_FEED_FILE_VALIDATION",
                "placeHolders": [
                    { "name": "NEW_FILE_NAME", "value": newFileName },
                    { "name": "CURRENT_FEED_TYPE", "value": currentFeedType },
                ]
            }));
            return false;
        }
    }
    return true;
}

/**
 * Validates the uploaded file name.
 *
 * @param fileName
 * @returns {boolean}
 */
function validateUploadedFile(fieldMeta) {
    if (fieldMeta) {
        var files = fieldMeta.getValue();
        var feedType = getFeedType();
        var csvReg = /\.csv$/i;
        var qifReg = /\.qif$/i;
        if (files) {
            for (var i = 0; i < files.length; i++) {
                var fileName = files[i]['FILENAME'];
                // Check for the file name, uploaded file name should be always csv/qif if not throw error
                if (fileName && (!csvReg.test(fileName) && !qifReg.test(fileName))) {
                    alert(GT("IA.CSV_QIF_FILE_VALIDATION"));
                    return false;
                }
                // Check for
                if (feedType != 'csv' || feedType != 'xml' || feedType != 'onl' || feedType != 'qif') {
                    alert(GT("IA.CSV_QIF_FILE_VALIDATION"));
                    return false;
                }
                else if ( feedType == 'csv' && !csvReg.test(files[i]['FEEDTYPE'] )) {
                    alert(GT("IA.CSV_FILE_VALIDATION"));
                    return false;
                }
                else if ( feedType == 'qif' && !qifReg.test(files[i]['FEEDTYPE'] )) {
                    alert(GT("IA.QIF_FILE_VALIDATION"));
                    return false;
                }
                else if ( feedType == 'xml' && feedType != files[i]['FEEDTYPE']) {
                    alert(GT("IA.XML_TO_OTHER_FEED_VALIDATION"));
                    return false;
                }

            }
        }
    }
    return true;
}
