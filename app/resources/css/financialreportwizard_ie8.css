.wizardglassbackground {
    background: black; /*IE 8 neede this here?*/
 }
 
.EditReportWizard #wizardRowTable>tbody>tr>td>input[type='checkbox']  {
    visibility: visible;
}

.EditReportWizard SPAN.input_CB {
    border: 1px solid #b0c6e0;
}

.EditReportWizard div.iedropdown {
    display: inline-block; 
    
    width: 14px; 
    height: 10px; 
    overflow: hidden;
 }
 
 .EditReportWizard div.iedropdown div.iedropdownimg {
   margin-top: -6px; 
   margin-left: -4px; 
   width: 25px;
   height: 25px;
   background-image:url('../theme2011/blue/images/more_apps_triangle_rollover.png')
 }
 
.EditReportWizard  .fwcolumntable  .unselectText {
    display: none;
}

.EditReportWizard #wizardColumnTable_wrapper .DTFC_LeftWrapper, 
.EditReportWizard #wizardColumnTable_wrapper .DTFC_LeftHeadWrapper,
.EditReportWizard #wizardColumnTable_wrapper .DTFC_LeftBodyWrapper {
    display: none;
}

.EditReportWizard #divHeaderScroller P .colTitle {
    font-weight: 700;
}

.EditReportWizard .fwcolumntable TBODY>TR>TD {
    border-bottom-color: #d9e2de;
    border-right: 1px solid #d9e2de;
    border-left-color: transparent;
    padding-right: 0px;
}
.EditReportWizard #divHeaderScroller>div,
.EditReportWizard .fwcolumntable>TBODY>TR>TD.colsel,
.EditReportWizard #divHeaderScroller>div.colsel {
    padding-right: 0px;
}

.EditReportWizard #wizardColumnTable_wrapper .help_field_modal_container {
    display: inline-block;
    width: 25px;
}

.EditReportWizard .fwcolumntable .cellLabel {
    width: 110px;
}

.EditReportWizard .dimensiondetail {
    width: 95px;
}

.EditReportWizard .expandbyaccountgroup {
    width: 200px;
}

.EditReportWizard  .reportHeaderSeperator {
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#D3D3D3', endColorstr='#FFFFFF'); /* for IE 8: only two color */
}


/*
.EditReportWizard  .fwcolumntable  TD.fixedCol,
.EditReportWizard  .fwcolumntable  TH.fixedCol {
    display: none;
}
.EditReportWizard  .fwcolumntable  TD.fixedCol B,
.EditReportWizard  .fwcolumntable  TH.fixedCol B {
    display: none;
 }
 */



.EditReportWizard div#AccountComponentOnAllColumnsDialog div.acctgrpcol {
    width: 450px;
}

.EditReportWizard div#AccountComponentOnAllColumnsDialog div.acctgrpcol span.componentcombo_specialdimensionallrows {
    padding-top: 0px;
}

.EditReportWizard div#AccountComponentOnAllColumnsDialog div.acctgrpcol span.componentcombo_specialdimensionallrows * {
    white-space: nowrap;
}

.EditReportWizard  div#placeholder_rptowner UL.combo-box{
    width: 220px;
}

.EditReportWizard  #AccountComponentOnAllColumnsDialog_allrows div.wizardModal   DIV.acctgrpcol,
.EditReportWizard  #AccountComponentOnAllColumnsDialog_onerow div.wizardModal   DIV.acctgrpcol{
    width: 420px;
}

.EditReportWizard .wizardModal .dialogBodyText ul.combo-box>DIV {
    padding: 0px;
}

.EditReportWizard .wizardModal .componentcombo>table {
    margin-right: 12px;
}



 
