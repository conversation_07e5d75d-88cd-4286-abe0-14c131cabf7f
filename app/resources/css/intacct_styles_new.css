/*
 *	FILE:		 intacct_styles.css
 *	AUTHOR:		 <PERSON>
 *	DESCRIPTION: This file is the standard CSS for the intacct
 *				 accounting interface. It works :)
 *
 *	(C)2000, Intacct Corporation, All Rights Reserved
 *
 *	This document contains trade secret data that belongs to Intacct
 *	corporation and is protected by the copyright laws. Information 
 *	herein may not be used, copied or disclosed in whole or part
 *	without prior written consent from Intacct Corporation.
 */


/* Universal selector - clear all defaults first, to fix cross browser issues */
/* * { margin:0px; } */

/*
BODY
{color: #000000;
font-family: Verdana, Arial, sans-serif;
font-size: 10px;
font-style: normal;
font-variant: normal;
font-weight: normal;
text-decoration: none;
text-transform: none;
}

FORM
{
	behavior: none;
}
*/
form.IndentedBody {
padding-left: 15px;
padding-right: 15px;
}

h3,
p h3{
font-size:14px;
}

/*  Checkbox class */
.checkBox {
	white-space:	nowrap;			/* Don't wrap checkbox titles. */
	margin:			5px;			/* Enough space on sides and top */
}

div.checkBox input {
	position:		absolute;		/* Only way to get it relatively centered. */
	height:			20px;			/* <PERSON><PERSON> must match margin indent in span below. */
	margin-top:		-2px;			/* Move checkbox up just a bit */
}

div.checkBox input[type=radio] {
	height:			13px;
}
.quirks_5 div.checkBox input[type=radio] {
    height:         20px;
 }

div.checkBox input.standAloneCheckBox {
	position:		relative;
	height:			13px;
	border:			none; 
}

.quirks_5 div.checkBox input.standAloneCheckBox {
    height:         20px;
 }

div.checkBox span {
	margin-left:	20px;			/* Indent past checkbox - must match height in checkbox above. */
}

.quirks_5 div.checkBox span {
    padding-left: 3px;
}

.quirks_5  SELECT {
    height: 20px;
}

.quirks_5  SELECT.notdropdown {
    height: auto;
}

.quirks_5  div.combo-control SELECT,
.quirks_5  SELECT.comboBox_select {
    height: auto;
}



/*  Base icons include left/right/up/down arrows, end of list arrow, etc */
.baseIcon, a.baseIcon {
text-decoration:none;
font-size: 12px; 
letter-spacing:-2px; 
line-height:1;
color:#000000;
margin-left: 4px;
margin-right: 4px;
}

/*  Sort icons are used in MultiPickControls, etc for sorting/deleting/etc items */
.sortIcon {
	margin: 2px;
}
	
/*
A
{color: #003366;
font-family: Verdana, Arial, sans-serif;
font-size: 10px;
font-style: normal;
font-variant: normal;
text-decoration: underline;
behavior: none;}

A:hover
{color: #336699;behavior: none;}

P
{color: #000000;
font-family: Verdana, Arial, sans-serif;
font-size: 10px;
font-style: normal;
font-variant: normal;
font-weight: normal;
text-decoration: none;
text-transform: none}

B
{
font-family: Verdana, Arial, sans-serif;
font-weight: bold;}

TD
{color: #000000;
font-family: Verdana, Arial, sans-serif;
font-size: 10px;
font-style: normal;
font-variant: normal;
font-weight: normal;
text-decoration: none;
text-transform: none;
text-align: left;
}

TABLE
{
	behavior: none;
}

TH
{color: #000000;
font-family: Verdana, Arial, sans-serif;
font-size: 10px;
font-style: normal;
font-variant: normal;
font-weight: bold;
text-decoration: none;
text-transform: none;
text-align: left;}

UL
{color: #000000;
font-family: Verdana, Arial, sans-serif;
font-size: 10px;
font-style: normal;
font-variant: normal;
font-weight: normal;
text-decoration: none;
text-transform: none}

OL
{color: #000000;
font-family: Verdana, Arial, sans-serif;
font-size: 10px;
font-style: normal;
font-variant: normal;
font-weight: normal;
text-decoration: none;
text-transform: none}

LI
{color: #000000;
font-family: Verdana, Arial, sans-serif;
font-size: 10px;
font-style: normal;
font-variant: normal;
font-weight: normal;
text-decoration: none;
text-transform: none}
*/
FONT.gray_bar_msg
{color: #000000;
font-family: Verdana, Arial, sans-serif;
font-size: 10px;
font-style: normal;
font-variant: normal;
text-decoration: none;
text-transform: none}

FONT.form_required
{color: #FF0000;
font-family: Arial, Verdana, sans-serif;
font-size:11px;
font-style: normal;
font-variant: normal;
text-decoration: none;
text-transform: none}

FONT.form_required_inlineitem
{color: #FF0000;
font-family: Arial, Verdana, sans-serif;
font-size:10px;
font-style: normal;
font-variant: normal;
text-decoration: none;
text-transform: none}

FONT.label_bold
{color: #000000;
font-family: Verdana, Arial, sans-serif;
font-size: 14px;
font-style: normal;
font-weight: bold;
font-variant: normal;
text-decoration: none;
text-transform: none}

FONT.small_note
{color: #000000;
font-family: Verdana, Arial, sans-serif;
font-size: 10px;
font-style: normal;
font-variant: normal;
text-decoration: none;
text-transform: none}

FONT.gray_note
{color: #777777;
font-family: Verdana, Arial, sans-serif;
font-size: 10px;
font-style: normal;
font-variant: normal;
text-decoration: none;
text-transform: none}

FONT.poweredby
{font-family: Arial, Helvetica, Geneva; font-size: 11px; color:000000; font-weight:none;}

.header_title, .header_title_nocap {
font-family: Arial, Helvetica, Geneva,sans;
font-size: 14px; 
color:#000000; 
font-weight:bold; 
letter-spacing:1.5;
padding-bottom:4px;
}

FONT.lineitem_title {
	font-family: arial, verdana,Helvetica, Geneva,sans;
	font-size: 12px; 
	color:#333; 
	font-weight:bold; 
	padding-left:5px;
}

FONT.header_title_normal
{color: #000000;
font-family: Verdana, sans-serif;
font-size: 10px;
font-style: normal;
font-variant: normal;
font-weight: bold;
text-decoration: none;
text-transform: none}

FONT.header_title_small
{color: #FFFFFF;
font-family: Arial, Verdana, sans-serif;
font-size: 9px;
font-style: normal;
font-variant: normal;
font-weight: bold;
text-decoration: none;
text-transform: none}

.tablhdr 
{color: #FFFFFF; 
font-family: Verdana, Arial, sans-serif; 
font-size: 14px; 
font-style: normal; 
font-variant: normal; 
font-weight:bold;
text-decoration: none; 
text-transform: none}

.blackmaptext
{color: #000000;
font-family: Arial, Verdana, sans-serif;
font-size: 10px;
/*letter-spacing:1.3; */
font-style: normal;
font-variant: normal;
font-weight: bold;
text-decoration: none;
text-transform: none}

.bluemaptext
{color: #000;
letter-spacing:1.3;
font-family: Arial, Verdana, sans-serif;
font-size: 10px;
font-style: normal;
font-variant: normal;
font-weight: bold;
text-decoration: none;
text-transform: none}

.bluemaptextsmall
{color: #000;
letter-spacing:1.3;
font-family: Arial, Verdana, sans-serif;
font-size: 9px;
font-style: normal;
font-variant: normal;
font-weight: bold;
text-decoration: none;
text-transform: none}

FONT.homepagetext
{color: #003366;
letter-spacing:1.2;
font-family: Arial, Verdana, sans-serif;
font-size: 24px;
font-style: normal;
font-variant: normal;
font-weight: bold;
text-decoration: none;
text-transform: none}

FONT.poweredby
{font-family: Arial, Helvetica, Geneva;	
font-size: 11px; 
color:000000; 
font-weight:none;}

FONT.Application	{font-family: Arial, Helvetica, Geneva;	font-size: 15px; font-weight:bold; letter-spacing:1.5;}

FONT.QVApplication	{font-family: Arial, Helvetica, Geneva;	font-size: 12px; font-weight:bold; letter-spacing:1.5;}

/*
INPUT
{
	height:			20px;
/*	border:			1px solid rgb(100,100,100); */ 
	vertical-align:		baseline;
	font-size:		10pt;
	behavior:url(#default#saveHistory);
}

DIV
{
	behavior: none;
}

SPAN
{
	behavior: none;
}
*/

INPUT.noborder
{
	/* We need to set the height here to the same value as the plain input, because IE will
	    find that plain one before this one.  If they're not the same, input.noborders will look
	    different between the browsers.  And don't bother with the important marker; doesn't work on IE
	    (correctly promotes the height, but also incorrectly promotes ALL other properties). */
	height:			14px;
	margin-top:		3px;
	margin-bottom:		3px;
	margin-right:		3px;
	border:			none; 
	vertical-align:		baseline;
	font-size:		10pt;
	behavior:url(#default#saveHistory);
}

/* Radio buttons need to have their height set separately for Mac FF to show them properly */
input[type=radio] {
	height:			13px;
}

A.hrefaction:link, 
A.hrefaction:visited,
A.hrefaction:active{
	color:#000;
	border: 1px solid #000;
	font-size:7pt;behavior:none;
	font-family:Verdana,Arial,Helvetica;
	background-color: #E5C646;
	text-decoration:none;
	padding:2px 5px 2px 5px;
	margin: 2px 2px;
	height:17px;
	vertical-align:middle;
	cursor:hand;
}

INPUT.nosavehistory {height:17px;border:1px solid #000;vertical-align:middle;font-size:7pt;behavior:none;font-family:Verdana,Arial,Helvetica;background-color:#E5C646;cursor:hand;}

INPUT.ac {width:78px;height:17px;border:1px solid #000;vertical-align:middle;font-size:7pt;behavior:none;font-family:Verdana,Arial,Helvetica;background-color:#E5C646;cursor:hand;}

INPUT.pf {width:60px;height:17px;border:1px solid #000;vertical-align:middle;font-size:7pt;behavior:none;font-family:Verdana,Arial,Helvetica;background-color:#E5C646;cursor:hand;}

INPUT.cac {width:120px;height:17px;border:1px solid #000;vertical-align:middle;font-size:7pt;behavior:none;font-family:Verdana,Arial,Helvetica;background-color:#E5C646;cursor:hand;}

INPUT.cap {width:136px;height:17px;border:1px solid #000;vertical-align:middle;font-size:7pt;behavior:none;font-family:Verdana,Arial,Helvetica;background-color:#E5C646;cursor:hand;}

INPUT.multiline_inputbox
{
	height:16px;
}

TEXTAREA
{
/*	border:			1px solid rgb(100,100,100); */ 
	//vertical-align:	baseline;
	vertical-align:	middle;
	font-size:		10pt;
	behavior:url(#default#savehistory);
}

SELECT
{
	behavior:url(#default#savehistory);
}

/*fieldset {
	padding: 0px;		// Makes IE and FF match.
}*/


A.Pick:link 		{font-family: Verdana,Arial,Helvetica; font-size:10px; color:#777777; behavior: none;}
A.Pick:visited 		{font-family: Verdana,Arial,Helvetica; font-size:10px; color:#777777; behavior: none;}
A.Pick:active 		{font-family: Verdana,Arial,Helvetica; font-size:10px; color:#777777; behavior: none;}
A.Pick:hover 		{font-family: Verdana,Arial,Helvetica; font-size:10px; color:#777777; behavior: none;}

A.ColDing:link 		{font-family: Webdings; font-size:15px; color:000000; font-weight:none; text-decoration:none;}
A.ColDing:visited	{font-family: Webdings; font-size:15px; color:000000; font-weight:none; text-decoration:none;}
A.ColDing:active 	{font-family: Webdings; font-size:15px; color:000000; font-weight:none; text-decoration:none;}
A.ColDing:hover 	{font-family: Webdings; font-size:15px; color:666666; font-weight:none; text-decoration:none;}

a.arrow_toggle{
text-decoration:none;
font-family: Webdings; 
font-size: 12px; 
letter-spacing:1.5; 
line-height:1;
color:#fff;
}

a.popup_toggle {
	text-decoration:none;
	font-family: Webdings;
	font-size: 12px;
	letter-spacing:1.5;
	line-height:1;
	color:#fff;
}

.toggleLayer {
	margin-top: 10px;	// Governs space between header and tabbed forms
}

CUSTOMTABDING {font-family: Webdings; font-size:12px; color:red;}

/* Login CSS */

body#bodyLogin{
	margin:0px;
	padding:0px;
	background:#fff;
}
/*td,div,span,p,li,input,select{
	font-family:verdana,arial,sans;
	font-size:10px;
	color#333;

} */
table.loginTable{
	border:1px solid #ccc;
	border-bottom:5px solid #ccc;
	background:#fff url(../../resources/images/ia-app/login/form_bg.gif) center left repeat-x; 
}
td.tdLogoCell{	
	padding:5px;
	border-right:1px solid #b2b2b2;
	background:#fff;
}
td.tdLoginCell{
	border-top:1px solid #b2b2b2;
}
td.tdHelpLinks{
	text-transform:uppercase;
	font-size:9px;
	text-align:right;
}
table.tableLoginData {
	margin-top:10px;
}
table.tableLoginData td{
	color:#333;
	padding:5px;
}

table.tableLoginData td.tdLabel{
	text-align:right;
	padding:10px 5px 10px 5px;
	font-weight:bold;
}

table.tableLoginData td.tdButton{
	border-top:1px solid #b2b2b2;
	padding:10px 0 0 120px;
}

table.databox {
	border-top:1px solid #000000;
	border-bottom:1px solid #000000;
}

/* Informational messages that appear in frames at top of tables, like success messages */
.infomessage {
	font-weight:bold;
	padding:10px 5px 10px 5px;
}

/* Warning messages that appear in frames at top of tables, prepended with red 'Warning' */
.warnmessage{
	font-weight:bold;
	padding:4px 4px 4px 4px;
}

/*  Error message styling */
.errorList {
	text-align: left;
	margin-left: 15px;
	padding-left: 15px;
	line-height: 500%;
}

.errorListItem {
	margin-left: 5px;
	padding-left: 5px;
}
/*  END Error message styling */

/* MultiPurpose */

.newFormButton{
 height:25px;
 background:#E5C646;
 font-size:10px;
}

/* Tabs CSS */
td.layoutTabOn {
	white-space: nowrap;
	border:1px solid #333;
	border-bottom:none;

	/* Skinnable 
	#477AD4 */
	font-family:arial;
	background:#666;
	font-weight:bold;
	color:#fff;
	padding:1px 3px 1px 3px;	
	font-size:12px;
	text-align:center;
}

td.layoutTabOff{
	font-family:arial;
	white-space: nowrap;
	border:1px solid #999;
	font-size:12px;
	border-bottom:none;
	text-align:center;
}

td.layoutTabOn a:link,
td.layoutTabOn a:visited,
td.layoutTabOn a:hover {
text-decoration:none;
color:#fff;
	font-weight:bold;
}
td.layoutTabOff a:link,
td.layoutTabOff a:visited {
	display:block;
	background:#fff;
	color:#999;
	padding:1px 3px 1px 3px;

	text-decoration:none;
	font-weight:bold;
}

td.layoutTabOff a:hover {
	display:block;
	background:#fff;
	color:#000;
}

td.required_text {
	padding:4px 4px 4px 0px;
	vertical-align:top;
}

td.bottom_button_holder {
	padding:4px 0px 4px 4px;
	vertical-align:top;
}

td.top_button_holder {
	padding:4px 4px 4px 4px;
	vertical-align:middle;
}

table.field_list_data{
	border:1px solid #333;
	background:#fff;
	empty-cells:show;
}
table.field_list_data_old{
	border:1px solid #333;
	background:#fff;
}
table.field_creditlist_data{
	border:0px solid #333;
	background:#ccc;
	padding-left:5px;
}
table.field_list_data_credit{
	border:0px solid #333;
	background:#fff;	
	empty-cells:show;
}
table.field_list_data_trans{
	border:1px solid #333;
	background:transparent;	
}
table.field_list_data_pad{
	border:1px solid #333;
	background:#fff;
	padding-left:2px;
	padding-right:2px;
}

table.field_list {
	border:0px solid #333;
	background:#fff;
	margin-bottom:15px;
}
table.field_list_nomargin {
	border:0px solid #333;
	background:#fff;
}
table.field_list_multitab_data{
	border:1px solid #333;
	border-top:0px none;
	background:#fff;
}
table.field_list_title {		
background:#DDD7C1;	
border:1px solid #333;
	border-bottom:none;
}
table.field_list_trans {
	border:0px solid #333;
	background:transparent;
	margin-bottom:15px;
}

table.field_list_title td h3 {
	font-family:arial;
	font-size:12px;
	/* Skinnable */
	background:#DDD7C1;
	color:#000;
	font-weight:bold;
	padding:2px 2px 2px 5px;

	margin:0;
}

table.field_list_title_box {		
background:#DDD7C1;	
border:1px solid #333;
}

table.section_title{
	background:#fff;
	padding:4px 0px 4px 0px;
	height:40px;
	border-bottom:2px solid #333;
}

table.stp_section_title{
	background:#fff;
	padding:0px 0px 0px 0px;
	border-bottom:2px solid #333;
}

table.bottom_section_title{
	background:#fff;
	padding:4px 0px 4px 0px;
	height:40px;	
}

/* Wizarrd */

table.wizTable{
margin-top:40px;
height:50px;
border:1px solid #b2b2b2;
}
tr.ctrl_bg {
	background:#DDD7C1;
}

td.list_value {
	border-bottom:1px solid #fff;
	padding-left:5px;
	padding-top:3px;
	padding-bottom:3px;
	empty-cells:show;		// Ensures borders are drawn correctly
	font-size:11px;
	font-family:Arial, Verdana, sans-serif;
}

td.wizHdr {
padding:10px 10px 5px 10px;
height:50px;
background:#fff url(../../resources/images/ia-app/backgrounds/tab_off_bg.gif) bottom left repeat-x;

}


td.wizSubHdr {
padding: 10px;
background:#fff url(../../resources/images/ia-app/login/form_bg.gif) bottom left repeat-x; 
	border-bottom:1px dashed #ccc;

}
table.wizTabs{
height:20px;
border-bottom:2px solid #000000;
}
table.wizTabs td{
}
table.wizTabs td.layoutTabOn{
border-top:1px solid #000;
}
table.wizTabs td.layoutTabOn a:link,
table.wizTabs td.layoutTabOn a:visited,
table.wizTabs td.layoutTabOn a:hover {
text-decoration:none;
color:#000;
	font-weight:bold;
}

table.wizFormTable{
border:1px solid #ccc;
margin:10px;
}

table.copy_table{
	border:1px solid #666;
}
table.copy_table td{
	background:#f0f0f0;
	padding:3px;
	vertical-align:middle;
	border-bottom:1px solid #fff;
}
/* Editor CSS */

.SimpleFieldLabel, .SimpleFieldLabelReq{
/*	display:block;
	background:#f0f0f0; 
	border-top:1px solid #fff;
	border-right:2px solid #fff;
	*/
	display:inline;
	padding: 0px;
	font-size:9px;
	
}

.SimpleFieldLabelReq{
font-weight:bold;	color:#f00;
}

.SimpleBoldField{
font-weight:bold;
}

//  New simple fields for all browsers.
.NewSimpleFieldLabel, .NewSimpleFieldLabelReq{
	padding: 0px;
	font-size:9px;
}

.NewSimpleFieldLabelReq{
font-weight:bold;	color:#f00;
}


td.tdAssistLinks{
	padding-left: 5px;
	
}

/*
td.tdAssistLinks a:link,
td.tdAssistLinks a:visited{
	
	padding:1px;
	background:#EFE6BF;
	color:#000;
	text-decoration:none;
	text-size:8px;
	font-family:verdana,sans;
	
}
td.tdAssistLinks a:hover {
	background:#fff;
	color:#666;
}
*/
table.tableMultiLineEditor{

border-collapse:collapse;
border-left:1px solid #333;
border-right:1px solid #333;

}
table.tableMultiLineEditor td {
vertical-align:top;
padding-top:0;
border-left:0px none;
border-right:0px none;

padding:bottom:0;
}
table.tableMultiLineEditor td.multilineCell{
border-bottom:0px none;
padding:2px;
}

td.multilineCell {
padding: 1px;
vertical-align: middle;
empty-cells:show;
}

.multilineCell a {
text-align: center;
vertical-align: middle;
}


td.multilineCell textarea {
overflow:auto;
}

td.singlelineCell {				/* Used for cell of single row items */
padding: 1px;
border-top: solid 1px rgb(102,102,102);
}

td.newmultilineTopCell {			/* Used for top cell of multi-row items */
padding: 1px;
border-top: solid 1px rgb(102,102,102);
}

td.newmultilineBottomCell {			/* Used for bottom cell of multi-row items */
padding: 1px;
}

.multilineCellDings a {
font-size:11px;
font-weight:none;
text-decoration:none;
}

.multilineCellSpaces {
empty-cells:show;
font-size:0px;
}

td.tdMultiLineHeaderCell{
background:#fff;
border:0px none;
padding-top:10px;
}

td.tdMultiLineItemHeaderCell{
border-top:1px solid #666;
}

td.tdMultiLineEditorCell {
border-top:1px solid #666;
	
}

td.tdShowMultiLineTotalHolder{
border-top:2px solid #666;
background:#666;
}
table.tableShowMultiLineTotals{
padding-top:3px;
}
table.tableShowMultiLineTotals td{
color:#fff;
}

table.tableMultiLineCustom {
}
table.tableMultiLineCustom td{
	background:#F0ECDC;
	padding:0 5px 0 5px;

}
.showCustomOptions{
	background:#F0ECDC;
	display:block;
	padding-top:5px;
	padding-bottom:5px;
}
td.tdCustomHolder{
	background:#F0ECDC;

}
.multiLineItemNumber{
	font-size:10px;
	color:#333;
	background:#ccc;
	display:block;
	padding-top:5px;
	padding-bottom:5px;
}

table.form_table{
background:#fff;
	border:1px solid #333;
	border-top:2px solid #999;
	margin-top:5px;
}
table.form_table td{

}

td.label_cell_mult {
	background:#E9E8DF;
	border-right:1px solid #CCCBC4;
	border-bottom:1px solid #FFFFFF;
	padding:5px;
	text-align:right;
	font-size:11px;
	font-family: Arial, sans-serif;
	color:#000000;
}

td.label_cell,
td.label_cell_req{
	background:#E9E8DF;
	border-right:1px solid #CCCBC4;
	width:270px;
	padding:5px;
	text-align:right;
	white-space:nowrap;
	font-size:11px;
	font-family: Arial, sans-serif;
	/*font-weight:bold;*/
	color:#000000;
}

td.label_cell_flexi{
	background:#E9E8DF;
	border-right:1px solid #CCCBC4;
	padding:5px;
	text-align:right;
	white-space:nowrap;
	font-size:11px;
	font-family: Arial, sans-serif;
	/*font-weight:bold;*/
	color:#000000;
}


td.layoutTabOn {
	background:#DDD7C1;
	font-size:11px;
	font-family: Arial, sans-serif;
	color:#000000;
	/* url(../../resources/images/ia-app/backgrounds/tab_on_bg_default.gif) top left repeat-x; */
}


td.layoutTabOff {
	background:#fff;
	color:#999999;
	font-size:11px;
	font-family: Arial, sans-serif;
	/* url(../../resources/images/ia-app/backgrounds/tab_on_bg_default.gif) top left repeat-x; */
}

td.label_cell_req{
color:#f00;
}

td.header_title_holder {
padding-bottom:4px 4px 4px 0px;
}

table.refresh_holder{
border:0px none;
border-left:1px solid #666;
float:right;

}
table.refresh_holder td{
padding-top:2px;}
}
value_cell {
	margin: 2px;
}
td.value_cell{
	font-size:11px;
	padding:2px 5px 2px 5px;
	white-space:nowrap;
	font-family:Arial, Verdana, sans-serif;
}
table.wizTable td.value_cell{
font-size:9px;
white-space:normal;
padding:1px 10px 1px 2px;
}
table.taskButtonHolder{
margin:2px 0 2px 0;
}
table.footerTable{
	padding:4px 4px 4px 4px;
}
table.footerTable td,
span.f,
table.footerTable td p{
font-size:10px;
color:#666;
}
h4 {
font-family: arial, Helvetica, Geneva,sans;
font-size: 12px; 
color:#000; 
font-weight:bold; 
letter-spacing:1.5;
text-transform:uppercase;
margin:0;
padding:5px 0 5px 0;
}



/* Calendar */
td.CalTop {
background:#f0f0f0;
}
td.CalMiddle{
background:#333;
}
td.CalBottom{
background:#666;
}
td.CalTitle{
background:#fff;
}

TABLE.PAGEHEADER {
	background:#fff;
	padding:0px 5px 4px 5px;
	height:40px;
	border-bottom:2px solid #333;
}
TABLE.PAGEFOOTER {
	background:#fff;
	padding:4px 0px 0px 4px;
	height:40px;
	border-top:0px solid #333;
}

/** For print check screens. Do clean up later. catch in code review phase **/
A.Column:link 		{font-family: Arial, Helvetica, Geneva; font-size:11px; color:000000; font-weight:bold; text-decoration:underline;}
A.Column:visited 	{font-family: Arial, Helvetica, Geneva; font-size:11px; color:000000; font-weight:bold; text-decoration:underline;}
A.Column:active 	{font-family: Arial, Helvetica, Geneva; font-size:11px; color:000000; font-weight:bold; text-decoration:underline;}
A.Column:hover 		{font-family: Arial, Helvetica, Geneva; font-size:11px; color:666666; font-weight:bold; text-decoration:underline;}

INPUT.Task{
color:#000; 
	
	border: 1px solid #000; 
	vertical-align: middle; 
	font-size:7pt; 
	behavior: none; 	
	background-color: #E5C646;
	height:18px;
	text-decoration:none; 
	cursor:hand;
}

INPUT.amtclass{
	font-size:11px;
	font-family: Verdana, Arial;
}


a.ReconLink:link,
a.ReconLink:visited,
a.ReconLink:active 	{
display:block;
padding:2px 2px 2px 15px;
font-family: Arial, Helvetica, Geneva; 
font-size:11px; 
border-bottom:1px solid #fff;
color:#2222FF; 
font-weight:none; 
text-decoration:underline;
height:12px;
background:#E9E8DF;
}
a.ReconLink:hover 	{
color:#333; 
border-bottom: 1px solid #999;
background:#fff;
}

DIV.QuickView {	
	padding:4px;
	margin: 4px;
	left:650px;
	top:100px;
	position:absolute;
	visibility:hidden;
	display:block;
	background:#eee;	
	border:solid 1px #b2b2b2;
	z-index:6;  /* before changing this see the top comment in FormComponents.js */
}

#page_screen0 {
	DISPLAY: none; FILTER: alpha(opacity=60); LEFT: 0px; WIDTH: 100%; POSITION: absolute; TOP: 0px; BACKGROUND-COLOR: #E9E8DF; opacity: 0.8
}

/* define custom css styles */
img.trans
{
url(../../resources/images/ia-app/backgrounds/trans.gif);
height:1;
}

.wizTopButton {
	margin: 4px;
	padding: 4px;
}

td.wizard_header{
	background:#E9E8DF;
	border-right:1px solid #CCCBC4;
	width:270px;
	padding:1px;
	text-align:left;
	white-space:nowrap;
	font-size:11px;
	font-family: Arial, sans-serif;
	/*font-weight:bold;*/
	color:#000000;
}

td.wizard_instruction{
	font-family:Arial, Helvetica, sans-serif;
	font-size:18px;
	font-weight: normal;
	padding:10px;
}

td.wizard_page_help{
	padding:10px;
	padding-left:25px;
	text-align:left;
	font-family: Arial, sans-serif;
	font-weight:bold;
	font-size:12;
	color:#000000;
}

.wizard_header_title {
  font-family:Arial, Helvetica, sans-serif;
  font-size:20px;
  color: #666666;
  font-weight: bold;
}

table.wizard_title{
	background-image: url(../../resources/images/ia-app/backgrounds/sub_header_bg.gif);
	height: 50px;
	border:solid 1px #b2b2b2;
	padding:0px 10px 0px 10px;
}

table.wizard_field_list_data{
	border:1px;
	background:#fff;	
}
table.wizard_fieldsonly_list_data{
	border:1px;
	background:#fff;	
}
table.wizard_controls_table {
	border-bottom:1px;
	border-color:333333
}
div.wizard_page {
/*	position:absolute;  */
	display:block; 
	left:30px; 
	padding:0 0px 0 0px; 
	visibility: visible; 
	width:100%;
}

.l_column_heeafader{
	background:#DDD7C1;
	border-right:0px solid #CCCBC4;
	width:270px;
	padding:1px;
	text-align:center;
	white-space:nowrap;
	font-size:11px;
	font-family: Arial, sans-serif;
	/*font-weight:bold;*/
	color:#000000;
}

/* define custom css styles */
img.trans
{
url(../../resources/images/ia-app/backgrounds/trans.gif);
height:1;
}

a.reqtextnolink{
text-align:right;
white-space:nowrap;
font-size:11px;
font-family: Arial, sans-serif;
color:#000;
text-decoration: none;
}
a.reqtextul {
text-align:right;
white-space:nowrap;
font-size:11px;
font-family: Arial, sans-serif;
color:#f00;
text-decoration: underline;
}
a.textnolink{
text-align:right;
white-space:nowrap;
font-size:11px;
font-family: Arial, sans-serif;
color:#000;
text-decoration: none;
}
a.textul {
text-align:right;
white-space:nowrap;
font-size:11px;
font-family: Arial, sans-serif;
color:#000;
text-decoration: underline;
}

.TopMargin {
margin-top:20px;
}

.BottomMargin {
margin-bottom:30px;
vertical-align:middle;
}

TD.consmap {
text-align:center;
vertical-align:middle;
white-space:nowrap;
--background-color:#FFF;
--border-bottom:1px solid #000;
font-family: Arial, Helvetica, Geneva; font-size:11px; color:000000; font-weight:bold; 
}

TD.lineitem_title {
	font-family: Arial, verdana,Helvetica, Geneva,sans;
	font-size: 12px; 
	color:#333; 
	font-weight:bold; 
	padding-left:5px;
	--text-align:center;
	vertical-align:middle;
	white-space:nowrap;
}
.toolTipStyle {padding: 5px; background-color: #E5C646;  border: 1px solid #000; width:180px;font-family:Verdana,Arial,Helvetica; font-size:7pt;  display:none; position:absolute;left:0px;top:0px; }

FONT.labelHelp{font-family: Arial, Helvetica, Geneva;font-size:10px;color:#0000FF;font-weight:none;font-style: italic}

.deliverselect{
	position:absolute;
	display:none;
	background-color:white;
}

.d_pc_title {
	color:#000000;
	font-size:12px;
	font-weight:bold;
	text-transform:uppercase;
}