/* Intelligent Web NameSpace */ 
var IW = IW || {}; 
/** 
 * Password validator logic 
 */ 
(function(IW) {
    
    var secondsInADay = 86400; 
    function PasswordValidator() { 
    }

    /**
     * Percentage limits which will be used to determine the strength of the password
     */
    PasswordValidator.prototype.strengthStrongLowerLimit   = 90;
    PasswordValidator.prototype.strengthMediumLowerLimit   = 50;
    PasswordValidator.prototype.strengthWeakLowerLimit     = 10;
    PasswordValidator.prototype.strengthVeryWeakLowerLimit = 1;
    /**
     * Expected minimum length of the Password as per Intacct Possword Policy
     */
    PasswordValidator.prototype.minimumPasswordLength = 8;
    
    /** 
     * How long a password can be expected to last 
     */ 
    PasswordValidator.prototype.passwordLifeTimeInDays = 365; 
    /** 
     * An estimate of how many attempts could be made per second to guess a password 
     */ 
    PasswordValidator.prototype.passwordAttemptsPerSecond = 500; 
    /** 
     * An array of regular expressions to match against the password. Each is associated 
     * with the number of unique characters that each expression can match. 
     * @param password 
     */ 
    PasswordValidator.prototype.expressions = [ 
        { regex : /[A-Z]+/, uniqueChars : 26 },
        { regex : /[a-z]+/, uniqueChars : 26 },
        { regex : /[0-9]+/, uniqueChars : 10 },
        { regex : /[`|&\^=_!\?.;,\\\/@$#*()%~<>{}\[\]'"+-]+/, uniqueChars : 31 }
    ]; 

    /** 
     * Checks the supplied password 
     * @param {String} password 
     * @return The predicted lifetime of the password, as a percentage of the defined password lifetime. 
     */ 
    PasswordValidator.prototype.checkPassword = function(password) { 
        var expressions = this.expressions;
        var i;
        var l = expressions.length;
        var expression;
        var possibilitiesPerLetterInPassword = 0;
 
        for (i = 0; i < l; i++) { 
            expression = expressions[i]; 
            if ( expression.regex.exec(password) ) {
                possibilitiesPerLetterInPassword += expression.uniqueChars; 
            } 
        } 

        var totalCombinations = Math.pow(possibilitiesPerLetterInPassword, password.length);
        // how long, on average, it would take to crack this (@ 200 attempts per second) 
        var crackTime = ((totalCombinations / this.passwordAttemptsPerSecond) / 2) / secondsInADay;
        // how close is the time to the projected time? 
        var percentage = crackTime / this.passwordLifeTimeInDays;

        percentage = Math.min(Math.max(password.length * 5, percentage * 100), 100);

        // Validate for minimum length of the password
        var minLengthElement  = jQuery("input[name='minIntacctPasswordLength']");
        if ( minLengthElement.val() > this.minimumPasswordLength ) {
            this.minimumPasswordLength = minLengthElement.val();
        }

        // Check for Intacct Password Policy
        if ( percentage > this.strengthStrongLowerLimit
             && !this.checkIntacctPasswordStandards(password) ) {
             // degrade strong to medium
            percentage = this.strengthStrongLowerLimit - 1;
        }

        return percentage;
    };

    /**
     * Check whether the password is in compliance with Intacct Password Policy. The check is
     * applicable only if the password is marked as strength by "Intelligent Web NameSpace"
     * 1. Must have atleast 6 characters
     * 2. Must have atleast one uppercase, lowercase character
     * 3. Must have atleast one number
     * 4. Must have atleast one special character
     * If password is violating any one of the policy, it will be termed as 'Medium'
     *
     * @param {String} password   Current password entered by user
     *
     * @return True|False Compliance with Intacct Password Policy or not
     */
    PasswordValidator.prototype.checkIntacctPasswordStandards = function(password) {
        var complianceWithIntacctStandards = true;
        if ( password.length >= this.minimumPasswordLength) {
            for (var i = 0; i < this.expressions.length; i++) {
                if ( !this.expressions[i].regex.exec(password) ) {
                    complianceWithIntacctStandards = false;
                    break;
                }
            }
        } else {
            complianceWithIntacctStandards = false;
        }
        
        return complianceWithIntacctStandards;
    };

    IW.PasswordValidator = new PasswordValidator(); 
})(IW); 

/** 
 * jQuery plugin which allows you to add password validation to any 
 * form element. 
 */ 
(function(IW, jQuery) { 
    function updatePassword() { 
        var percentage = IW.PasswordValidator.checkPassword(this.val());
        if ((new Number(percentage)).toFixed() == 0) {
            percentage = 0;
        }
        progressBar = jQuery("#passwordStrengthBar");
        progressText = jQuery("#passwordStrengthText");
        progressText.removeClass("strong medium weak useless").stop();
        progressBar.removeClass("strong medium weak useless").stop().animate({"width": percentage + "%"});

        if (percentage > IW.PasswordValidator.strengthStrongLowerLimit) {
            progressBar.addClass("strong"); 
            progressText.addClass("strong");
            progressText.text("Strong");
        } else if (percentage > IW.PasswordValidator.strengthMediumLowerLimit) {
            progressBar.addClass("medium");
            progressText.addClass("medium");
            progressText.text("Average");
        } else if (percentage > IW.PasswordValidator.strengthWeakLowerLimit) {
            progressBar.addClass("weak"); 
            progressText.addClass("weak");
            progressText.text("Weak");
        } else if (percentage > IW.PasswordValidator.strengthVeryWeakLowerLimit) {
            progressText.addClass("useless");
            progressText.text("Very weak");
            progressBar.addClass("useless");
        } else { 
            progressBar.addClass("useless"); 
            progressText.html("&nbsp;");
        }
    } 

    jQuery.fn.passwordValidate = function() 
    { 
        this.append("<label id='passwordStrengthText'></label><div class='bar_container'>" +
                    "<div class='passwordStrengthBar' id='passwordStrengthBar'></div></div>");

        var input = jQuery("input[id='newpassword']");
        input.bind('keyup', jQuery.proxy(updatePassword, input));
        updatePassword.apply(input);
        return input; // for chaining 
    } 
})(IW, jQuery); 

/** 
 * jQuery plugin which allows you to add password validation to any 
 * form element. 
 */ 
(function(jQuery) { 
    // Have all the password elements on the page validate
    jQuery(document).ready(function () {
        // Initialize the 'password match checking' logic
        jQuery(".passwordStrength").passwordValidate();
    });
})(jQuery);
