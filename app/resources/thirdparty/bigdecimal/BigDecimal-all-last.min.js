/*
 Copyright (c) 2012 <PERSON> and others
 Portions Copyright (c) 2003 STZ-IDA and PTV AG, Karlsruhe, Germany
 Portions Copyright (c) 1995-2001 International Business Machines Corporation and others

 All rights reserved.

 Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, provided that the above copyright notice(s) and this permission notice appear in all copies of the Software and that both the above copyright notice(s) and this permission notice appear in supporting documentation.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT OF THIRD PARTY RIGHTS. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR HOLDERS INCLUDED IN THIS NOTICE BE LIABLE FOR ANY CLAIM, OR ANY SPECIAL INDIRECT OR CONSEQUENTIAL DAMAGES, OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.

 Except as contained in this notice, the name of a copyright holder shall not be used in advertising or otherwise to promote the sale, use or other dealings in this Software without prior written authorization of the copyright holder.
*/
(function(){var v=function(){function g(){this.form=this.digits=0;this.lostDigits=!1;this.roundingMode=0;var s=this.DEFAULT_FORM,n=this.DEFAULT_LOSTDIGITS,j=this.DEFAULT_ROUNDINGMODE;if(4==g.arguments.length)s=g.arguments[1],n=g.arguments[2],j=g.arguments[3];else if(3==g.arguments.length)s=g.arguments[1],n=g.arguments[2];else if(2==g.arguments.length)s=g.arguments[1];else if(1!=g.arguments.length)throw"MathContext(): "+g.arguments.length+" arguments given; expected 1 to 4";var q=g.arguments[0];if(q!=
this.DEFAULT_DIGITS){if(q<this.MIN_DIGITS)throw"MathContext(): Digits too small: "+q;if(q>this.MAX_DIGITS)throw"MathContext(): Digits too large: "+q;}if(s!=this.SCIENTIFIC&&s!=this.ENGINEERING&&s!=this.PLAIN)throw"MathContext() Bad form value: "+s;if(!this.isValidRound(j))throw"MathContext(): Bad roundingMode value: "+j;this.digits=q;this.form=s;this.lostDigits=n;this.roundingMode=j}g.prototype.getDigits=function(){return this.digits};g.prototype.getForm=function(){return this.form};g.prototype.getLostDigits=
function(){return this.lostDigits};g.prototype.getRoundingMode=function(){return this.roundingMode};g.prototype.toString=function(){var g=null,n=0,j=null,g=this.form==this.SCIENTIFIC?"SCIENTIFIC":this.form==this.ENGINEERING?"ENGINEERING":"PLAIN",q=this.ROUNDS.length,n=0;a:for(;0<q;q--,n++)if(this.roundingMode==this.ROUNDS[n]){j=this.ROUNDWORDS[n];break a}return"digits="+this.digits+" form="+g+" lostDigits="+(this.lostDigits?"1":"0")+" roundingMode="+j};g.prototype.isValidRound=function(g){var n=0,
j=this.ROUNDS.length,n=0;for(;0<j;j--,n++)if(g==this.ROUNDS[n])return!0;return!1};g.prototype.PLAIN=0;g.prototype.SCIENTIFIC=1;g.prototype.ENGINEERING=2;g.prototype.ROUND_CEILING=2;g.prototype.ROUND_DOWN=1;g.prototype.ROUND_FLOOR=3;g.prototype.ROUND_HALF_DOWN=5;g.prototype.ROUND_HALF_EVEN=6;g.prototype.ROUND_HALF_UP=4;g.prototype.ROUND_UNNECESSARY=7;g.prototype.ROUND_UP=0;g.prototype.DEFAULT_FORM=g.prototype.SCIENTIFIC;g.prototype.DEFAULT_DIGITS=9;g.prototype.DEFAULT_LOSTDIGITS=!1;g.prototype.DEFAULT_ROUNDINGMODE=
g.prototype.ROUND_HALF_UP;g.prototype.MIN_DIGITS=0;g.prototype.MAX_DIGITS=999999999;g.prototype.ROUNDS=[g.prototype.ROUND_HALF_UP,g.prototype.ROUND_UNNECESSARY,g.prototype.ROUND_CEILING,g.prototype.ROUND_DOWN,g.prototype.ROUND_FLOOR,g.prototype.ROUND_HALF_DOWN,g.prototype.ROUND_HALF_EVEN,g.prototype.ROUND_UP];g.prototype.ROUNDWORDS="ROUND_HALF_UP,ROUND_UNNECESSARY,ROUND_CEILING,ROUND_DOWN,ROUND_FLOOR,ROUND_HALF_DOWN,ROUND_HALF_EVEN,ROUND_UP".split(",");g.prototype.DEFAULT=new g(g.prototype.DEFAULT_DIGITS,
g.prototype.DEFAULT_FORM,g.prototype.DEFAULT_LOSTDIGITS,g.prototype.DEFAULT_ROUNDINGMODE);return g}(),I=function(g){function s(a,b){return(a-a%b)/b}function n(a){var b=Array(a),c;for(c=0;c<a;++c)b[c]=0;return b}function j(){this.ind=0;this.form=g.prototype.PLAIN;this.mant=null;this.exp=0;if(0!=j.arguments.length){var a,b,c;1==j.arguments.length?(a=j.arguments[0],b=0,c=a.length):(a=j.arguments[0],b=j.arguments[1],c=j.arguments[2]);"string"==typeof a&&(a=a.split(""));var d,e,l,f,h,k=0,i=0;e=!1;var p=
i=i=k=0,m=0;f=0;0>=c&&this.bad("BigDecimal(): ",a);this.ind=this.ispos;"-"==a[0]?(c--,0==c&&this.bad("BigDecimal(): ",a),this.ind=this.isneg,b++):"+"==a[0]&&(c--,0==c&&this.bad("BigDecimal(): ",a),b++);e=d=!1;l=0;h=f=-1;p=c;k=b;a:for(;0<p;p--,k++){i=a[k];if("0"<=i&&"9">=i){h=k;l++;continue a}if("."==i){0<=f&&this.bad("BigDecimal(): ",a);f=k-b;continue a}if("e"!=i&&"E"!=i){("0">i||"9"<i)&&this.bad("BigDecimal(): ",a);d=!0;h=k;l++;continue a}k-b>c-2&&this.bad("BigDecimal(): ",a);e=!1;"-"==a[k+1]?(e=
!0,k+=2):k="+"==a[k+1]?k+2:k+1;i=c-(k-b);(0==i||9<i)&&this.bad("BigDecimal(): ",a);c=i;i=k;for(;0<c;c--,i++)p=a[i],"0">p&&this.bad("BigDecimal(): ",a),"9"<p?this.bad("BigDecimal(): ",a):m=p-0,this.exp=10*this.exp+m;if(e)this.exp=-this.exp;e=!0;break a}0==l&&this.bad("BigDecimal(): ",a);if(0<=f)this.exp=this.exp+f-l;m=h-1;k=b;a:for(;k<=m;k++)if(i=a[k],"0"==i)b++,f--,l--;else if("."==i)b++,f--;else break a;this.mant=Array(l);i=b;if(d){b=l;k=0;for(;0<b;b--,k++)k==f&&i++,p=a[i],"9">=p?this.mant[k]=p-
0:this.bad("BigDecimal(): ",a),i++}else{b=l;k=0;for(;0<b;b--,k++)k==f&&i++,this.mant[k]=a[i]-0,i++}if(0==this.mant[0]){this.ind=this.iszero;if(0<this.exp)this.exp=0;if(e)this.mant=this.ZERO.mant,this.exp=0}else if(e)this.form=g.prototype.SCIENTIFIC,f=this.exp+this.mant.length-1,(f<this.MinExp||f>this.MaxExp)&&this.bad("BigDecimal(): ",a)}}function q(){var a;if(1==q.arguments.length)a=q.arguments[0];else if(0==q.arguments.length)a=this.plainMC;else throw"abs(): "+q.arguments.length+" arguments given; expected 0 or 1";
return this.ind==this.isneg?this.negate(a):this.plus(a)}function w(){var a;if(2==w.arguments.length)a=w.arguments[1];else if(1==w.arguments.length)a=this.plainMC;else throw"add(): "+w.arguments.length+" arguments given; expected 1 or 2";var b=w.arguments[0],c,d,e,l,f,h,k,i=0;d=i=0;var i=null,p=i=0,m=0,n=0,q=0,o=0;a.lostDigits&&this.checkdigits(b,a.digits);c=this;if(0==c.ind&&a.form!=g.prototype.PLAIN)return b.plus(a);if(0==b.ind&&a.form!=g.prototype.PLAIN)return c.plus(a);d=a.digits;0<d&&(c.mant.length>
d&&(c=this.clone(c).round(a)),b.mant.length>d&&(b=this.clone(b).round(a)));e=new j;l=c.mant;f=c.mant.length;h=b.mant;k=b.mant.length;if(c.exp==b.exp)e.exp=c.exp;else if(c.exp>b.exp){i=f+c.exp-b.exp;if(i>=k+d+1&&0<d){e.mant=l;e.exp=c.exp;e.ind=c.ind;if(f<d)e.mant=this.extend(c.mant,d),e.exp-=d-f;return e.finish(a,!1)}e.exp=b.exp;i>d+1&&0<d&&(i=i-d-1,k-=i,e.exp+=i,i=d+1);i>f&&(f=i)}else{i=k+b.exp-c.exp;if(i>=f+d+1&&0<d){e.mant=h;e.exp=b.exp;e.ind=b.ind;if(k<d)e.mant=this.extend(b.mant,d),e.exp-=d-k;
return e.finish(a,!1)}e.exp=c.exp;i>d+1&&0<d&&(i=i-d-1,f-=i,e.exp+=i,i=d+1);i>k&&(k=i)}e.ind=c.ind==this.iszero?this.ispos:c.ind;if((c.ind==this.isneg?1:0)==(b.ind==this.isneg?1:0))d=1;else{do{d=-1;do if(b.ind!=this.iszero)if(f<k||c.ind==this.iszero)i=l,l=h,h=i,i=f,f=k,k=i,e.ind=-e.ind;else if(!(f>k)){p=i=0;m=l.length-1;n=h.length-1;c:for(;;){if(i<=m)q=l[i];else{if(p>n){if(a.form!=g.prototype.PLAIN)return this.ZERO;break c}q=0}o=p<=n?h[p]:0;if(q!=o){if(q<o)i=l,l=h,h=i,i=f,f=k,k=i,e.ind=-e.ind;break c}i++;
p++}}while(0)}while(0)}e.mant=this.byteaddsub(l,f,h,k,d,!1);return e.finish(a,!1)}function x(){var a;if(2==x.arguments.length)a=x.arguments[1];else if(1==x.arguments.length)a=this.plainMC;else throw"compareTo(): "+x.arguments.length+" arguments given; expected 1 or 2";var b=x.arguments[0],c=0,c=0;a.lostDigits&&this.checkdigits(b,a.digits);if(this.ind==b.ind&&this.exp==b.exp){c=this.mant.length;if(c<b.mant.length)return-this.ind;if(c>b.mant.length)return this.ind;if(c<=a.digits||0==a.digits){a=c;c=
0;for(;0<a;a--,c++){if(this.mant[c]<b.mant[c])return-this.ind;if(this.mant[c]>b.mant[c])return this.ind}return 0}}else{if(this.ind<b.ind)return-1;if(this.ind>b.ind)return 1}b=this.clone(b);b.ind=-b.ind;return this.add(b,a).ind}function r(){var a,b=-1;if(2==r.arguments.length)a="number"==typeof r.arguments[1]?new g(0,g.prototype.PLAIN,!1,r.arguments[1]):r.arguments[1];else if(3==r.arguments.length){b=r.arguments[1];if(0>b)throw"divide(): Negative scale: "+b;a=new g(0,g.prototype.PLAIN,!1,r.arguments[2])}else if(1==
r.arguments.length)a=this.plainMC;else throw"divide(): "+r.arguments.length+" arguments given; expected between 1 and 3";return this.dodivide("D",r.arguments[0],a,b)}function y(){var a;if(2==y.arguments.length)a=y.arguments[1];else if(1==y.arguments.length)a=this.plainMC;else throw"divideInteger(): "+y.arguments.length+" arguments given; expected 1 or 2";return this.dodivide("I",y.arguments[0],a,0)}function z(){var a;if(2==z.arguments.length)a=z.arguments[1];else if(1==z.arguments.length)a=this.plainMC;
else throw"max(): "+z.arguments.length+" arguments given; expected 1 or 2";var b=z.arguments[0];return 0<=this.compareTo(b,a)?this.plus(a):b.plus(a)}function A(){var a;if(2==A.arguments.length)a=A.arguments[1];else if(1==A.arguments.length)a=this.plainMC;else throw"min(): "+A.arguments.length+" arguments given; expected 1 or 2";var b=A.arguments[0];return 0>=this.compareTo(b,a)?this.plus(a):b.plus(a)}function B(){var a;if(2==B.arguments.length)a=B.arguments[1];else if(1==B.arguments.length)a=this.plainMC;
else throw"multiply(): "+B.arguments.length+" arguments given; expected 1 or 2";var b=B.arguments[0],c,d,e,l=e=null,f,h=0,g,i=0,p=0;a.lostDigits&&this.checkdigits(b,a.digits);c=this;d=0;e=a.digits;0<e?(c.mant.length>e&&(c=this.clone(c).round(a)),b.mant.length>e&&(b=this.clone(b).round(a))):(0<c.exp&&(d+=c.exp),0<b.exp&&(d+=b.exp));c.mant.length<b.mant.length?(e=c.mant,l=b.mant):(e=b.mant,l=c.mant);f=e.length+l.length-1;h=9<e[0]*l[0]?f+1:f;g=new j;var h=this.createArrayWithZeros(h),m=e.length,i=0;
for(;0<m;m--,i++)p=e[i],0!=p&&(h=this.byteaddsub(h,h.length,l,f,p,!0)),f--;g.ind=c.ind*b.ind;g.exp=c.exp+b.exp-d;g.mant=0==d?h:this.extend(h,h.length+d);return g.finish(a,!1)}function G(){var a;if(1==G.arguments.length)a=G.arguments[0];else if(0==G.arguments.length)a=this.plainMC;else throw"negate(): "+G.arguments.length+" arguments given; expected 0 or 1";var b;a.lostDigits&&this.checkdigits(null,a.digits);b=this.clone(this);b.ind=-b.ind;return b.finish(a,!1)}function H(){var a;if(1==H.arguments.length)a=
H.arguments[0];else if(0==H.arguments.length)a=this.plainMC;else throw"plus(): "+H.arguments.length+" arguments given; expected 0 or 1";a.lostDigits&&this.checkdigits(null,a.digits);return a.form==g.prototype.PLAIN&&this.form==g.prototype.PLAIN&&(this.mant.length<=a.digits||0==a.digits)?this:this.clone(this).finish(a,!1)}function C(){var a;if(2==C.arguments.length)a=C.arguments[1];else if(1==C.arguments.length)a=this.plainMC;else throw"pow(): "+C.arguments.length+" arguments given; expected 1 or 2";
var b=C.arguments[0],c,d,e,l=e=0,f,h=0;a.lostDigits&&this.checkdigits(b,a.digits);c=b.intcheck(this.MinArg,this.MaxArg);d=this;e=a.digits;if(0==e){if(b.ind==this.isneg)throw"pow(): Negative power: "+b.toString();e=0}else{if(b.mant.length+b.exp>e)throw"pow(): Too many digits: "+b.toString();d.mant.length>e&&(d=this.clone(d).round(a));l=b.mant.length+b.exp;e=e+l+1}e=new g(e,a.form,!1,a.roundingMode);l=this.ONE;if(0==c)return l;0>c&&(c=-c);f=!1;h=1;a:for(;;h++){c<<=1;0>c&&(f=!0,l=l.multiply(d,e));if(31==
h)break a;if(!f)continue a;l=l.multiply(l,e)}0>b.ind&&(l=this.ONE.divide(l,e));return l.finish(a,!0)}function D(){var a;if(2==D.arguments.length)a=D.arguments[1];else if(1==D.arguments.length)a=this.plainMC;else throw"remainder(): "+D.arguments.length+" arguments given; expected 1 or 2";return this.dodivide("R",D.arguments[0],a,-1)}function E(){var a;if(2==E.arguments.length)a=E.arguments[1];else if(1==E.arguments.length)a=this.plainMC;else throw"subtract(): "+E.arguments.length+" arguments given; expected 1 or 2";
var b=E.arguments[0];a.lostDigits&&this.checkdigits(b,a.digits);b=this.clone(b);b.ind=-b.ind;return this.add(b,a)}function t(){var a,b,c,d;if(6==t.arguments.length)a=t.arguments[2],b=t.arguments[3],c=t.arguments[4],d=t.arguments[5];else if(2==t.arguments.length)b=a=-1,c=g.prototype.SCIENTIFIC,d=this.ROUND_HALF_UP;else throw"format(): "+t.arguments.length+" arguments given; expected 2 or 6";var e=t.arguments[0],l=t.arguments[1],f,h=0,h=h=0,k=null,i=k=h=0;f=0;h=null;i=k=0;(-1>e||0==e)&&this.badarg("format",
1,e);-1>l&&this.badarg("format",2,l);(-1>a||0==a)&&this.badarg("format",3,a);-1>b&&this.badarg("format",4,b);if(c!=g.prototype.SCIENTIFIC&&c!=g.prototype.ENGINEERING)-1==c?c=g.prototype.SCIENTIFIC:this.badarg("format",5,c);if(d!=this.ROUND_HALF_UP)try{-1==d?d=this.ROUND_HALF_UP:new g(9,g.prototype.SCIENTIFIC,!1,d)}catch(j){this.badarg("format",6,d)}f=this.clone(this);-1==b?f.form=g.prototype.PLAIN:f.ind==this.iszero?f.form=g.prototype.PLAIN:(h=f.exp+f.mant.length,f.form=h>b?c:-5>h?c:g.prototype.PLAIN);
if(0<=l)a:for(;;){f.form==g.prototype.PLAIN?h=-f.exp:f.form==g.prototype.SCIENTIFIC?h=f.mant.length-1:(h=(f.exp+f.mant.length-1)%3,0>h&&(h=3+h),h++,h=h>=f.mant.length?0:f.mant.length-h);if(h==l)break a;if(h<l){k=this.extend(f.mant,f.mant.length+l-h);f.mant=k;f.exp-=l-h;if(f.exp<this.MinExp)throw"format(): Exponent Overflow: "+f.exp;break a}h-=l;if(h>f.mant.length){f.mant=this.ZERO.mant;f.ind=this.iszero;f.exp=0;continue a}k=f.mant.length-h;i=f.exp;f.round(k,d);if(f.exp-i==h)break a}b=f.layout();if(0<
e){c=b.length;f=0;a:for(;0<c;c--,f++){if("."==b[f])break a;if("E"==b[f])break a}f>e&&this.badarg("format",1,e);if(f<e){h=Array(b.length+e-f);e-=f;k=0;for(;0<e;e--,k++)h[k]=" ";this.arraycopy(b,0,h,k,b.length);b=h}}if(0<a){e=b.length-1;f=b.length-1;a:for(;0<e;e--,f--)if("E"==b[f])break a;if(0==f){h=Array(b.length+a+2);this.arraycopy(b,0,h,0,b.length);a+=2;k=b.length;for(;0<a;a--,k++)h[k]=" ";b=h}else if(i=b.length-f-2,i>a&&this.badarg("format",3,a),i<a){h=Array(b.length+a-i);this.arraycopy(b,0,h,0,
f+2);a-=i;k=f+2;for(;0<a;a--,k++)h[k]="0";this.arraycopy(b,f+2,h,k,i);b=h}}return b.join("")}function F(){var a;if(2==F.arguments.length)a=F.arguments[1];else if(1==F.arguments.length)a=this.ROUND_UNNECESSARY;else throw"setScale(): "+F.arguments.length+" given; expected 1 or 2";var b=F.arguments[0],c,d;c=c=0;c=this.scale();if(c==b&&this.form==g.prototype.PLAIN)return this;d=this.clone(this);if(c<=b)c=0==c?d.exp+b:b-c,d.mant=this.extend(d.mant,d.mant.length+c),d.exp=-b;else{if(0>b)throw"setScale(): Negative scale: "+
b;c=d.mant.length-(c-b);d=d.round(c,a);if(d.exp!=-b)d.mant=this.extend(d.mant,d.mant.length+1),d.exp-=1}d.form=g.prototype.PLAIN;return d}function v(){var a,b=0,c=0;a=Array(190);b=0;a:for(;189>=b;b++){c=b-90;if(0<=c){a[b]=c%10;j.prototype.bytecar[b]=s(c,10);continue a}c+=100;a[b]=c%10;j.prototype.bytecar[b]=s(c,10)-10}return a}function u(){var a,b;if(2==u.arguments.length)a=u.arguments[0],b=u.arguments[1];else if(1==u.arguments.length)b=u.arguments[0],a=b.digits,b=b.roundingMode;else throw"round(): "+
u.arguments.length+" arguments given; expected 1 or 2";var c,d,e=!1,g=0,f;c=null;c=this.mant.length-a;if(0>=c)return this;this.exp+=c;c=this.ind;d=this.mant;0<a?(this.mant=Array(a),this.arraycopy(d,0,this.mant,0,a),e=!0,g=d[a]):(this.mant=this.ZERO.mant,this.ind=this.iszero,e=!1,g=0==a?d[0]:0);f=0;if(b==this.ROUND_HALF_UP)5<=g&&(f=c);else if(b==this.ROUND_UNNECESSARY){if(!this.allzero(d,a))throw"round(): Rounding necessary";}else if(b==this.ROUND_HALF_DOWN)5<g?f=c:5==g&&(this.allzero(d,a+1)||(f=c));
else if(b==this.ROUND_HALF_EVEN)5<g?f=c:5==g&&(this.allzero(d,a+1)?1==this.mant[this.mant.length-1]%2&&(f=c):f=c);else if(b!=this.ROUND_DOWN)if(b==this.ROUND_UP)this.allzero(d,a)||(f=c);else if(b==this.ROUND_CEILING)0<c&&(this.allzero(d,a)||(f=c));else if(b==this.ROUND_FLOOR)0>c&&(this.allzero(d,a)||(f=c));else throw"round(): Bad round value: "+b;if(0!=f)this.ind==this.iszero?(this.mant=this.ONE.mant,this.ind=f):(this.ind==this.isneg&&(f=-f),c=this.byteaddsub(this.mant,this.mant.length,this.ONE.mant,
1,f,e),c.length>this.mant.length?(this.exp++,this.arraycopy(c,0,this.mant,0,this.mant.length)):this.mant=c);if(this.exp>this.MaxExp)throw"round(): Exponent Overflow: "+this.exp;return this}j.prototype.div=s;j.prototype.arraycopy=function(a,b,c,d,e){var g;if(d>b)for(g=e-1;0<=g;--g)c[g+d]=a[g+b];else for(g=0;g<e;++g)c[g+d]=a[g+b]};j.prototype.createArrayWithZeros=n;j.prototype.abs=q;j.prototype.add=w;j.prototype.compareTo=x;j.prototype.divide=r;j.prototype.divideInteger=y;j.prototype.max=z;j.prototype.min=
A;j.prototype.multiply=B;j.prototype.negate=G;j.prototype.plus=H;j.prototype.pow=C;j.prototype.remainder=D;j.prototype.subtract=E;j.prototype.equals=function(a){var b=0,c=null,d=null;if(null==a||!(a instanceof j)||this.ind!=a.ind)return!1;if(this.mant.length==a.mant.length&&this.exp==a.exp&&this.form==a.form){c=this.mant.length;b=0;for(;0<c;c--,b++)if(this.mant[b]!=a.mant[b])return!1}else{c=this.layout();d=a.layout();if(c.length!=d.length)return!1;a=c.length;b=0;for(;0<a;a--,b++)if(c[b]!=d[b])return!1}return!0};
j.prototype.format=t;j.prototype.intValueExact=function(){var a,b=0,c,d=0;a=0;if(this.ind==this.iszero)return 0;a=this.mant.length-1;if(0>this.exp){a+=this.exp;if(!this.allzero(this.mant,a+1))throw"intValueExact(): Decimal part non-zero: "+this.toString();if(0>a)return 0;b=0}else{if(9<this.exp+a)throw"intValueExact(): Conversion overflow: "+this.toString();b=this.exp}c=0;var e=a+b,d=0;for(;d<=e;d++)c*=10,d<=a&&(c+=this.mant[d]);if(9==a+b&&(a=s(c,1E9),a!=this.mant[0])){if(-2147483648==c&&this.ind==
this.isneg&&2==this.mant[0])return c;throw"intValueExact(): Conversion overflow: "+this.toString();}return this.ind==this.ispos?c:-c};j.prototype.movePointLeft=function(a){var b;b=this.clone(this);b.exp-=a;return b.finish(this.plainMC,!1)};j.prototype.movePointRight=function(a){var b;b=this.clone(this);b.exp+=a;return b.finish(this.plainMC,!1)};j.prototype.scale=function(){return 0<=this.exp?0:-this.exp};j.prototype.setScale=F;j.prototype.signum=function(){return this.ind};j.prototype.toString=function(){return this.layout().join("")};
j.prototype.layout=function(){var a,b=0,b=null,c=0,d=0;a=0;var d=null,e,b=0;a=Array(this.mant.length);c=this.mant.length;b=0;for(;0<c;c--,b++)a[b]=this.mant[b]+"";if(this.form!=g.prototype.PLAIN){b="";this.ind==this.isneg&&(b+="-");c=this.exp+a.length-1;if(this.form==g.prototype.SCIENTIFIC)b+=a[0],1<a.length&&(b+="."),b+=a.slice(1).join("");else if(d=c%3,0>d&&(d=3+d),c-=d,d++,d>=a.length){b+=a.join("");for(a=d-a.length;0<a;a--)b+="0"}else b+=a.slice(0,d).join(""),b=b+"."+a.slice(d).join("");0!=c&&
(0>c?(a="-",c=-c):a="+",b=b+"E"+a+c);return b.split("")}if(0==this.exp){if(0<=this.ind)return a;d=Array(a.length+1);d[0]="-";this.arraycopy(a,0,d,1,a.length);return d}c=this.ind==this.isneg?1:0;e=this.exp+a.length;if(1>e){b=c+2-this.exp;d=Array(b);0!=c&&(d[0]="-");d[c]="0";d[c+1]=".";var j=-e,b=c+2;for(;0<j;j--,b++)d[b]="0";this.arraycopy(a,0,d,c+2-e,a.length);return d}if(e>a.length){d=Array(c+e);0!=c&&(d[0]="-");this.arraycopy(a,0,d,c,a.length);e-=a.length;b=c+a.length;for(;0<e;e--,b++)d[b]="0";
return d}b=c+1+a.length;d=Array(b);0!=c&&(d[0]="-");this.arraycopy(a,0,d,c,e);d[c+e]=".";this.arraycopy(a,e,d,c+e+1,a.length-e);return d};j.prototype.intcheck=function(a,b){var c;c=this.intValueExact();if(c<a||c>b)throw"intcheck(): Conversion overflow: "+c;return c};j.prototype.dodivide=function(a,b,c,d){var e,l,f,h,k,i,p,m,q,n=0,o=0,r=0;l=l=o=o=o=0;e=null;e=e=0;e=null;c.lostDigits&&this.checkdigits(b,c.digits);e=this;if(0==b.ind)throw"dodivide(): Divide by 0";if(0==e.ind)return c.form!=g.prototype.PLAIN?
this.ZERO:-1==d?e:e.setScale(d);l=c.digits;if(0<l)e.mant.length>l&&(e=this.clone(e).round(c)),b.mant.length>l&&(b=this.clone(b).round(c));else{-1==d&&(d=e.scale());l=e.mant.length;d!=-e.exp&&(l=l+d+e.exp);l=l-(b.mant.length-1)-b.exp;if(l<e.mant.length)l=e.mant.length;if(l<b.mant.length)l=b.mant.length}f=e.exp-b.exp+e.mant.length-b.mant.length;if(0>f&&"D"!=a)return"I"==a?this.ZERO:this.clone(e).finish(c,!1);h=new j;h.ind=e.ind*b.ind;h.exp=f;h.mant=this.createArrayWithZeros(l+1);k=l+l+1;f=this.extend(e.mant,
k);i=k;p=b.mant;m=k;q=10*p[0]+1;1<p.length&&(q+=p[1]);k=0;a:for(;;){n=0;b:for(;;){if(i<m)break b;if(i==m){c:do{var t=i,o=0;for(;0<t;t--,o++){r=o<p.length?p[o]:0;if(f[o]<r)break b;if(f[o]>r)break c}n++;h.mant[k]=n;k++;f[0]=0;break a}while(0);o=f[0]}else o=10*f[0],1<i&&(o+=f[1]);o=s(10*o,q);0==o&&(o=1);n+=o;f=this.byteaddsub(f,i,p,m,-o,!0);if(0!=f[0])continue b;r=i-2;o=0;c:for(;o<=r;o++){if(0!=f[o])break c;i--}if(0==o)continue b;this.arraycopy(f,o,f,0,i)}if(0!=k||0!=n){h.mant[k]=n;k++;if(k==l+1)break a;
if(0==f[0])break a}if(0<=d&&-h.exp>d)break a;if("D"!=a&&0>=h.exp)break a;h.exp-=1;m--}0==k&&(k=1);if("I"==a||"R"==a){if(k+h.exp>l)throw"dodivide(): Integer overflow";if("R"==a){do{if(0==h.mant[0])return this.clone(e).finish(c,!1);if(0==f[0])return this.ZERO;h.ind=e.ind;l=l+l+1-e.mant.length;h.exp=h.exp-l+e.exp;l=i;o=l-1;b:for(;1<=o&&h.exp<e.exp&&h.exp<b.exp;o--){if(0!=f[o])break b;l--;h.exp+=1}l<f.length&&(e=Array(l),this.arraycopy(f,0,e,0,l),f=e);h.mant=f;return h.finish(c,!1)}while(0)}}else 0!=
f[0]&&(e=h.mant[k-1],0==e%5&&(h.mant[k-1]=e+1));if(0<=d){k!=h.mant.length&&(h.exp-=h.mant.length-k);e=h.mant.length-(-h.exp-d);h.round(e,c.roundingMode);if(h.exp!=-d)h.mant=this.extend(h.mant,h.mant.length+1),h.exp-=1;return h.finish(c,!0)}if(k==h.mant.length)h.round(c);else{if(0==h.mant[0])return this.ZERO;e=Array(k);this.arraycopy(h.mant,0,e,0,k);h.mant=e}return h.finish(c,!0)};j.prototype.bad=function(a,b){throw a+"Not a number: "+b;};j.prototype.badarg=function(a,b,c){throw"Bad argument "+b+" to "+
a+": "+c;};j.prototype.extend=function(a,b){var c;if(a.length==b)return a;c=n(b);this.arraycopy(a,0,c,0,a.length);return c};j.prototype.byteaddsub=function(a,b,c,d,e,g){var f,h,k,i,j,m,n=0;f=m=0;f=a.length;h=c.length;b-=1;i=k=d-1;i<b&&(i=b);d=null;g&&i+1==f&&(d=a);null==d&&(d=this.createArrayWithZeros(i+1));j=!1;1==e?j=!0:-1==e&&(j=!0);m=0;n=i;a:for(;0<=n;n--){0<=b&&(b<f&&(m+=a[b]),b--);0<=k&&(k<h&&(m=j?0<e?m+c[k]:m-c[k]:m+c[k]*e),k--);if(10>m&&0<=m){do{d[n]=m;m=0;continue a}while(0)}m+=90;d[n]=this.bytedig[m];
m=this.bytecar[m]}if(0==m)return d;c=null;g&&i+2==a.length&&(c=a);null==c&&(c=Array(i+2));c[0]=m;a=i+1;f=0;for(;0<a;a--,f++)c[f+1]=d[f];return c};j.prototype.diginit=v;j.prototype.clone=function(a){var b;b=new j;b.ind=a.ind;b.exp=a.exp;b.form=a.form;b.mant=a.mant;return b};j.prototype.checkdigits=function(a,b){if(0!=b){if(this.mant.length>b&&!this.allzero(this.mant,b))throw"Too many digits: "+this.toString();if(null!=a&&a.mant.length>b&&!this.allzero(a.mant,b))throw"Too many digits: "+a.toString();
}};j.prototype.round=u;j.prototype.allzero=function(a,b){var c=0;0>b&&(b=0);var d=a.length-1,c=b;for(;c<=d;c++)if(0!=a[c])return!1;return!0};j.prototype.finish=function(a,b){var c=0,d=0,e=null,c=d=0;0!=a.digits&&this.mant.length>a.digits&&this.round(a);if(b&&a.form!=g.prototype.PLAIN){c=this.mant.length;d=c-1;a:for(;1<=d;d--){if(0!=this.mant[d])break a;c--;this.exp++}if(c<this.mant.length)e=Array(c),this.arraycopy(this.mant,0,e,0,c),this.mant=e}this.form=g.prototype.PLAIN;c=this.mant.length;d=0;for(;0<
c;c--,d++)if(0!=this.mant[d]){if(0<d)e=Array(this.mant.length-d),this.arraycopy(this.mant,d,e,0,this.mant.length-d),this.mant=e;d=this.exp+this.mant.length;if(0<d){if(d>a.digits&&0!=a.digits)this.form=a.form;if(d-1<=this.MaxExp)return this}else if(-5>d)this.form=a.form;d--;if(d<this.MinExp||d>this.MaxExp){b:do{if(this.form==g.prototype.ENGINEERING&&(c=d%3,0>c&&(c=3+c),d-=c,d>=this.MinExp&&d<=this.MaxExp))break b;throw"finish(): Exponent Overflow: "+d;}while(0)}return this}this.ind=this.iszero;if(a.form!=
g.prototype.PLAIN)this.exp=0;else if(0<this.exp)this.exp=0;else if(this.exp<this.MinExp)throw"finish(): Exponent Overflow: "+this.exp;this.mant=this.ZERO.mant;return this};j.prototype.ROUND_CEILING=g.prototype.ROUND_CEILING;j.prototype.ROUND_DOWN=g.prototype.ROUND_DOWN;j.prototype.ROUND_FLOOR=g.prototype.ROUND_FLOOR;j.prototype.ROUND_HALF_DOWN=g.prototype.ROUND_HALF_DOWN;j.prototype.ROUND_HALF_EVEN=g.prototype.ROUND_HALF_EVEN;j.prototype.ROUND_HALF_UP=g.prototype.ROUND_HALF_UP;j.prototype.ROUND_UNNECESSARY=
g.prototype.ROUND_UNNECESSARY;j.prototype.ROUND_UP=g.prototype.ROUND_UP;j.prototype.ispos=1;j.prototype.iszero=0;j.prototype.isneg=-1;j.prototype.MinExp=-999999999;j.prototype.MaxExp=999999999;j.prototype.MinArg=-999999999;j.prototype.MaxArg=999999999;j.prototype.plainMC=new g(0,g.prototype.PLAIN);j.prototype.bytecar=Array(190);j.prototype.bytedig=v();j.prototype.ZERO=new j("0");j.prototype.ONE=new j("1");j.prototype.TEN=new j("10");return j}(v);if("function"===typeof define&&null!=define.amd)define({BigDecimal:I,
MathContext:v});else if("object"===typeof this)this.BigDecimal=I,this.MathContext=v}).call(this);
