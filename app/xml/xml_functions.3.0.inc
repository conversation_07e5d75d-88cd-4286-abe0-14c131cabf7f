<?
require_once 'xml_functions.2.1.inc';
global $xml_functions;

$xml_functions['getAPISession'] = array(
    'function' => 'getAPISession',
    'preProcessing' => 'full',
    'checkendpoint' => 'false');

$xml_functions['validate'] = array(
    'function' => 'validate',
    'preProcessing' => 'full');

$xml_functions['create'] = array(
    'function' => 'create',
    'preProcessing' => 'full');

$xml_functions['update'] = array(
    'function' => 'update',
    'preProcessing' => 'full');

$xml_functions['delete'] = array(
    'function' => 'delete',
    'preProcessing' => 'full');

$xml_functions['read'] = array(
    'function' => 'read',
    'preProcessing' => 'full');

$xml_functions['readRelated'] = array(
    'function' => 'readRelated',
    'preProcessing' => 'full');

$xml_functions['readByName'] = array(
    'function' => 'readByName',
    'preProcessing' => 'full');

$xml_functions['readByQuery'] = array(
    'function' => 'readByQuery',
    'preProcessing' => 'full');

$xml_functions['query'] = array(
    'function' => 'query',
    'preProcessing' => 'full');

$xml_functions['readMore'] = array(
    'function' => 'readMore',
    'preProcessing' => 'full');

$xml_functions['installApp'] = array(
    'function' => 'installApp',
    'preProcessing' => 'full');

$xml_functions['readReport'] = array(
    'function' => 'readReport',
    'preProcessing' => 'full');

$xml_functions['readView'] = array(
    'function' => 'readView',
    'preProcessing' => 'full');

$xml_functions['inspect'] = array(
    'function' => 'inspect',
    'preProcessing' => 'full');

$xml_functions['lookup'] = array(
    'function' => 'lookup',
    'preProcessing' => 'full');


$xml_functions['logout'] = array(
    'function' => 'logout',
    'preProcessing' => 'full');

$xml_functions['consolidate'] = array(
    'function' => 'consolidate',
    'preProcessing' => 'full',
    'permissionkey' => array('gl/activities/gcconsolidation'));

$xml_functions['consolidatebytier'] = array(
    'function' => 'consolidatebytier',
    'preProcessing' => 'full',
    'permissionkey' => array('atlas/activities/gctierconsolidation'));

$xml_functions['runDdsJob'] = array(
    'function' => 'runDdsJob',
    'preProcessing' => 'full'
);

$xml_functions['getDdsObjects'] = array(
    'function' => 'getDdsObjects',
    'preProcessing' => 'full');

$xml_functions['getDdsDdl'] = array(
    'function' => 'getDdsDdl',
    'preProcessing' => 'full');

$xml_functions['getFinancialSetup'] = array(
    'function' => 'getFinancialSetup',
    'preProcessing' => 'full',
);

$xml_functions['readEntityDetails'] = array(
    'function' => 'readEntityDetails',
    'preProcessing' => 'full',
);

$xml_functions['readUserFormatting'] = array(
    'function' => 'readUserFormatting',
    'preProcessing' => 'full',
);

$xml_functions['getDimensions'] = array(
    'function' => 'getDimensions',
    'preProcessing' => 'full',
);

$xml_functions['getDimensionRestrictions'] = array(
    'function' => 'getDimensionRestrictions',
    'preProcessing' => 'full'
);

$xml_functions['getDimensionRelationships'] = array(
    'function' => 'getDimensionRelationships',
    'preProcessing' => 'full'
);

$xml_functions['getDimensionAutofillDetails'] = array(
    'function' => 'getDimensionAutofillDetails',
    'preProcessing' => 'full'
);

$xml_functions['getDimensionRestrictedData'] = array(
    'function' => 'getDimensionRestrictedData',
    'preProcessing' => 'full'
);

$xml_functions['getDimensionAutofillData'] = array(
    'function' => 'getDimensionAutofillData',
    'preProcessing' => 'full'
);

$xml_functions['doProjectRevenueRecognitionInContract'] = array(
        'function' => 'doProjectRevenueRecognitionInContract',
        'preProcessing' => 'full'
);

$xml_functions['getUISession'] = array(
    'function' => 'getUISession',
    'preProcessing' => 'full'
);

$xml_functions['clearContractMEA'] = array(
    'function' => 'clearContractMEA',
    'preProcessing' => 'full'
);

$xml_functions['cancelContract'] = array(
    'function' => 'cancelContract',
    'preProcessing' => 'full'
);
$xml_functions['cancelContractDetail'] = array(
    'function' => 'cancelContractDetail',
    'preProcessing' => 'full'
);
$xml_functions['cancelContractExpenses'] = array(
        'function' => 'cancelContractExpenses',
        'preProcessing' => 'full'
);
$xml_functions['renewContractImmediate'] = array(
        'function' => 'renewContractImmediate',
        'preProcessing' => 'full'
);
$xml_functions['getAuditTrail'] = array(
    'function' => 'getAuditTrail',
    'preProcessing' => 'full'
);
$xml_functions['sforceEvent'] = array(
    'function' => 'sforceEvent',
    'preProcessing' => 'full'
);
$xml_functions['getTransactionsToApprove'] = array(
    'function' => 'getTransactionsToApprove',
    'preProcessing' => 'full'
);
$xml_functions['getApprovalHistory'] = array(
    'function' => 'getApprovalHistory',
    'preProcessing' => 'full'
);
$xml_functions['getPOApprovers'] = array(
    'function' => 'getPOApprovers',
    'preProcessing' => 'full'
);
$xml_functions['getEmployeesWithNoManager'] = array(
    'function' => 'getEmployeesWithNoManager',
    'preProcessing' => 'full'
);
$xml_functions['getContractBalances'] = array(
    'function' => 'getContractBalances',
    'preProcessing' => 'full'
);
$xml_functions['reprocessDBBPostings'] = array(
    'function' => 'reprocessDBBPostings',
    'preProcessing' => 'full'
);
$xml_functions['recallGLBatch'] = array(
    'function' => 'recallGLBatch',
    'preProcessing' => 'full'
);
$xml_functions['runDummyDispatcher'] = array(
    'function' => 'runDummyDispatcher',
    'preProcessing' => 'full'
);
$xml_functions['cleanCompanyData'] = array(
    'function' => 'cleanCompanyData',
    'preProcessing' => 'full'
);
$xml_functions['approveGLBatch'] = array(
    'function' => 'approveGLBatch',
    'preProcessing' => 'full',
    'permissionkey' => array('gl/activities/approveglbatch')
);
$xml_functions['declineGLBatch'] = array(
    'function' => 'declineGLBatch',
    'preProcessing' => 'full',
    'permissionkey' => array('gl/activities/approveglbatch')
);
$xml_functions['approveVendor'] = array(
    'function' => 'approveVendor',
    'preProcessing' => 'full',
    'permissionkey' => array('ap/activities/approvevendor')
);
$xml_functions['declineVendor'] = array(
    'function' => 'declineVendor',
    'preProcessing' => 'full',
    'permissionkey' => array('ap/activities/approvevendor')
);
$xml_functions['getVendorsToApprove'] = array(
    'function' => 'getVendorsToApprove',
    'preProcessing' => 'full',
    'permissionkey' => array('ap/activities/approvevendor')
);
$xml_functions['getVendorApprovalHistory'] = array(
    'function' => 'getVendorApprovalHistory',
    'preProcessing' => 'full',
    'permissionkey' => array('ap/activities/approvevendor')
);
$xml_functions['getGLBatchHistory'] = array(
    'function' => 'getGLBatchHistory',
    'preProcessing' => 'full'
);
$xml_functions['sagePeopleEvent'] = array(
    'function' => 'sagePeopleEvent',
    'preProcessing' => 'full'
);
$xml_functions['recallApBill'] = array(
    'function' => 'recallApBill',
    'preProcessing' => 'full'
);
