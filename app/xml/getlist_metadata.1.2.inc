<?  
//=============================================================================
//
//	FILE:			xmlgw_getlist.inc
//	AUTHOR:			<PERSON><PERSON>
//	DESCRIPTION:	backend functions for Wrappers to Ledger and Utility APIs
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================

global $xmlmapping;

$contact_fields = array(
    'subelements' => array('mailaddress'),
    'attr' => array(
        'printas' => 'PRINTAS',
        'companyname' => 'COMPANYNAME',
        'prefix' => 'MRMS',
        'firstname' => 'FIRSTNAME',
        'lastname' => 'LASTNAME',
        'initial' => 'MI',
        'phone1' => 'PHONE1',
        'phone2' => 'PHONE2',
        'cellphone' => 'CELLPHONE',
        'pager' => 'PAGER',
        'fax' => 'FAX',
        'email1' => 'EMAIL1',
        'email2' => 'EMAIL2',
        'url1' => 'URL1',
        'url2' => 'URL2'
    ),
    'table' => 'contact',
    'invfkey' => array('MAILADDRKEY'),
    'fkey' => 'RECORD#'
);
$mailaddress = array(
    'attr' => array(
        'address1' => 'ADDR1',
        'address2' => 'ADDR2',
        'city' => 'CITY',
        'state' => 'STATE',
        'zip' => 'ZIP',
        'country' => 'COUNTRY'
    ),
    'table' => 'mailaddress',
    'fkey' => 'RECORD#'
);

$batchdata = array(
    'key' => 'RECORD#',
    'batchtitle' => 'TITLE',
    'datecreated' => 'CREATED',
    'open' => 'OPEN',
    'status' => 'STATUS',
    'totalamount' => 'TOTAL'
);

$xmlmapping = array(
    'invoice' => array(
        'subelements' => array('lineitem'),
        'attr' => array(
            'key' => 'RECORD#',
            'customerid' => 'ENTITY',
            'datecreated' => 'WHENCREATED',
            'datedue' => 'WHENDUE',
            'termname' => 'TERMKEY',
            'invoiceno' =>'RECORDID',
            'ponumber' => 'DOCNUMBER',
            'totalamount' => 'TOTALENTERED',
            'totalpaid' => 'TOTALPAID',
            'totaldue' => 'TOTALDUE',
            'description' => 'DESCRIPTION'
        ),
        'permissionkey' => array('ar/activities/arbatch/view', 'ar/lists/invoice/view'),
        'intlevel' => array('invoiceitems'),
        'where' => array("recordtype='ri'"),
        'invfkey' => array('RECORD#'),
        'fkey' => 'prbatchkey',
        'table' => 'prrecord'
    ),
    'lineitem' => array(
        'attr' => array(
            'line_num' => 'LINE_NO',
            'accountlabel' => 'ACCOUNTLABELKEY',
            'amount' => 'AMOUNT',
            'memo' => 'DESCRIPTION',
            'locationid' => 'LOCATION#',
            'departmentid' => 'DEPT#'
        ),
        'where' => array("offset='1'"),
        'orderby' => "line_no",
        'table' => 'prentry',
        'fkey' => 'recordkey'
    ),
    'bill' => array(
        'subelements' => array('lineitem'),
        'attr' => array(
            'key' => 'RECORD#',
            'vendorid' => 'ENTITY',
            'datecreated' => 'WHENCREATED',
            'datedue' => 'WHENDUE',
            'termname' => 'TERMKEY',
            'billno' =>'RECORDID',
            'ponumber' => 'DOCNUMBER',
            'totalamount' => 'TOTALENTERED',
            'totalpaid' => 'TOTALPAID',
            'totaldue' => 'TOTALDUE',
            'description' => 'DESCRIPTION'
        ),
        'permissionkey' => array('ap/activities/apbatch/view','ap/lists/apbill/view'),
        'intlevel' => array('billitems'),
        'where' => array("recordtype='pi'"),
        'invfkey' => array('RECORD#'),
        'fkey' => 'prbatchkey',
        'table' => 'prrecord'
    ),
    'billbatch' => array(
        'subelements' => array('bill'),
        'attr' => $batchdata,
        'permissionkey' => array('ap/activities/apbatch/view'),
        'choiceattr' => array('open'),
        'where' => array("recordtype='pi'"),
        'invfkey' => array('RECORD#'),
        'table' => 'prbatch'
    ),
    'invoicebatch' => array(
        'subelements' => array('invoice'),
        'attr' => $batchdata,
        'permissionkey' => array('ar/activities/arbatch/view'),
        'choiceattr' => array('open'),
        'where' => array("recordtype='ri'"),
        'invfkey' => array('RECORD#'),
        'table' => 'prbatch'
    ),
    'apadjustmentbatch' => array(
        'subelements' => array('apadjustment'),
        'attr' => $batchdata,
        'permissionkey' => array('ap/activities/apbatch/view'),
        'choiceattr' => array('open'),
        'where' => array("recordtype='pa'"),
        'invfkey' => array('RECORD#'),
        'table' => 'prbatch'
    ),
    'aradjustmentbatch' => array(
        'subelements' => array('aradjustment'),
        'attr' => $batchdata,
        'permissionkey' => array('ar/activities/arbatch/view'),
        'choiceattr' => array('open'),
        'where' => array("recordtype='ra'"),
        'invfkey' => array('RECORD#'),
        'table' => 'prbatch'
    ),
    'arpaymentbatch' => array(
        'subelements' => array('arpayment'),
        'attr' => array(
            'key' => 'RECORD#',
            'batchtitle' => 'TITLE',
            'datecreated' => 'CREATED',
            'bankaccountid' => 'ACCOUNTNOKEY',
            'open' => 'OPEN',
            'status' => 'STATUS',
            'totalamount' => 'TOTAL'
        ),
        'permissionkey' => array('ar/activities/payments'),
        'choiceattr' => array('open'),
        'where' => array("recordtype='rp'"),
        'invfkey' => array('RECORD#'),
        'table' => 'prbatch'
    ),
    'apadjustment' => array(
        'subelements' => array('lineitem'),
        'attr' => array(
            'key' => 'RECORD#',
            'vendorid' => 'ENTITY',
            'adjustmentno' =>'RECORDID',
            'billno' => 'DOCNUMBER',
            'description' => 'DESCRIPTION',
            'totalamount' => 'TOTALENTERED',
            'totalpaid' => 'TOTALPAID',
            'totaldue' => 'TOTALDUE'
        ),
        'permissionkey' => array('ap/activities/apbatch/view', 'ap/lists/apadjustment/view'),
        'intlevel' => array('apadjustmentitems'),
        'where' => array("recordtype='pa'"),
        'invfkey' => array('RECORD#'),
        'fkey' => 'prbatchkey',
        'table' => 'prrecord'
    ),
    'aradjustment' => array(
        'subelements' => array('lineitem'),
        'attr' => array(
            'key' => 'RECORD#',
            'customerid' => 'ENTITY',
            'adjustmentno' =>'RECORDID',
            'invoiceno' => 'DOCNUMBER',
            'description' => 'DESCRIPTION',
            'totalamount' => 'TOTALENTERED',
            'totalpaid' => 'TOTALPAID',
            'totaldue' => 'TOTALDUE'
        ),
        'permissionkey' => array('ar/activities/arbatch/view', 'ar/lists/adjustment/view'),
        'intlevel' => array('aradjustmentitems'),
        'where' => array("recordtype='ra'"),
        'invfkey' => array('RECORD#'),
        'fkey' => 'prbatchkey',
        'table' => 'prrecord'
    ),
    'arpayment' => array(
        'subelements' => array('arpaymentitem'),
        'attr' => array(
            'customerid' => 'ENTITY',
            'paymentamount' => 'TOTALENTERED',
            'paymentapplied' => 'TOTALPAID',
            'refid' => 'DOCNUMBER',
            'datereceived' => 'WHENCREATED'
        ),
        'permissionkey' => array('ar/activities/payments/view'),
        'where' => array("recordtype='rp'"),
        'invfkey' => array('RECORD#'),
        'fkey' => 'prbatchkey',
        'table' => 'prrecord'
    ),
    'arpaymentitem' => array(
        'attr' => array(
            'invoicekey' => 'RECORDKEY',
            'amount' => 'AMOUNT'
        ),
        'fkey' => 'PAYMENTKEY',
        'table' => 'prpaymentrecords'
    ),
    'location' => array(
        'subelements' => array('contactinfo'),
        'attr' => array(
            'locationid' => 'LOCATION_NO',
            'name' => 'NAME',
            'startdate' => 'START_DATE',
            'enddate' => 'END_DATE', 
            'parentid' => 'PARENTKEY', 
            'employeeid' => 'EMPLOYEEKEY',
            'status' => 'STATUS'
        ),
        'permissionkey' => array('co/lists/location/view'),
        'table' => 'location',
        'invfkey' => array('CONTACTKEY'),
    ),
    'contactinfo' => $contact_fields,
    'returnto' => $contact_fields,
    'payto' => $contact_fields,
    'shipto' => $contact_fields,
    'billto' => $contact_fields,
    'personalinfo' => array(
        'subelements' => array('mailaddress'),
        'attr' => array(
            'lastname' => 'LASTNAME',
            'firstname' => 'FIRSTNAME',
            'initial' => 'MI',
            'phone1' => 'PHONE1',
            'phone2' => 'PHONE2',
            'email' => 'EMAIL1',
            'webpage' => 'URL1',
        ),
        'table' => 'contact',
        'invfkey' => array('MAILADDRKEY'),
        'fkey' => 'RECORD#'
    ),
    'contact' => $contact_fields,
    'mailaddress' => $mailaddress,
    'department' => array(
        'attr' => array(
            'departmentid' => 'DEPT_NO',
            'title' => 'TITLE',
            'parentid' => 'PARENT#',
            'supervisorid' => 'EMPLOYEEKEY',
            'status' => 'STATUS'
        ),
        'permissionkey' => array('co/lists/department/view'),
        'table' => 'department'
    ),
    'employee' => array(
        'subelements' => array('personalinfo'),
        'attr' => array(
            'employeeid' => 'EMPLOYEEID',
            'ssn' => 'SS',
            'title' => 'TITLE',
            'locationid' => 'LOCATIONKEY',
            'departmentid' => 'DEPTKEY',
            'supervisorid' => 'PARENTKEY',
            'birthdate' => 'BIRTHDATE',
            'startdate' => 'STARTDATE',
            'enddate' => 'ENDDATE',
            'status' => 'STATUS'
        ),
        //'intlevel' => array('personalinfo'),
        'permissionkey' => array('co/lists/employee/view'),
        'table' => 'employee',
        'invfkey' => array('CONTACTKEY')
    ),
    'vendor' => array(
        'subelements' => array('contactinfo','returnto','payto'),
        'attr' => array(
            'vendorid' => 'VENDORID',
            'name' => 'NAME',
            'parentid' => 'PARENTKEY',
            'termname' => 'TERMSKEY',
            'accountno' => 'ACCOUNTNO',
            'vendtype' => 'VENDTYPEKEY',
            'taxid' => 'TAXID',
            'creditlimit' => 'CREDITLIMIT',
            'totaldue' => 'TOTALDUE',
            'billingtype' => 'BILLINGTYPE',
            'status' => 'STATUS'
        ),
        'permissionkey' => array('ap/lists/vendor/view'),
        'choiceattr' => array('billingtype'),
        'table' => 'vendor',
        'invfkey' => array('CONTACTKEY', 'RETURNTOKEY', 'PAYTOKEY')
    ),
    'customer' => array(
        'subelements' => array('contactinfo','billto','shipto'),
        'attr' => array(
            'customerid' => 'CUSTOMERID',
            'name' => 'NAME',
            'parentid' => 'PARENTKEY',
            'termname' => 'TERMSKEY',
            'custrepid' => 'CUSTREPKEY',
            'shippingmethod' => 'SHIPVIAKEY',
            'custtype' => 'CUSTTYPEKEY',
            'taxid' => 'TAXID',
            'creditlimit' => 'CREDITLIMIT',
            'totaldue' => 'TOTALDUE',
            'territoryid' => 'TERRITORYKEY',
            'status' => 'STATUS'
        ),
        'permissionkey' => array('ar/lists/customer/view'),
        'table' => 'customer',
        'invfkey' => array('CONTACTKEY', 'BILLTOKEY', 'SHIPTOKEY')
    ),
    'apaccountlabel' => array(
        'attr' => array(
            'accountno' => 'ACCT_NO',
            'label' => 'DESCRIPTION',
            'status' => 'STATUS'
        ),
        'permissionkey' => array('ap/lists/apaccountlabel/view'),
        'table' => 'accountlabel',
        'where' => array("modulekey like '%AP'")
    ),
    'araccountlabel' => array(
        'attr' => array(
            'accountno' => 'ACCT_NO',
            'label' => 'DESCRIPTION',
            'status' => 'STATUS'
        ),
        'permissionkey' => array('ar/lists/araccountlabel/view'),
        'table' => 'accountlabel',
        'where' => array("modulekey like '%AR'")
    ),
    'journal' => array(
        'attr' => array(
            'symbol' => 'SYMBOL',
            'title' => 'TITLE',
            'adjustmentjrnl' => 'ADJ',
        ),
        'permissionkey' => array('gl/lists/journal/view'),
        'choiceattr' => array('adjustmentjrnl'),
        'table' => 'gljournal'
    ),
    'glaccount' => array(
        'attr' => array(
            'accountno' => 'ACCT_NO',
            'title' => 'TITLE',
            'parentid' => 'PARENT#',
            'normalbalance' => 'NORMAL_BALANCE',
            'requiredept' => 'REQUIREDEPT',
            'requireloc' => 'REQUIRELOC',
            'taxable' => 'TAXABLE',
            'accounttype' => 'ACCOUNT_TYPE',
            'status' => 'STATUS'
        ),
        'permissionkey' => array('gl/lists/glaccount/view'),
        'choiceattr' => array('normalbalance', 'accounttype', 'requiredept', 'requireloc', 'taxable'),
        'table' => 'glaccount'
    ),
    'term' => array(
        'attr' => array(
            'name' => 'NAME',
            'description' => 'DESCRIPTION',
            'status' => 'STATUS',
            'value' => 'VALUE',
            'module' => 'MODULEKEY'
        ),
        'permissionkey' => array('ap/lists/apterm/view','ar/lists/arterm/view'),
        'table' => 'term'
    ),
    'apterm' => array(
        'attr' => array(
            'name' => 'NAME',
            'description' => 'DESCRIPTION',
            'status' => 'STATUS',
            'value' => 'VALUE'
        ),
        'permissionkey' => array('ap/lists/apterm/view'),
        'where' => array("modulekey='3.AP'"),
        'table' => 'term'
    ),
    'arterm' => array(
        'attr' => array(
            'name' => 'NAME',
            'description' => 'DESCRIPTION',
            'status' => 'STATUS',
            'value' => 'VALUE'
        ),
        'permissionkey' => array('ar/lists/arterm/view'),
        'where' => array("modulekey='4.AR'"),
        'table' => 'term'
    ),
    'expensereport' => array(
        'subelements' => array('expense', 'approvalinfo'),
        'attr' => array(
            'key' => 'RECORD#',
            'employeeid' => 'ENTITY',
            'datecreated' => 'WHENCREATED',
            'expensereportno' =>'RECORDID',
            'description' => 'DESCRIPTION',
            'status'      => 'STATE',
            'totalamount' => 'TOTALENTERED',
            'totalpaid' => 'TOTALPAID',
            'totaldue' => 'TOTALDUE'
        ),
        'permissionkey' => array('ee/activities/myexpenses/view'),
        'intlevel' => array('expenses', 'approvalhistory'),
        'where' => array("recordtype='ei'"),
        'invfkey' => array('RECORD#', 'RECORD#'),
        'fkey' => 'prbatchkey',
        'table' => 'prrecord'
    ),
    'expensereportbatch' => array(
        'subelements' => array('expensereport'),
        'attr' => array(
            'key' => 'RECORD#',
            'batchtitle' => 'TITLE',
            'datecreated' => 'CREATED',
            'open' => 'OPEN',
            'status' => 'STATUS',
            'totalamount' => 'TOTAL'
        ),
        'permissionkey' => array('ee/activities/eebatch/view'),
        'choiceattr' => array('open'),
        'where' => array("recordtype='ei'"),
        'invfkey' => array('RECORD#'),
        'table' => 'prbatch'
    ),
    'expense' => array(
        'attr' => array(
            'line_num' => 'LINE_NO',
            'expensetype' => 'ACCOUNTLABELKEY',
            'amount' => 'AMOUNT',
            'expensedate' => 'ENTRY_DATE',
            'memo' => 'DESCRIPTION',
            'locationid' => 'LOCATION#',
            'departmentid' => 'DEPT#'
        ),
        'table' => 'prentry',
        'fkey' => 'recordkey'
    ),
    'approvalinfo' => array(
        'attr' => array(
            'reviewerid' => 'ENTITY',
            'action' => 'ACTION',
            'comments' => 'COMMENTS',
            'datereviewed' => 'WHENREVIEWED'
        ),
        'table' => 'approvalhistory',
        'fkey' => 'recordkey'
    ),
    'expensetypes' => array(
        'attr' => array(
            'accountno' => 'ACCT_NO',
            'expensetype' => 'DESCRIPTION',
            'status' => 'STATUS'
        ),
        'permissionkey' => array('ee/lists/eeaccountlabel/view'),
        'table' => 'accountlabel',
        'where' => array("modulekey like '%EE'")
    ),
    'reportingperiod' => array(
        'attr' => array(
            'name' => 'NAME',
            'startdate' => 'START_DATE',
            'enddate' => 'END_DATE'
        ),
        'permissionkey' => array('co/lists/reportingperiod/view'),
        'table' => 'glbudgettype',
        'where' => array("datetype = 99")

    ),
    'accountgroup' => array(
        'attr' => array(
            'accountgroupname' => 'NAME',
            'headingtitle' => 'TITLE',
            'totaltitle' => 'TOTALTITLE',
            'normalbalance' => 'NORMAL_BALANCE',
            'memberstype' => 'ISLEAF'
        ),
        'permissionkey' => array('gl/lists/glacctgrp/view'),
        'table' => 'glacctgrp'
    ),
    'bankaccount' => array(
        'subelements' => array('checklayout', 'mailaddress'),
        'attr' => array(
            'bankaccountid' => 'ACCOUNTID',
            'bankaccountno' => 'ACCOUNTNO', 
            'glaccountno' => 'GLACCOUNTKEY',
            'bankname' => 'NAME',
            'routingno' => 'ROUTINGNO',
            'branchid' => 'BRANCHID',
            'bankaccounttype' => 'TYPE',
            'phone' => 'PHONE',
            'nextcheck' => 'CHECK_CURRNO',
            'status' => 'STATUS',
            'lastreconciledbalance' => 'RECBAL',
            'lastreconcileddate' => 'RECDATE'
        ),
        'permissionkey' => array('cm/lists/checkingaccount/view', 'cm/lists/savingsaccount/view'),
        'invfkey' => array('CHECKLAYOUTKEY', 'MAILADDRKEY'),
        'table' => 'bankaccount'
    ),
    'checklayout' => array(
        'attr' => array(
            'checkpaperformat' => 'PAPERFORMAT',
            'signaturesrequired' => 'NUM_SIGLINES'
        ),
        'table' => 'checklayout',
        'fkey' => 'RECORD#'
    )
);

