<?php
//=============================================================================
//
//	FILE:			xml2dom.inc
//	AUTHOR:			
//	DESCRIPTION:	Function to convert a given XML code to a DOM in the 
//					form of a PHP3 associated array.
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================
/**
 * $Id: 
**/


// Example:
//	The input XML code looks like this.
//
// require "errors.php3";
//
// 
// <characters>
//	<name font='helvetica'>
//		Priyadarshini Shivaram Bhatt	
//	</name>
//	<name font='courier'>
//		Gokulmuthu
//	</name>
//	<name font='times'>
//		Anand Balaraman
//	</name>
// </characters>
//
//	The DOM generated for this file will look like this.
//
// $character['name'][0]['font'] = 'helvetica';
// $character['name'][0]['cdata'] = 'Priyadarshini Shivaram Bhatt';
// $character['name'][1]['font'] = 'courier';
// $character['name'][1]['cdata'] = 'Gokulmuthu';
// $character['name'][2]['font'] = 'times';
// $character['name'][2]['cdata'] = 'Anand Balaraman';
//
//
// $dom = xml2dom( $xmlfilename );
// echo $dom;
//
//


//********************************************************************
// The parser function provides a rich collection of error cases.
// To make use of them in generating the error message DOM we need
// this intermediate function 
//********************************************************************
/**
 * @param int $error_code
 * @param string $in_file
 * @param int $line_num
 */
function xml2dom_parse_error($error_code, $in_file, $line_num)
{
    switch ($error_code)
    {
    case XML_ERROR_NONE:
        xml2dom_error_message("E_PARSE_NONE", $in_file, $line_num);
        break;

    case XML_ERROR_NO_MEMORY:
        xml2dom_error_message("E_PARSE_NO_MEMORY", $in_file, $line_num);
        break;

    case XML_ERROR_SYNTAX:
        xml2dom_error_message("E_PARSE_SYNTAX", $in_file, $line_num);
        break;

    case XML_ERROR_NO_ELEMENTS:
        xml2dom_error_message("E_PARSE_NO_ELEMENTS", $in_file, $line_num);
        break;

    case XML_ERROR_INVALID_TOKEN:
        xml2dom_error_message(
            "E_PARSE_INVALID_TOKEN", $in_file, 
            $line_num
        );
        break;

    case XML_ERROR_UNCLOSED_TOKEN:
        xml2dom_error_message(
            "E_PARSE_UNCLOSED_TOKEN", $in_file, 
            $line_num
        );
        break;

    case XML_ERROR_PARTIAL_CHAR:
        xml2dom_error_message("E_PARSE_PARTIAL_CHAR", $in_file, $line_num);
        break;

    case XML_ERROR_TAG_MISMATCH:
        xml2dom_error_message("E_PARSE_TAG_MISMATCH", $in_file, $line_num);
        break;

    case XML_ERROR_DUPLICATE_ATTRIBUTE:
        xml2dom_error_message("E_PARSE_DUP_ATTR", $in_file, $line_num);
        break;

    case XML_ERROR_JUNK_AFTER_DOC_ELEMENT:
        xml2dom_error_message(
            "E_PARSE_JUNK_AFTER_DOC", $in_file, 
            $line_num
        );
        break;

    case XML_ERROR_PARAM_ENTITY_REF:
        xml2dom_error_message(
            "E_PARSE_ERR_PARAM_ENTITY_REF", $in_file, 
           $line_num
        );
        break;

    case XML_ERROR_UNDEFINED_ENTITY:
        xml2dom_error_message(
            "E_PARSE_UNDEFINED_ENTITY", $in_file, 
           $line_num
        );
        break;

    case XML_ERROR_RECURSIVE_ENTITY_REF:
        xml2dom_error_message(
            "E_PARSE_RECURSIVE_ENTITY_REF", $in_file, 
           $line_num
        );
        break;

    case XML_ERROR_ASYNC_ENTITY:
        xml2dom_error_message(
            "E_PARSE_ASYNC_ENTITY", $in_file, 
           $line_num
        );
        break;

    case XML_ERROR_BAD_CHAR_REF:
        xml2dom_error_message(
            "E_PARSE_BAD_CHAR_REF", $in_file, 
           $line_num
        );
        break;

    case XML_ERROR_BINARY_ENTITY_REF:
        xml2dom_error_message(
            "E_PARSE_BINARY_REF", $in_file, 
           $line_num
        );
        break;

    case XML_ERROR_ATTRIBUTE_EXTERNAL_ENTITY_REF:
        xml2dom_error_message(
            "E_PARSE_ATTR_EXT_ENTITY_REF", $in_file, 
           $line_num
        );
        break;

    case XML_ERROR_MISPLACED_XML_PI:
        xml2dom_error_message(
            "E_PARSE_MISPLACED_XML_PI", $in_file, 
            $line_num
        );
        break;

    case XML_ERROR_UNKNOWN_ENCODING:
        xml2dom_error_message(
            "E_PARSE_UNKNOWN_ENCODING", $in_file, 
           $line_num
        );
        break;

    case XML_ERROR_INCORRECT_ENCODING:
        xml2dom_error_message(
            "E_PARSE_INCORRECT_ENCODING", $in_file, 
           $line_num
        );
        break;

    case XML_ERROR_UNCLOSED_CDATA_SECTION:
        xml2dom_error_message(
            "E_PARSE_UNCLOSED_CDATA_SECTION", $in_file, 
           $line_num
        );
        break;

    case XML_ERROR_EXTERNAL_ENTITY_HANDLING:
        xml2dom_error_message(
            "E_PARSE_EXT_ENTITY_HANDLING", $in_file, 
           $line_num
        );
        break;
    }

}

//*********************************************************************
// This function takes the errortype as argument and generates 
// suitable DOM code specifying the type of error.
//**********************************************************************

/**
 * @param string $error_type
 * @param string $in_file
 * @param int $line_num
 */
function xml2dom_error_message($error_type, $in_file, $line_num)
{
    global $xml2dom_output, $xml2dom_error_count;
    include 'errors.inc';    // Force inclusion because somewhere it is being wiped before getting here.

    $xml2dom_error_head = "\$errormesg=[];\n\$errormesg['error'][";
    $xml2dom_error_tail1 = "]['errorno'][0]['cdata'] = "; 
    $xml2dom_error_tail2 = "]['description'][0]['cdata'] = "; 
    $xml2dom_error_tail3 = "]['source'][0]['cdata'] = "; 

    //If this is the first error clean the target string 
    //before writing the error messages.
    if( $xml2dom_error_count === 0) {
        $xml2dom_output = '';
    }

    //Generate the error message DOM with the necessary attributes
    /** @noinspection PhpUndefinedVariableInspection $errors comes from including errors.inc */
    /** @noinspection OnlyWritesOnParameterInspection */
    $xml2dom_output .= $xml2dom_error_head . $xml2dom_error_count . $xml2dom_error_tail1 . "'" .$errors["$error_type"]["num"] . "';\n";
    /** @noinspection OnlyWritesOnParameterInspection */
    $xml2dom_output .= $xml2dom_error_head . $xml2dom_error_count . $xml2dom_error_tail2 . "'" . $errors["$error_type"]["msg"] . "';\n";
    if ($in_file == "__XML_CODE__") {
        /** @noinspection OnlyWritesOnParameterInspection */
        $xml2dom_output .= $xml2dom_error_head . $xml2dom_error_count . $xml2dom_error_tail3 . "'line $line_num in input';\n";
    } else {
        /** @noinspection OnlyWritesOnParameterInspection */
        $xml2dom_output .= $xml2dom_error_head . $xml2dom_error_count . $xml2dom_error_tail3 . "'line $line_num in file " . urldecode($in_file) . "';\n";
    }

    $xml2dom_error_count++;
}

//**********************************************************************
// This function is to check if a given entity is defined or not.
// will return 'true' if it is defined and 'false' otherwise.
//***********************************************************************

/**
 * @param string $string
 *
 * @return bool
 */
function xml2dom_is_an_entity($string)
{
    global    $xml2dom_edata;

    if ( $xml2dom_edata[$string] ) {
        return true;
    }
    return false;
}

//************************************************************************
// This function tells the parser what to be done if it encounters
// a start tag of an element.
//************************************************************************

/**
 * @param resource  $parser
 * @param string    $name
 * @param array     $attrs
 */
function xml2dom_startElement(/** @noinspection PhpUnusedParameterInspection */ $parser, $name, $attrs)
{
    global    $xml2dom_depth, $xml2dom_tags, $xml2dom_tagi, 
    $xml2dom_output, $xml2dom_error_count,
    $xml2dom_parserState, $xml2dom_monitor;

    $xml2dom_parserState = "start";
    $xml2dom_monitor = "on";

    if ($xml2dom_depth === 0) {
        $xml2dom_output = "\$$name = [];\n";
    }

    $xml2dom_depth++;                //increment the depth.
    $steps = $xml2dom_depth;

    // Check if a given element at a given depth has already appeared
    // if so increment it, otherwise set its index to 0, indicating
    // its first occurence.
    if (isset($xml2dom_tagi[$steps][$name])) {    
        $xml2dom_tagi[$steps][$name]++; 
    }    
    else {
        $xml2dom_tagi[$steps][$name] = 0; 
    }

    if ($xml2dom_depth >= 1) {
        $xml2dom_tags[$xml2dom_depth] = $name;
    }
    //If there are any attributes to the element
    if (Util::countOrZero($attrs ?? [])) {
        // Collect the attributes one by one.
        
        foreach( $attrs as $key => $value )
        {
            // Generate the DOM corresponding to the current element.
            $pref = $xml2dom_tags[1];

            for ($j = 2; $j <= $xml2dom_depth; $j++)
            {
                $i = $xml2dom_tags[$j];
                $di = $xml2dom_tagi[$j][$i];

                $pref = $pref."['".$i;
                $pref = $pref."'][$di]";
            }
            $pref = $pref."['".$key."']";

            //$pref = strtolower($pref);
            if($xml2dom_error_count == 0) {
                // If there is no error encountered so far append the
                // generated DOM statement to the target string.
                $value = str_replace("'", "\'", $value);
                $xml2dom_output .= "\$$pref = '$value';\n";
            }

        }
    }
}


//************************************************************************
// This function tells the parser what to be done if it encounters
// the end tag of an element. 
//************************************************************************

/**
 * @param resource $parser
 * @param string   $name
 *
 * @throws Exception
 */
function xml2dom_endElement($parser, $name)
{
    global $xml2dom_depth, $xml2dom_tagi, $xml2dom_parserState, $xml2dom_monitor, $xml2dom_partial_parse;

    if ($xml2dom_parserState == "start" || $xml2dom_monitor == "on") {
        $xml2dom_parserState = "end";
        xml2dom_characterData($parser, "");
    }
    $xml2dom_parserState = "processed";

    $steps = $xml2dom_depth;
    $xml2dom_depth--;    //decrement the depth

    //unset the index of all the tags that appeared in the current 
    //depth level so that any occurence of them will be indexed
    //afresh for the next depth.
    unset($xml2dom_tagi[$steps + 1]);
                                        
    //  If this is a partial parse and we've reached the end of the authentication, then exit.
    if ($xml2dom_partial_parse && strcasecmp($name, "authentication") === 0) {
        throw new Exception("DONE");
    }
}


//************************************************************************
// This function tells the parser what to be done if it encounters
// a CDATA. 
//************************************************************************

/**
 * @param resource $parser
 * @param string   $data
 *
 */
function xml2dom_characterData(/** @noinspection PhpUnusedParameterInspection */ $parser, $data)
{
    global $xml2dom_depth, $xml2dom_tags, $xml2dom_tagi, $xml2dom_trimcdata;
    global $xml2dom_output, $xml2dom_error_count, $xml2dom_parserState, $xml2dom_monitor, $xml2dom_lasttags;

    //Generate the corresponding DOM statement
    $pref = $xml2dom_tags[1];

    for ($j = 2; $j <= $xml2dom_depth; $j++)
    {
        $i = $xml2dom_tags[$j];
        $di = $xml2dom_tagi[$j][$i];

        $pref = $pref."['".$i;
        $pref = $pref."'][$di]";
    }
    $pref = $pref."['cdata']";

    //  Check to see if we should trim.  Note that this function may be called when we are
    //   in the middle of a value - e.g. if the value contains UTF-8 characters.  So, we must
    //   check to see if the tag path is the same as the last call - if so, we're in the middle of a
    //   string value and do NOT want to strip spaces (they are significant, e.g. part of a account name).

    //  We need to handle any backslash at the end of a string.  If there is one, we will
    //   add another backslash to escape it.
    if ($data != '' && $data[strlen($data)-1] == '\\') { // DELIBERATE use of non-isl

        $withoutBackslashes = isl_rtrim($data, '\\');
        $lenWO = (int)(isl_strlen($data) - isl_strlen($withoutBackslashes));

        if ($lenWO & 1) {        //  Odd number of trailing backslashes.
            $data .= '\\';
        }
    }

    if ($pref != $xml2dom_lasttags) {
        //Trim the CDATA to remove the redundant whitespaces.
        //$data = trim($data);
        if (!$xml2dom_trimcdata) {
            $data = trim($data);
            if ($data != '') {
                $data = $data . "\n";
            }
            if ($data == "\n" && $xml2dom_parserState != "end") {
                $xml2dom_parserState = "cdata";
                return;
            }
        }
        else {    
            if (trim($data) == "" && $xml2dom_parserState != "end") {
                $xml2dom_parserState = "cdata";
                return;
            }
        }
    }

    $xml2dom_monitor = "off";
    $xml2dom_parserState = "cdata";

    //$pref = strtolower($pref);
    if($xml2dom_error_count == 0) {
        // If there is no error encountered so far append the
        // generated DOM statement to the target string.

        //  Escape any backslashes or single quotes, only (not double quotes).
        $data = addcslashes($data, '\\\'');

        // It is VERY important to use the `.=` operator on the variable `$xml2dom_output` (instead of repeating var in expr)
	// This changes the cost per call from exponential to linear. See https://onlinephp.io/c/e04b2
        $xml2dom_output .= "if (isset(\$$pref)) { \$$pref .= '$data'; } else { \$$pref = '$data'; }\n";

    }
    $xml2dom_lasttags = $pref;
}

//************************************************************************
// This function is to check if a given file is a trusted local file.
//************************************************************************

/**
 * @param string $file
 *
 * @return bool
 */
function xml2dom_trustedFile($file)
{
    if( $file == "__XML_CODE__") {
        return true; 
    }

    // only trust local files owned by ourselves
    else if (!preg_match("/^([a-z]+):\/\//i", $file) && fileowner($file) == getmyuid()) {
        return true;
    }
    return false;
}


//************************************************************************
// This function tells the parser what to be done if it encounters
// a PHP processing instruction. 
//************************************************************************

/**
 * @param resource $parser
 * @param string $target
 * @param string $data
 */
function xml2dom_PIHandler($parser, $target, $data)
{
    global $xml2dom_parser_file;

    switch (strtolower($target)) 
    {
    case "php":

        /** @noinspection PhpIllegalArrayKeyTypeInspection */
        if (xml2dom_trustedFile($xml2dom_parser_file[$parser])) {
            //If it is from a trusted file simply evaluate the
            //PHP instructions. 
            eval($data);
            echo "\n";
            //Note: Take care that the PHP instruction is a valid one.
            //There is no way of validating it inside this function.So
            //no hopes of handling this error.
        } 
        else 
        {
            //If the file is not a trusted one generate the corresponding
            //error message DOM.
            /** @noinspection PhpIllegalArrayKeyTypeInspection */
            $in_file = $xml2dom_parser_file[$parser];
            $line_num = xml_get_current_line_number($parser);
            xml2dom_error_message("E_NOT_TRUSTED_FILE", $in_file, $line_num);
        }
        break;
    }
}

//*******************************************************************
// This function tells the parser what to be done if it encounters
// anything other than start tag, end tag or CDATA. For our case
// this is being used to handle the internal entities.
//*******************************************************************

/**
 * @param resource $parser
 * @param string|bool $data
 */
function xml2dom_defaultHandler($parser, $data)
{
    global    $xml2dom_depth, $xml2dom_tags, $xml2dom_tagi, 
    $xml2dom_error_count, $xml2dom_output, $xml2dom_state, 
    $xml2dom_ename, $xml2dom_edata, $xml2dom_parser_file, $xml2dom_doctype; 

    //******************************************************************
    // As the parser encounters an entity definition it maintains
    // a state machine to determine if it is a internal entity or
    // not. The following lines upto the switch statement forms the
    // state machine and extracts the necessary information from the
    // internal entity definition.
    //****************************************************************** 

    if ( !($data == " " || $data == "\t" || $data == "\n") ) {
        if ($xml2dom_state == "normal" && $data == "<!ENTITY") {
            $xml2dom_state = "entity";
        }elseif ( $xml2dom_state == "entity" && $data != "" ) {
            $xml2dom_state = "name";
        }elseif ( $xml2dom_state == "name"  
            && ( strtolower($data) == "system" || strtolower($data) == "public")
        ) {
            $xml2dom_state = "normal";
        } elseif ( $xml2dom_state == "name" && $data != "" ) {
            $xml2dom_state = "alias";
        }

        if ($xml2dom_state == "normal" && $data == "<!DOCTYPE") {
            $xml2dom_doctype = $data;
            $xml2dom_state = "doctype";
        }elseif ( $xml2dom_state == "doctype" && $data != "" ) {
            /** @noinspection OnlyWritesOnParameterInspection */
            $xml2dom_doctype .= " " . $data;
            $xml2dom_state = "root";
        }elseif ( $xml2dom_state == "root"  
            && ( strtolower($data) == "system" || strtolower($data) == "public")
        ) {
            /** @noinspection OnlyWritesOnParameterInspection */
            $xml2dom_doctype .= " " . $data;
            $xml2dom_state = "system";
        } elseif ( $xml2dom_state == "system" && $data != "" ) {
            /** @noinspection OnlyWritesOnParameterInspection */
            $xml2dom_doctype .= " " . $data . ">";
            $xml2dom_state = "normal";
        }

        switch ($xml2dom_state)
        {
        case "name":
            $xml2dom_ename = $data;
            break;

        case "alias":
            $data_len = strlen($data) - 2;
            $data = substr($data, 1);
            $data = substr($data, 0, $data_len);
            $xml2dom_edata[$xml2dom_ename] = $data;
            $xml2dom_state = "normal";
            $xml2dom_ename = "";
            break;
            
        default :
            break;
        }
     
        //**********************************************************************
        // The following lines are executed when the parser encounters an
        // internal entity.
        //**********************************************************************
        if (substr($data, 0, 1) == "&" && substr($data, -1, 1) == ";") {
            $entity_len = strlen($data) - 2;
            $entity_name = substr($data, 1);
            $entity_name = substr($entity_name, 0, $entity_len);

            // Check if the entity is defined.
            if( xml2dom_is_an_entity($entity_name) == true) {
                //The entity is defined, so generate the corresponding 
                //DOM statement.
                $pref = $xml2dom_tags[1];

                for ($j = 2; $j <= $xml2dom_depth; $j++)
                {
                    $i = $xml2dom_tags[$j];
                    $di = $xml2dom_tagi[$j][$i];

                    $pref = $pref."['".$i;
                    $pref = $pref."'][$di]";
                }
                $pref = $pref."['cdata']";

                //$pref = strtolower($pref);
                if($xml2dom_error_count == 0) {
                    // If there is no error encountered so far append the
                    // generated DOM statement to the target string.
                    $xml2dom_output .= "\$$pref .= '$xml2dom_edata[$entity_name]';\n";
                }
            }
            else
            {
                //The internal entity used is not defined, so generate
                //the appropriate error message DOM.
                /** @noinspection PhpIllegalArrayKeyTypeInspection */
                $in_file = $xml2dom_parser_file[spl_object_hash($parser)];
                $line_num = xml_get_current_line_number($parser);
                echo "Line num: $line_num\n";
                xml2dom_error_message("E_UNDEFINED_ENTITY", $in_file, $line_num);
            }
        } 
    }

}

//***********************************************************************
// This function tells the parser what to do if it encounters an external
// entity.
//************************************************************************

/**
 * @param resource $parser
 * @param string $openEntityNames
 *
 * @return bool
 */
function xml2dom_externalEntityRefHandler($parser, /** @noinspection PhpUnusedParameterInspection */ $openEntityNames)
{
    global $xml2dom_parser_file;

    /** @noinspection PhpIllegalArrayKeyTypeInspection */
    $in_file = $xml2dom_parser_file[$parser];
    $line_num = xml_get_current_line_number($parser);
    xml2dom_error_message("E_OPEN_EXT_ENTITY", $in_file, $line_num);
    return false;
}

//**********************************************************************
// This function creates a new parser, equips it with the necessary
// information of what functions it should referto under various
// circumstances and returns an array encasing the handle to the 
// parser and a pointer to the file to be parsed.
//**********************************************************************

/**
 * @param string $file
 * @param bool $partialParse
 *
 * @return array|bool
 */
function xml2dom_new_xml_parser($file, $partialParse = false)
{
    global $xml2dom_parser_file, $xml2dom_partial_parse;

    // Create a new parser
    $xml_parser = xml_parser_create();

    // Various handler files the parser may have to use.
    xml_parser_set_option($xml_parser, XML_OPTION_CASE_FOLDING, 0);
    xml_set_element_handler($xml_parser, "xml2dom_startElement", "xml2dom_endElement");
    xml_set_character_data_handler($xml_parser, "xml2dom_characterData");
    xml_set_processing_instruction_handler($xml_parser, "xml2dom_PIHandler");
    xml_set_default_handler($xml_parser, "xml2dom_defaultHandler");
    xml_set_external_entity_ref_handler($xml_parser, "xml2dom_externalEntityRefHandler");
    
    if ($file == "__XML_CODE__") {
        $fp = null; //XML code is passed as string.
    }
    else
    {
        if (!($fp = @fopen($file, "r"))) { 
            return false;    //Unable to open the file.
        }    
    }
    if (!is_array($xml2dom_parser_file)) {
        settype($xml2dom_parser_file, "array");
    }
    $xml2dom_partial_parse = $partialParse;
    return array($xml_parser, $fp);
}

//**********************************************************************
// This is the mother function that performs the task of converting
// an XML input (stored in a file) to the corresponding DOM
//**********************************************************************

/**
 * @param string $filename
 * @param string $debug
 * @param int    $trimcdata
 * @param bool   $partialParse
 *
 * @return bool
 */
function xml2dom($filename, $debug = "no", $trimcdata = 1, $partialParse = false)
{
    // Declaration of variables
    global    $xml2dom_output, $xml2dom_depth, $xml2dom_tags, 
    $xml2dom_tagi, $xml2dom_parser;

    global    $xml2dom_state, $xml2dom_ename, $xml2dom_edata, 
    $xml2dom_error_count, $xml2dom_trimcdata, $xml2dom_lasttags; 

    $xml2dom_output    = "";
    $xml2dom_depth    = 0;
    $xml2dom_tags    = array();
    $xml2dom_tagi    = array();

    $xml2dom_state    = "normal"; 
    $xml2dom_ename    = "";
    $xml2dom_edata    = array();
    $xml2dom_trimcdata = $trimcdata;
    $xml2dom_lasttags = "";                // Tracks last tag, to see if value is fragmented or not.

    $xml2dom_error_count = 0;

    // Create a parser
    if (!(list($xml2dom_parser, $xml2dom_file) =        xml2dom_new_xml_parser($filename, $partialParse))
    ) {
        // Unable to create a parser, so generate the appropriate
        // error message DOM.

        $in_file = $filename;
        $line_num = 0;
        xml2dom_error_message("E_OPEN_FILE_URL", $in_file, $line_num);
        xml_parser_free($xml2dom_parser);

        return (xml2dom_pack_output($debug));
    }

    while ($data = fread($xml2dom_file, 4096)) 
    {
        // Segment the file and parse it part-by-part.
        if (!xml_parse($xml2dom_parser, $data, feof($xml2dom_file))) {
            // Error while parsing the file, so generate the appropriate
            // error message DOM.

            $in_file = $filename;
            $line_num = xml_get_current_line_number($xml2dom_parser);
            xml2dom_parse_error(
                xml_get_error_code($xml2dom_parser), 
                $in_file, $line_num
            );
            xml_parser_free($xml2dom_parser);

            return (xml2dom_pack_output($debug));
        }
    }
    xml_parser_free($xml2dom_parser);

    return (xml2dom_pack_output($debug));
}

//**********************************************************************
// This is the mother function that performs the task of converting
// an XML input ( stored in a string ) to the corresponding DOM
//**********************************************************************

/**
 * @param string $xml_string
 * @param string $debug
 * @param int    $trimcdata
 * @param bool   $partialParse
 * @param array    $largeValSubs
 *
 * @return bool|mixed
 * @throws Exception
 */
function xmlstr2dom($xml_string, $debug = "no", $trimcdata = 1, $partialParse = false, $largeValSubs = [])
{
    // Declaration of variables
    global    $xml2dom_output, $xml2dom_depth, $xml2dom_tags, 
    $xml2dom_tagi, $xml2dom_parser;

    global    $xml2dom_state, $xml2dom_ename, $xml2dom_edata, 
    $xml2dom_error_count, $xml2dom_trimcdata, $xml2dom_lasttags; 

    $xml2dom_output    = "";
    $xml2dom_depth    = 0;
    $xml2dom_tags    = array();
    $xml2dom_tagi    = array();

    $xml2dom_state    = "normal"; 
    $xml2dom_ename    = "";
    $xml2dom_edata    = array();

    $xml2dom_error_count = 0;
    $filename = "__XML_CODE__";
    $xml2dom_trimcdata = $trimcdata;
    $xml2dom_lasttags = "";                // Tracks last tag, to see if value is fragmented or not.

    $subsarray = xml_substitute_largevals($xml_string, $largeValSubs);

    // Create a parser
    /** @noinspection PhpUnusedLocalVariableInspection */
    if (!(list($xml2dom_parser, $xml2dom_file) = xml2dom_new_xml_parser($filename, $partialParse))) {
        // Unable to create a parser, so generate the appropriate
        // error message DOM.

        $in_file = $filename;
        $line_num = 0;
        xml2dom_error_message('E_OPEN_FILE_URL', $in_file, $line_num);
        xml_parser_free($xml2dom_parser);

        return (xml2dom_pack_output($debug));
    }

    if ($xml_string != '') {
        try {
            // Parse it.
            if (!xml_parse($xml2dom_parser, $xml_string, true)) {
                // Error while parsing the string, so generate the appropriate
                // error message DOM.
    
                $in_file = $filename;
                $line_num = xml_get_current_line_number($xml2dom_parser);
                xml2dom_parse_error(
                    xml_get_error_code($xml2dom_parser), 
                    $in_file, $line_num
                );
                xml_parser_free($xml2dom_parser);
    
                return (xml2dom_pack_output($debug));
            }
        } catch (Exception $e) {
            //  If this is the partial parse DONE exception, gracefully continue.
            if ($e->getMessage() != 'DONE') {
                throw $e;
            }
        }
    }
    xml_parser_free($xml2dom_parser);

    return (xml2dom_pack_output($debug, $largeValSubs, $subsarray));
}

/*
 *  xml_substitute_largevals
 *    This function takes the input XML string and substitutes potentially large leaf node
 *   strings with unique IDs prior to parsing.  This is due to limitations in the parser
 *   that cause large XML files to fail with a NO_MEMORY error.  This function is fed
 *   an array of arrays that describe the potentially large value tags to substitute for.
 *
 * @param string $xmlstring Complete XML string that will be parsed.
 * @param array  $subsArray Array of arrays - one for each tag with potentially large values.
 *
 * @return array Substituted large values, first indexed by SUBS_ID, then uniquely generated value IDs.
 */
/**
 * @param string $xmlstring
 * @param array $subsArray
 *
 * @return array
 */
function xml_substitute_largevals(&$xmlstring, $subsArray)
{

    //  Go through all the potentially large substitution tags.
    $outVals = array();
    foreach ($subsArray as &$subs) {

        if ( isset($subs['START_TAG']) ) {
            //  Break the string into pieces, separated by the start tag.
            $pieces = explode($subs['START_TAG'], $xmlstring);
            $cnt = count($pieces);
            /** @noinspection PhpUnusedLocalVariableInspection */
            $tagStartLen = isl_strlen($subs['START_TAG']);
            $theseOutVals = array();

            //  Go through each piece, extract the large value and substitute with an generated ID.
            for ($i=1; $i<$cnt; $i++) {
                $endPos = isl_strpos($pieces[$i], $subs['END_TAG']);

                //  Found one.  Extract the large value and remember it, then substitute a unique
                //   id that we'll use later to resubstitute the original value.
                /** @noinspection PhpUndefinedVariableInspection */
                if ( $pos !== false) {
                    $subsId = $subs['SUBS_ID'] . $i;
                    $theseOutVals[$subsId] = isl_substr($pieces[$i], 0, $endPos);
                    $pieces[$i] = isl_substr_replace($pieces[$i], $subsId, 0, $endPos);
                }
            }

            if (count($theseOutVals) > 0) {
                $outVals[$subs['SUBS_ID']] = $theseOutVals;
            }

            //  Reconstitute the string without the large values but with substitution ids.
            $xmlstring = implode($subs['START_TAG'], $pieces);
        } else if ( isset($subs['SUBS_TEXT']) ) {
            $xmlstring = str_replace($subs['SUBS_TEXT'], $subs['SUBS_ID'], $xmlstring);
        }
    }

    return $outVals;
}


//************************************************************************
// This function packs the target string suitably and returns it to the 
// function calling the XML parser.
//************************************************************************

/**
 * @param string $debug
 * @param array|null   $subsarray
 * @param array|null   $subsvalues
 *
 * @return bool
 */
function xml2dom_pack_output($debug = "no", $subsarray = null, $subsvalues = null)
{
    global $xml2dom_output;

    if ($debug != "no") {
        echo $xml2dom_output; 
    }

    $xml2dom_name = substr(
        $xml2dom_output, 1, 
        strpos($xml2dom_output, " = ")-1
    );

    //  Substitute back in any large values that were taken out earlier.
    if(is_array($subsarray)) {
        foreach ($subsarray as $subs) {
            if (isset($subs['START_TAG'])) {
                $pattern = '/(' . $subs['SUBS_ID'] . '[0-9]+)/';

                do {
                    $foundOne = false;
                    $pos = isl_strpos($xml2dom_output, $subs['SUBS_ID']);
                    if ($pos !== false) {
                        $res = isl_preg_match($pattern, $xml2dom_output, $matches);
                        if ($res === 1) {
                            $matchCount = count($matches);
                            for ($j = 1; $j < $matchCount; $j++) {
                                $subsVal = $subsvalues[$subs['SUBS_ID']][$matches[$j]];

                                // We need to replace exact key words
                                // For example: replace only __ATTACHMENTVAR__1 and NOT __ATTACHMENTVAR__10 ([__ATTACHMENTVAR__1]0)

                                // Using quote delimiters
                                $xml2dom_output = str_replace("'$matches[$j]'", "'$subsVal'", $xml2dom_output);

                                // Solution with regex using \b - word boundary
                                //$xml2dom_output = preg_replace('/\b' . $matches[$j] . '\b/', $subsVal, $xml2dom_output);

                                $foundOne = true;
                            }
                        }
                    }
                } while ($foundOne == true);

            } else if (isset($subs['SUBS_TEXT'])) {
                $xml2dom_output = str_replace($subs['SUBS_ID'], $subs['SUBS_TEXT'], $xml2dom_output);
            }
        }
    }

    try {
        $result = eval($xml2dom_output);
    } catch (ParseError $e) {
        $result = false;
    }

    //  If the eval failed, there is some unanticipated syntax error.  At least generate
    //   an error - if we don't, processing will continue but we'll have a bad status code (500).
    if ($result === false) {
        xml2dom_error_message("E_PARSE", '', 'unknown');
        eval($xml2dom_output);
        return false;
    }

    
    //eval ($xml2dom_output);
    $xml2dom_top = ${$xml2dom_name};
    
    return ($xml2dom_top);
}

