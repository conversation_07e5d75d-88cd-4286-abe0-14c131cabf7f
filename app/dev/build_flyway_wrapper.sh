#!/bin/bash

exit_function() {
  local exitCode=$1
  local text=$2
  if [ -z "$exitCode" ]; then
    exitCode=0
  fi
  if [ ! -z "$text" ]; then
    echo "$text"
  fi

  if [ ! -z "$CURRENTDIRECTORY" ]; then
    cd $CURRENTDIRECTORY
  fi

  exit $exitCode
}

readonly CURRENTDIRECTORY=$(pwd)
readonly BASEDIR=$(dirname "$0")

cd $BASEDIR/..
APP_DIR=`pwd -P`
FP_SVN_URL='**************:Intacct/ia-flyway-parallel.git'

if [[ ! -d "../../java_tools/ia-flyway-parallel" ]]; then
    # check out FlywayParallel
    cd ../..
    if [[ ! -d "java_tools" ]]; then
        mkdir java_tools
    fi
    cd java_tools
    git clone $FP_SVN_URL
    cd ia-flyway-parallel
else
  cd ../../java_tools/ia-flyway-parallel
  git pull
fi

source /usr/local/intacct/bin/get_nearest_infra_server.sh >/dev/null
MVN_PROXY_OPTIONS=''
if [[ -n "${IA_SQUID_PROXY}" || "$HOSTNAME" != "dev14.intacct.com" ]]; then
    MVN_PROXY_OPTIONS="-Dhttps.proxyHost=${IA_SQUID_PROXY} -Dhttps.proxyPort=3128"
fi

~master/opt/maven/bin/mvn $MVN_PROXY_OPTIONS package
exitCode=$?
if [[ $exitCode -ne 0 ]]; then
    exit_function 1 "Cannot build the flyway parallel project"
fi

if [[ ! -f "target/intacct.flyway-parallel-1.0.jar" ]]; then
  exit_function 1 "Cannot find the flyway paralle output jar, target/intacct.flyway-parallel-1.0.jar"
fi

cp -v --preserve target/intacct.flyway-parallel-1.0.jar $APP_DIR/db
cd $APP_DIR/db
if [[ ! -L "intacct.flyway-parallel.jar" ]]; then
    ln -vs intacct.flyway-parallel-1.0.jar intacct.flyway-parallel.jar
fi

exit_function 0 "Success"
