name: Check for Schemas
on:
  pull_request:
    types: [labeled]
    branches:
      -  main
      - 'Feb[2-9][0-9]'
      - 'May[2-9][0-9]'
      - 'Aug[2-9][0-9]'
      - 'Nov[2-9][0-9]'

jobs:
  re-start-PRjob:
   if: |
      github.event.pull_request.draft == false && 
      github.event.label.name == 'schemaNotAvailable' &&
      github.actor == 'jenkins-sageintacct'
       
   runs-on: self-hosted
   steps:
    - run: |
            if [[ ${{ github.event.pull_request.base.ref }} =~ ^main$ ]]   
            then
               echo "Checking availability of Schemas for main Branch "
            
               curl -X POST -i -k --user ci-automation:${{ secrets.PROD_JENKINS_TOKEN }}  "https://ci.intacct-dev.com/job/Schema_availability/buildWithParameters?PR_Number=${{ github.event.number }}&Branch_name=main"
            fi
            
            if [[ ${{ github.event.pull_request.base.ref }} =~ ^Aug22$ ]]   
            then
               echo "Checking availability of Schemas for Aug22 Branch "

                curl -X POST -i -k --user ci-automation:${{ secrets.PROD_JENKINS_TOKEN }}  "https://ci.intacct-dev.com/job/Schema_availability/buildWithParameters?PR_Number=${{ github.event.number }}&Branch_name=Release"
            fi
            if [[ ${{ github.event.pull_request.base.ref }} =~ ^Nov22$ ]]   
            then
                echo "Checking availability of Schemas for Nov22 Branch "

                curl -X POST -i -k --user ci-automation:${{ secrets.PROD_JENKINS_TOKEN }}  "https://ci.intacct-dev.com/job/Schema_availability/buildWithParameters?PR_Number=${{ github.event.number }}&Branch_name=Next_Rel"
            fi
            
        
  
