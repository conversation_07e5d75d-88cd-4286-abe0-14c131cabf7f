name: security_alert

on:
  push:
   branches:
      - 'main'
      - 'Feb[2-9][0-9]'
      - 'May[2-9][0-9]'
      - 'Aug[2-9][0-9]'
      - 'Nov[2-9][0-9]'
      - 'Aug[2-9][0-9]-[0-9][0-9]'
      - 'Aug[2-9][0-9]-[0-9][0-9]-[0-9][0-9]'
      - 'Feb[2-9][0-9]-[0-9][0-9]'
      - 'Feb[2-9][0-9]-[0-9][0-9]-[0-9][0-9]'
      - 'May[2-9][0-9]-[0-9][0-9]'
      - 'May[2-9][0-9]-[0-9][0-9]-[0-9][0-9]'
      - 'Nov[2-9][0-9]-[0-9][0-9]'
      - 'Nov[2-9][0-9]-[0-9][0-9]-[0-9][0-9]'
   paths:
      - 'app/**/*.phtml'
      - 'app/**/*.cfg'
      

jobs:
  Slack_Notification:
    #runs-on: ubuntu-latest
    runs-on: self-hosted  

    steps:
      # Slack Notification
      - name: Checkout intacct/ghaction-slack-post
        uses: actions/checkout@v4
        with:
          repository: intacct/ghaction-slack-post
          ref: main
          token: ${{ secrets.GH_PAT_IADODEPLOY_ACTION  }}
          path: ./actions/ghaction-slack-post

      - name: Post Slack message
        uses: ./actions/ghaction-slack-post
        with:
          args: '{\"channel\":\"C02ECE6BFDL\",\"text\":\" "Security Alert:\n A new *.phtml or *.cfg file has been Modified/Added to Repo:${{ github.repository }}"\n\n Commit Hash: ${{github.server_url}}/${{github.repository}}/commit/${{github.sha}} \"}'
        env:
          SLACK_BOT_TOKEN: ${{ secrets.IA_APP_SLACK_BOT_TOKEN }}

  Email_Notification:
    runs-on: self-hosted
    if: ${{ false }} # Disable email notification
    steps:
      # Outlook Email Notification
      - name: Checkout intacct/ghaction-send-email
        uses: actions/checkout@v4
        with:
          repository: intacct/ghaction-send-email
          ref: main
          token: ${{ secrets.GH_PAT_IADODEPLOY_ACTION  }}
          path: ./actions/ghaction-send-email

      - name: Email Notification
        uses: dawidd6/action-send-mail@v3.12.0
        with:
          server_address: smtp-eng.intacct.com
          port: 25
          subject: 'Alert !!! New .phtml or .cfg file Added/deleted in main Code'
          body: "Hello Team\n\n A new .phtml or .cfg file has been Modified/Added to Repo:: ${{ github.repository }}\n\n Commit Hash:\n ${{github.server_url}}/${{github.repository}}/commit/${{github.sha}}\n\n Thanks,\n GitHub-Actions."
          to: <EMAIL>, <EMAIL>
          from: <EMAIL>
